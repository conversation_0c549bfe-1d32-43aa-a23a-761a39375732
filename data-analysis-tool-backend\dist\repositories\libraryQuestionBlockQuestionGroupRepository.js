"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const prisma_1 = require("../utils/prisma");
class LibraryQuestionBlockQuestionGroupRepository {
    create(data) {
        return __awaiter(this, void 0, void 0, function* () {
            const newGroup = yield prisma_1.prisma.libraryQuestionBlockQuestionGroup.create({
                data: {
                    title: data.title,
                    order: data.order,
                    parentGroupId: data.parentGroupId,
                },
            });
            // Step 2: If there are selected question IDs, update those questions to associate them with the new group
            if (data.selectedQuestionIds && data.selectedQuestionIds.length > 0) {
                const question = yield prisma_1.prisma.libraryQuestionBlockQuestion.updateMany({
                    where: {
                        id: {
                            in: data.selectedQuestionIds, // Array of selected question IDs
                        },
                    },
                    data: {
                        libraryQuestionBlockQuestionGroupId: newGroup.id, // Set the new questionGroupId to associate with the new group
                    },
                });
            }
            return newGroup;
        });
    }
    delete(id) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.libraryQuestionBlockQuestionGroup.delete({
                where: { id },
            });
        });
    }
    deleteManyQuestionByGroup(id) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.libraryQuestionBlockQuestion.deleteMany({
                where: {
                    libraryQuestionBlockQuestionGroupId: id,
                },
            });
        });
    }
    findById(id) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.libraryQuestionBlockQuestionGroup.findUnique({
                where: { id },
                include: { questionBlockQuestion: true },
            });
        });
    }
    update(id, updates) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.libraryQuestionBlockQuestionGroup.update({
                where: { id },
                data: updates,
            });
        });
    }
    updateGroupInsideParentGroup(childGroupId, ParentGroupId) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.libraryQuestionBlockQuestionGroup.update({
                where: {
                    id: childGroupId,
                },
                data: {
                    parentGroupId: ParentGroupId,
                },
            });
        });
    }
    RemoveGroupFromParentGroup(groupId) {
        return __awaiter(this, void 0, void 0, function* () {
            return prisma_1.prisma.libraryQuestionBlockQuestionGroup.update({
                where: {
                    id: groupId,
                },
                data: {
                    parentGroupId: null,
                },
            });
        });
    }
}
exports.default = new LibraryQuestionBlockQuestionGroupRepository();
