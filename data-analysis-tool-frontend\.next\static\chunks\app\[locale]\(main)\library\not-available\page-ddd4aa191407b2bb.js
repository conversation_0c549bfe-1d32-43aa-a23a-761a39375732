(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6070],{1164:(e,r,t)=>{Promise.resolve().then(t.bind(t,66677))},17652:(e,r,t)=>{"use strict";t.d(r,{c3:()=>i});var n=t(46453);function a(e,r){return(...e)=>{try{return r(...e)}catch{throw Error(void 0)}}}let i=a(0,n.c3);a(0,n.kc)},35695:(e,r,t)=>{"use strict";var n=t(18999);t.o(n,"useParams")&&t.d(r,{useParams:function(){return n.useParams}}),t.o(n,"usePathname")&&t.d(r,{usePathname:function(){return n.usePathname}}),t.o(n,"useRouter")&&t.d(r,{useRouter:function(){return n.useRouter}}),t.o(n,"useSearchParams")&&t.d(r,{useSearchParams:function(){return n.useSearchParams}})},62672:(e,r,t)=>{"use strict";t.d(r,{Ay:()=>s,l:()=>i,yg:()=>a});let n=(0,t(51990).Z0)({name:"createLibraryItem",initialState:{visible:!1},reducers:{showCreateLibraryModal:e=>{e.visible=!0},hideCreateLibraryModal:e=>{e.visible=!1}}}),{showCreateLibraryModal:a,hideCreateLibraryModal:i}=n.actions,s=n.reducer},66677:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>l});var n=t(95155);t(12115);var a=t(35695),i=t(14549),s=t(34540),o=t(62672),c=t(17652);let l=()=>{let e=(0,s.wA)(),{category:r}=(0,a.useParams)(),t=(0,c.c3)();return(0,n.jsx)("div",{className:"flex flex-col items-center justify-center py-16 px-4 min-h-[70vh]",children:(0,n.jsxs)("div",{className:"bg-neutral-100 rounded-lg shadow-sm p-8 max-w-md w-full text-center",children:[(0,n.jsx)("div",{className:"flex justify-center mb-6",children:(0,n.jsx)("div",{className:"bg-neutral-200 p-5 rounded-full",children:(0,n.jsx)(i.rjU,{size:50,className:"text-primary-500"})})}),(0,n.jsx)("h2",{className:"text-2xl font-semibold text-neutral-800 mb-2",children:t("noLibraryItems")}),(0,n.jsx)("p",{className:"text-neutral-600 mb-8",children:t("collections"===r?"noCollections":"emptyLibrary")}),(0,n.jsx)("button",{onClick:()=>{e((0,o.yg)())},className:"btn-primary w-full",children:t("createNewLibraryItem")})]})})}},74436:(e,r,t)=>{"use strict";t.d(r,{k5:()=>u});var n=t(12115),a={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},i=n.createContext&&n.createContext(a),s=["attr","size","title"];function o(){return(o=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e}).apply(this,arguments)}function c(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,n)}return t}function l(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?c(Object(t),!0).forEach(function(r){var n,a,i;n=e,a=r,i=t[r],(a=function(e){var r=function(e,r){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,r||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==typeof r?r:r+""}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):c(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function u(e){return r=>n.createElement(m,o({attr:l({},e.attr)},r),function e(r){return r&&r.map((r,t)=>n.createElement(r.tag,l({key:t},r.attr),e(r.child)))}(e.child))}function m(e){var r=r=>{var t,{attr:a,size:i,title:c}=e,u=function(e,r){if(null==e)return{};var t,n,a=function(e,r){if(null==e)return{};var t={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(r.indexOf(n)>=0)continue;t[n]=e[n]}return t}(e,r);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)t=i[n],!(r.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(e,t)&&(a[t]=e[t])}return a}(e,s),m=i||r.size||"1em";return r.className&&(t=r.className),e.className&&(t=(t?t+" ":"")+e.className),n.createElement("svg",o({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},r.attr,a,u,{className:t,style:l(l({color:e.color||r.color},r.style),e.style),height:m,width:m,xmlns:"http://www.w3.org/2000/svg"}),c&&n.createElement("title",null,c),e.children)};return void 0!==i?n.createElement(i.Consumer,null,e=>r(e)):r(a)}}},e=>{var r=r=>e(e.s=r);e.O(0,[2150,6453,635,8441,1684,7358],()=>r(1164)),_N_E=e.O()}]);