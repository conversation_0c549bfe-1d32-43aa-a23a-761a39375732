"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const questionController_1 = require("../../controllers/questionController");
const questionRepository_1 = __importDefault(require("../../repositories/questionRepository"));
// Mock the dependencies
jest.mock("../../repositories/questionRepository");
describe("Question Controller", () => {
    let mockRequest;
    let mockResponse;
    let responseObject = {};
    beforeEach(() => {
        // Reset mocks before each test
        jest.clearAllMocks();
        jest.resetAllMocks();
        // Setup mock response
        mockResponse = {
            status: jest.fn().mockReturnThis(),
            json: jest.fn().mockImplementation((result) => {
                responseObject = result;
                return mockResponse;
            }),
        };
        // Reset response object
        responseObject = {};
        // Setup default authenticated user
        mockRequest = {
            user: {
                id: 1,
            },
            params: {},
            query: {},
            body: {},
        };
    });
    describe("getAllQuestion", () => {
        beforeEach(() => {
            mockRequest.params = { projectId: "1" };
        });
        it("should get all questions successfully", () => __awaiter(void 0, void 0, void 0, function* () {
            const mockQuestions = [
                {
                    id: 1,
                    label: "Question 1",
                    inputType: "text",
                    projectId: 1,
                },
                {
                    id: 2,
                    label: "Question 2",
                    inputType: "selectone",
                    projectId: 1,
                },
            ];
            questionRepository_1.default.isPorjectOwner.mockResolvedValue(true);
            questionRepository_1.default.findAll.mockResolvedValue(mockQuestions);
            yield (0, questionController_1.getAllQuestion)(mockRequest, mockResponse);
            expect(questionRepository_1.default.isPorjectOwner).toHaveBeenCalledWith(1, 1);
            expect(questionRepository_1.default.findAll).toHaveBeenCalledWith(1);
            expect(mockResponse.status).toHaveBeenCalledWith(200);
            expect(responseObject).toHaveProperty("message", "Successfully fetched questions.");
            expect(responseObject).toHaveProperty("questions", mockQuestions);
        }));
        it("should return 404 when user is not found", () => __awaiter(void 0, void 0, void 0, function* () {
            // No user in request
            mockRequest.user = undefined;
            yield (0, questionController_1.getAllQuestion)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(404);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "user not found");
        }));
        it("should return 400 when projectId is missing", () => __awaiter(void 0, void 0, void 0, function* () {
            mockRequest.params = {};
            mockRequest.query = {};
            yield (0, questionController_1.getAllQuestion)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(400);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "Project ID is required");
        }));
        it("should return 400 when user is not project owner", () => __awaiter(void 0, void 0, void 0, function* () {
            questionRepository_1.default.isPorjectOwner.mockResolvedValue(false);
            yield (0, questionController_1.getAllQuestion)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(400);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "only project owner can view the questions");
        }));
        it("should handle server errors", () => __awaiter(void 0, void 0, void 0, function* () {
            questionRepository_1.default.isPorjectOwner.mockRejectedValue(new Error("Database error"));
            yield (0, questionController_1.getAllQuestion)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(500);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "error fetching questions");
        }));
    });
    describe("createQuestion", () => {
        beforeEach(() => {
            mockRequest.params = { projectId: "1" };
            mockRequest.body = {
                label: "New Question",
                inputType: "text",
                isRequired: true,
                position: 1,
                hint: "This is a hint",
                placeholder: "Enter text",
            };
        });
        it("should create a question successfully", () => __awaiter(void 0, void 0, void 0, function* () {
            const mockQuestion = {
                id: 1,
                label: "New Question",
                inputType: "text",
                isRequired: true,
                position: 1,
                hint: "This is a hint",
                placeholder: "Enter text",
                projectId: 1,
            };
            questionRepository_1.default.isPorjectOwner.mockResolvedValue(true);
            questionRepository_1.default.create.mockResolvedValue(mockQuestion);
            yield (0, questionController_1.createQuestion)(mockRequest, mockResponse);
            expect(questionRepository_1.default.isPorjectOwner).toHaveBeenCalledWith(1, 1);
            expect(questionRepository_1.default.create).toHaveBeenCalledWith(expect.objectContaining({
                label: "New Question",
                inputType: "text",
                isRequired: true,
                position: 1,
                projectId: 1,
            }));
            expect(mockResponse.status).toHaveBeenCalledWith(200);
            expect(responseObject).toHaveProperty("data.question", mockQuestion);
        }));
        it("should create a question with options successfully", () => __awaiter(void 0, void 0, void 0, function* () {
            mockRequest.body = {
                label: "Question with Options",
                inputType: "selectone",
                isRequired: true,
                position: 2,
                questionOptions: [
                    { label: "Option 1", code: "OPT1" },
                    { label: "Option 2", code: "OPT2", nextQuestionId: 3 },
                ],
            };
            const mockQuestion = {
                id: 1,
                label: "Question with Options",
                inputType: "selectone",
                isRequired: true,
                position: 2,
                projectId: 1,
                questionOptions: [
                    { id: 1, label: "Option 1", code: "OPT1" },
                    { id: 2, label: "Option 2", code: "OPT2", nextQuestionId: 3 },
                ],
            };
            questionRepository_1.default.isPorjectOwner.mockResolvedValue(true);
            questionRepository_1.default.create.mockResolvedValue(mockQuestion);
            yield (0, questionController_1.createQuestion)(mockRequest, mockResponse);
            expect(questionRepository_1.default.create).toHaveBeenCalledWith(expect.objectContaining({
                label: "Question with Options",
                inputType: "selectone",
                projectId: 1,
                questionOptions: [
                    { label: "Option 1", code: "OPT1" },
                    { label: "Option 2", code: "OPT2", nextQuestionId: 3 },
                ],
            }));
            expect(mockResponse.status).toHaveBeenCalledWith(200);
        }));
        it("should return 403 when user is not project owner", () => __awaiter(void 0, void 0, void 0, function* () {
            questionRepository_1.default.isPorjectOwner.mockResolvedValue(false);
            yield (0, questionController_1.createQuestion)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(403);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "User is not associated with the project");
        }));
        it("should return 403 for invalid input due to unauthorized user", () => __awaiter(void 0, void 0, void 0, function* () {
            // Missing required fields but fails due to unauthorized user
            mockRequest.body = { label: "Invalid Question" };
            questionRepository_1.default.isPorjectOwner.mockResolvedValue(false);
            yield (0, questionController_1.createQuestion)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(403);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "User is not associated with the project");
            expect(questionRepository_1.default.create).not.toHaveBeenCalled();
        }));
        it("should handle server errors", () => __awaiter(void 0, void 0, void 0, function* () {
            questionRepository_1.default.isPorjectOwner.mockResolvedValue(true);
            questionRepository_1.default.create.mockRejectedValue(new Error("Database error"));
            yield (0, questionController_1.createQuestion)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(500);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "error creating qustion");
        }));
    });
    describe("updateQuestion", () => {
        beforeEach(() => {
            mockRequest.params = { id: "1" };
            mockRequest.body = {
                label: "Updated Question",
                hint: "Updated hint",
            };
        });
        it("should update a question successfully", () => __awaiter(void 0, void 0, void 0, function* () {
            const existingQuestion = {
                id: 1,
                label: "Original Question",
                inputType: "text",
                position: 1,
                projectId: 1,
            };
            const updatedQuestion = Object.assign(Object.assign({}, existingQuestion), { label: "Updated Question", hint: "Updated hint" });
            questionRepository_1.default.findById.mockResolvedValue(existingQuestion);
            questionRepository_1.default.isPorjectOwner.mockResolvedValue(true);
            questionRepository_1.default.updateById.mockResolvedValue(updatedQuestion);
            yield (0, questionController_1.updateQuestion)(mockRequest, mockResponse);
            expect(questionRepository_1.default.findById).toHaveBeenCalledWith(1);
            expect(questionRepository_1.default.isPorjectOwner).toHaveBeenCalledWith(1, 1);
            expect(questionRepository_1.default.updateById).toHaveBeenCalled();
            expect(mockResponse.status).toHaveBeenCalledWith(200);
        }));
        it("should return 404 when question not found", () => __awaiter(void 0, void 0, void 0, function* () {
            questionRepository_1.default.findById.mockResolvedValue(null);
            yield (0, questionController_1.updateQuestion)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(404);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "Question not found");
            expect(questionRepository_1.default.updateById).not.toHaveBeenCalled();
        }));
        it("should return 403 when user is not project owner", () => __awaiter(void 0, void 0, void 0, function* () {
            const existingQuestion = {
                id: 1,
                label: "Original Question",
                inputType: "text",
                position: 1,
                projectId: 1,
            };
            questionRepository_1.default.findById.mockResolvedValue(existingQuestion);
            questionRepository_1.default.isPorjectOwner.mockResolvedValue(false);
            yield (0, questionController_1.updateQuestion)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(403);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "You are not the project owner");
            expect(questionRepository_1.default.updateById).not.toHaveBeenCalled();
        }));
        it("should handle invalid question ID", () => __awaiter(void 0, void 0, void 0, function* () {
            mockRequest.params = { id: "invalid" };
            yield (0, questionController_1.updateQuestion)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(400);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "Invalid request");
        }));
        it("should handle server errors", () => __awaiter(void 0, void 0, void 0, function* () {
            questionRepository_1.default.findById.mockRejectedValue(new Error("Database error"));
            yield (0, questionController_1.updateQuestion)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(500);
            expect(responseObject).toHaveProperty("success", false);
        }));
    });
    describe("deleteQuestion", () => {
        beforeEach(() => {
            mockRequest.params = { id: "1" };
        });
        it("should delete a question successfully", () => __awaiter(void 0, void 0, void 0, function* () {
            const existingQuestion = {
                id: 1,
                label: "Question to Delete",
                projectId: 1,
            };
            questionRepository_1.default.findById.mockResolvedValue(existingQuestion);
            questionRepository_1.default.isPorjectOwner.mockResolvedValue(true);
            questionRepository_1.default.deleteQuestion.mockResolvedValue({
                id: 1,
            });
            yield (0, questionController_1.deleteQuestion)(mockRequest, mockResponse);
            expect(questionRepository_1.default.findById).toHaveBeenCalledWith(1);
            expect(questionRepository_1.default.isPorjectOwner).toHaveBeenCalledWith(1, 1);
            expect(questionRepository_1.default.deleteQuestion).toHaveBeenCalledWith(1);
            expect(mockResponse.status).toHaveBeenCalledWith(200);
            expect(responseObject).toHaveProperty("message", "Successfully deleted question");
        }));
        it("should return 404 when question not found", () => __awaiter(void 0, void 0, void 0, function* () {
            questionRepository_1.default.findById.mockResolvedValue(null);
            yield (0, questionController_1.deleteQuestion)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(404);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "Question not found");
            expect(questionRepository_1.default.deleteQuestion).not.toHaveBeenCalled();
        }));
        it("should return 403 when user is not project owner", () => __awaiter(void 0, void 0, void 0, function* () {
            const existingQuestion = {
                id: 1,
                label: "Question to Delete",
                projectId: 1,
            };
            questionRepository_1.default.findById.mockResolvedValue(existingQuestion);
            questionRepository_1.default.isPorjectOwner.mockResolvedValue(false);
            yield (0, questionController_1.deleteQuestion)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(403);
            expect(responseObject).toHaveProperty("succcess", false); // Match controller typo
            expect(responseObject).toHaveProperty("message", "Current user cannot delete question from this project");
            expect(questionRepository_1.default.deleteQuestion).not.toHaveBeenCalled();
        }));
        it("should handle invalid question ID", () => __awaiter(void 0, void 0, void 0, function* () {
            mockRequest.params = { id: "invalid" };
            yield (0, questionController_1.deleteQuestion)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(400);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "Invalid request: User ID or Question ID is missing");
        }));
        it("should handle server errors", () => __awaiter(void 0, void 0, void 0, function* () {
            questionRepository_1.default.findById.mockRejectedValue(new Error("Database error"));
            yield (0, questionController_1.deleteQuestion)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(500);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "Database error");
        }));
    });
    describe("duplicateQuestion", () => {
        beforeEach(() => {
            mockRequest.params = { id: "1" };
        });
        it("should duplicate a question successfully", () => __awaiter(void 0, void 0, void 0, function* () {
            const existingQuestion = {
                id: 1,
                label: "Original Question",
                inputType: "text",
                position: 1,
                projectId: 1,
            };
            const duplicatedQuestion = {
                id: 2,
                label: "Original Question (Copy)",
                inputType: "text",
                position: 2,
                projectId: 1,
            };
            questionRepository_1.default.findById.mockResolvedValue(existingQuestion);
            questionRepository_1.default.isPorjectOwner.mockResolvedValue(true);
            questionRepository_1.default.duplicateQuestion.mockResolvedValue(duplicatedQuestion);
            yield (0, questionController_1.duplicateQuestion)(mockRequest, mockResponse);
            expect(questionRepository_1.default.findById).toHaveBeenCalledWith(1);
            expect(questionRepository_1.default.isPorjectOwner).toHaveBeenCalledWith(1, 1);
            expect(questionRepository_1.default.duplicateQuestion).toHaveBeenCalledWith(1, 1);
            expect(mockResponse.status).toHaveBeenCalledWith(200);
            expect(responseObject).toHaveProperty("message", "Successfully duplicated the question.");
            expect(responseObject).toHaveProperty("duplicatedQuestion", duplicatedQuestion);
        }));
        it("should return 404 when question not found", () => __awaiter(void 0, void 0, void 0, function* () {
            questionRepository_1.default.findById.mockResolvedValue(null);
            yield (0, questionController_1.duplicateQuestion)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(404);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "Question not found");
            expect(questionRepository_1.default.duplicateQuestion).not.toHaveBeenCalled();
        }));
        it("should return 403 when user is not project owner", () => __awaiter(void 0, void 0, void 0, function* () {
            const existingQuestion = {
                id: 1,
                label: "Original Question",
                projectId: 1,
            };
            questionRepository_1.default.findById.mockResolvedValue(existingQuestion);
            questionRepository_1.default.isPorjectOwner.mockResolvedValue(false);
            yield (0, questionController_1.duplicateQuestion)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(403);
            expect(responseObject).toHaveProperty("succcess", false); // Match controller typo
            expect(responseObject).toHaveProperty("message", "Current user cannot delete question from this project");
            expect(questionRepository_1.default.duplicateQuestion).not.toHaveBeenCalled();
        }));
        it("should handle invalid question ID", () => __awaiter(void 0, void 0, void 0, function* () {
            mockRequest.params = { id: "invalid" };
            yield (0, questionController_1.duplicateQuestion)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(400);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "Invalid request: User ID or Question ID is missing");
        }));
        it("should handle server errors", () => __awaiter(void 0, void 0, void 0, function* () {
            questionRepository_1.default.findById.mockRejectedValue(new Error("Database error"));
            yield (0, questionController_1.duplicateQuestion)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(500);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "Database error");
        }));
    });
});
