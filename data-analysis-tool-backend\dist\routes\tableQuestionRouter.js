"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const tableQuestionController_1 = require("../controllers/tableQuestionController");
const auth_1 = require("../middleware/auth");
/**
 * Router for table question endpoints
 * Handles CRUD operations for table questions and related data
 *
 * Supports:
 * - Creating table questions with parent-child column relationships
 * - Retrieving table questions with their columns and rows
 * - Updating table questions while maintaining parent-child relationships
 * - Deleting table questions
 * - Saving cell values
 * - Getting all table questions for a project
 */
const router = express_1.default.Router();
// Apply authentication middleware to all routes
router.use(auth_1.authenticate);
// Create a new table question
router.post("/", tableQuestionController_1.createTableQuestion);
// Get a table question by ID
router.get("/:id", tableQuestionController_1.getTableQuestion);
// Update a table question
router.patch("/:id", tableQuestionController_1.updateTableQuestion);
// Save cell values
router.post("/cells", tableQuestionController_1.saveCellValues);
// Delete a table question
router.delete("/:id", tableQuestionController_1.deleteTableQuestion);
// Get all table questions for a project
// Note: This route must be defined after the /:id route to avoid conflicts
router.get("/project/:projectId", tableQuestionController_1.getTableQuestionsByProject);
exports.default = router;
