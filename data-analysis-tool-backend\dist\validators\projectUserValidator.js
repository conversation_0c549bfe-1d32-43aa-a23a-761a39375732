"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProjectUserSchema = void 0;
const zod_1 = require("zod");
exports.ProjectUserSchema = zod_1.z.object({
    userId: zod_1.z.number(),
    projectId: zod_1.z.number(),
    permission: zod_1.z
        .object({
        viewForm: zod_1.z.boolean().optional(),
        editForm: zod_1.z.boolean().optional(),
        viewSubmissions: zod_1.z.boolean().optional(),
        viewSubmissionsSpecific: zod_1.z.boolean().optional(),
        viewSubmissionsCondition: zod_1.z.boolean().optional(),
        addSubmissions: zod_1.z.boolean().optional(),
        editSubmissions: zod_1.z.boolean().optional(),
        editSubmissionsSpecific: zod_1.z.boolean().optional(),
        editSubmissionsCondition: zod_1.z.boolean().optional(),
        validateSubmissions: zod_1.z.boolean().optional(),
        validateSubmissionsSpecific: zod_1.z.boolean().optional(),
        validateSubmissionsCondition: zod_1.z.boolean().optional(),
        deleteSubmissions: zod_1.z.boolean().optional(),
        deleteSubmissionsSpecific: zod_1.z.boolean().optional(),
        deleteSubmissionsCondition: zod_1.z.boolean().optional(),
        manageProject: zod_1.z.boolean().optional(),
    })
        .strict(),
});
