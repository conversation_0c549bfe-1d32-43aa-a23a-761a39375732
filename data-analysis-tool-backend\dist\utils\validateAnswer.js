"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateInput = validateInput;
const zod_1 = require("zod");
function getAnswerSchemaByInputType(inputType) {
    switch (inputType) {
        case "text":
            return zod_1.z.string({ invalid_type_error: "must be a text" });
        case "number":
            return zod_1.z.number({ invalid_type_error: "Must be a number" });
        case "decimal":
            return zod_1.z.number({ invalid_type_error: "Must be a decimal number" });
        case "selectone":
            return zod_1.z.string().min(1, { message: "Must select one option" });
        case "selectmany":
            return zod_1.z
                .array(zod_1.z.string())
                .min(1, { message: "Must select at least one option" });
        case "date":
            return zod_1.z.string().refine((val) => !isNaN(Date.parse(val)), {
                message: "Invalid date format",
            });
        case "dateandtime":
            return zod_1.z.string().refine((val) => !isNaN(Date.parse(val)), {
                message: "Invalid date and time format",
            });
        default:
            throw new Error(`Unsupported input type: ${inputType}`);
    }
}
function validateInput(inputType, answer) {
    const schema = getAnswerSchemaByInputType(inputType);
    const result = schema.safeParse(answer);
    if (result.success) {
        return { valid: true };
    }
    else {
        console.error("❌ Validation error:", result.error.errors);
        return { valid: false, errors: result.error.errors };
    }
}
