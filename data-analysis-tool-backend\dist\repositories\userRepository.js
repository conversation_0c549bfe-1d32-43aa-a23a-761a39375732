"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const prisma_1 = require("../utils/prisma");
class UserRepository {
    /**
     * Find a user by email
     */
    findByEmail(email) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.user.findUnique({ where: { email } });
        });
    }
    /**
     * Find a user by ID (only selected fields)
     */
    findById(id) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.user.findUnique({
                where: { id },
                select: {
                    id: true,
                    email: true,
                    name: true,
                    country: true,
                    city: true,
                    bio: true,
                    sector: true,
                    organizationType: true,
                    createdAt: true,
                    updatedAt: true,
                },
            });
        });
    }
    /**
     * Get all users
     */
    findAll() {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.user.findMany({});
        });
    }
    /**
     * Create new user
     */
    create(userData) {
        return __awaiter(this, void 0, void 0, function* () {
            const { name, email, password, country, sector, organizationType } = userData;
            const salt = yield bcryptjs_1.default.genSalt(10);
            const hashedPassword = yield bcryptjs_1.default.hash(password, salt);
            return yield prisma_1.prisma.user.create({
                data: {
                    name,
                    email,
                    password: hashedPassword,
                    country,
                    sector,
                    organizationType,
                },
            });
        });
    }
    /**
     * Update user by ID
     */
    updateById(id, updateData) {
        return __awaiter(this, void 0, void 0, function* () {
            const data = {};
            if (updateData.name !== undefined)
                data.name = updateData.name;
            if (updateData.email !== undefined)
                data.email = updateData.email;
            if (updateData.country !== undefined)
                data.country = updateData.country;
            if (updateData.city !== undefined)
                data.city = updateData.city;
            if (updateData.bio !== undefined)
                data.bio = updateData.bio;
            if (updateData.sector !== undefined)
                data.sector = updateData.sector;
            if (updateData.organizationType !== undefined)
                data.organizationType = updateData.organizationType;
            return yield prisma_1.prisma.user.update({
                where: { id },
                data,
                select: {
                    id: true,
                    email: true,
                    name: true,
                    country: true,
                    city: true,
                    bio: true,
                    sector: true,
                    organizationType: true,
                    createdAt: true,
                    updatedAt: true,
                },
            });
        });
    }
    /**
     * Update user's password
     */
    updatePassword(id, newPassword) {
        return __awaiter(this, void 0, void 0, function* () {
            const salt = yield bcryptjs_1.default.genSalt(10);
            const hashedPassword = yield bcryptjs_1.default.hash(newPassword, salt);
            return yield prisma_1.prisma.user.update({
                where: { id },
                data: { password: hashedPassword },
            });
        });
    }
    /**
     * Delete a user by ID
     */
    deleteById(id) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.user.delete({
                where: { id },
            });
        });
    }
    /**
     * Check if email is in use
     */
    isEmailInUse(email, excludeUserId) {
        return __awaiter(this, void 0, void 0, function* () {
            const where = { email };
            if (excludeUserId) {
                where.id = { not: excludeUserId };
            }
            const count = yield prisma_1.prisma.user.count({ where });
            return count > 0;
        });
    }
    /**
     * Verify user password
     */
    verifyPassword(id, password) {
        return __awaiter(this, void 0, void 0, function* () {
            const user = yield prisma_1.prisma.user.findUnique({
                where: { id },
                select: { password: true },
            });
            if (!user)
                return false;
            return yield bcryptjs_1.default.compare(password, user.password);
        });
    }
    saveResetToken(userId, hashedToken, expires) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.user.update({
                where: { id: userId },
                data: {
                    resetPasswordToken: hashedToken,
                    resetPasswordExpires: expires,
                },
            });
        });
    }
    checkPasswordToken(hashedToken) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.user.findFirst({
                where: {
                    resetPasswordToken: hashedToken,
                },
            });
        });
    }
    checkPasswordTokenExpirey(hashedToken) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.user.findFirst({
                where: {
                    resetPasswordToken: hashedToken,
                    resetPasswordExpires: {
                        gte: new Date(),
                    },
                },
            });
        });
    }
    resetUserPassword(userId, password) {
        return __awaiter(this, void 0, void 0, function* () {
            const salt = yield bcryptjs_1.default.genSalt(10);
            const hashedPassword = yield bcryptjs_1.default.hash(password, salt);
            return yield prisma_1.prisma.user.update({
                where: { id: userId },
                data: {
                    password: hashedPassword,
                    resetPasswordToken: null,
                    resetPasswordExpires: null,
                },
            });
        });
    }
    sendEmailVerificationToken(userId, hasedToken, expires) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.user.update({
                where: { id: userId },
                data: {
                    emailVerificationToken: hasedToken,
                    emailVerificationExpires: expires,
                },
            });
        });
    }
    findByemailVerificationToken(hasedToken) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.user.findFirst({
                where: {
                    emailVerificationToken: hasedToken,
                },
            });
        });
    }
    isEmailVerificationTokenExpired(hasedToken) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.user.findFirst({
                where: {
                    emailVerificationToken: hasedToken,
                    emailVerificationExpires: {
                        gte: new Date(),
                    },
                },
            });
        });
    }
    verifyUser(userId) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.user.update({
                where: { id: userId },
                data: {
                    isVerified: true,
                    emailVerificationToken: null,
                    emailVerificationExpires: null,
                },
            });
        });
    }
    createSession(_a) {
        return __awaiter(this, arguments, void 0, function* ({ userId, deviceInfo, ipAddress, browserInfo, }) {
            return yield prisma_1.prisma.userSession.create({
                data: {
                    userId,
                    deviceInfo,
                    ipAddress,
                    browserInfo,
                },
            });
        });
    }
    getUserSessions(_a) {
        return __awaiter(this, arguments, void 0, function* ({ userId, }) {
            return yield prisma_1.prisma.userSession.findMany({
                where: { userId },
                orderBy: { createdAt: "desc" },
            });
        });
    }
    logoutAllDevices(userId) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.userSession.updateMany({
                where: { userId },
                data: { isActive: false },
            });
        });
    }
    logoutSingleDevice(userId, sessionId) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.userSession.update({
                where: { userId, id: sessionId },
                data: { isActive: false },
            });
        });
    }
    changeEmail(id_1, _a) {
        return __awaiter(this, arguments, void 0, function* (id, { email }) {
            return yield prisma_1.prisma.user.update({
                where: { id },
                data: { email, isVerified: false },
            });
        });
    }
}
exports.default = new UserRepository();
