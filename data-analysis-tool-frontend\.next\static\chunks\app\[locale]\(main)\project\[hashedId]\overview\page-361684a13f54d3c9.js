(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4653],{23950:(e,s,t)=>{Promise.resolve().then(t.bind(t,91778))},25784:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let a=t(23464).A.create({baseURL:"http://localhost:4000/api",headers:{"Content-Type":"application/json"},withCredentials:!0});a.interceptors.request.use(e=>e,e=>Promise.reject(e)),a.interceptors.response.use(e=>e,e=>("ERR_NETWORK"===e.code&&console.error("Network error - Please check if the backend server is running"),Promise.reject(e)));let r=a},29350:(e,s,t)=>{"use strict";t.d(s,{A:()=>c});var a=t(97381),r=t(59362),l=t(25784),n=t(35695),i=t(12115),o=t(34540);let c=e=>{let s=(0,o.wA)(),t=(0,n.useRouter)(),c=(0,n.usePathname)(),{status:d,user:u,error:p}=(0,o.d4)(e=>e.auth),m=async()=>{try{s((0,a.Le)());let e=(await l.A.get("/users/me")).data;s((0,a.tQ)(e))}catch(l){if(s((0,a.x9)()),(0,r.F0)(l)){var e,n,i,o,d;if(console.error("Auth error:",null==(e=l.response)?void 0:e.status,null==(n=l.response)?void 0:n.data),(null==(i=l.response)?void 0:i.status)===401){if(c.startsWith("/form-submission"))return;t.push("/")}else s((0,a.jB)((null==(d=l.response)||null==(o=d.data)?void 0:o.message)||l.message))}else s((0,a.jB)(l instanceof Error?l.message:"An unknown error occurred."))}};return(0,i.useEffect)(()=>{(null==e?void 0:e.skipFetchUser)||m()},[null==e?void 0:e.skipFetchUser]),(0,i.useEffect)(()=>{let e=e=>{if("logout"===e.key&&"true"===e.newValue)if(s((0,a.x9)()),c.startsWith("/form-submission")){let e=c.split("/")[2];e?t.push("/form-submission/".concat(e,"/sign-in")):t.push("/")}else t.push("/")};return window.addEventListener("storage",e),()=>{window.removeEventListener("storage",e)}},[s,t,c]),{status:d,user:u,error:p,isAuthenticated:"authenticated"===d,isLoading:"loading"===d,refreshAuthState:()=>{m()},signin:async(e,s,t)=>{try{await l.A.post("/users/login",e),await m(),null==s||s()}catch(e){if(e instanceof r.pe){var a,n;let s=null==(n=e.response)||null==(a=n.data)?void 0:a.errorType;null==t||t(s)}else null==t||t()}},logout:async()=>{try{await l.A.post("/users/logout"),localStorage.setItem("logout","true"),setTimeout(()=>localStorage.removeItem("logout"),100)}finally{if(s((0,a.x9)()),c.startsWith("/form-submission")){let e=c.split("/")[2];e?t.push("/form-submission/".concat(e,"/sign-in")):t.push("/")}else t.push("/")}}}}},57799:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});var a=t(95155);t(12115);let r=()=>(0,a.jsx)("div",{className:"w-full flex items-center justify-center",children:(0,a.jsx)("div",{className:"size-8 rounded-full border-x-2 border-primary-500 animate-spin my-16"})})},71402:(e,s,t)=>{"use strict";t.d(s,{Ay:()=>n,Ds:()=>r,_b:()=>l});let a=(0,t(51990).Z0)({name:"notification",initialState:{message:"",type:"",visible:!1},reducers:{showNotification:(e,s)=>{e.message=s.payload.message,e.type=s.payload.type,e.visible=!0},hideNotification:e=>{e.message="",e.type="",e.visible=!1}}}),{showNotification:r,hideNotification:l}=a.actions,n=a.reducer},77361:(e,s,t)=>{"use strict";t.d(s,{D_:()=>u,Im:()=>c,Oo:()=>p,c3:()=>l,kf:()=>r,lj:()=>h,or:()=>o,pf:()=>d,vj:()=>n,wI:()=>m,xx:()=>i});var a=t(25784);let r=async e=>{let{projectId:s}=e,{data:t}=await a.A.get("/projects/".concat(s));return t.project},l=async e=>{let{data:s}=await a.A.post("/projects/from-template",e);return s},n=async()=>{try{let{data:e}=await a.A.get("/projects");return e.projects}catch(e){throw console.error("Error fetching projects:",e),e}},i=async e=>{let{data:s}=await a.A.delete("/projects/delete/".concat(e));return s},o=async e=>{try{let{data:s}=await a.A.delete("/projects/delete-multiple",{data:{projectIds:e}});return s}catch(e){throw console.error("Error deleting multiple projects:",e),e}},c=async e=>{try{let{data:s}=await a.A.patch("/projects/change-status/".concat(e),{status:"archived"});return s}catch(e){throw console.error("Error archiving project:",e),e}},d=async function(e){arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{let{data:s}=await a.A.patch("/projects/change-status/".concat(e),{status:"deployed"});return s}catch(e){throw console.error("Error deploying project:",e),e}},u=async e=>{try{let{data:s}=await a.A.patch("/projects/update-many-status",{projectIds:e,status:"archived"});return s}catch(e){throw console.error("Error archiving multiple projects:",e),e}},p=async e=>{try{let{data:s}=await a.A.post("/users/check-email",{email:e});return s}catch(e){var s,t,r,l,n,i;throw Error("object"==typeof(null==(t=e.response)||null==(s=t.data)?void 0:s.message)?JSON.stringify(null==(l=e.response)||null==(r=l.data)?void 0:r.message):(null==(i=e.response)||null==(n=i.data)?void 0:n.message)||e.message||"Failed to check user")}},m=async e=>{let{projectId:s,email:t,permissions:r}=e;try{let e=await p(t);if(!e||!e.success)throw Error((null==e?void 0:e.message)||"User not found");let{data:l}=await a.A.post("/project-users",{userId:e.user.id,projectId:s,permission:r});return l}catch(e){var l,n,i,o,c,d;throw console.error("Error adding user to project:",e),Error("object"==typeof(null==(n=e.response)||null==(l=n.data)?void 0:l.message)?JSON.stringify(null==(o=e.response)||null==(i=o.data)?void 0:i.message):(null==(d=e.response)||null==(c=d.data)?void 0:c.message)||e.message||"Failed to add user")}},h=async e=>{try{let{data:s}=await a.A.post("/answers/multiple",e);return s}catch(e){throw console.error("Error creating answer submission:",e),e}}},88570:(e,s,t)=>{"use strict";t.d(s,{D:()=>i,l:()=>n});var a=t(41050);let r=t(49509).env.SALT||"rushan-salt",l=new a.A(r,12),n=e=>l.encode(e),i=e=>{let s=l.decode(e)[0];return"bigint"==typeof s?s<Number.MAX_SAFE_INTEGER?Number(s):null:"number"==typeof s?s:null}},91778:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>E});var a=t(95155),r=t(85339),l=t(94788),n=t(71007),i=t(17576),o=t(81887),c=t(14186),d=t(69074),u=t(59964),p=t(29869),m=t(34869),h=t(12115),x=t(63008),j=t(57799),y=t(19373),f=t(77361),g=t(35695),v=t(88570),N=t(29350),b=t(34540),w=t(71402),A=t(17652);let E=()=>{var e;let[s,t]=(0,h.useState)(!1);(0,h.useEffect)(()=>{t(!0)},[]);let{hashedId:E}=(0,g.useParams)(),z=(0,v.D)(E),k=(0,A.c3)(),{user:D}=(0,N.A)(),S=(0,b.wA)(),{data:M,isLoading:_,isError:I}=(0,y.I)({queryKey:["projects",null==D?void 0:D.id,z],queryFn:()=>(0,f.kf)({projectId:z}),enabled:!!z&&!!(null==D?void 0:D.id)});return s?_?(0,a.jsx)(j.A,{}):E&&null!==z?I?(0,a.jsx)("p",{className:"text-red-500"}):(0,a.jsxs)("div",{className:"flex flex-col gap-8",children:[(0,a.jsxs)("section",{className:"flex flex-col gap-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("span",{className:"text-lg flex items-center gap-2 font-medium capitalize",children:[(0,a.jsx)(r.A,{size:18})," ",k("description")]}),(0,a.jsxs)("span",{className:"rounded-full px-2 py-1 bg-accent-200 text-accent-700 font-medium text-sm flex items-center gap-2",children:[(0,a.jsx)("span",{className:"size-2 rounded-full bg-accent-700"}),(null==M?void 0:M.status)?k(M.status):k("unknownStatus")]})]}),(0,a.jsx)("p",{children:null==M?void 0:M.description})]}),(0,a.jsxs)("section",{className:"grid grid-cols-3 gap-8",children:[(0,a.jsxs)("div",{className:"rounded-md border-2 border-neutral-400 p-8 flex items-center justify-center flex-col gap-2",children:[(0,a.jsxs)("span",{className:"label-text capitalize",children:[(0,a.jsx)(l.A,{size:16}),k("questions")]}),(0,a.jsx)("span",{className:"text-lg font-medium",children:null==M||null==(e=M.questions)?void 0:e.length})]}),(0,a.jsxs)("div",{className:"rounded-md border-2 border-neutral-400 p-8 flex items-center justify-center flex-col gap-2",children:[(0,a.jsxs)("span",{className:"label-text capitalize",children:[(0,a.jsx)(n.A,{size:16}),k("owner")]}),(0,a.jsx)("span",{className:"text-lg font-medium",children:null==M?void 0:M.user.name})]}),(0,a.jsxs)("div",{className:"rounded-md border-2 border-neutral-400 p-8 flex items-center justify-center text-center flex-col gap-2",children:[(0,a.jsxs)("span",{className:"label-text capitalize",children:[(0,a.jsx)(i.A,{size:16}),k("sector")]}),(0,a.jsx)("span",{className:"text-lg font-medium w-full truncate",children:null==M?void 0:M.sector})]}),(0,a.jsxs)("div",{className:"flex flex-col gap-4",children:[(null==M?void 0:M.status)==="deployed"&&(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-lg flex items-center gap-2 font-medium capitalize pb-3",children:k("collectData")}),(0,a.jsxs)("div",{className:"flex gap-3",children:[(0,a.jsx)("button",{className:"btn-primary",onClick:()=>{if(E){let e="".concat(window.location.origin,"/form-submission/").concat(E);navigator.clipboard.writeText(e).then(()=>{S((0,w.Ds)({message:k("linkCopied"),type:"success"}))}).catch(e=>{console.error("Failed to copy: ",e),S((0,w.Ds)({message:k("copyFailed"),type:"error"}))})}else S((0,w.Ds)({message:k("invalidLink"),type:"warning"}))},children:k("copy")}),(0,a.jsx)("button",{className:"btn-primary",onClick:()=>{E?window.open("/form-submission/".concat(E),"_blank"):console.error("hashedId is missing")},children:k("open")})]})]}),(0,a.jsxs)("span",{className:"text-lg flex items-center gap-2 font-medium capitalize",children:[(0,a.jsx)(o.A,{size:18}),k("timeline")]}),(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)("div",{className:"rounded-full p-2 bg-primary-200",children:(0,a.jsx)(c.A,{size:16,className:"text-primary-500"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"capitalize",children:k("lastModified")}),(0,a.jsxs)("span",{className:"label-text",children:[(0,a.jsx)(d.A,{size:16}),(null==M?void 0:M.updatedAt)?(0,x.GP)(new Date(M.updatedAt),"MMMM d, yyyy h:mm a"):"N/A"]})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)("div",{className:"rounded-full p-2 bg-primary-200",children:(0,a.jsx)(u.A,{size:16,className:"text-primary-500"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"capitalize",children:k("lastDeployed")}),(0,a.jsxs)("span",{className:"label-text",children:[(0,a.jsx)(d.A,{size:16}),(null==M?void 0:M.lastDeployedAt)?(0,x.GP)(new Date(M.lastDeployedAt),"MMMM d, yyyy h:mm a"):k("notDeployedYet")]})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)("div",{className:"rounded-full p-2 bg-primary-200",children:(0,a.jsx)(p.A,{size:16,className:"text-primary-500"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"capitalize",children:k("latestSubmission")}),(0,a.jsxs)("span",{className:"label-text",children:[(0,a.jsx)(d.A,{size:16}),(null==M?void 0:M.lastSubmissionAt)?(0,x.GP)(new Date(M.lastSubmissionAt),"MMMM d, yyyy h:mm a"):k("noSubmissionsYet")]})]})]})]})]}),(0,a.jsxs)("section",{className:"label-text cursor-default",children:[(0,a.jsx)(m.A,{size:16})," ",null==M?void 0:M.country]})]}):(0,a.jsxs)("div",{className:"error-message",children:[(0,a.jsxs)("h1",{className:"text-red-500",children:[" ",k("invalidProjectId")," (hashedId)."]}),(0,a.jsx)("p",{className:"text-neutral-700",children:k("invalidProjectUrl")})]}):null}},97381:(e,s,t)=>{"use strict";t.d(s,{Ay:()=>o,Le:()=>n,jB:()=>i,tQ:()=>r,x9:()=>l});let a=(0,t(51990).Z0)({name:"auth",initialState:{status:"loading",user:null,error:null},reducers:{setAuthenticatedUser:(e,s)=>{e.status="authenticated",e.user=s.payload,e.error=null},setUnauthenticated:e=>{e.status="unauthenticated",e.user=null,e.error=null},setAuthLoading:e=>{e.status="loading"},setAuthError:(e,s)=>{e.status="unauthenticated",e.error=s.payload,e.user=null}}}),{setAuthenticatedUser:r,setUnauthenticated:l,setAuthLoading:n,setAuthError:i}=a.actions,o=a.reducer}},e=>{var s=s=>e(e.s=s);e.O(0,[6453,635,1111,6967,9373,7836,473,8441,1684,7358],()=>s(23950)),_N_E=e.O()}]);