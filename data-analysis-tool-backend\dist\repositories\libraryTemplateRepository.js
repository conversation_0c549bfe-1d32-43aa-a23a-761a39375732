"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const prisma_1 = require("../utils/prisma");
class LibraryTemplateRepository {
    findByName(name, userId) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.libraryTemplate.findUnique({
                where: {
                    name_userId: {
                        name,
                        userId,
                    },
                },
            });
        });
    }
    findById(id) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.libraryTemplate.findUnique({
                where: { id },
            });
        });
    }
    findLibraryTemplateByIdAndUser(id, userId) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.libraryTemplate.findFirst({
                where: {
                    id,
                    userId,
                },
                include: {
                    user: true,
                    libraryQuestions: true,
                },
            });
        });
    }
    findAll(id) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.libraryTemplate.findMany({
                where: {
                    userId: id,
                },
                include: {
                    user: true,
                },
            });
        });
    }
    create(libraryTemplateData) {
        return __awaiter(this, void 0, void 0, function* () {
            const { name, description, sector, userId, country } = libraryTemplateData;
            return yield prisma_1.prisma.libraryTemplate.create({
                data: {
                    name,
                    description,
                    sector,
                    userId,
                    country,
                },
            });
        });
    }
    updateById(id, updateData) {
        return __awaiter(this, void 0, void 0, function* () {
            const data = {};
            if (updateData.name !== undefined)
                data.name = updateData.name;
            if (updateData.description !== undefined)
                data.description = updateData.description;
            if (updateData.sector !== undefined)
                data.sector = updateData.sector;
            if (updateData.country !== undefined)
                data.country = updateData.country;
            return yield prisma_1.prisma.libraryTemplate.update({
                where: { id },
                data,
            });
        });
    }
    deleteLibraryTemplate(id) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.libraryTemplate.delete({
                where: { id },
            });
        });
    }
    isOwner(userId, templateId) {
        return __awaiter(this, void 0, void 0, function* () {
            const template = yield prisma_1.prisma.libraryTemplate.findFirst({
                where: {
                    id: templateId,
                    userId: userId,
                },
            });
            return template !== null;
        });
    }
    findLibraryWithQuestionGroupAndQuestion(id) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.libraryTemplate.findUnique({
                where: { id },
                include: {
                    LibraryTemplateQuestionGroup: {
                        include: {
                            libraryQuestions: {
                                include: {
                                    questionOptions: true,
                                    questionConditions: true,
                                },
                                orderBy: { position: "asc" },
                            },
                            subGroups: {
                                include: {
                                    libraryQuestions: {
                                        include: {
                                            questionConditions: true,
                                            questionOptions: true,
                                        },
                                        orderBy: { position: "asc" },
                                    },
                                },
                            },
                        },
                        orderBy: { order: "asc" },
                    },
                    libraryQuestions: {
                        where: { libraryTemplateQuestionGroupId: null },
                        include: {
                            questionOptions: true,
                            questionConditions: true,
                        },
                        orderBy: { position: "asc" },
                    },
                },
            });
        });
    }
    deleteMultipleLibraryTemplate(ids) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.libraryTemplate.deleteMany({
                where: { id: { in: ids } },
            });
        });
    }
}
exports.default = new LibraryTemplateRepository();
