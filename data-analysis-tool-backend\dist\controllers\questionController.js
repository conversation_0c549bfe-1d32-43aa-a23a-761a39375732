"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateUnifiedPositions = exports.updateQuestionPositions = exports.duplicateQuestion = exports.deleteQuestion = exports.updateQuestion = exports.createQuestion = exports.getAllQuestion = void 0;
const ApiResponse_1 = require("../utils/ApiResponse");
const questionValidators_1 = require("../validators/questionValidators");
const client_1 = require("@prisma/client");
const questionRepository_1 = __importDefault(require("../repositories/questionRepository"));
const questionRepository_2 = __importDefault(require("../repositories/questionRepository"));
const prisma_1 = require("../utils/prisma");
const exceljs_1 = __importDefault(require("exceljs"));
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const getAllQuestion = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        if (!req.user || !req.user.id) {
            return res.status(404).json({
                success: false,
                message: "user not found",
            });
        }
        const userId = Number(req.user.id);
        // Get projectId from either path parameter or query parameter
        const projectId = Number(req.params.projectId || req.query.projectId);
        if (!projectId) {
            return res.status(400).json({
                success: false,
                message: "Project ID is required",
            });
        }
        const questions = yield questionRepository_2.default.findAll(projectId);
        return res
            .status(200)
            .json({ message: "Successfully fetched questions.", questions });
    }
    catch (error) {
        return res.status(500).json({
            success: false,
            message: "error fetching questions",
            error: error instanceof Error ? error.message : "unexpected error",
        });
    }
});
exports.getAllQuestion = getAllQuestion;
const createQuestion = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const projectId = Number(req.params.projectId);
        // Pre-process form data for proper type conversion
        if (req.file) {
            // Convert string values to appropriate types
            if (req.body.isRequired !== undefined) {
                req.body.isRequired = req.body.isRequired === "true";
            }
            if (req.body.position !== undefined) {
                req.body.position = Number(req.body.position);
            }
            // For file uploads, we don't expect questionOptions in the request body
            // They will be parsed from the file
            req.body.questionOptions = undefined;
        }
        // Choose the appropriate validation schema based on whether a file is uploaded
        const validationSchema = req.file ? questionValidators_1.questionWithFileSchema : questionValidators_1.questionSchema;
        // Parse and validate the body
        const result = validationSchema.safeParse(req.body);
        if (!result.success) {
            res.status(400).json({
                success: false,
                errors: result.error.flatten().fieldErrors,
            });
            return;
        }
        const _a = result.data, { questionOptions = [], conditions = [] } = _a, questionData = __rest(_a, ["questionOptions", "conditions"]);
        // Handle Excel file if present
        let excelOptions = [];
        if (req.file) {
            try {
                const workbook = new exceljs_1.default.Workbook();
                const filePath = path_1.default.join(__dirname, "../../tmp/my-uploads", req.file.filename);
                yield workbook.xlsx.readFile(filePath);
                const worksheet = workbook.worksheets[0];
                worksheet.eachRow((row, rowNumber) => {
                    if (rowNumber === 1)
                        return; // Skip header row
                    // Parse data from Excel
                    if (Array.isArray(row.values)) {
                        const [, label, code, nextQuestionIdRaw] = row.values;
                        if (label && code) {
                            excelOptions.push({
                                label: String(label),
                                code: String(code),
                                nextQuestionId: nextQuestionIdRaw
                                    ? Number(nextQuestionIdRaw)
                                    : null,
                            });
                        }
                    }
                });
                // Remove uploaded file
                fs_1.default.unlinkSync(filePath);
                // Validate that we have at least one option from the Excel file
                if ((questionData.inputType === client_1.InputType.selectone ||
                    questionData.inputType === client_1.InputType.selectmany) &&
                    excelOptions.length === 0) {
                    res.status(400).json({
                        success: false,
                        message: "The Excel file must contain at least one valid option for select input types",
                    });
                    return;
                }
            }
            catch (error) {
                console.error("Error processing Excel file:", error);
                res.status(400).json({
                    success: false,
                    message: "Failed to process the Excel file. Please ensure it's a valid Excel file with the correct format.",
                });
                return;
            }
        }
        // Combine options from request body and Excel file
        const allOptions = [...(questionOptions || []), ...excelOptions];
        // For select input types, ensure we have options (either from body or Excel)
        if ((questionData.inputType === client_1.InputType.selectone ||
            questionData.inputType === client_1.InputType.selectmany) &&
            allOptions.length === 0) {
            res.status(400).json({
                success: false,
                message: "Options are required for select input types",
            });
            return;
        }
        // Start transaction
        const createdQuestion = yield prisma_1.prisma.$transaction((tx) => __awaiter(void 0, void 0, void 0, function* () {
            var _a, _b, _c;
            const question = yield tx.question.create({
                data: Object.assign(Object.assign({}, questionData), { projectId }),
            });
            if (allOptions.length > 0) {
                yield tx.questionOption.createMany({
                    data: allOptions.map((option) => ({
                        label: option.label,
                        sublabel: option.sublabel,
                        code: option.code,
                        nextQuestionId: option.nextQuestionId || null,
                        questionId: question.id,
                    })),
                });
            }
            if (conditions && conditions.length > 0) {
                yield tx.questionCondition.createMany({
                    data: conditions.map((cond) => ({
                        operator: cond.operator,
                        value: cond.value,
                        questionId: question.id,
                    })),
                });
            }
            const lastOrder = yield tx.projectQuestionOrder.findFirst({
                where: {
                    parentGroupId: (_a = questionData.parentGroupId) !== null && _a !== void 0 ? _a : null,
                },
                orderBy: {
                    position: "desc",
                },
            });
            const nextPosition = lastOrder ? lastOrder.position + 1 : 1;
            yield tx.projectQuestionOrder.create({
                data: {
                    questionId: question.id,
                    groupId: (_b = questionData.groupId) !== null && _b !== void 0 ? _b : null,
                    type: "question",
                    position: nextPosition,
                    parentGroupId: (_c = questionData.parentGroupId) !== null && _c !== void 0 ? _c : null,
                    projectId: projectId
                },
            });
            return question;
        }));
        res
            .status(200)
            .json(new ApiResponse_1.ApiResponse(200, { question: createdQuestion }, "Question created successfully"));
    }
    catch (error) {
        console.error("Error creating question:", error);
        res.status(500).json({
            success: false,
            message: "Failed to create question",
            error: error instanceof Error ? error.message : "Unexpected error",
        });
    }
});
exports.createQuestion = createQuestion;
const updateQuestion = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const id = Number(req.params.id);
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
        if (!userId || isNaN(id)) {
            res.status(400).json({
                success: false,
                message: "Invalid request",
            });
            return;
        }
        const existingQuestion = yield questionRepository_1.default.findById(id);
        if (!existingQuestion) {
            res.status(404).json({
                success: false,
                message: "Question not found",
            });
            return;
        }
        if (existingQuestion.projectId === undefined) {
            res.status(500).json({
                success: false,
                message: "Question has no associated project",
            });
            return;
        }
        const isOwner = yield questionRepository_1.default.isPorjectOwner(userId, existingQuestion.projectId);
        if (!isOwner) {
            res.status(403).json({
                success: false,
                message: "You are not the project owner",
            });
            return;
        }
        // Parse the request body without requiring all fields
        const parseResult = questionValidators_1.questionSchema.safeParse(Object.assign(Object.assign({}, req.body), { 
            // Add dummy values for any required fields that aren't in the update
            // These won't actually be used for the update, just to pass validation
            label: req.body.label || existingQuestion.label, inputType: req.body.inputType || existingQuestion.inputType, position: req.body.position || existingQuestion.position }));
        if (!parseResult.success) {
            res.status(400).json({
                success: false,
                message: "Validation error",
                errors: parseResult.error.errors,
            });
            return;
        }
        // Only include fields that were actually in the request body
        // Only include fields that were actually in the request body
        const validatedData = {};
        const fields = [
            "label",
            "inputType",
            "isRequired",
            "hint",
            "placeholder",
            "position",
            "questionOptions",
            "conditions",
        ];
        for (const field of fields) {
            if (req.body[field] !== undefined) {
                // Rename questionOptions to options
                if (field === "questionOptions") {
                    validatedData.options = parseResult.data[field];
                }
                else {
                    validatedData[field] = parseResult.data[field];
                }
            }
        }
        // Check if options are required for select input types
        if (validatedData.inputType === "selectone" ||
            validatedData.inputType === "selectmany") {
            if (!validatedData.options ||
                !Array.isArray(validatedData.options) ||
                validatedData.options.length === 0) {
                res.status(400).json({
                    success: false,
                    message: "Options must be provided for select input types",
                });
                return;
            }
        }
        // Use the updated repository method to handle options and conditions
        const updatedQuestion = yield questionRepository_1.default.updateById(id, validatedData);
        res
            .status(200)
            .json(new ApiResponse_1.ApiResponse(200, { question: updatedQuestion }, "question updated success"));
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: "error updating question",
            error: error instanceof Error ? error.message : "unexpected error",
        });
        return;
    }
});
exports.updateQuestion = updateQuestion;
const deleteQuestion = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
        const id = Number(req.params.id);
        if (!userId || isNaN(id)) {
            return res.status(400).json({
                message: "Invalid request: User ID or Question ID is missing",
                success: false,
            });
        }
        const currentQuestion = yield questionRepository_2.default.findById(id);
        if (!currentQuestion) {
            return res.status(404).json({
                message: "Question not found",
                success: false,
            });
        }
        const isProjectOwner = yield questionRepository_2.default.isPorjectOwner(userId, currentQuestion.projectId // Add ! to assert that projectId is not undefined
        );
        if (!isProjectOwner) {
            return res.status(403).json({
                message: "Current user cannot delete question from this project",
                succcess: false,
            });
        }
        yield questionRepository_2.default.deleteQuestion(id);
        return res.status(200).json({
            message: "Successfully deleted question",
            success: true,
        });
    }
    catch (error) {
        return res.status(500).json({
            message: error instanceof Error ? error.message : "unexpected error",
            success: false,
        });
    }
});
exports.deleteQuestion = deleteQuestion;
const duplicateQuestion = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
        const id = Number(req.params.id);
        if (!userId || isNaN(id)) {
            return res.status(400).json({
                message: "Invalid request: User ID or Question ID is missing",
                success: false,
            });
        }
        const currentQuestion = yield questionRepository_2.default.findById(id);
        if (!currentQuestion) {
            return res.status(404).json({
                message: "Question not found",
                success: false,
            });
        }
        const isProjectOwner = yield questionRepository_2.default.isPorjectOwner(userId, currentQuestion.projectId);
        if (!isProjectOwner) {
            return res.status(403).json({
                message: "Current user cannot delete question from this project",
                succcess: false,
            });
        }
        const duplicatedQuestion = yield questionRepository_2.default.duplicateQuestion(id, currentQuestion.projectId);
        return res.status(200).json({
            message: "Successfully duplicated the question.",
            success: true,
            duplicatedQuestion,
        });
    }
    catch (error) {
        return res.status(500).json({
            message: error instanceof Error ? error.message : "unexpected error",
            success: false,
        });
    }
});
exports.duplicateQuestion = duplicateQuestion;
const updateQuestionPositions = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
        const projectId = Number(req.query.projectId);
        if (!userId || isNaN(projectId)) {
            res.status(400).json({
                success: false,
                message: "Invalid request - missing userId or projectId",
            });
            return;
        }
        // Validate request body structure using the schema
        const parseResult = questionValidators_1.questionPositionsSchema.safeParse(req.body);
        if (!parseResult.success) {
            res.status(400).json({
                success: false,
                message: "Validation error",
                errors: parseResult.error.flatten().fieldErrors,
            });
            return;
        }
        const { questionPositions } = parseResult.data;
        // Check if user owns the project
        const isOwner = yield questionRepository_2.default.isPorjectOwner(userId, projectId);
        if (!isOwner) {
            res.status(403).json({
                success: false,
                message: "You are not the project owner",
            });
            return;
        }
        // Update positions in bulk
        const updatedQuestions = yield questionRepository_2.default.updateMultiplePositions(questionPositions);
        res.status(200).json({
            success: true,
            message: "Question positions updated successfully",
            data: { questions: updatedQuestions },
        });
    }
    catch (error) {
        console.error("Error in updateQuestionPositions:", error);
        res.status(500).json({
            success: false,
            message: "Error updating question positions",
            error: error instanceof Error ? error.message : "Unexpected error",
        });
    }
});
exports.updateQuestionPositions = updateQuestionPositions;
// NEW UNIFIED POSITION SYSTEM: Update positions using ProjectQuestionOrder schema
const updateUnifiedPositions = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
        const projectId = Number(req.query.projectId);
        if (!userId || isNaN(projectId)) {
            res.status(400).json({
                success: false,
                message: "Invalid request - missing userId or projectId",
            });
            return;
        }
        const { positionUpdates } = req.body;
        if (!positionUpdates || !Array.isArray(positionUpdates)) {
            res.status(400).json({
                success: false,
                message: "Invalid request - positionUpdates array is required",
            });
            return;
        }
        // Validate each position update
        for (const update of positionUpdates) {
            if (!update.id || !update.type || typeof update.position !== 'number') {
                res.status(400).json({
                    success: false,
                    message: "Invalid position update - id, type, and position are required",
                });
                return;
            }
            if (!['question', 'group'].includes(update.type)) {
                res.status(400).json({
                    success: false,
                    message: "Invalid type - must be 'question' or 'group'",
                });
                return;
            }
        }
        // Check if user owns the project
        const isOwner = yield questionRepository_2.default.isPorjectOwner(userId, projectId);
        if (!isOwner) {
            res.status(403).json({
                success: false,
                message: "You are not the project owner",
            });
            return;
        }
        // Update positions using unified system
        const result = yield questionRepository_2.default.updateUnifiedPositions(projectId, positionUpdates);
        res.status(200).json({
            success: true,
            message: "Positions updated successfully using unified system",
            data: result,
        });
    }
    catch (error) {
        console.error("Error in updateUnifiedPositions:", error);
        res.status(500).json({
            success: false,
            message: "Error updating unified positions",
            error: error instanceof Error ? error.message : "Unexpected error",
        });
    }
});
exports.updateUnifiedPositions = updateUnifiedPositions;
