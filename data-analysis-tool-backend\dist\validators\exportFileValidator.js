"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExportedFileValidator = void 0;
const zod_1 = require("zod");
// Validator for the ExportedFile model
exports.ExportedFileValidator = zod_1.z.object({
    projectId: zod_1.z.number().optional(), // projectId is optional
    userId: zod_1.z.number().optional(), // userId is optional
    fileName: zod_1.z.string().min(1, "File name is required"), // Ensure file name is not empty
    fileType: zod_1.z.string().min(1, "File type is required"), // Ensure file type is not empty
    contentType: zod_1.z.string().min(1, "Content type is required"), // Ensure content type is not empty
    fileBuffer: zod_1.z
        .instanceof(Buffer)
        .refine((buffer) => buffer.length > 0, "File buffer cannot be empty"), // Ensure fileBuffer is a non-empty buffer
    createdAt: zod_1.z
        .date()
        .optional()
        .default(() => new Date()), // createdAt is optional and defaults to current date if not provided
});
