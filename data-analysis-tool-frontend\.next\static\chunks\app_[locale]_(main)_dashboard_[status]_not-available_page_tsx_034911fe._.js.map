{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/app/%5Blocale%5D/%28main%29/dashboard/%5Bstatus%5D/not-available/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { useParams } from \"next/navigation\";\r\nimport { BsFolderPlus } from \"react-icons/bs\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { showCreateProjectModal } from \"@/redux/slices/createProjectSlice\";\r\nimport { useTranslations } from \"next-intl\";\r\n\r\nconst NotAvailablePage = () => {\r\n  const { status } = useParams();\r\n  const dispatch = useDispatch();\r\n  const t = useTranslations();\r\n  \r\n  const getStatusTranslated = () => {\r\n    if (!status || typeof status !== 'string') return t('projects');\r\n    return t(`status2.${status}`);\r\n  };\r\n\r\n  const handleCreateProject = () => {\r\n    dispatch(showCreateProjectModal());\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex flex-col items-center justify-center py-16 px-4 min-h-[70vh]\">\r\n      <div className=\"bg-neutral-100 rounded-lg shadow-sm p-8 max-w-md w-full text-center\">\r\n        <div className=\"flex justify-center mb-6\">\r\n          <div className=\"bg-neutral-200 p-5 rounded-full\">\r\n            <BsFolderPlus size={50} className=\"text-primary-500\" />\r\n          </div>\r\n        </div>\r\n        <h2 className=\"text-2xl font-semibold text-neutral-800 mb-2\">\r\n  {t('noProjects', { status: getStatusTranslated() })}\r\n</h2>\r\n        <p className=\"text-neutral-600 mb-8\">\r\n          {status === \"draft\" && t('noDraftProjects')}\r\n          {status === \"deployed\" && t('noDeployedProjects')}\r\n          {status === \"archived\" && t('noArchivedProjects')}\r\n          {!status && t('noProjectsInCategory')}\r\n        </p>\r\n        \r\n        {status === \"draft\" && (\r\n          <button \r\n            onClick={handleCreateProject}\r\n            className=\"btn-primary w-full\"\r\n          >\r\n            {t('createNewProject')}\r\n          </button>\r\n        )}\r\n        \r\n        {status === \"deployed\" && (\r\n          <div className=\"text-sm text-neutral-500\">\r\n            {t('createAndDeployProject')}\r\n          </div>\r\n        )}\r\n        \r\n        {status === \"archived\" && (\r\n          <div className=\"text-sm text-neutral-500\">\r\n            {t('archiveProjectsInfo')}\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default NotAvailablePage; "], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;AASA,MAAM,mBAAmB;;IACvB,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAC3B,MAAM,WAAW,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD;IAExB,MAAM,sBAAsB;QAC1B,IAAI,CAAC,UAAU,OAAO,WAAW,UAAU,OAAO,EAAE;QACpD,OAAO,EAAE,CAAC,QAAQ,EAAE,QAAQ;IAC9B;IAEA,MAAM,sBAAsB;QAC1B,SAAS,CAAA,GAAA,wIAAA,CAAA,yBAAsB,AAAD;IAChC;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,iJAAA,CAAA,eAAY;4BAAC,MAAM;4BAAI,WAAU;;;;;;;;;;;;;;;;8BAGtC,6LAAC;oBAAG,WAAU;8BACnB,EAAE,cAAc;wBAAE,QAAQ;oBAAsB;;;;;;8BAE3C,6LAAC;oBAAE,WAAU;;wBACV,WAAW,WAAW,EAAE;wBACxB,WAAW,cAAc,EAAE;wBAC3B,WAAW,cAAc,EAAE;wBAC3B,CAAC,UAAU,EAAE;;;;;;;gBAGf,WAAW,yBACV,6LAAC;oBACC,SAAS;oBACT,WAAU;8BAET,EAAE;;;;;;gBAIN,WAAW,4BACV,6LAAC;oBAAI,WAAU;8BACZ,EAAE;;;;;;gBAIN,WAAW,4BACV,6LAAC;oBAAI,WAAU;8BACZ,EAAE;;;;;;;;;;;;;;;;;AAMf;GAvDM;;QACe,qIAAA,CAAA,YAAS;QACX,4JAAA,CAAA,cAAW;QAClB,yMAAA,CAAA,kBAAe;;;KAHrB;uCAyDS", "debugId": null}}]}