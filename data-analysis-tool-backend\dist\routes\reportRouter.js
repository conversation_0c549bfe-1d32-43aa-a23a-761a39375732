"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const auth_1 = require("../middleware/auth");
const reportController_1 = require("../controllers/reportController");
const router = express_1.default.Router();
// Get project report with optional filters
router.get("/projects/:projectId/report", auth_1.authenticate, reportController_1.getProjectReport);
exports.default = router;
