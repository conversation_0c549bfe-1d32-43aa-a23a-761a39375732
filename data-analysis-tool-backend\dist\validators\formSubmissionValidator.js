"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateFormSubmissionSchema = exports.formSubmissionSchema = void 0;
const zod_1 = require("zod");
exports.formSubmissionSchema = zod_1.z.object({
    projectId: zod_1.z.number(),
    status: zod_1.z.string().optional(),
    deviceInfo: zod_1.z.string().optional(),
    loginRequired: zod_1.z.boolean().optional(),
    location: zod_1.z.string().optional(),
    metadata: zod_1.z.record(zod_1.z.any()).optional(),
    startedAt: zod_1.z.coerce.date().optional(),
});
exports.updateFormSubmissionSchema = zod_1.z.object({
    status: zod_1.z.string().optional(),
    completedAt: zod_1.z.coerce.date().optional(),
    durationSeconds: zod_1.z.number().optional(),
    loginRequired: zod_1.z.boolean().optional(),
    metadata: zod_1.z.record(zod_1.z.any()).optional(),
    submittedAt: zod_1.z.coerce.date().optional(),
});
