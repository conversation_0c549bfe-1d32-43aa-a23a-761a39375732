exports.id=6226,exports.ids=[6226],exports.modules={10125:(e,t,r)=>{"use strict";r.d(t,{Notification:()=>m});var i=r(60687),s=r(43210),n=r(54864),o=r(88920),a=r(57101),l=r(19150),c=r(14719),d=r(43649),u=r(93613);let m=()=>{let e=(0,n.wA)(),{message:t,type:r,visible:m}=(0,n.d4)(e=>e.notification);(0,s.useEffect)(()=>{if(m){let t=setTimeout(()=>{e((0,l._b)())},5e3);return()=>clearTimeout(t)}},[m,e]);let h="success"===r?(0,i.jsx)(c.A,{}):"warning"===r?(0,i.jsx)(d.A,{}):(0,i.jsx)(u.A,{});return(0,i.jsx)(o.N,{children:m&&(0,i.jsxs)(a.P.div,{className:`z-50 fixed top-0 right-0 m-4 px-4 py-2 rounded font-semibold w-auto max-w-xs flex items-center gap-2 cursor-pointer ${"success"===r?"bg-green-500 hover:bg-green-600":"warning"===r?"bg-yellow-500 hover:bg-yellow-600":"bg-red-500 hover:bg-red-600"} transition-colors duration-300`,onClick:()=>e((0,l._b)()),initial:{opacity:0,y:-20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.3,ease:"easeIn"},children:[(0,i.jsx)("span",{className:"text-2xl",children:h}),(0,i.jsx)("span",{className:"break-words neutral-100space-normal",children:t})]})})}},10271:(e,t,r)=>{"use strict";r.d(t,{ReactQueryProvider:()=>l});var i=r(60687),s=r(43210),n=r(39091),o=r(8693),a=r(9124);let l=({children:e})=>{let[t]=(0,s.useState)(()=>new n.E({defaultOptions:{queries:{staleTime:3e5,refetchOnWindowFocus:!1}}}));return(0,i.jsxs)(o.Ht,{client:t,children:[e,(0,i.jsx)(a.E,{initialIsOpen:!1})]})}},12810:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let i=r(51060).A.create({baseURL:"http://localhost:4000/api",headers:{"Content-Type":"application/json"},withCredentials:!0});i.interceptors.request.use(e=>e,e=>Promise.reject(e)),i.interceptors.response.use(e=>e,e=>("ERR_NETWORK"===e.code&&console.error("Network error - Please check if the backend server is running"),Promise.reject(e)));let s=i},16319:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},19150:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>o,Ds:()=>s,_b:()=>n});let i=(0,r(9317).Z0)({name:"notification",initialState:{message:"",type:"",visible:!1},reducers:{showNotification:(e,t)=>{e.message=t.payload.message,e.type=t.payload.type,e.visible=!0},hideNotification:e=>{e.message="",e.type="",e.visible=!1}}}),{showNotification:s,hideNotification:n}=i.actions,o=i.reducer},26946:(e,t,r)=>{Promise.resolve().then(r.bind(r,10125)),Promise.resolve().then(r.bind(r,10271)),Promise.resolve().then(r.bind(r,49271))},35790:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>o,l:()=>n,yg:()=>s});let i=(0,r(9317).Z0)({name:"createLibraryItem",initialState:{visible:!1},reducers:{showCreateLibraryModal:e=>{e.visible=!0},hideCreateLibraryModal:e=>{e.visible=!1}}}),{showCreateLibraryModal:s,hideCreateLibraryModal:n}=i.actions,o=i.reducer},36039:(e,t,r)=>{Promise.resolve().then(r.bind(r,45196))},42895:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>l,Le:()=>o,jB:()=>a,tQ:()=>s,x9:()=>n});let i=(0,r(9317).Z0)({name:"auth",initialState:{status:"loading",user:null,error:null},reducers:{setAuthenticatedUser:(e,t)=>{e.status="authenticated",e.user=t.payload,e.error=null},setUnauthenticated:e=>{e.status="unauthenticated",e.user=null,e.error=null},setAuthLoading:e=>{e.status="loading"},setAuthError:(e,t)=>{e.status="unauthenticated",e.error=t.payload,e.user=null}}}),{setAuthenticatedUser:s,setUnauthenticated:n,setAuthLoading:o,setAuthError:a}=i.actions,l=i.reducer},44395:(e,t,r)=>{"use strict";r.d(t,{Notification:()=>i});let i=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call Notification() from the server but Notification is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\components\\general\\Notification.tsx","Notification")},46055:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var i=r(31658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,i.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},49271:(e,t,r)=>{"use strict";r.d(t,{ReduxProvider:()=>m});var i=r(60687),s=r(9317),n=r(19150),o=r(58432),a=r(42895),l=r(35790),c=r(89011);let d=(0,s.U1)({reducer:{notification:n.Ay,createProject:o.Ay,auth:a.Ay,createLibrary:l.Ay,createLibraryItem:c.Ay}});r(43210);var u=r(54864);let m=({children:e})=>(0,i.jsx)(u.Kq,{store:d,children:e})},50823:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},52911:(e,t,r)=>{Promise.resolve().then(r.bind(r,80994))},58014:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>m,metadata:()=>u});var i=r(37413);r(82704);var s=r(7990),n=r.n(s),o=r(60866),a=r.n(o),l=r(77832),c=r(44395),d=r(60265);let u={title:"Data analysis tool",description:"A tool for data collection and analysis."};function m({children:e}){return(0,i.jsx)("html",{lang:"en",children:(0,i.jsx)("body",{className:`${n().className} ${a().className} antialiased`,children:(0,i.jsx)(l.ReduxProvider,{children:(0,i.jsxs)(d.ReactQueryProvider,{children:[(0,i.jsx)(c.Notification,{}),(0,i.jsx)("main",{className:"bg-neutral-200",children:e})]})})})})}},58432:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>o,Gl:()=>s,th:()=>n});let i=(0,r(9317).Z0)({name:"createProject",initialState:{visible:!1},reducers:{showCreateProjectModal:e=>{e.visible=!0},hideCreateProjectModal:e=>{e.visible=!1}}}),{showCreateProjectModal:s,hideCreateProjectModal:n}=i.actions,o=i.reducer},60265:(e,t,r)=>{"use strict";r.d(t,{ReactQueryProvider:()=>i});let i=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call ReactQueryProvider() from the server but ReactQueryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\providers\\ReactQueryProvider.tsx","ReactQueryProvider")},64668:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var i=r(60687),s=r(8610),n=r(16189),o=r(85814),a=r.n(o);function l(){let e=(0,s.Ym)(),t=(0,n.usePathname)(),r=e=>{let r=t.replace(/^\/(en|ne)/,"");return`/${e}${r}`};return(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(a(),{href:r("en"),className:`px-3 py-1 rounded ${"en"===e?"bg-primary-600 text-white":"bg-gray-200"}`,children:"English"}),(0,i.jsx)(a(),{href:r("ne"),className:`px-3 py-1 rounded ${"ne"===e?"bg-primary-600 text-white":"bg-gray-200"}`,children:"नेपाली"})]})}},72121:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c,generateStaticParams:()=>l});var i=r(37413),s=r(60958),n=r(39916),o=r(81015);let a=["en","ne"];function l(){return a.map(e=>({locale:e}))}async function c({children:e,params:t}){let{locale:r}=await t;a.includes(r)||(0,n.notFound)();let l=await (0,o.V)(r);return(0,i.jsx)(s.A,{locale:r,messages:l,children:e})}},76565:(e,t,r)=>{var i={"./en.json":[87368,7368],"./ne.json":[3018,3018]};function s(e){if(!r.o(i,e))return Promise.resolve().then(()=>{var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t});var t=i[e],s=t[0];return r.e(t[1]).then(()=>r.t(s,19))}s.keys=()=>Object.keys(i),s.id=76565,e.exports=s},77832:(e,t,r)=>{"use strict";r.d(t,{ReduxProvider:()=>i});let i=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call ReduxProvider() from the server but ReduxProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\providers\\ReduxProvider.tsx","ReduxProvider")},81015:(e,t,r)=>{"use strict";r.d(t,{A:()=>o,V:()=>n});var i=r(35471);let s=["en","ne"];async function n(e){s.includes(e)||(console.warn(`Unsupported locale: ${e}, falling back to 'en'`),e="en");try{let t=(await r(76565)(`./${e}.json`)).default;if(!t||"object"!=typeof t)throw Error(`Invalid messages format for locale: ${e}`);return t}catch(t){if(console.error(`Failed to load messages for locale: ${e}`,t),"en"!==e)try{return console.log("Falling back to English messages"),(await r.e(7368).then(r.t.bind(r,87368,19))).default}catch(e){console.error("Failed to load fallback English messages",e)}return{}}}let o=(0,i.A)(async({locale:e})=>{let t=e?.toString()||"en";return{locale:t,messages:await n(t),timeZone:"Asia/Kathmandu",formats:{dateTime:{short:{day:"numeric",month:"short",year:"numeric"},medium:{day:"numeric",month:"long",year:"numeric"},long:{weekday:"long",day:"numeric",month:"long",year:"numeric"}},number:{currency:{style:"currency",currency:"NPR"}}}}})},82704:()=>{},86778:(e,t,r)=>{Promise.resolve().then(r.bind(r,44395)),Promise.resolve().then(r.bind(r,60265)),Promise.resolve().then(r.bind(r,77832))},89011:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>o,dQ:()=>s,g7:()=>n});let i=(0,r(9317).Z0)({initialState:{visible:!1,option:""},name:"createLibraryItem",reducers:{showCreateLibraryItemModal:(e,t)=>{e.visible=!0,e.option=t.payload},hideCreateLibraryItemModal:e=>{e.visible=!1,e.option=""}}}),{showCreateLibraryItemModal:s,hideCreateLibraryItemModal:n}=i.actions,o=i.reducer}};