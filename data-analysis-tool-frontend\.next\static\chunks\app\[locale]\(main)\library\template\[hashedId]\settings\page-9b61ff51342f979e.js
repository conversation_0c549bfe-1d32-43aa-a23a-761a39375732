(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6278],{2511:(e,a,t)=>{"use strict";t.d(a,{b:()=>n});let n={information_media:"Information / Media",econommic_social_development:"Economic & Social Development",security_police_peacekeeping:"Security / Police / Peacekeeping",disarmament_and_demobilization:"Disarmament & Demobilization",environment:"Environment",private_sector:"Private Sector",humanitarian_coordination_information_management:"Humanitarian - Coordination & Info Management",humanitarian_multiple_clusters:"Humanitarian - Multiple Clusters",humanitarian_camp_management_and_coordination:"Humanitarian - Camp Management & Coordination",humanitarian_early_recovery:"Humanitarian - Early Recovery",humanitarian_education:"Humanitarian - Education",humanitarian_emergency_shelter:"Humanitarian - Emergency Shelter",humanitarian_emergency_telecoms:"Humanitarian - Emergency Telecoms",humanitarian_food_security:"Humanitarian - Food Security",humanitarian_health:"Humanitarian - Health",humanitarian_logistics:"Humanitarian - Logistics",humanitarian_nutrition:"Humanitarian - Nutrition",humanitarian_protection:"Humanitarian - Protection",humanitarian_sanitation_water_and_hygiene:"Humanitarian - Sanitation / Water / Hygiene",other:"Other"}},13163:(e,a,t)=>{"use strict";t.d(a,{A:()=>o});var n=t(95155),i=t(60760),r=t(44518),s=t(95233),l=t(54416);t(12115);let o=e=>{let{children:a,className:t,isOpen:o,onClose:u,preventOutsideClick:c=!1}=e;return(0,n.jsx)(i.N,{children:o&&(0,n.jsx)(r.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-neutral-900/50 flex items-center justify-center z-40 p-4 overflow-y-auto",onClick:e=>{c||u()},children:(0,n.jsxs)(r.P.div,{initial:{scale:.6,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.6,opacity:0},transition:{duration:.3,ease:s.am},className:"relative section flex flex-col max-w-4xl max-h-[90vh] overflow-y-auto ".concat(t),onClick:e=>e.stopPropagation(),children:[(0,n.jsx)(l.A,{onClick:u,className:"absolute self-end cursor-pointer text-neutral-700 hover:text-neutral-900 active:scale-90 transition-all duration-300"}),a]})})})}},25784:(e,a,t)=>{"use strict";t.d(a,{A:()=>i});let n=t(23464).A.create({baseURL:"http://localhost:4000/api",headers:{"Content-Type":"application/json"},withCredentials:!0});n.interceptors.request.use(e=>e,e=>Promise.reject(e)),n.interceptors.response.use(e=>e,e=>("ERR_NETWORK"===e.code&&console.error("Network error - Please check if the backend server is running"),Promise.reject(e)));let i=n},29350:(e,a,t)=>{"use strict";t.d(a,{A:()=>u});var n=t(97381),i=t(59362),r=t(25784),s=t(35695),l=t(12115),o=t(34540);let u=e=>{let a=(0,o.wA)(),t=(0,s.useRouter)(),u=(0,s.usePathname)(),{status:c,user:d,error:m}=(0,o.d4)(e=>e.auth),p=async()=>{try{a((0,n.Le)());let e=(await r.A.get("/users/me")).data;a((0,n.tQ)(e))}catch(r){if(a((0,n.x9)()),(0,i.F0)(r)){var e,s,l,o,c;if(console.error("Auth error:",null==(e=r.response)?void 0:e.status,null==(s=r.response)?void 0:s.data),(null==(l=r.response)?void 0:l.status)===401){if(u.startsWith("/form-submission"))return;t.push("/")}else a((0,n.jB)((null==(c=r.response)||null==(o=c.data)?void 0:o.message)||r.message))}else a((0,n.jB)(r instanceof Error?r.message:"An unknown error occurred."))}};return(0,l.useEffect)(()=>{(null==e?void 0:e.skipFetchUser)||p()},[null==e?void 0:e.skipFetchUser]),(0,l.useEffect)(()=>{let e=e=>{if("logout"===e.key&&"true"===e.newValue)if(a((0,n.x9)()),u.startsWith("/form-submission")){let e=u.split("/")[2];e?t.push("/form-submission/".concat(e,"/sign-in")):t.push("/")}else t.push("/")};return window.addEventListener("storage",e),()=>{window.removeEventListener("storage",e)}},[a,t,u]),{status:c,user:d,error:m,isAuthenticated:"authenticated"===c,isLoading:"loading"===c,refreshAuthState:()=>{p()},signin:async(e,a,t)=>{try{await r.A.post("/users/login",e),await p(),null==a||a()}catch(e){if(e instanceof i.pe){var n,s;let a=null==(s=e.response)||null==(n=s.data)?void 0:n.errorType;null==t||t(a)}else null==t||t()}},logout:async()=>{try{await r.A.post("/users/logout"),localStorage.setItem("logout","true"),setTimeout(()=>localStorage.removeItem("logout"),100)}finally{if(a((0,n.x9)()),u.startsWith("/form-submission")){let e=u.split("/")[2];e?t.push("/form-submission/".concat(e,"/sign-in")):t.push("/")}else t.push("/")}}}}},45242:(e,a,t)=>{Promise.resolve().then(t.bind(t,65207))},50408:(e,a,t)=>{"use strict";t.d(a,{l:()=>s});var n=t(95155),i=t(66474),r=t(12115);let s=e=>{let{id:a,options:t,value:s,onChange:l}=e,[o,u]=(0,r.useState)(!1),c=(0,r.useRef)(null),d=(0,r.useRef)([]),m=(0,r.useRef)(null);(0,r.useEffect)(()=>{let e=e=>{m.current&&!m.current.contains(e.target)&&u(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]);let p=e=>{if(!o)return;let a=e.key.toLowerCase();if(a.match(/[a-z]/)){let e=t.findIndex(e=>e.toLowerCase().startsWith(a));if(-1!==e&&d.current[e]){var n;null==(n=d.current[e])||n.scrollIntoView({behavior:"auto",block:"nearest"})}}};return(0,r.useEffect)(()=>(document.addEventListener("keydown",p),()=>{document.removeEventListener("keydown",p)}),[o,t]),(0,n.jsxs)("div",{className:"relative",ref:m,children:[(0,n.jsxs)("button",{id:a,type:"button",className:"px-4 py-2 flex items-center justify-between rounded-md border border-neutral-400 focus:border-primary-500 duration-300 w-full text-left cursor-pointer",onClick:()=>{u(!o)},children:[(0,n.jsx)("span",{children:s||"Select an option"}),(0,n.jsx)(i.A,{})]}),o&&(0,n.jsx)("ul",{className:"absolute z-10 max-h-[180px] overflow-auto border border-neutral-400 rounded-md bg-neutral-100 w-full mt-1 flex flex-col",ref:c,children:t.map((e,a)=>(0,n.jsx)("li",{ref:e=>{d.current[a]=e},className:"cursor-pointer bg-neutral-100 hover:bg-neutral-200 px-4 py-2",onClick:()=>{l(e),u(!1)},children:e},a))})]})}},57799:(e,a,t)=>{"use strict";t.d(a,{A:()=>i});var n=t(95155);t(12115);let i=()=>(0,n.jsx)("div",{className:"w-full flex items-center justify-center",children:(0,n.jsx)("div",{className:"size-8 rounded-full border-x-2 border-primary-500 animate-spin my-16"})})},63642:(e,a,t)=>{"use strict";t.d(a,{R:()=>r});var n=t(95155);t(12115);var i=t(13163);let r=e=>{let{showModal:a,onClose:t,onConfirm:r,title:s,description:l,confirmButtonText:o,cancelButtonText:u,confirmButtonClass:c,children:d}=e;return(0,n.jsxs)(i.A,{isOpen:a,onClose:t,className:"p-6 rounded-md max-w-xl",children:[(0,n.jsx)("h2",{className:"text-lg font-semibold text-neutral-700",children:s}),(0,n.jsx)("div",{className:"text-neutral-700 mt-2",children:l}),d&&(0,n.jsx)("div",{className:"mt-6 space-y-4",children:d}),(0,n.jsxs)("div",{className:"flex justify-end gap-4 mt-6",children:[(0,n.jsx)("button",{className:"btn-outline",onClick:t,type:"button",children:u||"Cancel"}),(0,n.jsx)("button",{className:"font-medium rounded-md shadow-md text-neutral-100 flex items-center justify-center gap-2 px-4 py-2 active:scale-95 transition-all duration-300 ".concat(c),onClick:r,type:"button",children:o})]})]})}},64368:(e,a,t)=>{"use strict";t.d(a,{H:()=>n});let n=(e,a)=>{let t=Object.entries(a).find(a=>{let[t,n]=a;return n===e});return t?t[0]:null}},65207:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>E});var n=t(95155),i=t(12115),r=t(62177),s=t(25784),l=t(35695),o=t(88570),u=t(26715),c=t(19373),d=t(5041),m=t(34540),p=t(71402),h=t(50408),x=t(57434),y=t(34869),g=t(17576),f=t(57799),b=t(2511),v=t(74567),N=t(64368),j=t(29350),S=t(63642),w=t(94974),A=t(17652);let C=async e=>{let{templateId:a,dataToSend:t}=e,{data:n}=await s.A.patch("/libraries/".concat(a),t);return n},E=()=>{let[e,a]=(0,i.useState)(!1);(0,i.useEffect)(()=>{a(!0)},[]);let{register:t,formState:{isSubmitting:s,errors:E,isSubmitted:_},handleSubmit:k,setValue:T,reset:L}=(0,r.mN)(),B=(0,l.useRouter)(),[M,P]=(0,i.useState)(!1),z=(0,A.c3)(),[D,I]=(0,i.useState)(null),[R,F]=(0,i.useState)(null),[H,K]=(0,i.useState)(!1),[q,G]=(0,i.useState)(!1),[U,O]=(0,i.useState)(null);(0,i.useEffect)(()=>{t("country",{required:z("pleaseSelectCountry")}),t("sector",{required:z("pleaseSelectSector")})},[t]),(0,i.useEffect)(()=>{T("country",D,{shouldValidate:_}),T("sector",R,{shouldValidate:_})},[T,D,R]);let{hashedId:V}=(0,l.useParams)(),Q=(0,o.D)(V),{user:W}=(0,j.A)(),J=(0,u.jE)();(0,i.useEffect)(()=>()=>{Q&&(null==W?void 0:W.id)&&J.cancelQueries({queryKey:["templates",W.id,Q]})},[Q,null==W?void 0:W.id,J]);let{data:Z,isLoading:X,isError:Y}=(0,c.I)({queryKey:["templates",null==W?void 0:W.id,Q],queryFn:()=>(0,w.J2)({templateId:Q}),enabled:!!Q&&!!(null==W?void 0:W.id)});(0,i.useEffect)(()=>{Z&&(L({templateName:Z.name||"",description:Z.description||"",country:Z.country||"",sector:Z.sector||""}),I(Z.country||null),F(Z.sector||null))},[Z,L]);let $=(0,m.wA)(),ee=(0,d.n)({mutationFn:C,onSuccess:()=>{J.invalidateQueries({queryKey:["templates"],exact:!1}),$((0,p.Ds)({message:z("templateUpdated"),type:"success"}))},onError:e=>{$((0,p.Ds)({message:z("templateUpdateFailed")+e.message,type:"error"}))}}),ea=(0,d.n)({mutationFn:()=>(0,w.I7)(Q),onSuccess:()=>{P(!0),G(!1),J.cancelQueries({queryKey:["templates",null==W?void 0:W.id,Q]}),J.removeQueries({queryKey:["template",null==W?void 0:W.id,Q]}),J.invalidateQueries({queryKey:["templates"],exact:!1}),$((0,p.Ds)({message:z("templateDeleted"),type:"success"})),setTimeout(()=>{B.push("/library")},1e3)},onError:e=>{G(!1),console.error("Template deletion error:",e),$((0,p.Ds)({message:z("templateDeleteFailed"),type:"error"}))}}),et=async e=>{ee.mutate({templateId:Q,dataToSend:{name:e.templateName,description:e.description,country:e.country,sector:e.sector}})};return e?M||X?(0,n.jsx)(f.A,{}):V&&null!==Q?Y&&!M?(0,n.jsx)("p",{className:"text-red-500",children:z("failedFetchTemplate")}):(0,n.jsxs)("form",{className:"flex flex-col gap-8",onSubmit:k(et),children:[(0,n.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,n.jsxs)("div",{className:"label-input-group group",children:[(0,n.jsxs)("label",{htmlFor:"template-name",className:"label-text",children:[(0,n.jsx)(x.A,{size:16})," ",z("templateName")]}),(0,n.jsx)("input",{...t("templateName",{required:z("templateNameRequired")}),id:"template-name",type:"text",className:"input-field",placeholder:z("templateNamePlaceholder")}),E.templateName&&(0,n.jsx)("p",{className:"text-red-500 text-sm",children:"".concat(E.templateName.message)})]}),(0,n.jsxs)("div",{className:"label-input-group group",children:[(0,n.jsx)("label",{htmlFor:"description",className:"label-text",children:z("description")}),(0,n.jsx)("textarea",{id:"description",...t("description"),className:"input-field resize-none",cols:4,placeholder:z("templateDescriptionPlaceholder")})]}),(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{className:"label-input-group group",children:[(0,n.jsxs)("label",{htmlFor:"country",className:"label-text",children:[(0,n.jsx)(y.A,{size:16}),z("country")]}),(0,n.jsx)(h.l,{id:"country",options:v,value:D,onChange:I}),E.country&&(0,n.jsx)("p",{className:"text-red-500 text-sm",children:"".concat(E.country.message)})]}),(0,n.jsxs)("div",{className:"label-input-group group",children:[(0,n.jsxs)("label",{htmlFor:"sector",className:"label-text",children:[(0,n.jsx)(g.A,{size:16})," ",z("sector")]}),(0,n.jsx)(h.l,{id:"sector",options:Object.values(b.b),value:R&&b.b[R]?b.b[R]:z("selectOption"),onChange:e=>{F((0,N.H)(e,b.b))}}),E.sector&&(0,n.jsx)("p",{className:"text-red-500 text-sm",children:"".concat(E.sector.message)})]})]}),(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)("div",{className:"flex items-center gap-4",children:(0,n.jsx)("button",{onClick:()=>{O({title:z("confirmDelete"),description:(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("p",{children:z("templateDeleteConfirm")}),(0,n.jsxs)("ul",{className:"list-disc list-inside flex flex-col gap-2 mt-2 text-neutral-700",children:[(0,n.jsx)("li",{children:z("templateDeleteWarningData")}),(0,n.jsx)("li",{children:z("templateDeleteWarningForms")}),(0,n.jsx)("li",{children:z("templateDeleteWarningRecover")})]})]}),confirmButtonText:"Delete",confirmButtonClass:"btn-danger",onConfirm:()=>{ea.mutate()}}),G(!0)},type:"button",className:"btn-danger",children:z("delete")})}),(0,n.jsx)("button",{type:"submit",className:"btn-primary self-end",children:s?(0,n.jsxs)("span",{className:"flex items-center gap-2",children:[z("saving"),(0,n.jsx)("div",{className:"size-4 animate-spin border-x border-neutral-100 rounded-full"})]}):z("saveChanges")})]})]}),U&&(0,n.jsx)(S.R,{showModal:q,onClose:()=>G(!1),title:U.title,description:U.description,confirmButtonText:U.confirmButtonText,confirmButtonClass:U.confirmButtonClass,onConfirm:U.onConfirm})]}):(0,n.jsxs)("div",{className:"error-message",children:[(0,n.jsx)("h1",{className:"text-red-500",children:z("invalidTemplateIdError")}),(0,n.jsx)("p",{className:"text-neutral-700",children:z("invalidTemplateUrl")})]}):null}},71402:(e,a,t)=>{"use strict";t.d(a,{Ay:()=>s,Ds:()=>i,_b:()=>r});let n=(0,t(51990).Z0)({name:"notification",initialState:{message:"",type:"",visible:!1},reducers:{showNotification:(e,a)=>{e.message=a.payload.message,e.type=a.payload.type,e.visible=!0},hideNotification:e=>{e.message="",e.type="",e.visible=!1}}}),{showNotification:i,hideNotification:r}=n.actions,s=n.reducer},74567:e=>{"use strict";e.exports=JSON.parse('["Afghanistan","Albania","Algeria","Andorra","Angola","Argentina","Armenia","Australia","Austria","Azerbaijan","Bahamas","Bahrain","Bangladesh","Barbados","Belarus","Belgium","Belize","Benin","Bhutan","Bolivia","Bosnia and Herzegovina","Botswana","Brazil","Brunei","Bulgaria","Burkina Faso","Burundi","Cabo Verde","Cambodia","Cameroon","Canada","Central African Republic","Chad","Chile","China","Colombia","Comoros","Congo (Congo-Brazzaville)","Costa Rica","Croatia","Cuba","Cyprus","Czech Republic","Denmark","Djibouti","Dominica","Dominican Republic","Ecuador","Egypt","El Salvador","Equatorial Guinea","Eritrea","Estonia","Eswatini","Ethiopia","Fiji","Finland","France","Gabon","Gambia","Georgia","Germany","Ghana","Greece","Grenada","Guatemala","Guinea","Guinea-Bissau","Guyana","Haiti","Honduras","Hungary","Iceland","India","Indonesia","Iran","Iraq","Ireland","Israel","Italy","Jamaica","Japan","Jordan","Kazakhstan","Kenya","Kiribati","Kuwait","Kyrgyzstan","Laos","Latvia","Lebanon","Lesotho","Liberia","Libya","Liechtenstein","Lithuania","Luxembourg","Madagascar","Malawi","Malaysia","Maldives","Mali","Malta","Marshall Islands","Mauritania","Mauritius","Mexico","Micronesia","Moldova","Monaco","Mongolia","Montenegro","Morocco","Mozambique","Myanmar","Namibia","Nauru","Nepal","Netherlands","New Zealand","Nicaragua","Niger","Nigeria","North Korea","North Macedonia","Norway","Oman","Pakistan","Palau","Palestine","Panama","Papua New Guinea","Paraguay","Peru","Philippines","Poland","Portugal","Qatar","Romania","Russia","Rwanda","Saint Kitts and Nevis","Saint Lucia","Saint Vincent and the Grenadines","Samoa","San Marino","Sao Tome and Principe","Saudi Arabia","Senegal","Serbia","Seychelles","Sierra Leone","Singapore","Slovakia","Slovenia","Solomon Islands","Somalia","South Africa","South Korea","South Sudan","Spain","Sri Lanka","Sudan","Suriname","Sweden","Switzerland","Syria","Taiwan","Tajikistan","Tanzania","Thailand","Timor-Leste","Togo","Tonga","Trinidad and Tobago","Tunisia","Turkey","Turkmenistan","Tuvalu","Uganda","Ukraine","United Arab Emirates","United Kingdom","United States","Uruguay","Uzbekistan","Vanuatu","Vatican City","Venezuela","Vietnam","Yemen","Zambia","Zimbabwe"]')},88570:(e,a,t)=>{"use strict";t.d(a,{D:()=>l,l:()=>s});var n=t(41050);let i=t(49509).env.SALT||"rushan-salt",r=new n.A(i,12),s=e=>r.encode(e),l=e=>{let a=r.decode(e)[0];return"bigint"==typeof a?a<Number.MAX_SAFE_INTEGER?Number(a):null:"number"==typeof a?a:null}},94974:(e,a,t)=>{"use strict";t.d(a,{I7:()=>l,J2:()=>i,QK:()=>s,Xu:()=>r,nh:()=>o});var n=t(25784);let i=async e=>{let{templateId:a}=e,{data:t}=await n.A.get("/libraries/".concat(a));return t.template},r=async e=>{let{data:a}=await n.A.post("/libraries",e);return a},s=async()=>{let{data:e}=await n.A.get("/libraries");return e.templates},l=async e=>{let{data:a}=await n.A.delete("/libraries/".concat(e));return a},o=async e=>{let{templateIds:a}=e;return null}},97381:(e,a,t)=>{"use strict";t.d(a,{Ay:()=>o,Le:()=>s,jB:()=>l,tQ:()=>i,x9:()=>r});let n=(0,t(51990).Z0)({name:"auth",initialState:{status:"loading",user:null,error:null},reducers:{setAuthenticatedUser:(e,a)=>{e.status="authenticated",e.user=a.payload,e.error=null},setUnauthenticated:e=>{e.status="unauthenticated",e.user=null,e.error=null},setAuthLoading:e=>{e.status="loading"},setAuthError:(e,a)=>{e.status="unauthenticated",e.error=a.payload,e.user=null}}}),{setAuthenticatedUser:i,setUnauthenticated:r,setAuthLoading:s,setAuthError:l}=n.actions,o=n.reducer}},e=>{var a=a=>e(e.s=a);e.O(0,[6453,635,1111,6967,9373,4601,1380,5047,8441,1684,7358],()=>a(45242)),_N_E=e.O()}]);