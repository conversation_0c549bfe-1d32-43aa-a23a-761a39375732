"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6539],{25519:(e,t,r)=>{r.d(t,{n:()=>d});var n=r(12115),o=r(6101),i=r(63655),l=r(39033),a=r(95155),s="focusScope.autoFocusOnMount",u="focusScope.autoFocusOnUnmount",c={bubbles:!1,cancelable:!0},d=n.forwardRef((e,t)=>{let{loop:r=!1,trapped:d=!1,onMountAutoFocus:y,onUnmountAutoFocus:h,...b}=e,[g,E]=n.useState(null),w=(0,l.c)(y),x=(0,l.c)(h),O=n.useRef(null),P=(0,o.s)(t,e=>E(e)),C=n.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;n.useEffect(()=>{if(d){let e=function(e){if(C.paused||!g)return;let t=e.target;g.contains(t)?O.current=t:v(O.current,{select:!0})},t=function(e){if(C.paused||!g)return;let t=e.relatedTarget;null!==t&&(g.contains(t)||v(O.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let r=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&v(g)});return g&&r.observe(g,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),r.disconnect()}}},[d,g,C.paused]),n.useEffect(()=>{if(g){m.add(C);let e=document.activeElement;if(!g.contains(e)){let t=new CustomEvent(s,c);g.addEventListener(s,w),g.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=document.activeElement;for(let n of e)if(v(n,{select:t}),document.activeElement!==r)return}(f(g).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&v(g))}return()=>{g.removeEventListener(s,w),setTimeout(()=>{let t=new CustomEvent(u,c);g.addEventListener(u,x),g.dispatchEvent(t),t.defaultPrevented||v(null!=e?e:document.body,{select:!0}),g.removeEventListener(u,x),m.remove(C)},0)}}},[g,w,x,C]);let j=n.useCallback(e=>{if(!r&&!d||C.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,n=document.activeElement;if(t&&n){let t=e.currentTarget,[o,i]=function(e){let t=f(e);return[p(t,e),p(t.reverse(),e)]}(t);o&&i?e.shiftKey||n!==i?e.shiftKey&&n===o&&(e.preventDefault(),r&&v(i,{select:!0})):(e.preventDefault(),r&&v(o,{select:!0})):n===t&&e.preventDefault()}},[r,d,C.paused]);return(0,a.jsx)(i.sG.div,{tabIndex:-1,...b,ref:P,onKeyDown:j})});function f(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function p(e,t){for(let r of e)if(!function(e,t){let{upTo:r}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===r||e!==r);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(r,{upTo:t}))return r}function v(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var r;let n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&(r=e)instanceof HTMLInputElement&&"select"in r&&t&&e.select()}}d.displayName="FocusScope";var m=function(){let e=[];return{add(t){let r=e[0];t!==r&&(null==r||r.pause()),(e=y(e,t)).unshift(t)},remove(t){var r;null==(r=(e=y(e,t))[0])||r.resume()}}}();function y(e,t){let r=[...e],n=r.indexOf(t);return -1!==n&&r.splice(n,1),r}},34378:(e,t,r)=>{r.d(t,{Z:()=>s});var n=r(12115),o=r(47650),i=r(63655),l=r(52712),a=r(95155),s=n.forwardRef((e,t)=>{var r,s;let{container:u,...c}=e,[d,f]=n.useState(!1);(0,l.N)(()=>f(!0),[]);let p=u||d&&(null==(s=globalThis)||null==(r=s.document)?void 0:r.body);return p?o.createPortal((0,a.jsx)(i.sG.div,{...c,ref:t}),p):null});s.displayName="Portal"},54416:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},58434:(e,t,r)=>{r.d(t,{qW:()=>p});var n,o=r(12115),i=r(85185),l=r(63655),a=r(6101),s=r(39033),u=r(51595),c=r(95155),d="dismissableLayer.update",f=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),p=o.forwardRef((e,t)=>{var r,p;let{disableOutsidePointerEvents:y=!1,onEscapeKeyDown:h,onPointerDownOutside:b,onFocusOutside:g,onInteractOutside:E,onDismiss:w,...x}=e,O=o.useContext(f),[P,C]=o.useState(null),j=null!=(p=null==P?void 0:P.ownerDocument)?p:null==(r=globalThis)?void 0:r.document,[,N]=o.useState({}),L=(0,a.s)(t,e=>C(e)),S=Array.from(O.layers),[A]=[...O.layersWithOutsidePointerEventsDisabled].slice(-1),D=S.indexOf(A),k=P?S.indexOf(P):-1,T=O.layersWithOutsidePointerEventsDisabled.size>0,R=k>=D,F=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,n=(0,s.c)(e),i=o.useRef(!1),l=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!i.current){let t=function(){m("dismissableLayer.pointerDownOutside",n,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(r.removeEventListener("click",l.current),l.current=t,r.addEventListener("click",l.current,{once:!0})):t()}else r.removeEventListener("click",l.current);i.current=!1},t=window.setTimeout(()=>{r.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),r.removeEventListener("pointerdown",e),r.removeEventListener("click",l.current)}},[r,n]),{onPointerDownCapture:()=>i.current=!0}}(e=>{let t=e.target,r=[...O.branches].some(e=>e.contains(t));R&&!r&&(null==b||b(e),null==E||E(e),e.defaultPrevented||null==w||w())},j),W=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,n=(0,s.c)(e),i=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!i.current&&m("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return r.addEventListener("focusin",e),()=>r.removeEventListener("focusin",e)},[r,n]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}(e=>{let t=e.target;![...O.branches].some(e=>e.contains(t))&&(null==g||g(e),null==E||E(e),e.defaultPrevented||null==w||w())},j);return(0,u.U)(e=>{k===O.layers.size-1&&(null==h||h(e),!e.defaultPrevented&&w&&(e.preventDefault(),w()))},j),o.useEffect(()=>{if(P)return y&&(0===O.layersWithOutsidePointerEventsDisabled.size&&(n=j.body.style.pointerEvents,j.body.style.pointerEvents="none"),O.layersWithOutsidePointerEventsDisabled.add(P)),O.layers.add(P),v(),()=>{y&&1===O.layersWithOutsidePointerEventsDisabled.size&&(j.body.style.pointerEvents=n)}},[P,j,y,O]),o.useEffect(()=>()=>{P&&(O.layers.delete(P),O.layersWithOutsidePointerEventsDisabled.delete(P),v())},[P,O]),o.useEffect(()=>{let e=()=>N({});return document.addEventListener(d,e),()=>document.removeEventListener(d,e)},[]),(0,c.jsx)(l.sG.div,{...x,ref:L,style:{pointerEvents:T?R?"auto":"none":void 0,...e.style},onFocusCapture:(0,i.m)(e.onFocusCapture,W.onFocusCapture),onBlurCapture:(0,i.m)(e.onBlurCapture,W.onBlurCapture),onPointerDownCapture:(0,i.m)(e.onPointerDownCapture,F.onPointerDownCapture)})});function v(){let e=new CustomEvent(d);document.dispatchEvent(e)}function m(e,t,r,n){let{discrete:o}=n,i=r.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&i.addEventListener(e,t,{once:!0}),o?(0,l.hO)(i,a):i.dispatchEvent(a)}p.displayName="DismissableLayer",o.forwardRef((e,t)=>{let r=o.useContext(f),n=o.useRef(null),i=(0,a.s)(t,n);return o.useEffect(()=>{let e=n.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,c.jsx)(l.sG.div,{...e,ref:i})}).displayName="DismissableLayerBranch"},63753:(e,t,r)=>{r.d(t,{Mz:()=>T,i3:()=>F,UC:()=>R,bL:()=>k,Bk:()=>y});var n=r(12115),o=r(84945),i=r(22475),l=r(63655),a=r(95155),s=n.forwardRef((e,t)=>{let{children:r,width:n=10,height:o=5,...i}=e;return(0,a.jsx)(l.sG.svg,{...i,ref:t,width:n,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?r:(0,a.jsx)("polygon",{points:"0,0 30,0 15,10"})})});s.displayName="Arrow";var u=r(6101),c=r(46081),d=r(39033),f=r(52712),p=r(11275),v="Popper",[m,y]=(0,c.A)(v),[h,b]=m(v),g=e=>{let{__scopePopper:t,children:r}=e,[o,i]=n.useState(null);return(0,a.jsx)(h,{scope:t,anchor:o,onAnchorChange:i,children:r})};g.displayName=v;var E="PopperAnchor",w=n.forwardRef((e,t)=>{let{__scopePopper:r,virtualRef:o,...i}=e,s=b(E,r),c=n.useRef(null),d=(0,u.s)(t,c);return n.useEffect(()=>{s.onAnchorChange((null==o?void 0:o.current)||c.current)}),o?null:(0,a.jsx)(l.sG.div,{...i,ref:d})});w.displayName=E;var x="PopperContent",[O,P]=m(x),C=n.forwardRef((e,t)=>{var r,s,c,v,m,y,h,g;let{__scopePopper:E,side:w="bottom",sideOffset:P=0,align:C="center",alignOffset:j=0,arrowPadding:N=0,avoidCollisions:L=!0,collisionBoundary:k=[],collisionPadding:T=0,sticky:R="partial",hideWhenDetached:F=!1,updatePositionStrategy:W="optimized",onPlaced:I,...z}=e,B=b(x,E),[K,M]=n.useState(null),U=(0,u.s)(t,e=>M(e)),[_,G]=n.useState(null),H=(0,p.X)(_),Y=null!=(h=null==H?void 0:H.width)?h:0,X=null!=(g=null==H?void 0:H.height)?g:0,q="number"==typeof T?T:{top:0,right:0,bottom:0,left:0,...T},Z=Array.isArray(k)?k:[k],J=Z.length>0,Q={padding:q,boundary:Z.filter(S),altBoundary:J},{refs:V,floatingStyles:$,placement:ee,isPositioned:et,middlewareData:er}=(0,o.we)({strategy:"fixed",placement:w+("center"!==C?"-"+C:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,i.ll)(...t,{animationFrame:"always"===W})},elements:{reference:B.anchor},middleware:[(0,o.cY)({mainAxis:P+X,alignmentAxis:j}),L&&(0,o.BN)({mainAxis:!0,crossAxis:!1,limiter:"partial"===R?(0,o.ER)():void 0,...Q}),L&&(0,o.UU)({...Q}),(0,o.Ej)({...Q,apply:e=>{let{elements:t,rects:r,availableWidth:n,availableHeight:o}=e,{width:i,height:l}=r.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(n,"px")),a.setProperty("--radix-popper-available-height","".concat(o,"px")),a.setProperty("--radix-popper-anchor-width","".concat(i,"px")),a.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),_&&(0,o.UE)({element:_,padding:N}),A({arrowWidth:Y,arrowHeight:X}),F&&(0,o.jD)({strategy:"referenceHidden",...Q})]}),[en,eo]=D(ee),ei=(0,d.c)(I);(0,f.N)(()=>{et&&(null==ei||ei())},[et,ei]);let el=null==(r=er.arrow)?void 0:r.x,ea=null==(s=er.arrow)?void 0:s.y,es=(null==(c=er.arrow)?void 0:c.centerOffset)!==0,[eu,ec]=n.useState();return(0,f.N)(()=>{K&&ec(window.getComputedStyle(K).zIndex)},[K]),(0,a.jsx)("div",{ref:V.setFloating,"data-radix-popper-content-wrapper":"",style:{...$,transform:et?$.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:eu,"--radix-popper-transform-origin":[null==(v=er.transformOrigin)?void 0:v.x,null==(m=er.transformOrigin)?void 0:m.y].join(" "),...(null==(y=er.hide)?void 0:y.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,a.jsx)(O,{scope:E,placedSide:en,onArrowChange:G,arrowX:el,arrowY:ea,shouldHideArrow:es,children:(0,a.jsx)(l.sG.div,{"data-side":en,"data-align":eo,...z,ref:U,style:{...z.style,animation:et?void 0:"none"}})})})});C.displayName=x;var j="PopperArrow",N={top:"bottom",right:"left",bottom:"top",left:"right"},L=n.forwardRef(function(e,t){let{__scopePopper:r,...n}=e,o=P(j,r),i=N[o.placedSide];return(0,a.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,a.jsx)(s,{...n,ref:t,style:{...n.style,display:"block"}})})});function S(e){return null!==e}L.displayName=j;var A=e=>({name:"transformOrigin",options:e,fn(t){var r,n,o,i,l;let{placement:a,rects:s,middlewareData:u}=t,c=(null==(r=u.arrow)?void 0:r.centerOffset)!==0,d=c?0:e.arrowWidth,f=c?0:e.arrowHeight,[p,v]=D(a),m={start:"0%",center:"50%",end:"100%"}[v],y=(null!=(i=null==(n=u.arrow)?void 0:n.x)?i:0)+d/2,h=(null!=(l=null==(o=u.arrow)?void 0:o.y)?l:0)+f/2,b="",g="";return"bottom"===p?(b=c?m:"".concat(y,"px"),g="".concat(-f,"px")):"top"===p?(b=c?m:"".concat(y,"px"),g="".concat(s.floating.height+f,"px")):"right"===p?(b="".concat(-f,"px"),g=c?m:"".concat(h,"px")):"left"===p&&(b="".concat(s.floating.width+f,"px"),g=c?m:"".concat(h,"px")),{data:{x:b,y:g}}}});function D(e){let[t,r="center"]=e.split("-");return[t,r]}var k=g,T=w,R=C,F=L},74436:(e,t,r)=>{r.d(t,{k5:()=>c});var n=r(12115),o={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},i=n.createContext&&n.createContext(o),l=["attr","size","title"];function a(){return(a=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function s(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function u(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?s(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function c(e){return t=>n.createElement(d,a({attr:u({},e.attr)},t),function e(t){return t&&t.map((t,r)=>n.createElement(t.tag,u({key:r},t.attr),e(t.child)))}(e.child))}function d(e){var t=t=>{var r,{attr:o,size:i,title:s}=e,c=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,l),d=i||t.size||"1em";return t.className&&(r=t.className),e.className&&(r=(r?r+" ":"")+e.className),n.createElement("svg",a({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,o,c,{className:r,style:u(u({color:e.color||t.color},t.style),e.style),height:d,width:d,xmlns:"http://www.w3.org/2000/svg"}),s&&n.createElement("title",null,s),e.children)};return void 0!==i?n.createElement(i.Consumer,null,e=>t(e)):t(o)}}}]);