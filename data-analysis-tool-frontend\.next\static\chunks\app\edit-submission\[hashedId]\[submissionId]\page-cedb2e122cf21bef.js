(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7767],{5287:(e,t,r)=>{"use strict";r.d(t,{GN:()=>o,J6:()=>i,O8:()=>n,s4:()=>a});var s=r(25784);let n=async(e,t)=>{try{let{data:r}=await s.A.delete("/form-submissions/".concat(e,"?projectId=").concat(t));return r}catch(e){throw console.error("Error deleting form submission:",e),e}},i=async(e,t)=>{try{let r=e.map(e=>s.A.delete("/form-submissions/".concat(e,"?projectId=").concat(t)));return(await Promise.all(r)).map(e=>e.data)}catch(e){throw console.error("Error deleting multiple form submissions:",e),e}},a=async(e,t)=>{try{if(!e.submissionId||!e.questionId)throw Error("submissionId and questionId are required");let r={...e};null===r.questionOptionId?delete r.questionOptionId:Array.isArray(r.questionOptionId)&&(r.questionOptionId=r.questionOptionId.filter(e=>null!=e),0===r.questionOptionId.length&&delete r.questionOptionId);let{data:n}=await s.A.patch("/answers/".concat(e.questionId,"?projectId=").concat(t),r);return n}catch(e){throw console.error("Error updating answer:",e),e}},o=async(e,t)=>{try{let{data:r}=await s.A.patch("/answers/multiple?projectId=".concat(t),e);return r}catch(e){throw console.error("Error updating multiple answers with endpoint:",e),e}}},37818:(e,t,r)=>{Promise.resolve().then(r.bind(r,60289))},60289:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>A});var s=r(95155),n=r(35695),i=r(19373),a=r(25784),o=r(88570),l=r(34947),u=r(57799),d=r(12115),c=r(82714),p=r(99474),m=r(95139),h=r(55747),y=r(92138),f=r(5041),g=r(34540),b=r(71402),v=r(16112),w=r(5287),x=r(10150),j=r(77361),q=r(3587),I=r(13388),O=r(80423),N=r(52707);function S(e){let{questions:t,submission:r,projectId:n,submissionId:o,onClose:l,onSave:u}=e,S=(0,g.wA)(),[E,A]=(0,d.useState)({}),[k,T]=(0,d.useState)({}),[C,F]=(0,d.useState)({}),[J,R]=(0,d.useState)(!1),[D,_]=(0,d.useState)([]),[G,K]=(0,d.useState)([]),[L,M]=(0,d.useState)({}),[Q,P]=(0,d.useState)(new Set),Y=(0,d.useRef)(new Set),z=(0,d.useRef)(!1),U=(0,d.useRef)(""),{data:H=[]}=(0,i.I)({queryKey:["questionGroups",n],queryFn:()=>(0,x.pr)({projectId:n}),enabled:!!n}),{data:V}=(0,i.I)({queryKey:["project",n],queryFn:()=>(0,j.kf)({projectId:n}),enabled:!!n});(0,d.useEffect)(()=>{let e={};t.forEach(t=>{if("selectmany"===t.inputType){let s=r.answers.filter(e=>e.question.id===t.id);e[t.id]=s.map(e=>e.value).filter(e=>null!=e&&""!==String(e).trim())}else{var s;let n=r.answers.find(e=>e.question.id===t.id);e[t.id]=null!=(s=null==n?void 0:n.value)?s:""}}),A(e),T(JSON.parse(JSON.stringify(e)))},[t,r]),(0,d.useEffect)(()=>{if(!t||0===Object.keys(k).length)return;let e=JSON.stringify(E);if(e===U.current)return;let r=(0,q.UL)(t,E),s=new Set(r.map(e=>e.id));if(_(r),K((0,q.Tr)(t,E)),!z.current){Y.current=s,z.current=!0,U.current=e;return}if(!(s.size!==Y.current.size||[...s].some(e=>!Y.current.has(e)))){U.current=e;return}let n=new Set([...s].filter(e=>!Y.current.has(e))),i=!1,a={...E};n.size>0&&n.forEach(e=>{let t=e.toString();!Q.has(e)&&(void 0===E[t]||""===E[t]||Array.isArray(E[t])&&0===E[t].length)&&void 0!==k[t]&&""!==k[t]&&!(Array.isArray(k[t])&&0===k[t].length)&&(a[t]=k[t],i=!0)});let o=(0,q.OD)(a,r),l=Object.keys(o).length!==Object.keys(a).length;Y.current=s,i||l?(U.current=JSON.stringify(o),A(o)):U.current=e},[t,E,k]);let X=(0,d.useMemo)(()=>(0,N.yi)(H,t),[H,t]);(0,d.useEffect)(()=>{X.length>0&&M((0,N.cZ)(X,!0))},[X.length]);let B=(0,d.useMemo)(()=>(0,N.ru)(t),[t]),W=(0,d.useMemo)(()=>(0,N.XV)(X,B),[X,B]),Z=(0,d.useCallback)(e=>{M(t=>({...t,[e]:!t[e]}))},[]),$=(0,d.useCallback)((e,t)=>{P(t=>new Set(t).add(e)),A(r=>({...r,[e]:t})),F(t=>({...t,[e]:""}))},[]),ee=()=>{let e=(0,q.WK)(D,E);return F(e),0===Object.keys(e).length},et=(0,f.n)({mutationFn:async e=>{let s=t.map(t=>{let s=e[t.id],i="selectmany"===t.inputType,a=r.answers.find(e=>e.question.id===t.id),l=!(null==a?void 0:a.id);if(i&&Array.isArray(s)){if(s.length>0){let e=[];t.questionOptions&&(e=s.map(e=>{let r=t.questionOptions.find(t=>t.label===e);return null==r?void 0:r.id}).filter(e=>void 0!==e));let r={projectId:n,questionId:t.id,answerType:t.inputType,value:s.join(", "),questionOptionId:e,isOtherOption:!1,formSubmissionId:o};return l?r:{...r,id:a.id}}return null}{let e,r;if(void 0===(e="number"===t.inputType||"decimal"===t.inputType?s?Number(s):void 0:"date"===t.inputType||"dateandtime"===t.inputType?s||void 0:"table"===t.inputType?Array.isArray(s)&&s.length>0?JSON.stringify(s):void 0:s?String(s):void 0))return null;if("selectone"===t.inputType&&s&&t.questionOptions){let e=t.questionOptions.find(e=>e.label===s);r=null==e?void 0:e.id}let i={projectId:n,questionId:t.id,answerType:t.inputType,value:e,questionOptionId:r,isOtherOption:!1,formSubmissionId:o};return l?i:{...i,id:a.id}}}).filter(e=>null!==e);if(0===s.length)throw Error("No valid answers with IDs to submit");let i=s.map(e=>e.id?{id:e.id,questionId:e.questionId,projectId:n,value:e.value,answerType:e.answerType,questionOptionId:e.questionOptionId,isOtherOption:e.isOtherOption||!1,formSubmissionId:e.formSubmissionId}:e.questionId?{questionId:e.questionId,projectId:n,value:e.value,answerType:e.answerType,questionOptionId:e.questionOptionId,isOtherOption:e.isOtherOption||!1,formSubmissionId:e.formSubmissionId}:null).filter(e=>null!==e);try{return await (0,w.GN)(i,n)}catch(r){console.error("Error with /answers/multiple endpoint:",r),r.response&&(console.error("Error response data:",JSON.stringify(r.response.data,null,2)),console.error("Error response status:",r.response.status),console.error("Error response headers:",r.response.headers));let e=[],t=[];for(let r of s)try{if(r.id){let{data:t}=await a.A.patch("/answers/".concat(r.id,"?projectId=").concat(n),{id:r.id,questionId:r.questionId,projectId:n,value:r.value,answerType:r.answerType,questionOptionId:r.questionOptionId,isOtherOption:r.isOtherOption||!1,formSubmissionId:r.formSubmissionId});e.push(t)}else if(r.questionId){let{data:t}=await a.A.post("/answers?projectId=".concat(n),{submissionId:r.formSubmissionId,questionId:r.questionId,value:r.value,answerType:r.answerType,questionOptionId:r.questionOptionId,isOtherOption:r.isOtherOption||!1});e.push(t)}}catch(s){let e=r.id||r.questionId;console.error("Error handling answer ".concat(e,":"),s),s.response&&console.error("Individual error response data:",JSON.stringify(s.response.data,null,2)),t.push(e)}if(t.length>0)throw Error("Failed to update answers with IDs: ".concat(t.join(", ")));if(e.length>0)return S((0,b.Ds)({message:"Submission updated successfully using individual updates. Consider checking the bulk update endpoint.",type:"warning"})),e;throw r}},onSuccess:()=>{S((0,b.Ds)({message:"Submission updated successfully. You can continue editing if needed.",type:"success"})),P(new Set),u()},onError:e=>{var t,r,s,n,i,a;let o=(null==(r=e.response)||null==(t=r.data)?void 0:t.message)||(null==(n=e.response)||null==(s=n.data)?void 0:s.error)||e.message||"Failed to update submission. Please check your input and try again.";S((0,b.Ds)({message:o,type:"error"})),console.error("Update Error:",{message:o,status:null==(i=e.response)?void 0:i.status,data:JSON.stringify(null==(a=e.response)?void 0:a.data,null,2)})},onSettled:()=>{R(!1)}}),er=async e=>{e.preventDefault(),ee()&&(R(!0),et.mutate(E))},es=e=>t.some(t=>{var r;return null==(r=t.questionOptions)?void 0:r.some(t=>t.nextQuestionId===e)}),en=e=>{var t;return(null==(t=e.questionOptions)?void 0:t.some(e=>e.nextQuestionId))||!1},ei=e=>{let t=es(e.id),r=en(e);return(0,s.jsxs)("div",{className:"border rounded-md p-4 ".concat(t?"border-primary-200 dark:border-primary-700 bg-primary-100 dark:bg-primary-900/20":"border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800"),children:[(0,s.jsxs)("div",{className:"mb-2",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsxs)(c.J,{className:"text-base font-medium",children:[e.label,e.isRequired&&(0,s.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),t&&(0,s.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200",children:[(0,s.jsx)(y.A,{className:"w-3 h-3 mr-1"}),"Follow-up"]}),r&&(0,s.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-accent-200 text-accent-700 dark:bg-accent-700/20 dark:text-accent-200",children:"Has conditions"})]}),e.hint&&(0,s.jsx)("p",{className:"text-sm mt-1 ".concat(t?"text-primary-700 dark:text-primary-300":"text-muted-foreground"),children:e.hint}),C[e.id]&&(0,s.jsx)("p",{className:"text-sm text-red-500 mt-1",children:C[e.id]})]}),(0,s.jsx)("div",{className:"mt-2",children:ea(e)})]},e.id)},ea=e=>{var t,r,n,i;let a=null!=(t=E[e.id])?t:"selectmany"===e.inputType?[]:"";switch(e.inputType){case"text":if(null==(r=e.hint)?void 0:r.includes("multiline"))return(0,s.jsx)(p.T,{value:a,onChange:t=>$(e.id,t.target.value),placeholder:e.hint||"Your answer",required:e.isRequired});return(0,s.jsx)("input",{className:"input-field w-full",value:a,onChange:t=>$(e.id,t.target.value),placeholder:e.hint||"Your answer",required:e.isRequired});case"number":return(0,s.jsx)("input",{className:"input-field w-full",type:"number",value:a,onChange:t=>$(e.id,t.target.value),placeholder:e.hint||"Your answer",required:e.isRequired});case"decimal":return(0,s.jsx)("input",{className:"input-field w-full",type:"number",step:"any",value:a,onChange:t=>$(e.id,t.target.value),placeholder:e.hint||"Your answer",required:e.isRequired});case"selectone":return(0,s.jsx)(h.z,{value:a,onValueChange:t=>$(e.id,t),required:e.isRequired,children:(0,s.jsx)("div",{className:"space-y-2",children:null==(n=e.questionOptions)?void 0:n.map((e,t)=>(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(h.C,{value:e.label,id:"option-".concat(e.id)}),(0,s.jsx)(c.J,{htmlFor:"option-".concat(e.id),className:"cursor-pointer",children:e.label})]},t))})});case"selectmany":return(0,s.jsx)("div",{className:"space-y-2",children:null==(i=e.questionOptions)?void 0:i.map(t=>(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(m.S,{id:"option-".concat(t.id),checked:(a||[]).includes(t.label),onCheckedChange:r=>{let s=a||[],n=r?[...s,t.label]:s.filter(e=>e!==t.label);$(e.id,n)}}),(0,s.jsx)(c.J,{htmlFor:"option-".concat(t.id),className:"cursor-pointer",children:t.label})]},t.id))});case"date":return(0,s.jsx)("div",{className:"relative",children:(0,s.jsx)("input",{className:"input-field w-full",type:"date",value:a,onChange:t=>$(e.id,t.target.value),placeholder:e.hint||"Select date",required:e.isRequired})});case"dateandtime":return(0,s.jsx)("div",{className:"relative",children:(0,s.jsx)("input",{className:"input-field w-full",type:"time",value:a,onChange:t=>$(e.id,t.target.value),placeholder:e.hint||"Select time",required:e.isRequired})});case"table":return(0,s.jsx)(v.N,{questionId:e.id,value:a,onChange:t=>$(e.id,t),required:e.isRequired,tableLabel:e.label});default:return null}};return(0,s.jsxs)("div",{className:"w-full max-w-screen-lg bg-neutral-100 dark:bg-gray-800 rounded-md shadow-sm border border-gray-200 dark:border-gray-700",children:[(0,s.jsxs)("h2",{className:"text-2xl font-semibold p-6 border-b border-gray-200 dark:border-gray-700",children:["Edit Submission",(null==V?void 0:V.name)?" for ".concat(V.name):""]}),(0,s.jsx)("form",{onSubmit:er,className:"p-6",children:(0,s.jsxs)("div",{className:"space-y-6",children:[0===t.length?(0,s.jsx)("div",{className:"text-center py-12",children:(0,s.jsx)("p",{className:"text-muted-foreground",children:"This form has no questions yet."})}):W.map(e=>{if("group"===e.type){let t=e.data,r=L[t.id];return(0,s.jsx)(O.A,{group:t,nestingLevel:0,visibleQuestions:D,nestedQuestions:G,renderQuestionInput:ea,errors:C,onToggleExpansion:Z,isExpanded:r,expandedGroups:L,className:""},"group-".concat(t.id))}{let t=e.data;if(!D.some(e=>e.id===t.id))return null;let r=G.find(e=>e.question.id===t.id);return r?(0,s.jsx)(I.A,{questionGroup:r,renderQuestionInput:ea,errors:C,className:""},t.id):ei(t)}}),t.length>0&&(0,s.jsxs)("div",{className:"mt-6 flex justify-end gap-4",children:[(0,s.jsx)("button",{className:"btn-primary bg-neutral-500 hover:bg-neutral-600",type:"button",onClick:l,disabled:J,children:"Cancel"}),(0,s.jsx)("button",{className:"btn-primary",type:"submit",disabled:J,children:J?"Saving...":"Save Changes"})]})]})})]})}let E=async(e,t)=>{let{data:r}=await a.A.get("/form-submissions/".concat(e)),s=r.data.formSubmissions.find(e=>e.id===t);if(!s)throw Error("Submission not found");return s};function A(){let{hashedId:e,submissionId:t}=(0,n.useParams)(),r=(0,o.D)(e),a=Number(t);if(null===r||isNaN(a))return(0,s.jsx)("div",{children:"Error: Invalid project or submission ID."});let{data:d=[],isLoading:c,isError:p}=(0,i.I)({queryKey:["questions",r],queryFn:()=>(0,l.K4)({projectId:r}),enabled:!!r}),{data:m,isLoading:h,isError:y,refetch:f}=(0,i.I)({queryKey:["submission",r,a],queryFn:()=>E(r,a),enabled:!!r&&!!a});return c||h?(0,s.jsx)(u.A,{}):p||y||!d||!m?(0,s.jsx)("p",{className:"text-sm text-red-500",children:"Error loading submission or form. Please try again."}):(0,s.jsx)("div",{className:"min-h-screen w-full bg-neutral-100 dark:bg-gray-900 flex flex-col items-center p-4 md:p-6",children:(0,s.jsx)(S,{questions:d,submission:m,projectId:r,submissionId:a,onSave:()=>{window.opener&&window.opener.postMessage({type:"REFETCH_SUBMISSIONS"},"*"),f()}})})}},77361:(e,t,r)=>{"use strict";r.d(t,{D_:()=>c,Im:()=>u,Oo:()=>p,c3:()=>i,kf:()=>n,lj:()=>h,or:()=>l,pf:()=>d,vj:()=>a,wI:()=>m,xx:()=>o});var s=r(25784);let n=async e=>{let{projectId:t}=e,{data:r}=await s.A.get("/projects/".concat(t));return r.project},i=async e=>{let{data:t}=await s.A.post("/projects/from-template",e);return t},a=async()=>{try{let{data:e}=await s.A.get("/projects");return e.projects}catch(e){throw console.error("Error fetching projects:",e),e}},o=async e=>{let{data:t}=await s.A.delete("/projects/delete/".concat(e));return t},l=async e=>{try{let{data:t}=await s.A.delete("/projects/delete-multiple",{data:{projectIds:e}});return t}catch(e){throw console.error("Error deleting multiple projects:",e),e}},u=async e=>{try{let{data:t}=await s.A.patch("/projects/change-status/".concat(e),{status:"archived"});return t}catch(e){throw console.error("Error archiving project:",e),e}},d=async function(e){arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{let{data:t}=await s.A.patch("/projects/change-status/".concat(e),{status:"deployed"});return t}catch(e){throw console.error("Error deploying project:",e),e}},c=async e=>{try{let{data:t}=await s.A.patch("/projects/update-many-status",{projectIds:e,status:"archived"});return t}catch(e){throw console.error("Error archiving multiple projects:",e),e}},p=async e=>{try{let{data:t}=await s.A.post("/users/check-email",{email:e});return t}catch(e){var t,r,n,i,a,o;throw Error("object"==typeof(null==(r=e.response)||null==(t=r.data)?void 0:t.message)?JSON.stringify(null==(i=e.response)||null==(n=i.data)?void 0:n.message):(null==(o=e.response)||null==(a=o.data)?void 0:a.message)||e.message||"Failed to check user")}},m=async e=>{let{projectId:t,email:r,permissions:n}=e;try{let e=await p(r);if(!e||!e.success)throw Error((null==e?void 0:e.message)||"User not found");let{data:i}=await s.A.post("/project-users",{userId:e.user.id,projectId:t,permission:n});return i}catch(e){var i,a,o,l,u,d;throw console.error("Error adding user to project:",e),Error("object"==typeof(null==(a=e.response)||null==(i=a.data)?void 0:i.message)?JSON.stringify(null==(l=e.response)||null==(o=l.data)?void 0:o.message):(null==(d=e.response)||null==(u=d.data)?void 0:u.message)||e.message||"Failed to add user")}},h=async e=>{try{let{data:t}=await s.A.post("/answers/multiple",e);return t}catch(e){throw console.error("Error creating answer submission:",e),e}}},88570:(e,t,r)=>{"use strict";r.d(t,{D:()=>o,l:()=>a});var s=r(41050);let n=r(49509).env.SALT||"rushan-salt",i=new s.A(n,12),a=e=>i.encode(e),o=e=>{let t=i.decode(e)[0];return"bigint"==typeof t?t<Number.MAX_SAFE_INTEGER?Number(t):null:"number"==typeof t?t:null}}},e=>{var t=t=>e(e.s=t);e.O(0,[635,1111,6967,9373,4277,556,3481,7823,5645,8441,1684,7358],()=>t(37818)),_N_E=e.O()}]);