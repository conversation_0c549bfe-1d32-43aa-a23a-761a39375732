(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3799],{2511:(e,a,t)=>{"use strict";t.d(a,{b:()=>n});let n={information_media:"Information / Media",econommic_social_development:"Economic & Social Development",security_police_peacekeeping:"Security / Police / Peacekeeping",disarmament_and_demobilization:"Disarmament & Demobilization",environment:"Environment",private_sector:"Private Sector",humanitarian_coordination_information_management:"Humanitarian - Coordination & Info Management",humanitarian_multiple_clusters:"Humanitarian - Multiple Clusters",humanitarian_camp_management_and_coordination:"Humanitarian - Camp Management & Coordination",humanitarian_early_recovery:"Humanitarian - Early Recovery",humanitarian_education:"Humanitarian - Education",humanitarian_emergency_shelter:"Humanitarian - Emergency Shelter",humanitarian_emergency_telecoms:"Humanitarian - Emergency Telecoms",humanitarian_food_security:"Humanitarian - Food Security",humanitarian_health:"Humanitarian - Health",humanitarian_logistics:"Humanitarian - Logistics",humanitarian_nutrition:"Humanitarian - Nutrition",humanitarian_protection:"Humanitarian - Protection",humanitarian_sanitation_water_and_hygiene:"Humanitarian - Sanitation / Water / Hygiene",other:"Other"}},13163:(e,a,t)=>{"use strict";t.d(a,{A:()=>l});var n=t(95155),r=t(60760),i=t(44518),o=t(95233),s=t(54416);t(12115);let l=e=>{let{children:a,className:t,isOpen:l,onClose:c,preventOutsideClick:u=!1}=e;return(0,n.jsx)(r.N,{children:l&&(0,n.jsx)(i.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-neutral-900/50 flex items-center justify-center z-40 p-4 overflow-y-auto",onClick:e=>{u||c()},children:(0,n.jsxs)(i.P.div,{initial:{scale:.6,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.6,opacity:0},transition:{duration:.3,ease:o.am},className:"relative section flex flex-col max-w-4xl max-h-[90vh] overflow-y-auto ".concat(t),onClick:e=>e.stopPropagation(),children:[(0,n.jsx)(s.A,{onClick:c,className:"absolute self-end cursor-pointer text-neutral-700 hover:text-neutral-900 active:scale-90 transition-all duration-300"}),a]})})})}},14560:(e,a,t)=>{Promise.resolve().then(t.bind(t,29024))},25784:(e,a,t)=>{"use strict";t.d(a,{A:()=>r});let n=t(23464).A.create({baseURL:"http://localhost:4000/api",headers:{"Content-Type":"application/json"},withCredentials:!0});n.interceptors.request.use(e=>e,e=>Promise.reject(e)),n.interceptors.response.use(e=>e,e=>("ERR_NETWORK"===e.code&&console.error("Network error - Please check if the backend server is running"),Promise.reject(e)));let r=n},29024:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>E});var n=t(95155),r=t(12115),i=t(62177),o=t(25784),s=t(35695),l=t(88570),c=t(26715),u=t(19373),d=t(5041),m=t(77361),p=t(34540),h=t(71402),g=t(50408),v=t(57434),y=t(34869),x=t(17576),b=t(57799),f=t(2511),j=t(74567),N=t(64368),S=t(29350),C=t(10786),k=t(63642),_=t(17652);let w=async e=>{let{projectId:a,dataToSend:t}=e,{data:n}=await o.A.patch("/projects/".concat(a),t);return n},E=()=>{let[e,a]=(0,r.useState)(!1);(0,r.useEffect)(()=>{a(!0)},[]);let{register:t,formState:{isSubmitting:o,errors:E,isSubmitted:P},handleSubmit:A,setValue:D,reset:M}=(0,i.mN)(),z=(0,s.useRouter)(),[B,L]=(0,r.useState)(!1),T=(0,_.c3)(),[F,H]=(0,r.useState)(null),[I,R]=(0,r.useState)(null),[K,q]=(0,r.useState)(!1),[G,U]=(0,r.useState)(!1),[Q,V]=(0,r.useState)(null);(0,r.useEffect)(()=>{t("country",{required:"Please select a country"}),t("sector",{required:"Please select a sector"})},[t]),(0,r.useEffect)(()=>{D("country",F,{shouldValidate:P}),D("sector",I,{shouldValidate:P})},[D,F,I]);let{hashedId:O}=(0,s.useParams)(),W=(0,l.D)(O),{user:J}=(0,S.A)(),Z=(0,c.jE)();(0,r.useEffect)(()=>()=>{W&&(null==J?void 0:J.id)&&Z.cancelQueries({queryKey:["projects",J.id,W]})},[W,null==J?void 0:J.id,Z]);let{data:X,isLoading:Y,isError:$}=(0,u.I)({queryKey:["projects",null==J?void 0:J.id,W],queryFn:()=>(0,m.kf)({projectId:W}),enabled:!!W&&!!(null==J?void 0:J.id)&&!B});(0,r.useEffect)(()=>{X&&(M({projectName:X.name||"",description:X.description||"",country:X.country||"",sector:X.sector||""}),H(X.country||null),R(X.sector||null))},[X,M]);let ee=(0,p.wA)(),ea=(0,d.n)({mutationFn:w,onSuccess:()=>{Z.invalidateQueries({queryKey:["projects",null==J?void 0:J.id],exact:!1}),ee((0,h.Ds)({message:T("projectUpdated"),type:"success"}))},onError:e=>{ee((0,h.Ds)({message:T("projectUpdateFailed")+e.message,type:"error"}))}}),et=(0,d.n)({mutationFn:e=>(0,m.pf)(W,(null==e?void 0:e.isUnarchive)||!1),onSuccess:(e,a)=>{let t=(null==a?void 0:a.isUnarchive)||!1;Z.invalidateQueries({queryKey:["projects",null==J?void 0:J.id]}),ee((0,h.Ds)({message:t?T("projectUnarchived"):T("projectDeployed"),type:"success"})),U(!1)},onError:e=>{ee((0,h.Ds)({message:T("projectDeployFailed"),type:"error"})),U(!1)}}),en=(0,d.n)({mutationFn:()=>(0,m.Im)(W),onSuccess:()=>{Z.invalidateQueries({queryKey:["projects",null==J?void 0:J.id,W]}),ee((0,h.Ds)({message:T("projectArchived"),type:"success"})),U(!1),z.push("/dashboard"),Z.invalidateQueries({queryKey:["projects",null==J?void 0:J.id]})},onError:e=>{console.error("Project archive error:",e),ee((0,h.Ds)({message:T("projectArchiveFailed"),type:"error"})),U(!1)}}),er=(0,d.n)({mutationFn:()=>(0,m.xx)(W),onSuccess:()=>{L(!0),U(!1),Z.cancelQueries({queryKey:["projects",null==J?void 0:J.id,W]}),Z.removeQueries({queryKey:["projects",null==J?void 0:J.id,W]}),Z.invalidateQueries({queryKey:["projects",null==J?void 0:J.id]}),ee((0,h.Ds)({message:T("projectDeleted"),type:"success"})),setTimeout(()=>{z.push("/dashboard")},1e3)},onError:e=>{U(!1),console.error("Project deletion error:",e),ee((0,h.Ds)({message:T("projectDeleteFailed"),type:"error"}))}}),ei=()=>{let e=(null==X?void 0:X.status)==="archived",a=T("confirmDeployMessage");(null==X?void 0:X.status)==="deployed"?a=T("confirmRedeployMessage"):(null==X?void 0:X.status)==="archived"&&(a=T("confirmDeployMessage")),V({title:e?T("confirmUnarchive"):T("confirmDeploy"),description:a,confirmButtonText:e?T("unarchive"):T("deploy"),confirmButtonClass:"btn-primary",onConfirm:()=>{et.mutate({isUnarchive:e})}}),U(!0)},eo=async e=>{ea.mutate({projectId:W,dataToSend:{name:e.projectName,description:e.description,country:e.country,sector:e.sector}})};return e?B||Y?(0,n.jsx)(b.A,{}):O&&null!==W?$&&!B?(0,n.jsx)("p",{className:"text-red-500",children:T("fetchProjectFailed")}):(0,n.jsxs)("form",{className:"flex flex-col gap-8",onSubmit:A(eo),children:[(0,n.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,n.jsxs)("div",{className:"label-input-group group",children:[(0,n.jsxs)("label",{htmlFor:"project-name",className:"label-text",children:[(0,n.jsx)(v.A,{size:16})," ",T("projectName")]}),(0,n.jsx)("input",{...t("projectName",{required:T("projectNameRequired")}),id:"project-name",type:"text",className:"input-field",placeholder:T("enterProjectName")}),E.projectName&&(0,n.jsx)("p",{className:"text-red-500 text-sm",children:"".concat(E.projectName.message)})]}),(0,n.jsxs)("div",{className:"label-input-group group",children:[(0,n.jsx)("label",{htmlFor:"description",className:"label-text",children:T("description")}),(0,n.jsx)("textarea",{id:"description",...t("description"),className:"input-field resize-none",cols:4,placeholder:T("enterProjectDescription")})]}),(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{className:"label-input-group group",children:[(0,n.jsxs)("label",{htmlFor:"country",className:"label-text",children:[(0,n.jsx)(y.A,{size:16}),T("country")]}),(0,n.jsx)(g.l,{id:"country",options:j,value:F,onChange:H}),E.country&&(0,n.jsx)("p",{className:"text-red-500 text-sm",children:"".concat(E.country.message)})]}),(0,n.jsxs)("div",{className:"label-input-group group",children:[(0,n.jsxs)("label",{htmlFor:"sector",className:"label-text",children:[(0,n.jsx)(x.A,{size:16})," ",T("sector")]}),(0,n.jsx)(g.l,{id:"sector",options:Object.values(f.b),value:I&&f.b[I]?f.b[I]:T("selectOption"),onChange:e=>{R((0,N.H)(e,f.b))}}),E.sector&&(0,n.jsx)("p",{className:"text-red-500 text-sm",children:"".concat(E.sector.message)})]})]}),(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex items-center gap-4",children:[(null==X?void 0:X.status)==="deployed"&&(0,n.jsx)("button",{onClick:()=>{V({title:T("confirmArchive"),description:(0,n.jsx)(n.Fragment,{children:T("confirmArchiveMessage")}),confirmButtonText:T("archive"),confirmButtonClass:"btn-primary",onConfirm:()=>{en.mutate()}}),U(!0)},type:"button",className:"btn-outline",children:T("archive")}),(null==X?void 0:X.status)==="deployed"&&(0,n.jsx)("button",{onClick:ei,type:"button",className:"btn-outline",children:T("redeploy")}),(null==X?void 0:X.status)==="archived"&&(0,n.jsx)("button",{onClick:ei,type:"button",className:"btn-outline",children:T("deploy")}),(null==X?void 0:X.status)==="draft"&&(0,n.jsx)("button",{onClick:ei,type:"button",className:"btn-outline",children:T("deploy")}),(0,n.jsx)("button",{type:"button",className:"btn-outline",onClick:()=>{q(!0)},children:T("share")}),(0,n.jsx)("button",{onClick:()=>{V({title:T("confirmDelete"),description:(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("p",{children:T("confirmDeleteMessage")}),(0,n.jsxs)("ul",{className:"list-disc list-inside flex flex-col gap-2 mt-2 text-neutral-700",children:[(0,n.jsx)("li",{children:T("deleteProjectWarning1")}),(0,n.jsx)("li",{children:T("deleteProjectWarning2")}),(0,n.jsx)("li",{children:T("deleteProjectWarning3")})]})]}),confirmButtonText:T("delete"),confirmButtonClass:"btn-danger",onConfirm:()=>{er.mutate()}}),U(!0)},type:"button",className:"btn-danger",children:T("delete")})]}),(0,n.jsx)("button",{type:"submit",className:"btn-primary self-end",children:o?(0,n.jsxs)("span",{className:"flex items-center gap-2",children:[T("saving"),(0,n.jsx)("div",{className:"size-4 animate-spin border-x border-neutral-100 rounded-full"})]}):T("saveChanges")})]})]}),(0,n.jsx)(C.m,{showModal:K,onClose:()=>q(!1),onShare:()=>{q(!1)}}),Q&&(0,n.jsx)(k.R,{showModal:G,onClose:()=>U(!1),title:Q.title,description:Q.description,confirmButtonText:Q.confirmButtonText,confirmButtonClass:Q.confirmButtonClass,onConfirm:Q.onConfirm})]}):(0,n.jsxs)("div",{className:"error-message",children:[(0,n.jsx)("h1",{className:"text-red-500",children:T("invalidProjectIdError")}),(0,n.jsx)("p",{className:"text-neutral-700",children:T("invalidProjectIdMessage")})]}):null}},50408:(e,a,t)=>{"use strict";t.d(a,{l:()=>o});var n=t(95155),r=t(66474),i=t(12115);let o=e=>{let{id:a,options:t,value:o,onChange:s}=e,[l,c]=(0,i.useState)(!1),u=(0,i.useRef)(null),d=(0,i.useRef)([]),m=(0,i.useRef)(null);(0,i.useEffect)(()=>{let e=e=>{m.current&&!m.current.contains(e.target)&&c(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]);let p=e=>{if(!l)return;let a=e.key.toLowerCase();if(a.match(/[a-z]/)){let e=t.findIndex(e=>e.toLowerCase().startsWith(a));if(-1!==e&&d.current[e]){var n;null==(n=d.current[e])||n.scrollIntoView({behavior:"auto",block:"nearest"})}}};return(0,i.useEffect)(()=>(document.addEventListener("keydown",p),()=>{document.removeEventListener("keydown",p)}),[l,t]),(0,n.jsxs)("div",{className:"relative",ref:m,children:[(0,n.jsxs)("button",{id:a,type:"button",className:"px-4 py-2 flex items-center justify-between rounded-md border border-neutral-400 focus:border-primary-500 duration-300 w-full text-left cursor-pointer",onClick:()=>{c(!l)},children:[(0,n.jsx)("span",{children:o||"Select an option"}),(0,n.jsx)(r.A,{})]}),l&&(0,n.jsx)("ul",{className:"absolute z-10 max-h-[180px] overflow-auto border border-neutral-400 rounded-md bg-neutral-100 w-full mt-1 flex flex-col",ref:u,children:t.map((e,a)=>(0,n.jsx)("li",{ref:e=>{d.current[a]=e},className:"cursor-pointer bg-neutral-100 hover:bg-neutral-200 px-4 py-2",onClick:()=>{s(e),c(!1)},children:e},a))})]})}},53999:(e,a,t)=>{"use strict";t.d(a,{Y:()=>o,cn:()=>i});var n=t(52596),r=t(39688);function i(){for(var e=arguments.length,a=Array(e),t=0;t<e;t++)a[t]=arguments[t];return(0,r.QP)((0,n.$)(a))}function o(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"short";if(!e)return"";try{let t="string"==typeof e?new Date(e):e;if(isNaN(t.getTime()))return"";switch(a){case"short":return t.toLocaleDateString(void 0,{year:"numeric",month:"short",day:"numeric"});case"long":return t.toLocaleDateString(void 0,{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"});case"full":return t.toLocaleDateString(void 0,{year:"numeric",month:"long",day:"numeric",weekday:"long",hour:"2-digit",minute:"2-digit",second:"2-digit"});default:return t.toLocaleDateString()}}catch(a){return console.error("Error formatting date:",a),String(e)}}},64368:(e,a,t)=>{"use strict";t.d(a,{H:()=>n});let n=(e,a)=>{let t=Object.entries(a).find(a=>{let[t,n]=a;return n===e});return t?t[0]:null}},74567:e=>{"use strict";e.exports=JSON.parse('["Afghanistan","Albania","Algeria","Andorra","Angola","Argentina","Armenia","Australia","Austria","Azerbaijan","Bahamas","Bahrain","Bangladesh","Barbados","Belarus","Belgium","Belize","Benin","Bhutan","Bolivia","Bosnia and Herzegovina","Botswana","Brazil","Brunei","Bulgaria","Burkina Faso","Burundi","Cabo Verde","Cambodia","Cameroon","Canada","Central African Republic","Chad","Chile","China","Colombia","Comoros","Congo (Congo-Brazzaville)","Costa Rica","Croatia","Cuba","Cyprus","Czech Republic","Denmark","Djibouti","Dominica","Dominican Republic","Ecuador","Egypt","El Salvador","Equatorial Guinea","Eritrea","Estonia","Eswatini","Ethiopia","Fiji","Finland","France","Gabon","Gambia","Georgia","Germany","Ghana","Greece","Grenada","Guatemala","Guinea","Guinea-Bissau","Guyana","Haiti","Honduras","Hungary","Iceland","India","Indonesia","Iran","Iraq","Ireland","Israel","Italy","Jamaica","Japan","Jordan","Kazakhstan","Kenya","Kiribati","Kuwait","Kyrgyzstan","Laos","Latvia","Lebanon","Lesotho","Liberia","Libya","Liechtenstein","Lithuania","Luxembourg","Madagascar","Malawi","Malaysia","Maldives","Mali","Malta","Marshall Islands","Mauritania","Mauritius","Mexico","Micronesia","Moldova","Monaco","Mongolia","Montenegro","Morocco","Mozambique","Myanmar","Namibia","Nauru","Nepal","Netherlands","New Zealand","Nicaragua","Niger","Nigeria","North Korea","North Macedonia","Norway","Oman","Pakistan","Palau","Palestine","Panama","Papua New Guinea","Paraguay","Peru","Philippines","Poland","Portugal","Qatar","Romania","Russia","Rwanda","Saint Kitts and Nevis","Saint Lucia","Saint Vincent and the Grenadines","Samoa","San Marino","Sao Tome and Principe","Saudi Arabia","Senegal","Serbia","Seychelles","Sierra Leone","Singapore","Slovakia","Slovenia","Solomon Islands","Somalia","South Africa","South Korea","South Sudan","Spain","Sri Lanka","Sudan","Suriname","Sweden","Switzerland","Syria","Taiwan","Tajikistan","Tanzania","Thailand","Timor-Leste","Togo","Tonga","Trinidad and Tobago","Tunisia","Turkey","Turkmenistan","Tuvalu","Uganda","Ukraine","United Arab Emirates","United Kingdom","United States","Uruguay","Uzbekistan","Vanuatu","Vatican City","Venezuela","Vietnam","Yemen","Zambia","Zimbabwe"]')},88570:(e,a,t)=>{"use strict";t.d(a,{D:()=>s,l:()=>o});var n=t(41050);let r=t(49509).env.SALT||"rushan-salt",i=new n.A(r,12),o=e=>i.encode(e),s=e=>{let a=i.decode(e)[0];return"bigint"==typeof a?a<Number.MAX_SAFE_INTEGER?Number(a):null:"number"==typeof a?a:null}},97168:(e,a,t)=>{"use strict";t.d(a,{$:()=>l});var n=t(95155);t(12115);var r=t(99708),i=t(74466),o=t(53999);let s=(0,i.F)("inline-flex items-center justify-center gap-2 neutral-100space-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-neutral-100 shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:a,variant:t,size:i,asChild:l=!1,...c}=e,u=l?r.DX:"button";return(0,n.jsx)(u,{"data-slot":"button",className:(0,o.cn)(s({variant:t,size:i,className:a})),...c})}}},e=>{var a=a=>e(e.s=a);e.O(0,[2150,6453,635,1111,6967,9373,4601,1380,4277,740,3465,8441,1684,7358],()=>a(14560)),_N_E=e.O()}]);