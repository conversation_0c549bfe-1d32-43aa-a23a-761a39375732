"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7823],{9428:(e,t,r)=>{r.d(t,{A:()=>i});let i=(0,r(19946).A)("circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},13052:(e,t,r)=>{r.d(t,{A:()=>i});let i=(0,r(19946).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},40968:(e,t,r)=>{r.d(t,{b:()=>l});var i=r(12115),n=r(63655),s=r(95155),a=i.forwardRef((e,t)=>(0,s.jsx)(n.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));a.displayName="Label";var l=a},41050:(e,t,r)=>{r.d(t,{A:()=>y});let i=e=>[...new Set(e)],n=(e,t)=>e.filter(e=>!t.includes(e)),s=(e,t)=>e.filter(e=>t.includes(e)),a=e=>"bigint"==typeof e||!Number.isNaN(Number(e))&&Math.floor(Number(e))===e,l=e=>"bigint"==typeof e||e>=0&&Number.isSafeInteger(e);function o(e,t){let r;if(0===t.length)return e;let i=[...e];for(let e=i.length-1,n=0,s=0;e>0;e--,n++){n%=t.length,s+=r=t[n].codePointAt(0);let a=(r+n+s)%e,l=i[e],o=i[a];i[a]=l,i[e]=o}return i}let h=(e,t)=>{let r=[],i=e;if("bigint"==typeof i){let e=BigInt(t.length);do r.unshift(t[Number(i%e)]),i/=e;while(i>BigInt(0))}else do r.unshift(t[i%t.length]),i=Math.floor(i/t.length);while(i>0);return r},d=(e,t)=>e.reduce((r,i)=>{let n=t.indexOf(i);if(-1===n)throw Error(`The provided ID (${e.join("")}) is invalid, as it contains characters that do not exist in the alphabet (${t.join("")})`);if("bigint"==typeof r)return r*BigInt(t.length)+BigInt(n);let s=r*t.length+n;return Number.isSafeInteger(s)?s:(m("Unable to decode the provided string, due to lack of support for BigInt numbers in the current environment"),BigInt(r)*BigInt(t.length)+BigInt(n))},0),u=/^\+?\d+$/,p=e=>{if(!u.test(e))return Number.NaN;let t=Number.parseInt(e,10);return Number.isSafeInteger(t)?t:(m("Unable to encode the provided BigInt string without loss of information due to lack of support for BigInt type in the current environment"),BigInt(e))},c=(e,t,r)=>Array.from({length:Math.ceil(e.length/t)},(i,n)=>r(e.slice(n*t,(n+1)*t))),g=e=>new RegExp(e.map(e=>b(e)).sort((e,t)=>t.length-e.length).join("|")),f=e=>RegExp(`^[${e.map(e=>b(e)).sort((e,t)=>t.length-e.length).join("")}]+$`),b=e=>e.replace(/[\s#$()*+,.?[\\\]^{|}-]/g,"\\$&"),m=(e="BigInt is not available in this environment")=>{if("function"!=typeof BigInt)throw TypeError(e)};class y{constructor(e="",t=0,r="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890",a="cfhistuCFHISTU"){let l,h;if(this.minLength=t,"number"!=typeof t)throw TypeError(`Hashids: Provided 'minLength' has to be a number (is ${typeof t})`);if("string"!=typeof e)throw TypeError(`Hashids: Provided 'salt' has to be a string (is ${typeof e})`);if("string"!=typeof r)throw TypeError(`Hashids: Provided alphabet has to be a string (is ${typeof r})`);let d=Array.from(e),u=Array.from(r),p=Array.from(a);this.salt=d;let c=i(u);if(c.length<16)throw Error(`Hashids: alphabet must contain at least 16 unique characters, provided: ${c.join("")}`);this.alphabet=n(c,p);let b=s(p,c);this.seps=o(b,d),(0===this.seps.length||this.alphabet.length/this.seps.length>3.5)&&(l=Math.ceil(this.alphabet.length/3.5))>this.seps.length&&(h=l-this.seps.length,this.seps.push(...this.alphabet.slice(0,h)),this.alphabet=this.alphabet.slice(h)),this.alphabet=o(this.alphabet,d);let m=Math.ceil(this.alphabet.length/12);this.alphabet.length<3?(this.guards=this.seps.slice(0,m),this.seps=this.seps.slice(m)):(this.guards=this.alphabet.slice(0,m),this.alphabet=this.alphabet.slice(m)),this.guardsRegExp=g(this.guards),this.sepsRegExp=g(this.seps),this.allowedCharsRegExp=f([...this.alphabet,...this.guards,...this.seps])}encode(e,...t){let r=Array.isArray(e)?e:[...null!=e?[e]:[],...t];return 0===r.length?"":(r.every(a)||(r=r.map(e=>"bigint"==typeof e||"number"==typeof e?e:p(String(e)))),r.every(l))?this._encode(r).join(""):""}decode(e){return e&&"string"==typeof e&&0!==e.length?this._decode(e):[]}encodeHex(e){let t=e;switch(typeof t){case"bigint":t=t.toString(16);break;case"string":if(!/^[\dA-Fa-f]+$/.test(t))return"";break;default:throw Error(`Hashids: The provided value is neither a string, nor a BigInt (got: ${typeof t})`)}let r=c(t,12,e=>Number.parseInt(`1${e}`,16));return this.encode(r)}decodeHex(e){return this.decode(e).map(e=>e.toString(16).slice(1)).join("")}isValidId(e){return this.allowedCharsRegExp.test(e)}_encode(e){let{alphabet:t}=this,r=e.reduce((e,t,r)=>e+("bigint"==typeof t?Number(t%BigInt(r+100)):t%(r+100)),0),i=[t[r%t.length]],n=[...i],{seps:s}=this,{guards:a}=this;if(e.forEach((r,a)=>{let l=n.concat(this.salt,t),d=h(r,t=o(t,l));if(i.push(...d),a+1<e.length){let e=d[0].codePointAt(0)+a,t="bigint"==typeof r?Number(r%BigInt(e)):r%e;i.push(s[t%s.length])}}),i.length<this.minLength){let e=(r+i[0].codePointAt(0))%a.length;if(i.unshift(a[e]),i.length<this.minLength){let e=(r+i[2].codePointAt(0))%a.length;i.push(a[e])}}let l=Math.floor(t.length/2);for(;i.length<this.minLength;){t=o(t,t),i.unshift(...t.slice(l)),i.push(...t.slice(0,l));let e=i.length-this.minLength;if(e>0){let t=e/2;i=i.slice(t,t+this.minLength)}}return i}_decode(e){if(!this.isValidId(e))throw Error(`The provided ID (${e}) is invalid, as it contains characters that do not exist in the alphabet (${this.guards.join("")}${this.seps.join("")}${this.alphabet.join("")})`);let t=e.split(this.guardsRegExp),r=+(3===t.length||2===t.length),i=t[r];if(0===i.length)return[];let n=i[Symbol.iterator]().next().value,s=i.slice(n.length).split(this.sepsRegExp),a=this.alphabet,l=[];for(let e of s){let t=[n,...this.salt,...a],r=o(a,t.slice(0,a.length));l.push(d(Array.from(e),r)),a=r}return this._encode(l).join("")!==e?[]:l}}},54059:(e,t,r)=>{r.d(t,{C1:()=>D,bL:()=>M,q7:()=>q});var i=r(12115),n=r(85185),s=r(6101),a=r(46081),l=r(63655),o=r(89196),h=r(5845),d=r(94315),u=r(11275),p=r(45503),c=r(28905),g=r(95155),f="Radio",[b,m]=(0,a.A)(f),[y,v]=b(f),w=i.forwardRef((e,t)=>{let{__scopeRadio:r,name:a,checked:o=!1,required:h,disabled:d,value:u="on",onCheck:p,form:c,...f}=e,[b,m]=i.useState(null),v=(0,s.s)(t,e=>m(e)),w=i.useRef(!1),I=!b||c||!!b.closest("form");return(0,g.jsxs)(y,{scope:r,checked:o,disabled:d,children:[(0,g.jsx)(l.sG.button,{type:"button",role:"radio","aria-checked":o,"data-state":E(o),"data-disabled":d?"":void 0,disabled:d,value:u,...f,ref:v,onClick:(0,n.m)(e.onClick,e=>{o||null==p||p(),I&&(w.current=e.isPropagationStopped(),w.current||e.stopPropagation())})}),I&&(0,g.jsx)(x,{control:b,bubbles:!w.current,name:a,value:u,checked:o,required:h,disabled:d,form:c,style:{transform:"translateX(-100%)"}})]})});w.displayName=f;var I="RadioIndicator",k=i.forwardRef((e,t)=>{let{__scopeRadio:r,forceMount:i,...n}=e,s=v(I,r);return(0,g.jsx)(c.C,{present:i||s.checked,children:(0,g.jsx)(l.sG.span,{"data-state":E(s.checked),"data-disabled":s.disabled?"":void 0,...n,ref:t})})});k.displayName=I;var x=i.forwardRef((e,t)=>{let{__scopeRadio:r,control:n,checked:a,bubbles:o=!0,...h}=e,d=i.useRef(null),c=(0,s.s)(d,t),f=(0,p.Z)(a),b=(0,u.X)(n);return i.useEffect(()=>{let e=d.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(f!==a&&t){let r=new Event("click",{bubbles:o});t.call(e,a),e.dispatchEvent(r)}},[f,a,o]),(0,g.jsx)(l.sG.input,{type:"radio","aria-hidden":!0,defaultChecked:a,...h,tabIndex:-1,ref:c,style:{...h.style,...b,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function E(e){return e?"checked":"unchecked"}x.displayName="RadioBubbleInput";var R=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],A="RadioGroup",[j,N]=(0,a.A)(A,[o.RG,m]),B=(0,o.RG)(),$=m(),[C,L]=j(A),P=i.forwardRef((e,t)=>{let{__scopeRadioGroup:r,name:i,defaultValue:n,value:s,required:a=!1,disabled:u=!1,orientation:p,dir:c,loop:f=!0,onValueChange:b,...m}=e,y=B(r),v=(0,d.jH)(c),[w,I]=(0,h.i)({prop:s,defaultProp:null!=n?n:"",onChange:b,caller:A});return(0,g.jsx)(C,{scope:r,name:i,required:a,disabled:u,value:w,onValueChange:I,children:(0,g.jsx)(o.bL,{asChild:!0,...y,orientation:p,dir:v,loop:f,children:(0,g.jsx)(l.sG.div,{role:"radiogroup","aria-required":a,"aria-orientation":p,"data-disabled":u?"":void 0,dir:v,...m,ref:t})})})});P.displayName=A;var S="RadioGroupItem",G=i.forwardRef((e,t)=>{let{__scopeRadioGroup:r,disabled:a,...l}=e,h=L(S,r),d=h.disabled||a,u=B(r),p=$(r),c=i.useRef(null),f=(0,s.s)(t,c),b=h.value===l.value,m=i.useRef(!1);return i.useEffect(()=>{let e=e=>{R.includes(e.key)&&(m.current=!0)},t=()=>m.current=!1;return document.addEventListener("keydown",e),document.addEventListener("keyup",t),()=>{document.removeEventListener("keydown",e),document.removeEventListener("keyup",t)}},[]),(0,g.jsx)(o.q7,{asChild:!0,...u,focusable:!d,active:b,children:(0,g.jsx)(w,{disabled:d,required:h.required,checked:b,...p,...l,name:h.name,ref:f,onCheck:()=>h.onValueChange(l.value),onKeyDown:(0,n.m)(e=>{"Enter"===e.key&&e.preventDefault()}),onFocus:(0,n.m)(l.onFocus,()=>{var e;m.current&&(null==(e=c.current)||e.click())})})})});G.displayName=S;var H=i.forwardRef((e,t)=>{let{__scopeRadioGroup:r,...i}=e,n=$(r);return(0,g.jsx)(k,{...n,...i,ref:t})});H.displayName="RadioGroupIndicator";var M=P,q=G,D=H},66474:(e,t,r)=>{r.d(t,{A:()=>i});let i=(0,r(19946).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},92138:(e,t,r)=>{r.d(t,{A:()=>i});let i=(0,r(19946).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])}}]);