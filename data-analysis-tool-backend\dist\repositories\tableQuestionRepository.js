"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const prisma_1 = require("../utils/prisma");
/**
 * TableQuestionRepository
 *
 * Handles all database operations related to table questions.
 * Responsible for:
 * - Creating, reading, updating, and deleting table questions
 * - Managing table columns and rows
 * - Handling cell values
 * - Maintaining parent-child relationships between columns
 */
class TableQuestionRepository {
    /**
     * Create a new table question with columns and rows
     *
     * @param data - Object containing table question data
     * @param data.label - Label for the table question
     * @param data.projectId - ID of the project this table belongs to
     * @param data.columns - Array of column objects with names and optional parent IDs
     * @param data.rows - Array of row objects with names
     * @returns The created table question with its columns and rows
     * @throws Error if validation fails or database operation fails
     */
    createTableQuestion(data) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                // Validate input data
                if (!data.label || !data.label.trim()) {
                    throw new Error("Table label is required");
                }
                if (!data.projectId || isNaN(data.projectId)) {
                    throw new Error("Valid project ID is required");
                }
                if (!data.columns ||
                    !Array.isArray(data.columns) ||
                    data.columns.length === 0) {
                    throw new Error("At least one column is required");
                }
                // Rows are optional - validate only if provided
                if (data.rows && !Array.isArray(data.rows)) {
                    throw new Error("Rows must be an array if provided");
                }
                // Validate column data
                for (const column of data.columns) {
                    if (!column.columnName || !column.columnName.trim()) {
                        throw new Error("All columns must have valid names");
                    }
                    // If parentColumnId is provided, make sure it's a valid number
                    if (column.parentColumnId !== undefined) {
                        if (isNaN(column.parentColumnId)) {
                            throw new Error("Invalid parent column ID");
                        }
                        // Check if the parent column exists
                        const parentColumn = data.columns.find((_, idx) => idx + 1 === column.parentColumnId);
                        if (!parentColumn) {
                            throw new Error(`Parent column with position ${column.parentColumnId} not found`);
                        }
                        // Check if the parent column is already a child column
                        if (parentColumn.parentColumnId !== undefined) {
                            throw new Error("Child columns cannot have their own children. Only parent columns can have children.");
                        }
                    }
                }
                // Count child columns per parent to enforce maximum of 2 children
                const parentChildCount = new Map();
                data.columns.forEach((column) => {
                    if (column.parentColumnId !== undefined) {
                        const parentPos = column.parentColumnId;
                        parentChildCount.set(parentPos, (parentChildCount.get(parentPos) || 0) + 1);
                        if (parentChildCount.get(parentPos) > 2) {
                            const parentColumn = data.columns.find((_, i) => i + 1 === parentPos);
                            throw new Error(`Parent column "${(parentColumn === null || parentColumn === void 0 ? void 0 : parentColumn.columnName) || "Unknown"}" cannot have more than 2 child columns`);
                        }
                    }
                });
                // Validate row data if provided
                if (data.rows) {
                    for (const row of data.rows) {
                        if (!row.rowsName || !row.rowsName.trim()) {
                            throw new Error("All rows must have valid names");
                        }
                    }
                }
                // Use a transaction to ensure data consistency
                return yield prisma_1.prisma.$transaction((tx) => __awaiter(this, void 0, void 0, function* () {
                    var _a, _b, _c;
                    // Determine the next position for the question
                    const lastOrder = yield tx.projectQuestionOrder.findFirst({
                        where: {
                            parentGroupId: (_a = data.parentGroupId) !== null && _a !== void 0 ? _a : null,
                            projectId: data.projectId,
                        },
                        orderBy: {
                            position: "desc",
                        },
                    });
                    const nextPosition = (_b = data.position) !== null && _b !== void 0 ? _b : (lastOrder ? lastOrder.position + 1 : 1);
                    // Create the question
                    const question = yield tx.question.create({
                        data: Object.assign({ label: data.label.trim(), inputType: "table", projectId: data.projectId, position: nextPosition }, (data.rows &&
                            data.rows.length > 0 && {
                            tableRows: {
                                create: data.rows.map((row) => ({
                                    rowsName: row.rowsName.trim(),
                                })),
                            },
                        })),
                        include: {
                            tableRows: true,
                        },
                    });
                    // Separate columns into parent and child columns
                    const parentColumns = data.columns.filter((col) => col.parentColumnId === undefined);
                    const childColumns = data.columns.filter((col) => col.parentColumnId !== undefined);
                    // Create parent columns first
                    const createdParentColumns = yield Promise.all(parentColumns.map((col) => tx.tableColumn.create({
                        data: {
                            columnName: col.columnName.trim(),
                            questionId: question.id,
                        },
                    })));
                    // Create a mapping of original column index to created column ID
                    const columnMapping = new Map();
                    parentColumns.forEach((col, index) => {
                        columnMapping.set(data.columns.indexOf(col), createdParentColumns[index].id);
                    });
                    // Create child columns with correct parent column IDs
                    if (childColumns.length > 0) {
                        const createdColumnMap = new Map();
                        parentColumns.forEach((_, index) => {
                            const position = data.columns.indexOf(parentColumns[index]) + 1;
                            createdColumnMap.set(position, createdParentColumns[index].id);
                        });
                        for (const col of childColumns) {
                            const parentPosition = col.parentColumnId;
                            const actualParentId = parentPosition !== undefined
                                ? createdColumnMap.get(parentPosition)
                                : undefined;
                            if (actualParentId) {
                                const createdColumn = yield tx.tableColumn.create({
                                    data: {
                                        columnName: col.columnName.trim(),
                                        questionId: question.id,
                                        parentColumnId: actualParentId,
                                    },
                                });
                                const position = data.columns.indexOf(col) + 1;
                                createdColumnMap.set(position, createdColumn.id);
                            }
                            else {
                                console.warn(`Could not find parent ID for column "${col.columnName}" with parent position ${parentPosition}`);
                            }
                        }
                        // Check for unprocessed columns
                        const processedPositions = Array.from(createdColumnMap.keys());
                        const allPositions = data.columns.map((_, idx) => idx + 1);
                        const unprocessedPositions = allPositions.filter((pos) => !processedPositions.includes(pos));
                        if (unprocessedPositions.length > 0) {
                            console.warn(`${unprocessedPositions.length} columns were not processed:`, unprocessedPositions.map((pos) => {
                                const col = data.columns[pos - 1];
                                return `${col.columnName} (position: ${pos}, parent: ${col.parentColumnId})`;
                            }));
                        }
                    }
                    // Create the project question order entry
                    yield tx.projectQuestionOrder.create({
                        data: {
                            questionId: question.id,
                            groupId: null, // Set to null unless you have a specific group ID
                            type: "question",
                            position: nextPosition,
                            parentGroupId: (_c = data.parentGroupId) !== null && _c !== void 0 ? _c : null,
                            projectId: data.projectId,
                        },
                    });
                    // Return the complete question with all related data
                    return tx.question.findUnique({
                        where: { id: question.id },
                        include: {
                            tableColumns: {
                                include: {
                                    childColumns: true,
                                },
                            },
                            tableRows: true,
                        },
                    });
                }));
            }
            catch (error) {
                console.error("Error in createTableQuestion repository method:", error);
                throw error;
            }
        });
    }
    /**
     * Get a table question by ID with all its columns and rows
     *
     * @param id - ID of the table question to retrieve
     * @returns The table question with its columns and rows, or null if not found
     */
    getTableQuestionById(id) {
        return __awaiter(this, void 0, void 0, function* () {
            // First get all columns for this question
            const columns = yield prisma_1.prisma.tableColumn.findMany({
                where: {
                    questionId: id,
                    parentColumnId: null, // Get only parent columns
                },
                include: {
                    childColumns: {
                        orderBy: {
                            id: "asc",
                        },
                    },
                },
                orderBy: {
                    id: "asc",
                },
            });
            // Get the question with its rows
            const question = yield prisma_1.prisma.question.findUnique({
                where: { id },
                include: {
                    tableRows: {
                        orderBy: {
                            id: "asc",
                        },
                    },
                },
            });
            if (!question) {
                return null;
            }
            // Combine the results
            return Object.assign(Object.assign({}, question), { tableColumns: columns });
        });
    }
    /**
     * Save cell values for a table question
     *
     * @param data - Object containing cell values data
     * @param data.questionId - ID of the table question
     * @param data.cellValues - Array of cell value objects
     * @returns The created or updated cell values
     * @throws Error if the table question is not found
     */
    saveCellValues(data) {
        return __awaiter(this, void 0, void 0, function* () {
            // First check if the question exists
            const question = yield prisma_1.prisma.question.findUnique({
                where: { id: data.questionId },
                include: { tableColumns: true, tableRows: true },
            });
            if (!question) {
                throw new Error("Table question not found");
            }
            // Create or update cell values
            const cellUpdates = data.cellValues.map((cell) => {
                return prisma_1.prisma.columnRow.upsert({
                    where: {
                        columnId_rowsId: {
                            columnId: cell.columnId,
                            rowsId: cell.rowsId,
                        },
                    },
                    update: {
                        value: cell.value,
                        code: cell.code,
                    },
                    create: {
                        columnId: cell.columnId,
                        rowsId: cell.rowsId,
                        value: cell.value,
                        code: cell.code,
                    },
                });
            });
            return prisma_1.prisma.$transaction(cellUpdates);
        });
    }
    /**
     * Get all cell values for a table question
     *
     * @param questionId - ID of the table question
     * @returns A map of cell values indexed by columnId_rowsId
     */
    getCellValues(questionId) {
        return __awaiter(this, void 0, void 0, function* () {
            const cellValues = yield prisma_1.prisma.columnRow.findMany({
                where: {
                    column: {
                        questionId,
                    },
                },
                include: {
                    column: true,
                    row: true,
                },
            });
            // Format the cell values as a map for easier access
            const cellValueMap = {};
            cellValues.forEach((cell) => {
                cellValueMap[`${cell.columnId}_${cell.rowsId}`] = {
                    value: cell.value || "",
                    code: cell.code || "",
                };
            });
            return cellValueMap;
        });
    }
    /**
     * Update a table question with new columns and rows
     *
     * @param id - ID of the table question to update
     * @param data - Object containing updated table question data
     * @param data.label - Updated label for the table question
     * @param data.columns - Array of updated column objects
     * @param data.rows - Array of updated row objects
     * @param data.projectId - ID of the project this table belongs to
     * @returns The updated table question with its columns and rows
     * @throws Error if validation fails or database operation fails
     */
    updateTableQuestion(id, data) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                // Validate input data
                if (!data.label || !data.label.trim()) {
                    throw new Error("Table label is required");
                }
                if (!data.columns ||
                    !Array.isArray(data.columns) ||
                    data.columns.length === 0) {
                    throw new Error("At least one column is required");
                }
                // Rows are now optional - validate only if provided
                if (data.rows && !Array.isArray(data.rows)) {
                    throw new Error("Rows must be an array if provided");
                }
                // Validate column data
                for (const column of data.columns) {
                    if (!column.columnName || !column.columnName.trim()) {
                        throw new Error("All columns must have valid names");
                    }
                    // If parentColumnId is provided, make sure it's a valid number
                    if (column.parentColumnId !== undefined) {
                        if (isNaN(column.parentColumnId)) {
                            throw new Error("Invalid parent column ID");
                        }
                        // Check if the parent column exists by ID
                        // For existing columns, parentColumnId should be the actual database ID
                        // For new columns, it might be a position-based index
                        let parentColumn = data.columns.find((col) => col.id === column.parentColumnId);
                        // If we can't find by ID, try to find by position (1-based index)
                        if (!parentColumn &&
                            column.parentColumnId > 0 &&
                            column.parentColumnId <= data.columns.length) {
                            // Adjust for 1-based indexing
                            parentColumn = data.columns[column.parentColumnId - 1];
                        }
                        if (!parentColumn) {
                            throw new Error(`Parent column with ID/position ${column.parentColumnId} not found`);
                        }
                        // Check if the parent column is already a child column
                        // We don't want children to have their own children (no grandchildren)
                        if (parentColumn.parentColumnId !== undefined) {
                            throw new Error("Child columns cannot have their own children. Only parent columns can have children.");
                        }
                    }
                }
                // Count child columns per parent to enforce maximum of 2 children per parent
                const parentChildCount = new Map();
                // Count how many children each parent has
                data.columns.forEach((column) => {
                    if (column.parentColumnId !== undefined) {
                        const parentId = column.parentColumnId;
                        parentChildCount.set(parentId, (parentChildCount.get(parentId) || 0) + 1);
                        // Check if any parent has more than 2 children
                        if (parentChildCount.get(parentId) > 2) {
                            const parentColumn = data.columns.find((col) => col.id === parentId);
                            throw new Error(`Parent column "${(parentColumn === null || parentColumn === void 0 ? void 0 : parentColumn.columnName) || "Unknown"}" cannot have more than 2 child columns`);
                        }
                    }
                });
                // Validate row data if rows are provided
                if (data.rows) {
                    for (const row of data.rows) {
                        if (!row.rowsName || !row.rowsName.trim()) {
                            throw new Error("All rows must have valid names");
                        }
                    }
                }
                // Use a transaction to ensure data consistency
                return yield prisma_1.prisma.$transaction((tx) => __awaiter(this, void 0, void 0, function* () {
                    // First, update the question label and projectId
                    yield tx.question.update({
                        where: { id },
                        data: {
                            label: data.label.trim(),
                            projectId: data.projectId, // Include the project ID in the update
                        },
                    });
                    // Get existing rows (we'll need these for updating/deleting)
                    // Note: We don't need existingColumns since we're deleting all columns anyway
                    const existingRows = yield tx.tableRow.findMany({
                        where: { questionId: id },
                    });
                    // Handle rows first (simpler) - only if rows are provided
                    if (data.rows) {
                        // 1. Update existing rows
                        const rowsToUpdate = data.rows
                            .filter((row) => row.id !== undefined)
                            .map((row) => ({
                            id: row.id,
                            rowsName: row.rowsName.trim(),
                        }));
                        for (const row of rowsToUpdate) {
                            yield tx.tableRow.update({
                                where: { id: row.id },
                                data: { rowsName: row.rowsName },
                            });
                        }
                        // 2. Add new rows
                        const rowsToAdd = data.rows
                            .filter((row) => row.id === undefined)
                            .map((row) => ({
                            rowsName: row.rowsName.trim(),
                            questionId: id,
                        }));
                        if (rowsToAdd.length > 0) {
                            yield tx.tableRow.createMany({
                                data: rowsToAdd,
                            });
                        }
                        // 3. Delete rows that are no longer needed
                        const rowIdsToKeep = data.rows
                            .filter((row) => row.id !== undefined)
                            .map((row) => row.id);
                        const rowIdsToDelete = existingRows
                            .filter((row) => !rowIdsToKeep.includes(row.id))
                            .map((row) => row.id);
                        if (rowIdsToDelete.length > 0) {
                            yield tx.tableRow.deleteMany({
                                where: {
                                    id: {
                                        in: rowIdsToDelete,
                                    },
                                },
                            });
                        }
                    }
                    else {
                        // If no rows provided, delete all existing rows
                        if (existingRows.length > 0) {
                            yield tx.tableRow.deleteMany({
                                where: { questionId: id },
                            });
                        }
                    }
                    // Handle columns (more complex due to parent-child relationships)
                    // Log the column structure we're about to create
                    // 1. Delete all existing columns and recreate them to avoid complex parent-child relationship updates
                    // This is safer than trying to update in place with complex hierarchies
                    yield tx.tableColumn.deleteMany({
                        where: { questionId: id },
                    });
                    // 2. Recreate columns with the new structure
                    // Separate columns into parent columns (no parentColumnId) and child columns (with parentColumnId)
                    const parentColumns = data.columns.filter((col) => col.parentColumnId === undefined);
                    const childColumns = data.columns.filter((col) => col.parentColumnId !== undefined);
                    // Create parent columns first - supports unlimited parent columns
                    const createdParentColumns = yield Promise.all(parentColumns.map((col) => tx.tableColumn.create({
                        data: {
                            columnName: col.columnName.trim(),
                            questionId: id,
                        },
                    })));
                    // Create a mapping of original column index to created column ID
                    const columnMapping = new Map();
                    parentColumns.forEach((col, index) => {
                        columnMapping.set(data.columns.indexOf(col), createdParentColumns[index].id);
                    });
                    // Now create child columns with the correct parent column IDs
                    if (childColumns.length > 0) {
                        // Create a map to store the created column IDs
                        // This will map from the original column ID to the created column ID
                        const createdColumnMap = new Map();
                        // Add parent columns to the map
                        parentColumns.forEach((col, index) => {
                            // If the column has an ID, map it to the created column ID
                            if (col.id !== undefined) {
                                createdColumnMap.set(col.id, createdParentColumns[index].id);
                            }
                        });
                        // Also map parent columns by their position in the array (for new columns without IDs)
                        parentColumns.forEach((col, index) => {
                            const position = data.columns.indexOf(col) + 1; // 1-based index
                            createdColumnMap.set(position, createdParentColumns[index].id);
                        });
                        // Process all child columns (all are direct children of parent columns)
                        for (const col of childColumns) {
                            // Get the parent column's ID
                            const parentId = col.parentColumnId;
                            // Get the actual ID of the parent column that was created
                            let actualParentId = undefined;
                            if (parentId !== undefined) {
                                // First try to get the parent ID from the map using the database ID
                                actualParentId = createdColumnMap.get(parentId);
                                // If that fails, try to get it using the position (for new columns)
                                if (actualParentId === undefined) {
                                    // Find the parent column's position in the array
                                    const parentPosition = data.columns.findIndex((c) => c === data.columns.find((c) => c.id === parentId)) + 1; // 1-based index
                                    if (parentPosition > 0) {
                                        actualParentId = createdColumnMap.get(parentPosition);
                                    }
                                }
                            }
                            if (actualParentId) {
                                // Create the column
                                const createdColumn = yield tx.tableColumn.create({
                                    data: {
                                        columnName: col.columnName.trim(),
                                        questionId: id,
                                        parentColumnId: actualParentId,
                                    },
                                });
                                // Add the created column to the map
                                // If the column has an ID, map it to the created column ID
                                if (col.id !== undefined) {
                                    createdColumnMap.set(col.id, createdColumn.id);
                                }
                                // Also map by position
                                const position = data.columns.indexOf(col) + 1; // 1-based index
                                createdColumnMap.set(position, createdColumn.id);
                            }
                            else {
                                console.warn(`Could not find parent ID for column "${col.columnName}" with parent ID ${parentId}`);
                            }
                        }
                        // Check if any columns were not processed
                        const processedIds = Array.from(createdColumnMap.keys());
                        const allIds = data.columns
                            .filter((col) => col.id !== undefined)
                            .map((col) => col.id);
                        const unprocessedIds = allIds.filter((id) => !processedIds.includes(id));
                        if (unprocessedIds.length > 0) {
                            console.warn(`${unprocessedIds.length} columns were not processed:`, unprocessedIds.map((id) => {
                                const col = data.columns.find((c) => c.id === id);
                                return `${col === null || col === void 0 ? void 0 : col.columnName} (id: ${id}, parent: ${col === null || col === void 0 ? void 0 : col.parentColumnId})`;
                            }));
                        }
                    }
                    // Return the updated question with all related data
                    return tx.question.findUnique({
                        where: { id },
                        include: {
                            tableColumns: {
                                include: {
                                    childColumns: true, // Include only direct child columns
                                },
                            },
                            tableRows: true,
                        },
                    });
                }));
            }
            catch (error) {
                console.error("Error in updateTableQuestion repository method:", error);
                throw error;
            }
        });
    }
    /**
     * Delete a table question and all related data
     *
     * @param id - ID of the table question to delete
     * @returns The deleted table question
     * @throws Error if the table question is not found
     */
    deleteTableQuestion(id) {
        return __awaiter(this, void 0, void 0, function* () {
            // This will cascade delete all related columns, rows, and cell values
            return prisma_1.prisma.question.delete({
                where: { id },
            });
        });
    }
    /**
     * Get all table questions for a project
     *
     * @param projectId - ID of the project
     * @returns Array of table questions with their columns and rows
     */
    getTableQuestionsByProjectId(projectId) {
        return __awaiter(this, void 0, void 0, function* () {
            // First get all table questions for this project
            const questions = yield prisma_1.prisma.question.findMany({
                where: {
                    projectId,
                    inputType: "table",
                    tableColumns: {
                        some: {},
                    },
                },
                include: {
                    tableRows: {
                        orderBy: {
                            id: "asc",
                        },
                    },
                },
                orderBy: {
                    createdAt: "desc",
                },
            });
            // If no questions found, return empty array
            if (questions.length === 0) {
                return [];
            }
            // Get all question IDs
            const questionIds = questions.map((q) => q.id);
            // Get all parent columns for these questions with their children
            const allParentColumns = yield prisma_1.prisma.tableColumn.findMany({
                where: {
                    questionId: { in: questionIds },
                    parentColumnId: null, // Only get parent columns
                },
                include: {
                    childColumns: {
                        orderBy: {
                            id: "asc",
                        },
                    },
                },
                orderBy: {
                    id: "asc",
                },
            });
            // Group columns by question ID
            const columnsByQuestionId = new Map();
            allParentColumns.forEach((column) => {
                const questionId = column.questionId;
                if (!columnsByQuestionId.has(questionId)) {
                    columnsByQuestionId.set(questionId, []);
                }
                columnsByQuestionId.get(questionId).push(column);
            });
            // Combine the results
            return questions.map((question) => (Object.assign(Object.assign({}, question), { tableColumns: columnsByQuestionId.get(question.id) || [] })));
        });
    }
}
exports.default = new TableQuestionRepository();
