"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const libraryTemplateQuestionGroupController_1 = require("../controllers/libraryTemplateQuestionGroupController");
const router = express_1.default.Router();
router.get("/", libraryTemplateQuestionGroupController_1.findAllLibraryTemplateGroupByLibrarytemplate);
router.post("/", libraryTemplateQuestionGroupController_1.createLibraryTemplateQuestionGroup);
router.patch("/", libraryTemplateQuestionGroupController_1.updateLibraryTemplateQuestionGroup);
router.delete("/", libraryTemplateQuestionGroupController_1.deleteLibraryTemplateQuestionGroup);
router.delete("/group/question", libraryTemplateQuestionGroupController_1.deleteLibraryTemplateQuestionAndGroup);
router.patch("/question/remove", libraryTemplateQuestionGroupController_1.removeLibraryTemplateQuestionIdFromGroup);
router.patch("/question/move", libraryTemplateQuestionGroupController_1.updateLibraryTemplateQuestionFromOneGroupToAnother);
router.patch("/group/add", libraryTemplateQuestionGroupController_1.updateOneLibraryTemplateGroupInsideAnotherGroup);
router.patch("/group/remove", libraryTemplateQuestionGroupController_1.removeLibraryTemplateGroupFromParentGroup);
exports.default = router;
