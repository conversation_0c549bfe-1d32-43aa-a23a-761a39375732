(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1361],{5287:(e,t,s)=>{"use strict";s.d(t,{GN:()=>i,J6:()=>n,O8:()=>l,s4:()=>a});var r=s(25784);let l=async(e,t)=>{try{let{data:s}=await r.A.delete("/form-submissions/".concat(e,"?projectId=").concat(t));return s}catch(e){throw console.error("Error deleting form submission:",e),e}},n=async(e,t)=>{try{let s=e.map(e=>r.A.delete("/form-submissions/".concat(e,"?projectId=").concat(t)));return(await Promise.all(s)).map(e=>e.data)}catch(e){throw console.error("Error deleting multiple form submissions:",e),e}},a=async(e,t)=>{try{if(!e.submissionId||!e.questionId)throw Error("submissionId and questionId are required");let s={...e};null===s.questionOptionId?delete s.questionOptionId:Array.isArray(s.questionOptionId)&&(s.questionOptionId=s.questionOptionId.filter(e=>null!=e),0===s.questionOptionId.length&&delete s.questionOptionId);let{data:l}=await r.A.patch("/answers/".concat(e.questionId,"?projectId=").concat(t),s);return l}catch(e){throw console.error("Error updating answer:",e),e}},i=async(e,t)=>{try{let{data:s}=await r.A.patch("/answers/multiple?projectId=".concat(t),e);return s}catch(e){throw console.error("Error updating multiple answers with endpoint:",e),e}}},17652:(e,t,s)=>{"use strict";s.d(t,{c3:()=>n});var r=s(46453);function l(e,t){return(...e)=>{try{return t(...e)}catch{throw Error(void 0)}}}let n=l(0,r.c3);l(0,r.kc)},34947:(e,t,s)=>{"use strict";s.d(t,{Af:()=>i,K4:()=>n,ae:()=>m,dI:()=>u,ej:()=>a,gf:()=>p,ku:()=>c,sr:()=>d,ul:()=>o});var r=s(25784);let l=e=>{if("project"===e)return"/questions";if("template"===e)return"/template-questions";if("questionBlock"===e)return"/question-blocks";throw Error("Unsupported context type")},n=async e=>{let{projectId:t}=e,{data:s}=await r.A.get("/questions/".concat(t));return s.questions},a=async e=>{let{templateId:t}=e,{data:s}=await r.A.get("/template-questions/".concat(t));return s.questions},i=async e=>{var t,s,n,a,i,o;let{contextType:c,contextId:d,dataToSend:u,position:m}=e,p="questionBlock"===c?"".concat(l(c)):"".concat(l(c),"/").concat(d);if(!u.label||!u.inputType)throw Error("Label and inputType are required");let h=["selectone","selectmany"].includes(u.inputType),x=u.file instanceof File,g=Array.isArray(u.questionOptions)&&u.questionOptions.length>0;if(h&&!x&&!g)throw Error("Options are required for select input types");if(x){let e=new FormData;e.append("label",u.label),e.append("isRequired",u.isRequired?"true":"false"),e.append("inputType",u.inputType),u.hint&&e.append("hint",u.hint),u.placeholder&&e.append("placeholder",u.placeholder),e.append("position",String(m||1)),e.append("file",u.file);try{let{data:t}=await r.A.post(p,e,{headers:{"Content-Type":"multipart/form-data"}});return t}catch(e){throw console.error("Upload error details:",(null==(t=e.response)?void 0:t.data)||e.message),Error("Failed to upload question with file: ".concat((null==(n=e.response)||null==(s=n.data)?void 0:s.message)||e.message))}}try{let{data:e}=await r.A.post(p,{label:u.label,isRequired:u.isRequired,hint:u.hint,placeholder:u.placeholder,inputType:u.inputType,questionOptions:u.questionOptions,position:m||1});return e}catch(e){throw console.error("API error details:",(null==(a=e.response)?void 0:a.data)||e.message),Error("Failed to add question: ".concat((null==(o=e.response)||null==(i=o.data)?void 0:i.message)||e.message))}},o=async e=>{let{contextType:t,id:s,projectId:n}=e,{data:a}=await r.A.delete("".concat(l(t),"/").concat(s,"?projectId=").concat(n));return a},c=async e=>{let{id:t,contextType:s,contextId:n}=e,{data:a}=await r.A.post("".concat(l(s),"/duplicate/").concat(t,"?projectId=").concat(n),"questionBlock"===s?{}:"project"===s?{projectId:n}:{templateId:n});return a},d=async e=>{let{id:t,contextType:s,dataToSend:n,contextId:a}=e,{data:i}=await r.A.patch("".concat(l(s),"/").concat(t,"?projectId=").concat(a),n);return i},u=async()=>{try{return(await r.A.get("/question-blocks")).data.questions||[]}catch(e){throw console.error("Error fetching question block questions:",e),e}},m=async e=>{let{contextType:t,contextId:s,questionPositions:n}=e;if("project"!==t)throw Error("Question position updates are only supported for projects");let a="".concat(l(t),"/positions?projectId=").concat(s);try{let{data:e}=await r.A.patch(a,{questionPositions:n});return e}catch(e){var i,o,c,d,u,m;throw console.error("Update failed - Full error:",e),console.error("Update failed - Error details:",{status:null==(i=e.response)?void 0:i.status,statusText:null==(o=e.response)?void 0:o.statusText,data:null==(c=e.response)?void 0:c.data,message:e.message,config:{url:null==(d=e.config)?void 0:d.url,method:null==(u=e.config)?void 0:u.method,data:null==(m=e.config)?void 0:m.data}}),e}},p=async e=>{let{projectId:t}=e,{data:s}=await r.A.get("/projects/getalldata/".concat(t));return s.data}},61080:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>H});var r=s(95155),l=s(95139);let n=(0,s(19946).A)("arrow-up-down",[["path",{d:"m21 16-4 4-4-4",key:"f6ql7i"}],["path",{d:"M17 20V4",key:"1ejh1v"}],["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}]]);var a=s(92657),i=s(53999),o=s(12115),c=s(13163),d=s(88524);let u=e=>{var t,s,l;let{isOpen:n,onClose:a,title:i,tableData:u,uniqueColumns:m,uniqueRows:p,rowsMap:h,useParentChildColumns:x=!1,loading:g=!1,tableStructure:b}=e,f=o.useMemo(()=>{if(!u||0===u.length)return[];let e=[];return u.forEach(t=>{let s=parseInt(t.column),r=parseInt(t.row);isNaN(s)||isNaN(r)||e.push({columnId:s,rowsId:r,value:t.value})}),e},[u]),y=o.useMemo(()=>{if(!(null==b?void 0:b.tableColumns)||0===b.tableColumns.length)return{parentColumns:[],columnMap:new Map,hasChildColumns:!1};let e=b.tableColumns.filter(e=>void 0===e.parentColumnId||null===e.parentColumnId),t=new Map;e.forEach(e=>{let s=b.tableColumns.filter(t=>t.parentColumnId===e.id);t.set(e.id,s)});let s=e.some(e=>(t.get(e.id)||[]).length>0);return{parentColumns:e,columnMap:t,hasChildColumns:s}},[b]),w=o.useMemo(()=>{let e=new Map;return f.forEach(t=>{e.set("".concat(t.columnId,"_").concat(t.rowsId),t.value)}),e},[f]);return(0,r.jsx)(c.A,{isOpen:n,onClose:a,className:"p-6 rounded-md max-w-4xl w-[95%] mobile:w-[85%] tablet:w-[75%] laptop:w-[60%]",children:(0,r.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-neutral-700",children:i}),g?(0,r.jsxs)("div",{className:"flex items-center justify-center p-8",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"}),(0,r.jsx)("span",{className:"ml-2 text-neutral-600",children:"Loading table data..."})]}):(null==b?void 0:b.tableColumns)&&0!==b.tableColumns.length?(0,r.jsx)("div",{className:"overflow-auto max-h-[70vh]",children:(0,r.jsxs)(d.XI,{className:"border-collapse border border-amber-700",children:[(0,r.jsxs)(d.A0,{className:"bg-amber-100",children:[(0,r.jsxs)(d.Hj,{children:[(0,r.jsx)(d.nd,{className:"px-3 py-2 text-xs font-medium text-gray-700 uppercase tracking-wider border border-amber-700 bg-amber-100",rowSpan:y.hasChildColumns?2:1,children:i}),y.parentColumns.map(e=>{let t=(y.columnMap.get(e.id)||[]).length||1;return(0,r.jsx)(d.nd,{colSpan:t,className:"px-3 py-2 text-xs font-medium text-gray-700 uppercase tracking-wider text-center border border-amber-700 bg-amber-100",children:e.columnName},e.id)})]}),y.hasChildColumns&&(0,r.jsx)(d.Hj,{children:y.parentColumns.map(e=>{let t=y.columnMap.get(e.id)||[];return 0===t.length?null:t.map(e=>(0,r.jsx)(d.nd,{className:"px-3 py-2 text-xs font-medium text-gray-700 uppercase tracking-wider border border-amber-700 bg-amber-50",children:e.columnName},e.id))})})]}),(0,r.jsx)(d.BF,{children:null==(l=b.tableRows)?void 0:l.map((e,t)=>(0,r.jsxs)(d.Hj,{className:"bg-white",children:[(0,r.jsx)(d.nA,{className:"px-3 py-2 text-xs font-medium border border-amber-700 bg-amber-50",children:e.rowsName}),y.parentColumns.map(t=>{let s=y.columnMap.get(t.id)||[];return 0===s.length?(0,r.jsx)(d.nA,{className:"px-3 py-2 text-xs border border-amber-700",children:w.get("".concat(t.id,"_").concat(e.id))||""},"cell-".concat(t.id,"-").concat(e.id)):s.map(t=>(0,r.jsx)(d.nA,{className:"px-3 py-2 text-xs border border-amber-700",children:w.get("".concat(t.id,"_").concat(e.id))||""},"cell-".concat(t.id,"-").concat(e.id)))})]},e.id))})]})}):(0,r.jsxs)("div",{className:"py-4 text-center text-amber-600",children:[(0,r.jsx)("p",{children:"No table structure available."}),(0,r.jsx)("p",{className:"text-sm mt-2",children:"Debug info:"}),(0,r.jsx)("pre",{className:"text-xs mt-2 bg-gray-100 p-2 rounded overflow-auto max-h-40",children:JSON.stringify({hasTableStructure:!!b,tableColumnsLength:(null==b||null==(t=b.tableColumns)?void 0:t.length)||0,tableRowsLength:(null==b||null==(s=b.tableRows)?void 0:s.length)||0,tableDataLength:(null==u?void 0:u.length)||0,useParentChildColumns:x},null,2)})]}),(0,r.jsx)("div",{className:"flex justify-end mt-4",children:(0,r.jsx)("button",{onClick:a,className:"px-4 py-2 bg-primary-500 text-white rounded-md hover:bg-primary-600 transition-colors",children:"Close"})})]})})};var m=s(25784),p=s(27859);let h=(e,t)=>{if(null==e)return"-";if("boolean"==typeof e)return e?"Yes":"No";if(e instanceof Date)return(0,i.Y)(e);if("date"===t&&"string"==typeof e)try{return(0,i.Y)(new Date(e))}catch(t){return e}return String(e)},x=async e=>{try{let t=null;try{let{data:s}=await m.A.get("/table-questions/".concat(e));s&&s.success&&s.data&&(t=s.data.question)}catch(e){console.error("Error fetching from /table-questions/ endpoint:",e)}if(t&&t.tableColumns&&!t.tableRows)try{let{data:s}=await m.A.get("/table-rows/".concat(e));s&&s.data&&s.data.tableRows&&(t.tableRows=s.data.tableRows)}catch(e){console.error("Error fetching table rows separately:",e)}if(!t)return{id:e,label:"Table Data",tableColumns:[],tableRows:[{id:1,rowsName:"Row 1"},{id:2,rowsName:"Row 2"},{id:3,rowsName:"Row 3"}]};if(t.tableColumns&&Array.isArray(t.tableColumns)){let e=[];t.tableColumns.forEach(t=>{e.push({id:t.id,columnName:t.columnName,parentColumnId:t.parentColumnId||null}),t.childColumns&&Array.isArray(t.childColumns)&&t.childColumns.forEach(s=>{e.push({id:s.id,columnName:s.columnName,parentColumnId:s.parentColumnId||t.id})})}),t.tableColumns=e}else console.error("tableColumns is missing or not an array, creating default tableColumns"),t.tableColumns=[];return t.tableRows&&Array.isArray(t.tableRows)||(console.error("tableRows is missing or not an array, creating default tableRows"),t.tableColumns&&t.tableColumns.length>0?t.tableRows=t.tableColumns.map(e=>({id:e.id,rowsName:"Row ".concat(e.id)})):t.tableRows=[{id:1,rowsName:"Row 1"},{id:2,rowsName:"Row 2"},{id:3,rowsName:"Row 3"}]),t}catch(t){return console.error("Error fetching table structure:",t),{id:e,label:"Table Data",tableColumns:[],tableRows:[{id:1,rowsName:"Row 1"},{id:2,rowsName:"Row 2"},{id:3,rowsName:"Row 3"}]}}},g=(e,t,s,i)=>{var c;let d=[{id:"select",header:e=>{let{table:t}=e;return(0,r.jsx)(l.S,{className:"w-5 h-5 data-[state=checked]:bg-white data-[state=checked]:text-primary-500 border-neutral-100 cursor-pointer",checked:t.getIsAllPageRowsSelected()||t.getIsSomePageRowsSelected()&&"indeterminate",onCheckedChange:e=>t.toggleAllPageRowsSelected(!!e),"aria-label":"Select all"})},cell:e=>{let{row:t}=e;return(0,r.jsx)(l.S,{className:"w-5 h-5 bg-neutral-100 border-neutral-400 data-[state=checked]:bg-white data-[state=checked]:text-primary-500 cursor-pointer",checked:t.getIsSelected(),onCheckedChange:e=>t.toggleSelected(!!e),"aria-label":"Select row"})},enableHiding:!1},{id:"id",header:e=>{let{column:t}=e;return(0,r.jsxs)("div",{className:"flex items-center hover:text-neutral-300",children:[(0,r.jsx)("span",{children:"ID"}),(0,r.jsx)("span",{className:"ml-2 flex-shrink-0 w-4 h-4",children:(0,r.jsx)(n,{className:"w-full h-full",onClick:()=>t.toggleSorting("asc"===t.getIsSorted())})})]})},accessorFn:(e,t)=>t+1,enableSorting:!0,cell:t=>{let{row:l}=t;return(0,r.jsxs)("div",{className:"flex items-center gap-8 font-medium text-neutral-700",children:[l.index+1,(0,r.jsxs)("span",{className:"flex items-center gap-2",children:[(0,r.jsx)(a.A,{onClick:()=>e(l.original),className:"w-4 h-4 cursor-pointer hover:text-primary-500"}),(0,r.jsx)(p.JBV,{className:"w-4 h-4 cursor-pointer hover:text-primary-500",title:"Edit",onClick:()=>{let e=l.original.id;e&&s&&window.open("/edit-submission/".concat(s,"/").concat(e),"_blank")}})]})]})}},{id:"validation",header:"Validation",accessorKey:"validation"}],m=[];if(i&&i.length>0)m=i;else{if(!t||null==(c=t.answers)||!c.length)return d;m=Array.from(new Map(t.answers.map(e=>[e.question.id,e.question])).values())}return[...d,...m.map(e=>({id:"".concat(e.label),header:t=>{let{column:s}=t;return(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{children:e.label}),(0,r.jsx)(n,{className:"ml-1 h-4 w-4 cursor-pointer opacity-60",onClick:()=>s.toggleSorting("asc"===s.getIsSorted())})]})},accessorFn:t=>{var s,r;let l=t.answers.filter(t=>{var s;return(null==(s=t.question)?void 0:s.id)===e.id});if(0===l.length)return null;if("selectmany"===e.inputType){let e=l.map(e=>e.value).filter(e=>e&&""!==String(e).trim()).sort();return e.length>0?e.join(", "):null}return null!=(r=null==(s=l[0])?void 0:s.value)?r:null},cell:t=>{let{getValue:s}=t,l=s();if("table"===e.inputType)try{let t,s="string"==typeof l?l:String(l);if(s.startsWith("[")&&s.includes("{"))try{t=JSON.parse(s)}catch(e){console.error("Failed to parse JSON string:",e),s=s.replace(/\\"/g,'"').replace(/^"/,"").replace(/"$/,"");try{t=JSON.parse(s)}catch(r){console.error("Failed second parse attempt:",r);let e=s.match(/\[([\s\S]*)\]/);if(e&&e[0])try{t=JSON.parse(e[0])}catch(s){console.error("Failed third parse attempt:",s);try{let s=e[0].replace(/'/g,'"');t=JSON.parse(s)}catch(e){console.error("Failed fourth parse attempt:",e)}}}}if(!t&&s.includes("columnId")&&s.includes("rowsId"))try{let e=s.replace(/'/g,'"');t=JSON.parse(e)}catch(e){console.error("Failed custom format parsing:",e)}if(Array.isArray(t)){let s=t.map(e=>{let t="";e.columnName?t=e.columnName:e.column&&e.column.columnName?t=e.column.columnName:e.columnId&&(t=e.name?e.name:e.label?e.label:String(e.columnId));let s="";e.rowsName?s=e.rowsName:e.row&&e.row.rowsName?s=e.row.rowsName:e.rowsId&&(s=e.name?e.name:e.label?e.label:String(e.rowsId));let r=void 0!==e.value?e.value:"";return{column:t,row:s,value:r}}),l=new Map,n=new Set;s.forEach(e=>{var t;n.add(String(e.column)),l.has(String(e.row))||l.set(String(e.row),new Map),null==(t=l.get(String(e.row)))||t.set(String(e.column),String(e.value))});let i=Array.from(n),c=Array.from(l.keys());return(0,r.jsx)(()=>{let[t,n]=(0,o.useState)(!1),[d,m]=(0,o.useState)(null),[p,h]=(0,o.useState)(!1);(0,o.useEffect)(()=>{},[d]);let g=async()=>{if(!e.id)return void console.error("No question ID available");h(!0);try{let t=await x(e.id);if(t&&(t.tableRows||console.error("tableRows is missing from the structure!"),m(t),t.tableColumns&&t.tableRows)){let e=new Map,r=new Map;t.tableColumns.forEach(t=>{e.set(t.id,t.columnName),e.set(String(t.id),t.columnName)}),t.tableRows.forEach(e=>{r.set(e.id,e.rowsName),r.set(String(e.id),e.rowsName)}),s.forEach(s=>{if(s.column&&!isNaN(Number(s.column))){let r=s.column;if(e.has(r))s.column,s.column=e.get(r);else{let e=t.tableColumns.find(e=>String(e.id)===String(r));e&&(s.column,s.column=e.columnName)}}if(s.row&&!isNaN(Number(s.row))){let e=s.row;if(r.has(e))s.row,s.row=r.get(e);else{let r=t.tableRows.find(t=>String(t.id)===String(e));r&&(s.row,s.row=r.rowsName)}}});let n=new Map,a=new Set;s.forEach(e=>{var t;a.add(String(e.column)),n.has(String(e.row))||n.set(String(e.row),new Map),null==(t=n.get(String(e.row)))||t.set(String(e.column),String(e.value))}),i.length=0,c.length=0,a.forEach(e=>i.push(e)),n.forEach((e,t)=>c.push(t)),l.clear(),n.forEach((e,t)=>{l.set(t,e)})}}catch(e){console.error("Error fetching table structure:",e)}finally{h(!1)}};return(0,r.jsxs)("div",{className:"font-medium text-neutral-700",children:[(0,r.jsxs)("a",{href:"#",onClick:async e=>{e.preventDefault(),h(!0),n(!0),await g()},className:"inline-flex items-center gap-1 text-primary-500 hover:text-primary-700 hover:underline whitespace-nowrap",children:[(0,r.jsx)(a.A,{size:12,className:"inline"})," Click to view table"]}),null,(0,r.jsx)(u,{isOpen:t,onClose:()=>n(!1),title:e.label||"Table Data",tableData:s,uniqueColumns:i,uniqueRows:c,rowsMap:l,useParentChildColumns:!0,loading:p,tableStructure:d})]})},{})}}catch(e){console.error("Error parsing table data:",e,"Value:",l)}return null==l||""===l?(0,r.jsx)("div",{className:"font-medium text-neutral-400 italic",children:"-"}):(0,r.jsx)("div",{className:"font-medium text-neutral-700",children:String(l)})},enableSorting:!0})),{id:"submissionTime",header:e=>{let{column:t}=e;return(0,r.jsxs)("div",{className:"flex items-center gap-4 hover:text-neutral-300",children:[(0,r.jsx)("span",{children:"Submission Time"}),(0,r.jsx)("span",{className:"ml-2 flex-shrink-0 w-4 h-4",children:(0,r.jsx)(n,{className:"w-full h-full",onClick:()=>t.toggleSorting("asc"===t.getIsSorted())})})]})},accessorKey:"submissionTime",cell:e=>{let{getValue:t}=e,s=t();return(0,r.jsx)("div",{className:"font-medium text-neutral-700",children:h(s,"date")||"Not recorded"})},enableSorting:!0},{id:"submittedBy",header:e=>{let{column:t}=e;return(0,r.jsxs)("div",{className:"flex items-center gap-4 hover:text-neutral-300",children:[(0,r.jsx)("span",{children:"Submitted By"}),(0,r.jsx)("span",{className:"ml-2 flex-shrink-0 w-4 h-4",children:(0,r.jsx)(n,{className:"w-full h-full",onClick:()=>t.toggleSorting("asc"===t.getIsSorted())})})]})},accessorKey:"submittedBy",accessorFn:e=>{var t;return(null==(t=e.user)?void 0:t.name)||"Anonymous"},cell:e=>{let{getValue:t}=e,s=t();return(0,r.jsx)("div",{className:"font-medium text-neutral-700",children:s})},enableSorting:!0}]};var b=s(89852),f=s(93347),y=s(29911),w=s(14549),v=s(36268),j=s(11032),N=s(34540),S=s(71402),C=s(5287),I=s(26715),q=s(5041),k=s(17652);let A=e=>{if(!e||!e.answers)return[];let t=new Map;return e.answers.forEach(e=>{var s,r,l;let n=null==(s=e.question)?void 0:s.id,a=(null==(r=e.question)?void 0:r.label)||"unknown";if(n)if(t.has(n)){let s=t.get(n);s.answers.push(e.value),s.originalData.push(e)}else t.set(n,{type:(null==(l=e.question)?void 0:l.inputType)||"text",question:a,questionObject:e.question,answers:[e.value],originalData:[e]})}),Array.from(t.values())},O=e=>{var t;let{showModal:s,onClose:l,onConfirm:n,submission:a,isMultipleSelection:i=!1,selectedSubmissions:u=[],projectId:m}=e,[p,h]=(0,o.useState)(!1),[x,g]=(0,o.useState)(null),[f,y]=(0,o.useState)(""),[w,O]=(0,o.useState)(null),E=(0,N.wA)(),R=(0,I.jE)(),D=(0,k.c3)(),T=(0,o.useRef)(null),F=(0,o.useRef)(null),[M,B]=(0,o.useState)(""),[K,J]=(0,o.useState)(""),P=o.useMemo(()=>{if(i&&w){let e=u.find(e=>e.id===w);return e?A(e):[]}return A(a)},[a,i,w,u]);(0,o.useEffect)(()=>{if(i&&u.length>0&&!w){var e;let t=null==(e=u[0])?void 0:e.id;void 0!==t&&O(t)}},[i,u,w]);let V=o.useMemo(()=>P.filter(e=>{let t=e.question.toLowerCase().includes(M.toLowerCase())||!M,s=String(e.answers).toLowerCase().includes(K.toLowerCase())||!K;return t&&s}),[P,M,K]),H=(0,v.N4)({data:V,columns:[{accessorKey:"type",header:D("type")},{accessorKey:"question",header:()=>(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{children:"Question"}),(0,r.jsx)(b.p,{ref:T,placeholder:D("searchQuestions"),value:M,onChange:e=>B(e.target.value),className:"bg-neutral-100 text-neutral-700 mt-2 h-8"})]})},{accessorKey:"answers",header:()=>(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{children:"Answer"}),(0,r.jsx)(b.p,{ref:F,placeholder:D("searchAnswers"),value:K,onChange:e=>J(e.target.value),className:"bg-neutral-100 text-neutral-700 mt-2 h-8"})]}),cell:e=>{let{row:t}=e,s=t.original.answers;return i?(0,r.jsx)("div",{className:"flex flex-col",children:(0,r.jsx)("div",{className:"text-neutral-800 italic",children:D("multipleResponses")})}):(0,r.jsx)("div",{className:"flex flex-col",children:s.map((e,t)=>(0,r.jsx)("div",{className:"",children:String(e)},t))})}},{accessorKey:"action",header:D("action"),cell:e=>{let{row:t}=e;return(0,r.jsx)("button",{className:"btn-primary",onClick:()=>L(t.original),children:D("edit")})}}],getCoreRowModel:(0,j.HT)()}),L=e=>{h(!0),g(e),y(e.answers.join("\n"))},_=(0,q.n)({mutationFn:e=>{if(!e.questionId)throw Error(D("questionIdRequired"));if(!e.submissionId)throw Error(D("submissionIdRequired"));let t={submissionId:e.submissionId,questionId:e.questionId,answerType:e.answerType,value:e.value};return"selectmany"===e.answerType?t.questionOptionId=Array.isArray(e.questionOptionId)?e.questionOptionId:e.questionOptionId?[e.questionOptionId]:[]:void 0!==e.questionOptionId&&(t.questionOptionId=Array.isArray(e.questionOptionId)?e.questionOptionId[0]:e.questionOptionId),"number"===e.answerType||"decimal"===e.answerType?t.value="string"==typeof e.value?parseFloat(e.value):"number"==typeof e.value?e.value:0:"selectmany"===e.answerType?t.value=Array.isArray(e.value)?e.value.map(e=>String(e)):[String(e.value)]:t.value=String(e.value),(0,C.s4)(t,m)},onSuccess:e=>{E((0,S.Ds)({message:D("answerUpdated"),type:"success"})),R.invalidateQueries({queryKey:["formSubmissions"]}),h(!1),g(null),y(""),n()},onError:e=>{var t,s,r,l,n,a,i;console.error("Error updating answer:",e),console.error("Error details:",{response:null==e||null==(t=e.response)?void 0:t.data,status:null==e||null==(s=e.response)?void 0:s.status,headers:null==e||null==(r=e.response)?void 0:r.headers});let o=(null==e||null==(n=e.response)||null==(l=n.data)?void 0:l.message)||(null==e||null==(i=e.response)||null==(a=i.data)?void 0:a.errors)||"Failed to update answer";E((0,S.Ds)({message:"string"==typeof o?o:D("validationError"),type:"error"}))}}),Q=async()=>{var e,t,s,r;if(!x)return;let l=i&&w&&u.find(e=>e.id===w)||a;if(!(null==l?void 0:l.id))return void E((0,S.Ds)({message:D("submissionIdMissing"),type:"error"}));let n=f.split("\n").map(e=>e.trim()).filter(e=>e);if(0===n.length)return void E((0,S.Ds)({message:D("enterValidResponse"),type:"error"}));let o=null==(e=x.questionObject)?void 0:e.id;if(!o)return void E((0,S.Ds)({message:D("questionIdMissing"),type:"error"}));let c=x.type||"text";try{let e;if("selectmany"===c){let s=null==(t=x.originalData)?void 0:t.map(e=>e.questionOptionId).filter(Boolean);if(s&&0!==s.length)if(s.length!==n.length)for(e=[...s];e.length<n.length;)e.push(e[0]||null);else e=s;else e=n.map(()=>null)}else e=(null==(r=x.originalData)||null==(s=r[0])?void 0:s.questionOptionId)||null;let a={submissionId:l.id,questionId:o,answerType:c,value:"selectmany"===c?n:n[0],questionOptionId:e};_.mutate(a)}catch(e){console.error("Form validation error:",e),E((0,S.Ds)({message:D("checkInputTryAgain"),type:"error"}))}};return(0,r.jsx)(r.Fragment,{children:(0,r.jsx)(c.A,{isOpen:s,onClose:l,className:"flex flex-col gap-5 p-6 rounded-md",children:p?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("h2",{className:"text-lg font-semibold text-neutral-700",children:[D("editingQuestion"),": ",null==x?void 0:x.question]}),(0,r.jsx)("p",{className:"text-sm text-neutral-700",children:D("editingMultipleInfo")}),(0,r.jsx)("textarea",{value:f,onChange:e=>y(e.target.value),className:"mt-4 border border-neutral-400 rounded-md p-2 w-full h-24 focus:outline-none focus:ring-2 focus:ring-primary-500",placeholder:D("enterNewResponse")}),(0,r.jsxs)("div",{className:"flex justify-end gap-4 mt-6",children:[(0,r.jsx)("button",{className:"btn-outline",onClick:()=>{h(!1),g(null),y("")},disabled:_.isPending,children:D("cancel")}),(0,r.jsx)("button",{className:"btn-primary",onClick:Q,disabled:_.isPending,children:_.isPending?D("saving"):D("confirmAndSave")})]})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"flex flex-col gap-4 max-h-[500px] overflow-y-auto",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-neutral-700",children:i?D("editSelectedSubmission"):D("editSubmission")}),(0,r.jsx)("div",{children:i&&u.length>0&&(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("label",{htmlFor:"submission-selector",className:"text-sm font-medium text-neutral-700",children:D("selectSubmissionToEdit")}),(0,r.jsx)("select",{id:"submission-selector",className:"border border-neutral-300 rounded-md p-1 text-sm bg-white",value:w||"",onChange:e=>{O(Number(e.target.value))},children:u.map(e=>(0,r.jsxs)("option",{value:e.id,children:[D("id"),": ",e.id]},e.id))})]})})]}),i&&w&&(0,r.jsxs)("div",{className:"bg-primary-500 border text-neutral-100 rounded-md p-3 text-sm",children:[(0,r.jsxs)("p",{className:"font-medium",children:[D("editingSubmissionId"),": ",w]}),(0,r.jsx)("p",{className:"text-xs mt-1",children:D("editingOneFromMultiple")})]}),(0,r.jsx)("p",{className:"text-sm text-neutral-700",children:i?D("multipleSelectedChoose"):D("editingSingle")}),(0,r.jsx)("div",{className:"rounded-md border border-neutral-400 max-h-[450px] overflow-auto",children:(0,r.jsxs)(d.XI,{children:[(0,r.jsx)(d.A0,{className:"h-20",children:H.getHeaderGroups().map(e=>(0,r.jsx)(d.Hj,{className:"text-sm border-neutral-400",children:e.headers.map(e=>(0,r.jsx)(d.nd,{className:"py-1 px-6 text-left bg-primary-500 text-neutral-100 font-semibold",children:e.isPlaceholder?null:(0,v.Kv)(e.column.columnDef.header,e.getContext())},e.id))},e.id))}),(0,r.jsx)(d.BF,{children:(null==(t=H.getRowModel().rows)?void 0:t.length)?H.getRowModel().rows.map(e=>(0,r.jsx)(d.Hj,{className:" text-sm border-neutral-400","data-state":e.getIsSelected()&&"selected",children:e.getVisibleCells().map(e=>(0,r.jsx)(d.nA,{className:"py-4 px-6",children:(0,v.Kv)(e.column.columnDef.cell,e.getContext())},e.id))},e.id)):(0,r.jsx)(d.Hj,{children:(0,r.jsx)(d.nA,{colSpan:H.getAllColumns().length,className:"h-24 text-center",children:D("noResults")})})})]})}),(0,r.jsxs)("div",{className:"flex justify-end gap-4 mt-6",children:[(0,r.jsx)("button",{className:"btn-outline",onClick:l,children:D("cancel")}),(0,r.jsx)("button",{className:"btn-primary",onClick:Q,disabled:_.isPending,children:_.isPending?D("saving"):D("confirmAndSave")})]})]})})})};var E=s(66163);let R=e=>{var t;let{isOpen:s,onClose:l,submission:n}=e,[a,i]=(0,o.useState)(!1),d=(0,k.c3)(),u=(0,o.useRef)(null),m=new Map;return n.answers.forEach(e=>{let t=e.question.label;m.has(t)||m.set(t,[]),m.get(t).push(e.value)}),(0,r.jsx)(c.A,{isOpen:s,onClose:l,className:"p-6 rounded-md max-w-4xl w-full ",children:(0,r.jsxs)("div",{ref:u,className:"flex flex-col gap-4 transition-all duration-300 ".concat(a?"fixed inset-0 bg-neutral-100 w-screen h-screen z-50 p-6 overflow-auto":""),children:[(0,r.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-neutral-700",children:d("submissionDetails")}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{className:"text-sm text-neutral-600",children:"Validation status:"}),(0,r.jsxs)("select",{className:"px-3 py-1 border border-neutral-500 rounded-md text-sm",children:[(0,r.jsx)("option",{value:"",children:d("select")}),(0,r.jsx)("option",{value:"valid",children:d("valid")}),(0,r.jsx)("option",{value:"invalid",children:d("notValid")}),(0,r.jsx)("option",{value:"pending",children:d("pending")})]}),(0,r.jsxs)("button",{onClick:()=>{i(e=>!e)},className:"btn-primary",children:[(0,r.jsx)(w.D4o,{className:"w-5 h-5"}),a?d("exitFullscreen"):d("fullscreen")]})]})]}),(0,r.jsx)("div",{className:"overflow-x-auto rounded-md border border-neutral-200 bg-neutral-100",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-neutral-200",children:[(0,r.jsx)("thead",{className:"bg-primary-500 text-neutral-100",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium  uppercase tracking-wider",children:d("question")}),(0,r.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium uppercase tracking-wider",children:d("response")})]})}),(0,r.jsx)("tbody",{className:"bg-neutral-100 divide-y divide-neutral-200",children:[...m.entries()].map(e=>{let[t,s]=e,l=n.answers.find(e=>e.question.label===t),a=(null==l?void 0:l.question.inputType)==="table";return(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{className:"px-4 py-2 align-top",children:t}),(0,r.jsx)("td",{className:"px-4 py-2",children:a?(0,r.jsx)("div",{className:"text-neutral-600 italic",children:d("tableDataNote")}):s.join(", ")})]},t)})})]})}),(0,r.jsxs)("div",{className:"flex justify-between items-center mt-4",children:[(0,r.jsxs)("div",{className:"text-sm text-neutral-600 font-semibold",children:[(0,r.jsxs)("p",{children:[d("submittedBy"),": ",null==(t=n.user)?void 0:t.name]}),(0,r.jsxs)("p",{children:[d("submissionTime"),": ",n.submissionTime]})]}),(0,r.jsx)("div",{children:(0,r.jsx)("button",{className:"btn-primary",onClick:l,children:d("close")})})]})]})})};var D=s(34947),T=s(67133),F=s(97168),M=s(63642),B=s(35695),K=s(88570),J=s(19373);let P=async e=>{let{data:t}=await m.A.get("/form-submissions/".concat(e));return t.data.formSubmissions},V="data-table-column-visibility",H=()=>{let{hashedId:e}=(0,B.useParams)(),t=(0,k.c3)(),s=Number((0,K.D)(e)),l=(0,N.wA)(),n=(0,I.jE)(),{data:a=[],isLoading:i,refetch:c}=(0,J.I)({queryKey:["formSubmissions",s],queryFn:()=>P(s),enabled:null!==s,refetchInterval:1e3,staleTime:0,gcTime:0}),{data:d=[],isLoading:u}=(0,J.I)({queryKey:["allQuestions",s],queryFn:()=>(0,D.K4)({projectId:s}),enabled:null!==s,staleTime:3e5}),[m,h]=(0,o.useState)(!1),[x,v]=(0,o.useState)(!1),[j,A]=(0,o.useState)(""),[H,L]=(0,o.useState)(!1),[_,Q]=o.useState(null),[U,z]=(0,o.useState)(!1),[Y,G]=o.useState({}),[X,$]=o.useState(null),[W,Z]=o.useState({}),[ee,et]=(0,o.useState)(!1),[es,er]=(0,o.useState)(null),el=a.length>0&&d.length>0?g(e=>{er(e),et(!0)},a[0],e,d):[],[en,ea]=o.useState(!1),[ei,eo]=(0,o.useState)(!1),ec=(0,o.useRef)(null),ed=(0,o.useRef)(null),eu=(0,q.n)({mutationFn:e=>(0,C.O8)(e,s),onSuccess:()=>{l((0,S.Ds)({message:t("submissionDeleted"),type:"success"})),n.invalidateQueries({queryKey:["formSubmissions",s]}),Z({}),L(!1),er(null),v(!1)},onError:e=>{console.error("Error deleting submission:",e),l((0,S.Ds)({message:t("submissionDeleteFailed"),type:"error"})),v(!1)}}),em=(0,q.n)({mutationFn:e=>(0,C.J6)(e,s),onSuccess:(e,r)=>{l((0,S.Ds)({message:"".concat(r.length," ").concat(t("submissionsDeleted")),type:"success"})),n.invalidateQueries({queryKey:["formSubmissions",s]}),Z({}),L(!1),er(null),v(!1)},onError:e=>{console.error("Error deleting multiple submissions:",e),l((0,S.Ds)({message:t("submissionsDeleteFailed"),type:"error"})),v(!1)}});return(0,o.useEffect)(()=>{let e=e=>{ec.current&&!ec.current.contains(e.target)&&eo(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]),(0,o.useEffect)(()=>{try{let e=localStorage.getItem(V);if(e){let t=JSON.parse(e);t&&"object"==typeof t&&!Array.isArray(t)?G(t):console.warn("Invalid format in localstorage for column visibility")}}catch(e){console.error("Error loading column visibility:",e)}},[]),(0,o.useEffect)(()=>{if(Object.keys(Y).length>0)try{localStorage.setItem(V,JSON.stringify(Y))}catch(e){console.error("Error saving column visibility:",e)}},[Y]),(0,o.useEffect)(()=>{X&&0===Object.keys(W).length&&X.resetRowSelection()},[W,X]),(0,r.jsxs)("div",{ref:ed,className:"flex flex-col gap-4 transition-all duration-300 ".concat(m?"fixed inset-0 bg-neutral-100 w-screen h-screen z-50 p-6 overflow-auto":""),children:[(0,r.jsxs)("div",{className:"flex flex-col desktop:flex-row justify-between gap-8 items-center py-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)(b.p,{placeholder:t("searchAllColumns"),value:j,onChange:e=>A(e.target.value)}),X&&(0,r.jsxs)(T.rI,{open:en,onOpenChange:e=>ea(e),children:[(0,r.jsx)(T.ty,{asChild:!0,children:(0,r.jsxs)(F.$,{variant:"outline",className:"flex items-center gap-2 cursor-pointer",children:[t("showHideColumns"),en?(0,r.jsx)(y.Ucs,{className:"w-3 h-3"}):(0,r.jsx)(y.Vr3,{className:"w-3 h-3"})]})}),(0,r.jsx)(T.SQ,{align:"start",className:"bg-neutral-100 border border-neutral-200 shadow-md",children:X.getAllColumns().filter(e=>e.getCanHide()).map(e=>{var t;return(0,r.jsx)(T.hO,{className:"capitalize cursor-pointer hover:bg-neutral-200",checked:null==(t=Y[e.id])||t,onCheckedChange:t=>G(s=>({...s,[e.id]:t})),children:e.id},e.id)})})]})]}),(0,r.jsxs)("div",{ref:ec,className:"flex relative items-center gap-4 text-neutral-800",children:[(0,r.jsxs)("button",{onClick:()=>{h(e=>!e)},className:"btn-primary",children:[(0,r.jsx)(w.D4o,{className:"w-5 h-5"}),m?t("exitFullscreen"):t("fullscreen")]}),(0,r.jsxs)("button",{className:" bg-primary-500 font-medium rounded-lg shadow-md text-neutral-100 flex items-center justify-center gap-2 px-4 py-2  active:scale-95 transition-all duration-300 ".concat(H?"hover:bg-primary-600  cursor-pointer":"opacity-50"),onClick:H?()=>{eo(e=>!e)}:void 0,children:[t("status"),ei?(0,r.jsx)(y.Ucs,{className:"w-3 h-3"}):(0,r.jsx)(y.Vr3,{className:"w-3 h-3"})]}),ei&&(0,r.jsx)("div",{className:"absolute left-30 top-10 mt-2 w-64 bg-neutral-100 border border-gray-200 shadow-md rounded-md p-2 z-40",children:(0,r.jsxs)("div",{className:"flex flex-col  gap-2",children:[(0,r.jsx)("div",{className:"hover:bg-neutral-200 cursor-pointer p-2 text-neutral-800 rounded-sm",children:t("setOnApproved")}),(0,r.jsx)("div",{className:"hover:bg-neutral-200 cursor-pointer p-2 text-neutral-800 rounded-sm",children:t("setOnNotApproved")}),(0,r.jsx)("div",{className:"hover:bg-neutral-200 cursor-pointer p-2 text-neutral-800 rounded-sm",children:t("setOnHold")})]})}),(0,r.jsxs)("button",{className:" bg-primary-500 font-medium rounded-lg shadow-md text-neutral-100 flex items-center justify-center gap-2 px-4 py-2  active:scale-95 transition-all duration-300 ".concat(H?"hover:bg-primary-600  cursor-pointer":"opacity-50"),onClick:H?()=>{let e=Object.keys(W);if(0===e.length)return void l((0,S.Ds)({message:t("noSubmissionSelected"),type:"error"}));let s=e.map(e=>a[Number(e)]).filter(Boolean);if(1===e.length){er(s[0]),z(!0);return}e.length>1&&(er(s[0]),z(!0))}:void 0,children:[(0,r.jsx)(p.JBV,{className:"h-4 w-4"}),t("edit")]}),(0,r.jsxs)("button",{className:" bg-primary-500 font-medium rounded-lg shadow-md text-neutral-100 flex items-center justify-center gap-2 px-4 py-2  active:scale-95 transition-all duration-300 ".concat(H?"hover:bg-primary-600  cursor-pointer":"opacity-50"),onClick:H?()=>{let e=Object.keys(W).map(e=>{var t;return(null==(t=a[parseInt(e)])?void 0:t.id)||0}).filter(e=>e>0);Q({title:t("confirmDeletion"),description:(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)("p",{children:[t("areYouSureToDelete"),e.length>1?"".concat(t("these")," ").concat(e.length," ").concat(t("submissions")):t("thisSubmission"),"? ",t("cannotRecoverSubmissions")]})}),confirmButtonText:t("delete"),confirmButtonClass:"bg-red-500 hover:bg-red-600 cursor-pointer",onConfirm:()=>{1===e.length?eu.mutate(e[0]):e.length>1&&em.mutate(e)}}),v(!0)}:void 0,children:[(0,r.jsx)(f.hJ0,{className:"h-4 w-4"}),t("delete")]})]})]}),i||u?(0,r.jsx)("div",{className:"flex justify-center items-center py-12",children:(0,r.jsx)("div",{className:"text-muted-foreground",children:t("loadingData")})}):(0,r.jsx)(E.x,{columns:el,data:a,globalFilter:j,setGlobalFilter:A,onTableInit:e=>{$(e),Object.keys(Y).length>0&&e.setColumnVisibility(Y)},columnVisibility:Y,setColumnVisibility:e=>{G(e)},onRowSelectionChange:e=>{L(Object.keys(e).length>0),Z(e),1===Object.keys(e).length?er(a[Number(Object.keys(e)[0])]):0===Object.keys(e).length&&er(null)},rowSelection:W}),U&&es&&(0,r.jsx)(O,{showModal:U,projectId:s,onClose:()=>{z(!1),er(null),Z({}),L(!1)},onConfirm:()=>{n.invalidateQueries({queryKey:["formSubmissions",s]}),Z({}),L(!1),z(!1),er(null)},submission:es,isMultipleSelection:Object.keys(W).length>1,selectedSubmissions:Object.keys(W).length>1?Object.keys(W).map(e=>a[Number(e)]).filter(Boolean):[]}),_&&(0,r.jsx)(M.R,{showModal:x,onClose:()=>v(!1),onConfirm:_.onConfirm,title:_.title,description:_.description,confirmButtonText:_.confirmButtonText,confirmButtonClass:_.confirmButtonClass}),es&&(0,r.jsx)(R,{isOpen:ee,onClose:()=>et(!1),submission:es})]})}},63642:(e,t,s)=>{"use strict";s.d(t,{R:()=>n});var r=s(95155);s(12115);var l=s(13163);let n=e=>{let{showModal:t,onClose:s,onConfirm:n,title:a,description:i,confirmButtonText:o,cancelButtonText:c,confirmButtonClass:d,children:u}=e;return(0,r.jsxs)(l.A,{isOpen:t,onClose:s,className:"p-6 rounded-md max-w-xl",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-neutral-700",children:a}),(0,r.jsx)("div",{className:"text-neutral-700 mt-2",children:i}),u&&(0,r.jsx)("div",{className:"mt-6 space-y-4",children:u}),(0,r.jsxs)("div",{className:"flex justify-end gap-4 mt-6",children:[(0,r.jsx)("button",{className:"btn-outline",onClick:s,type:"button",children:c||"Cancel"}),(0,r.jsx)("button",{className:"font-medium rounded-md shadow-md text-neutral-100 flex items-center justify-center gap-2 px-4 py-2 active:scale-95 transition-all duration-300 ".concat(d),onClick:n,type:"button",children:o})]})]})}},71402:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>a,Ds:()=>l,_b:()=>n});let r=(0,s(51990).Z0)({name:"notification",initialState:{message:"",type:"",visible:!1},reducers:{showNotification:(e,t)=>{e.message=t.payload.message,e.type=t.payload.type,e.visible=!0},hideNotification:e=>{e.message="",e.type="",e.visible=!1}}}),{showNotification:l,hideNotification:n}=r.actions,a=r.reducer},92657:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},97099:(e,t,s)=>{Promise.resolve().then(s.bind(s,61080))}},e=>{var t=t=>e(e.s=t);e.O(0,[2150,6711,3873,9204,6453,635,1111,6967,9373,4601,4277,556,3481,2854,6539,6268,4695,9660,8441,1684,7358],()=>t(97099)),_N_E=e.O()}]);