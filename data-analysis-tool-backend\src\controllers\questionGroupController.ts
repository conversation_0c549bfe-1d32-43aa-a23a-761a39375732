import { Request, Response } from "express";
import { QuestionGroup } from "@prisma/client";
import { v4 as uuidv4 } from "uuid";
import {
  questionGroupSchema,
  updateQuestionGroupSchema,
  updateSubGroupOrderAndAddQuestionSchema,
} from "../validators/questionGroupValidator";
import questionGroupRepository from "../repositories/questionGroupRepository";
import { prisma } from "../utils/prisma";
import questionRepository from "../repositories/questionRepository";

export const createQuestionGroup = async (req: Request, res: Response) => {
  try {
    const result = questionGroupSchema.safeParse(req.body);
    if (!result.success) {
      return res.status(400).json({
        success: false,
        errors: result.error.flatten().fieldErrors,
      });
    }
    const questionData = result.data;

    const questionGroup = await questionGroupRepository.create(questionData);

    return res.status(200).json({
      success: true,
      message: "question group created",
      data: { questionGroup },
    });
  } catch (error: unknown) {
    return res.status(500).json({
      success: false,
      message: "error creating question group",
      error: error instanceof Error ? error.message : "unexpected error",
    });
  }
};
export const updateQuestionGroup = async (req: Request, res: Response) => {
  try {
    const result = updateQuestionGroupSchema.safeParse(req.body);
    if (!result.success) {
      return res.status(400).json({
        success: false,
        errors: result.error.flatten().fieldErrors,
      });
    }

    const { id, title, order, parentGroupId, selectedQuestionIds } =
      result.data;

    const updates: any = {
      title,
      order,
      parentGroupId,
    };

    if (selectedQuestionIds && selectedQuestionIds.length > 0) {
      updates.question = {
        set: selectedQuestionIds.map((questionId: number) => ({
          id: questionId,
        })),
      };
    }

    const updateQuestionGroup = await questionGroupRepository.update(
      id,
      updates
    );

    return res.status(200).json({
      success: true,
      message: "Group question updated successfully",
      data: { updateQuestionGroup },
    });
  } catch (error: unknown) {
    return res.status(500).json({
      success: false,
      message: "Error updating question group",
      error: error instanceof Error ? error.message : "Unexpected error",
    });
  }
};

export const deleteQuestionGroup = async (req: Request, res: Response) => {
  try {
    const id = Number(req.params.id);
    if (!id) {
      return res.status(404).json({
        success: false,
        message: "invalid id",
      });
    }
    await questionGroupRepository.delete(id);

    return res.status(200).json({
      success: true,
      message: "group deleted sucess",
    });
  } catch (error: unknown) {
    return res.status(500).json({
      success: false,
      message: "error delete question group",
      error: error instanceof Error ? error.message : "unexpected error",
    });
  }
};

export const deleteQuestionAndGroup = async (req: Request, res: Response) => {
  try {
    const id = Number(req.params.id);
    if (!id) {
      return res.status(404).json({
        success: false,
        message: "invalid id",
      });
    }

    await questionGroupRepository.deleteManyQuestionByGroup(id);

    await questionGroupRepository.delete(id);

    return res.status(200).json({
      success: true,
      message: "group and question related to that group are delete succesfuly",
    });
  } catch (error: unknown) {
    return res.status(500).json({
      success: false,
      message: "error delete question group",
      error: error instanceof Error ? error.message : "unexpected error",
    });
  }
};

export const findAllProjectGroup = async (req: Request, res: Response) => {
  try {
    const { projectId } = req.body;
    if (!projectId) {
      return res.status(404).json({
        sucess: false,
        message: "please provide project id",
      });
    }

    const projectGroup = await questionGroupRepository.findAllByProject(
      projectId
    );

    return res.status(200).json({
      succes: true,
      message: "project group fetched success",
      data: { projectGroup },
    });
  } catch (error: unknown) {
    return res.status(500).json({
      success: false,
      message: "error getting question group",
      error: error instanceof Error ? error.message : "unexpected error",
    });
  }
};

export const removeQuestionIdFromGroup = async (
  req: Request,
  res: Response
) => {
  try {
    const { groupId, questionId } = req.body;

    const group = await prisma.questionGroup.findUnique({
      where: { id: Number(groupId) },
      include: { question: true },
    });

    if (!group) {
      return res.status(404).json({
        success: false,
        message: "Question group not found",
      });
    }

    const questionExists = group.question.some(
      (q) => q.id === Number(questionId)
    );
    if (!questionExists) {
      return res.status(404).json({
        success: false,
        message: "Question not found in this group",
      });
    }

    await prisma.question.update({
      where: { id: Number(questionId) },
      data: { questionGroupId: null }, // 👈 remove its link to the group
    });

    res.status(200).json({
      success: true,
      message: "Question removed from group successfully",
    });
  } catch (error: unknown) {
    return res.status(500).json({
      success: false,
      message: "error removing question from group",
      error: error instanceof Error ? error.message : "unexpected error",
    });
  }
};

export const updateQuestionFromOneGroupToAnother = async (
  req: Request,
  res: Response
) => {
  try {
    const { groupId, newGroupId, questionId } = req.body;
    if (!groupId || !newGroupId || !questionId) {
      return res.status(404).json({
        success: false,
        message: "id not found",
      });
    }

    const groupid = questionGroupRepository.findById(groupId);
    if (!groupid) {
      return res.status(404).json({
        success: false,
        message: "group id not found",
      });
    }

    const newGroupid = questionGroupRepository.findById(newGroupId);

    if (!newGroupid) {
      return res.status(404).json({
        success: false,
        message: "new group id not found",
      });
    }
    const question = await prisma.question.findUnique({
      where: { id: Number(questionId) },
    });

    if (!question) {
      return res.status(404).json({
        success: false,
        message: "question id not found",
      });
    }

    if (question.questionGroupId !== Number(groupId)) {
      return res.status(400).json({
        success: false,
        message: "Question does not belong to the old group",
      });
    }

    await prisma.question.update({
      where: { id: Number(questionId) },
      data: {
        questionGroupId: Number(newGroupId),
      },
    });

    return res.status(200).json({
      success: true,
      message: "update success",
    });
  } catch (error: unknown) {
    return res.status(500).json({
      success: false,
      message: "error adding question from one group to another",
      error: error instanceof Error ? error.message : "unexpected error",
    });
  }
};

export const updateOneGroupInsideAnotherGroup = async (
  req: Request,
  res: Response
) => {
  try {
    const { childGroupId, ParentGroupId } = req.body;

    const childGroupid = questionGroupRepository.findById(childGroupId);
    if (!childGroupid) {
      return res.status(404).json({
        success: false,
        message: "group id not found",
      });
    }

    const ParentGroupid = questionGroupRepository.findById(ParentGroupId);

    if (!ParentGroupid) {
      return res.status(404).json({
        success: false,
        message: "new group id not found",
      });
    }

    const update = await questionGroupRepository.updateGroupInsideParentGroup(
      childGroupId,
      ParentGroupId
    );

    return res.status(200).json({
      success: false,
      message: "question Group updated success",
      data: { update },
    });
  } catch (error: unknown) {
    return res.status(500).json({
      success: false,
      message: "error moving group inside the parentGroup",
      error: error instanceof Error ? error.message : "unexpected error",
    });
  }
};

export const removeGroupFromParentGroup = async (
  req: Request,
  res: Response
) => {
  try {
    const { groupId } = req.body;
    const groupid = questionGroupRepository.findById(groupId);
    if (!groupid) {
      return res.status(400).json({
        success: false,
        message: "Group id is required",
      });
    }
    const group = await questionGroupRepository.findById(groupId);

    if (!group) {
      return res.status(404).json({
        success: false,
        message: "Group id not found",
      });
    }

    // Optional: Check if group has a parentGroupId
    if (!group.parentGroupId) {
      return res.status(400).json({
        success: false,
        message: "Group has no parent group to remove",
      });
    }

    await questionGroupRepository.RemoveGroupFromParentGroup(groupId);

    return res.status(200).json({
      success: false,
      message: "question remove success",
    });
  } catch (error: unknown) {
    return res.status(500).json({
      success: false,
      message: "error adding question from one group to another",
      error: error instanceof Error ? error.message : "unexpected error",
    });
  }
};

export const updateGroupPositions = async (req: Request, res: Response) => {
  try {
    const { projectId, groupPositions } = req.body;

    if (!projectId || !groupPositions || !Array.isArray(groupPositions)) {
      return res.status(400).json({
        success: false,
        message: "Project ID and group positions array are required",
      });
    }

    // Update positions in bulk
    const updatedGroups = await questionGroupRepository.updateMultiplePositions(
      groupPositions
    );

    return res.status(200).json({
      success: true,
      message: "Group positions updated successfully",
      data: { groups: updatedGroups },
    });
  } catch (error: unknown) {
    return res.status(500).json({
      success: false,
      message: "error updating group positions",
      error: error instanceof Error ? error.message : "unexpected error",
    });
  }
};
