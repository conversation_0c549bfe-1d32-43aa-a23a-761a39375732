"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3481],{5041:(t,e,r)=>{r.d(e,{n:()=>h});var s=r(12115),n=r(34560),i=r(7165),o=r(25910),a=r(52020),u=class extends o.Q{#t;#e=void 0;#r;#s;constructor(t,e){super(),this.#t=t,this.setOptions(e),this.bindMethods(),this.#n()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(t){let e=this.options;this.options=this.#t.defaultMutationOptions(t),(0,a.f8)(this.options,e)||this.#t.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#r,observer:this}),e?.mutationKey&&this.options.mutationKey&&(0,a.EN)(e.mutationKey)!==(0,a.EN)(this.options.mutationKey)?this.reset():this.#r?.state.status==="pending"&&this.#r.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#r?.removeObserver(this)}onMutationUpdate(t){this.#n(),this.#i(t)}getCurrentResult(){return this.#e}reset(){this.#r?.removeObserver(this),this.#r=void 0,this.#n(),this.#i()}mutate(t,e){return this.#s=e,this.#r?.removeObserver(this),this.#r=this.#t.getMutationCache().build(this.#t,this.options),this.#r.addObserver(this),this.#r.execute(t)}#n(){let t=this.#r?.state??(0,n.$)();this.#e={...t,isPending:"pending"===t.status,isSuccess:"success"===t.status,isError:"error"===t.status,isIdle:"idle"===t.status,mutate:this.mutate,reset:this.reset}}#i(t){i.jG.batch(()=>{if(this.#s&&this.hasListeners()){let e=this.#e.variables,r=this.#e.context;t?.type==="success"?(this.#s.onSuccess?.(t.data,e,r),this.#s.onSettled?.(t.data,null,e,r)):t?.type==="error"&&(this.#s.onError?.(t.error,e,r),this.#s.onSettled?.(void 0,t.error,e,r))}this.listeners.forEach(t=>{t(this.#e)})})}},l=r(26715),c=r(63768);function h(t,e){let r=(0,l.jE)(e),[n]=s.useState(()=>new u(r,t));s.useEffect(()=>{n.setOptions(t)},[n,t]);let o=s.useSyncExternalStore(s.useCallback(t=>n.subscribe(i.jG.batchCalls(t)),[n]),()=>n.getCurrentResult(),()=>n.getCurrentResult()),a=s.useCallback((t,e)=>{n.mutate(t,e).catch(c.l)},[n]);if(o.error&&(0,c.G)(n.options.throwOnError,[o.error]))throw o.error;return{...o,mutate:a,mutateAsync:o.mutate}}},5196:(t,e,r)=>{r.d(e,{A:()=>s});let s=(0,r(19946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},34560:(t,e,r)=>{r.d(e,{$:()=>a,s:()=>o});var s=r(7165),n=r(57948),i=r(6784),o=class extends n.k{#o;#a;#u;constructor(t){super(),this.mutationId=t.mutationId,this.#a=t.mutationCache,this.#o=[],this.state=t.state||a(),this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){this.#o.includes(t)||(this.#o.push(t),this.clearGcTimeout(),this.#a.notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){this.#o=this.#o.filter(e=>e!==t),this.scheduleGc(),this.#a.notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){this.#o.length||("pending"===this.state.status?this.scheduleGc():this.#a.remove(this))}continue(){return this.#u?.continue()??this.execute(this.state.variables)}async execute(t){let e=()=>{this.#l({type:"continue"})};this.#u=(0,i.II)({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(Error("No mutationFn found")),onFail:(t,e)=>{this.#l({type:"failed",failureCount:t,error:e})},onPause:()=>{this.#l({type:"pause"})},onContinue:e,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#a.canRun(this)});let r="pending"===this.state.status,s=!this.#u.canStart();try{if(r)e();else{this.#l({type:"pending",variables:t,isPaused:s}),await this.#a.config.onMutate?.(t,this);let e=await this.options.onMutate?.(t);e!==this.state.context&&this.#l({type:"pending",context:e,variables:t,isPaused:s})}let n=await this.#u.start();return await this.#a.config.onSuccess?.(n,t,this.state.context,this),await this.options.onSuccess?.(n,t,this.state.context),await this.#a.config.onSettled?.(n,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(n,null,t,this.state.context),this.#l({type:"success",data:n}),n}catch(e){try{throw await this.#a.config.onError?.(e,t,this.state.context,this),await this.options.onError?.(e,t,this.state.context),await this.#a.config.onSettled?.(void 0,e,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,e,t,this.state.context),e}finally{this.#l({type:"error",error:e})}}finally{this.#a.runNext(this)}}#l(t){this.state=(e=>{switch(t.type){case"failed":return{...e,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...e,isPaused:!0};case"continue":return{...e,isPaused:!1};case"pending":return{...e,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...e,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...e,data:void 0,error:t.error,failureCount:e.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}})(this.state),s.jG.batch(()=>{this.#o.forEach(e=>{e.onMutationUpdate(t)}),this.#a.notify({mutation:this,type:"updated",action:t})})}};function a(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}},37328:(t,e,r)=>{function s(t,e,r){if(!e.has(t))throw TypeError("attempted to "+r+" private field on non-instance");return e.get(t)}function n(t,e){var r=s(t,e,"get");return r.get?r.get.call(t):r.value}function i(t,e,r){var n=s(t,e,"set");if(n.set)n.set.call(t,r);else{if(!n.writable)throw TypeError("attempted to set read only private field");n.value=r}return r}r.d(e,{N:()=>d});var o,a=r(12115),u=r(46081),l=r(6101),c=r(99708),h=r(95155);function d(t){let e=t+"CollectionProvider",[r,s]=(0,u.A)(e),[n,i]=r(e,{collectionRef:{current:null},itemMap:new Map}),o=t=>{let{scope:e,children:r}=t,s=a.useRef(null),i=a.useRef(new Map).current;return(0,h.jsx)(n,{scope:e,itemMap:i,collectionRef:s,children:r})};o.displayName=e;let d=t+"CollectionSlot",f=(0,c.TL)(d),p=a.forwardRef((t,e)=>{let{scope:r,children:s}=t,n=i(d,r),o=(0,l.s)(e,n.collectionRef);return(0,h.jsx)(f,{ref:o,children:s})});p.displayName=d;let m=t+"CollectionItemSlot",v="data-radix-collection-item",y=(0,c.TL)(m),b=a.forwardRef((t,e)=>{let{scope:r,children:s,...n}=t,o=a.useRef(null),u=(0,l.s)(e,o),c=i(m,r);return a.useEffect(()=>(c.itemMap.set(o,{ref:o,...n}),()=>void c.itemMap.delete(o))),(0,h.jsx)(y,{...{[v]:""},ref:u,children:s})});return b.displayName=m,[{Provider:o,Slot:p,ItemSlot:b},function(e){let r=i(t+"CollectionConsumer",e);return a.useCallback(()=>{let t=r.collectionRef.current;if(!t)return[];let e=Array.from(t.querySelectorAll("[".concat(v,"]")));return Array.from(r.itemMap.values()).sort((t,r)=>e.indexOf(t.ref.current)-e.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},s]}var f=new WeakMap;function p(t,e){if("at"in Array.prototype)return Array.prototype.at.call(t,e);let r=function(t,e){let r=t.length,s=m(e),n=s>=0?s:r+s;return n<0||n>=r?-1:n}(t,e);return -1===r?void 0:t[r]}function m(t){return t!=t||0===t?0:Math.trunc(t)}o=new WeakMap},45503:(t,e,r)=>{r.d(e,{Z:()=>n});var s=r(12115);function n(t){let e=s.useRef({value:t,previous:t});return s.useMemo(()=>(e.current.value!==t&&(e.current.previous=e.current.value,e.current.value=t),e.current.previous),[t])}},63655:(t,e,r)=>{r.d(e,{hO:()=>u,sG:()=>a});var s=r(12115),n=r(47650),i=r(99708),o=r(95155),a=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((t,e)=>{let r=(0,i.TL)(`Primitive.${e}`),n=s.forwardRef((t,s)=>{let{asChild:n,...i}=t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,o.jsx)(n?r:e,{...i,ref:s})});return n.displayName=`Primitive.${e}`,{...t,[e]:n}},{});function u(t,e){t&&n.flushSync(()=>t.dispatchEvent(e))}},76981:(t,e,r)=>{r.d(e,{C1:()=>E,bL:()=>M});var s=r(12115),n=r(6101),i=r(46081),o=r(85185),a=r(5845),u=r(45503),l=r(11275),c=r(28905),h=r(63655),d=r(95155),f="Checkbox",[p,m]=(0,i.A)(f),[v,y]=p(f),b=s.forwardRef((t,e)=>{let{__scopeCheckbox:r,name:i,checked:u,defaultChecked:l,required:c,disabled:p,value:m="on",onCheckedChange:y,form:b,...w}=t,[x,M]=s.useState(null),E=(0,n.s)(e,t=>M(t)),k=s.useRef(!1),S=!x||b||!!x.closest("form"),[O,A]=(0,a.i)({prop:u,defaultProp:null!=l&&l,onChange:y,caller:f}),j=s.useRef(O);return s.useEffect(()=>{let t=null==x?void 0:x.form;if(t){let e=()=>A(j.current);return t.addEventListener("reset",e),()=>t.removeEventListener("reset",e)}},[x,A]),(0,d.jsxs)(v,{scope:r,state:O,disabled:p,children:[(0,d.jsx)(h.sG.button,{type:"button",role:"checkbox","aria-checked":g(O)?"mixed":O,"aria-required":c,"data-state":C(O),"data-disabled":p?"":void 0,disabled:p,value:m,...w,ref:E,onKeyDown:(0,o.m)(t.onKeyDown,t=>{"Enter"===t.key&&t.preventDefault()}),onClick:(0,o.m)(t.onClick,t=>{A(t=>!!g(t)||!t),S&&(k.current=t.isPropagationStopped(),k.current||t.stopPropagation())})}),S&&(0,d.jsx)(R,{control:x,bubbles:!k.current,name:i,value:m,checked:O,required:c,disabled:p,form:b,style:{transform:"translateX(-100%)"},defaultChecked:!g(l)&&l})]})});b.displayName=f;var w="CheckboxIndicator",x=s.forwardRef((t,e)=>{let{__scopeCheckbox:r,forceMount:s,...n}=t,i=y(w,r);return(0,d.jsx)(c.C,{present:s||g(i.state)||!0===i.state,children:(0,d.jsx)(h.sG.span,{"data-state":C(i.state),"data-disabled":i.disabled?"":void 0,...n,ref:e,style:{pointerEvents:"none",...t.style}})})});x.displayName=w;var R=s.forwardRef((t,e)=>{let{__scopeCheckbox:r,control:i,checked:o,bubbles:a=!0,defaultChecked:c,...f}=t,p=s.useRef(null),m=(0,n.s)(p,e),v=(0,u.Z)(o),y=(0,l.X)(i);s.useEffect(()=>{let t=p.current;if(!t)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(v!==o&&e){let r=new Event("click",{bubbles:a});t.indeterminate=g(o),e.call(t,!g(o)&&o),t.dispatchEvent(r)}},[v,o,a]);let b=s.useRef(!g(o)&&o);return(0,d.jsx)(h.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:null!=c?c:b.current,...f,tabIndex:-1,ref:m,style:{...f.style,...y,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function g(t){return"indeterminate"===t}function C(t){return g(t)?"indeterminate":t?"checked":"unchecked"}R.displayName="CheckboxBubbleInput";var M=b,E=x},89196:(t,e,r)=>{r.d(e,{RG:()=>R,bL:()=>j,q7:()=>I});var s=r(12115),n=r(85185),i=r(37328),o=r(6101),a=r(46081),u=r(61285),l=r(63655),c=r(39033),h=r(5845),d=r(94315),f=r(95155),p="rovingFocusGroup.onEntryFocus",m={bubbles:!1,cancelable:!0},v="RovingFocusGroup",[y,b,w]=(0,i.N)(v),[x,R]=(0,a.A)(v,[w]),[g,C]=x(v),M=s.forwardRef((t,e)=>(0,f.jsx)(y.Provider,{scope:t.__scopeRovingFocusGroup,children:(0,f.jsx)(y.Slot,{scope:t.__scopeRovingFocusGroup,children:(0,f.jsx)(E,{...t,ref:e})})}));M.displayName=v;var E=s.forwardRef((t,e)=>{let{__scopeRovingFocusGroup:r,orientation:i,loop:a=!1,dir:u,currentTabStopId:y,defaultCurrentTabStopId:w,onCurrentTabStopIdChange:x,onEntryFocus:R,preventScrollOnEntryFocus:C=!1,...M}=t,E=s.useRef(null),k=(0,o.s)(e,E),S=(0,d.jH)(u),[O,j]=(0,h.i)({prop:y,defaultProp:null!=w?w:null,onChange:x,caller:v}),[I,P]=s.useState(!1),G=(0,c.c)(R),T=b(r),F=s.useRef(!1),[D,N]=s.useState(0);return s.useEffect(()=>{let t=E.current;if(t)return t.addEventListener(p,G),()=>t.removeEventListener(p,G)},[G]),(0,f.jsx)(g,{scope:r,orientation:i,dir:S,loop:a,currentTabStopId:O,onItemFocus:s.useCallback(t=>j(t),[j]),onItemShiftTab:s.useCallback(()=>P(!0),[]),onFocusableItemAdd:s.useCallback(()=>N(t=>t+1),[]),onFocusableItemRemove:s.useCallback(()=>N(t=>t-1),[]),children:(0,f.jsx)(l.sG.div,{tabIndex:I||0===D?-1:0,"data-orientation":i,...M,ref:k,style:{outline:"none",...t.style},onMouseDown:(0,n.m)(t.onMouseDown,()=>{F.current=!0}),onFocus:(0,n.m)(t.onFocus,t=>{let e=!F.current;if(t.target===t.currentTarget&&e&&!I){let e=new CustomEvent(p,m);if(t.currentTarget.dispatchEvent(e),!e.defaultPrevented){let t=T().filter(t=>t.focusable);A([t.find(t=>t.active),t.find(t=>t.id===O),...t].filter(Boolean).map(t=>t.ref.current),C)}}F.current=!1}),onBlur:(0,n.m)(t.onBlur,()=>P(!1))})})}),k="RovingFocusGroupItem",S=s.forwardRef((t,e)=>{let{__scopeRovingFocusGroup:r,focusable:i=!0,active:o=!1,tabStopId:a,children:c,...h}=t,d=(0,u.B)(),p=a||d,m=C(k,r),v=m.currentTabStopId===p,w=b(r),{onFocusableItemAdd:x,onFocusableItemRemove:R,currentTabStopId:g}=m;return s.useEffect(()=>{if(i)return x(),()=>R()},[i,x,R]),(0,f.jsx)(y.ItemSlot,{scope:r,id:p,focusable:i,active:o,children:(0,f.jsx)(l.sG.span,{tabIndex:v?0:-1,"data-orientation":m.orientation,...h,ref:e,onMouseDown:(0,n.m)(t.onMouseDown,t=>{i?m.onItemFocus(p):t.preventDefault()}),onFocus:(0,n.m)(t.onFocus,()=>m.onItemFocus(p)),onKeyDown:(0,n.m)(t.onKeyDown,t=>{if("Tab"===t.key&&t.shiftKey)return void m.onItemShiftTab();if(t.target!==t.currentTarget)return;let e=function(t,e,r){var s;let n=(s=t.key,"rtl"!==r?s:"ArrowLeft"===s?"ArrowRight":"ArrowRight"===s?"ArrowLeft":s);if(!("vertical"===e&&["ArrowLeft","ArrowRight"].includes(n))&&!("horizontal"===e&&["ArrowUp","ArrowDown"].includes(n)))return O[n]}(t,m.orientation,m.dir);if(void 0!==e){if(t.metaKey||t.ctrlKey||t.altKey||t.shiftKey)return;t.preventDefault();let r=w().filter(t=>t.focusable).map(t=>t.ref.current);if("last"===e)r.reverse();else if("prev"===e||"next"===e){"prev"===e&&r.reverse();let s=r.indexOf(t.currentTarget);r=m.loop?function(t,e){return t.map((r,s)=>t[(e+s)%t.length])}(r,s+1):r.slice(s+1)}setTimeout(()=>A(r))}}),children:"function"==typeof c?c({isCurrentTabStop:v,hasTabStop:null!=g}):c})})});S.displayName=k;var O={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function A(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.activeElement;for(let s of t)if(s===r||(s.focus({preventScroll:e}),document.activeElement!==r))return}var j=M,I=S},94315:(t,e,r)=>{r.d(e,{jH:()=>i});var s=r(12115);r(95155);var n=s.createContext(void 0);function i(t){let e=s.useContext(n);return t||e||"ltr"}}}]);