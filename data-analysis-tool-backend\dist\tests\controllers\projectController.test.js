"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const projectController_1 = require("../../controllers/projectController");
const projectRepository_1 = __importDefault(require("../../repositories/projectRepository"));
const client_1 = require("@prisma/client");
// Mock the dependencies
jest.mock("../../repositories/projectRepository");
describe("Project Controller", () => {
    let mockRequest;
    let mockResponse;
    let responseObject = {};
    beforeEach(() => {
        // Reset mocks before each test
        jest.clearAllMocks();
        jest.resetAllMocks();
        // Setup mock response
        mockResponse = {
            status: jest.fn().mockReturnThis(),
            json: jest.fn().mockImplementation((result) => {
                responseObject = result;
                return mockResponse;
            }),
        };
        // Reset response object
        responseObject = {};
        // Setup default authenticated user
        mockRequest = {
            user: {
                id: 1,
            },
            params: {},
            query: {},
            body: {},
        };
        // Default mock for findProjectByIdAndUser to pass ownership checks (used in other tests)
        projectRepository_1.default.findProjectByIdAndUser.mockImplementation((id, userId) => Promise.resolve({ id, userId, name: `Project ${id}` }));
    });
    describe("createProject", () => {
        beforeEach(() => {
            mockRequest.body = {
                name: "Test Project",
                description: "This is a test project",
                sector: "information_media",
                country: "United States",
            };
        });
        it("should create a project successfully", () => __awaiter(void 0, void 0, void 0, function* () {
            const mockProject = {
                id: 1,
                name: "Test Project",
                description: "This is a test project",
                sector: "information_media",
                country: "United States",
                userId: 1,
                createdAt: new Date(),
                updatedAt: new Date(),
            };
            projectRepository_1.default.findByName.mockResolvedValue(null);
            projectRepository_1.default.create.mockResolvedValue(mockProject);
            yield (0, projectController_1.createProject)(mockRequest, mockResponse);
            expect(projectRepository_1.default.findByName).toHaveBeenCalledWith("Test Project", 1);
            expect(projectRepository_1.default.create).toHaveBeenCalledWith({
                name: "Test Project",
                description: "This is a test project",
                sector: "information_media",
                userId: 1,
                country: "United States",
            });
            expect(mockResponse.status).toHaveBeenCalledWith(201);
            expect(responseObject).toHaveProperty("message", "Project created successfully");
            expect(responseObject).toHaveProperty("project", mockProject);
        }));
        it("should return 400 when project already exists", () => __awaiter(void 0, void 0, void 0, function* () {
            const existingProject = {
                id: 1,
                name: "Test Project",
                userId: 1,
            };
            projectRepository_1.default.findByName.mockResolvedValue(existingProject);
            yield (0, projectController_1.createProject)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(400);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "project already exist");
            expect(projectRepository_1.default.create).not.toHaveBeenCalled();
        }));
        it("should return 400 for invalid input", () => __awaiter(void 0, void 0, void 0, function* () {
            // Missing required fields
            mockRequest.body = { name: "Test Project" };
            yield (0, projectController_1.createProject)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(400);
            expect(responseObject).toHaveProperty("success", false);
            expect(projectRepository_1.default.create).not.toHaveBeenCalled();
        }));
        it("should handle server errors", () => __awaiter(void 0, void 0, void 0, function* () {
            projectRepository_1.default.findByName.mockRejectedValue(new Error("Database error"));
            yield (0, projectController_1.createProject)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(500);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "error creating project");
        }));
    });
    describe("getAllProject", () => {
        it("should get all projects successfully", () => __awaiter(void 0, void 0, void 0, function* () {
            const mockProjects = [
                {
                    id: 1,
                    name: "Project 1",
                    description: "Description 1",
                    userId: 1,
                },
                {
                    id: 2,
                    name: "Project 2",
                    description: "Description 2",
                    userId: 1,
                },
            ];
            projectRepository_1.default.findAll.mockResolvedValue(mockProjects);
            yield (0, projectController_1.getAllProject)(mockRequest, mockResponse);
            expect(projectRepository_1.default.findAll).toHaveBeenCalledWith(1);
            expect(mockResponse.status).toHaveBeenCalledWith(200);
            expect(responseObject).toHaveProperty("message", "Successfully fetched all projects");
            expect(responseObject).toHaveProperty("projects", mockProjects);
        }));
        it("should handle server errors", () => __awaiter(void 0, void 0, void 0, function* () {
            projectRepository_1.default.findAll.mockRejectedValue(new Error("Database error"));
            yield (0, projectController_1.getAllProject)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(500);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "error getting projects");
        }));
    });
    describe("updateProjects", () => {
        beforeEach(() => {
            mockRequest.params = { id: "1" };
            mockRequest.body = {
                name: "Updated Project",
                description: "Updated description",
                sector: "information_media",
                country: "Canada",
            };
        });
        it("should update a project successfully", () => __awaiter(void 0, void 0, void 0, function* () {
            const updatedProject = {
                id: 1,
                name: "Updated Project",
                description: "Updated description",
                sector: "information_media",
                country: "Canada",
                userId: 1,
            };
            projectRepository_1.default.updateById.mockResolvedValue(updatedProject);
            yield (0, projectController_1.updateProjects)(mockRequest, mockResponse);
            expect(projectRepository_1.default.findProjectByIdAndUser).toHaveBeenCalledWith(1, 1);
            expect(projectRepository_1.default.updateById).toHaveBeenCalledWith(1, expect.objectContaining({
                name: "Updated Project",
                description: "Updated description",
            }));
            expect(mockResponse.status).toHaveBeenCalledWith(200);
            expect(responseObject.data).toHaveProperty("updatedProject", updatedProject);
        }));
        it("should return 404 when project not found", () => __awaiter(void 0, void 0, void 0, function* () {
            projectRepository_1.default.findProjectByIdAndUser.mockResolvedValue(null);
            yield (0, projectController_1.updateProjects)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(404);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "no project found");
            expect(projectRepository_1.default.updateById).not.toHaveBeenCalled();
        }));
        it("should return 400 for invalid input", () => __awaiter(void 0, void 0, void 0, function* () {
            // Invalid input - missing required fields
            mockRequest.body = {};
            yield (0, projectController_1.updateProjects)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(400);
            expect(responseObject).toHaveProperty("success", false);
            expect(projectRepository_1.default.updateById).not.toHaveBeenCalled();
        }));
        it("should handle server errors", () => __awaiter(void 0, void 0, void 0, function* () {
            projectRepository_1.default.findProjectByIdAndUser.mockRejectedValue(new Error("Database error"));
            yield (0, projectController_1.updateProjects)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(500);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "error updating project");
        }));
    });
    describe("deleteProject", () => {
        beforeEach(() => {
            mockRequest.params = { id: "1" };
        });
        it("should delete a project successfully", () => __awaiter(void 0, void 0, void 0, function* () {
            projectRepository_1.default.deleteProject.mockResolvedValue({
                id: 1,
            });
            yield (0, projectController_1.deleteProject)(mockRequest, mockResponse);
            expect(projectRepository_1.default.findProjectByIdAndUser).toHaveBeenCalledWith(1, 1);
            expect(projectRepository_1.default.deleteProject).toHaveBeenCalledWith(1);
            expect(mockResponse.status).toHaveBeenCalledWith(200);
            expect(responseObject).toHaveProperty("message", "project deleted success");
        }));
        it("should return 404 when project not found", () => __awaiter(void 0, void 0, void 0, function* () {
            projectRepository_1.default.findProjectByIdAndUser.mockResolvedValue(null);
            yield (0, projectController_1.deleteProject)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(404);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "no project found with give id");
            expect(projectRepository_1.default.deleteProject).not.toHaveBeenCalled();
        }));
        it("should handle server errors", () => __awaiter(void 0, void 0, void 0, function* () {
            projectRepository_1.default.findProjectByIdAndUser.mockRejectedValue(new Error("Database error"));
            yield (0, projectController_1.deleteProject)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(500);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "error deleting project");
        }));
    });
    describe("getProjectById", () => {
        beforeEach(() => {
            mockRequest.params = { id: "1" };
        });
        it("should get a project by ID successfully", () => __awaiter(void 0, void 0, void 0, function* () {
            const mockProject = {
                id: 1,
                name: "Test Project",
                description: "This is a test project",
                userId: 1,
            };
            projectRepository_1.default.findProjectByIdAndUser.mockResolvedValue(mockProject);
            yield (0, projectController_1.getProjectById)(mockRequest, mockResponse);
            expect(projectRepository_1.default.findProjectByIdAndUser).toHaveBeenCalledWith(1, 1);
            expect(mockResponse.status).toHaveBeenCalledWith(200);
            expect(responseObject).toHaveProperty("message", "Successfully fetched project");
            expect(responseObject).toHaveProperty("project", mockProject);
        }));
        it("should return 404 when project not found", () => __awaiter(void 0, void 0, void 0, function* () {
            projectRepository_1.default.findProjectByIdAndUser.mockResolvedValue(null);
            yield (0, projectController_1.getProjectById)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(404);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "project not found");
        }));
        it("should handle server errors", () => __awaiter(void 0, void 0, void 0, function* () {
            projectRepository_1.default.findProjectByIdAndUser.mockRejectedValue(new Error("Database error"));
            yield (0, projectController_1.getProjectById)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(500);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "error getting user by id");
        }));
    });
    describe("changeProjectStatus", () => {
        beforeEach(() => {
            mockRequest.params = { id: "1" };
            mockRequest.body = {
                status: "deployed",
            };
        });
        it("should change project status successfully", () => __awaiter(void 0, void 0, void 0, function* () {
            const updatedProject = {
                id: 1,
                name: "Test Project",
                status: "deployed",
            };
            projectRepository_1.default.changeProjectStatus.mockResolvedValue(updatedProject);
            yield (0, projectController_1.changeProjectStatus)(mockRequest, mockResponse);
            expect(projectRepository_1.default.changeProjectStatus).toHaveBeenCalledWith(1, "deployed");
            expect(mockResponse.status).toHaveBeenCalledWith(200);
            expect(responseObject.data).toHaveProperty("updatedProject", updatedProject);
        }));
        it("should return 400 for invalid input", () => __awaiter(void 0, void 0, void 0, function* () {
            // Invalid status
            mockRequest.body = { status: "INVALID_STATUS" };
            yield (0, projectController_1.changeProjectStatus)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(400);
            expect(responseObject).toHaveProperty("success", false);
            expect(projectRepository_1.default.changeProjectStatus).not.toHaveBeenCalled();
        }));
        it("should handle server errors", () => __awaiter(void 0, void 0, void 0, function* () {
            projectRepository_1.default.changeProjectStatus.mockRejectedValue(new Error("Database error"));
            yield (0, projectController_1.changeProjectStatus)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(500);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "error creating qustion");
        }));
    });
    describe("updateManyProjectStatus", () => {
        beforeEach(() => {
            mockRequest.body = {
                projectIds: [1, 2, 3],
                status: client_1.Status.deployed,
            };
        });
        it("should update many project statuses successfully", () => __awaiter(void 0, void 0, void 0, function* () {
            const mockResult = { count: 3 };
            projectRepository_1.default.changeManyProjectStatus.mockResolvedValue(mockResult);
            yield (0, projectController_1.updateManyProjectStatus)(mockRequest, mockResponse);
            expect(projectRepository_1.default.changeManyProjectStatus).toHaveBeenCalledWith([1, 2, 3], client_1.Status.deployed);
            expect(mockResponse.status).toHaveBeenCalledWith(200);
            expect(responseObject).toHaveProperty("success", false); // Controller bug
            expect(responseObject).toHaveProperty("message", "status changed success");
            expect(responseObject.data).toHaveProperty("updatedProject", mockResult);
        }));
        it("should return 400 for invalid input", () => __awaiter(void 0, void 0, void 0, function* () {
            // Missing required fields
            mockRequest.body = { projectIds: [1, 2, 3] };
            yield (0, projectController_1.updateManyProjectStatus)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(400);
            expect(responseObject).toHaveProperty("success", false);
            expect(projectRepository_1.default.changeManyProjectStatus).not.toHaveBeenCalled();
        }));
        it("should handle server errors", () => __awaiter(void 0, void 0, void 0, function* () {
            projectRepository_1.default.changeManyProjectStatus.mockRejectedValue(new Error("Database error"));
            yield (0, projectController_1.updateManyProjectStatus)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(500);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "error creating qustion");
        }));
    });
    describe("DeleteMultipleProject", () => {
        beforeEach(() => {
            mockRequest.body = {
                projectIds: [1, 2, 3],
            };
        });
        it("should delete multiple projects successfully", () => __awaiter(void 0, void 0, void 0, function* () {
            const mockResult = { count: 3 };
            projectRepository_1.default.deleteMultipleProject.mockResolvedValue(mockResult);
            yield (0, projectController_1.DeleteMultipleProject)(mockRequest, mockResponse);
            expect(projectRepository_1.default.deleteMultipleProject).toHaveBeenCalledWith([
                1, 2, 3,
            ]);
            expect(mockResponse.status).toHaveBeenCalledWith(200);
            expect(responseObject).toHaveProperty("success", false); // Controller bug
            expect(responseObject).toHaveProperty("message", "project deleted success");
        }));
        it("should return 400 for invalid input", () => __awaiter(void 0, void 0, void 0, function* () {
            // Invalid input (not an array)
            mockRequest.body = { projectIds: "not an array" };
            yield (0, projectController_1.DeleteMultipleProject)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(400);
            expect(responseObject).toHaveProperty("success", false);
            expect(projectRepository_1.default.deleteMultipleProject).not.toHaveBeenCalled();
        }));
        it("should handle server errors", () => __awaiter(void 0, void 0, void 0, function* () {
            projectRepository_1.default.deleteMultipleProject.mockRejectedValue(new Error("Database error"));
            yield (0, projectController_1.DeleteMultipleProject)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(500);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "error creating qustion");
        }));
    });
});
