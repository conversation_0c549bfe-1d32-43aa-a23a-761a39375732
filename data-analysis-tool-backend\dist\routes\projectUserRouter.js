"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const projectUserController_1 = require("../controllers/projectUserController");
const auth_1 = require("../middleware/auth");
const checkPermission_1 = require("../middleware/checkPermission");
const router = express_1.default.Router();
router.post("/", auth_1.authenticate, 
// checkPermission("manageProject") as unknown as express.RequestHandler,
projectUserController_1.createProjectUser);
router.get("/:projectId", auth_1.authenticate, projectUserController_1.getAllProjectUser);
router.patch("/", auth_1.authenticate, projectUserController_1.updateProjectUser);
router.delete("/", auth_1.authenticate, (0, checkPermission_1.checkPermission)("manageProject"), projectUserController_1.deletProjectUser);
router.post("/copy-users", auth_1.authenticate, projectUserController_1.copyProjectUsers);
// router.post(
//   "/get-users",
//   authenticate,
//   getAllProjectUser as unknown as express.RequestHandler
// );
exports.default = router;
