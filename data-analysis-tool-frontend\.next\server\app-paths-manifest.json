{"/_not-found/page": "app/_not-found/page.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/form-submission/[hashedId]/sign-in/page": "app/form-submission/[hashedId]/sign-in/page.js", "/form-submission/[hashedId]/page": "app/form-submission/[hashedId]/page.js", "/edit-submission/[hashedId]/[submissionId]/page": "app/edit-submission/[hashedId]/[submissionId]/page.js", "/[locale]/(auth)/page": "app/[locale]/(auth)/page.js", "/[locale]/(auth)/signup/page": "app/[locale]/(auth)/signup/page.js", "/[locale]/(auth)/reset-password/page": "app/[locale]/(auth)/reset-password/page.js", "/[locale]/test-page/page": "app/[locale]/test-page/page.js", "/[locale]/(auth)/reset-password/change-password/page": "app/[locale]/(auth)/reset-password/change-password/page.js", "/[locale]/(main)/dashboard/[status]/not-available/page": "app/[locale]/(main)/dashboard/[status]/not-available/page.js", "/[locale]/(main)/dashboard/page": "app/[locale]/(main)/dashboard/page.js", "/[locale]/(main)/library/page": "app/[locale]/(main)/library/page.js", "/[locale]/(main)/library/asset/page": "app/[locale]/(main)/library/asset/page.js", "/[locale]/(main)/policy/page": "app/[locale]/(main)/policy/page.js", "/[locale]/(main)/library/not-available/page": "app/[locale]/(main)/library/not-available/page.js", "/[locale]/(main)/terms/page": "app/[locale]/(main)/terms/page.js", "/[locale]/(main)/account/profile/page": "app/[locale]/(main)/account/profile/page.js", "/[locale]/(main)/library/question-block/form-builder/page": "app/[locale]/(main)/library/question-block/form-builder/page.js", "/[locale]/(main)/library/template/[hashedId]/settings/page": "app/[locale]/(main)/library/template/[hashedId]/settings/page.js", "/[locale]/(main)/account/security/page": "app/[locale]/(main)/account/security/page.js", "/[locale]/(main)/library/template/[hashedId]/form-builder/page": "app/[locale]/(main)/library/template/[hashedId]/form-builder/page.js", "/[locale]/(main)/project/[hashedId]/settings/page": "app/[locale]/(main)/project/[hashedId]/settings/page.js", "/[locale]/(main)/project/[hashedId]/overview/page": "app/[locale]/(main)/project/[hashedId]/overview/page.js", "/[locale]/(main)/project/[hashedId]/form-builder/page": "app/[locale]/(main)/project/[hashedId]/form-builder/page.js", "/[locale]/(main)/project/[hashedId]/data/downloads/page": "app/[locale]/(main)/project/[hashedId]/data/downloads/page.js", "/[locale]/(main)/project/[hashedId]/data/gallery/page": "app/[locale]/(main)/project/[hashedId]/data/gallery/page.js", "/[locale]/(main)/project/[hashedId]/data/reports/page": "app/[locale]/(main)/project/[hashedId]/data/reports/page.js", "/[locale]/(main)/project/[hashedId]/data/page": "app/[locale]/(main)/project/[hashedId]/data/page.js", "/[locale]/(main)/project/[hashedId]/data/table/page": "app/[locale]/(main)/project/[hashedId]/data/table/page.js", "/[locale]/(main)/project/[hashedId]/data/map/page": "app/[locale]/(main)/project/[hashedId]/data/map/page.js"}