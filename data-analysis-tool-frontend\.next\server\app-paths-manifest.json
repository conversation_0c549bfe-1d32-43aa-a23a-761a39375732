{"/[locale]/(auth)/page": "app/[locale]/(auth)/page.js", "/[locale]/(main)/dashboard/[status]/not-available/page": "app/[locale]/(main)/dashboard/[status]/not-available/page.js", "/[locale]/(main)/dashboard/page": "app/[locale]/(main)/dashboard/page.js", "/[locale]/(main)/library/page": "app/[locale]/(main)/library/page.js", "/[locale]/(main)/library/question-block/form-builder/page": "app/[locale]/(main)/library/question-block/form-builder/page.js", "/[locale]/(main)/library/template/[hashedId]/form-builder/page": "app/[locale]/(main)/library/template/[hashedId]/form-builder/page.js", "/[locale]/(main)/project/[hashedId]/data/page": "app/[locale]/(main)/project/[hashedId]/data/page.js", "/[locale]/(main)/project/[hashedId]/data/table/page": "app/[locale]/(main)/project/[hashedId]/data/table/page.js", "/[locale]/(main)/project/[hashedId]/form-builder/page": "app/[locale]/(main)/project/[hashedId]/form-builder/page.js", "/[locale]/(main)/project/[hashedId]/overview/page": "app/[locale]/(main)/project/[hashedId]/overview/page.js", "/[locale]/(main)/project/[hashedId]/settings/page": "app/[locale]/(main)/project/[hashedId]/settings/page.js", "/edit-submission/[hashedId]/[submissionId]/page": "app/edit-submission/[hashedId]/[submissionId]/page.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/form-submission/[hashedId]/page": "app/form-submission/[hashedId]/page.js"}