"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const client_1 = require("@prisma/client");
const prisma = new client_1.PrismaClient();
class ExportFileRepository {
    create(data) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma.exportedFile.create({
                data: {
                    projectId: data.projectId,
                    userId: data.userId,
                    fileName: data.fileName,
                    fileType: data.fileType,
                    contentType: data.contentType,
                    fileBuffer: data.fileBuffer,
                },
            });
        });
    }
    findById(id) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma.exportedFile.findUnique({
                where: { id },
            });
        });
    }
    findAll(userId, projectId) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma.exportedFile.findMany({
                where: {
                    userId: userId,
                    projectId: projectId,
                },
            });
        });
    }
    deleteFile(formId) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma.exportedFile.delete({
                where: {
                    id: formId,
                },
            });
        });
    }
}
exports.default = new ExportFileRepository();
