"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.checkUserByEmail = exports.getUserSessions = exports.changeEmail = exports.getUserSession = exports.sendVerificationEmail = exports.userProfile = exports.findAllUsers = exports.findUserById = exports.resetPassword = exports.checkPasswordResetToken = exports.forgetPassword = exports.changePassword = exports.updateProfile = exports.logoutFromAllDevice = exports.logout = exports.login = exports.verifyEmail = exports.signup = void 0;
// import jwt from 'jsonwebtoken';
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const userRepository_1 = __importDefault(require("../repositories/userRepository"));
const ApiResponse_1 = require("../utils/ApiResponse");
const UserValidators_1 = require("../validators/UserValidators");
const crypto_js_1 = __importDefault(require("crypto-js"));
const sha256_1 = __importDefault(require("crypto-js/sha256"));
const sendMail_1 = require("../utils/sendMail");
const emailTemplate_1 = require("../utils/emailTemplate");
const forgotPasswordEmailTemplate_1 = require("../utils/forgotPasswordEmailTemplate");
const useragent_1 = __importDefault(require("useragent"));
const baseUrl = process.env.BASE_URL || "http://localhost:4000";
const clientUrl = process.env.CLIENT_URL || "http://localhost:3000";
// User signup
const signup = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const result = UserValidators_1.userSchema.safeParse(req.body);
        if (!result.success) {
            res.status(400).json({
                success: false,
                message: result.error.flatten().fieldErrors,
            });
            return;
        }
        const { name, email, password, country, sector, organizationType } = result.data;
        // Check if user already exists
        const existingUser = yield userRepository_1.default.findByEmail(email);
        if (existingUser) {
            res.status(400).json({
                success: false,
                errorField: "email",
                message: "email already in use",
            });
            return;
        }
        // Create new user
        const user = yield userRepository_1.default.create({
            name,
            email,
            password,
            country,
            sector,
            organizationType,
        });
        const verificationToken = crypto_js_1.default.lib.WordArray.random(32).toString(crypto_js_1.default.enc.Hex);
        const hashedToken = crypto_js_1.default.SHA256(verificationToken).toString(crypto_js_1.default.enc.Hex);
        const expires = new Date(Date.now() + 15 * 60 * 1000); // 15 minutes
        // update verification token
        yield userRepository_1.default.sendEmailVerificationToken(user.id, hashedToken, expires);
        const verifyUrl = `${baseUrl}/api/users/verifyemail/${verificationToken}`;
        res.status(201).json({
            success: true,
            data: user,
            message: "user register success",
        });
        setTimeout(() => __awaiter(void 0, void 0, void 0, function* () {
            try {
                yield (0, sendMail_1.sendEmail)(email, "verify your email", (0, emailTemplate_1.emailTemplate)(verifyUrl));
            }
            catch (emailError) {
                console.error(`Failed to send verification email to ${email}:`, emailError);
            }
        }), 0);
    }
    catch (error) {
        return res.status(500).json({
            success: false,
            message: "error creating user",
            error: error instanceof Error ? error.message : "Unexpected error",
        });
    }
});
exports.signup = signup;
const verifyEmail = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const { token } = req.params;
    try {
        const hashedToken = crypto_js_1.default.SHA256(token).toString(crypto_js_1.default.enc.Hex);
        const tokenavailable = yield userRepository_1.default.findByemailVerificationToken(hashedToken);
        if (!tokenavailable) {
            res.redirect(`${clientUrl}/email-verify?status=INVALID`);
            return;
        }
        const expiarystatus = yield userRepository_1.default.isEmailVerificationTokenExpired(hashedToken);
        if (!expiarystatus) {
            res.redirect(`${clientUrl}/email-verify?status=EXPIRED`);
            return;
        }
        yield userRepository_1.default.verifyUser(expiarystatus.id);
        res.redirect(`${clientUrl}/email-verify?status=SUCCESS`);
        return;
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: "error verifying email",
            error: error.message,
        });
        return;
    }
});
exports.verifyEmail = verifyEmail;
// User login
const login = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const { email, password } = req.body;
        // Check if user exists
        const user = yield userRepository_1.default.findByEmail(email);
        if (!user || !user.id) {
            return res.status(400).json({
                success: false,
                message: "user not found",
            });
        }
        // Verify password
        const isPasswordValid = yield userRepository_1.default.verifyPassword(user.id, password);
        if (!isPasswordValid) {
            return res.status(400).json({
                success: false,
                message: "invalid password",
            });
        }
        if (!user.isVerified) {
            return res.status(403).json({
                success: false,
                errorType: "unverified",
                message: "Email is not verified. Please verify your email before logging in.",
            });
        }
        const agent = useragent_1.default.parse(req.headers["user-agent"]);
        const browserInfo = agent.family; // eg: Chrome, brave etc
        const deviceInfo = agent.os.toString(); // OS name and version (e.g., "Mac OS X 10_15_4")
        const ipAddress = (_a = req.ip) !== null && _a !== void 0 ? _a : "unknown-ip";
        const session = yield userRepository_1.default.createSession({
            userId: user.id,
            deviceInfo,
            ipAddress,
            browserInfo,
        });
        // Create JWT token
        const token = jsonwebtoken_1.default.sign({
            id: user.id,
            name: user.name,
            email: user.email,
            sessionId: session.id,
        }, process.env.JWT_SECRET, {
            expiresIn: "24h",
        });
        const options = {
            httpOnly: true,
            secure: process.env.NODE_ENV === "production",
            domain: process.env.DOMAIN_NAME,
        };
        // Set cookie and send response
        return res
            .status(200)
            .cookie("token", token, options)
            .json({ message: "Log in successful.", user });
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: "error logging user",
            error: error.message,
        });
        return;
    }
});
exports.login = login;
const logout = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a, _b;
    try {
        // for deactivating the user session
        const userId = Number((_a = req.user) === null || _a === void 0 ? void 0 : _a.id);
        const sessionId = Number((_b = req.user) === null || _b === void 0 ? void 0 : _b.sessionId);
        yield userRepository_1.default.logoutSingleDevice(userId, sessionId);
        // removing token from cookie
        res
            .status(200)
            .clearCookie("token", {
            httpOnly: true,
            secure: true,
            domain: process.env.DOMAIN_NAME,
        })
            .json(new ApiResponse_1.ApiResponse(200, null, "user logout success"));
        return;
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: "error logging out user",
            error: error.message,
        });
        return;
    }
});
exports.logout = logout;
const logoutFromAllDevice = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        if (!req.user) {
            res.status(404).json({
                success: false,
                message: "user not found",
            });
            return;
        }
        const userId = req.user.id;
        yield userRepository_1.default.logoutAllDevices(userId);
        res.status(200).json({
            success: true,
            message: "logged out from all the devices",
        });
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: "error creating qustion",
            error: error instanceof Error ? error.message : "unexpected error",
        });
        return;
    }
});
exports.logoutFromAllDevice = logoutFromAllDevice;
const updateProfile = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const result = UserValidators_1.updateProfileSchema.safeParse(req.body);
        if (!result.success) {
            res.status(400).json({
                success: false,
                message: "Validation failed",
                error: result.error.flatten(),
            });
            return;
        }
        const { name, country, city, bio, sector, organizationType } = result.data;
        if (!req.user || !req.user.id) {
            res.status(401).json({
                success: false,
                message: "unauthorized : user not found in request",
            });
            return;
        }
        const userId = req.user.id;
        const updateUser = yield userRepository_1.default.updateById(userId, {
            name,
            country,
            city,
            bio,
            sector,
            organizationType,
        });
        res
            .status(200)
            .json(new ApiResponse_1.ApiResponse(200, { updateUser }, "user updated success"));
        return;
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: "error updating profile",
            error: error.message,
        });
        return;
    }
});
exports.updateProfile = updateProfile;
const changePassword = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a, _b, _c;
    try {
        const { currentPassword, newPassword } = req.body;
        if (!req.user || !((_a = req.user) === null || _a === void 0 ? void 0 : _a.id)) {
            res.status(400).json({ success: false, message: "user is not found" });
            return;
        }
        const isPasswordValid = yield userRepository_1.default.verifyPassword((_b = req.user) === null || _b === void 0 ? void 0 : _b.id, currentPassword);
        if (!isPasswordValid) {
            res
                .status(400)
                .json({ success: false, message: "current password doesnot match" });
            return;
        }
        yield userRepository_1.default.updatePassword((_c = req.user) === null || _c === void 0 ? void 0 : _c.id, newPassword);
        res.status(200).json(new ApiResponse_1.ApiResponse(200, "password change success"));
        return;
    }
    catch (error) {
        res.status(500).json({ success: false, error: "server error" });
        return;
    }
});
exports.changePassword = changePassword;
const forgetPassword = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const { email } = req.body;
    try {
        const user = yield userRepository_1.default.findByEmail(email);
        if (!user) {
            res.status(404).json({ message: "user not found" });
            return;
        }
        const resetToken = crypto_js_1.default.lib.WordArray.random(32).toString(crypto_js_1.default.enc.Hex);
        const hashedToken = crypto_js_1.default.SHA256(resetToken).toString(crypto_js_1.default.enc.Hex);
        const tokenExpiry = new Date(Date.now() + 15 * 60 * 1000); // 15 minutes
        yield userRepository_1.default.saveResetToken(user.id, hashedToken, tokenExpiry);
        const resetURL = `${baseUrl}/api/users/resetpasswordtoken/${resetToken}`;
        const message = (0, forgotPasswordEmailTemplate_1.forgetPasswordEmailTemplate)(resetURL);
        yield (0, sendMail_1.sendEmail)(user.email, "Reset password request", message);
        res.status(200).json({
            message: "Password reset email sent",
        });
        return;
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: "error calling api",
            error: error.message,
        });
        return;
    }
});
exports.forgetPassword = forgetPassword;
const checkPasswordResetToken = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const { token } = req.params;
    try {
        const hashedToken = (0, sha256_1.default)(token).toString(crypto_js_1.default.enc.Hex);
        const tokenAvailable = yield userRepository_1.default.checkPasswordToken(hashedToken);
        if (!tokenAvailable) {
            res.redirect(`${clientUrl}/reset-password/change-password?status=INVALID`);
        }
        const expiaryStatus = yield userRepository_1.default.checkPasswordTokenExpirey(hashedToken);
        if (!expiaryStatus) {
            res.redirect(`${clientUrl}/reset-password/change-password?status=EXPIRED`);
            return;
        }
        res.redirect(`${clientUrl}/reset-password/change-password?status=SUCCESS&token=${hashedToken}`);
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: "error creating qustion",
            error: error instanceof Error ? error.message : "unexpected error",
        });
        return;
    }
});
exports.checkPasswordResetToken = checkPasswordResetToken;
const resetPassword = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { newPassword, token } = req.body;
        const user = yield userRepository_1.default.checkPasswordToken(token);
        if (!user) {
            res.status(404).json({
                success: false,
                message: "invalid user token",
            });
            return;
        }
        yield userRepository_1.default.resetUserPassword(user.id, newPassword);
        res.status(200).json({
            success: true,
            message: "password changed success",
        });
        return;
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: "error calling api",
            error: error.message,
        });
    }
});
exports.resetPassword = resetPassword;
const findUserById = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const id = Number(req.params.id);
    try {
        const user = yield userRepository_1.default.findById(id);
        if (!user) {
            res.status(404).json({
                success: false,
                message: "no user found with given id",
            });
            return;
        }
        res
            .status(200)
            .json(new ApiResponse_1.ApiResponse(200, { user }, "user fetched success"));
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: "error calling api",
            error: error.message,
        });
        return;
    }
});
exports.findUserById = findUserById;
const findAllUsers = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const users = yield userRepository_1.default.findAll();
        if (!users) {
            res.status(404).json({
                success: false,
                message: "no user found",
            });
            return;
        }
        res
            .status(200)
            .json(new ApiResponse_1.ApiResponse(200, { users }, "users fetch success"));
        return;
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: "error calling api",
            error: error.message,
        });
    }
});
exports.findAllUsers = findAllUsers;
const userProfile = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const id = Number(req.user.id);
        if (!id) {
            res.status(401).json({
                success: false,
                message: "id not found, unauthenticated",
            });
            return;
        }
        const profile = yield userRepository_1.default.findById(id);
        res
            .status(200)
            .json({ message: "Successfully fetched user profile", profile });
        return;
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: "error calling api",
            error: error.message,
        });
    }
});
exports.userProfile = userProfile;
const sendVerificationEmail = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { email } = yield req.body;
        if (!email) {
            return res
                .status(400)
                .json({ message: "Email is not provided in the request" });
        }
        const user = yield userRepository_1.default.findByEmail(email);
        if (!user) {
            return res
                .status(404)
                .json({ message: "User with the provided email doesn't exist." });
        }
        const verificationToken = crypto_js_1.default.lib.WordArray.random(32).toString(crypto_js_1.default.enc.Hex);
        const hashedToken = crypto_js_1.default.SHA256(verificationToken).toString(crypto_js_1.default.enc.Hex);
        const expires = new Date(Date.now() + 15 * 60 * 1000); // 15 minutes
        // update verification token
        yield userRepository_1.default.sendEmailVerificationToken(user.id, hashedToken, expires);
        const verifyUrl = `${baseUrl}/api/users/verifyemail/${verificationToken}`;
        const message = (0, emailTemplate_1.emailTemplate)(verifyUrl);
        yield (0, sendMail_1.sendEmail)(user.email, "verify your email", message);
        return res
            .status(200)
            .json({ message: "Verification email sent successfully" });
    }
    catch (error) {
        return res.status(500).json({
            message: error instanceof Error
                ? `An error occured while trying to send verification email: ${error.message}`
                : "An unexpected error occured while trying to send verification email",
        });
    }
});
exports.sendVerificationEmail = sendVerificationEmail;
// This function returns the current session information (not from the session table but from the token)
const getUserSession = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const token = req.cookies.token;
    if (!token) {
        return res.status(401).json({ user: null });
    }
    try {
        const jwtSecret = process.env.JWT_SECRET;
        if (!jwtSecret) {
            throw new Error("Jwt Secret is not present in environment variable");
        }
        const decoded = jsonwebtoken_1.default.verify(token, jwtSecret);
        return res.status(200).json({
            id: decoded.id,
            email: decoded.email,
            name: decoded.name,
            sessionId: decoded.sessionId,
        });
    }
    catch (error) {
        return res.status(401).json({ user: null });
    }
});
exports.getUserSession = getUserSession;
const changeEmail = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const id = Number((_a = req.user) === null || _a === void 0 ? void 0 : _a.id);
        if (!id) {
            return res.status(401).json({ message: "Id not found, unauthenticated" });
        }
        const { email } = req.body;
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            return res.status(400).json({ message: "Invalid email format." });
        }
        const existingUser = yield userRepository_1.default.findByEmail(email);
        if (existingUser) {
            return res.status(400).json({
                success: false,
                errorField: "email",
                message: "email already in use",
            });
        }
        const user = yield userRepository_1.default.changeEmail(id, { email });
        return res
            .status(200)
            .json({ message: "Successfully updated email.", user });
    }
    catch (error) {
        return res.status(500).json({
            message: error instanceof Error
                ? `Failed to update email: ${error.message}`
                : "An unexpected error occured while tyring to update email.",
        });
    }
});
exports.changeEmail = changeEmail;
const getUserSessions = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const userId = Number((_a = req.user) === null || _a === void 0 ? void 0 : _a.id);
        if (!userId) {
            return res.status(401).json({ message: "Id not found, unauthenticated" });
        }
        const sessions = yield userRepository_1.default.getUserSessions({ userId });
        return res
            .status(200)
            .json({ message: "Successfully fetched user sessions.", sessions });
    }
    catch (error) {
        return res.status(500).json({
            message: error instanceof Error
                ? `Failed to fetch user sessions ${error.message}`
                : "An unexpected error occured while trying to fetch user sessions",
        });
    }
});
exports.getUserSessions = getUserSessions;
const checkUserByEmail = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { email } = req.body;
        if (!email) {
            return res.status(400).json({
                success: false,
                message: "Email is required",
            });
        }
        const user = yield userRepository_1.default.findByEmail(email);
        if (!user) {
            return res.status(404).json({
                success: false,
                message: "User with this email does not exist",
            });
        }
        return res.status(200).json({
            success: true,
            message: "User exists",
            user: {
                id: user.id,
                email: user.email,
                name: user.name,
            },
        });
    }
    catch (error) {
        return res.status(500).json({
            success: false,
            message: "Error checking user existence",
            error: error instanceof Error ? error.message : "Unexpected error",
        });
    }
});
exports.checkUserByEmail = checkUserByEmail;
