"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4601],{19827:(t,e,i)=>{i.d(e,{l:()=>s});let s=t=>t},32082:(t,e,i)=>{i.d(e,{xQ:()=>r});var s=i(12115),n=i(80845);function r(t=!0){let e=(0,s.useContext)(n.t);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:o,register:a}=e,l=(0,s.useId)();(0,s.useEffect)(()=>{if(t)return a(l)},[t]);let h=(0,s.useCallback)(()=>t&&o&&o(l),[l,o,t]);return!i&&o?[!1,h]:[!0]}},44518:(t,e,i)=>{let s;function n(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function r(t){let e=[{},{}];return t?.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function o(t,e,i,s){if("function"==typeof e){let[n,o]=r(s);e=e(void 0!==i?i:t.custom,n,o)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[n,o]=r(s);e=e(void 0!==i?i:t.custom,n,o)}return e}function a(t,e,i){let s=t.getProps();return o(s,e,void 0!==i?i:s.custom,t)}function l(t,e){return t?.[e]??t?.default??t}i.d(e,{P:()=>rS});var h,u,d=i(19827);let c={skipAnimations:!1,useManualTiming:!1},p=["read","resolveKeyframes","update","preRender","render","postRender"],m={value:null,addProjectionMetrics:null};function f(t,e){let i=!1,s=!0,n={delta:0,timestamp:0,isProcessing:!1},r=()=>i=!0,o=p.reduce((t,i)=>(t[i]=function(t,e){let i=new Set,s=new Set,n=!1,r=!1,o=new WeakSet,a={delta:0,timestamp:0,isProcessing:!1},l=0;function h(e){o.has(e)&&(u.schedule(e),t()),l++,e(a)}let u={schedule:(t,e=!1,r=!1)=>{let a=r&&n?i:s;return e&&o.add(t),a.has(t)||a.add(t),t},cancel:t=>{s.delete(t),o.delete(t)},process:t=>{if(a=t,n){r=!0;return}n=!0,[i,s]=[s,i],i.forEach(h),e&&m.value&&m.value.frameloop[e].push(l),l=0,i.clear(),n=!1,r&&(r=!1,u.process(t))}};return u}(r,e?i:void 0),t),{}),{read:a,resolveKeyframes:l,update:h,preRender:u,render:d,postRender:f}=o,g=()=>{let r=c.useManualTiming?n.timestamp:performance.now();i=!1,c.useManualTiming||(n.delta=s?1e3/60:Math.max(Math.min(r-n.timestamp,40),1)),n.timestamp=r,n.isProcessing=!0,a.process(n),l.process(n),h.process(n),u.process(n),d.process(n),f.process(n),n.isProcessing=!1,i&&e&&(s=!1,t(g))},v=()=>{i=!0,s=!0,n.isProcessing||t(g)};return{schedule:p.reduce((t,e)=>{let s=o[e];return t[e]=(t,e=!1,n=!1)=>(i||v(),s.schedule(t,e,n)),t},{}),cancel:t=>{for(let e=0;e<p.length;e++)o[p[e]].cancel(t)},state:n,steps:o}}let{schedule:g,cancel:v,state:y,steps:x}=f("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:d.l,!0),w=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],P=new Set(w),T=new Set(["width","height","top","left","right","bottom",...w]);function b(t,e){-1===t.indexOf(e)&&t.push(e)}function S(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}class A{constructor(){this.subscriptions=[]}add(t){return b(this.subscriptions,t),()=>S(this.subscriptions,t)}notify(t,e,i){let s=this.subscriptions.length;if(s)if(1===s)this.subscriptions[0](t,e,i);else for(let n=0;n<s;n++){let s=this.subscriptions[n];s&&s(t,e,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function M(){s=void 0}let E={now:()=>(void 0===s&&E.set(y.isProcessing||c.useManualTiming?y.timestamp:performance.now()),s),set:t=>{s=t,queueMicrotask(M)}},V=t=>!isNaN(parseFloat(t)),C={current:void 0};class D{constructor(t,e={}){this.version="12.7.4",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let i=E.now();this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),e&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=E.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=V(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new A);let i=this.events[t].add(e);return"change"===t?()=>{i(),g.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return C.current&&C.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t;let e=E.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return t=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*t:0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function k(t,e){return new D(t,e)}let R=t=>Array.isArray(t),L=t=>!!(t&&"object"==typeof t&&t.mix&&t.toValue),j=t=>R(t)?t[t.length-1]||0:t,F=t=>!!(t&&t.getVelocity);function B(t,e){let i=t.getValue("willChange");if(F(i)&&i.add)return i.add(e);if(!i&&c.WillChange){let i=new c.WillChange("auto");t.addValue("willChange",i),i.add(e)}}let O=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),I="data-"+O("framerAppearId");function U(t){let e;return()=>(void 0===e&&(e=t()),e)}let $=U(()=>void 0!==window.ScrollTimeline);class N{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}get finished(){return Promise.all(this.animations.map(t=>t.finished))}getAll(t){return this.animations[0][t]}setAll(t,e){for(let i=0;i<this.animations.length;i++)this.animations[i][t]=e}attachTimeline(t,e){let i=this.animations.map(i=>$()&&i.attachTimeline?i.attachTimeline(t):"function"==typeof e?e(i):void 0);return()=>{i.forEach((t,e)=>{t&&t(),this.animations[e].stop()})}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let e=0;e<this.animations.length;e++)t=Math.max(t,this.animations[e].duration);return t}runAll(t){this.animations.forEach(e=>e[t]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class W extends N{then(t,e){return this.finished.finally(t).then(()=>{})}}let z=t=>1e3*t,Y=t=>t/1e3,X={current:!1};function H(t){return"function"==typeof t&&"applyToOptions"in t}let K=t=>Array.isArray(t)&&"number"==typeof t[0],q={},G=function(t,e){let i=U(t);return()=>q[e]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),_=([t,e,i,s])=>`cubic-bezier(${t}, ${e}, ${i}, ${s})`,Z={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:_([0,.65,.55,1]),circOut:_([.55,0,1,.45]),backIn:_([.31,.01,.66,-.59]),backOut:_([.33,1.53,.69,.99])},Q={layout:0,mainThread:0,waapi:0},J=(t,e,i=10)=>{let s="",n=Math.max(Math.round(e/i),2);for(let e=0;e<n;e++)s+=t(e/(n-1))+", ";return`linear(${s.substring(0,s.length-2)})`};function tt(t,e){t.timeline=e,t.onfinish=null}var te=i(83945);let ti=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,ts=t=>e=>1-t(1-e),tn=(0,te.A)(.33,1.53,.69,.99),tr=ts(tn),to=ti(tr),ta=t=>(t*=2)<1?.5*tr(t):.5*(2-Math.pow(2,-10*(t-1))),tl=t=>1-Math.sin(Math.acos(t)),th=ts(tl),tu=ti(tl),td=t=>/^0[^.\s]+$/u.test(t),tc=(t,e,i)=>i>e?e:i<t?t:i,tp={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},tm={...tp,transform:t=>tc(0,1,t)},tf={...tp,default:1},tg=t=>Math.round(1e5*t)/1e5,tv=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,ty=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,tx=(t,e)=>i=>!!("string"==typeof i&&ty.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),tw=(t,e,i)=>s=>{if("string"!=typeof s)return s;let[n,r,o,a]=s.match(tv);return{[t]:parseFloat(n),[e]:parseFloat(r),[i]:parseFloat(o),alpha:void 0!==a?parseFloat(a):1}},tP=t=>tc(0,255,t),tT={...tp,transform:t=>Math.round(tP(t))},tb={test:tx("rgb","red"),parse:tw("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:s=1})=>"rgba("+tT.transform(t)+", "+tT.transform(e)+", "+tT.transform(i)+", "+tg(tm.transform(s))+")"},tS={test:tx("#"),parse:function(t){let e="",i="",s="",n="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),s=t.substring(5,7),n=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),s=t.substring(3,4),n=t.substring(4,5),e+=e,i+=i,s+=s,n+=n),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(s,16),alpha:n?parseInt(n,16)/255:1}},transform:tb.transform},tA=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),tM=tA("deg"),tE=tA("%"),tV=tA("px"),tC=tA("vh"),tD=tA("vw"),tk={...tE,parse:t=>tE.parse(t)/100,transform:t=>tE.transform(100*t)},tR={test:tx("hsl","hue"),parse:tw("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:s=1})=>"hsla("+Math.round(t)+", "+tE.transform(tg(e))+", "+tE.transform(tg(i))+", "+tg(tm.transform(s))+")"},tL={test:t=>tb.test(t)||tS.test(t)||tR.test(t),parse:t=>tb.test(t)?tb.parse(t):tR.test(t)?tR.parse(t):tS.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?tb.transform(t):tR.transform(t)},tj=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,tF="number",tB="color",tO=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function tI(t){let e=t.toString(),i=[],s={color:[],number:[],var:[]},n=[],r=0,o=e.replace(tO,t=>(tL.test(t)?(s.color.push(r),n.push(tB),i.push(tL.parse(t))):t.startsWith("var(")?(s.var.push(r),n.push("var"),i.push(t)):(s.number.push(r),n.push(tF),i.push(parseFloat(t))),++r,"${}")).split("${}");return{values:i,split:o,indexes:s,types:n}}function tU(t){return tI(t).values}function t$(t){let{split:e,types:i}=tI(t),s=e.length;return t=>{let n="";for(let r=0;r<s;r++)if(n+=e[r],void 0!==t[r]){let e=i[r];e===tF?n+=tg(t[r]):e===tB?n+=tL.transform(t[r]):n+=t[r]}return n}}let tN=t=>"number"==typeof t?0:t,tW={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(tv)?.length||0)+(t.match(tj)?.length||0)>0},parse:tU,createTransformer:t$,getAnimatableNone:function(t){let e=tU(t);return t$(t)(e.map(tN))}},tz=new Set(["brightness","contrast","saturate","opacity"]);function tY(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[s]=i.match(tv)||[];if(!s)return t;let n=i.replace(s,""),r=+!!tz.has(e);return s!==i&&(r*=100),e+"("+r+n+")"}let tX=/\b([a-z-]*)\(.*?\)/gu,tH={...tW,getAnimatableNone:t=>{let e=t.match(tX);return e?e.map(tY).join(" "):t}},tK={...tp,transform:Math.round},tq={borderWidth:tV,borderTopWidth:tV,borderRightWidth:tV,borderBottomWidth:tV,borderLeftWidth:tV,borderRadius:tV,radius:tV,borderTopLeftRadius:tV,borderTopRightRadius:tV,borderBottomRightRadius:tV,borderBottomLeftRadius:tV,width:tV,maxWidth:tV,height:tV,maxHeight:tV,top:tV,right:tV,bottom:tV,left:tV,padding:tV,paddingTop:tV,paddingRight:tV,paddingBottom:tV,paddingLeft:tV,margin:tV,marginTop:tV,marginRight:tV,marginBottom:tV,marginLeft:tV,backgroundPositionX:tV,backgroundPositionY:tV,rotate:tM,rotateX:tM,rotateY:tM,rotateZ:tM,scale:tf,scaleX:tf,scaleY:tf,scaleZ:tf,skew:tM,skewX:tM,skewY:tM,distance:tV,translateX:tV,translateY:tV,translateZ:tV,x:tV,y:tV,z:tV,perspective:tV,transformPerspective:tV,opacity:tm,originX:tk,originY:tk,originZ:tV,zIndex:tK,size:tV,fillOpacity:tm,strokeOpacity:tm,numOctaves:tK},tG={...tq,color:tL,backgroundColor:tL,outlineColor:tL,fill:tL,stroke:tL,borderColor:tL,borderTopColor:tL,borderRightColor:tL,borderBottomColor:tL,borderLeftColor:tL,filter:tH,WebkitFilter:tH},t_=t=>tG[t];function tZ(t,e){let i=t_(t);return i!==tH&&(i=tW),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let tQ=new Set(["auto","none","0"]),tJ=t=>180*t/Math.PI,t0=t=>t5(tJ(Math.atan2(t[1],t[0]))),t1={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:t0,rotateZ:t0,skewX:t=>tJ(Math.atan(t[1])),skewY:t=>tJ(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},t5=t=>((t%=360)<0&&(t+=360),t),t2=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),t3=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),t9={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:t2,scaleY:t3,scale:t=>(t2(t)+t3(t))/2,rotateX:t=>t5(tJ(Math.atan2(t[6],t[5]))),rotateY:t=>t5(tJ(Math.atan2(-t[2],t[0]))),rotateZ:t0,rotate:t0,skewX:t=>tJ(Math.atan(t[4])),skewY:t=>tJ(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function t8(t){return+!!t.includes("scale")}function t4(t,e){let i,s;if(!t||"none"===t)return t8(e);let n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(n)i=t9,s=n;else{let e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=t1,s=e}if(!s)return t8(e);let r=i[e],o=s[1].split(",").map(t7);return"function"==typeof r?r(o):o[r]}let t6=(t,e)=>{let{transform:i="none"}=getComputedStyle(t);return t4(i,e)};function t7(t){return parseFloat(t.trim())}let et=t=>t===tp||t===tV,ee=new Set(["x","y","z"]),ei=w.filter(t=>!ee.has(t)),es={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>t4(e,"x"),y:(t,{transform:e})=>t4(e,"y")};es.translateX=es.x,es.translateY=es.y;let en=new Set,er=!1,eo=!1;function ea(){if(eo){let t=Array.from(en).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return ei.forEach(i=>{let s=t.getValue(i);void 0!==s&&(e.push([i,s.get()]),s.set(+!!i.startsWith("scale")))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{t.getValue(e)?.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}eo=!1,er=!1,en.forEach(t=>t.complete()),en.clear()}function el(){en.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(eo=!0)})}class eh{constructor(t,e,i,s,n,r=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=s,this.element=n,this.isAsync=r}scheduleResolve(){this.isScheduled=!0,this.isAsync?(en.add(this),er||(er=!0,g.read(el),g.resolveKeyframes(ea))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:s}=this;for(let n=0;n<t.length;n++)if(null===t[n])if(0===n){let n=s?.get(),r=t[t.length-1];if(void 0!==n)t[0]=n;else if(i&&e){let s=i.readValue(e,r);null!=s&&(t[0]=s)}void 0===t[0]&&(t[0]=r),s&&void 0===n&&s.set(t[0])}else t[n]=t[n-1]}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),en.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,en.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}let eu=()=>{},ed=()=>{},ec=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),ep=t=>e=>"string"==typeof e&&e.startsWith(t),em=ep("--"),ef=ep("var(--"),eg=t=>!!ef(t)&&ev.test(t.split("/*")[0].trim()),ev=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,ey=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,ex=t=>e=>e.test(t),ew=[tp,tV,tE,tM,tD,tC,{test:t=>"auto"===t,parse:t=>t}],eP=t=>ew.find(ex(t));class eT extends eh{constructor(t,e,i,s,n){super(t,e,i,s,n,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let s=t[i];if("string"==typeof s&&eg(s=s.trim())){let n=function t(e,i,s=1){ed(s<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[n,r]=function(t){let e=ey.exec(t);if(!e)return[,];let[,i,s,n]=e;return[`--${i??s}`,n]}(e);if(!n)return;let o=window.getComputedStyle(i).getPropertyValue(n);if(o){let t=o.trim();return ec(t)?parseFloat(t):t}return eg(r)?t(r,i,s+1):r}(s,e.current);void 0!==n&&(t[i]=n),i===t.length-1&&(this.finalKeyframe=s)}}if(this.resolveNoneKeyframes(),!T.has(i)||2!==t.length)return;let[s,n]=t,r=eP(s),o=eP(n);if(r!==o)if(et(r)&&et(o))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else this.needsMeasurement=!0}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var s;("number"==typeof(s=t[e])?0===s:null===s||"none"===s||"0"===s||td(s))&&i.push(e)}i.length&&function(t,e,i){let s,n=0;for(;n<t.length&&!s;){let e=t[n];"string"==typeof e&&!tQ.has(e)&&tI(e).values.length&&(s=t[n]),n++}if(s&&i)for(let n of e)t[n]=tZ(i,s)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=es[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let s=e[e.length-1];void 0!==s&&t.getValue(i,s).jump(s,!1)}measureEndState(){let{element:t,name:e,unresolvedKeyframes:i}=this;if(!t||!t.current)return;let s=t.getValue(e);s&&s.jump(this.measuredOrigin,!1);let n=i.length-1,r=i[n];i[n]=es[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==r&&void 0===this.finalKeyframe&&(this.finalKeyframe=r),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,i])=>{t.getValue(e).set(i)}),this.resolveNoneKeyframes()}}let eb=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(tW.test(t)||"0"===t)&&!t.startsWith("url(")),eS=t=>null!==t;function eA(t,{repeat:e,repeatType:i="loop"},s){let n=t.filter(eS),r=e&&"loop"!==i&&e%2==1?0:n.length-1;return r&&void 0!==s?s:n[r]}class eM{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:s=0,repeatDelay:n=0,repeatType:r="loop",...o}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=E.now(),this.options={autoplay:t,delay:e,type:i,repeat:s,repeatDelay:n,repeatType:r,...o},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt}get resolved(){return this._resolved||this.hasAttemptedResolve||(el(),ea()),this._resolved}onKeyframesResolved(t,e){this.resolvedAt=E.now(),this.hasAttemptedResolve=!0;let{name:i,type:s,velocity:n,delay:r,onComplete:o,onUpdate:a,isGenerator:l}=this.options;if(!l&&!function(t,e,i,s){let n=t[0];if(null===n)return!1;if("display"===e||"visibility"===e)return!0;let r=t[t.length-1],o=eb(n,e),a=eb(r,e);return eu(o===a,`You are trying to animate ${e} from "${n}" to "${r}". ${n} is not an animatable value - to enable this animation set ${n} to a value animatable to ${r} via the \`style\` property.`),!!o&&!!a&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||H(i))&&s)}(t,i,s,n))if(X.current||!r){a&&a(eA(t,this.options,e)),o&&o(),this.resolveFinishedPromise();return}else this.options.duration=0;let h=this.initPlayback(t,e);!1!==h&&(this._resolved={keyframes:t,finalKeyframe:e,...h},this.onPostResolved())}onPostResolved(){}then(t,e){return this.currentFinishedPromise.then(t,e)}flatten(){this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear")}updateFinishedPromise(){this.currentFinishedPromise=new Promise(t=>{this.resolveFinishedPromise=t})}}function eE(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}let eV=(t,e,i)=>t+(e-t)*i;function eC(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function eD(t,e){return i=>i>0?e:t}let ek=(t,e,i)=>{let s=t*t,n=i*(e*e-s)+s;return n<0?0:Math.sqrt(n)},eR=[tS,tb,tR],eL=t=>eR.find(e=>e.test(t));function ej(t){let e=eL(t);if(eu(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`),!e)return!1;let i=e.parse(t);return e===tR&&(i=function({hue:t,saturation:e,lightness:i,alpha:s}){t/=360,i/=100;let n=0,r=0,o=0;if(e/=100){let s=i<.5?i*(1+e):i+e-i*e,a=2*i-s;n=eC(a,s,t+1/3),r=eC(a,s,t),o=eC(a,s,t-1/3)}else n=r=o=i;return{red:Math.round(255*n),green:Math.round(255*r),blue:Math.round(255*o),alpha:s}}(i)),i}let eF=(t,e)=>{let i=ej(t),s=ej(e);if(!i||!s)return eD(t,e);let n={...i};return t=>(n.red=ek(i.red,s.red,t),n.green=ek(i.green,s.green,t),n.blue=ek(i.blue,s.blue,t),n.alpha=eV(i.alpha,s.alpha,t),tb.transform(n))},eB=(t,e)=>i=>e(t(i)),eO=(...t)=>t.reduce(eB),eI=new Set(["none","hidden"]);function eU(t,e){return i=>eV(t,e,i)}function e$(t){return"number"==typeof t?eU:"string"==typeof t?eg(t)?eD:tL.test(t)?eF:ez:Array.isArray(t)?eN:"object"==typeof t?tL.test(t)?eF:eW:eD}function eN(t,e){let i=[...t],s=i.length,n=t.map((t,i)=>e$(t)(t,e[i]));return t=>{for(let e=0;e<s;e++)i[e]=n[e](t);return i}}function eW(t,e){let i={...t,...e},s={};for(let n in i)void 0!==t[n]&&void 0!==e[n]&&(s[n]=e$(t[n])(t[n],e[n]));return t=>{for(let e in s)i[e]=s[e](t);return i}}let ez=(t,e)=>{let i=tW.createTransformer(e),s=tI(t),n=tI(e);return s.indexes.var.length===n.indexes.var.length&&s.indexes.color.length===n.indexes.color.length&&s.indexes.number.length>=n.indexes.number.length?eI.has(t)&&!n.values.length||eI.has(e)&&!s.values.length?function(t,e){return eI.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):eO(eN(function(t,e){let i=[],s={color:0,var:0,number:0};for(let n=0;n<e.values.length;n++){let r=e.types[n],o=t.indexes[r][s[r]],a=t.values[o]??0;i[n]=a,s[r]++}return i}(s,n),n.values),i):(eu(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),eD(t,e))};function eY(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?eV(t,e,i):e$(t)(t,e)}function eX(t,e,i){var s,n;let r=Math.max(e-5,0);return s=i-t(r),(n=e-r)?1e3/n*s:0}let eH={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function eK(t,e){return t*Math.sqrt(1-e*e)}let eq=["duration","bounce"],eG=["stiffness","damping","mass"];function e_(t,e){return e.some(e=>void 0!==t[e])}function eZ(t=eH.visualDuration,e=eH.bounce){let i,s="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:n,restDelta:r}=s,o=s.keyframes[0],a=s.keyframes[s.keyframes.length-1],l={done:!1,value:o},{stiffness:h,damping:u,mass:d,duration:c,velocity:p,isResolvedFromDuration:m}=function(t){let e={velocity:eH.velocity,stiffness:eH.stiffness,damping:eH.damping,mass:eH.mass,isResolvedFromDuration:!1,...t};if(!e_(t,eG)&&e_(t,eq))if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),s=i*i,n=2*tc(.05,1,1-(t.bounce||0))*Math.sqrt(s);e={...e,mass:eH.mass,stiffness:s,damping:n}}else{let i=function({duration:t=eH.duration,bounce:e=eH.bounce,velocity:i=eH.velocity,mass:s=eH.mass}){let n,r;eu(t<=z(eH.maxDuration),"Spring duration must be 10 seconds or less");let o=1-e;o=tc(eH.minDamping,eH.maxDamping,o),t=tc(eH.minDuration,eH.maxDuration,Y(t)),o<1?(n=e=>{let s=e*o,n=s*t;return .001-(s-i)/eK(e,o)*Math.exp(-n)},r=e=>{let s=e*o*t,r=Math.pow(o,2)*Math.pow(e,2)*t,a=Math.exp(-s),l=eK(Math.pow(e,2),o);return(s*i+i-r)*a*(-n(e)+.001>0?-1:1)/l}):(n=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),r=e=>t*t*(i-e)*Math.exp(-e*t));let a=function(t,e,i){let s=i;for(let i=1;i<12;i++)s-=t(s)/e(s);return s}(n,r,5/t);if(t=z(t),isNaN(a))return{stiffness:eH.stiffness,damping:eH.damping,duration:t};{let e=Math.pow(a,2)*s;return{stiffness:e,damping:2*o*Math.sqrt(s*e),duration:t}}}(t);(e={...e,...i,mass:eH.mass}).isResolvedFromDuration=!0}return e}({...s,velocity:-Y(s.velocity||0)}),f=p||0,g=u/(2*Math.sqrt(h*d)),v=a-o,y=Y(Math.sqrt(h/d)),x=5>Math.abs(v);if(n||(n=x?eH.restSpeed.granular:eH.restSpeed.default),r||(r=x?eH.restDelta.granular:eH.restDelta.default),g<1){let t=eK(y,g);i=e=>a-Math.exp(-g*y*e)*((f+g*y*v)/t*Math.sin(t*e)+v*Math.cos(t*e))}else if(1===g)i=t=>a-Math.exp(-y*t)*(v+(f+y*v)*t);else{let t=y*Math.sqrt(g*g-1);i=e=>{let i=Math.exp(-g*y*e),s=Math.min(t*e,300);return a-i*((f+g*y*v)*Math.sinh(s)+t*v*Math.cosh(s))/t}}let w={calculatedDuration:m&&c||null,next:t=>{let e=i(t);if(m)l.done=t>=c;else{let s=0;g<1&&(s=0===t?z(f):eX(i,t,e));let o=Math.abs(a-e)<=r;l.done=Math.abs(s)<=n&&o}return l.value=l.done?a:e,l},toString:()=>{let t=Math.min(eE(w),2e4),e=J(e=>w.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return w}function eQ({keyframes:t,velocity:e=0,power:i=.8,timeConstant:s=325,bounceDamping:n=10,bounceStiffness:r=500,modifyTarget:o,min:a,max:l,restDelta:h=.5,restSpeed:u}){let d,c,p=t[0],m={done:!1,value:p},f=t=>void 0!==a&&t<a||void 0!==l&&t>l,g=t=>void 0===a?l:void 0===l||Math.abs(a-t)<Math.abs(l-t)?a:l,v=i*e,y=p+v,x=void 0===o?y:o(y);x!==y&&(v=x-p);let w=t=>-v*Math.exp(-t/s),P=t=>x+w(t),T=t=>{let e=w(t),i=P(t);m.done=Math.abs(e)<=h,m.value=m.done?x:i},b=t=>{f(m.value)&&(d=t,c=eZ({keyframes:[m.value,g(m.value)],velocity:eX(P,t,m.value),damping:n,stiffness:r,restDelta:h,restSpeed:u}))};return b(0),{calculatedDuration:null,next:t=>{let e=!1;return(c||void 0!==d||(e=!0,T(t),b(t)),void 0!==d&&t>=d)?c.next(t-d):(e||T(t),m)}}}eZ.applyToOptions=t=>{let e=function(t,e=100,i){let s=i({...t,keyframes:[0,e]}),n=Math.min(eE(s),2e4);return{type:"keyframes",ease:t=>s.next(n*t).value/e,duration:Y(n)}}(t,100,eZ);return t.ease=G()?e.ease:"easeOut",t.duration=z(e.duration),t.type="keyframes",t};var eJ=i(95233);let e0=t=>Array.isArray(t)&&"number"!=typeof t[0],e1={linear:d.l,easeIn:eJ.a6,easeInOut:eJ.am,easeOut:eJ.vT,circIn:tl,circInOut:tu,circOut:th,backIn:tr,backInOut:to,backOut:tn,anticipate:ta},e5=t=>{if(K(t)){ed(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,i,s,n]=t;return(0,te.A)(e,i,s,n)}return"string"==typeof t?(ed(void 0!==e1[t],`Invalid easing type '${t}'`),e1[t]):t},e2=(t,e,i)=>{let s=e-t;return 0===s?1:(i-t)/s};function e3({duration:t=300,keyframes:e,times:i,ease:s="easeInOut"}){var n;let r=e0(s)?s.map(e5):e5(s),o={done:!1,value:e[0]},a=function(t,e,{clamp:i=!0,ease:s,mixer:n}={}){let r=t.length;if(ed(r===e.length,"Both input and output ranges must be the same length"),1===r)return()=>e[0];if(2===r&&e[0]===e[1])return()=>e[1];let o=t[0]===t[1];t[0]>t[r-1]&&(t=[...t].reverse(),e=[...e].reverse());let a=function(t,e,i){let s=[],n=i||eY,r=t.length-1;for(let i=0;i<r;i++){let r=n(t[i],t[i+1]);e&&(r=eO(Array.isArray(e)?e[i]||d.l:e,r)),s.push(r)}return s}(e,s,n),l=a.length,h=i=>{if(o&&i<t[0])return e[0];let s=0;if(l>1)for(;s<t.length-2&&!(i<t[s+1]);s++);let n=e2(t[s],t[s+1],i);return a[s](n)};return i?e=>h(tc(t[0],t[r-1],e)):h}((n=i&&i.length===e.length?i:function(t){let e=[0];return!function(t,e){let i=t[t.length-1];for(let s=1;s<=e;s++){let n=e2(0,e,s);t.push(eV(i,1,n))}}(e,t.length-1),e}(e),n.map(e=>e*t)),e,{ease:Array.isArray(r)?r:e.map(()=>r||eJ.am).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(o.value=a(e),o.done=e>=t,o)}}let e9=t=>{let e=({timestamp:e})=>t(e);return{start:()=>g.update(e,!0),stop:()=>v(e),now:()=>y.isProcessing?y.timestamp:E.now()}},e8={decay:eQ,inertia:eQ,tween:e3,keyframes:e3,spring:eZ},e4=t=>t/100;class e6 extends eM{constructor(t){super(t),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.teardown();let{onStop:t}=this.options;t&&t()};let{name:e,motionValue:i,element:s,keyframes:n}=this.options,r=s?.KeyframeResolver||eh;this.resolver=new r(n,(t,e)=>this.onKeyframesResolved(t,e),e,i,s),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(t){let e,i,{type:s="keyframes",repeat:n=0,repeatDelay:r=0,repeatType:o,velocity:a=0}=this.options,l=H(s)?s:e8[s]||e3;l!==e3&&"number"!=typeof t[0]&&(e=eO(e4,eY(t[0],t[1])),t=[0,100]);let h=l({...this.options,keyframes:t});"mirror"===o&&(i=l({...this.options,keyframes:[...t].reverse(),velocity:-a})),null===h.calculatedDuration&&(h.calculatedDuration=eE(h));let{calculatedDuration:u}=h,d=u+r;return{generator:h,mirroredGenerator:i,mapPercentToKeyframes:e,calculatedDuration:u,resolvedDuration:d,totalDuration:d*(n+1)-r}}onPostResolved(){let{autoplay:t=!0}=this.options;Q.mainThread++,this.play(),"paused"!==this.pendingPlayState&&t?this.state=this.pendingPlayState:this.pause()}tick(t,e=!1){let{resolved:i}=this;if(!i){let{keyframes:t}=this.options;return{done:!0,value:t[t.length-1]}}let{finalKeyframe:s,generator:n,mirroredGenerator:r,mapPercentToKeyframes:o,keyframes:a,calculatedDuration:l,totalDuration:h,resolvedDuration:u}=i;if(null===this.startTime)return n.next(0);let{delay:d,repeat:c,repeatType:p,repeatDelay:m,onUpdate:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-h/this.speed,this.startTime)),e?this.currentTime=t:null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=Math.round(t-this.startTime)*this.speed;let g=this.currentTime-d*(this.speed>=0?1:-1),v=this.speed>=0?g<0:g>h;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=h);let y=this.currentTime,x=n;if(c){let t=Math.min(this.currentTime,h)/u,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,c+1))%2&&("reverse"===p?(i=1-i,m&&(i-=m/u)):"mirror"===p&&(x=r)),y=tc(0,1,i)*u}let w=v?{done:!1,value:a[0]}:x.next(y);o&&(w.value=o(w.value));let{done:P}=w;v||null===l||(P=this.speed>=0?this.currentTime>=h:this.currentTime<=0);let T=null===this.holdTime&&("finished"===this.state||"running"===this.state&&P);return T&&void 0!==s&&(w.value=eA(a,this.options,s)),f&&f(w.value),T&&this.finish(),w}get duration(){let{resolved:t}=this;return t?Y(t.calculatedDuration):0}get time(){return Y(this.currentTime)}set time(t){t=z(t),this.currentTime=t,null!==this.holdTime||0===this.speed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.speed)}get speed(){return this.playbackSpeed}set speed(t){let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=Y(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;let{driver:t=e9,onPlay:e,startTime:i}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),e&&e();let s=this.driver.now();null!==this.holdTime?this.startTime=s-this.holdTime:this.startTime?"finished"===this.state&&(this.startTime=s):this.startTime=i??this.calcStartTime(),"finished"===this.state&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=this.currentTime??0}complete(){"running"!==this.state&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";let{onComplete:t}=this.options;t&&t()}cancel(){null!==this.cancelTime&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel(),Q.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}get finished(){return this.currentFinishedPromise}}let e7=new Set(["opacity","clipPath","filter","transform"]),it=U(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),ie={anticipate:ta,backInOut:to,circInOut:tu};class ii extends eM{constructor(t){super(t);let{name:e,motionValue:i,element:s,keyframes:n}=this.options;this.resolver=new eT(n,(t,e)=>this.onKeyframesResolved(t,e),e,i,s),this.resolver.scheduleResolve()}initPlayback(t,e){var i;let{duration:s=300,times:n,ease:r,type:o,motionValue:a,name:l,startTime:h}=this.options;if(!a.owner||!a.owner.current)return!1;if("string"==typeof r&&G()&&r in ie&&(r=ie[r]),H((i=this.options).type)||"spring"===i.type||!function t(e){return!!("function"==typeof e&&G()||!e||"string"==typeof e&&(e in Z||G())||K(e)||Array.isArray(e)&&e.every(t))}(i.ease)){let{onComplete:e,onUpdate:i,motionValue:a,element:l,...h}=this.options,u=function(t,e){let i=new e6({...e,keyframes:t,repeat:0,delay:0,isGenerator:!0}),s={done:!1,value:t[0]},n=[],r=0;for(;!s.done&&r<2e4;)n.push((s=i.sample(r)).value),r+=10;return{times:void 0,keyframes:n,duration:r-10,ease:"linear"}}(t,h);1===(t=u.keyframes).length&&(t[1]=t[0]),s=u.duration,n=u.times,r=u.ease,o="keyframes"}let u=function(t,e,i,{delay:s=0,duration:n=300,repeat:r=0,repeatType:o="loop",ease:a="easeInOut",times:l}={},h){let u={[e]:i};l&&(u.offset=l);let d=function t(e,i){if(e)return"function"==typeof e&&G()?J(e,i):K(e)?_(e):Array.isArray(e)?e.map(e=>t(e,i)||Z.easeOut):Z[e]}(a,n);Array.isArray(d)&&(u.easing=d),m.value&&Q.waapi++;let c=t.animate(u,{delay:s,duration:n,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:r+1,direction:"reverse"===o?"alternate":"normal",pseudoElement:void 0});return m.value&&c.finished.finally(()=>{Q.waapi--}),c}(a.owner.current,l,t,{...this.options,duration:s,times:n,ease:r});return u.startTime=h??this.calcStartTime(),this.pendingTimeline?(tt(u,this.pendingTimeline),this.pendingTimeline=void 0):u.onfinish=()=>{let{onComplete:i}=this.options;a.set(eA(t,this.options,e)),i&&i(),this.cancel(),this.resolveFinishedPromise()},{animation:u,duration:s,times:n,type:o,ease:r,keyframes:t}}get duration(){let{resolved:t}=this;if(!t)return 0;let{duration:e}=t;return Y(e)}get time(){let{resolved:t}=this;if(!t)return 0;let{animation:e}=t;return Y(e.currentTime||0)}set time(t){let{resolved:e}=this;if(!e)return;let{animation:i}=e;i.currentTime=z(t)}get speed(){let{resolved:t}=this;if(!t)return 1;let{animation:e}=t;return e.playbackRate}get finished(){return this.resolved.animation.finished}set speed(t){let{resolved:e}=this;if(!e)return;let{animation:i}=e;i.playbackRate=t}get state(){let{resolved:t}=this;if(!t)return"idle";let{animation:e}=t;return e.playState}get startTime(){let{resolved:t}=this;if(!t)return null;let{animation:e}=t;return e.startTime}attachTimeline(t){if(this._resolved){let{resolved:e}=this;if(!e)return d.l;let{animation:i}=e;tt(i,t)}else this.pendingTimeline=t;return d.l}play(){if(this.isStopped)return;let{resolved:t}=this;if(!t)return;let{animation:e}=t;"finished"===e.playState&&this.updateFinishedPromise(),e.play()}pause(){let{resolved:t}=this;if(!t)return;let{animation:e}=t;e.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.resolveFinishedPromise(),this.updateFinishedPromise();let{resolved:t}=this;if(!t)return;let{animation:e,keyframes:i,duration:s,type:n,ease:r,times:o}=t;if("idle"===e.playState||"finished"===e.playState)return;if(this.time){let{motionValue:t,onUpdate:e,onComplete:a,element:l,...h}=this.options,u=new e6({...h,keyframes:i,duration:s,type:n,ease:r,times:o,isGenerator:!0}),d=z(this.time);t.setWithVelocity(u.sample(d-10).value,u.sample(d).value,10)}let{onStop:a}=this.options;a&&a(),this.cancel()}complete(){let{resolved:t}=this;t&&t.animation.finish()}cancel(){let{resolved:t}=this;t&&t.animation.cancel()}static supports(t){let{motionValue:e,name:i,repeatDelay:s,repeatType:n,damping:r,type:o}=t;if(!e||!e.owner||!(e.owner.current instanceof HTMLElement))return!1;let{onUpdate:a,transformTemplate:l}=e.owner.getProps();return it()&&i&&e7.has(i)&&("transform"!==i||!l)&&!a&&!s&&"mirror"!==n&&0!==r&&"inertia"!==o}}let is={type:"spring",stiffness:500,damping:25,restSpeed:10},ir=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),io={type:"keyframes",duration:.8},ia={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},il=(t,{keyframes:e})=>e.length>2?io:P.has(t)?t.startsWith("scale")?ir(e[1]):is:ia,ih=(t,e,i,s={},n,r)=>o=>{let a=l(s,t)||{},h=a.delay||s.delay||0,{elapsed:u=0}=s;u-=z(h);let d={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-u,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{o(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:r?void 0:n};!function({when:t,delay:e,delayChildren:i,staggerChildren:s,staggerDirection:n,repeat:r,repeatType:o,repeatDelay:a,from:l,elapsed:h,...u}){return!!Object.keys(u).length}(a)&&(d={...d,...il(t,d)}),d.duration&&(d.duration=z(d.duration)),d.repeatDelay&&(d.repeatDelay=z(d.repeatDelay)),void 0!==d.from&&(d.keyframes[0]=d.from);let p=!1;if(!1!==d.type&&(0!==d.duration||d.repeatDelay)||(d.duration=0,0===d.delay&&(p=!0)),(X.current||c.skipAnimations)&&(p=!0,d.duration=0,d.delay=0),d.allowFlatten=!a.type&&!a.ease,p&&!r&&void 0!==e.get()){let t=eA(d.keyframes,a);if(void 0!==t)return g.update(()=>{d.onUpdate(t),d.onComplete()}),new W([])}return!r&&ii.supports(d)?new ii(d):new e6(d)};function iu(t,e,{delay:i=0,transitionOverride:s,type:n}={}){let{transition:r=t.getDefaultTransition(),transitionEnd:o,...h}=e;s&&(r=s);let u=[],d=n&&t.animationState&&t.animationState.getState()[n];for(let e in h){let s=t.getValue(e,t.latestValues[e]??null),n=h[e];if(void 0===n||d&&function({protectedKeys:t,needsAnimating:e},i){let s=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,s}(d,e))continue;let o={delay:i,...l(r||{},e)},a=!1;if(window.MotionHandoffAnimation){let i=t.props[I];if(i){let t=window.MotionHandoffAnimation(i,e,g);null!==t&&(o.startTime=t,a=!0)}}B(t,e),s.start(ih(e,s,n,t.shouldReduceMotion&&T.has(e)?{type:!1}:o,t,a));let c=s.animation;c&&u.push(c)}return o&&Promise.all(u).then(()=>{g.update(()=>{o&&function(t,e){let{transitionEnd:i={},transition:s={},...n}=a(t,e)||{};for(let e in n={...n,...i}){let i=j(n[e]);t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,k(i))}}(t,o)})}),u}function id(t,e,i={}){let s=a(t,e,"exit"===i.type?t.presenceContext?.custom:void 0),{transition:n=t.getDefaultTransition()||{}}=s||{};i.transitionOverride&&(n=i.transitionOverride);let r=s?()=>Promise.all(iu(t,s,i)):()=>Promise.resolve(),o=t.variantChildren&&t.variantChildren.size?(s=0)=>{let{delayChildren:r=0,staggerChildren:o,staggerDirection:a}=n;return function(t,e,i=0,s=0,n=1,r){let o=[],a=(t.variantChildren.size-1)*s,l=1===n?(t=0)=>t*s:(t=0)=>a-t*s;return Array.from(t.variantChildren).sort(ic).forEach((t,s)=>{t.notify("AnimationStart",e),o.push(id(t,e,{...r,delay:i+l(s)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(o)}(t,e,r+s,o,a,i)}:()=>Promise.resolve(),{when:l}=n;if(!l)return Promise.all([r(),o(i.delay)]);{let[t,e]="beforeChildren"===l?[r,o]:[o,r];return t().then(()=>e())}}function ic(t,e){return t.sortNodePosition(e)}function ip(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let s=0;s<i;s++)if(e[s]!==t[s])return!1;return!0}function im(t){return"string"==typeof t||Array.isArray(t)}let ig=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],iv=["initial",...ig],iy=iv.length,ix=[...ig].reverse(),iw=ig.length;function iP(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function iT(){return{animate:iP(!0),whileInView:iP(),whileHover:iP(),whileTap:iP(),whileDrag:iP(),whileFocus:iP(),exit:iP()}}class ib{constructor(t){this.isMounted=!1,this.node=t}update(){}}class iS extends ib{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let s;if(t.notify("AnimationStart",e),Array.isArray(e))s=Promise.all(e.map(e=>id(t,e,i)));else if("string"==typeof e)s=id(t,e,i);else{let n="function"==typeof e?a(t,e,i.custom):e;s=Promise.all(iu(t,n,i))}return s.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=iT(),s=!0,r=e=>(i,s)=>{let n=a(t,s,"exit"===e?t.presenceContext?.custom:void 0);if(n){let{transition:t,transitionEnd:e,...s}=n;i={...i,...s,...e}}return i};function o(o){let{props:l}=t,h=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<iy;t++){let s=iv[t],n=e.props[s];(im(n)||!1===n)&&(i[s]=n)}return i}(t.parent)||{},u=[],d=new Set,c={},p=1/0;for(let e=0;e<iw;e++){var m,f;let a=ix[e],g=i[a],v=void 0!==l[a]?l[a]:h[a],y=im(v),x=a===o?g.isActive:null;!1===x&&(p=e);let w=v===h[a]&&v!==l[a]&&y;if(w&&s&&t.manuallyAnimateOnMount&&(w=!1),g.protectedKeys={...c},!g.isActive&&null===x||!v&&!g.prevProp||n(v)||"boolean"==typeof v)continue;let P=(m=g.prevProp,"string"==typeof(f=v)?f!==m:!!Array.isArray(f)&&!ip(f,m)),T=P||a===o&&g.isActive&&!w&&y||e>p&&y,b=!1,S=Array.isArray(v)?v:[v],A=S.reduce(r(a),{});!1===x&&(A={});let{prevResolvedValues:M={}}=g,E={...M,...A},V=e=>{T=!0,d.has(e)&&(b=!0,d.delete(e)),g.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in E){let e=A[t],i=M[t];if(c.hasOwnProperty(t))continue;let s=!1;(R(e)&&R(i)?ip(e,i):e===i)?void 0!==e&&d.has(t)?V(t):g.protectedKeys[t]=!0:null!=e?V(t):d.add(t)}g.prevProp=v,g.prevResolvedValues=A,g.isActive&&(c={...c,...A}),s&&t.blockInitialAnimation&&(T=!1);let C=!(w&&P)||b;T&&C&&u.push(...S.map(t=>({animation:t,options:{type:a}})))}if(d.size){let e={};if("boolean"!=typeof l.initial){let i=a(t,Array.isArray(l.initial)?l.initial[0]:l.initial);i&&i.transition&&(e.transition=i.transition)}d.forEach(i=>{let s=t.getBaseTarget(i),n=t.getValue(i);n&&(n.liveStyle=!0),e[i]=s??null}),u.push({animation:e})}let g=!!u.length;return s&&(!1===l.initial||l.initial===l.animate)&&!t.manuallyAnimateOnMount&&(g=!1),s=!1,g?e(u):Promise.resolve()}return{animateChanges:o,setActive:function(e,s){if(i[e].isActive===s)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,s)),i[e].isActive=s;let n=o(e);for(let t in i)i[t].protectedKeys={};return n},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=iT(),s=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();n(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let iA=0;class iM extends ib{constructor(){super(...arguments),this.id=iA++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let s=this.node.animationState.setActive("exit",!t);e&&!t&&s.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}let iE={x:!1,y:!1};function iV(t,e,i,s={passive:!0}){return t.addEventListener(e,i,s),()=>t.removeEventListener(e,i)}let iC=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function iD(t){return{point:{x:t.pageX,y:t.pageY}}}let ik=t=>e=>iC(e)&&t(e,iD(e));function iR(t,e,i,s){return iV(t,e,ik(i),s)}function iL({top:t,left:e,right:i,bottom:s}){return{x:{min:e,max:i},y:{min:t,max:s}}}function ij(t){return t.max-t.min}function iF(t,e,i,s=.5){t.origin=s,t.originPoint=eV(e.min,e.max,t.origin),t.scale=ij(i)/ij(e),t.translate=eV(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function iB(t,e,i,s){iF(t.x,e.x,i.x,s?s.originX:void 0),iF(t.y,e.y,i.y,s?s.originY:void 0)}function iO(t,e,i){t.min=i.min+e.min,t.max=t.min+ij(e)}function iI(t,e,i){t.min=e.min-i.min,t.max=t.min+ij(e)}function iU(t,e,i){iI(t.x,e.x,i.x),iI(t.y,e.y,i.y)}let i$=()=>({translate:0,scale:1,origin:0,originPoint:0}),iN=()=>({x:i$(),y:i$()}),iW=()=>({min:0,max:0}),iz=()=>({x:iW(),y:iW()});function iY(t){return[t("x"),t("y")]}function iX(t){return void 0===t||1===t}function iH({scale:t,scaleX:e,scaleY:i}){return!iX(t)||!iX(e)||!iX(i)}function iK(t){return iH(t)||iq(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function iq(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function iG(t,e,i,s,n){return void 0!==n&&(t=s+n*(t-s)),s+i*(t-s)+e}function i_(t,e=0,i=1,s,n){t.min=iG(t.min,e,i,s,n),t.max=iG(t.max,e,i,s,n)}function iZ(t,{x:e,y:i}){i_(t.x,e.translate,e.scale,e.originPoint),i_(t.y,i.translate,i.scale,i.originPoint)}function iQ(t,e){t.min=t.min+e,t.max=t.max+e}function iJ(t,e,i,s,n=.5){let r=eV(t.min,t.max,n);i_(t,e,i,r,s)}function i0(t,e){iJ(t.x,e.x,e.scaleX,e.scale,e.originX),iJ(t.y,e.y,e.scaleY,e.scale,e.originY)}function i1(t,e){return iL(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),s=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:s.y,right:s.x}}(t.getBoundingClientRect(),e))}let i5=({current:t})=>t?t.ownerDocument.defaultView:null;function i2(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}let i3=(t,e)=>Math.abs(t-e);class i9{constructor(t,e,{transformPagePoint:i,contextWindow:s,dragSnapToOrigin:n=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=i6(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(i3(t.x,e.x)**2+i3(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!i)return;let{point:s}=t,{timestamp:n}=y;this.history.push({...s,timestamp:n});let{onStart:r,onMove:o}=this.handlers;e||(r&&r(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=i8(e,this.transformPagePoint),g.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:s,resumeAnimation:n}=this.handlers;if(this.dragSnapToOrigin&&n&&n(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let r=i6("pointercancel"===t.type?this.lastMoveEventInfo:i8(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,r),s&&s(t,r)},!iC(t))return;this.dragSnapToOrigin=n,this.handlers=e,this.transformPagePoint=i,this.contextWindow=s||window;let r=i8(iD(t),this.transformPagePoint),{point:o}=r,{timestamp:a}=y;this.history=[{...o,timestamp:a}];let{onSessionStart:l}=e;l&&l(t,i6(r,this.history)),this.removeListeners=eO(iR(this.contextWindow,"pointermove",this.handlePointerMove),iR(this.contextWindow,"pointerup",this.handlePointerUp),iR(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),v(this.updatePoint)}}function i8(t,e){return e?{point:e(t.point)}:t}function i4(t,e){return{x:t.x-e.x,y:t.y-e.y}}function i6({point:t},e){return{point:t,delta:i4(t,i7(e)),offset:i4(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,s=null,n=i7(t);for(;i>=0&&(s=t[i],!(n.timestamp-s.timestamp>z(.1)));)i--;if(!s)return{x:0,y:0};let r=Y(n.timestamp-s.timestamp);if(0===r)return{x:0,y:0};let o={x:(n.x-s.x)/r,y:(n.y-s.y)/r};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(e,.1)}}function i7(t){return t[t.length-1]}function st(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function se(t,e){let i=e.min-t.min,s=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,s]=[s,i]),{min:i,max:s}}function si(t,e,i){return{min:ss(t,e),max:ss(t,i)}}function ss(t,e){return"number"==typeof t?t:t[e]||0}let sn=new WeakMap;class sr{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=iz(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:s}=this.getProps();this.panSession=new i9(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(iD(t).point)},onStart:(t,e)=>{let{drag:i,dragPropagation:s,onDragStart:n}=this.getProps();if(i&&!s&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(t){if("x"===t||"y"===t)if(iE[t])return null;else return iE[t]=!0,()=>{iE[t]=!1};return iE.x||iE.y?null:(iE.x=iE.y=!0,()=>{iE.x=iE.y=!1})}(i),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),iY(t=>{let e=this.getAxisMotionValue(t).get()||0;if(tE.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let s=i.layout.layoutBox[t];s&&(e=ij(s)*(parseFloat(e)/100))}}this.originPoint[t]=e}),n&&g.postRender(()=>n(t,e)),B(this.visualElement,"transform");let{animationState:r}=this.visualElement;r&&r.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:i,dragDirectionLock:s,onDirectionLock:n,onDrag:r}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:o}=e;if(s&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(o),null!==this.currentDirection&&n&&n(this.currentDirection);return}this.updateAxis("x",e.point,o),this.updateAxis("y",e.point,o),this.visualElement.render(),r&&r(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>iY(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:s,contextWindow:i5(this.visualElement)})}stop(t,e){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:s}=e;this.startAnimation(s);let{onDragEnd:n}=this.getProps();n&&g.postRender(()=>n(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:s}=this.getProps();if(!i||!so(t,s,this.currentDirection))return;let n=this.getAxisMotionValue(t),r=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(r=function(t,{min:e,max:i},s){return void 0!==e&&t<e?t=s?eV(e,t,s.min):Math.max(t,e):void 0!==i&&t>i&&(t=s?eV(i,t,s.max):Math.min(t,i)),t}(r,this.constraints[t],this.elastic[t])),n.set(r)}resolveConstraints(){let{dragConstraints:t,dragElastic:e}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,s=this.constraints;t&&i2(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&i?this.constraints=function(t,{top:e,left:i,bottom:s,right:n}){return{x:st(t.x,i,n),y:st(t.y,e,s)}}(i.layoutBox,t):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:si(t,"left","right"),y:si(t,"top","bottom")}}(e),s!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&iY(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!i2(e))return!1;let s=e.current;ed(null!==s,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:n}=this.visualElement;if(!n||!n.layout)return!1;let r=function(t,e,i){let s=i1(t,i),{scroll:n}=e;return n&&(iQ(s.x,n.offset.x),iQ(s.y,n.offset.y)),s}(s,n.root,this.visualElement.getTransformPagePoint()),o=(t=n.layout.layoutBox,{x:se(t.x,r.x),y:se(t.y,r.y)});if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=iL(t))}return o}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:s,dragTransition:n,dragSnapToOrigin:r,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all(iY(o=>{if(!so(o,e,this.currentDirection))return;let l=a&&a[o]||{};r&&(l={min:0,max:0});let h={type:"inertia",velocity:i?t[o]:0,bounceStiffness:s?200:1e6,bounceDamping:s?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...n,...l};return this.startAxisValueAnimation(o,h)})).then(o)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return B(this.visualElement,t),i.start(ih(t,i,0,e,this.visualElement,!1))}stopAnimation(){iY(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){iY(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){iY(e=>{let{drag:i}=this.getProps();if(!so(e,i,this.currentDirection))return;let{projection:s}=this.visualElement,n=this.getAxisMotionValue(e);if(s&&s.layout){let{min:i,max:r}=s.layout.layoutBox[e];n.set(t[e]-eV(i,r,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!i2(e)||!i||!this.constraints)return;this.stopAnimation();let s={x:0,y:0};iY(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();s[t]=function(t,e){let i=.5,s=ij(t),n=ij(e);return n>s?i=e2(e.min,e.max-s,t.min):s>n&&(i=e2(t.min,t.max-n,e.min)),tc(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:n}=this.visualElement.getProps();this.visualElement.current.style.transform=n?n({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),iY(e=>{if(!so(e,t,null))return;let i=this.getAxisMotionValue(e),{min:n,max:r}=this.constraints[e];i.set(eV(n,r,s[e]))})}addListeners(){if(!this.visualElement.current)return;sn.set(this.visualElement,this);let t=iR(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();i2(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,s=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),g.read(e);let n=iV(window,"resize",()=>this.scalePositionWithinConstraints()),r=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(iY(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{n(),t(),s(),r&&r()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:s=!1,dragConstraints:n=!1,dragElastic:r=.35,dragMomentum:o=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:s,dragConstraints:n,dragElastic:r,dragMomentum:o}}}function so(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class sa extends ib{constructor(t){super(t),this.removeGroupControls=d.l,this.removeListeners=d.l,this.controls=new sr(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||d.l}unmount(){this.removeGroupControls(),this.removeListeners()}}let sl=t=>(e,i)=>{t&&g.postRender(()=>t(e,i))};class sh extends ib{constructor(){super(...arguments),this.removePointerDownListener=d.l}onPointerDown(t){this.session=new i9(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:i5(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:s}=this.node.getProps();return{onSessionStart:sl(t),onStart:sl(e),onMove:i,onEnd:(t,e)=>{delete this.session,s&&g.postRender(()=>s(t,e))}}}mount(){this.removePointerDownListener=iR(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var su=i(95155);let{schedule:sd}=f(queueMicrotask,!1);var sc=i(12115),sp=i(32082),sm=i(90869);let sf=(0,sc.createContext)({}),sg={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function sv(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let sy={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t)if(!tV.test(t))return t;else t=parseFloat(t);let i=sv(t,e.target.x),s=sv(t,e.target.y);return`${i}% ${s}%`}},sx={};class sw extends sc.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:s}=this.props,{projection:n}=t;for(let t in sT)sx[t]=sT[t],em(t)&&(sx[t].isCSSVariable=!0);n&&(e.group&&e.group.add(n),i&&i.register&&s&&i.register(n),n.root.didUpdate(),n.addEventListener("animationComplete",()=>{this.safeToRemove()}),n.setOptions({...n.options,onExitComplete:()=>this.safeToRemove()})),sg.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:s,isPresent:n}=this.props,r=i.projection;return r&&(r.isPresent=n,s||t.layoutDependency!==e||void 0===e||t.isPresent!==n?r.willUpdate():this.safeToRemove(),t.isPresent!==n&&(n?r.promote():r.relegate()||g.postRender(()=>{let t=r.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),sd.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:s}=t;s&&(s.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(s),i&&i.deregister&&i.deregister(s))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function sP(t){let[e,i]=(0,sp.xQ)(),s=(0,sc.useContext)(sm.L);return(0,su.jsx)(sw,{...t,layoutGroup:s,switchLayoutGroup:(0,sc.useContext)(sf),isPresent:e,safeToRemove:i})}let sT={borderRadius:{...sy,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:sy,borderTopRightRadius:sy,borderBottomLeftRadius:sy,borderBottomRightRadius:sy,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let s=tW.parse(t);if(s.length>5)return t;let n=tW.createTransformer(t),r=+("number"!=typeof s[0]),o=i.x.scale*e.x,a=i.y.scale*e.y;s[0+r]/=o,s[1+r]/=a;let l=eV(o,a,.5);return"number"==typeof s[2+r]&&(s[2+r]/=l),"number"==typeof s[3+r]&&(s[3+r]/=l),n(s)}}},sb=(t,e)=>t.depth-e.depth;class sS{constructor(){this.children=[],this.isDirty=!1}add(t){b(this.children,t),this.isDirty=!0}remove(t){S(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(sb),this.isDirty=!1,this.children.forEach(t)}}function sA(t){let e=F(t)?t.get():t;return L(e)?e.toValue():e}let sM=["TopLeft","TopRight","BottomLeft","BottomRight"],sE=sM.length,sV=t=>"string"==typeof t?parseFloat(t):t,sC=t=>"number"==typeof t||tV.test(t);function sD(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let sk=sL(0,.5,th),sR=sL(.5,.95,d.l);function sL(t,e,i){return s=>s<t?0:s>e?1:i(e2(t,e,s))}function sj(t,e){t.min=e.min,t.max=e.max}function sF(t,e){sj(t.x,e.x),sj(t.y,e.y)}function sB(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function sO(t,e,i,s,n){return t-=e,t=s+1/i*(t-s),void 0!==n&&(t=s+1/n*(t-s)),t}function sI(t,e,[i,s,n],r,o){!function(t,e=0,i=1,s=.5,n,r=t,o=t){if(tE.test(e)&&(e=parseFloat(e),e=eV(o.min,o.max,e/100)-o.min),"number"!=typeof e)return;let a=eV(r.min,r.max,s);t===r&&(a-=e),t.min=sO(t.min,e,i,a,n),t.max=sO(t.max,e,i,a,n)}(t,e[i],e[s],e[n],e.scale,r,o)}let sU=["x","scaleX","originX"],s$=["y","scaleY","originY"];function sN(t,e,i,s){sI(t.x,e,sU,i?i.x:void 0,s?s.x:void 0),sI(t.y,e,s$,i?i.y:void 0,s?s.y:void 0)}function sW(t){return 0===t.translate&&1===t.scale}function sz(t){return sW(t.x)&&sW(t.y)}function sY(t,e){return t.min===e.min&&t.max===e.max}function sX(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function sH(t,e){return sX(t.x,e.x)&&sX(t.y,e.y)}function sK(t){return ij(t.x)/ij(t.y)}function sq(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class sG{constructor(){this.members=[]}add(t){b(this.members,t),t.scheduleRender()}remove(t){if(S(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e,i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:s}=t.options;!1===s&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let s_={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},sZ=["","X","Y","Z"],sQ={visibility:"hidden"},sJ=0;function s0(t,e,i,s){let{latestValues:n}=e;n[t]&&(i[t]=n[t],e.setStaticValue(t,0),s&&(s[t]=0))}function s1({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:s,resetTransform:n}){return class{constructor(t={},i=e?.()){this.id=sJ++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,m.value&&(s_.nodes=s_.calculatedTargetDeltas=s_.calculatedProjections=0),this.nodes.forEach(s3),this.nodes.forEach(ne),this.nodes.forEach(ni),this.nodes.forEach(s9),m.addProjectionMetrics&&m.addProjectionMetrics(s_)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new sS)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new A),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e,i=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=e instanceof SVGElement&&"svg"!==e.tagName,this.instance=e;let{layoutId:s,layout:n,visualElement:r}=this.options;if(r&&!r.current&&r.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),i&&(n||s)&&(this.isLayoutDirty=!0),t){let i,s=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=E.now(),s=({timestamp:n})=>{let r=n-i;r>=250&&(v(s),t(r-e))};return g.read(s,!0),()=>v(s)}(s,250),sg.hasAnimatedSinceResize&&(sg.hasAnimatedSinceResize=!1,this.nodes.forEach(nt))})}s&&this.root.registerSharedNode(s,this),!1!==this.options.animate&&r&&(s||n)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:i,layout:s})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let n=this.options.transition||r.getDefaultTransition()||nl,{onLayoutAnimationStart:o,onLayoutAnimationComplete:a}=r.getProps(),h=!this.targetLayout||!sH(this.targetLayout,s),u=!e&&i;if(this.options.layoutRoot||this.resumeFrom||u||e&&(h||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,u);let e={...l(n,"layout"),onPlay:o,onComplete:a};(r.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e)}else e||nt(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=s})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,v(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(ns),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let s=i.props[I];if(window.MotionHasOptimisedAnimation(s,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(s,"transform",g,!(t||i))}let{parent:n}=e;n&&!n.hasCheckedOptimisedAppear&&t(n)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let s=this.getTransformTemplate();this.prevTransformTemplateValue=s?s(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(s4);return}this.isUpdating||this.nodes.forEach(s6),this.isUpdating=!1,this.nodes.forEach(s7),this.nodes.forEach(s5),this.nodes.forEach(s2),this.clearAllSnapshots();let t=E.now();y.delta=tc(0,1e3/60,t-y.timestamp),y.timestamp=t,y.isProcessing=!0,x.update.process(y),x.preRender.process(y),x.render.process(y),y.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,sd.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(s8),this.sharedNodes.forEach(nn)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,g.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){g.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||ij(this.snapshot.measuredBox.x)||ij(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=iz(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e){let e=s(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!n)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!sz(this.projectionDelta),i=this.getTransformTemplate(),s=i?i(this.latestValues,""):void 0,r=s!==this.prevTransformTemplateValue;t&&(e||iK(this.latestValues)||r)&&(n(this.instance,s),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),s=this.removeElementScroll(i);return t&&(s=this.removeTransform(s)),nd((e=s).x),nd(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:s,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return iz();let e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(np))){let{scroll:t}=this.root;t&&(iQ(e.x,t.offset.x),iQ(e.y,t.offset.y))}return e}removeElementScroll(t){let e=iz();if(sF(e,t),this.scroll?.wasRoot)return e;for(let i=0;i<this.path.length;i++){let s=this.path[i],{scroll:n,options:r}=s;s!==this.root&&n&&r.layoutScroll&&(n.wasRoot&&sF(e,t),iQ(e.x,n.offset.x),iQ(e.y,n.offset.y))}return e}applyTransform(t,e=!1){let i=iz();sF(i,t);for(let t=0;t<this.path.length;t++){let s=this.path[t];!e&&s.options.layoutScroll&&s.scroll&&s!==s.root&&i0(i,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),iK(s.latestValues)&&i0(i,s.latestValues)}return iK(this.latestValues)&&i0(i,this.latestValues),i}removeTransform(t){let e=iz();sF(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!iK(i.latestValues))continue;iH(i.latestValues)&&i.updateSnapshot();let s=iz();sF(s,i.measurePageBox()),sN(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,s)}return iK(this.latestValues)&&sN(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==y.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){let e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==e;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:s,layoutId:n}=this.options;if(this.layout&&(s||n)){if(this.resolvedRelativeTargetAt=y.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iz(),this.relativeTargetOrigin=iz(),iU(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),sF(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=iz(),this.targetWithTransforms=iz()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var r,o,a;this.forceRelativeParentToResolveTarget(),r=this.target,o=this.relativeTarget,a=this.relativeParent.target,iO(r.x,o.x,a.x),iO(r.y,o.y,a.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):sF(this.target,this.layout.layoutBox),iZ(this.target,this.targetDelta)):sF(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iz(),this.relativeTargetOrigin=iz(),iU(this.relativeTargetOrigin,this.target,t.target),sF(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}m.value&&s_.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||iH(this.parent.latestValues)||iq(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let t=this.getLead(),e=!!this.resumingFrom||this!==t,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===y.timestamp&&(i=!1),i)return;let{layout:s,layoutId:n}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(s||n))return;sF(this.layoutCorrected,this.layout.layoutBox);let r=this.treeScale.x,o=this.treeScale.y;!function(t,e,i,s=!1){let n,r,o=i.length;if(o){e.x=e.y=1;for(let a=0;a<o;a++){r=(n=i[a]).projectionDelta;let{visualElement:o}=n.options;(!o||!o.props.style||"contents"!==o.props.style.display)&&(s&&n.options.layoutScroll&&n.scroll&&n!==n.root&&i0(t,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,iZ(t,r)),s&&iK(n.latestValues)&&i0(t,n.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,e),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=iz());let{target:a}=t;if(!a){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(sB(this.prevProjectionDelta.x,this.projectionDelta.x),sB(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),iB(this.projectionDelta,this.layoutCorrected,a,this.latestValues),this.treeScale.x===r&&this.treeScale.y===o&&sq(this.projectionDelta.x,this.prevProjectionDelta.x)&&sq(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",a)),m.value&&s_.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=iN(),this.projectionDelta=iN(),this.projectionDeltaWithTransform=iN()}setAnimationOrigin(t,e=!1){let i,s=this.snapshot,n=s?s.latestValues:{},r={...this.latestValues},o=iN();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let a=iz(),l=(s?s.source:void 0)!==(this.layout?this.layout.source:void 0),h=this.getStack(),u=!h||h.members.length<=1,d=!!(l&&!u&&!0===this.options.crossfade&&!this.path.some(na));this.animationProgress=0,this.mixTargetDelta=e=>{let s=e/1e3;if(nr(o.x,t.x,s),nr(o.y,t.y,s),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var h,c,p,m,f,g;iU(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=a,g=s,no(p.x,m.x,f.x,g),no(p.y,m.y,f.y,g),i&&(h=this.relativeTarget,c=i,sY(h.x,c.x)&&sY(h.y,c.y))&&(this.isProjectionDirty=!1),i||(i=iz()),sF(i,this.relativeTarget)}l&&(this.animationValues=r,function(t,e,i,s,n,r){n?(t.opacity=eV(0,i.opacity??1,sk(s)),t.opacityExit=eV(e.opacity??1,0,sR(s))):r&&(t.opacity=eV(e.opacity??1,i.opacity??1,s));for(let n=0;n<sE;n++){let r=`border${sM[n]}Radius`,o=sD(e,r),a=sD(i,r);(void 0!==o||void 0!==a)&&(o||(o=0),a||(a=0),0===o||0===a||sC(o)===sC(a)?(t[r]=Math.max(eV(sV(o),sV(a),s),0),(tE.test(a)||tE.test(o))&&(t[r]+="%")):t[r]=a)}(e.rotate||i.rotate)&&(t.rotate=eV(e.rotate||0,i.rotate||0,s))}(r,n,this.latestValues,s,d,u)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=s},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(v(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=g.update(()=>{sg.hasAnimatedSinceResize=!0,Q.layout++,this.currentAnimation=function(t,e,i){let s=F(0)?0:k(t);return s.start(ih("",s,1e3,i)),s.animation}(0,0,{...t,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{Q.layout--},onComplete:()=>{Q.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:s,latestValues:n}=t;if(e&&i&&s){if(this!==t&&this.layout&&s&&nc(this.options.animationType,this.layout.layoutBox,s.layoutBox)){i=this.target||iz();let e=ij(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let s=ij(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+s}sF(e,i),i0(e,n),iB(this.projectionDeltaWithTransform,this.layoutCorrected,e,n)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new sG),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){let{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let s=this.getStack();s&&s.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let s={};i.z&&s0("z",t,s,this.animationValues);for(let e=0;e<sZ.length;e++)s0(`rotate${sZ[e]}`,t,s,this.animationValues),s0(`skew${sZ[e]}`,t,s,this.animationValues);for(let e in t.render(),s)t.setStaticValue(e,s[e]),this.animationValues&&(this.animationValues[e]=s[e]);t.scheduleRender()}getProjectionStyles(t){if(!this.instance||this.isSVG)return;if(!this.isVisible)return sQ;let e={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,e.opacity="",e.pointerEvents=sA(t?.pointerEvents)||"",e.transform=i?i(this.latestValues,""):"none",e;let s=this.getLead();if(!this.projectionDelta||!this.layout||!s.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=sA(t?.pointerEvents)||""),this.hasProjected&&!iK(this.latestValues)&&(e.transform=i?i({},""):"none",this.hasProjected=!1),e}let n=s.animationValues||s.latestValues;this.applyTransformsToTarget(),e.transform=function(t,e,i){let s="",n=t.x.translate/e.x,r=t.y.translate/e.y,o=i?.z||0;if((n||r||o)&&(s=`translate3d(${n}px, ${r}px, ${o}px) `),(1!==e.x||1!==e.y)&&(s+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:n,rotateY:r,skewX:o,skewY:a}=i;t&&(s=`perspective(${t}px) ${s}`),e&&(s+=`rotate(${e}deg) `),n&&(s+=`rotateX(${n}deg) `),r&&(s+=`rotateY(${r}deg) `),o&&(s+=`skewX(${o}deg) `),a&&(s+=`skewY(${a}deg) `)}let a=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==a||1!==l)&&(s+=`scale(${a}, ${l})`),s||"none"}(this.projectionDeltaWithTransform,this.treeScale,n),i&&(e.transform=i(n,e.transform));let{x:r,y:o}=this.projectionDelta;for(let t in e.transformOrigin=`${100*r.origin}% ${100*o.origin}% 0`,s.animationValues?e.opacity=s===this?n.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:n.opacityExit:e.opacity=s===this?void 0!==n.opacity?n.opacity:"":void 0!==n.opacityExit?n.opacityExit:0,sx){if(void 0===n[t])continue;let{correct:i,applyTo:r,isCSSVariable:o}=sx[t],a="none"===e.transform?n[t]:i(n[t],s);if(r){let t=r.length;for(let i=0;i<t;i++)e[r[i]]=a}else o?this.options.visualElement.renderState.vars[t]=a:e[t]=a}return this.options.layoutId&&(e.pointerEvents=s===this?sA(t?.pointerEvents)||"":"none"),e}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop()),this.root.nodes.forEach(s4),this.root.sharedNodes.clear()}}}function s5(t){t.updateLayout()}function s2(t){let e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:s}=t.layout,{animationType:n}=t.options,r=e.source!==t.layout.source;"size"===n?iY(t=>{let s=r?e.measuredBox[t]:e.layoutBox[t],n=ij(s);s.min=i[t].min,s.max=s.min+n}):nc(n,e.layoutBox,i)&&iY(s=>{let n=r?e.measuredBox[s]:e.layoutBox[s],o=ij(i[s]);n.max=n.min+o,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[s].max=t.relativeTarget[s].min+o)});let o=iN();iB(o,i,e.layoutBox);let a=iN();r?iB(a,t.applyTransform(s,!0),e.measuredBox):iB(a,i,e.layoutBox);let l=!sz(o),h=!1;if(!t.resumeFrom){let s=t.getClosestProjectingParent();if(s&&!s.resumeFrom){let{snapshot:n,layout:r}=s;if(n&&r){let o=iz();iU(o,e.layoutBox,n.layoutBox);let a=iz();iU(a,i,r.layoutBox),sH(o,a)||(h=!0),s.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=o,t.relativeParent=s)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:e,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeLayoutChanged:h})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function s3(t){m.value&&s_.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function s9(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function s8(t){t.clearSnapshot()}function s4(t){t.clearMeasurements()}function s6(t){t.isLayoutDirty=!1}function s7(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function nt(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function ne(t){t.resolveTargetDelta()}function ni(t){t.calcProjection()}function ns(t){t.resetSkewAndRotation()}function nn(t){t.removeLeadSnapshot()}function nr(t,e,i){t.translate=eV(e.translate,0,i),t.scale=eV(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function no(t,e,i,s){t.min=eV(e.min,i.min,s),t.max=eV(e.max,i.max,s)}function na(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let nl={duration:.45,ease:[.4,0,.1,1]},nh=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),nu=nh("applewebkit/")&&!nh("chrome/")?Math.round:d.l;function nd(t){t.min=nu(t.min),t.max=nu(t.max)}function nc(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(sK(e)-sK(i)))}function np(t){return t!==t.root&&t.scroll?.wasRoot}let nm=s1({attachResizeListener:(t,e)=>iV(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),nf={current:void 0},ng=s1({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!nf.current){let t=new nm({});t.mount(window),t.setOptions({layoutScroll:!0}),nf.current=t}return nf.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function nv(t,e){let i=function(t,e,i){if(t instanceof EventTarget)return[t];if("string"==typeof t){let e=document,i=(void 0)??e.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}(t),s=new AbortController;return[i,{passive:!0,...e,signal:s.signal},()=>s.abort()]}function ny(t){return!("touch"===t.pointerType||iE.x||iE.y)}function nx(t,e,i){let{props:s}=t;t.animationState&&s.whileHover&&t.animationState.setActive("whileHover","Start"===i);let n=s["onHover"+i];n&&g.postRender(()=>n(e,iD(e)))}class nw extends ib{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[s,n,r]=nv(t,i),o=t=>{if(!ny(t))return;let{target:i}=t,s=e(i,t);if("function"!=typeof s||!i)return;let r=t=>{ny(t)&&(s(t),i.removeEventListener("pointerleave",r))};i.addEventListener("pointerleave",r,n)};return s.forEach(t=>{t.addEventListener("pointerenter",o,n)}),r}(t,(t,e)=>(nx(this.node,e,"Start"),t=>nx(this.node,t,"End"))))}unmount(){}}class nP extends ib{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=eO(iV(this.node.current,"focus",()=>this.onFocus()),iV(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let nT=(t,e)=>!!e&&(t===e||nT(t,e.parentElement)),nb=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),nS=new WeakSet;function nA(t){return e=>{"Enter"===e.key&&t(e)}}function nM(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let nE=(t,e)=>{let i=t.currentTarget;if(!i)return;let s=nA(()=>{if(nS.has(i))return;nM(i,"down");let t=nA(()=>{nM(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>nM(i,"cancel"),e)});i.addEventListener("keydown",s,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",s),e)};function nV(t){return iC(t)&&!(iE.x||iE.y)}function nC(t,e,i){let{props:s}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&s.whileTap&&t.animationState.setActive("whileTap","Start"===i);let n=s["onTap"+("End"===i?"":i)];n&&g.postRender(()=>n(e,iD(e)))}class nD extends ib{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[s,n,r]=nv(t,i),o=t=>{let s=t.currentTarget;if(!nV(t)||nS.has(s))return;nS.add(s);let r=e(s,t),o=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),nV(t)&&nS.has(s)&&(nS.delete(s),"function"==typeof r&&r(t,{success:e}))},a=t=>{o(t,s===window||s===document||i.useGlobalTarget||nT(s,t.target))},l=t=>{o(t,!1)};window.addEventListener("pointerup",a,n),window.addEventListener("pointercancel",l,n)};return s.forEach(t=>{((i.useGlobalTarget?window:t).addEventListener("pointerdown",o,n),t instanceof HTMLElement)&&(t.addEventListener("focus",t=>nE(t,n)),nb.has(t.tagName)||-1!==t.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),r}(t,(t,e)=>(nC(this.node,e,"Start"),(t,{success:e})=>nC(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let nk=new WeakMap,nR=new WeakMap,nL=t=>{let e=nk.get(t.target);e&&e(t)},nj=t=>{t.forEach(nL)},nF={some:0,all:1};class nB extends ib{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:s="some",once:n}=t,r={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof s?s:nF[s]};return function(t,e,i){let s=function({root:t,...e}){let i=t||document;nR.has(i)||nR.set(i,{});let s=nR.get(i),n=JSON.stringify(e);return s[n]||(s[n]=new IntersectionObserver(nj,{root:t,...e})),s[n]}(e);return nk.set(t,i),s.observe(t),()=>{nk.delete(t),s.unobserve(t)}}(this.node.current,r,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,n&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:s}=this.node.getProps(),r=e?i:s;r&&r(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let nO=(0,sc.createContext)({strict:!1});var nI=i(51508);let nU=(0,sc.createContext)({});function n$(t){return n(t.animate)||iv.some(e=>im(t[e]))}function nN(t){return!!(n$(t)||t.variants)}function nW(t){return Array.isArray(t)?t.join(" "):t}var nz=i(68972);let nY={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},nX={};for(let t in nY)nX[t]={isEnabled:e=>nY[t].some(t=>!!e[t])};let nH=Symbol.for("motionComponentSymbol");var nK=i(80845),nq=i(97494);function nG(t,{layout:e,layoutId:i}){return P.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!sx[t]||"opacity"===t)}let n_=(t,e)=>e&&"number"==typeof t?e.transform(t):t,nZ={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},nQ=w.length;function nJ(t,e,i){let{style:s,vars:n,transformOrigin:r}=t,o=!1,a=!1;for(let t in e){let i=e[t];if(P.has(t)){o=!0;continue}if(em(t)){n[t]=i;continue}{let e=n_(i,tq[t]);t.startsWith("origin")?(a=!0,r[t]=e):s[t]=e}}if(!e.transform&&(o||i?s.transform=function(t,e,i){let s="",n=!0;for(let r=0;r<nQ;r++){let o=w[r],a=t[o];if(void 0===a)continue;let l=!0;if(!(l="number"==typeof a?a===+!!o.startsWith("scale"):0===parseFloat(a))||i){let t=n_(a,tq[o]);if(!l){n=!1;let e=nZ[o]||o;s+=`${e}(${t}) `}i&&(e[o]=t)}}return s=s.trim(),i?s=i(e,n?"":s):n&&(s="none"),s}(e,t.transform,i):s.transform&&(s.transform="none")),a){let{originX:t="50%",originY:e="50%",originZ:i=0}=r;s.transformOrigin=`${t} ${e} ${i}`}}let n0=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function n1(t,e,i){for(let s in e)F(e[s])||nG(s,i)||(t[s]=e[s])}let n5=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function n2(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||n5.has(t)}let n3=t=>!n2(t);try{!function(t){t&&(n3=e=>e.startsWith("on")?!n2(e):t(e))}(require("@emotion/is-prop-valid").default)}catch{}let n9=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function n8(t){if("string"!=typeof t||t.includes("-"));else if(n9.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}let n4={offset:"stroke-dashoffset",array:"stroke-dasharray"},n6={offset:"strokeDashoffset",array:"strokeDasharray"};function n7(t,e,i){return"string"==typeof t?t:tV.transform(e+i*t)}function rt(t,{attrX:e,attrY:i,attrScale:s,originX:n,originY:r,pathLength:o,pathSpacing:a=1,pathOffset:l=0,...h},u,d){if(nJ(t,h,d),u){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:c,style:p,dimensions:m}=t;c.transform&&(m&&(p.transform=c.transform),delete c.transform),m&&(void 0!==n||void 0!==r||p.transform)&&(p.transformOrigin=function(t,e,i){let s=n7(e,t.x,t.width),n=n7(i,t.y,t.height);return`${s} ${n}`}(m,void 0!==n?n:.5,void 0!==r?r:.5)),void 0!==e&&(c.x=e),void 0!==i&&(c.y=i),void 0!==s&&(c.scale=s),void 0!==o&&function(t,e,i=1,s=0,n=!0){t.pathLength=1;let r=n?n4:n6;t[r.offset]=tV.transform(-s);let o=tV.transform(e),a=tV.transform(i);t[r.array]=`${o} ${a}`}(c,o,a,l,!1)}let re=()=>({...n0(),attrs:{}}),ri=t=>"string"==typeof t&&"svg"===t.toLowerCase();var rs=i(82885);let rn=t=>(e,i)=>{let s=(0,sc.useContext)(nU),r=(0,sc.useContext)(nK.t),a=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e,onUpdate:i},s,r,a){let l={latestValues:function(t,e,i,s){let r={},a=s(t,{});for(let t in a)r[t]=sA(a[t]);let{initial:l,animate:h}=t,u=n$(t),d=nN(t);e&&d&&!u&&!1!==t.inherit&&(void 0===l&&(l=e.initial),void 0===h&&(h=e.animate));let c=!!i&&!1===i.initial,p=(c=c||!1===l)?h:l;if(p&&"boolean"!=typeof p&&!n(p)){let e=Array.isArray(p)?p:[p];for(let i=0;i<e.length;i++){let s=o(t,e[i]);if(s){let{transitionEnd:t,transition:e,...i}=s;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=c?e.length-1:0;e=e[t]}null!==e&&(r[t]=e)}for(let e in t)r[e]=t[e]}}}return r}(s,r,a,t),renderState:e()};return i&&(l.onMount=t=>i({props:s,current:t,...l}),l.onUpdate=t=>i(t)),l})(t,e,s,r);return i?a():(0,rs.M)(a)};function rr(t,e,i){let{style:s}=t,n={};for(let r in s)(F(s[r])||e.style&&F(e.style[r])||nG(r,t)||i?.getValue(r)?.liveStyle!==void 0)&&(n[r]=s[r]);return n}let ro={useVisualState:rn({scrapeMotionValuesFromProps:rr,createRenderState:n0})};function ra(t,e){try{e.dimensions="function"==typeof t.getBBox?t.getBBox():t.getBoundingClientRect()}catch(t){e.dimensions={x:0,y:0,width:0,height:0}}}function rl(t,{style:e,vars:i},s,n){for(let r in Object.assign(t.style,e,n&&n.getProjectionStyles(s)),i)t.style.setProperty(r,i[r])}let rh=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function ru(t,e,i,s){for(let i in rl(t,e,void 0,s),e.attrs)t.setAttribute(rh.has(i)?i:O(i),e.attrs[i])}function rd(t,e,i){let s=rr(t,e,i);for(let i in t)(F(t[i])||F(e[i]))&&(s[-1!==w.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return s}let rc=["x","y","width","height","cx","cy","r"],rp={useVisualState:rn({scrapeMotionValuesFromProps:rd,createRenderState:re,onUpdate:({props:t,prevProps:e,current:i,renderState:s,latestValues:n})=>{if(!i)return;let r=!!t.drag;if(!r){for(let t in n)if(P.has(t)){r=!0;break}}if(!r)return;let o=!e;if(e)for(let i=0;i<rc.length;i++){let s=rc[i];t[s]!==e[s]&&(o=!0)}o&&g.read(()=>{ra(i,s),g.render(()=>{rt(s,n,ri(i.tagName),t.transformTemplate),ru(i,s)})})}})},rm={current:null},rf={current:!1},rg=[...ew,tL,tW],rv=t=>rg.find(ex(t)),ry=new WeakMap,rx=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class rw{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:s,blockInitialAnimation:n,visualState:r},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=eh,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=E.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,g.render(this.render,!1,!0))};let{latestValues:a,renderState:l,onUpdate:h}=r;this.onUpdate=h,this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=s,this.options=o,this.blockInitialAnimation=!!n,this.isControllingVariants=n$(e),this.isVariantNode=nN(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:u,...d}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in d){let e=d[t];void 0!==a[t]&&F(e)&&e.set(a[t],!1)}}mount(t){this.current=t,ry.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),rf.current||function(){if(rf.current=!0,nz.B)if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>rm.current=t.matches;t.addListener(e),e()}else rm.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||rm.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),v(this.notifyUpdate),v(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let s=P.has(t);s&&this.onBindTransform&&this.onBindTransform();let n=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&g.preRender(this.notifyUpdate),s&&this.projection&&(this.projection.isTransformDirty=!0)}),r=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{n(),r(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in nX){let e=nX[t];if(!e)continue;let{isEnabled:i,Feature:s}=e;if(!this.features[t]&&s&&i(this.props)&&(this.features[t]=new s(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):iz()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<rx.length;e++){let i=rx[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let s=t["on"+i];s&&(this.propEventSubscriptions[i]=this.on(i,s))}this.prevMotionValues=function(t,e,i){for(let s in e){let n=e[s],r=i[s];if(F(n))t.addValue(s,n);else if(F(r))t.addValue(s,k(n,{owner:t}));else if(r!==n)if(t.hasValue(s)){let e=t.getValue(s);!0===e.liveStyle?e.jump(n):e.hasAnimated||e.set(n)}else{let e=t.getStaticValue(s);t.addValue(s,k(void 0!==e?e:n,{owner:t}))}}for(let s in i)void 0===e[s]&&t.removeValue(s);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue(),this.onUpdate&&this.onUpdate(this)}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=k(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){let i=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=i&&("string"==typeof i&&(ec(i)||td(i))?i=parseFloat(i):!rv(i)&&tW.test(e)&&(i=tZ(t,e)),this.setBaseTarget(t,F(i)?i.get():i)),F(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e,{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let s=o(this.props,i,this.presenceContext?.custom);s&&(e=s[t])}if(i&&void 0!==e)return e;let s=this.getBaseTargetFromProps(this.props,t);return void 0===s||F(s)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:s}on(t,e){return this.events[t]||(this.events[t]=new A),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class rP extends rw{constructor(){super(...arguments),this.KeyframeResolver=eT}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;F(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}class rT extends rP{constructor(){super(...arguments),this.type="html",this.renderInstance=rl}readValueFromInstance(t,e){if(P.has(e))return t6(t,e);{let i=window.getComputedStyle(t),s=(em(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof s?s.trim():s}}measureInstanceViewportBox(t,{transformPagePoint:e}){return i1(t,e)}build(t,e,i){nJ(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return rr(t,e,i)}}class rb extends rP{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=iz,this.updateDimensions=()=>{this.current&&!this.renderState.dimensions&&ra(this.current,this.renderState)}}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(P.has(e)){let t=t_(e);return t&&t.default||0}return e=rh.has(e)?e:O(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return rd(t,e,i)}onBindTransform(){this.current&&!this.renderState.dimensions&&g.postRender(this.updateDimensions)}build(t,e,i){rt(t,e,this.isSVGTag,i.transformTemplate)}renderInstance(t,e,i,s){ru(t,e,i,s)}mount(t){this.isSVGTag=ri(t.tagName),super.mount(t)}}let rS=function(t){if("undefined"==typeof Proxy)return t;let e=new Map;return new Proxy((...e)=>t(...e),{get:(i,s)=>"create"===s?t:(e.has(s)||e.set(s,t(s)),e.get(s))})}((h={animation:{Feature:iS},exit:{Feature:iM},inView:{Feature:nB},tap:{Feature:nD},focus:{Feature:nP},hover:{Feature:nw},pan:{Feature:sh},drag:{Feature:sa,ProjectionNode:ng,MeasureLayout:sP},layout:{ProjectionNode:ng,MeasureLayout:sP}},u=(t,e)=>n8(t)?new rb(e):new rT(e,{allowProjection:t!==sc.Fragment}),function(t,{forwardMotionProps:e}={forwardMotionProps:!1}){return function(t){var e,i;let{preloadedFeatures:s,createVisualElement:n,useRender:r,useVisualState:o,Component:a}=t;function l(t,e){var i,s,l;let h,u={...(0,sc.useContext)(nI.Q),...t,layoutId:function(t){let{layoutId:e}=t,i=(0,sc.useContext)(sm.L).id;return i&&void 0!==e?i+"-"+e:e}(t)},{isStatic:d}=u,c=function(t){let{initial:e,animate:i}=function(t,e){if(n$(t)){let{initial:e,animate:i}=t;return{initial:!1===e||im(e)?e:void 0,animate:im(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,sc.useContext)(nU));return(0,sc.useMemo)(()=>({initial:e,animate:i}),[nW(e),nW(i)])}(t),p=o(t,d);if(!d&&nz.B){s=0,l=0,(0,sc.useContext)(nO).strict;let t=function(t){let{drag:e,layout:i}=nX;if(!e&&!i)return{};let s={...e,...i};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==i?void 0:i.isEnabled(t))?s.MeasureLayout:void 0,ProjectionNode:s.ProjectionNode}}(u);h=t.MeasureLayout,c.visualElement=function(t,e,i,s,n){let{visualElement:r}=(0,sc.useContext)(nU),o=(0,sc.useContext)(nO),a=(0,sc.useContext)(nK.t),l=(0,sc.useContext)(nI.Q).reducedMotion,h=(0,sc.useRef)(null);s=s||o.renderer,!h.current&&s&&(h.current=s(t,{visualState:e,parent:r,props:i,presenceContext:a,blockInitialAnimation:!!a&&!1===a.initial,reducedMotionConfig:l}));let u=h.current,d=(0,sc.useContext)(sf);u&&!u.projection&&n&&("html"===u.type||"svg"===u.type)&&function(t,e,i,s){let{layoutId:n,layout:r,drag:o,dragConstraints:a,layoutScroll:l,layoutRoot:h,layoutCrossfade:u}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:n,layout:r,alwaysMeasureLayout:!!o||a&&i2(a),visualElement:t,animationType:"string"==typeof r?r:"both",initialPromotionConfig:s,crossfade:u,layoutScroll:l,layoutRoot:h})}(h.current,i,n,d);let c=(0,sc.useRef)(!1);(0,sc.useInsertionEffect)(()=>{u&&c.current&&u.update(i,a)});let p=i[I],m=(0,sc.useRef)(!!p&&!window.MotionHandoffIsComplete?.(p)&&window.MotionHasOptimisedAnimation?.(p));return(0,nq.E)(()=>{u&&(c.current=!0,window.MotionIsMounted=!0,u.updateFeatures(),sd.render(u.render),m.current&&u.animationState&&u.animationState.animateChanges())}),(0,sc.useEffect)(()=>{u&&(!m.current&&u.animationState&&u.animationState.animateChanges(),m.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(p)}),m.current=!1))}),u}(a,p,u,n,t.ProjectionNode)}return(0,su.jsxs)(nU.Provider,{value:c,children:[h&&c.visualElement?(0,su.jsx)(h,{visualElement:c.visualElement,...u}):null,r(a,t,(i=c.visualElement,(0,sc.useCallback)(t=>{t&&p.onMount&&p.onMount(t),i&&(t?i.mount(t):i.unmount()),e&&("function"==typeof e?e(t):i2(e)&&(e.current=t))},[i])),p,d,c.visualElement)]})}s&&function(t){for(let e in t)nX[e]={...nX[e],...t[e]}}(s),l.displayName="motion.".concat("string"==typeof a?a:"create(".concat(null!=(i=null!=(e=a.displayName)?e:a.name)?i:"",")"));let h=(0,sc.forwardRef)(l);return h[nH]=a,h}({...n8(t)?rp:ro,preloadedFeatures:h,useRender:function(t=!1){return(e,i,s,{latestValues:n},r)=>{let o=(n8(e)?function(t,e,i,s){let n=(0,sc.useMemo)(()=>{let i=re();return rt(i,e,ri(s),t.transformTemplate),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};n1(e,t.style,t),n.style={...e,...n.style}}return n}:function(t,e){let i={},s=function(t,e){let i=t.style||{},s={};return n1(s,i,t),Object.assign(s,function({transformTemplate:t},e){return(0,sc.useMemo)(()=>{let i=n0();return nJ(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),s}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,s.userSelect=s.WebkitUserSelect=s.WebkitTouchCallout="none",s.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=s,i})(i,n,r,e),a=function(t,e,i){let s={};for(let n in t)("values"!==n||"object"!=typeof t.values)&&(n3(n)||!0===i&&n2(n)||!e&&!n2(n)||t.draggable&&n.startsWith("onDrag"))&&(s[n]=t[n]);return s}(i,"string"==typeof e,t),l=e!==sc.Fragment?{...a,...o,ref:s}:{},{children:h}=i,u=(0,sc.useMemo)(()=>F(h)?h.get():h,[h]);return(0,sc.createElement)(e,{...l,children:u})}}(e),createVisualElement:u,Component:t})}))},51508:(t,e,i)=>{i.d(e,{Q:()=>s});let s=(0,i(12115).createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"})},60760:(t,e,i)=>{i.d(e,{N:()=>v});var s=i(95155),n=i(12115),r=i(90869),o=i(82885),a=i(97494),l=i(80845),h=i(51508);class u extends n.Component{getSnapshotBeforeUpdate(t){let e=this.props.childRef.current;if(e&&t.isPresent&&!this.props.isPresent){let t=e.offsetParent,i=t instanceof HTMLElement&&t.offsetWidth||0,s=this.props.sizeRef.current;s.height=e.offsetHeight||0,s.width=e.offsetWidth||0,s.top=e.offsetTop,s.left=e.offsetLeft,s.right=i-s.width-s.left}return null}componentDidUpdate(){}render(){return this.props.children}}function d(t){let{children:e,isPresent:i,anchorX:r}=t,o=(0,n.useId)(),a=(0,n.useRef)(null),l=(0,n.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:d}=(0,n.useContext)(h.Q);return(0,n.useInsertionEffect)(()=>{let{width:t,height:e,top:s,left:n,right:h}=l.current;if(i||!a.current||!t||!e)return;a.current.dataset.motionPopId=o;let u=document.createElement("style");return d&&(u.nonce=d),document.head.appendChild(u),u.sheet&&u.sheet.insertRule('\n          [data-motion-pop-id="'.concat(o,'"] {\n            position: absolute !important;\n            width: ').concat(t,"px !important;\n            height: ").concat(e,"px !important;\n            ").concat("left"===r?"left: ".concat(n):"right: ".concat(h),"px !important;\n            top: ").concat(s,"px !important;\n          }\n        ")),()=>{document.head.removeChild(u)}},[i]),(0,s.jsx)(u,{isPresent:i,childRef:a,sizeRef:l,children:n.cloneElement(e,{ref:a})})}let c=t=>{let{children:e,initial:i,isPresent:r,onExitComplete:a,custom:h,presenceAffectsLayout:u,mode:c,anchorX:m}=t,f=(0,o.M)(p),g=(0,n.useId)(),v=!0,y=(0,n.useMemo)(()=>(v=!1,{id:g,initial:i,isPresent:r,custom:h,onExitComplete:t=>{for(let e of(f.set(t,!0),f.values()))if(!e)return;a&&a()},register:t=>(f.set(t,!1),()=>f.delete(t))}),[r,f,a]);return u&&v&&(y={...y}),(0,n.useMemo)(()=>{f.forEach((t,e)=>f.set(e,!1))},[r]),n.useEffect(()=>{r||f.size||!a||a()},[r]),"popLayout"===c&&(e=(0,s.jsx)(d,{isPresent:r,anchorX:m,children:e})),(0,s.jsx)(l.t.Provider,{value:y,children:e})};function p(){return new Map}var m=i(32082);let f=t=>t.key||"";function g(t){let e=[];return n.Children.forEach(t,t=>{(0,n.isValidElement)(t)&&e.push(t)}),e}let v=t=>{let{children:e,custom:i,initial:l=!0,onExitComplete:h,presenceAffectsLayout:u=!0,mode:d="sync",propagate:p=!1,anchorX:v="left"}=t,[y,x]=(0,m.xQ)(p),w=(0,n.useMemo)(()=>g(e),[e]),P=p&&!y?[]:w.map(f),T=(0,n.useRef)(!0),b=(0,n.useRef)(w),S=(0,o.M)(()=>new Map),[A,M]=(0,n.useState)(w),[E,V]=(0,n.useState)(w);(0,a.E)(()=>{T.current=!1,b.current=w;for(let t=0;t<E.length;t++){let e=f(E[t]);P.includes(e)?S.delete(e):!0!==S.get(e)&&S.set(e,!1)}},[E,P.length,P.join("-")]);let C=[];if(w!==A){let t=[...w];for(let e=0;e<E.length;e++){let i=E[e],s=f(i);P.includes(s)||(t.splice(e,0,i),C.push(i))}return"wait"===d&&C.length&&(t=C),V(g(t)),M(w),null}let{forceRender:D}=(0,n.useContext)(r.L);return(0,s.jsx)(s.Fragment,{children:E.map(t=>{let e=f(t),n=(!p||!!y)&&(w===E||P.includes(e));return(0,s.jsx)(c,{isPresent:n,initial:(!T.current||!!l)&&void 0,custom:i,presenceAffectsLayout:u,mode:d,onExitComplete:n?void 0:()=>{if(!S.has(e))return;S.set(e,!0);let t=!0;S.forEach(e=>{e||(t=!1)}),t&&(null==D||D(),V(b.current),p&&(null==x||x()),h&&h())},anchorX:v,children:t},e)})})}},68972:(t,e,i)=>{i.d(e,{B:()=>s});let s="undefined"!=typeof window},80845:(t,e,i)=>{i.d(e,{t:()=>s});let s=(0,i(12115).createContext)(null)},82885:(t,e,i)=>{i.d(e,{M:()=>n});var s=i(12115);function n(t){let e=(0,s.useRef)(null);return null===e.current&&(e.current=t()),e.current}},83945:(t,e,i)=>{i.d(e,{A:()=>r});var s=i(19827);let n=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function r(t,e,i,r){if(t===e&&i===r)return s.l;let o=e=>(function(t,e,i,s,r){let o,a,l=0;do(o=n(a=e+(i-e)/2,s,r)-t)>0?i=a:e=a;while(Math.abs(o)>1e-7&&++l<12);return a})(e,0,1,t,i);return t=>0===t||1===t?t:n(o(t),e,r)}},90869:(t,e,i)=>{i.d(e,{L:()=>s});let s=(0,i(12115).createContext)({})},95233:(t,e,i)=>{i.d(e,{a6:()=>n,am:()=>o,vT:()=>r});var s=i(83945);let n=(0,s.A)(.42,0,1,1),r=(0,s.A)(0,0,.58,1),o=(0,s.A)(.42,0,.58,1)},97494:(t,e,i)=>{i.d(e,{E:()=>n});var s=i(12115);let n=i(68972).B?s.useLayoutEffect:s.useEffect}}]);