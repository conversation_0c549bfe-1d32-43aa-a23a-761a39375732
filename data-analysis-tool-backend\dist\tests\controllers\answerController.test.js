"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const answerController_1 = require("../../controllers/answerController");
const answerRepository_1 = __importDefault(require("../../repositories/answerRepository"));
// Mock the dependencies
jest.mock("../../repositories/answerRepository");
jest.mock("../../utils/validateAnswer", () => ({
    validateInput: jest.fn().mockReturnValue({ valid: true }),
}));
describe("Answer Controller", () => {
    let mockRequest;
    let mockResponse;
    let responseObject = {};
    beforeEach(() => {
        // Reset mocks before each test
        jest.clearAllMocks();
        jest.resetAllMocks();
        // Setup mock response
        mockResponse = {
            status: jest.fn().mockReturnThis(),
            json: jest.fn().mockImplementation((result) => {
                responseObject = result;
                return mockResponse;
            }),
        };
        // Reset response object
        responseObject = {};
    });
    describe("createAnswer", () => {
        beforeEach(() => {
            // Setup request for tests
            mockRequest = {
                body: {
                    submissionId: 1,
                    questionId: 2,
                    value: "Test answer",
                    answerType: "text",
                    questionOptionId: 1, // Changed from null to a number to pass validation
                },
            };
        });
        it("should create an answer successfully", () => __awaiter(void 0, void 0, void 0, function* () {
            // Mock repository response
            const mockAnswer = {
                id: 1,
                formSubmissionId: 1,
                questionId: 2,
                value: "Test answer",
                answerType: "text",
                questionOptionId: 1, // Changed from null to match the request
                createdAt: new Date(),
                updatedAt: new Date(),
            };
            // Mock the validation
            const validateInput = require("../../utils/validateAnswer").validateInput;
            validateInput.mockReturnValue({ valid: true });
            // Mock repository methods - use direct assignment for more reliable mocking
            const findBySubmissionIdQuestionMock = jest.fn().mockResolvedValue(null);
            answerRepository_1.default.findBySubmissionIdQuestion =
                findBySubmissionIdQuestionMock;
            const createAnswerMock = jest.fn().mockResolvedValue(mockAnswer);
            answerRepository_1.default.createAnswer = createAnswerMock;
            yield (0, answerController_1.createAnswer)(mockRequest, mockResponse);
            expect(answerRepository_1.default.findBySubmissionIdQuestion).toHaveBeenCalledWith(1, 2);
            expect(answerRepository_1.default.createAnswer).toHaveBeenCalledWith(expect.objectContaining({
                submissionId: 1,
                questionId: 2,
                value: "Test answer",
                answerType: "text",
            }));
            expect(mockResponse.status).toHaveBeenCalledWith(201);
            expect(responseObject).toHaveProperty("success", true);
            expect(responseObject).toHaveProperty("message", "Answer created successfully");
            expect(responseObject.data).toHaveProperty("answer", mockAnswer);
        }));
        it("should return 400 when answer already exists", () => __awaiter(void 0, void 0, void 0, function* () {
            // Mock existing answer
            const findBySubmissionIdQuestionMock = jest.fn().mockResolvedValue({
                id: 1,
                formSubmissionId: 1,
                questionId: 2,
                answerType: "text", // Add answerType to match the controller's check
            });
            answerRepository_1.default.findBySubmissionIdQuestion =
                findBySubmissionIdQuestionMock;
            // Make sure validation passes
            const validateInput = require("../../utils/validateAnswer").validateInput;
            validateInput.mockReturnValue({ valid: true });
            yield (0, answerController_1.createAnswer)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(400);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "Answer already created");
            expect(answerRepository_1.default.createAnswer).not.toHaveBeenCalled();
        }));
        it("should handle selectmany answers correctly", () => __awaiter(void 0, void 0, void 0, function* () {
            // Setup request for selectmany
            mockRequest = {
                body: {
                    submissionId: 1,
                    questionId: 2,
                    value: ["Option 1", "Option 2"],
                    answerType: "selectmany",
                    questionOptionId: [1, 2],
                },
            };
            // Mock repository responses
            const mockAnswers = [
                {
                    id: 1,
                    formSubmissionId: 1,
                    questionId: 2,
                    value: "Option 1",
                    answerType: "selectmany",
                    questionOptionId: 1,
                },
                {
                    id: 2,
                    formSubmissionId: 1,
                    questionId: 2,
                    value: "Option 2",
                    answerType: "selectmany",
                    questionOptionId: 2,
                },
            ];
            // Make sure validateInput returns valid for this test
            const validateInput = require("../../utils/validateAnswer").validateInput;
            validateInput.mockReturnValue({ valid: true });
            answerRepository_1.default.findBySubmissionIdQuestion.mockResolvedValue(null);
            answerRepository_1.default.createAnswer
                .mockResolvedValueOnce(mockAnswers[0])
                .mockResolvedValueOnce(mockAnswers[1]);
            yield (0, answerController_1.createAnswer)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(201);
            expect(responseObject).toHaveProperty("success", true);
            expect(responseObject).toHaveProperty("message", "Multiple answers created successfully");
            expect(responseObject.data).toHaveProperty("answers");
        }));
        it("should return 400 for invalid selectmany input (mismatched arrays)", () => __awaiter(void 0, void 0, void 0, function* () {
            // Setup request with mismatched arrays
            mockRequest = {
                body: {
                    submissionId: 1,
                    questionId: 2,
                    value: ["Option 1", "Option 2"],
                    answerType: "selectmany",
                    questionOptionId: [1], // Only one option ID but two values
                },
            };
            // Make sure validateInput returns valid for this test
            const validateInput = require("../../utils/validateAnswer").validateInput;
            validateInput.mockReturnValue({ valid: true });
            yield (0, answerController_1.createAnswer)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(400);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "`value` and `questionOptionId` arrays must be the same length");
        }));
        it("should return 400 for invalid input", () => __awaiter(void 0, void 0, void 0, function* () {
            // Missing required fields
            mockRequest.body = { submissionId: 1 };
            yield (0, answerController_1.createAnswer)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(400);
            expect(responseObject).toHaveProperty("errors");
            expect(answerRepository_1.default.createAnswer).not.toHaveBeenCalled();
        }));
        it("should handle server errors", () => __awaiter(void 0, void 0, void 0, function* () {
            // Mock a server error
            const findBySubmissionIdQuestionMock = jest
                .fn()
                .mockRejectedValue(new Error("Database error"));
            answerRepository_1.default.findBySubmissionIdQuestion =
                findBySubmissionIdQuestionMock;
            // Make sure validation passes
            const validateInput = require("../../utils/validateAnswer").validateInput;
            validateInput.mockReturnValue({ valid: true });
            yield (0, answerController_1.createAnswer)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(500);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "Error creating answer");
        }));
    });
    describe("getAnswersBySubmission", () => {
        beforeEach(() => {
            mockRequest = {
                body: {
                    submissionId: "1", // Note: string format to test parsing
                },
            };
        });
        it("should get answers by submission ID successfully", () => __awaiter(void 0, void 0, void 0, function* () {
            const mockAnswers = [
                {
                    id: 1,
                    formSubmissionId: 1,
                    questionId: 2,
                    value: "Answer 1",
                },
                {
                    id: 2,
                    formSubmissionId: 1,
                    questionId: 3,
                    value: "Answer 2",
                },
            ];
            answerRepository_1.default.getAnswersBySubmission.mockResolvedValue(mockAnswers);
            yield (0, answerController_1.getAnswersBySubmission)(mockRequest, mockResponse);
            expect(answerRepository_1.default.getAnswersBySubmission).toHaveBeenCalledWith(1);
            expect(mockResponse.status).toHaveBeenCalledWith(200);
            expect(responseObject).toHaveProperty("success", true);
            expect(responseObject.data).toHaveProperty("answers", mockAnswers);
        }));
        it("should handle server errors", () => __awaiter(void 0, void 0, void 0, function* () {
            answerRepository_1.default.getAnswersBySubmission.mockRejectedValue(new Error("Database error"));
            yield (0, answerController_1.getAnswersBySubmission)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(500);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "error getting answer");
        }));
    });
    describe("getAnswerById", () => {
        beforeEach(() => {
            mockRequest = {
                params: {
                    id: "1", // Note: string format to test parsing
                },
            };
        });
        it("should get answer by ID successfully", () => __awaiter(void 0, void 0, void 0, function* () {
            const mockAnswer = {
                id: 1,
                formSubmissionId: 1,
                questionId: 2,
                value: "Test answer",
            };
            answerRepository_1.default.getAnswerById.mockResolvedValue(mockAnswer);
            yield (0, answerController_1.getAnswerById)(mockRequest, mockResponse);
            expect(answerRepository_1.default.getAnswerById).toHaveBeenCalledWith(1);
            expect(mockResponse.status).toHaveBeenCalledWith(201);
            expect(responseObject).toHaveProperty("success", true);
            expect(responseObject.data).toHaveProperty("answer", mockAnswer);
        }));
        it("should return 404 when answer not found", () => __awaiter(void 0, void 0, void 0, function* () {
            answerRepository_1.default.getAnswerById.mockResolvedValue(null);
            yield (0, answerController_1.getAnswerById)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(404);
            expect(responseObject).toHaveProperty("message", "Answer not found");
        }));
        it("should handle server errors", () => __awaiter(void 0, void 0, void 0, function* () {
            answerRepository_1.default.getAnswerById.mockRejectedValue(new Error("Database error"));
            yield (0, answerController_1.getAnswerById)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(500);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "error getting answer");
        }));
    });
    describe("updateAnswer", () => {
        beforeEach(() => {
            mockRequest = {
                body: {
                    submissionId: 1,
                    questionId: 2,
                    value: "Updated answer",
                    answerType: "text",
                    questionOptionId: 1, // Changed from null to a number to pass validation
                },
            };
        });
        it("should update a single answer successfully", () => __awaiter(void 0, void 0, void 0, function* () {
            // Mock repository response
            const updatedAnswer = {
                id: 1,
                formSubmissionId: 1,
                questionId: 2,
                value: "Updated answer",
                answerType: "text",
                questionOptionId: 1, // Changed from null to match the request
                updatedAt: new Date(),
            };
            // Make sure validation passes
            const validateInput = require("../../utils/validateAnswer").validateInput;
            validateInput.mockReturnValue({ valid: true });
            // Use direct assignment for more reliable mocking
            const updateSingleAnswerMock = jest.fn().mockResolvedValue(updatedAnswer);
            answerRepository_1.default.updateSingleAnswer = updateSingleAnswerMock;
            yield (0, answerController_1.updateAnswer)(mockRequest, mockResponse);
            expect(answerRepository_1.default.updateSingleAnswer).toHaveBeenCalledWith(expect.objectContaining({
                submissionId: 1,
                questionId: 2,
                value: "Updated answer",
                answerType: "text",
            }));
            expect(mockResponse.status).toHaveBeenCalledWith(200);
            expect(responseObject).toHaveProperty("success", true);
            expect(responseObject).toHaveProperty("message", "Answer updated successfully");
            expect(responseObject.data).toHaveProperty("answer", updatedAnswer);
        }));
        it("should update selectmany answers correctly", () => __awaiter(void 0, void 0, void 0, function* () {
            // Setup request for selectmany
            mockRequest = {
                body: {
                    submissionId: 1,
                    questionId: 2,
                    value: ["Updated Option 1", "Updated Option 2"],
                    answerType: "selectmany",
                    questionOptionId: [1, 2],
                },
            };
            const mockUpdatedAnswers = [
                {
                    id: 1,
                    formSubmissionId: 1,
                    questionId: 2,
                    value: "Updated Option 1",
                    answerType: "selectmany",
                    questionOptionId: 1,
                },
                {
                    id: 2,
                    formSubmissionId: 1,
                    questionId: 2,
                    value: "Updated Option 2",
                    answerType: "selectmany",
                    questionOptionId: 2,
                },
            ];
            // Make sure validation passes
            const validateInput = require("../../utils/validateAnswer").validateInput;
            validateInput.mockReturnValue({ valid: true });
            answerRepository_1.default.updateSelectManyAnswers.mockResolvedValue(mockUpdatedAnswers);
            yield (0, answerController_1.updateAnswer)(mockRequest, mockResponse);
            expect(answerRepository_1.default.updateSelectManyAnswers).toHaveBeenCalledWith(1, 2, ["Updated Option 1", "Updated Option 2"], [1, 2]);
            expect(mockResponse.status).toHaveBeenCalledWith(200);
            expect(responseObject).toHaveProperty("success", true);
            expect(responseObject).toHaveProperty("message", "select_many answers updated");
        }));
        it("should return 400 for invalid input", () => __awaiter(void 0, void 0, void 0, function* () {
            // Missing required fields
            mockRequest.body = { submissionId: 1 };
            yield (0, answerController_1.updateAnswer)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(400);
            expect(responseObject).toHaveProperty("errors");
            expect(answerRepository_1.default.updateSingleAnswer).not.toHaveBeenCalled();
        }));
        it("should handle server errors", () => __awaiter(void 0, void 0, void 0, function* () {
            // Mock server error
            const updateSingleAnswerMock = jest
                .fn()
                .mockRejectedValue(new Error("Database error"));
            answerRepository_1.default.updateSingleAnswer = updateSingleAnswerMock;
            // Make sure validation passes
            const validateInput = require("../../utils/validateAnswer").validateInput;
            validateInput.mockReturnValue({ valid: true });
            yield (0, answerController_1.updateAnswer)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(500);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "Error updating answer");
        }));
        it("should return 400 for invalid selectmany input (not an array)", () => __awaiter(void 0, void 0, void 0, function* () {
            // Setup request with invalid value (not an array)
            mockRequest = {
                body: {
                    submissionId: 1,
                    questionId: 2,
                    value: "Not an array",
                    answerType: "selectmany",
                    questionOptionId: [1, 2],
                },
            };
            // Mock validateInput to return valid for this test
            const validateInput = require("../../utils/validateAnswer").validateInput;
            validateInput.mockReturnValue({ valid: true });
            yield (0, answerController_1.updateAnswer)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(400);
            expect(responseObject).toHaveProperty("message", "`value` must be an array for select_many");
        }));
    });
    describe("deleteAnswer", () => {
        beforeEach(() => {
            mockRequest = {
                body: {
                    submissionId: 1,
                    questionId: 2,
                    answerType: "text",
                },
            };
        });
        it("should delete an answer successfully", () => __awaiter(void 0, void 0, void 0, function* () {
            const mockDeletedAnswer = { id: 1 };
            answerRepository_1.default.deleteAnswersBySubmissionIdAndQuestionId.mockResolvedValue(mockDeletedAnswer);
            yield (0, answerController_1.deleteAnswer)(mockRequest, mockResponse);
            expect(answerRepository_1.default.deleteAnswersBySubmissionIdAndQuestionId).toHaveBeenCalledWith(1, 2, "text");
            expect(mockResponse.status).toHaveBeenCalledWith(200);
            expect(responseObject).toHaveProperty("success", true);
            expect(responseObject).toHaveProperty("message", "Answer(s) deleted successfully");
            expect(responseObject.data).toEqual(mockDeletedAnswer);
        }));
        it("should return 400 for invalid input", () => __awaiter(void 0, void 0, void 0, function* () {
            // Missing required fields
            mockRequest.body = { submissionId: "not a number" };
            yield (0, answerController_1.deleteAnswer)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(400);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "submissionId, questionId and answerType are required");
            expect(answerRepository_1.default.deleteAnswersBySubmissionIdAndQuestionId).not.toHaveBeenCalled();
        }));
        it("should handle server errors", () => __awaiter(void 0, void 0, void 0, function* () {
            answerRepository_1.default.deleteAnswersBySubmissionIdAndQuestionId.mockRejectedValue(new Error("Database error"));
            yield (0, answerController_1.deleteAnswer)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(500);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "Error deleting answer");
        }));
        it("should handle when no answer is found to delete", () => __awaiter(void 0, void 0, void 0, function* () {
            answerRepository_1.default.deleteAnswersBySubmissionIdAndQuestionId.mockResolvedValue(null);
            yield (0, answerController_1.deleteAnswer)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(200);
            expect(responseObject).toHaveProperty("success", true);
            expect(responseObject).toHaveProperty("message", "Answer(s) deleted successfully");
        }));
    });
});
