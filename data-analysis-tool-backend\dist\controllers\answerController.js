"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateMultipleAnswers = exports.submitMultipleAnswer = exports.deleteAnswer = exports.updateAnswer = exports.getAnswerById = exports.getAnswersBySubmission = exports.createAnswer = void 0;
const answerRepository_1 = __importDefault(require("../repositories/answerRepository"));
const answerValidator_1 = require("../validators/answerValidator");
const validateAnswer_1 = require("../utils/validateAnswer");
const formSubmissionRepository_1 = __importDefault(require("../repositories/formSubmissionRepository"));
const axios_1 = __importDefault(require("axios"));
const useragent_1 = __importDefault(require("useragent"));
const questionRepository_1 = __importDefault(require("../repositories/questionRepository"));
const getLocationByIP = (ip) => __awaiter(void 0, void 0, void 0, function* () {
    const response = yield axios_1.default.get(`https://ipapi.co/${ip}/json/`);
    const { city, region, country_name } = response.data;
    return `${city}, ${region}, ${country_name}`;
});
const createAnswer = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const result = answerValidator_1.createAnswerSchema.safeParse(req.body);
        if (!result.success) {
            return res.status(400).json({
                success: false,
                errors: result.error.flatten().fieldErrors,
            });
        }
        const validatedData = result.data;
        const { submissionId, questionId, answerType, value, questionOptionId } = validatedData;
        // Check for duplicate entry unless it's selectmany
        const existance = yield answerRepository_1.default.findBySubmissionIdQuestion(submissionId, questionId);
        if (existance && answerType !== "selectmany") {
            return res.status(400).json({
                success: false,
                message: "Answer already created",
            });
        }
        // Validate input
        const validation = (0, validateAnswer_1.validateInput)(answerType, value);
        if (!validation.valid) {
            return res.status(400).json({
                success: false,
                message: "Validation error",
                errors: validation.errors,
            });
        }
        // Handle selectmany
        if (answerType === "selectmany") {
            if (!Array.isArray(value)) {
                return res.status(400).json({
                    success: false,
                    message: "`value` must be an array for selectmany answers",
                });
            }
            if (!Array.isArray(questionOptionId)) {
                return res.status(400).json({
                    success: false,
                    message: "`questionOptionId` must be an array for selectmany answers",
                });
            }
            if (value.length !== questionOptionId.length) {
                return res.status(400).json({
                    success: false,
                    message: "`value` and `questionOptionId` arrays must be the same length",
                });
            }
            const createdAnswers = [];
            for (let i = 0; i < value.length; i++) {
                const singleAnswer = Object.assign(Object.assign({}, validatedData), { value: String(value[i]), questionOptionId: questionOptionId[i] });
                const answer = yield answerRepository_1.default.createAnswer(singleAnswer);
                createdAnswers.push(answer);
            }
            return res.status(201).json({
                success: true,
                message: "Multiple answers created successfully",
                data: { answers: createdAnswers },
            });
        }
        // Handle single-value answers
        if (typeof value !== "string") {
            validatedData.value = String(value);
        }
        const answer = yield answerRepository_1.default.createAnswer(validatedData);
        return res.status(201).json({
            success: true,
            message: "Answer created successfully",
            data: { answer },
        });
    }
    catch (error) {
        return res.status(500).json({
            success: false,
            message: "Error creating answer",
            error: error instanceof Error ? error.message : "Unexpected error",
        });
    }
});
exports.createAnswer = createAnswer;
const getAnswersBySubmission = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { submissionId } = req.body;
        const answers = yield answerRepository_1.default.getAnswersBySubmission(parseInt(submissionId));
        return res.status(200).json({
            success: true,
            message: "success getting answer by submission id",
            data: { answers },
        });
    }
    catch (error) {
        return res.status(500).json({
            success: false,
            message: "error getting answer",
            error: error instanceof Error ? error.message : "unexpected error",
        });
    }
});
exports.getAnswersBySubmission = getAnswersBySubmission;
const getAnswerById = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = req.params;
        const answer = yield answerRepository_1.default.getAnswerById(parseInt(id));
        if (!answer) {
            return res.status(404).json({ message: "Answer not found" });
        }
        return res.status(201).json({
            success: true,
            message: "success getting answer by id",
            data: { answer },
        });
    }
    catch (error) {
        return res.status(500).json({
            success: false,
            message: "error getting answer",
            error: error instanceof Error ? error.message : "unexpected error",
        });
    }
});
exports.getAnswerById = getAnswerById;
const updateAnswer = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const result = answerValidator_1.createAnswerSchema.safeParse(req.body);
        if (!result.success) {
            return res.status(400).json({
                success: false,
                errors: result.error.flatten().fieldErrors,
            });
        }
        const validatedData = result.data;
        const { submissionId, questionId, answerType, value, questionOptionId } = validatedData;
        const validation = (0, validateAnswer_1.validateInput)(answerType, value);
        if (!validation.valid) {
            return res.status(400).json({
                success: false,
                message: "Validation error",
                errors: validation.errors,
            });
        }
        // Handle select_many: delete old answers and create new ones
        if (answerType === "selectmany") {
            if (!Array.isArray(value)) {
                return res.status(400).json({
                    success: false,
                    message: "`value` must be an array for select_many",
                });
            }
            const answerOptionIds = Array.isArray(questionOptionId)
                ? questionOptionId
                : [];
            const updatedAnswers = yield answerRepository_1.default.updateSelectManyAnswers(submissionId, questionId, value, answerOptionIds);
            return res.status(200).json({
                success: true,
                message: "select_many answers updated",
                data: { answers: updatedAnswers },
            });
        }
        // Handle all other answer types
        const updatedAnswer = yield answerRepository_1.default.updateSingleAnswer(validatedData);
        return res.status(200).json({
            success: true,
            message: "Answer updated successfully",
            data: { answer: updatedAnswer },
        });
    }
    catch (error) {
        return res.status(500).json({
            success: false,
            message: "Error updating answer",
            error: error instanceof Error ? error.message : "Unexpected error",
        });
    }
});
exports.updateAnswer = updateAnswer;
const deleteAnswer = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { submissionId, questionId, answerType } = req.body;
        if (!submissionId ||
            !questionId ||
            !answerType ||
            typeof submissionId !== "number" ||
            typeof questionId !== "number") {
            return res.status(400).json({
                success: false,
                message: "submissionId, questionId and answerType are required",
            });
        }
        const deleted = yield answerRepository_1.default.deleteAnswersBySubmissionIdAndQuestionId(submissionId, questionId, answerType);
        return res.status(200).json({
            success: true,
            message: "Answer(s) deleted successfully",
            data: deleted,
        });
    }
    catch (error) {
        return res.status(500).json({
            success: false,
            message: "Error deleting answer",
            error: error instanceof Error ? error.message : "Unexpected error",
        });
    }
});
exports.deleteAnswer = deleteAnswer;
const submitMultipleAnswer = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
        if (!userId) {
            return res.status(401).json({
                success: false,
                message: "Unauthorized",
            });
        }
        const result = answerValidator_1.answerArraySchema.safeParse(req.body);
        if (!result.success) {
            return res.status(400).json({
                success: false,
                errors: result.error.flatten().fieldErrors,
            });
        }
        const validatedData = result.data;
        const formSubmissionData = {
            projectId: validatedData[0].projectId,
        };
        const agent = useragent_1.default.parse(req.headers["user-agent"]);
        const deviceInfo = `${agent.family} on ${agent.os}`;
        const ip = req.ip;
        const rawLocation = yield getLocationByIP(ip);
        const location = rawLocation !== null && rawLocation !== void 0 ? rawLocation : undefined;
        const formSubmission = yield formSubmissionRepository_1.default.create(formSubmissionData, userId, deviceInfo, location);
        const transformed = validatedData.flatMap((a) => {
            var _a;
            if (a.answerType === "selectmany" && Array.isArray(a.questionOptionId)) {
                const values = a.value
                    ? String(a.value)
                        .split(", ")
                        .filter((v) => v)
                    : [];
                return a.questionOptionId.map((optionId, index) => {
                    var _a;
                    return ({
                        formSubmissionId: formSubmission.id,
                        questionId: a.questionId,
                        value: values[index] || "",
                        answerType: a.answerType,
                        imageUrl: a.imageUrl,
                        questionOptionId: optionId,
                        isOtherOption: (_a = a.isOtherOption) !== null && _a !== void 0 ? _a : false,
                    });
                });
            }
            else {
                // Handle other answer types
                return [
                    {
                        formSubmissionId: formSubmission.id,
                        questionId: a.questionId,
                        value: typeof a.value !== "undefined" ? String(a.value) : "",
                        answerType: a.answerType,
                        imageUrl: a.imageUrl,
                        questionOptionId: Array.isArray(a.questionOptionId)
                            ? undefined
                            : a.questionOptionId,
                        isOtherOption: (_a = a.isOtherOption) !== null && _a !== void 0 ? _a : false,
                    },
                ];
            }
        });
        const answer = yield answerRepository_1.default.AddMultipleAnswer(transformed);
        return res.status(200).json({
            success: true,
            message: "answer created success",
            data: { answer },
        });
    }
    catch (error) {
        return res.status(500).json({
            success: false,
            message: "error creating answers",
            error: error instanceof Error ? error.message : "unexpected error",
        });
    }
});
exports.submitMultipleAnswer = submitMultipleAnswer;
const updateMultipleAnswers = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const validationResult = answerValidator_1.updateMultipleAnswerSchema.safeParse(req.body);
        if (!validationResult.success) {
            return res.status(400).json({
                success: false,
                message: "Validation failed",
                errors: validationResult.error.flatten(),
            });
        }
        const inputAnswer = validationResult.data;
        // Split into updates and creates
        const updates = inputAnswer.filter((ans) => ans.id !== undefined);
        const creates = inputAnswer.filter((ans) => ans.id === undefined && ans.value !== undefined);
        // Filter out duplicates from create list
        const filteredCreates = [];
        for (const ans of creates) {
            const existance = yield questionRepository_1.default.findById(ans === null || ans === void 0 ? void 0 : ans.questionId);
            if (!existance) {
                return res.status(404).json({ message: "question id not found" });
            }
            const isDuplicate = yield answerRepository_1.default.findDuplicateAnswer(ans.formSubmissionId, ans.questionId, ans.answerType);
            if (!isDuplicate) {
                filteredCreates.push({
                    formSubmissionId: ans.formSubmissionId,
                    questionId: ans.questionId,
                    value: ans.value,
                    answerType: ans.answerType,
                    imageUrl: ans.imageUrl,
                    questionOptionId: ans.questionOptionId,
                    isOtherOption: ans.isOtherOption,
                });
            }
        }
        // Perform update
        const updateAnswers = updates.length > 0
            ? yield answerRepository_1.default.UpdateMultipleAnswers(updates)
            : [];
        // Perform create
        const createdAnswers = filteredCreates.length > 0
            ? yield answerRepository_1.default.AddMultipleAnswer(filteredCreates)
            : [];
        const allAnswers = [...updateAnswers, ...createdAnswers];
        return res.status(200).json({
            success: true,
            message: "Answers updated successfully",
            data: allAnswers,
        });
    }
    catch (error) {
        console.error("Error updating multiple answers:", error);
        return res.status(500).json({
            success: false,
            message: "Something went wrong while updating answers",
            error: error.message,
        });
    }
});
exports.updateMultipleAnswers = updateMultipleAnswers;
