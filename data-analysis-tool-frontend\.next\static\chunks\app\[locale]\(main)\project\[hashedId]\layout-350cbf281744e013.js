(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5746],{381:(e,r,a)=>{"use strict";a.d(r,{A:()=>s});let s=(0,a(19946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},10602:(e,r,a)=>{Promise.resolve().then(a.bind(a,93429))},17652:(e,r,a)=>{"use strict";a.d(r,{c3:()=>l});var s=a(46453);function t(e,r){return(...e)=>{try{return r(...e)}catch{throw Error(void 0)}}}let l=t(0,s.c3);t(0,s.kc)},25784:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});let s=a(23464).A.create({baseURL:"http://localhost:4000/api",headers:{"Content-Type":"application/json"},withCredentials:!0});s.interceptors.request.use(e=>e,e=>Promise.reject(e)),s.interceptors.response.use(e=>e,e=>("ERR_NETWORK"===e.code&&console.error("Network error - Please check if the backend server is running"),Promise.reject(e)));let t=s},35169:(e,r,a)=>{"use strict";a.d(r,{A:()=>s});let s=(0,a(19946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},39716:(e,r,a)=>{"use strict";a.d(r,{F:()=>d});var s=a(95155),t=a(35695),l=a(6874),i=a.n(l);a(12115);let d=e=>{let{items:r}=e,a=(0,t.usePathname)(),l=e=>a.startsWith(e);return(0,s.jsx)("div",{className:"border-y border-neutral-400 rounded-md bg-primary-500 my-4 shadow-md",children:(0,s.jsx)("div",{className:"flex items-center",children:r.map(e=>e.disabled?(0,s.jsxs)("div",{className:"flex items-center gap-2 font-medium transition-all duration-300 p-4 border-b-2 text-gray-400 cursor-not-allowed",children:[e.icon,e.label]},e.route):(0,s.jsxs)(i(),{href:e.route,className:"flex items-center gap-2 font-medium transition-all duration-300 p-4 border-b-2 text-neutral-100 ".concat(l(e.route)?"border-neutral-100":"border-transparent hover:border-neutral-400"),children:[e.icon,e.label]},e.route))})})}},49992:(e,r,a)=>{"use strict";a.d(r,{A:()=>s});let s=(0,a(19946).A)("file-pen",[["path",{d:"M12.5 22H18a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v9.5",key:"1couwa"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M13.378 15.626a1 1 0 1 0-3.004-3.004l-5.01 5.012a2 2 0 0 0-.506.854l-.837 2.87a.5.5 0 0 0 .62.62l2.87-.837a2 2 0 0 0 .854-.506z",key:"1y4qbx"}]])},57799:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});var s=a(95155);a(12115);let t=()=>(0,s.jsx)("div",{className:"w-full flex items-center justify-center",children:(0,s.jsx)("div",{className:"size-8 rounded-full border-x-2 border-primary-500 animate-spin my-16"})})},93429:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>w});var s=a(95155),t=a(57799),l=a(19946);let i=(0,l.A)("panels-top-left",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M9 21V9",key:"1oto5p"}]]);var d=a(49992);let c=(0,l.A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]);var n=a(381),o=a(35695),u=a(39716),m=a(17652);let h=e=>{let{permissions:r}=e,{hashedId:a}=(0,o.useParams)(),t=(0,m.c3)(),l=r.manageProject,h=l||r.viewForm||r.editForm,p=l||r.viewSubmissions||r.editSubmissions||r.addSubmissions||r.deleteSubmissions,x=[{label:t("overview"),icon:(0,s.jsx)(i,{size:16}),route:"/project/".concat(a,"/overview"),disabled:!1},{label:t("formBuilder"),icon:(0,s.jsx)(d.A,{size:16}),route:"/project/".concat(a,"/form-builder"),disabled:!h},{label:t("data"),icon:(0,s.jsx)(c,{size:16}),route:"/project/".concat(a,"/data"),disabled:!p},{label:t("settings"),icon:(0,s.jsx)(n.A,{size:16}),route:"/project/".concat(a,"/settings"),disabled:!l}];return(0,s.jsx)(u.F,{items:x})};var p=a(29350),x=a(86817),b=a(77361),v=a(88570),j=a(19373),y=a(35169),f=a(6874),k=a.n(f),N=a(12115);let w=e=>{let{children:r}=e,{hashedId:a}=(0,o.useParams)(),{user:l}=(0,p.A)(),i=(0,m.c3)(),d=(0,N.useMemo)(()=>(0,v.D)(a),[a]),{data:c,isLoading:n,isError:u}=(0,j.I)({queryKey:["projects",null==l?void 0:l.id,d],queryFn:()=>(0,b.kf)({projectId:d}),enabled:!!d&&!!(null==l?void 0:l.id)}),f=(0,x.F)({projectData:c,user:l});return a&&null!==d?n?(0,s.jsx)(t.A,{}):u?(0,s.jsx)("p",{className:"text-red-500",children:i("fetchProjectFailed")}):(0,s.jsxs)("div",{className:"section flex flex-col gap-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("h1",{className:"heading-text capitalize",children:null==c?void 0:c.name}),(0,s.jsxs)(k(),{href:"/dashboard",className:"flex items-center gap-2",children:[(0,s.jsx)(y.A,{size:16}),i("backToDashboard")]})]}),(0,s.jsx)(h,{permissions:f}),(0,s.jsx)("div",{className:"px-8",children:r})]}):(0,s.jsxs)("div",{className:"error-message",children:[(0,s.jsx)("h1",{className:"text-red-500",children:i("invalidProjectIdError")}),(0,s.jsx)("p",{className:"text-neutral-700",children:i("invalidProjectIdMessage")})]})}}},e=>{var r=r=>e(e.s=r);e.O(0,[6453,635,1111,6967,9373,6874,2137,8441,1684,7358],()=>r(10602)),_N_E=e.O()}]);