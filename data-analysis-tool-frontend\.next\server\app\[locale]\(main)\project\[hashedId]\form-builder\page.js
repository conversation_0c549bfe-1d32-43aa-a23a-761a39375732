(()=>{var e={};e.id=2998,e.ids=[2998],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7182:(e,t,s)=>{Promise.resolve().then(s.bind(s,90541))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19991:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>h});var r=s(60687),o=s(48661),a=s(43210),i=s(29494),n=s(16189),l=s(6986),d=s(88678),c=s(78407),p=s(71845),u=s(21650),m=s(77618);function h(){let[e,t]=(0,a.useState)(!1),{hashedId:s}=(0,n.useParams)(),{user:h}=(0,u.A)(),x=Number((0,l.D)(s)),b=(0,m.c3)(),{data:f}=(0,i.I)({queryKey:["projects",h?.id,x],queryFn:()=>(0,p.kf)({projectId:x}),enabled:!!x&&!!h?.id}),j=(0,c.F)({projectData:f,user:h});return s&&null!==x?(0,r.jsx)("div",{className:"p-6",children:e?(0,r.jsx)(o.F,{projectId:x,onClose:()=>t(!1),hashedId:s}):(0,r.jsx)(d.o,{setIsPreviewMode:t,contextType:"project",contextId:x,permissions:j})}):(0,r.jsxs)("div",{className:"error-message",children:[(0,r.jsx)("h1",{className:"text-red-500",children:b("invalidProjectId")}),(0,r.jsx)("p",{className:"text-neutral-700",children:b("invalidProjectUrl")})]})}},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},37446:(e,t,s)=>{Promise.resolve().then(s.bind(s,51129))},42389:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Kobo-project\\\\data-analysis-tool-frontend\\\\app\\\\[locale]\\\\(main)\\\\project\\\\[hashedId]\\\\form-builder\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\project\\[hashedId]\\form-builder\\page.tsx","default")},51129:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Kobo-project\\\\data-analysis-tool-frontend\\\\app\\\\[locale]\\\\(main)\\\\project\\\\[hashedId]\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\project\\[hashedId]\\layout.tsx","default")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},61611:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},74363:(e,t,s)=>{Promise.resolve().then(s.bind(s,42389))},78407:(e,t,s)=>{"use strict";s.d(t,{F:()=>o});var r=s(43210);let o=({projectData:e,user:t})=>(0,r.useMemo)(()=>{let s=t?.id===e?.user?.id,r=e?.projectUser?.[0],o=r?.permission||{};return{viewForm:s||o.viewForm||!1,editForm:s||o.editForm||!1,viewSubmissions:s||o.viewSubmissions||!1,addSubmissions:s||o.addSubmissions||!1,deleteSubmissions:s||o.deleteSubmissions||!1,validateSubmissions:s||o.validateSubmissions||!1,editSubmissions:s||o.editSubmissions||!1,manageProject:s||o.manageProject||!1}},[t?.id,e])},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},82236:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d});var r=s(65239),o=s(48088),a=s(88170),i=s.n(a),n=s(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);s.d(t,l);let d={children:["",{children:["[locale]",{children:["(main)",{children:["project",{children:["[hashedId]",{children:["form-builder",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,42389)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\project\\[hashedId]\\form-builder\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,51129)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\project\\[hashedId]\\layout.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,84606)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,72121)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,58014)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\project\\[hashedId]\\form-builder\\page.tsx"],p={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/[locale]/(main)/project/[hashedId]/form-builder/page",pathname:"/[locale]/project/[hashedId]/form-builder",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},83997:e=>{"use strict";e.exports=require("tty")},84027:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},84979:(e,t,s)=>{Promise.resolve().then(s.bind(s,19991))},86757:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("panels-top-left",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M9 21V9",key:"1oto5p"}]])},90541:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>g});var r=s(60687),o=s(86429),a=s(86757),i=s(17090),n=s(61611),l=s(84027),d=s(16189),c=s(20174),p=s(77618);let u=({permissions:e})=>{let{hashedId:t}=(0,d.useParams)(),s=(0,p.c3)(),o=e.manageProject,u=o||e.viewForm||e.editForm,m=o||e.viewSubmissions||e.editSubmissions||e.addSubmissions||e.deleteSubmissions,h=[{label:s("overview"),icon:(0,r.jsx)(a.A,{size:16}),route:`/project/${t}/overview`,disabled:!1},{label:s("formBuilder"),icon:(0,r.jsx)(i.A,{size:16}),route:`/project/${t}/form-builder`,disabled:!u},{label:s("data"),icon:(0,r.jsx)(n.A,{size:16}),route:`/project/${t}/data`,disabled:!m},{label:s("settings"),icon:(0,r.jsx)(l.A,{size:16}),route:`/project/${t}/settings`,disabled:!o}];return(0,r.jsx)(c.F,{items:h})};var m=s(21650),h=s(78407),x=s(71845),b=s(6986),f=s(29494),j=s(28559),v=s(85814),y=s.n(v),P=s(43210);let g=({children:e})=>{let{hashedId:t}=(0,d.useParams)(),{user:s}=(0,m.A)(),a=(0,p.c3)(),i=(0,P.useMemo)(()=>(0,b.D)(t),[t]),{data:n,isLoading:l,isError:c}=(0,f.I)({queryKey:["projects",s?.id,i],queryFn:()=>(0,x.kf)({projectId:i}),enabled:!!i&&!!s?.id}),v=(0,h.F)({projectData:n,user:s});return t&&null!==i?l?(0,r.jsx)(o.A,{}):c?(0,r.jsx)("p",{className:"text-red-500",children:a("fetchProjectFailed")}):(0,r.jsxs)("div",{className:"section flex flex-col gap-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h1",{className:"heading-text capitalize",children:n?.name}),(0,r.jsxs)(y(),{href:"/dashboard",className:"flex items-center gap-2",children:[(0,r.jsx)(j.A,{size:16}),a("backToDashboard")]})]}),(0,r.jsx)(u,{permissions:v}),(0,r.jsx)("div",{className:"px-8",children:e})]}):(0,r.jsxs)("div",{className:"error-message",children:[(0,r.jsx)("h1",{className:"text-red-500",children:a("invalidProjectIdError")}),(0,r.jsx)("p",{className:"text-neutral-700",children:a("invalidProjectIdMessage")})]})}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,7404,1658,6560,8610,5374,2198,5814,3851,8581,4921,6886,8159,5841,5041,4072,695],()=>s(82236));module.exports=r})();