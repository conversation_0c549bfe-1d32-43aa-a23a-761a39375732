"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeleteMultipleLibraryTemplate = exports.fetchQuestionForLibraryTemplate = exports.getLibraryTemplateById = exports.deleteLibraryTemplate = exports.updateLibraryTemplate = exports.getAllLibraryTemplates = exports.createLibraryTemplate = void 0;
const ApiResponse_1 = require("../utils/ApiResponse");
const libraryTemplateValidators_1 = require("../validators/libraryTemplateValidators");
const libraryTemplateRepository_1 = __importDefault(require("../repositories/libraryTemplateRepository"));
const createLibraryTemplate = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const result = libraryTemplateValidators_1.LibraryTemplateSchema.safeParse(req.body);
        if (!result.success) {
            res.status(400).json({
                success: false,
                message: result.error.flatten().fieldErrors,
            });
            return;
        }
        const { name, description, sector, country } = result.data;
        const existingLibraryTemplate = yield libraryTemplateRepository_1.default.findByName(name, userId);
        if (existingLibraryTemplate) {
            res
                .status(400)
                .json({ success: false, message: "library template already exists" });
            return;
        }
        const libraryTemplate = yield libraryTemplateRepository_1.default.create({
            name,
            description,
            sector,
            userId,
            country,
        });
        res.status(201).json({
            message: "Library template created successfully",
            libraryTemplate,
        });
        return;
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: "error creating library template",
            error: error.message,
        });
        return;
    }
});
exports.createLibraryTemplate = createLibraryTemplate;
const getAllLibraryTemplates = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const id = Number(req.user.id);
        if (!id) {
            res.status(404).json({
                success: false,
                message: "user id not found",
            });
            return;
        }
        const templates = yield libraryTemplateRepository_1.default.findAll(id);
        res.status(200).json({
            message: "Successfully fetched all library templates",
            templates,
        });
        return;
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: "error getting library templates",
            error: error.message,
        });
        return;
    }
});
exports.getAllLibraryTemplates = getAllLibraryTemplates;
const updateLibraryTemplate = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const id = Number(req.params.id);
    try {
        const userId = req.user.id;
        const libraryTemplateUser = yield libraryTemplateRepository_1.default.findLibraryTemplateByIdAndUser(id, userId);
        if (!libraryTemplateUser) {
            res.status(404).json({
                success: false,
                message: "no library template found",
            });
            return;
        }
        const result = libraryTemplateValidators_1.LibraryTemplateSchema.safeParse(req.body);
        if (!result.success) {
            res.status(400).json({
                success: false,
                message: "Validation failed",
                error: result.error.flatten(),
            });
            return;
        }
        const updatedData = result.data;
        const updatedLibraryTemplate = yield libraryTemplateRepository_1.default.updateById(id, updatedData);
        res
            .status(200)
            .json(new ApiResponse_1.ApiResponse(200, { updatedLibraryTemplate }, "Library template updated successfully"));
        return;
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: "error updating library template",
            error: error.message,
        });
        return;
    }
});
exports.updateLibraryTemplate = updateLibraryTemplate;
const deleteLibraryTemplate = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const id = Number(req.params.id);
    try {
        const userId = req.user.id;
        const libraryTemplate = yield libraryTemplateRepository_1.default.findLibraryTemplateByIdAndUser(id, userId);
        if (!libraryTemplate) {
            res.status(404).json({
                success: false,
                message: "no library template found with given id",
            });
            return;
        }
        yield libraryTemplateRepository_1.default.deleteLibraryTemplate(id);
        res
            .status(200)
            .json(new ApiResponse_1.ApiResponse(200, {}, "library template deleted successfully"));
        return;
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: "error deleting library template",
            error: error.message,
        });
        return;
    }
});
exports.deleteLibraryTemplate = deleteLibraryTemplate;
const getLibraryTemplateById = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const id = Number(req.params.id);
    try {
        const userId = req.user.id;
        const template = yield libraryTemplateRepository_1.default.findLibraryTemplateByIdAndUser(id, userId);
        if (!template) {
            res.status(404).json({
                success: false,
                message: "library template not found",
            });
            return;
        }
        res.status(200).json({
            message: "Successfully fetched library template",
            template,
        });
        return;
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: "error getting library template by id",
            error: error.message,
        });
        return;
    }
});
exports.getLibraryTemplateById = getLibraryTemplateById;
const fetchQuestionForLibraryTemplate = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const id = Number(req.params.id);
        if (!id) {
            return res.status(400).json({
                message: "id not found",
            });
        }
        const library = yield libraryTemplateRepository_1.default.findLibraryWithQuestionGroupAndQuestion(id);
        return res.status(200).json({
            success: true,
            message: "library question fetched success",
            data: { library },
        });
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: "error getting library template questions",
            error: error.message,
        });
        return;
    }
});
exports.fetchQuestionForLibraryTemplate = fetchQuestionForLibraryTemplate;
const DeleteMultipleLibraryTemplate = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const result = libraryTemplateValidators_1.deleteMultipleLibraryTemplateSchema.safeParse(req.body);
        if (!result.success) {
            return res.status(400).json({
                success: false,
                message: "Validation failed",
                error: result.error.flatten(),
            });
        }
        const updatedData = result.data;
        const deleted = yield libraryTemplateRepository_1.default.deleteMultipleLibraryTemplate(updatedData.templateIds);
        return res.status(200).json({
            success: false,
            message: "library template deleted success",
        });
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: "error deleting libray template",
            error: error instanceof Error ? error.message : "unexpected error",
        });
        return;
    }
});
exports.DeleteMultipleLibraryTemplate = DeleteMultipleLibraryTemplate;
