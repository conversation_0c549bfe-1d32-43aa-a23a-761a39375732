"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.libraryQuestionSchema = exports.libraryQuestionConditionSchema = exports.libraryQuestionOptionSchema = void 0;
const zod_1 = require("zod");
const client_1 = require("@prisma/client");
exports.libraryQuestionOptionSchema = zod_1.z.object({
    id: zod_1.z.number().optional(),
    label: zod_1.z.string().min(1, "Option label is required"),
    code: zod_1.z.string().min(1, "Option code is required"),
    nextLibraryQuestionId: zod_1.z.number().optional().nullable(),
});
exports.libraryQuestionConditionSchema = zod_1.z.object({
    id: zod_1.z.number().optional(),
    operator: zod_1.z.nativeEnum(client_1.Operator, {
        errorMap: () => ({ message: "Invalid operator selected" }),
    }),
    value: zod_1.z.string().min(1, "Condition value is required"),
});
exports.libraryQuestionSchema = zod_1.z.object({
    label: zod_1.z.string().min(1, "Question label is required"),
    inputType: zod_1.z.nativeEnum(client_1.InputType, {
        errorMap: () => ({ message: "Invalid input type selected" }),
    }),
    hint: zod_1.z.string().optional(),
    placeholder: zod_1.z.string().optional(),
    isRequired: zod_1.z.boolean(),
    position: zod_1.z.number().int().positive("Position must be a positive number"),
    questionOptions: zod_1.z.array(exports.libraryQuestionOptionSchema).optional(),
    conditions: zod_1.z.array(exports.libraryQuestionConditionSchema).optional(),
});
