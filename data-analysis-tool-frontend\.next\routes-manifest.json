{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/edit-submission/[hashedId]/[submissionId]", "regex": "^/edit\\-submission/([^/]+?)/([^/]+?)(?:/)?$", "routeKeys": {"nxtPhashedId": "nxtPhashedId", "nxtPsubmissionId": "nxtPsubmissionId"}, "namedRegex": "^/edit\\-submission/(?<nxtPhashedId>[^/]+?)/(?<nxtPsubmissionId>[^/]+?)(?:/)?$"}, {"page": "/form-submission/[hashedId]", "regex": "^/form\\-submission/([^/]+?)(?:/)?$", "routeKeys": {"nxtPhashedId": "nxtPhashedId"}, "namedRegex": "^/form\\-submission/(?<nxtPhashedId>[^/]+?)(?:/)?$"}, {"page": "/form-submission/[hashedId]/sign-in", "regex": "^/form\\-submission/([^/]+?)/sign\\-in(?:/)?$", "routeKeys": {"nxtPhashedId": "nxtPhashedId"}, "namedRegex": "^/form\\-submission/(?<nxtPhashedId>[^/]+?)/sign\\-in(?:/)?$"}, {"page": "/[locale]", "regex": "^/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)(?:/)?$"}, {"page": "/[locale]/account/profile", "regex": "^/([^/]+?)/account/profile(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/account/profile(?:/)?$"}, {"page": "/[locale]/account/security", "regex": "^/([^/]+?)/account/security(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/account/security(?:/)?$"}, {"page": "/[locale]/dashboard", "regex": "^/([^/]+?)/dashboard(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/dashboard(?:/)?$"}, {"page": "/[locale]/dashboard/[status]/not-available", "regex": "^/([^/]+?)/dashboard/([^/]+?)/not\\-available(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPstatus": "nxtPstatus"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/dashboard/(?<nxtPstatus>[^/]+?)/not\\-available(?:/)?$"}, {"page": "/[locale]/library", "regex": "^/([^/]+?)/library(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/library(?:/)?$"}, {"page": "/[locale]/library/asset", "regex": "^/([^/]+?)/library/asset(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/library/asset(?:/)?$"}, {"page": "/[locale]/library/not-available", "regex": "^/([^/]+?)/library/not\\-available(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/library/not\\-available(?:/)?$"}, {"page": "/[locale]/library/question-block/form-builder", "regex": "^/([^/]+?)/library/question\\-block/form\\-builder(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/library/question\\-block/form\\-builder(?:/)?$"}, {"page": "/[locale]/library/template/[hashedId]/form-builder", "regex": "^/([^/]+?)/library/template/([^/]+?)/form\\-builder(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPhashedId": "nxtPhashedId"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/library/template/(?<nxtPhashedId>[^/]+?)/form\\-builder(?:/)?$"}, {"page": "/[locale]/library/template/[hashedId]/settings", "regex": "^/([^/]+?)/library/template/([^/]+?)/settings(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPhashedId": "nxtPhashedId"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/library/template/(?<nxtPhashedId>[^/]+?)/settings(?:/)?$"}, {"page": "/[locale]/policy", "regex": "^/([^/]+?)/policy(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/policy(?:/)?$"}, {"page": "/[locale]/project/[hashedId]/data", "regex": "^/([^/]+?)/project/([^/]+?)/data(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPhashedId": "nxtPhashedId"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/project/(?<nxtPhashedId>[^/]+?)/data(?:/)?$"}, {"page": "/[locale]/project/[hashedId]/data/downloads", "regex": "^/([^/]+?)/project/([^/]+?)/data/downloads(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPhashedId": "nxtPhashedId"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/project/(?<nxtPhashedId>[^/]+?)/data/downloads(?:/)?$"}, {"page": "/[locale]/project/[hashedId]/data/gallery", "regex": "^/([^/]+?)/project/([^/]+?)/data/gallery(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPhashedId": "nxtPhashedId"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/project/(?<nxtPhashedId>[^/]+?)/data/gallery(?:/)?$"}, {"page": "/[locale]/project/[hashedId]/data/map", "regex": "^/([^/]+?)/project/([^/]+?)/data/map(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPhashedId": "nxtPhashedId"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/project/(?<nxtPhashedId>[^/]+?)/data/map(?:/)?$"}, {"page": "/[locale]/project/[hashedId]/data/reports", "regex": "^/([^/]+?)/project/([^/]+?)/data/reports(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPhashedId": "nxtPhashedId"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/project/(?<nxtPhashedId>[^/]+?)/data/reports(?:/)?$"}, {"page": "/[locale]/project/[hashedId]/data/table", "regex": "^/([^/]+?)/project/([^/]+?)/data/table(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPhashedId": "nxtPhashedId"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/project/(?<nxtPhashedId>[^/]+?)/data/table(?:/)?$"}, {"page": "/[locale]/project/[hashedId]/form-builder", "regex": "^/([^/]+?)/project/([^/]+?)/form\\-builder(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPhashedId": "nxtPhashedId"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/project/(?<nxtPhashedId>[^/]+?)/form\\-builder(?:/)?$"}, {"page": "/[locale]/project/[hashedId]/overview", "regex": "^/([^/]+?)/project/([^/]+?)/overview(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPhashedId": "nxtPhashedId"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/project/(?<nxtPhashedId>[^/]+?)/overview(?:/)?$"}, {"page": "/[locale]/project/[hashedId]/settings", "regex": "^/([^/]+?)/project/([^/]+?)/settings(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPhashedId": "nxtPhashedId"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/project/(?<nxtPhashedId>[^/]+?)/settings(?:/)?$"}, {"page": "/[locale]/reset-password", "regex": "^/([^/]+?)/reset\\-password(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/reset\\-password(?:/)?$"}, {"page": "/[locale]/reset-password/change-password", "regex": "^/([^/]+?)/reset\\-password/change\\-password(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/reset\\-password/change\\-password(?:/)?$"}, {"page": "/[locale]/signup", "regex": "^/([^/]+?)/signup(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/signup(?:/)?$"}, {"page": "/[locale]/terms", "regex": "^/([^/]+?)/terms(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/terms(?:/)?$"}, {"page": "/[locale]/test-page", "regex": "^/([^/]+?)/test\\-page(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/test\\-page(?:/)?$"}], "staticRoutes": [{"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}