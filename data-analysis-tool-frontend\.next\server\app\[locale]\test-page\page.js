(()=>{var e={};e.id=6458,e.ids=[6458],e.modules={3280:(e,t,r)=>{Promise.resolve().then(r.bind(r,78041))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8411:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(60687),o=r(77618),n=r(85814),i=r.n(n);function a(){let e=(0,o.c3)();return(0,s.jsxs)("div",{className:"p-8",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold mb-4",children:"Internationalization Test Page"}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("p",{children:"Current translations:"}),(0,s.jsxs)("ul",{className:"list-disc pl-5",children:[(0,s.jsxs)("li",{children:["Save: ",e("save")]}),(0,s.jsxs)("li",{children:["Cancel: ",e("cancel")]})]})]}),(0,s.jsxs)("div",{className:"flex gap-4",children:[(0,s.jsx)(i(),{href:"/en/test-page",className:"px-4 py-2 bg-blue-500 text-white rounded",children:"English"}),(0,s.jsx)(i(),{href:"/ne/test-page",className:"px-4 py-2 bg-blue-500 text-white rounded",children:"Nepali"})]}),(0,s.jsx)("div",{className:"mt-4",children:(0,s.jsx)(i(),{href:"/",className:"text-blue-500 underline",children:"Go to Home"})})]})}},10125:(e,t,r)=>{"use strict";r.d(t,{Notification:()=>p});var s=r(60687),o=r(43210),n=r(54864),i=r(88920),a=r(57101),l=r(19150),d=r(14719),c=r(43649),u=r(93613);let p=()=>{let e=(0,n.wA)(),{message:t,type:r,visible:p}=(0,n.d4)(e=>e.notification);(0,o.useEffect)(()=>{if(p){let t=setTimeout(()=>{e((0,l._b)())},5e3);return()=>clearTimeout(t)}},[p,e]);let m="success"===r?(0,s.jsx)(d.A,{}):"warning"===r?(0,s.jsx)(c.A,{}):(0,s.jsx)(u.A,{});return(0,s.jsx)(i.N,{children:p&&(0,s.jsxs)(a.P.div,{className:`z-50 fixed top-0 right-0 m-4 px-4 py-2 rounded font-semibold w-auto max-w-xs flex items-center gap-2 cursor-pointer ${"success"===r?"bg-green-500 hover:bg-green-600":"warning"===r?"bg-yellow-500 hover:bg-yellow-600":"bg-red-500 hover:bg-red-600"} transition-colors duration-300`,onClick:()=>e((0,l._b)()),initial:{opacity:0,y:-20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.3,ease:"easeIn"},children:[(0,s.jsx)("span",{className:"text-2xl",children:m}),(0,s.jsx)("span",{className:"break-words neutral-100space-normal",children:t})]})})}},10271:(e,t,r)=>{"use strict";r.d(t,{ReactQueryProvider:()=>l});var s=r(60687),o=r(43210),n=r(39091),i=r(8693),a=r(9124);let l=({children:e})=>{let[t]=(0,o.useState)(()=>new n.E({defaultOptions:{queries:{staleTime:3e5,refetchOnWindowFocus:!1}}}));return(0,s.jsxs)(i.Ht,{client:t,children:[e,(0,s.jsx)(a.E,{initialIsOpen:!1})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13008:(e,t,r)=>{Promise.resolve().then(r.bind(r,8411))},16319:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19150:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>i,Ds:()=>o,_b:()=>n});let s=(0,r(9317).Z0)({name:"notification",initialState:{message:"",type:"",visible:!1},reducers:{showNotification:(e,t)=>{e.message=t.payload.message,e.type=t.payload.type,e.visible=!0},hideNotification:e=>{e.message="",e.type="",e.visible=!1}}}),{showNotification:o,hideNotification:n}=s.actions,i=s.reducer},26946:(e,t,r)=>{Promise.resolve().then(r.bind(r,10125)),Promise.resolve().then(r.bind(r,10271)),Promise.resolve().then(r.bind(r,49271))},28698:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=r(65239),o=r(48088),n=r(88170),i=r.n(n),a=r(30893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let d={children:["",{children:["[locale]",{children:["test-page",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,78041)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\test-page\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,72121)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\test-page\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/[locale]/test-page/page",pathname:"/[locale]/test-page",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35790:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>i,l:()=>n,yg:()=>o});let s=(0,r(9317).Z0)({name:"createLibraryItem",initialState:{visible:!1},reducers:{showCreateLibraryModal:e=>{e.visible=!0},hideCreateLibraryModal:e=>{e.visible=!1}}}),{showCreateLibraryModal:o,hideCreateLibraryModal:n}=s.actions,i=s.reducer},36039:(e,t,r)=>{Promise.resolve().then(r.bind(r,45196))},42895:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>l,Le:()=>i,jB:()=>a,tQ:()=>o,x9:()=>n});let s=(0,r(9317).Z0)({name:"auth",initialState:{status:"loading",user:null,error:null},reducers:{setAuthenticatedUser:(e,t)=>{e.status="authenticated",e.user=t.payload,e.error=null},setUnauthenticated:e=>{e.status="unauthenticated",e.user=null,e.error=null},setAuthLoading:e=>{e.status="loading"},setAuthError:(e,t)=>{e.status="unauthenticated",e.error=t.payload,e.user=null}}}),{setAuthenticatedUser:o,setUnauthenticated:n,setAuthLoading:i,setAuthError:a}=s.actions,l=s.reducer},44395:(e,t,r)=>{"use strict";r.d(t,{Notification:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call Notification() from the server but Notification is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\components\\general\\Notification.tsx","Notification")},46055:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var s=r(31658);let o=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},49271:(e,t,r)=>{"use strict";r.d(t,{ReduxProvider:()=>p});var s=r(60687),o=r(9317),n=r(19150),i=r(58432),a=r(42895),l=r(35790),d=r(89011);let c=(0,o.U1)({reducer:{notification:n.Ay,createProject:i.Ay,auth:a.Ay,createLibrary:l.Ay,createLibraryItem:d.Ay}});r(43210);var u=r(54864);let p=({children:e})=>(0,s.jsx)(u.Kq,{store:c,children:e})},50823:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},52911:(e,t,r)=>{Promise.resolve().then(r.bind(r,80994))},58014:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p,metadata:()=>u});var s=r(37413);r(82704);var o=r(7990),n=r.n(o),i=r(60866),a=r.n(i),l=r(77832),d=r(44395),c=r(60265);let u={title:"Data analysis tool",description:"A tool for data collection and analysis."};function p({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:`${n().className} ${a().className} antialiased`,children:(0,s.jsx)(l.ReduxProvider,{children:(0,s.jsxs)(c.ReactQueryProvider,{children:[(0,s.jsx)(d.Notification,{}),(0,s.jsx)("main",{className:"bg-neutral-200",children:e})]})})})})}},58432:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>i,Gl:()=>o,th:()=>n});let s=(0,r(9317).Z0)({name:"createProject",initialState:{visible:!1},reducers:{showCreateProjectModal:e=>{e.visible=!0},hideCreateProjectModal:e=>{e.visible=!1}}}),{showCreateProjectModal:o,hideCreateProjectModal:n}=s.actions,i=s.reducer},60265:(e,t,r)=>{"use strict";r.d(t,{ReactQueryProvider:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call ReactQueryProvider() from the server but ReactQueryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\providers\\ReactQueryProvider.tsx","ReactQueryProvider")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72121:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,generateStaticParams:()=>l});var s=r(37413),o=r(60958),n=r(39916),i=r(81015);let a=["en","ne"];function l(){return a.map(e=>({locale:e}))}async function d({children:e,params:t}){let{locale:r}=await t;a.includes(r)||(0,n.notFound)();let l=await (0,i.V)(r);return(0,s.jsx)(o.A,{locale:r,messages:l,children:e})}},76565:(e,t,r)=>{var s={"./en.json":[87368,7368],"./ne.json":[3018,3018]};function o(e){if(!r.o(s,e))return Promise.resolve().then(()=>{var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t});var t=s[e],o=t[0];return r.e(t[1]).then(()=>r.t(o,19))}o.keys=()=>Object.keys(s),o.id=76565,e.exports=o},77832:(e,t,r)=>{"use strict";r.d(t,{ReduxProvider:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call ReduxProvider() from the server but ReduxProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\providers\\ReduxProvider.tsx","ReduxProvider")},78041:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Kobo-project\\\\data-analysis-tool-frontend\\\\app\\\\[locale]\\\\test-page\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\test-page\\page.tsx","default")},79551:e=>{"use strict";e.exports=require("url")},81015:(e,t,r)=>{"use strict";r.d(t,{A:()=>i,V:()=>n});var s=r(35471);let o=["en","ne"];async function n(e){o.includes(e)||(console.warn(`Unsupported locale: ${e}, falling back to 'en'`),e="en");try{let t=(await r(76565)(`./${e}.json`)).default;if(!t||"object"!=typeof t)throw Error(`Invalid messages format for locale: ${e}`);return t}catch(t){if(console.error(`Failed to load messages for locale: ${e}`,t),"en"!==e)try{return console.log("Falling back to English messages"),(await r.e(7368).then(r.t.bind(r,87368,19))).default}catch(e){console.error("Failed to load fallback English messages",e)}return{}}}let i=(0,s.A)(async({locale:e})=>{let t=e?.toString()||"en";return{locale:t,messages:await n(t),timeZone:"Asia/Kathmandu",formats:{dateTime:{short:{day:"numeric",month:"short",year:"numeric"},medium:{day:"numeric",month:"long",year:"numeric"},long:{weekday:"long",day:"numeric",month:"long",year:"numeric"}},number:{currency:{style:"currency",currency:"NPR"}}}}})},82704:()=>{},86778:(e,t,r)=>{Promise.resolve().then(r.bind(r,44395)),Promise.resolve().then(r.bind(r,60265)),Promise.resolve().then(r.bind(r,77832))},89011:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>i,dQ:()=>o,g7:()=>n});let s=(0,r(9317).Z0)({initialState:{visible:!1,option:""},name:"createLibraryItem",reducers:{showCreateLibraryItemModal:(e,t)=>{e.visible=!0,e.option=t.payload},hideCreateLibraryItemModal:e=>{e.visible=!1,e.option=""}}}),{showCreateLibraryItemModal:o,hideCreateLibraryItemModal:n}=s.actions,i=s.reducer}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,7404,1658,7618,63],()=>r(28698));module.exports=s})();