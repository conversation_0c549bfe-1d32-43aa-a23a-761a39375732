"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.fetchQuestionForForms = exports.fetchQuestionForForm = exports.DeleteMultipleProject = exports.updateManyProjectStatus = exports.changeProjectStatus = exports.createProjectFromLibraryTemplate = exports.getProjectById = exports.deleteProject = exports.updateProjects = exports.getAllProject = exports.createProject = void 0;
const ApiResponse_1 = require("../utils/ApiResponse");
const projectValidators_1 = require("../validators/projectValidators");
const projectRepository_1 = __importDefault(require("../repositories/projectRepository"));
const client_1 = require("@prisma/client");
const prisma_1 = require("../utils/prisma");
const libraryTemplateRepository_1 = __importDefault(require("../repositories/libraryTemplateRepository"));
const libraryQuestionRepository_1 = __importDefault(require("../repositories/libraryQuestionRepository"));
const createProject = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const result = projectValidators_1.ProjectSchema.safeParse(req.body);
        if (!result.success) {
            res.status(400).json({
                success: false,
                message: result.error.flatten().fieldErrors,
            });
            return;
        }
        const { name, description, sector, country } = result.data;
        const existanceProject = yield projectRepository_1.default.findByName(name, userId);
        if (existanceProject) {
            res
                .status(400)
                .json({ success: false, message: "project already exist" });
            return;
        }
        const project = yield projectRepository_1.default.create({
            name,
            description,
            sector,
            userId,
            country,
        });
        res.status(201).json({ message: "Project created successfully", project });
        return;
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: "error creating project",
            error: error.message,
        });
        return;
    }
});
exports.createProject = createProject;
const getAllProject = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const id = Number(req.user.id);
        if (!id) {
            res.status(404).json({
                success: false,
                message: "user id not found",
            });
        }
        const projects = yield projectRepository_1.default.findAll(id);
        res
            .status(200)
            .json({ message: "Successfully fetched all projects", projects });
        return;
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: "error getting projects",
            error: error.message,
        });
        return;
    }
});
exports.getAllProject = getAllProject;
const updateProjects = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const id = Number(req.params.id);
    try {
        const userId = req.user.id;
        const projectuser = yield projectRepository_1.default.findProjectByIdAndUser(id, userId);
        if (!projectuser) {
            res.status(404).json({
                success: false,
                message: "no project found",
            });
            return;
        }
        const result = projectValidators_1.ProjectSchema.safeParse(req.body);
        if (!result.success) {
            res.status(400).json({
                success: false,
                message: "Validation failed",
                error: result.error.flatten(),
            });
            return;
        }
        const updatedData = result.data;
        const updatedProject = yield projectRepository_1.default.updateById(id, updatedData);
        res
            .status(200)
            .json(new ApiResponse_1.ApiResponse(200, { updatedProject }, "Project updated success"));
        return;
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: "error updating project",
            error: error.message,
        });
        return;
    }
});
exports.updateProjects = updateProjects;
const deleteProject = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const id = Number(req.params.id);
    try {
        const userId = req.user.id;
        const project = yield projectRepository_1.default.findProjectByIdAndUser(id, userId);
        if (!project) {
            res.status(404).json({
                success: false,
                message: "no project found with give id",
            });
            return;
        }
        yield projectRepository_1.default.deleteProject(id);
        res.status(200).json(new ApiResponse_1.ApiResponse(200, {}, "project deleted success"));
        return;
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: "error deleting project",
            error: error.message,
        });
        return;
    }
});
exports.deleteProject = deleteProject;
const getProjectById = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const id = Number(req.params.id);
    try {
        const userId = req.user.id;
        const project = yield projectRepository_1.default.findProjectByIdAndUser(id, userId);
        if (!project) {
            res.status(404).json({
                success: false,
                message: "project not found",
            });
            return;
        }
        res.status(200).json({ message: "Successfully fetched project", project });
        return;
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: "error getting user by id",
            error: error.message,
        });
        return;
    }
});
exports.getProjectById = getProjectById;
const createProjectFromLibraryTemplate = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const { templateId } = req.body;
        if (!templateId) {
            return res.status(400).json({
                success: false,
                message: "Template ID is not provided in the request body",
            });
        }
        const libraryTemplate = yield libraryTemplateRepository_1.default.findById(templateId);
        if (!libraryTemplate) {
            res.status(404).json({
                success: false,
                message: "Library template not found",
            });
            return;
        }
        // Verify user owns the library template
        const isOwner = yield libraryTemplateRepository_1.default.isOwner(userId, templateId);
        if (!isOwner) {
            res.status(403).json({
                success: false,
                message: "You don't have permission to use this library template",
            });
            return;
        }
        // Get all library questions for this template
        const libraryQuestions = yield libraryQuestionRepository_1.default.findByLibraryTemplateId(templateId);
        // Validate project data from request body
        const result = projectValidators_1.ProjectSchema.safeParse(req.body);
        if (!result.success) {
            res.status(400).json({
                success: false,
                errors: result.error.flatten().fieldErrors,
            });
            return;
        }
        // Check if project with same name already exists for this user
        const existingProject = yield prisma_1.prisma.project.findFirst({
            where: {
                name: result.data.name,
                userId: userId,
            },
        });
        if (existingProject) {
            res.status(400).json({
                success: false,
                message: "A project with this name already exists for your account",
            });
            return;
        }
        // Create a transaction to ensure all operations complete together
        const project = yield prisma_1.prisma.$transaction((tx) => __awaiter(void 0, void 0, void 0, function* () {
            // Create the main project
            const project = yield tx.project.create({
                data: {
                    name: result.data.name,
                    description: result.data.description || "",
                    status: client_1.Status.draft,
                    userId: userId,
                    sector: result.data.sector,
                    country: result.data.country,
                },
            });
            // Add user as project owner
            yield tx.projectUser.create({
                data: {
                    projectId: project.id,
                    userId: userId,
                    permission: {
                        isOwner: true,
                        canEdit: true,
                        canDelete: true,
                        canView: true,
                    },
                },
            });
            // Convert library questions to project questions
            if (libraryQuestions.length > 0) {
                for (const libraryQuestion of libraryQuestions) {
                    // Create the project question from library question
                    const question = yield tx.question.create({
                        data: {
                            label: libraryQuestion.label,
                            inputType: libraryQuestion.inputType,
                            hint: libraryQuestion.hint,
                            placeholder: libraryQuestion.placeholder,
                            isRequired: libraryQuestion.isRequired,
                            position: libraryQuestion.position,
                            projectId: project.id,
                        },
                    });
                    // Get library question options
                    const libraryOptions = yield tx.libraryQuestionOption.findMany({
                        where: {
                            libraryQuestionId: libraryQuestion.id,
                        },
                    });
                    // Create question options if any
                    if (libraryOptions.length > 0) {
                        for (const option of libraryOptions) {
                            yield tx.questionOption.create({
                                data: {
                                    label: option.label,
                                    code: option.code,
                                    questionId: question.id,
                                },
                            });
                        }
                    }
                    // Get library question conditions
                    const libraryConditions = yield tx.libraryQuestionCondition.findMany({
                        where: {
                            libraryQuestionId: libraryQuestion.id,
                        },
                    });
                    // Create question conditions if any
                    if (libraryConditions.length > 0) {
                        for (const condition of libraryConditions) {
                            yield tx.questionCondition.create({
                                data: {
                                    operator: condition.operator,
                                    value: condition.value,
                                    questionId: question.id,
                                },
                            });
                        }
                    }
                }
            }
            return project;
        }));
        res
            .status(201)
            .json(new ApiResponse_1.ApiResponse(201, { project }, "Project created successfully from library template"));
        return;
    }
    catch (error) {
        console.error("Error creating project from template:", error);
        res.status(500).json({
            success: false,
            message: "Error creating project from library template",
            error: error instanceof Error ? error.message : "Unexpected error",
        });
        return;
    }
});
exports.createProjectFromLibraryTemplate = createProjectFromLibraryTemplate;
const changeProjectStatus = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const id = Number(req.params.id);
        if (!id) {
            return res.status(404).json({
                success: false,
                message: "project id is required",
            });
        }
        const result = projectValidators_1.ChangeStatusSchema.safeParse(req.body);
        if (!result.success) {
            return res.status(400).json({
                success: false,
                message: "Validation failed",
                error: result.error.flatten(),
            });
        }
        const updatedData = result.data;
        const updatedProject = yield projectRepository_1.default.changeProjectStatus(id, updatedData.status);
        return res.status(200).json({
            success: true,
            message: "status updated success",
            data: { updatedProject },
        });
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: "error creating qustion",
            error: error instanceof Error ? error.message : "unexpected error",
        });
        return;
    }
});
exports.changeProjectStatus = changeProjectStatus;
const updateManyProjectStatus = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const result = projectValidators_1.updateProjectStatusesSchema.safeParse(req.body);
        if (!result.success) {
            return res.status(400).json({
                success: false,
                message: "Validation failed",
                error: result.error.flatten(),
            });
        }
        const updatedData = result.data;
        const updatedProject = yield projectRepository_1.default.changeManyProjectStatus(updatedData.projectIds, updatedData.status);
        return res.status(200).json({
            success: false,
            message: "status changed success",
            data: { updatedProject },
        });
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: "error creating qustion",
            error: error instanceof Error ? error.message : "unexpected error",
        });
        return;
    }
});
exports.updateManyProjectStatus = updateManyProjectStatus;
const DeleteMultipleProject = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const result = projectValidators_1.deleteMultipleProjectSchema.safeParse(req.body);
        if (!result.success) {
            return res.status(400).json({
                success: false,
                message: "Validation failed",
                error: result.error.flatten(),
            });
        }
        const updatedData = result.data;
        yield projectRepository_1.default.deleteMultipleProject(updatedData.projectIds);
        return res.status(200).json({
            success: false,
            message: "project deleted success",
        });
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: "error deleteing project",
            error: error instanceof Error ? error.message : "unexpected error",
        });
        return;
    }
});
exports.DeleteMultipleProject = DeleteMultipleProject;
const fetchQuestionForForm = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const id = Number(req.params.id);
        if (!id) {
            return res.status(404).json({
                message: "id not found",
            });
        }
        const project = yield projectRepository_1.default.fetchQuestionGroupWithQuestion(id);
        return res.status(200).json({
            success: true,
            message: "data fetch success",
            data: { project },
        });
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: "error getting question group and questions",
            error: error instanceof Error ? error.message : "unexpected error",
        });
        return;
    }
});
exports.fetchQuestionForForm = fetchQuestionForForm;
const fetchQuestionForForms = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const id = Number(req.params.id);
        if (!id) {
            return res.status(404).json({ message: "id not found" });
        }
        const project = yield prisma_1.prisma.project.findUnique({ where: { id } });
        if (!project) {
            return res.status(404).json({ message: "project not found" });
        }
        const orderedItems = yield projectRepository_1.default.fetchOrderedQuestions(id);
        console.log("project", orderedItems);
        return res.status(200).json({
            success: true,
            message: "Fetched ordered questions and groups",
            data: {
                project,
                items: orderedItems,
            },
        });
    }
    catch (err) {
        return res.status(500).json({
            success: false,
            message: "Internal server error",
            error: err.message,
        });
    }
});
exports.fetchQuestionForForms = fetchQuestionForForms;
