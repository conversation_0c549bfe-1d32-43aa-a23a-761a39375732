"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
const prisma_1 = require("../utils/prisma");
class LibraryQuestionRepository {
    findById(id) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.libraryQuestion.findUnique({
                where: { id },
                include: {
                    questionOptions: true,
                    questionConditions: true,
                },
            });
        });
    }
    findByLibraryTemplateId(libraryTemplateId) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.libraryQuestion.findMany({
                where: { libraryTemplateId },
                include: {
                    questionOptions: true,
                    questionConditions: true,
                },
                orderBy: {
                    position: "asc",
                },
            });
        });
    }
    isLibraryTemplateOwner(userId, libraryTemplateId) {
        return __awaiter(this, void 0, void 0, function* () {
            const count = yield prisma_1.prisma.libraryTemplate.count({
                where: {
                    id: libraryTemplateId,
                    userId,
                },
            });
            return count > 0;
        });
    }
    create(data) {
        return __awaiter(this, void 0, void 0, function* () {
            const { questionOptions, conditions } = data, questionData = __rest(data, ["questionOptions", "conditions"]);
            return yield prisma_1.prisma.$transaction((tx) => __awaiter(this, void 0, void 0, function* () {
                var _a, _b;
                // Create the question
                const question = yield tx.libraryQuestion.create({
                    // data: questionData,
                    data: Object.assign(Object.assign({}, questionData), { hint: (_a = questionData.hint) !== null && _a !== void 0 ? _a : "", placeholder: (_b = questionData.placeholder) !== null && _b !== void 0 ? _b : "" }),
                });
                // Create options if provided (for select type questions)
                if (questionOptions && questionOptions.length > 0) {
                    yield tx.libraryQuestionOption.createMany({
                        data: questionOptions.map((option) => ({
                            label: option.label,
                            code: option.code,
                            libraryQuestionId: question.id,
                            nextLibraryQuestionId: option.nextLibraryQuestionId || null,
                        })),
                    });
                }
                // Create conditions if provided
                if (conditions && conditions.length > 0) {
                    yield tx.libraryQuestionCondition.createMany({
                        data: conditions.map((condition) => ({
                            operator: condition.operator,
                            value: condition.value,
                            libraryQuestionId: question.id,
                        })),
                    });
                }
                // Return the created question with options and conditions
                return (yield tx.libraryQuestion.findUnique({
                    where: { id: question.id },
                    include: {
                        questionOptions: true,
                        questionConditions: true,
                    },
                }));
            }));
        });
    }
    duplicateQuestion(id, templateId) {
        return __awaiter(this, void 0, void 0, function* () {
            const question = yield prisma_1.prisma.libraryQuestion.findUnique({
                where: { id },
            });
            if (!question) {
                return null;
            }
            const duplicatedQuestion = yield prisma_1.prisma.libraryQuestion.create({
                data: Object.assign(Object.assign({}, question), { id: undefined, libraryTemplateId: templateId }),
                include: {
                    questionOptions: true,
                    questionConditions: true,
                },
            });
            return duplicatedQuestion;
        });
    }
    update(id, data) {
        return __awaiter(this, void 0, void 0, function* () {
            const { questionOptions, conditions } = data, questionData = __rest(data, ["questionOptions", "conditions"]);
            return yield prisma_1.prisma.$transaction((tx) => __awaiter(this, void 0, void 0, function* () {
                // Update the question
                const updatedQuestion = yield tx.libraryQuestion.update({
                    where: { id },
                    data: questionData,
                });
                // Handle options if provided
                if (questionOptions) {
                    const existingOptions = yield tx.libraryQuestionOption.findMany({
                        where: { libraryQuestionId: id },
                    });
                    // Identify options to add/update/delete
                    const existingIds = existingOptions.map((o) => o.id);
                    const updatedIds = questionOptions
                        .filter((o) => o.id)
                        .map((o) => o.id);
                    const idsToDelete = existingIds.filter((id) => !updatedIds.includes(id));
                    // Delete removed options
                    if (idsToDelete.length > 0) {
                        yield tx.libraryQuestionOption.deleteMany({
                            where: { id: { in: idsToDelete } },
                        });
                    }
                    // Add new options and update existing ones
                    for (const option of questionOptions) {
                        if (option.id) {
                            // Update existing option
                            yield tx.libraryQuestionOption.update({
                                where: { id: option.id },
                                data: {
                                    label: option.label,
                                    nextLibraryQuestionId: option.nextLibraryQuestionId || null,
                                },
                            });
                        }
                        else {
                            // Create new option
                            yield tx.libraryQuestionOption.create({
                                data: {
                                    label: option.label,
                                    code: option.code,
                                    libraryQuestionId: id,
                                    nextLibraryQuestionId: option.nextLibraryQuestionId || null,
                                },
                            });
                        }
                    }
                }
                // Handle conditions if provided
                if (conditions) {
                    const existingConditions = yield tx.libraryQuestionCondition.findMany({
                        where: { libraryQuestionId: id },
                    });
                    // Identify conditions to add/update/delete
                    const existingIds = existingConditions.map((c) => c.id);
                    const updatedIds = conditions
                        .filter((c) => c.id)
                        .map((c) => c.id);
                    const idsToDelete = existingIds.filter((id) => !updatedIds.includes(id));
                    // Delete removed conditions
                    if (idsToDelete.length > 0) {
                        yield tx.libraryQuestionCondition.deleteMany({
                            where: { id: { in: idsToDelete } },
                        });
                    }
                    // Add new conditions and update existing ones
                    for (const condition of conditions) {
                        if (condition.id) {
                            // Check if condition exists and belongs to this question
                            const existingCondition = yield tx.libraryQuestionCondition.findUnique({
                                where: {
                                    id: condition.id,
                                },
                            });
                            // Only update if condition exists and belongs to this question
                            if (existingCondition &&
                                existingCondition.libraryQuestionId === id) {
                                // Update existing condition
                                yield tx.libraryQuestionCondition.update({
                                    where: { id: condition.id },
                                    data: {
                                        operator: condition.operator,
                                        value: condition.value,
                                    },
                                });
                            }
                            else {
                                // Create new condition if ID doesn't exist
                                yield tx.libraryQuestionCondition.create({
                                    data: {
                                        operator: condition.operator,
                                        value: condition.value,
                                        libraryQuestionId: id,
                                    },
                                });
                            }
                        }
                        else {
                            // Create new condition
                            yield tx.libraryQuestionCondition.create({
                                data: {
                                    operator: condition.operator,
                                    value: condition.value,
                                    libraryQuestionId: id,
                                },
                            });
                        }
                    }
                }
                // Return the updated question with options and conditions
                return (yield tx.libraryQuestion.findUnique({
                    where: { id },
                    include: {
                        questionOptions: true,
                        questionConditions: true,
                    },
                }));
            }));
        });
    }
    delete(id) {
        return __awaiter(this, void 0, void 0, function* () {
            yield prisma_1.prisma.libraryQuestion.delete({
                where: { id },
            });
        });
    }
}
exports.default = new LibraryQuestionRepository();
