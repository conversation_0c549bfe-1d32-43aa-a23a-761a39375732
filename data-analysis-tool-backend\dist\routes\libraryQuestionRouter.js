"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const libraryQuestionController_1 = require("../controllers/libraryQuestionController");
const auth_1 = require("../middleware/auth");
const router = express_1.default.Router();
// Routes for library questions
// All routes need authentication
// Base path: /api/library/:libraryTemplateId/questions
// Create a new question for a library template
router.post("/:libraryTemplateId", auth_1.authenticate, libraryQuestionController_1.createLibraryQuestion);
router.post("/duplicate/:id", auth_1.authenticate, libraryQuestionController_1.duplicateTemplateQuestion);
// Get all questions for a library template
router.get("/:libraryTemplateId", auth_1.authenticate, libraryQuestionController_1.getAllLibraryQuestions);
// Get a specific question
router.get("/:id", auth_1.authenticate, libraryQuestionController_1.getLibraryQuestionById);
// Update a specific question
router.patch("/:id", auth_1.authenticate, libraryQuestionController_1.updateLibraryQuestion);
// Delete a specific question
router.delete("/:id", auth_1.authenticate, libraryQuestionController_1.deleteLibraryQuestion);
exports.default = router;
