"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.saveCellValuesSchema = exports.updateTableQuestionSchema = exports.createTableQuestionSchema = void 0;
const zod_1 = require("zod");
/**
 * Column schema for creating table questions
 * Validates column structure and parent-child relationships
 */
const createColumnSchema = zod_1.z.object({
    columnName: zod_1.z.string().min(1, "Column name is required"),
    parentColumnId: zod_1.z.number().int().positive().optional(),
});
/**
 * Column schema for updating table questions
 * Includes optional ID field for existing columns
 */
const updateColumnSchema = zod_1.z.object({
    id: zod_1.z.number().int().positive().optional(),
    columnName: zod_1.z.string().min(1, "Column name is required"),
    parentColumnId: zod_1.z.number().int().positive().optional(),
});
/**
 * Row schema for creating table questions
 */
const createRowSchema = zod_1.z.object({
    rowsName: zod_1.z.string().min(1, "Row name is required"),
});
/**
 * Row schema for updating table questions
 * Includes optional ID field for existing rows
 */
const updateRowSchema = zod_1.z.object({
    id: zod_1.z.number().int().positive().optional(),
    rowsName: zod_1.z.string().min(1, "Row name is required"),
});
/**
 * Schema for creating table questions
 * Validates the entire table structure including parent-child relationships
 */
exports.createTableQuestionSchema = zod_1.z
    .object({
    label: zod_1.z.string().min(1, "Table label is required"),
    projectId: zod_1.z.number().int().positive("Valid project ID is required"),
    columns: zod_1.z
        .array(createColumnSchema)
        .min(1, "At least one column is required"),
    rows: zod_1.z.array(createRowSchema).optional().default([]),
})
    .superRefine((data, ctx) => {
    // Validate parent-child relationships
    validateParentChildRelationships(data.columns, ctx);
});
/**
 * Schema for updating table questions
 * Validates the entire table structure including parent-child relationships
 */
exports.updateTableQuestionSchema = zod_1.z
    .object({
    label: zod_1.z.string().min(1, "Table label is required"),
    columns: zod_1.z
        .array(updateColumnSchema)
        .min(1, "At least one column is required"),
    rows: zod_1.z.array(updateRowSchema).optional().default([]),
    projectId: zod_1.z.number().int().positive().optional(), // Make it optional since we'll get it from the existing question
})
    .superRefine((data, ctx) => {
    // Validate parent-child relationships
    validateParentChildRelationships(data.columns, ctx);
});
/**
 * Schema for saving cell values
 */
exports.saveCellValuesSchema = zod_1.z.object({
    questionId: zod_1.z.number().int().positive("Valid question ID is required"),
    cellValues: zod_1.z
        .array(zod_1.z.object({
        columnId: zod_1.z.number().int().positive("Valid column ID is required"),
        rowsId: zod_1.z.number().int().positive("Valid row ID is required"),
        value: zod_1.z.string(),
        code: zod_1.z.string().optional(),
    }))
        .min(1, "At least one cell value is required"),
});
/**
 * Validates parent-child relationships in column data
 *
 * Rules:
 * 1. A parent column must exist before it can be referenced
 * 2. A column cannot be its own parent
 * 3. A parent can have at most 2 children
 * 4. No circular references are allowed
 *
 * @param columns - Array of column objects
 * @param ctx - Zod refinement context
 */
function validateParentChildRelationships(columns, ctx) {
    // Map to track parent-child relationships
    const parentChildMap = new Map();
    // Map to track column positions for error messages
    const columnPositions = new Map();
    // First pass: build the parent-child map and validate basic rules
    columns.forEach((column, index) => {
        const position = index + 1;
        // Store column position for error messages
        if (column.id) {
            columnPositions.set(column.id, position);
        }
        if (column.parentColumnId) {
            // Rule 2: A column cannot be its own parent
            if (column.id && column.parentColumnId === column.id) {
                ctx.addIssue({
                    code: zod_1.z.ZodIssueCode.custom,
                    message: `Column "${column.columnName}" (position ${position}) cannot be its own parent`,
                    path: ["columns", index, "parentColumnId"],
                });
                return;
            }
            // Add child to parent's children list
            if (!parentChildMap.has(column.parentColumnId)) {
                parentChildMap.set(column.parentColumnId, [position]);
            }
            else {
                parentChildMap.get(column.parentColumnId).push(position);
            }
        }
    });
    // Second pass: validate parent existence and child count
    columns.forEach((column, index) => {
        var _a, _b, _c;
        const position = index + 1;
        if (column.parentColumnId) {
            // Rule 1: Parent column must exist
            const parentExists = columns.some((col, idx) => {
                // Check if parent exists by ID or by position
                return ((col.id && col.id === column.parentColumnId) ||
                    idx + 1 === column.parentColumnId);
            });
            if (!parentExists) {
                ctx.addIssue({
                    code: zod_1.z.ZodIssueCode.custom,
                    message: `Parent column with ID ${column.parentColumnId} does not exist`,
                    path: ["columns", index, "parentColumnId"],
                });
            }
            // Rule 3: A parent can have at most 2 children
            const parentId = column.parentColumnId;
            const childCount = ((_a = parentChildMap.get(parentId)) === null || _a === void 0 ? void 0 : _a.length) || 0;
            if (childCount > 2) {
                const parentPosition = columnPositions.get(parentId) ||
                    columns.findIndex((col) => col.id === parentId) + 1;
                const parentName = ((_b = columns.find((col) => col.id === parentId)) === null || _b === void 0 ? void 0 : _b.columnName) ||
                    ((_c = columns[parentPosition - 1]) === null || _c === void 0 ? void 0 : _c.columnName) ||
                    `Parent ${parentId}`;
                ctx.addIssue({
                    code: zod_1.z.ZodIssueCode.custom,
                    message: `Parent column "${parentName}" (position ${parentPosition}) cannot have more than 2 child columns`,
                    path: ["columns", index, "parentColumnId"],
                });
            }
        }
    });
    // Third pass: detect circular references
    const visited = new Set();
    const recursionStack = new Set();
    function detectCycle(columnId) {
        if (recursionStack.has(columnId)) {
            return true; // Cycle detected
        }
        if (visited.has(columnId)) {
            return false; // Already visited, no cycle
        }
        visited.add(columnId);
        recursionStack.add(columnId);
        // Get children of this column
        const children = columns
            .filter((col) => col.parentColumnId === columnId)
            .map((col) => col.id)
            .filter((id) => id !== undefined);
        for (const childId of children) {
            if (detectCycle(childId)) {
                return true;
            }
        }
        recursionStack.delete(columnId);
        return false;
    }
    // Check for cycles starting from each column
    columns.forEach((column, index) => {
        if (column.id && !visited.has(column.id)) {
            if (detectCycle(column.id)) {
                ctx.addIssue({
                    code: zod_1.z.ZodIssueCode.custom,
                    message: `Circular reference detected involving column "${column.columnName}"`,
                    path: ["columns", index],
                });
            }
        }
    });
}
