(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7177],{1243:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});let i=(0,s(19946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},2276:(e,t,s)=>{"use strict";s.d(t,{Notification:()=>d});var i=s(95155),a=s(12115),r=s(34540),n=s(60760),o=s(44518),u=s(71402);let l=(0,s(19946).A)("circle-check",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]]);var h=s(1243),c=s(85339);let d=()=>{let e=(0,r.wA)(),{message:t,type:s,visible:d}=(0,r.d4)(e=>e.notification);(0,a.useEffect)(()=>{if(d){let t=setTimeout(()=>{e((0,u._b)())},5e3);return()=>clearTimeout(t)}},[d,e]);let y="success"===s?(0,i.jsx)(l,{}):"warning"===s?(0,i.jsx)(h.A,{}):(0,i.jsx)(c.A,{});return(0,i.jsx)(n.N,{children:d&&(0,i.jsxs)(o.P.div,{className:"z-50 fixed top-0 right-0 m-4 px-4 py-2 rounded font-semibold w-auto max-w-xs flex items-center gap-2 cursor-pointer ".concat("success"===s?"bg-green-500 hover:bg-green-600":"warning"===s?"bg-yellow-500 hover:bg-yellow-600":"bg-red-500 hover:bg-red-600"," transition-colors duration-300"),onClick:()=>e((0,u._b)()),initial:{opacity:0,y:-20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.3,ease:"easeIn"},children:[(0,i.jsx)("span",{className:"text-2xl",children:y}),(0,i.jsx)("span",{className:"break-words neutral-100space-normal",children:t})]})})}},19324:()=>{},19946:(e,t,s)=>{"use strict";s.d(t,{A:()=>h});var i=s(12115);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),r=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase()),n=e=>{let t=r(e);return t.charAt(0).toUpperCase()+t.slice(1)},o=function(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return t.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim()};var u={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=(0,i.forwardRef)((e,t)=>{let{color:s="currentColor",size:a=24,strokeWidth:r=2,absoluteStrokeWidth:n,className:l="",children:h,iconNode:c,...d}=e;return(0,i.createElement)("svg",{ref:t,...u,width:a,height:a,stroke:s,strokeWidth:n?24*Number(r)/Number(a):r,className:o("lucide",l),...d},[...c.map(e=>{let[t,s]=e;return(0,i.createElement)(t,s)}),...Array.isArray(h)?h:[h]])}),h=(e,t)=>{let s=(0,i.forwardRef)((s,r)=>{let{className:u,...h}=s;return(0,i.createElement)(l,{ref:r,iconNode:t,className:o("lucide-".concat(a(n(e))),"lucide-".concat(e),u),...h})});return s.displayName=n(e),s}},31998:(e,t,s)=>{"use strict";s.d(t,{ReduxProvider:()=>d});var i=s(95155),a=s(51990),r=s(71402),n=s(39286),o=s(97381),u=s(62672),l=s(75173);let h=(0,a.U1)({reducer:{notification:r.Ay,createProject:n.Ay,auth:o.Ay,createLibrary:u.Ay,createLibraryItem:l.Ay}});s(12115);var c=s(34540);let d=e=>{let{children:t}=e;return(0,i.jsx)(c.Kq,{store:h,children:t})}},34560:(e,t,s)=>{"use strict";s.d(t,{$:()=>o,s:()=>n});var i=s(7165),a=s(57948),r=s(6784),n=class extends a.k{#e;#t;#s;constructor(e){super(),this.mutationId=e.mutationId,this.#t=e.mutationCache,this.#e=[],this.state=e.state||o(),this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options=e,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){this.#e.includes(e)||(this.#e.push(e),this.clearGcTimeout(),this.#t.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.#e=this.#e.filter(t=>t!==e),this.scheduleGc(),this.#t.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.#e.length||("pending"===this.state.status?this.scheduleGc():this.#t.remove(this))}continue(){return this.#s?.continue()??this.execute(this.state.variables)}async execute(e){let t=()=>{this.#i({type:"continue"})};this.#s=(0,r.II)({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(Error("No mutationFn found")),onFail:(e,t)=>{this.#i({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#i({type:"pause"})},onContinue:t,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#t.canRun(this)});let s="pending"===this.state.status,i=!this.#s.canStart();try{if(s)t();else{this.#i({type:"pending",variables:e,isPaused:i}),await this.#t.config.onMutate?.(e,this);let t=await this.options.onMutate?.(e);t!==this.state.context&&this.#i({type:"pending",context:t,variables:e,isPaused:i})}let a=await this.#s.start();return await this.#t.config.onSuccess?.(a,e,this.state.context,this),await this.options.onSuccess?.(a,e,this.state.context),await this.#t.config.onSettled?.(a,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(a,null,e,this.state.context),this.#i({type:"success",data:a}),a}catch(t){try{throw await this.#t.config.onError?.(t,e,this.state.context,this),await this.options.onError?.(t,e,this.state.context),await this.#t.config.onSettled?.(void 0,t,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,t,e,this.state.context),t}finally{this.#i({type:"error",error:t})}}finally{this.#t.runNext(this)}}#i(e){this.state=(t=>{switch(e.type){case"failed":return{...t,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...t,isPaused:!0};case"continue":return{...t,isPaused:!1};case"pending":return{...t,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:e.isPaused,status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...t,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...t,data:void 0,error:e.error,failureCount:t.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}})(this.state),i.jG.batch(()=>{this.#e.forEach(t=>{t.onMutationUpdate(e)}),this.#t.notify({mutation:this,type:"updated",action:e})})}};function o(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}},39286:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>n,Gl:()=>a,th:()=>r});let i=(0,s(51990).Z0)({name:"createProject",initialState:{visible:!1},reducers:{showCreateProjectModal:e=>{e.visible=!0},hideCreateProjectModal:e=>{e.visible=!1}}}),{showCreateProjectModal:a,hideCreateProjectModal:r}=i.actions,n=i.reducer},59703:e=>{e.exports={style:{fontFamily:"'Poppins', 'Poppins Fallback'",fontStyle:"normal"},className:"__className_cfd010"}},62672:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>n,l:()=>r,yg:()=>a});let i=(0,s(51990).Z0)({name:"createLibraryItem",initialState:{visible:!1},reducers:{showCreateLibraryModal:e=>{e.visible=!0},hideCreateLibraryModal:e=>{e.visible=!1}}}),{showCreateLibraryModal:a,hideCreateLibraryModal:r}=i.actions,n=i.reducer},64638:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,19324,23)),Promise.resolve().then(s.bind(s,2276)),Promise.resolve().then(s.t.bind(s,59703,23)),Promise.resolve().then(s.t.bind(s,68971,23)),Promise.resolve().then(s.bind(s,74078)),Promise.resolve().then(s.bind(s,31998))},68971:e=>{e.exports={style:{fontFamily:"'Noto Sans Devanagari', 'Noto Sans Devanagari Fallback'",fontStyle:"normal"},className:"__className_fd230c"}},71402:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>n,Ds:()=>a,_b:()=>r});let i=(0,s(51990).Z0)({name:"notification",initialState:{message:"",type:"",visible:!1},reducers:{showNotification:(e,t)=>{e.message=t.payload.message,e.type=t.payload.type,e.visible=!0},hideNotification:e=>{e.message="",e.type="",e.visible=!1}}}),{showNotification:a,hideNotification:r}=i.actions,n=i.reducer},74078:(e,t,s)=>{"use strict";s.d(t,{ReactQueryProvider:()=>C});var i=s(95155),a=s(12115),r=s(52020),n=s(39853),o=s(7165),u=s(25910),l=class extends u.Q{constructor(e={}){super(),this.config=e,this.#a=new Map}#a;build(e,t,s){let i=t.queryKey,a=t.queryHash??(0,r.F$)(i,t),o=this.get(a);return o||(o=new n.X({client:e,queryKey:i,queryHash:a,options:e.defaultQueryOptions(t),state:s,defaultOptions:e.getQueryDefaults(i)}),this.add(o)),o}add(e){this.#a.has(e.queryHash)||(this.#a.set(e.queryHash,e),this.notify({type:"added",query:e}))}remove(e){let t=this.#a.get(e.queryHash);t&&(e.destroy(),t===e&&this.#a.delete(e.queryHash),this.notify({type:"removed",query:e}))}clear(){o.jG.batch(()=>{this.getAll().forEach(e=>{this.remove(e)})})}get(e){return this.#a.get(e)}getAll(){return[...this.#a.values()]}find(e){let t={exact:!0,...e};return this.getAll().find(e=>(0,r.MK)(t,e))}findAll(e={}){let t=this.getAll();return Object.keys(e).length>0?t.filter(t=>(0,r.MK)(e,t)):t}notify(e){o.jG.batch(()=>{this.listeners.forEach(t=>{t(e)})})}onFocus(){o.jG.batch(()=>{this.getAll().forEach(e=>{e.onFocus()})})}onOnline(){o.jG.batch(()=>{this.getAll().forEach(e=>{e.onOnline()})})}},h=s(34560),c=class extends u.Q{constructor(e={}){super(),this.config=e,this.#r=new Set,this.#n=new Map,this.#o=0}#r;#n;#o;build(e,t,s){let i=new h.s({mutationCache:this,mutationId:++this.#o,options:e.defaultMutationOptions(t),state:s});return this.add(i),i}add(e){this.#r.add(e);let t=d(e);if("string"==typeof t){let s=this.#n.get(t);s?s.push(e):this.#n.set(t,[e])}this.notify({type:"added",mutation:e})}remove(e){if(this.#r.delete(e)){let t=d(e);if("string"==typeof t){let s=this.#n.get(t);if(s)if(s.length>1){let t=s.indexOf(e);-1!==t&&s.splice(t,1)}else s[0]===e&&this.#n.delete(t)}}this.notify({type:"removed",mutation:e})}canRun(e){let t=d(e);if("string"!=typeof t)return!0;{let s=this.#n.get(t),i=s?.find(e=>"pending"===e.state.status);return!i||i===e}}runNext(e){let t=d(e);if("string"!=typeof t)return Promise.resolve();{let s=this.#n.get(t)?.find(t=>t!==e&&t.state.isPaused);return s?.continue()??Promise.resolve()}}clear(){o.jG.batch(()=>{this.#r.forEach(e=>{this.notify({type:"removed",mutation:e})}),this.#r.clear(),this.#n.clear()})}getAll(){return Array.from(this.#r)}find(e){let t={exact:!0,...e};return this.getAll().find(e=>(0,r.nJ)(t,e))}findAll(e={}){return this.getAll().filter(t=>(0,r.nJ)(e,t))}notify(e){o.jG.batch(()=>{this.listeners.forEach(t=>{t(e)})})}resumePausedMutations(){let e=this.getAll().filter(e=>e.state.isPaused);return o.jG.batch(()=>Promise.all(e.map(e=>e.continue().catch(r.lQ))))}};function d(e){return e.options.scope?.id}var y=s(50920),f=s(21239);function p(e){return{onFetch:(t,s)=>{let i=t.options,a=t.fetchOptions?.meta?.fetchMore?.direction,n=t.state.data?.pages||[],o=t.state.data?.pageParams||[],u={pages:[],pageParams:[]},l=0,h=async()=>{let s=!1,h=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(t.signal.aborted?s=!0:t.signal.addEventListener("abort",()=>{s=!0}),t.signal)})},c=(0,r.ZM)(t.options,t.fetchOptions),d=async(e,i,a)=>{if(s)return Promise.reject();if(null==i&&e.pages.length)return Promise.resolve(e);let n={client:t.client,queryKey:t.queryKey,pageParam:i,direction:a?"backward":"forward",meta:t.options.meta};h(n);let o=await c(n),{maxPages:u}=t.options,l=a?r.ZZ:r.y9;return{pages:l(e.pages,o,u),pageParams:l(e.pageParams,i,u)}};if(a&&n.length){let e="backward"===a,t={pages:n,pageParams:o},s=(e?function(e,{pages:t,pageParams:s}){return t.length>0?e.getPreviousPageParam?.(t[0],t,s[0],s):void 0}:m)(i,t);u=await d(t,s,e)}else{let t=e??n.length;do{let e=0===l?o[0]??i.initialPageParam:m(i,u);if(l>0&&null==e)break;u=await d(u,e),l++}while(l<t)}return u};t.options.persister?t.fetchFn=()=>t.options.persister?.(h,{client:t.client,queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},s):t.fetchFn=h}}}function m(e,{pages:t,pageParams:s}){let i=t.length-1;return t.length>0?e.getNextPageParam(t[i],t,s[i],s):void 0}var g=class{#u;#t;#l;#h;#c;#d;#y;#f;constructor(e={}){this.#u=e.queryCache||new l,this.#t=e.mutationCache||new c,this.#l=e.defaultOptions||{},this.#h=new Map,this.#c=new Map,this.#d=0}mount(){this.#d++,1===this.#d&&(this.#y=y.m.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#u.onFocus())}),this.#f=f.t.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#u.onOnline())}))}unmount(){this.#d--,0===this.#d&&(this.#y?.(),this.#y=void 0,this.#f?.(),this.#f=void 0)}isFetching(e){return this.#u.findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return this.#t.findAll({...e,status:"pending"}).length}getQueryData(e){let t=this.defaultQueryOptions({queryKey:e});return this.#u.get(t.queryHash)?.state.data}ensureQueryData(e){let t=this.defaultQueryOptions(e),s=this.#u.build(this,t),i=s.state.data;return void 0===i?this.fetchQuery(e):(e.revalidateIfStale&&s.isStaleByTime((0,r.d2)(t.staleTime,s))&&this.prefetchQuery(t),Promise.resolve(i))}getQueriesData(e){return this.#u.findAll(e).map(({queryKey:e,state:t})=>[e,t.data])}setQueryData(e,t,s){let i=this.defaultQueryOptions({queryKey:e}),a=this.#u.get(i.queryHash),n=a?.state.data,o=(0,r.Zw)(t,n);if(void 0!==o)return this.#u.build(this,i).setData(o,{...s,manual:!0})}setQueriesData(e,t,s){return o.jG.batch(()=>this.#u.findAll(e).map(({queryKey:e})=>[e,this.setQueryData(e,t,s)]))}getQueryState(e){let t=this.defaultQueryOptions({queryKey:e});return this.#u.get(t.queryHash)?.state}removeQueries(e){let t=this.#u;o.jG.batch(()=>{t.findAll(e).forEach(e=>{t.remove(e)})})}resetQueries(e,t){let s=this.#u;return o.jG.batch(()=>(s.findAll(e).forEach(e=>{e.reset()}),this.refetchQueries({type:"active",...e},t)))}cancelQueries(e,t={}){let s={revert:!0,...t};return Promise.all(o.jG.batch(()=>this.#u.findAll(e).map(e=>e.cancel(s)))).then(r.lQ).catch(r.lQ)}invalidateQueries(e,t={}){return o.jG.batch(()=>(this.#u.findAll(e).forEach(e=>{e.invalidate()}),e?.refetchType==="none")?Promise.resolve():this.refetchQueries({...e,type:e?.refetchType??e?.type??"active"},t))}refetchQueries(e,t={}){let s={...t,cancelRefetch:t.cancelRefetch??!0};return Promise.all(o.jG.batch(()=>this.#u.findAll(e).filter(e=>!e.isDisabled()).map(e=>{let t=e.fetch(void 0,s);return s.throwOnError||(t=t.catch(r.lQ)),"paused"===e.state.fetchStatus?Promise.resolve():t}))).then(r.lQ)}fetchQuery(e){let t=this.defaultQueryOptions(e);void 0===t.retry&&(t.retry=!1);let s=this.#u.build(this,t);return s.isStaleByTime((0,r.d2)(t.staleTime,s))?s.fetch(t):Promise.resolve(s.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(r.lQ).catch(r.lQ)}fetchInfiniteQuery(e){return e.behavior=p(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(r.lQ).catch(r.lQ)}ensureInfiniteQueryData(e){return e.behavior=p(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return f.t.isOnline()?this.#t.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#u}getMutationCache(){return this.#t}getDefaultOptions(){return this.#l}setDefaultOptions(e){this.#l=e}setQueryDefaults(e,t){this.#h.set((0,r.EN)(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){let t=[...this.#h.values()],s={};return t.forEach(t=>{(0,r.Cp)(e,t.queryKey)&&Object.assign(s,t.defaultOptions)}),s}setMutationDefaults(e,t){this.#c.set((0,r.EN)(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){let t=[...this.#c.values()],s={};return t.forEach(t=>{(0,r.Cp)(e,t.mutationKey)&&Object.assign(s,t.defaultOptions)}),s}defaultQueryOptions(e){if(e._defaulted)return e;let t={...this.#l.queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=(0,r.F$)(t.queryKey,t)),void 0===t.refetchOnReconnect&&(t.refetchOnReconnect="always"!==t.networkMode),void 0===t.throwOnError&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.queryFn===r.hT&&(t.enabled=!1),t}defaultMutationOptions(e){return e?._defaulted?e:{...this.#l.mutations,...e?.mutationKey&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){this.#u.clear(),this.#t.clear()}},b=s(26715),v=function(){return null};let C=e=>{let{children:t}=e,[s]=(0,a.useState)(()=>new g({defaultOptions:{queries:{staleTime:3e5,refetchOnWindowFocus:!1}}}));return(0,i.jsxs)(b.Ht,{client:s,children:[t,(0,i.jsx)(v,{initialIsOpen:!1})]})}},75173:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>n,dQ:()=>a,g7:()=>r});let i=(0,s(51990).Z0)({initialState:{visible:!1,option:""},name:"createLibraryItem",reducers:{showCreateLibraryItemModal:(e,t)=>{e.visible=!0,e.option=t.payload},hideCreateLibraryItemModal:e=>{e.visible=!1,e.option=""}}}),{showCreateLibraryItemModal:a,hideCreateLibraryItemModal:r}=i.actions,n=i.reducer},85339:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});let i=(0,s(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},97381:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>u,Le:()=>n,jB:()=>o,tQ:()=>a,x9:()=>r});let i=(0,s(51990).Z0)({name:"auth",initialState:{status:"loading",user:null,error:null},reducers:{setAuthenticatedUser:(e,t)=>{e.status="authenticated",e.user=t.payload,e.error=null},setUnauthenticated:e=>{e.status="unauthenticated",e.user=null,e.error=null},setAuthLoading:e=>{e.status="loading"},setAuthError:(e,t)=>{e.status="unauthenticated",e.error=t.payload,e.user=null}}}),{setAuthenticatedUser:a,setUnauthenticated:r,setAuthLoading:n,setAuthError:o}=i.actions,u=i.reducer}},e=>{var t=t=>e(e.s=t);e.O(0,[837,635,6967,4601,8441,1684,7358],()=>t(64638)),_N_E=e.O()}]);