(()=>{var e={};e.id=9454,e.ids=[9454],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7053:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Kobo-project\\\\data-analysis-tool-frontend\\\\app\\\\[locale]\\\\(main)\\\\library\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\library\\page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21324:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=r(65239),a=r(48088),n=r(88170),l=r.n(n),o=r(30893),i={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>o[e]);r.d(t,i);let d={children:["",{children:["[locale]",{children:["(main)",{children:["library",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,7053)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\library\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,84606)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,72121)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\library\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/(main)/library/page",pathname:"/[locale]/library",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},21820:e=>{"use strict";e.exports=require("os")},24119:(e,t,r)=>{Promise.resolve().then(r.bind(r,70618))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70618:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>$});var s=r(60687),a=r(21650),n=r(3984),l=r(29494),o=r(8693),i=r(54050),d=r(43210),c=r.n(d),u=r(86429),p=r(73678),h=r(93617),m=r(78272),x=r(3589),g=r(96362),y=r(55629),b=r(26312),f=r(43782),j=r(6986),v=r(85814),w=r.n(v);let q=[{id:"select",header:({table:e})=>(0,s.jsx)(f.Sc,{className:"w-6 h-6 data-[state=checked]:bg-primary-500 data-[state=checked]:text-neutral-500 rounded border border-neutral-100 data-[state=checked]:border-neutral-100 cursor-pointer",checked:e.getIsAllPageRowsSelected()||e.getIsSomePageRowsSelected()&&"indeterminate",onCheckedChange:t=>e.toggleAllPageRowsSelected(!!t),"aria-label":"Select all"}),cell:({row:e})=>(0,s.jsx)(f.Sc,{className:"w-6 h-6 bg-neutral-100 rounded border border-neutral-400 data-[state=checked]:bg-neutral-100 data-[state=checked]:text-primary-500 data-[state=checked]:border-primary-500 cursor-pointer",checked:e.getIsSelected(),onCheckedChange:t=>e.toggleSelected(!!t),"aria-label":"Select row"}),enableHiding:!1,enableSorting:!1},{accessorKey:"name",header:"Template Name",cell:({row:e})=>{let t=e.original.id,r=(0,j.l)(t);return(0,s.jsx)(w(),{href:`library/template/${r}/form-builder`,className:"cursor-pointer text-primary-500 hover:text-primary-600 hover:underline transition-all duration-300",children:e.getValue("name")})}},{accessorKey:"description",header:"Description"},{id:"owner",accessorFn:e=>e.user?.name??"unknown",header:"Owner",cell:({getValue:e})=>e()},{accessorKey:"sector",header:"Sector"},{accessorKey:"country",header:"Country"},{accessorKey:"updatedAt",header:"Last Modified"}];var C=r(77618);let N="data-table-column-visibility",k=()=>{let{user:e}=(0,a.A)(),t=(0,C.c3)(),[r,f]=(0,d.useState)(!1),{data:j,isLoading:v,isError:w}=(0,l.I)({queryKey:["templates",e?.id],queryFn:n.QK,enabled:!!e?.id}),[k,S]=(0,d.useState)(""),[A,P]=(0,d.useState)({}),[E,K]=(0,d.useState)(null),[I,R]=(0,d.useState)(!1),[T,$]=(0,d.useState)({}),M=(0,o.jE)();(0,d.useEffect)(()=>{let e=localStorage.getItem(N);if(e)try{P(JSON.parse(e))}catch(e){console.error("Failed to parse saved column visibility",e)}},[]),(0,d.useEffect)(()=>{Object.keys(A).length>0&&localStorage.setItem(N,JSON.stringify(A))},[A]);let O=(0,i.n)({mutationFn:n.nh,onSuccess:()=>{M.invalidateQueries({queryKey:["templates",e?.id]}),f(!1)},onError:()=>{}}),F=c().useMemo(()=>Object.keys(T).filter(e=>T[e]).map(e=>parseInt(e,10)),[T]);return v||!j?(0,s.jsx)(u.A,{}):w?(0,s.jsx)("p",{className:"text-red-500",children:t("error_loading_data")}):(0,s.jsxs)("div",{children:[(0,s.jsx)(p.R,{showModal:r,onClose:()=>f(!1),title:t("deleteTemplates"),description:t("confirmDeleteTemplates"),confirmButtonText:t("delete"),confirmButtonClass:"btn-danger",onConfirm:()=>O.mutate({templateIds:F})}),(0,s.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsx)("h1",{className:"sub-heading-text",children:t("templates")}),(0,s.jsx)("div",{children:(0,s.jsx)("input",{type:"text",value:k,onChange:e=>S(e.target.value),placeholder:t("searchTemplates"),className:"input-field text-sm"})}),E&&(0,s.jsxs)(y.rI,{open:I,onOpenChange:e=>R(e),children:[(0,s.jsx)(b.ty,{asChild:!0,children:(0,s.jsxs)("button",{className:"btn-outline text-sm text-neutral-700 border-neutral-400 font-normal",children:[t("showHideColumns"),I?(0,s.jsx)(m.A,{size:16}):(0,s.jsx)(x.A,{size:16})]})}),(0,s.jsx)(y.SQ,{align:"start",className:"border bg-neutral-100 border-neutral-200 shadow-md px-2",children:E.getAllColumns().filter(e=>e.getCanHide()).map(e=>(0,s.jsx)(y.hO,{className:"capitalize cursor-pointer hover:bg-neutral-200",checked:A[e.id]??!0,onCheckedChange:t=>P(r=>({...r,[e.id]:t})),children:e.id},e.id))})]}),(0,s.jsxs)("button",{type:"button",className:"ml-auto btn-danger text-sm",onClick:()=>{0!==F.length&&f(!0)},disabled:0===F.length,children:[t("delete")," ",(0,s.jsx)(g.A,{size:16})]})]}),j.length>0?(0,s.jsx)(h.x,{columns:q,data:j,globalFilter:k,setGlobalFilter:S,onTableInit:e=>K(e),onRowSelectionChange:$,columnVisibility:A,setColumnVisibility:P}):(0,s.jsxs)("div",{className:"text-center py-16 space-y-4",children:[(0,s.jsx)("p",{className:"text-lg",children:t("getStarted")}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:t("advancedUsers")})]})]})]})},S=()=>{let e=(0,C.c3)();return[{id:"sno",header:e("sn"),cell:({row:e})=>e.index+1},{accessorKey:"label",header:e("question")},{accessorKey:"inputType",header:e("inputType"),cell:({row:e})=>{let t=e.getValue("inputType");return t.charAt(0).toUpperCase()+t.slice(1)}},{accessorKey:"questionOptions",header:e("options"),cell:({row:e})=>{let t=e.original.inputType;if("selectone"===t||"selectmany"===t){let t=e.original.questionOptions||[];return(0,s.jsx)("div",{className:"space-y-1",children:t.map((e,t)=>(0,s.jsxs)("div",{className:"text-sm",children:[(0,s.jsxs)("span",{className:"font-medium",children:["-",e.label]}),e.sublabel&&(0,s.jsxs)("span",{className:"text-neutral-500 ml-2",children:["(",e.sublabel,")"]})]},t))})}return null}},{accessorKey:"hint",header:e("hint")},{accessorKey:"isRequired",header:e("required"),cell:({row:e})=>e.getValue("isRequired")?"Yes":"No"},{accessorKey:"updatedAt",header:e("lastModified"),cell:({row:e})=>new Date(e.getValue("updatedAt")).toLocaleDateString()}]};var A=r(56090),P=r(93772),E=r(96752);let K=({columns:e,data:t,globalFilter:r,setGlobalFilter:a,onTableInit:n,columnVisibility:l,setColumnVisibility:o,onRowSelectionChange:i,rowSelection:d,onRowClick:u})=>{let[p,h]=c().useState({pageIndex:0,pageSize:8}),[m,x]=c().useState([]),[g,y]=c().useState([]),[b,f]=c().useState({}),[j,v]=c().useState({}),w=void 0!==d?d:j,q=(0,A.N4)({data:t,columns:e,onPaginationChange:h,onColumnFiltersChange:x,onGlobalFilterChange:a,onColumnVisibilityChange:o??f,onRowSelectionChange:e=>{let t="function"==typeof e?e(w):e;void 0===d&&v(t),i&&i(t)},onSortingChange:y,getCoreRowModel:(0,P.HT)(),getFilteredRowModel:(0,P.hM)(),getPaginationRowModel:(0,P.kW)(),getSortedRowModel:(0,P.h5)(),enableRowSelection:!0,enableSorting:!0,enableSortingRemoval:!0,state:{pagination:p,columnFilters:m,globalFilter:r,columnVisibility:l??b,rowSelection:w,sorting:g}}),N=(0,C.c3)();return c().useEffect(()=>{n&&n(q)},[n,q]),c().useEffect(()=>{void 0!==d&&q.setRowSelection(d)},[d,q]),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"rounded-md border border-neutral-400 overflow-hidden",children:(0,s.jsxs)(E.XI,{className:"min-w-full",children:[(0,s.jsx)(E.A0,{className:"h-20",children:q.getHeaderGroups().map(e=>(0,s.jsx)(E.Hj,{className:"text-sm border-neutral-400",children:e.headers.map(e=>(0,s.jsxs)(E.nd,{className:`py-1 px-6 text-left bg-primary-500 text-neutral-100 font-semibold ${0===e.index?"w-12 py-3 px-6":""}`,style:{cursor:e.column.getCanSort()?"pointer":"default"},children:[(0,s.jsx)("div",{onClick:e.column.getToggleSortingHandler(),children:(0,s.jsx)("div",{children:e.isPlaceholder?null:(0,A.Kv)(e.column.columnDef.header,e.getContext())})}),e.column.getCanFilter()&&(0,s.jsx)("input",{placeholder:N("search"),value:e.column.getFilterValue()||"",onChange:t=>e.column.setFilterValue(t.target.value),className:"input-field max-w-48 text-sm my-1 px-2 py-1 bg-neutral-100 text-neutral-700 font-light border-none rounded-md"})]},e.id))},e.id))}),(0,s.jsx)(E.BF,{children:q.getPaginationRowModel().rows.length?q.getPaginationRowModel().rows.map(e=>(0,s.jsx)(E.Hj,{"data-state":e.getIsSelected()&&"selected",className:"hover:bg-neutral-50 text-sm border-neutral-400",onClick:()=>u?.(e.original),children:e.getVisibleCells().map((e,t)=>(0,s.jsx)(E.nA,{className:`py-4 px-6 max-w-48  ${0===t?"py-3 px-6":""} text-neutral-700 `,children:(0,A.Kv)(e.column.columnDef.cell,e.getContext())},e.id))},e.id)):(0,s.jsx)(E.Hj,{children:(0,s.jsx)(E.nA,{colSpan:e.length,className:"h-24 text-center",children:N("no_results")})})})]})}),(0,s.jsx)("div",{className:"flex items-center justify-end space-x-2 py-4",children:t.length>p.pageSize&&(0,s.jsxs)("div",{className:"flex items-center justify-end space-x-2 py-4",children:[(0,s.jsx)("button",{className:"btn-primary",onClick:()=>q.previousPage(),disabled:!q.getCanPreviousPage(),children:N("previous")}),(0,s.jsx)("button",{className:"btn-primary",onClick:()=>q.nextPage(),disabled:!q.getCanNextPage(),children:N("next")})]})})]})},I=({questions:e})=>{let[t,r]=(0,d.useState)(""),a=S();return(0,s.jsx)("div",{className:"space-y-4",children:(0,s.jsx)(K,{columns:a,data:e,globalFilter:t,setGlobalFilter:r})})};var R=r(75531);let T=()=>{let{user:e}=(0,a.A)(),t=e?.id,{data:r,isLoading:s,isError:n,error:o,refetch:i}=(0,l.I)({queryKey:["questionBlockQuestions",t],queryFn:()=>(0,R.dI)(),enabled:!!t,retry:1});return{questions:Array.isArray(r)?r:[],isLoading:s,isError:n,error:o,refetch:i,userId:t}};function $(){let{questions:e}=T(),t=(0,C.c3)();return(0,s.jsxs)("div",{className:"p-6 space-y-6 section gap-8",children:[(0,s.jsx)("h1",{className:"text-2xl font-semibold",children:t("myLibrary")}),(0,s.jsx)(k,{}),(0,s.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,s.jsx)("h1",{className:"sub-heading-text hover:text-neutral-700",children:(0,s.jsx)(w(),{href:"/library/question-block/form-builder",children:t("questionBlocks")})}),e.length>0?(0,s.jsx)(I,{questions:e}):(0,s.jsxs)("div",{className:"text-center py-16 space-y-4",children:[(0,s.jsx)("p",{className:"text-lg",children:t("getStarted")}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:t("advancedUsers")})]})]})]})}},73678:(e,t,r)=>{"use strict";r.d(t,{R:()=>n});var s=r(60687);r(43210);var a=r(38587);let n=({showModal:e,onClose:t,onConfirm:r,title:n,description:l,confirmButtonText:o,cancelButtonText:i,confirmButtonClass:d,children:c})=>(0,s.jsxs)(a.A,{isOpen:e,onClose:t,className:"p-6 rounded-md max-w-xl",children:[(0,s.jsx)("h2",{className:"text-lg font-semibold text-neutral-700",children:n}),(0,s.jsx)("div",{className:"text-neutral-700 mt-2",children:l}),c&&(0,s.jsx)("div",{className:"mt-6 space-y-4",children:c}),(0,s.jsxs)("div",{className:"flex justify-end gap-4 mt-6",children:[(0,s.jsx)("button",{className:"btn-outline",onClick:t,type:"button",children:i||"Cancel"}),(0,s.jsx)("button",{className:`font-medium rounded-md shadow-md text-neutral-100 flex items-center justify-center gap-2 px-4 py-2 active:scale-95 transition-all duration-300 ${d}`,onClick:r,type:"button",children:o})]})]})},74075:e=>{"use strict";e.exports=require("zlib")},75531:(e,t,r)=>{"use strict";r.d(t,{Af:()=>o,K4:()=>n,ae:()=>p,dI:()=>u,eL:()=>h,ej:()=>l,gf:()=>m,ku:()=>d,sr:()=>c,ul:()=>i});var s=r(12810);let a=e=>{if("project"===e)return"/questions";if("template"===e)return"/template-questions";if("questionBlock"===e)return"/question-blocks";throw Error("Unsupported context type")},n=async({projectId:e})=>{let{data:t}=await s.A.get(`/questions/${e}`);return t.questions},l=async({templateId:e})=>{let{data:t}=await s.A.get(`/template-questions/${e}`);return t.questions},o=async({contextType:e,contextId:t,dataToSend:r,position:n})=>{let l="questionBlock"===e?`${a(e)}`:`${a(e)}/${t}`;if(!r.label||!r.inputType)throw Error("Label and inputType are required");let o=["selectone","selectmany"].includes(r.inputType),i=r.file instanceof File,d=Array.isArray(r.questionOptions)&&r.questionOptions.length>0;if(o&&!i&&!d)throw Error("Options are required for select input types");if(i){let e=new FormData;e.append("label",r.label),e.append("isRequired",r.isRequired?"true":"false"),e.append("inputType",r.inputType),r.hint&&e.append("hint",r.hint),r.placeholder&&e.append("placeholder",r.placeholder),e.append("position",String(n||1)),e.append("file",r.file);try{let{data:t}=await s.A.post(l,e,{headers:{"Content-Type":"multipart/form-data"}});return t}catch(e){throw console.error("Upload error details:",e.response?.data||e.message),Error(`Failed to upload question with file: ${e.response?.data?.message||e.message}`)}}try{let{data:e}=await s.A.post(l,{label:r.label,isRequired:r.isRequired,hint:r.hint,placeholder:r.placeholder,inputType:r.inputType,questionOptions:r.questionOptions,position:n||1});return e}catch(e){throw console.error("API error details:",e.response?.data||e.message),Error(`Failed to add question: ${e.response?.data?.message||e.message}`)}},i=async({contextType:e,id:t,projectId:r})=>{let{data:n}=await s.A.delete(`${a(e)}/${t}?projectId=${r}`);return n},d=async({id:e,contextType:t,contextId:r})=>{let{data:n}=await s.A.post(`${a(t)}/duplicate/${e}?projectId=${r}`,"questionBlock"===t?{}:"project"===t?{projectId:r}:{templateId:r});return n},c=async({id:e,contextType:t,dataToSend:r,contextId:n})=>{let{data:l}=await s.A.patch(`${a(t)}/${e}?projectId=${n}`,r);return l},u=async()=>{try{return(await s.A.get("/question-blocks")).data.questions||[]}catch(e){throw console.error("Error fetching question block questions:",e),e}},p=async({contextType:e,contextId:t,questionPositions:r})=>{if("project"!==e)throw Error("Question position updates are only supported for projects");let n=`${a(e)}/positions?projectId=${t}`;try{let{data:e}=await s.A.patch(n,{questionPositions:r});return e}catch(e){throw console.error("Update failed - Full error:",e),console.error("Update failed - Error details:",{status:e.response?.status,statusText:e.response?.statusText,data:e.response?.data,message:e.message,config:{url:e.config?.url,method:e.config?.method,data:e.config?.data}}),e}},h=async({contextType:e,contextId:t,positionUpdates:r})=>{if("project"!==e)throw Error("Unified position updates are only supported for projects");let n=`${a(e)}/unified-positions?projectId=${t}`;try{let{data:e}=await s.A.patch(n,{positionUpdates:r});return e}catch(e){throw console.error("Unified position update failed - Full error:",e),console.error("Unified position update failed - Error details:",{status:e.response?.status,statusText:e.response?.statusText,data:e.response?.data,message:e.message,config:{url:e.config?.url,method:e.config?.method,data:e.config?.data}}),e}},m=async({projectId:e})=>{let{data:t}=await s.A.get(`/projects/getalldata/${e}`);return t.data}},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},87167:(e,t,r)=>{Promise.resolve().then(r.bind(r,7053))},94735:e=>{"use strict";e.exports=require("events")},96362:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("trash",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}]])}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,7404,1658,6560,7618,63,7605,3851,8581,6226,5233],()=>r(21324));module.exports=s})();