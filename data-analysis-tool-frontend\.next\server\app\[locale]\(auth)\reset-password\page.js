(()=>{var e={};e.id=5127,e.ids=[5127],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11860:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19169:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28559:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30408:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Kobo-project\\\\data-analysis-tool-frontend\\\\app\\\\[locale]\\\\(auth)\\\\reset-password\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(auth)\\reset-password\\page.tsx","default")},32470:(e,t,s)=>{Promise.resolve().then(s.bind(s,30408))},33873:e=>{"use strict";e.exports=require("path")},38038:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("shield-check",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},38587:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var r=s(60687),a=s(88920),i=s(57101),l=s(74699),o=s(11860);s(43210);let n=({children:e,className:t,isOpen:s,onClose:n,preventOutsideClick:c=!1})=>(0,r.jsx)(a.N,{children:s&&(0,r.jsx)(i.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-neutral-900/50 flex items-center justify-center z-40 p-4 overflow-y-auto",onClick:e=>{c||n()},children:(0,r.jsxs)(i.P.div,{initial:{scale:.6,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.6,opacity:0},transition:{duration:.3,ease:l.am},className:`relative section flex flex-col max-w-4xl max-h-[90vh] overflow-y-auto ${t}`,onClick:e=>e.stopPropagation(),children:[(0,r.jsx)(o.A,{onClick:n,className:"absolute self-end cursor-pointer text-neutral-700 hover:text-neutral-900 active:scale-90 transition-all duration-300"}),e]})})})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56030:(e,t,s)=>{Promise.resolve().then(s.bind(s,82007))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},82007:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>f});var r=s(60687),a=s(43210),i=s(38587),l=s(38038),o=s(19169),n=s(28559),c=s(85814),d=s.n(c),p=s(77618);let x=({email:e,showModal:t,setShowModal:s})=>{let a=(0,p.c3)(),c=[a("checkEmailSteps1"),a("checkEmailSteps2"),a("checkEmailSteps3"),a("checkEmailSteps4")];return(0,r.jsxs)(i.A,{isOpen:t,onClose:()=>s(!1),className:"flex flex-col gap-8",children:[(0,r.jsxs)("div",{className:"flex flex-col items-center gap-2",children:[(0,r.jsx)(l.A,{size:36}),(0,r.jsx)("h1",{className:"text-2xl tablet:text-3xl font-semibold text-center",children:a("checkYourEmail")}),(0,r.jsxs)("p",{className:"text-neutral-700 text-center",children:[a("resetLinkSentTo")," ",e]})]}),(0,r.jsxs)("div",{className:"rounded-md p-4 bg-neutral-200 text-neutral-700 flex flex-col gap-2",children:[(0,r.jsxs)("span",{className:"flex items-center gap-2 text-lg font-medium",children:[(0,r.jsx)(o.A,{size:18})," ",a("whatToDoNext")]}),(0,r.jsx)("ol",{children:c.map((e,t)=>(0,r.jsx)("li",{children:`${t+1}. ${e}`},t))})]}),(0,r.jsxs)(d(),{href:"/",className:"text-neutral-700 self-center flex items-center gap-2",children:[(0,r.jsx)(n.A,{size:16})," ",a("backToSignin")]})]})};var u=s(27605),m=s(12810),h=s(64668);let f=()=>{let{register:e,formState:{errors:t,isSubmitting:s},handleSubmit:i,getValues:o}=(0,u.mN)(),[c,f]=(0,a.useState)(!1),j=(0,p.c3)(),b=async e=>{try{await m.A.post("/users/forgetpassword",{email:e.email}),f(!0)}catch(e){console.error(e instanceof Error?e.message:e)}};return(0,r.jsxs)("div",{className:"flex items-center justify-center min-h-screen",children:[(0,r.jsx)(x,{email:o("email"),showModal:c,setShowModal:f}),(0,r.jsxs)("div",{className:"section flex flex-col gap-8 w-11/12 mobile:w-4/5 tablet:w-lg",children:[(0,r.jsxs)("div",{className:"flex flex-col items-center gap-2",children:[(0,r.jsx)(l.A,{size:36}),(0,r.jsx)("h1",{className:"text-2xl tablet:text-3xl font-semibold text-center",children:j("resetYourPassword")}),(0,r.jsx)("p",{className:"text-neutral-700 text-center",children:j("resetPasswordInstructions")})]}),(0,r.jsxs)("form",{className:"flex flex-col gap-4 ",onSubmit:i(b),noValidate:!0,children:[(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsx)("label",{htmlFor:"email",className:"label-text",children:j("email")}),(0,r.jsx)("input",{...e("email",{required:j("pleaseEnterYourEmail")}),id:"email",type:"email",className:"input-field",placeholder:"eg: <EMAIL>"}),t.email&&(0,r.jsx)("p",{className:"text-sm text-red-500",children:`${t.email.message}`})]}),(0,r.jsx)("button",{type:"submit",className:"btn-primary",children:s?(0,r.jsxs)("span",{className:"flex items-center gap-2",children:[j("sending"),(0,r.jsx)("div",{className:"animate-spin border-x-2 border-neutral-100 rounded-full size-4"})]}):(0,r.jsx)("span",{className:"flex items-center gap-2",children:j("sendResetLink")})})]}),(0,r.jsxs)(d(),{href:"/",className:"text-neutral-700 self-center flex items-center gap-2",children:[(0,r.jsx)(n.A,{size:16})," ",j("backToSignin")]}),(0,r.jsx)(h.A,{})]})]})}},82260:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>x,tree:()=>c});var r=s(65239),a=s(48088),i=s(88170),l=s.n(i),o=s(30893),n={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>o[e]);s.d(t,n);let c={children:["",{children:["[locale]",{children:["(auth)",{children:["reset-password",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,30408)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(auth)\\reset-password\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,72121)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,58014)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(auth)\\reset-password\\page.tsx"],p={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/(auth)/reset-password/page",pathname:"/[locale]/reset-password",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,7404,1658,6560,7618,63,7605,6226],()=>s(82260));module.exports=r})();