"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.checkPermission = void 0;
const permissionChecker_1 = require("../utils/permissionChecker");
const prisma_1 = require("../utils/prisma");
const checkPermission = (actionKeys) => {
    return (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
        try {
            if (!req.user)
                return res.status(400).json({ message: "User ID not found" });
            const userId = Number(req.user.id);
            const projectIdStr = req.params.projectId || req.query.projectId;
            const projectId = parseInt(projectIdStr);
            if (isNaN(projectId))
                return res.status(400).json({ message: "Invalid project ID" });
            const project = yield prisma_1.prisma.project.findUnique({
                where: { id: projectId },
                select: { id: true, userId: true },
            });
            if (!project)
                return res.status(404).json({ message: "Project not found" });
            if (project.userId === userId)
                return next();
            const projectUser = yield prisma_1.prisma.projectUser.findUnique({
                where: { userId_projectId: { userId, projectId } },
            });
            if (!projectUser)
                return res.status(403).json({ message: "No access to project" });
            const keys = Array.isArray(actionKeys) ? actionKeys : [actionKeys];
            const allowed = keys.some((key) => (0, permissionChecker_1.hasPermission)(projectUser.permission, key));
            if (!allowed)
                return res.status(403).json({ message: "Permission denied" });
            next();
        }
        catch (error) {
            console.error("checkPermission error:", error);
            res.status(500).json({ message: "Internal server error" });
        }
    });
};
exports.checkPermission = checkPermission;
