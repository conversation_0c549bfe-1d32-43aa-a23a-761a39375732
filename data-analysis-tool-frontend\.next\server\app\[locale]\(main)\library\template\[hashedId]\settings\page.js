(()=>{var e={};e.id=6278,e.ids=[6278],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},17090:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("file-pen",[["path",{d:"M12.5 22H18a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v9.5",key:"1couwa"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M13.378 15.626a1 1 0 1 0-3.004-3.004l-5.01 5.012a2 2 0 0 0-.506.854l-.837 2.87a.5.5 0 0 0 .62.62l2.87-.837a2 2 0 0 0 .854-.506z",key:"1y4qbx"}]])},17118:(e,t,s)=>{Promise.resolve().then(s.bind(s,29695))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20174:(e,t,s)=>{"use strict";s.d(t,{F:()=>n});var r=s(60687),a=s(16189),l=s(85814),i=s.n(l);s(43210);let n=({items:e})=>{let t=(0,a.usePathname)(),s=e=>t.startsWith(e);return(0,r.jsx)("div",{className:"border-y border-neutral-400 rounded-md bg-primary-500 my-4 shadow-md",children:(0,r.jsx)("div",{className:"flex items-center",children:e.map(e=>e.disabled?(0,r.jsxs)("div",{className:"flex items-center gap-2 font-medium transition-all duration-300 p-4 border-b-2 text-gray-400 cursor-not-allowed",children:[e.icon,e.label]},e.route):(0,r.jsxs)(i(),{href:e.route,className:`flex items-center gap-2 font-medium transition-all duration-300 p-4 border-b-2 text-neutral-100 ${s(e.route)?"border-neutral-100":"border-transparent hover:border-neutral-400"}`,children:[e.icon,e.label]},e.route))})})}},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28559:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29695:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>E});var r=s(60687),a=s(43210),l=s(27605),i=s(12810),n=s(16189),o=s(6986),d=s(8693),c=s(29494),p=s(54050),u=s(54864),m=s(19150),x=s(68292),h=s(10022),f=s(11437),b=s(57800),y=s(86429),j=s(32833),v=s(15566),g=s(40480),N=s(21650),C=s(73678),q=s(3984),k=s(77618);let P=async({templateId:e,dataToSend:t})=>{let{data:s}=await i.A.patch(`/libraries/${e}`,t);return s},E=()=>{let[e,t]=(0,a.useState)(!1);(0,a.useEffect)(()=>{t(!0)},[]);let{register:s,formState:{isSubmitting:i,errors:E,isSubmitted:D},handleSubmit:A,setValue:w,reset:I}=(0,l.mN)(),M=(0,n.useRouter)(),[K,F]=(0,a.useState)(!1),S=(0,k.c3)(),[z,_]=(0,a.useState)(null),[O,U]=(0,a.useState)(null),[T,R]=(0,a.useState)(!1),[$,B]=(0,a.useState)(!1),[G,Q]=(0,a.useState)(null);(0,a.useEffect)(()=>{s("country",{required:S("pleaseSelectCountry")}),s("sector",{required:S("pleaseSelectSector")})},[s]),(0,a.useEffect)(()=>{w("country",z,{shouldValidate:D}),w("sector",O,{shouldValidate:D})},[w,z,O]);let{hashedId:V}=(0,n.useParams)(),H=(0,o.D)(V),{user:W}=(0,N.A)(),L=(0,d.jE)();(0,a.useEffect)(()=>()=>{H&&W?.id&&L.cancelQueries({queryKey:["templates",W.id,H]})},[H,W?.id,L]);let{data:J,isLoading:X,isError:Y}=(0,c.I)({queryKey:["templates",W?.id,H],queryFn:()=>(0,q.J2)({templateId:H}),enabled:!!H&&!!W?.id});(0,a.useEffect)(()=>{J&&(I({templateName:J.name||"",description:J.description||"",country:J.country||"",sector:J.sector||""}),_(J.country||null),U(J.sector||null))},[J,I]);let Z=(0,u.wA)(),ee=(0,p.n)({mutationFn:P,onSuccess:()=>{L.invalidateQueries({queryKey:["templates"],exact:!1}),Z((0,m.Ds)({message:S("templateUpdated"),type:"success"}))},onError:e=>{Z((0,m.Ds)({message:S("templateUpdateFailed")+e.message,type:"error"}))}}),et=(0,p.n)({mutationFn:()=>(0,q.I7)(H),onSuccess:()=>{F(!0),B(!1),L.cancelQueries({queryKey:["templates",W?.id,H]}),L.removeQueries({queryKey:["template",W?.id,H]}),L.invalidateQueries({queryKey:["templates"],exact:!1}),Z((0,m.Ds)({message:S("templateDeleted"),type:"success"})),setTimeout(()=>{M.push("/library")},1e3)},onError:e=>{B(!1),console.error("Template deletion error:",e),Z((0,m.Ds)({message:S("templateDeleteFailed"),type:"error"}))}}),es=async e=>{ee.mutate({templateId:H,dataToSend:{name:e.templateName,description:e.description,country:e.country,sector:e.sector}})};return e?K||X?(0,r.jsx)(y.A,{}):V&&null!==H?Y&&!K?(0,r.jsx)("p",{className:"text-red-500",children:S("failedFetchTemplate")}):(0,r.jsxs)("form",{className:"flex flex-col gap-8",onSubmit:A(es),children:[(0,r.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsxs)("label",{htmlFor:"template-name",className:"label-text",children:[(0,r.jsx)(h.A,{size:16})," ",S("templateName")]}),(0,r.jsx)("input",{...s("templateName",{required:S("templateNameRequired")}),id:"template-name",type:"text",className:"input-field",placeholder:S("templateNamePlaceholder")}),E.templateName&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:`${E.templateName.message}`})]}),(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsx)("label",{htmlFor:"description",className:"label-text",children:S("description")}),(0,r.jsx)("textarea",{id:"description",...s("description"),className:"input-field resize-none",cols:4,placeholder:S("templateDescriptionPlaceholder")})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsxs)("label",{htmlFor:"country",className:"label-text",children:[(0,r.jsx)(f.A,{size:16}),S("country")]}),(0,r.jsx)(x.l,{id:"country",options:v,value:z,onChange:_}),E.country&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:`${E.country.message}`})]}),(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsxs)("label",{htmlFor:"sector",className:"label-text",children:[(0,r.jsx)(b.A,{size:16})," ",S("sector")]}),(0,r.jsx)(x.l,{id:"sector",options:Object.values(j.b),value:O&&j.b[O]?j.b[O]:S("selectOption"),onChange:e=>{U((0,g.H)(e,j.b))}}),E.sector&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:`${E.sector.message}`})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("div",{className:"flex items-center gap-4",children:(0,r.jsx)("button",{onClick:()=>{Q({title:S("confirmDelete"),description:(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("p",{children:S("templateDeleteConfirm")}),(0,r.jsxs)("ul",{className:"list-disc list-inside flex flex-col gap-2 mt-2 text-neutral-700",children:[(0,r.jsx)("li",{children:S("templateDeleteWarningData")}),(0,r.jsx)("li",{children:S("templateDeleteWarningForms")}),(0,r.jsx)("li",{children:S("templateDeleteWarningRecover")})]})]}),confirmButtonText:"Delete",confirmButtonClass:"btn-danger",onConfirm:()=>{et.mutate()}}),B(!0)},type:"button",className:"btn-danger",children:S("delete")})}),(0,r.jsx)("button",{type:"submit",className:"btn-primary self-end",children:i?(0,r.jsxs)("span",{className:"flex items-center gap-2",children:[S("saving"),(0,r.jsx)("div",{className:"size-4 animate-spin border-x border-neutral-100 rounded-full"})]}):S("saveChanges")})]})]}),G&&(0,r.jsx)(C.R,{showModal:$,onClose:()=>B(!1),title:G.title,description:G.description,confirmButtonText:G.confirmButtonText,confirmButtonClass:G.confirmButtonClass,onConfirm:G.onConfirm})]}):(0,r.jsxs)("div",{className:"error-message",children:[(0,r.jsx)("h1",{className:"text-red-500",children:S("invalidTemplateIdError")}),(0,r.jsx)("p",{className:"text-neutral-700",children:S("invalidTemplateUrl")})]}):null}},31801:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Kobo-project\\\\data-analysis-tool-frontend\\\\app\\\\[locale]\\\\(main)\\\\library\\\\template\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\library\\template\\layout.tsx","default")},33873:e=>{"use strict";e.exports=require("path")},39197:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Kobo-project\\\\data-analysis-tool-frontend\\\\app\\\\[locale]\\\\(main)\\\\library\\\\template\\\\[hashedId]\\\\settings\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\library\\template\\[hashedId]\\settings\\page.tsx","default")},46576:(e,t,s)=>{Promise.resolve().then(s.bind(s,47172))},47172:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>j});var r=s(60687),a=s(86429),l=s(17090),i=s(84027),n=s(16189),o=s(20174);let d=()=>{let{hashedId:e}=(0,n.useParams)(),t=[{label:"Form Builder",icon:(0,r.jsx)(l.A,{size:16}),route:`/library/template/${e}/form-builder`},{label:"Settings",icon:(0,r.jsx)(i.A,{size:16}),route:`/library/template/${e}/settings`}];return(0,r.jsx)(o.F,{items:t})};var c=s(21650),p=s(3984),u=s(6986),m=s(29494),x=s(28559),h=s(85814),f=s.n(h),b=s(43210),y=s(77618);let j=({children:e})=>{let[t,s]=(0,b.useState)(!1);(0,b.useEffect)(()=>{s(!0)},[]);let{hashedId:l}=(0,n.useParams)(),i=(0,u.D)(l),{user:o}=(0,c.A)(),h=(0,y.c3)(),{data:j,isLoading:v,isError:g}=(0,m.I)({queryKey:["templates",o?.id,i],queryFn:()=>(0,p.J2)({templateId:i}),enabled:!!i&&!!o?.id});return t?v?(0,r.jsx)(a.A,{}):l&&null!==i?g?(0,r.jsx)("p",{className:"text-red-500",children:h("failedFetchTemplate")}):(0,r.jsxs)("div",{className:"section flex flex-col gap-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h1",{className:"heading-text capitalize",children:j?.name}),(0,r.jsxs)(f(),{href:"/library",className:"flex items-center gap-2",children:[(0,r.jsx)(x.A,{size:16}),h("backToLibrary")]})]}),(0,r.jsx)(d,{}),(0,r.jsx)("div",{className:"px-8",children:e})]}):(0,r.jsxs)("div",{className:"error-message",children:[(0,r.jsx)("h1",{className:"text-red-500",children:h("errorInvalidTemplateId")}),(0,r.jsx)("p",{className:"text-neutral-700",children:h("invalidUrlProjectId")})]}):null}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73678:(e,t,s)=>{"use strict";s.d(t,{R:()=>l});var r=s(60687);s(43210);var a=s(38587);let l=({showModal:e,onClose:t,onConfirm:s,title:l,description:i,confirmButtonText:n,cancelButtonText:o,confirmButtonClass:d,children:c})=>(0,r.jsxs)(a.A,{isOpen:e,onClose:t,className:"p-6 rounded-md max-w-xl",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-neutral-700",children:l}),(0,r.jsx)("div",{className:"text-neutral-700 mt-2",children:i}),c&&(0,r.jsx)("div",{className:"mt-6 space-y-4",children:c}),(0,r.jsxs)("div",{className:"flex justify-end gap-4 mt-6",children:[(0,r.jsx)("button",{className:"btn-outline",onClick:t,type:"button",children:o||"Cancel"}),(0,r.jsx)("button",{className:`font-medium rounded-md shadow-md text-neutral-100 flex items-center justify-center gap-2 px-4 py-2 active:scale-95 transition-all duration-300 ${d}`,onClick:s,type:"button",children:n})]})]})},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84027:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},86430:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d});var r=s(65239),a=s(48088),l=s(88170),i=s.n(l),n=s(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);s.d(t,o);let d={children:["",{children:["[locale]",{children:["(main)",{children:["library",{children:["template",{children:["[hashedId]",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,39197)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\library\\template\\[hashedId]\\settings\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,31801)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\library\\template\\layout.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,84606)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,72121)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,58014)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\library\\template\\[hashedId]\\settings\\page.tsx"],p={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/(main)/library/template/[hashedId]/settings/page",pathname:"/[locale]/library/template/[hashedId]/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},88432:(e,t,s)=>{Promise.resolve().then(s.bind(s,31801))},93558:(e,t,s)=>{Promise.resolve().then(s.bind(s,39197))},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,7404,1658,6560,7618,63,7605,3851,8581,6226,5233],()=>s(86430));module.exports=r})();