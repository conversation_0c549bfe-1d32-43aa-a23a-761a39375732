"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const libraryQuestionController_1 = require("../../controllers/libraryQuestionController");
const libraryQuestionRepository_1 = __importDefault(require("../../repositories/libraryQuestionRepository"));
const libraryTemplateRepository_1 = __importDefault(require("../../repositories/libraryTemplateRepository"));
// Mock the repositories
jest.mock("../../repositories/libraryQuestionRepository");
jest.mock("../../repositories/libraryTemplateRepository");
describe("Library Question Controller", () => {
    let mockRequest;
    let mockResponse;
    let responseObject = {};
    beforeEach(() => {
        // Reset mocks before each test
        jest.clearAllMocks();
        jest.resetAllMocks();
        // Setup mock response
        mockResponse = {
            status: jest.fn().mockReturnThis(),
            json: jest.fn().mockImplementation((result) => {
                responseObject = result;
                return mockResponse;
            }),
        };
        // Reset response object
        responseObject = {};
        // Setup default authenticated user
        mockRequest = {
            user: {
                id: 1,
            },
            params: {},
            body: {},
        };
    });
    describe("createLibraryQuestion", () => {
        beforeEach(() => {
            mockRequest.params = { libraryTemplateId: "1" };
            mockRequest.body = {
                label: "Test Question",
                inputType: "text",
                isRequired: true,
                position: 1,
            };
        });
        it("should create a library question successfully", () => __awaiter(void 0, void 0, void 0, function* () {
            const mockQuestion = {
                id: 1,
                libraryTemplateId: 1,
                label: "Test Question",
                inputType: "text",
                isRequired: true,
                position: 1,
                questionOptions: [],
                questionConditions: [],
            };
            libraryQuestionRepository_1.default.isLibraryTemplateOwner.mockResolvedValue(true);
            libraryQuestionRepository_1.default.create.mockResolvedValue(mockQuestion);
            yield (0, libraryQuestionController_1.createLibraryQuestion)(mockRequest, mockResponse);
            expect(libraryQuestionRepository_1.default.isLibraryTemplateOwner).toHaveBeenCalledWith(1, 1);
            expect(libraryQuestionRepository_1.default.create).toHaveBeenCalledWith(expect.objectContaining({
                label: "Test Question",
                inputType: "text",
                isRequired: true,
                position: 1,
                libraryTemplateId: 1,
            }));
            expect(mockResponse.status).toHaveBeenCalledWith(201);
            expect(responseObject).toHaveProperty("data.question", mockQuestion);
            expect(responseObject).toHaveProperty("message", "Library question created successfully");
        }));
        it("should return 403 when user is not the template owner", () => __awaiter(void 0, void 0, void 0, function* () {
            libraryQuestionRepository_1.default.isLibraryTemplateOwner.mockResolvedValue(false);
            yield (0, libraryQuestionController_1.createLibraryQuestion)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(403);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "User is not associated with this library template");
            expect(libraryQuestionRepository_1.default.create).not.toHaveBeenCalled();
        }));
        it("should return 400 for invalid input", () => __awaiter(void 0, void 0, void 0, function* () {
            // First we need to mock isLibraryTemplateOwner to return true
            // so that the validation check is reached
            libraryQuestionRepository_1.default.isLibraryTemplateOwner.mockResolvedValue(true);
            // Then provide invalid input
            mockRequest.body = {}; // Missing required fields
            yield (0, libraryQuestionController_1.createLibraryQuestion)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(400);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("errors");
            expect(libraryQuestionRepository_1.default.create).not.toHaveBeenCalled();
        }));
        it("should handle server errors", () => __awaiter(void 0, void 0, void 0, function* () {
            libraryQuestionRepository_1.default.isLibraryTemplateOwner.mockRejectedValue(new Error("Database error"));
            yield (0, libraryQuestionController_1.createLibraryQuestion)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(500);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "Error creating library question");
        }));
    });
    describe("getAllLibraryQuestions", () => {
        beforeEach(() => {
            mockRequest.params = { libraryTemplateId: "1" };
        });
        it("should get all library questions successfully", () => __awaiter(void 0, void 0, void 0, function* () {
            const mockQuestions = [
                {
                    id: 1,
                    libraryTemplateId: 1,
                    label: "Question 1",
                    inputType: "text",
                    isRequired: true,
                    position: 1,
                    questionOptions: [],
                    questionConditions: [],
                },
                {
                    id: 2,
                    libraryTemplateId: 1,
                    label: "Question 2",
                    inputType: "selectone",
                    isRequired: false,
                    position: 2,
                    questionOptions: [],
                    questionConditions: [],
                },
            ];
            libraryQuestionRepository_1.default.isLibraryTemplateOwner.mockResolvedValue(true);
            libraryQuestionRepository_1.default.findByLibraryTemplateId.mockResolvedValue(mockQuestions);
            yield (0, libraryQuestionController_1.getAllLibraryQuestions)(mockRequest, mockResponse);
            expect(libraryQuestionRepository_1.default.isLibraryTemplateOwner).toHaveBeenCalledWith(1, 1);
            expect(libraryQuestionRepository_1.default.findByLibraryTemplateId).toHaveBeenCalledWith(1);
            expect(mockResponse.status).toHaveBeenCalledWith(200);
            expect(responseObject).toHaveProperty("success", true);
            expect(responseObject).toHaveProperty("message", "Successfully fetched template questions");
            expect(responseObject).toHaveProperty("questions", mockQuestions);
        }));
        it("should return 403 when user is not the template owner", () => __awaiter(void 0, void 0, void 0, function* () {
            libraryQuestionRepository_1.default.isLibraryTemplateOwner.mockResolvedValue(false);
            yield (0, libraryQuestionController_1.getAllLibraryQuestions)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(403);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "User is not associated with this library template");
            expect(libraryQuestionRepository_1.default.findByLibraryTemplateId).not.toHaveBeenCalled();
        }));
        it("should handle server errors", () => __awaiter(void 0, void 0, void 0, function* () {
            libraryQuestionRepository_1.default.isLibraryTemplateOwner.mockRejectedValue(new Error("Database error"));
            yield (0, libraryQuestionController_1.getAllLibraryQuestions)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(500);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "Error retrieving library questions");
        }));
    });
    describe("getLibraryQuestionById", () => {
        beforeEach(() => {
            mockRequest.params = { id: "1" };
        });
        it("should get a library question by ID successfully", () => __awaiter(void 0, void 0, void 0, function* () {
            const mockQuestion = {
                id: 1,
                libraryTemplateId: 1,
                label: "Test Question",
                inputType: "text",
                isRequired: true,
                position: 1,
                questionOptions: [],
                questionConditions: [],
            };
            libraryQuestionRepository_1.default.findById.mockResolvedValue(mockQuestion);
            libraryQuestionRepository_1.default.isLibraryTemplateOwner.mockResolvedValue(true);
            yield (0, libraryQuestionController_1.getLibraryQuestionById)(mockRequest, mockResponse);
            expect(libraryQuestionRepository_1.default.findById).toHaveBeenCalledWith(1);
            expect(libraryQuestionRepository_1.default.isLibraryTemplateOwner).toHaveBeenCalledWith(1, 1);
            expect(mockResponse.status).toHaveBeenCalledWith(200);
            expect(responseObject).toHaveProperty("data.question", mockQuestion);
            expect(responseObject).toHaveProperty("message", "Library question retrieved successfully");
        }));
        it("should return 404 when question not found", () => __awaiter(void 0, void 0, void 0, function* () {
            libraryQuestionRepository_1.default.findById.mockResolvedValue(null);
            yield (0, libraryQuestionController_1.getLibraryQuestionById)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(404);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "Library question not found");
        }));
        it("should return 403 when user is not the template owner", () => __awaiter(void 0, void 0, void 0, function* () {
            const mockQuestion = {
                id: 1,
                libraryTemplateId: 1,
                label: "Test Question",
                inputType: "text",
                isRequired: true,
                position: 1,
            };
            libraryQuestionRepository_1.default.findById.mockResolvedValue(mockQuestion);
            libraryQuestionRepository_1.default.isLibraryTemplateOwner.mockResolvedValue(false);
            yield (0, libraryQuestionController_1.getLibraryQuestionById)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(403);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "User is not associated with this library template");
        }));
        it("should handle server errors", () => __awaiter(void 0, void 0, void 0, function* () {
            libraryQuestionRepository_1.default.findById.mockRejectedValue(new Error("Database error"));
            yield (0, libraryQuestionController_1.getLibraryQuestionById)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(500);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "Error retrieving library question");
        }));
    });
    describe("updateLibraryQuestion", () => {
        beforeEach(() => {
            mockRequest.params = { id: "1" };
            mockRequest.body = {
                label: "Updated Question",
                isRequired: false,
            };
        });
        it("should update a library question successfully", () => __awaiter(void 0, void 0, void 0, function* () {
            const existingQuestion = {
                id: 1,
                libraryTemplateId: 1,
                label: "Test Question",
                inputType: "text",
                isRequired: true,
                position: 1,
            };
            const updatedQuestion = Object.assign(Object.assign({}, existingQuestion), { label: "Updated Question", isRequired: false, questionOptions: [], questionConditions: [] });
            libraryQuestionRepository_1.default.findById.mockResolvedValue(existingQuestion);
            libraryQuestionRepository_1.default.isLibraryTemplateOwner.mockResolvedValue(true);
            libraryQuestionRepository_1.default.update.mockResolvedValue(updatedQuestion);
            yield (0, libraryQuestionController_1.updateLibraryQuestion)(mockRequest, mockResponse);
            expect(libraryQuestionRepository_1.default.findById).toHaveBeenCalledWith(1);
            expect(libraryQuestionRepository_1.default.isLibraryTemplateOwner).toHaveBeenCalledWith(1, 1);
            expect(libraryQuestionRepository_1.default.update).toHaveBeenCalledWith(1, expect.objectContaining({
                label: "Updated Question",
                isRequired: false,
            }));
            expect(mockResponse.status).toHaveBeenCalledWith(200);
            expect(responseObject).toHaveProperty("data.question", updatedQuestion);
            expect(responseObject).toHaveProperty("message", "Library question updated successfully");
        }));
        it("should return 404 when question not found", () => __awaiter(void 0, void 0, void 0, function* () {
            libraryQuestionRepository_1.default.findById.mockResolvedValue(null);
            yield (0, libraryQuestionController_1.updateLibraryQuestion)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(404);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "Library question not found");
            expect(libraryQuestionRepository_1.default.update).not.toHaveBeenCalled();
        }));
        it("should return 403 when user is not the template owner", () => __awaiter(void 0, void 0, void 0, function* () {
            const existingQuestion = {
                id: 1,
                libraryTemplateId: 1,
                label: "Test Question",
                inputType: "text",
                isRequired: true,
                position: 1,
            };
            libraryQuestionRepository_1.default.findById.mockResolvedValue(existingQuestion);
            libraryQuestionRepository_1.default.isLibraryTemplateOwner.mockResolvedValue(false);
            yield (0, libraryQuestionController_1.updateLibraryQuestion)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(403);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "User is not associated with this library template");
            expect(libraryQuestionRepository_1.default.update).not.toHaveBeenCalled();
        }));
        it("should handle server errors", () => __awaiter(void 0, void 0, void 0, function* () {
            libraryQuestionRepository_1.default.findById.mockRejectedValue(new Error("Database error"));
            yield (0, libraryQuestionController_1.updateLibraryQuestion)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(500);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "Error updating library question");
        }));
    });
    describe("duplicateTemplateQuestion", () => {
        beforeEach(() => {
            mockRequest.params = { id: "1" };
        });
        it("should duplicate a library question successfully", () => __awaiter(void 0, void 0, void 0, function* () {
            const originalQuestion = {
                id: 1,
                libraryTemplateId: 1,
                label: "Original Question",
                inputType: "text",
                isRequired: true,
                position: 1,
            };
            const duplicatedQuestion = {
                id: 2,
                libraryTemplateId: 1,
                label: "Original Question",
                inputType: "text",
                isRequired: true,
                position: 2,
                questionOptions: [],
                questionConditions: [],
            };
            libraryQuestionRepository_1.default.findById.mockResolvedValue(originalQuestion);
            libraryTemplateRepository_1.default.isOwner.mockResolvedValue(true);
            libraryQuestionRepository_1.default.duplicateQuestion.mockResolvedValue(duplicatedQuestion);
            yield (0, libraryQuestionController_1.duplicateTemplateQuestion)(mockRequest, mockResponse);
            expect(libraryQuestionRepository_1.default.findById).toHaveBeenCalledWith(1);
            expect(libraryTemplateRepository_1.default.isOwner).toHaveBeenCalledWith(1, 1);
            expect(libraryQuestionRepository_1.default.duplicateQuestion).toHaveBeenCalledWith(1, 1);
            expect(mockResponse.status).toHaveBeenCalledWith(200);
            expect(responseObject).toHaveProperty("success", true);
            expect(responseObject).toHaveProperty("message", "Successfully duplicated the question");
            expect(responseObject).toHaveProperty("duplicatedQuestion", duplicatedQuestion);
        }));
        it("should return 404 when question not found", () => __awaiter(void 0, void 0, void 0, function* () {
            libraryQuestionRepository_1.default.findById.mockResolvedValue(null);
            yield (0, libraryQuestionController_1.duplicateTemplateQuestion)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(404);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "Question not found with the provided question id");
            expect(libraryQuestionRepository_1.default.duplicateQuestion).not.toHaveBeenCalled();
        }));
        it("should return 403 when user is not the template owner", () => __awaiter(void 0, void 0, void 0, function* () {
            const originalQuestion = {
                id: 1,
                libraryTemplateId: 1,
                label: "Original Question",
                inputType: "text",
                isRequired: true,
                position: 1,
            };
            libraryQuestionRepository_1.default.findById.mockResolvedValue(originalQuestion);
            libraryTemplateRepository_1.default.isOwner.mockResolvedValue(false);
            yield (0, libraryQuestionController_1.duplicateTemplateQuestion)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(403);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "Unauthorized user");
            expect(libraryQuestionRepository_1.default.duplicateQuestion).not.toHaveBeenCalled();
        }));
        it("should handle server errors", () => __awaiter(void 0, void 0, void 0, function* () {
            libraryQuestionRepository_1.default.findById.mockRejectedValue(new Error("Database error"));
            yield (0, libraryQuestionController_1.duplicateTemplateQuestion)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(500);
            expect(responseObject).toHaveProperty("message", expect.stringContaining("Failed to duplicate question"));
        }));
    });
    describe("deleteLibraryQuestion", () => {
        beforeEach(() => {
            mockRequest.params = { id: "1" };
        });
        it("should delete a library question successfully", () => __awaiter(void 0, void 0, void 0, function* () {
            const existingQuestion = {
                id: 1,
                libraryTemplateId: 1,
                label: "Test Question",
                inputType: "text",
                isRequired: true,
                position: 1,
            };
            libraryQuestionRepository_1.default.findById.mockResolvedValue(existingQuestion);
            libraryQuestionRepository_1.default.isLibraryTemplateOwner.mockResolvedValue(true);
            libraryQuestionRepository_1.default.delete.mockResolvedValue(undefined);
            yield (0, libraryQuestionController_1.deleteLibraryQuestion)(mockRequest, mockResponse);
            expect(libraryQuestionRepository_1.default.findById).toHaveBeenCalledWith(1);
            expect(libraryQuestionRepository_1.default.isLibraryTemplateOwner).toHaveBeenCalledWith(1, 1);
            expect(libraryQuestionRepository_1.default.delete).toHaveBeenCalledWith(1);
            expect(mockResponse.status).toHaveBeenCalledWith(200);
            expect(responseObject).toHaveProperty("message", "Library question deleted successfully");
        }));
        it("should return 404 when question not found", () => __awaiter(void 0, void 0, void 0, function* () {
            libraryQuestionRepository_1.default.findById.mockResolvedValue(null);
            yield (0, libraryQuestionController_1.deleteLibraryQuestion)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(404);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "Library question not found");
            expect(libraryQuestionRepository_1.default.delete).not.toHaveBeenCalled();
        }));
        it("should return 403 when user is not the template owner", () => __awaiter(void 0, void 0, void 0, function* () {
            const existingQuestion = {
                id: 1,
                libraryTemplateId: 1,
                label: "Test Question",
                inputType: "text",
                isRequired: true,
                position: 1,
            };
            libraryQuestionRepository_1.default.findById.mockResolvedValue(existingQuestion);
            libraryQuestionRepository_1.default.isLibraryTemplateOwner.mockResolvedValue(false);
            yield (0, libraryQuestionController_1.deleteLibraryQuestion)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(403);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "User is not associated with this library template");
            expect(libraryQuestionRepository_1.default.delete).not.toHaveBeenCalled();
        }));
        it("should handle server errors", () => __awaiter(void 0, void 0, void 0, function* () {
            libraryQuestionRepository_1.default.findById.mockRejectedValue(new Error("Database error"));
            yield (0, libraryQuestionController_1.deleteLibraryQuestion)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(500);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "Error deleting library question");
        }));
    });
});
