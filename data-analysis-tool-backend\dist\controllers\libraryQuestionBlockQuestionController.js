"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getAllLibraryQustionBlockQuestion = exports.deleteLibraryQustionBlockQuestion = exports.updateLibraryQustionBlockQuestion = exports.createLibraryQustionBlockQuestion = void 0;
const libraryQuestionBlockQuestionRepository_1 = __importDefault(require("../repositories/libraryQuestionBlockQuestionRepository"));
const libraryQuestionBlockQuestionValidator_1 = require("../validators/libraryQuestionBlockQuestionValidator");
const createLibraryQustionBlockQuestion = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const { label, inputType, hint, placeholder, isRequired, position, questionOptions, conditions, libraryQuestionBlockQuestionGroupId, } = req.body;
        const userId = Number((_a = req.user) === null || _a === void 0 ? void 0 : _a.id); // assuming req.user exists via auth middleware
        if (!userId) {
            return res.status(401).json({ message: "User not authenticated" });
        }
        const newQuestion = yield libraryQuestionBlockQuestionRepository_1.default.create({
            label,
            inputType,
            hint,
            placeholder,
            isRequired,
            position,
            libraryQuestionBlockQuestionGroupId,
            questionOptions: questionOptions === null || questionOptions === void 0 ? void 0 : questionOptions.map((option) => ({
                label: option.label,
                code: option.code,
                nextQuestionId: option.nextQuestionId,
            })),
            conditions: conditions === null || conditions === void 0 ? void 0 : conditions.map((cond) => ({
                operator: cond.operator,
                value: cond.value,
            })),
        }, userId);
        res.status(201).json(newQuestion);
    }
    catch (error) {
        console.error("Error creating question:", error);
        res.status(500).json({ message: "Internal server error" });
    }
});
exports.createLibraryQustionBlockQuestion = createLibraryQustionBlockQuestion;
const updateLibraryQustionBlockQuestion = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const id = Number(req.params.id);
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
        if (!userId || isNaN(id)) {
            res.status(400).json({
                success: false,
                message: "Invalid request",
            });
            return;
        }
        const existingQuestion = yield libraryQuestionBlockQuestionRepository_1.default.findById(id);
        if (!existingQuestion) {
            res.status(404).json({
                success: false,
                message: "Question not found",
            });
            return;
        }
        // Parse the request body without requiring all fields
        const parseResult = libraryQuestionBlockQuestionValidator_1.LibraryQuestionBlockQuestionSchema.safeParse(Object.assign(Object.assign({}, req.body), { 
            // Add dummy values for any required fields that aren't in the update
            // These won't actually be used for the update, just to pass validation
            label: req.body.label || existingQuestion.label, inputType: req.body.inputType || existingQuestion.inputType, position: req.body.position || existingQuestion.position, userId }));
        if (!parseResult.success) {
            res.status(400).json({
                success: false,
                message: "Validation error",
                errors: parseResult.error.errors,
            });
            return;
        }
        // Only include fields that were actually in the request body
        // Only include fields that were actually in the request body
        const validatedData = {};
        const fields = [
            "label",
            "inputType",
            "isRequired",
            "hint",
            "placeholder",
            "position",
            "questionOptions",
            "conditions",
        ];
        for (const field of fields) {
            if (req.body[field] !== undefined) {
                // Rename questionOptions to options
                if (field === "questionOptions") {
                    validatedData.options = parseResult.data[field];
                }
                else {
                    validatedData[field] = parseResult.data[field];
                }
            }
        }
        // Check if options are required for select input types
        if (validatedData.inputType === "selectone" ||
            validatedData.inputType === "selectmany") {
            if (!validatedData.options ||
                !Array.isArray(validatedData.options) ||
                validatedData.options.length === 0) {
                res.status(400).json({
                    success: false,
                    message: "Options must be provided for select input types",
                });
                return;
            }
        }
        // Use the updated repository method to handle options and conditions
        const updatedQuestion = yield libraryQuestionBlockQuestionRepository_1.default.updateById(id, validatedData);
        res.status(200).json({ message: "question updated success" });
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: "error updating question",
            error: error instanceof Error ? error.message : "unexpected error",
        });
        return;
    }
});
exports.updateLibraryQustionBlockQuestion = updateLibraryQustionBlockQuestion;
const deleteLibraryQustionBlockQuestion = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
        const id = Number(req.params.id);
        if (!userId || isNaN(id)) {
            return res.status(400).json({
                message: "Invalid request: User ID or Question ID is missing",
                success: false,
            });
        }
        const currentQuestion = yield libraryQuestionBlockQuestionRepository_1.default.findById(id);
        if (!currentQuestion) {
            return res.status(404).json({
                message: "Question not found",
                success: false,
            });
        }
        yield libraryQuestionBlockQuestionRepository_1.default.deleteQuestion(id);
        return res.status(200).json({
            message: "Successfully deleted question",
            success: true,
        });
    }
    catch (error) {
        return res.status(500).json({
            message: error instanceof Error ? error.message : "unexpected error",
            success: false,
        });
    }
});
exports.deleteLibraryQustionBlockQuestion = deleteLibraryQustionBlockQuestion;
const getAllLibraryQustionBlockQuestion = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        if (!req.user || !req.user.id) {
            return res.status(404).json({
                success: false,
                message: "user not found",
            });
        }
        const userId = Number(req.user.id);
        const questions = yield libraryQuestionBlockQuestionRepository_1.default.findAll(userId);
        return res
            .status(200)
            .json({ message: "Successfully fetched questions.", questions });
    }
    catch (error) {
        return res.status(500).json({
            success: false,
            message: "error fetching questions",
            error: error instanceof Error ? error.message : "unexpected error",
        });
    }
});
exports.getAllLibraryQustionBlockQuestion = getAllLibraryQustionBlockQuestion;
