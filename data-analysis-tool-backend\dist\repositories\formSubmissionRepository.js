"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const prisma_1 = require("../utils/prisma");
class FormSubmissionRepository {
    // Create a new form submission
    create(formData, userId, deviceInfo, location) {
        return __awaiter(this, void 0, void 0, function* () {
            const { projectId, status, metadata } = formData;
            return yield prisma_1.prisma.formSubmission.create({
                data: {
                    projectId,
                    userId,
                    status,
                    deviceInfo,
                    location,
                    metadata,
                    startedAt: new Date(Date.now()),
                },
            });
        });
    }
    // Get a specific form submission with its answers
    findById(id) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.formSubmission.findUnique({
                where: { id },
                include: {
                    answers: true,
                    project: {
                        include: {
                            questions: {
                                include: {
                                    questionOptions: true,
                                    questionConditions: true,
                                },
                            },
                        },
                    },
                },
            });
        });
    }
    // Get all submissions for a project
    findByProjectId(projectId) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.formSubmission.findMany({
                where: { projectId },
                include: {
                    user: true,
                    answers: {
                        include: {
                            question: true,
                            questionOption: true,
                        },
                    },
                },
                orderBy: {
                    createdAt: "desc",
                },
            });
        });
    }
    // Update a form submission
    update(id, data) {
        return __awaiter(this, void 0, void 0, function* () {
            const { status, completedAt, durationSeconds, metadata, submittedAt } = data;
            return yield prisma_1.prisma.formSubmission.update({
                where: { id },
                data: {
                    status,
                    completedAt,
                    durationSeconds,
                    metadata,
                    submittedAt,
                },
            });
        });
    }
    // Delete a form submission
    delete(id) {
        return __awaiter(this, void 0, void 0, function* () {
            yield prisma_1.prisma.formSubmission.delete({
                where: { id },
            });
        });
    }
    // Check if user has permission to access a project
    isProjectAccessible(userId, projectId) {
        return __awaiter(this, void 0, void 0, function* () {
            const project = yield prisma_1.prisma.project.findFirst({
                where: {
                    id: projectId,
                    OR: [
                        { userId: userId },
                        {
                            projectUser: {
                                some: {
                                    userId: userId,
                                },
                            },
                        },
                    ],
                },
            });
            return !!project;
        });
    }
    changeLoginRequired(id) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.formSubmission.update({
                where: { id },
                data: {
                    loginRequired: false,
                },
            });
        });
    }
    deleteMultipleSubmission(ids) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.formSubmission.deleteMany({
                where: {
                    id: {
                        in: ids,
                    },
                },
            });
        });
    }
}
exports.default = new FormSubmissionRepository();
