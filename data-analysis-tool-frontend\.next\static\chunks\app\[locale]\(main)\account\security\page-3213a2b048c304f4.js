(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2525],{5041:(e,t,s)=>{"use strict";s.d(t,{n:()=>d});var a=s(12115),r=s(34560),i=s(7165),n=s(25910),l=s(52020),o=class extends n.Q{#e;#t=void 0;#s;#a;constructor(e,t){super(),this.#e=e,this.setOptions(t),this.bindMethods(),this.#r()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){let t=this.options;this.options=this.#e.defaultMutationOptions(e),(0,l.f8)(this.options,t)||this.#e.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#s,observer:this}),t?.mutationKey&&this.options.mutationKey&&(0,l.EN)(t.mutationKey)!==(0,l.EN)(this.options.mutationKey)?this.reset():this.#s?.state.status==="pending"&&this.#s.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#s?.removeObserver(this)}onMutationUpdate(e){this.#r(),this.#i(e)}getCurrentResult(){return this.#t}reset(){this.#s?.removeObserver(this),this.#s=void 0,this.#r(),this.#i()}mutate(e,t){return this.#a=t,this.#s?.removeObserver(this),this.#s=this.#e.getMutationCache().build(this.#e,this.options),this.#s.addObserver(this),this.#s.execute(e)}#r(){let e=this.#s?.state??(0,r.$)();this.#t={...e,isPending:"pending"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset}}#i(e){i.jG.batch(()=>{if(this.#a&&this.hasListeners()){let t=this.#t.variables,s=this.#t.context;e?.type==="success"?(this.#a.onSuccess?.(e.data,t,s),this.#a.onSettled?.(e.data,null,t,s)):e?.type==="error"&&(this.#a.onError?.(e.error,t,s),this.#a.onSettled?.(void 0,e.error,t,s))}this.listeners.forEach(e=>{e(this.#t)})})}},c=s(26715),u=s(63768);function d(e,t){let s=(0,c.jE)(t),[r]=a.useState(()=>new o(s,e));a.useEffect(()=>{r.setOptions(e)},[r,e]);let n=a.useSyncExternalStore(a.useCallback(e=>r.subscribe(i.jG.batchCalls(e)),[r]),()=>r.getCurrentResult(),()=>r.getCurrentResult()),l=a.useCallback((e,t)=>{r.mutate(e,t).catch(u.l)},[r]);if(n.error&&(0,u.G)(r.options.throwOnError,[n.error]))throw n.error;return{...n,mutate:l,mutateAsync:n.mutate}}},13163:(e,t,s)=>{"use strict";s.d(t,{A:()=>o});var a=s(95155),r=s(60760),i=s(44518),n=s(95233),l=s(54416);s(12115);let o=e=>{let{children:t,className:s,isOpen:o,onClose:c,preventOutsideClick:u=!1}=e;return(0,a.jsx)(r.N,{children:o&&(0,a.jsx)(i.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-neutral-900/50 flex items-center justify-center z-40 p-4 overflow-y-auto",onClick:e=>{u||c()},children:(0,a.jsxs)(i.P.div,{initial:{scale:.6,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.6,opacity:0},transition:{duration:.3,ease:n.am},className:"relative section flex flex-col max-w-4xl max-h-[90vh] overflow-y-auto ".concat(s),onClick:e=>e.stopPropagation(),children:[(0,a.jsx)(l.A,{onClick:c,className:"absolute self-end cursor-pointer text-neutral-700 hover:text-neutral-900 active:scale-90 transition-all duration-300"}),t]})})})}},25784:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let a=s(23464).A.create({baseURL:"http://localhost:4000/api",headers:{"Content-Type":"application/json"},withCredentials:!0});a.interceptors.request.use(e=>e,e=>Promise.reject(e)),a.interceptors.response.use(e=>e,e=>("ERR_NETWORK"===e.code&&console.error("Network error - Please check if the backend server is running"),Promise.reject(e)));let r=a},26437:(e,t,s)=>{Promise.resolve().then(s.bind(s,86550))},28883:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},29350:(e,t,s)=>{"use strict";s.d(t,{A:()=>c});var a=s(97381),r=s(59362),i=s(25784),n=s(35695),l=s(12115),o=s(34540);let c=e=>{let t=(0,o.wA)(),s=(0,n.useRouter)(),c=(0,n.usePathname)(),{status:u,user:d,error:h}=(0,o.d4)(e=>e.auth),m=async()=>{try{t((0,a.Le)());let e=(await i.A.get("/users/me")).data;t((0,a.tQ)(e))}catch(i){if(t((0,a.x9)()),(0,r.F0)(i)){var e,n,l,o,u;if(console.error("Auth error:",null==(e=i.response)?void 0:e.status,null==(n=i.response)?void 0:n.data),(null==(l=i.response)?void 0:l.status)===401){if(c.startsWith("/form-submission"))return;s.push("/")}else t((0,a.jB)((null==(u=i.response)||null==(o=u.data)?void 0:o.message)||i.message))}else t((0,a.jB)(i instanceof Error?i.message:"An unknown error occurred."))}};return(0,l.useEffect)(()=>{(null==e?void 0:e.skipFetchUser)||m()},[null==e?void 0:e.skipFetchUser]),(0,l.useEffect)(()=>{let e=e=>{if("logout"===e.key&&"true"===e.newValue)if(t((0,a.x9)()),c.startsWith("/form-submission")){let e=c.split("/")[2];e?s.push("/form-submission/".concat(e,"/sign-in")):s.push("/")}else s.push("/")};return window.addEventListener("storage",e),()=>{window.removeEventListener("storage",e)}},[t,s,c]),{status:u,user:d,error:h,isAuthenticated:"authenticated"===u,isLoading:"loading"===u,refreshAuthState:()=>{m()},signin:async(e,t,s)=>{try{await i.A.post("/users/login",e),await m(),null==t||t()}catch(e){if(e instanceof r.pe){var a,n;let t=null==(n=e.response)||null==(a=n.data)?void 0:a.errorType;null==s||s(t)}else null==s||s()}},logout:async()=>{try{await i.A.post("/users/logout"),localStorage.setItem("logout","true"),setTimeout(()=>localStorage.removeItem("logout"),100)}finally{if(t((0,a.x9)()),c.startsWith("/form-submission")){let e=c.split("/")[2];e?s.push("/form-submission/".concat(e,"/sign-in")):s.push("/")}else s.push("/")}}}}},34560:(e,t,s)=>{"use strict";s.d(t,{$:()=>l,s:()=>n});var a=s(7165),r=s(57948),i=s(6784),n=class extends r.k{#n;#l;#o;constructor(e){super(),this.mutationId=e.mutationId,this.#l=e.mutationCache,this.#n=[],this.state=e.state||l(),this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options=e,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){this.#n.includes(e)||(this.#n.push(e),this.clearGcTimeout(),this.#l.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.#n=this.#n.filter(t=>t!==e),this.scheduleGc(),this.#l.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.#n.length||("pending"===this.state.status?this.scheduleGc():this.#l.remove(this))}continue(){return this.#o?.continue()??this.execute(this.state.variables)}async execute(e){let t=()=>{this.#c({type:"continue"})};this.#o=(0,i.II)({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(Error("No mutationFn found")),onFail:(e,t)=>{this.#c({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#c({type:"pause"})},onContinue:t,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#l.canRun(this)});let s="pending"===this.state.status,a=!this.#o.canStart();try{if(s)t();else{this.#c({type:"pending",variables:e,isPaused:a}),await this.#l.config.onMutate?.(e,this);let t=await this.options.onMutate?.(e);t!==this.state.context&&this.#c({type:"pending",context:t,variables:e,isPaused:a})}let r=await this.#o.start();return await this.#l.config.onSuccess?.(r,e,this.state.context,this),await this.options.onSuccess?.(r,e,this.state.context),await this.#l.config.onSettled?.(r,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(r,null,e,this.state.context),this.#c({type:"success",data:r}),r}catch(t){try{throw await this.#l.config.onError?.(t,e,this.state.context,this),await this.options.onError?.(t,e,this.state.context),await this.#l.config.onSettled?.(void 0,t,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,t,e,this.state.context),t}finally{this.#c({type:"error",error:t})}}finally{this.#l.runNext(this)}}#c(e){this.state=(t=>{switch(e.type){case"failed":return{...t,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...t,isPaused:!0};case"continue":return{...t,isPaused:!1};case"pending":return{...t,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:e.isPaused,status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...t,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...t,data:void 0,error:e.error,failureCount:t.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}})(this.state),a.jG.batch(()=>{this.#n.forEach(t=>{t.onMutationUpdate(e)}),this.#l.notify({mutation:this,type:"updated",action:e})})}};function l(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}},39953:(e,t,s)=>{"use strict";s.d(t,{BZ:()=>i,eg:()=>n,ep:()=>l,kH:()=>o,l2:()=>r});var a=s(25784);let r=async()=>{let{data:e}=await a.A.get("/users/profile");return e.profile},i=async e=>{let{email:t}=e,{data:s}=await a.A.patch("/users/change-email",{email:t});return s},n=async e=>{let{dataToSend:t}=e,{data:s}=await a.A.patch("/users/update",t);return s},l=async()=>{let{data:e}=await a.A.get("/users/sessions");return e.sessions},o=async e=>{let{data:t}=await a.A.post("/users/sendverificationemail",{email:e});return t}},53999:(e,t,s)=>{"use strict";s.d(t,{Y:()=>n,cn:()=>i});var a=s(52596),r=s(39688);function i(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.QP)((0,a.$)(t))}function n(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"short";if(!e)return"";try{let s="string"==typeof e?new Date(e):e;if(isNaN(s.getTime()))return"";switch(t){case"short":return s.toLocaleDateString(void 0,{year:"numeric",month:"short",day:"numeric"});case"long":return s.toLocaleDateString(void 0,{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"});case"full":return s.toLocaleDateString(void 0,{year:"numeric",month:"long",day:"numeric",weekday:"long",hour:"2-digit",minute:"2-digit",second:"2-digit"});default:return s.toLocaleDateString()}}catch(t){return console.error("Error formatting date:",t),String(e)}}},54416:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},57799:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var a=s(95155);s(12115);let r=()=>(0,a.jsx)("div",{className:"w-full flex items-center justify-center",children:(0,a.jsx)("div",{className:"size-8 rounded-full border-x-2 border-primary-500 animate-spin my-16"})})},59362:(e,t,s)=>{"use strict";s.d(t,{F0:()=>d,pe:()=>r});let{Axios:a,AxiosError:r,CanceledError:i,isCancel:n,CancelToken:l,VERSION:o,all:c,Cancel:u,isAxiosError:d,spread:h,toFormData:m,AxiosHeaders:p,HttpStatusCode:x,formToJSON:f,getAdapter:y,mergeConfig:g}=s(23464).A},63642:(e,t,s)=>{"use strict";s.d(t,{R:()=>i});var a=s(95155);s(12115);var r=s(13163);let i=e=>{let{showModal:t,onClose:s,onConfirm:i,title:n,description:l,confirmButtonText:o,cancelButtonText:c,confirmButtonClass:u,children:d}=e;return(0,a.jsxs)(r.A,{isOpen:t,onClose:s,className:"p-6 rounded-md max-w-xl",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-neutral-700",children:n}),(0,a.jsx)("div",{className:"text-neutral-700 mt-2",children:l}),d&&(0,a.jsx)("div",{className:"mt-6 space-y-4",children:d}),(0,a.jsxs)("div",{className:"flex justify-end gap-4 mt-6",children:[(0,a.jsx)("button",{className:"btn-outline",onClick:s,type:"button",children:c||"Cancel"}),(0,a.jsx)("button",{className:"font-medium rounded-md shadow-md text-neutral-100 flex items-center justify-center gap-2 px-4 py-2 active:scale-95 transition-all duration-300 ".concat(u),onClick:i,type:"button",children:o})]})]})}},71402:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>n,Ds:()=>r,_b:()=>i});let a=(0,s(51990).Z0)({name:"notification",initialState:{message:"",type:"",visible:!1},reducers:{showNotification:(e,t)=>{e.message=t.payload.message,e.type=t.payload.type,e.visible=!0},hideNotification:e=>{e.message="",e.type="",e.visible=!1}}}),{showNotification:r,hideNotification:i}=a.actions,n=a.reducer},78749:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},86550:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>E});var a=s(95155),r=s(12115),i=s(19946);let n=(0,i.A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]),l=(0,i.A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]);var o=s(28883),c=s(71402),u=s(25784),d=s(78749),h=s(92657),m=s(62177),p=s(34540),x=s(17652);let f=()=>{let e=(0,x.c3)(),[t,s]=(0,r.useState)(!1),[i,n]=(0,r.useState)(!1),[l,o]=(0,r.useState)(!1),[f,y]=(0,r.useState)(!1),g=(0,p.wA)(),{register:v,handleSubmit:w,formState:{errors:b},setValue:j,watch:N}=(0,m.mN)({defaultValues:{currentPassword:"",newPassword:"",confirmPassword:"",email:""}}),_=N("newPassword");N("confirmPassword");let C=async t=>{let{currentPassword:a,newPassword:r,confirmPassword:i}=t;if(r!==i)return void g((0,c.Ds)({message:e("password_mismatch"),type:"error"}));try{let t=await u.A.post("/users/changepassword",{currentPassword:a,newPassword:r,confirmPassword:i});200===t.status&&(g((0,c.Ds)({message:e("password_changed_successfully"),type:"success"})),s(!1),j("currentPassword",""),j("newPassword",""),j("confirmPassword",""))}catch(e){var n,l;g((0,c.Ds)({message:(null==(l=e.response)||null==(n=l.data)?void 0:n.message)||"Server error",type:"error"}))}};return(0,a.jsx)("div",{children:t?(0,a.jsxs)("form",{onSubmit:w(C),className:"flex-col flex gap-4",children:[(0,a.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,a.jsxs)("div",{className:"label-input-group group",children:[(0,a.jsx)("label",{htmlFor:"current-password",className:"label-text",children:e("current_password")}),(0,a.jsxs)("div",{className:"relative laptop:w-1/3",children:[(0,a.jsx)("input",{id:"current-password",type:i?"text":"password",placeholder:e("enter_current_password"),className:"input-field w-full pr-10",...v("currentPassword",{required:e("current_password_required")})}),(0,a.jsxs)("button",{type:"button",tabIndex:-1,className:"absolute right-2 top-1/2 -translate-y-1/2 text-gray-500",onClick:()=>n(!i),children:[i?(0,a.jsx)(d.A,{className:"h-4 w-4"}):(0,a.jsx)(h.A,{className:"h-4 w-4"}),(0,a.jsxs)("span",{className:"sr-only",children:[i?"Hide":"Show"," password"]})]})]}),b.currentPassword&&(0,a.jsx)("p",{className:"text-red-500 text-sm",children:b.currentPassword.message})]}),(0,a.jsxs)("div",{className:"label-input-group group",children:[(0,a.jsx)("label",{htmlFor:"new-password",className:"label-text",children:e("new_password")}),(0,a.jsxs)("div",{className:"relative laptop:w-1/3",children:[(0,a.jsx)("input",{id:"new-password",type:l?"text":"password",placeholder:e("enter_new_password"),className:"input-field w-full pr-10",...v("newPassword",{required:e("new_password_required"),minLength:{value:6,message:e("password_min_length")},validate:t=>t!==N("currentPassword")||e("new_password_must_be_different")})}),(0,a.jsxs)("button",{type:"button",tabIndex:-1,className:"absolute right-2 top-1/2 -translate-y-1/2 text-gray-500",onClick:()=>o(!l),children:[l?(0,a.jsx)(d.A,{className:"h-4 w-4"}):(0,a.jsx)(h.A,{className:"h-4 w-4"}),(0,a.jsxs)("span",{className:"sr-only",children:[l?"Hide":"Show"," password"]})]})]}),b.newPassword&&(0,a.jsx)("p",{className:"text-red-500 text-sm",children:b.newPassword.message})]}),(0,a.jsxs)("div",{className:"label-input-group group",children:[(0,a.jsx)("label",{htmlFor:"confirm-password",className:"label-text",children:e("confirm_password")}),(0,a.jsxs)("div",{className:"relative laptop:w-1/3",children:[(0,a.jsx)("input",{id:"confirm-password",type:f?"text":"password",placeholder:e("enter_confirm_password"),className:"input-field w-full pr-10",...v("confirmPassword",{required:e("confirm_password_required"),validate:t=>t===_||e("passwords_do_not_match")})}),(0,a.jsxs)("button",{type:"button",tabIndex:-1,className:"absolute right-2 top-1/2 -translate-y-1/2 text-gray-500",onClick:()=>y(!f),children:[f?(0,a.jsx)(d.A,{className:"h-4 w-4"}):(0,a.jsx)(h.A,{className:"h-4 w-4"}),(0,a.jsxs)("span",{className:"sr-only",children:[f?"Hide":"Show"," password"]})]})]}),b.confirmPassword&&(0,a.jsx)("p",{className:"text-red-500 text-sm",children:b.confirmPassword.message})]})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)("button",{type:"submit",className:"btn-primary",children:e("update_password")}),(0,a.jsx)("button",{className:"btn-outline",onClick:()=>s(!1),children:e("cancel")})]})]}):(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsx)("button",{className:"btn-primary",onClick:()=>s(!0),children:e("change_password")})})})};var y=s(36268),g=s(11032),v=s(88524);function w(e){var t;let{columns:s,data:i}=e,[n,l]=r.useState({pageIndex:0,pageSize:8}),o=(0,y.N4)({data:i,columns:s,onPaginationChange:l,getPaginationRowModel:(0,g.kW)(),getCoreRowModel:(0,g.HT)(),state:{pagination:n}}),c=(0,x.c3)();return(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"rounded-md border border-neutral-400 overflow-hidden",children:(0,a.jsxs)(v.XI,{children:[(0,a.jsx)(v.A0,{children:o.getHeaderGroups().map(e=>(0,a.jsx)(v.Hj,{className:"text-sm border-neutral-400",children:e.headers.map(e=>(0,a.jsx)(v.nd,{className:"py-1 px-6 text-left bg-primary-500 text-neutral-100 font-semibold",children:e.isPlaceholder?null:(0,y.Kv)(e.column.columnDef.header,e.getContext())},e.id))},e.id))}),(0,a.jsx)(v.BF,{children:(null==(t=o.getRowModel().rows)?void 0:t.length)?o.getRowModel().rows.map(e=>(0,a.jsx)(v.Hj,{className:" text-sm border-neutral-400","data-state":e.getIsSelected()&&"selected",children:e.getVisibleCells().map(e=>(0,a.jsx)(v.nA,{className:"py-4 px-6",children:(0,y.Kv)(e.column.columnDef.cell,e.getContext())},e.id))},e.id)):(0,a.jsx)(v.Hj,{children:(0,a.jsx)(v.nA,{colSpan:s.length,className:"h-24 text-center",children:c("no_results")})})})]})}),i.length>n.pageSize&&(0,a.jsxs)("div",{className:"flex items-center justify-end space-x-2 py-4",children:[(0,a.jsx)("button",{className:"btn-primary",onClick:()=>o.previousPage(),disabled:!o.getCanPreviousPage(),children:c("previous")}),(0,a.jsx)("button",{className:"btn-primary",onClick:()=>o.nextPage(),disabled:!o.getCanNextPage(),children:c("next")})]})]})}var b=s(53999);let j=(e,t,s)=>{if(null==e)return"-";if("boolean"==typeof e)return e?s?s("yes"):"Yes":s?s("no"):"No";if(e instanceof Date)return(0,b.Y)(e);if("date"===t&&"string"==typeof e)try{return(0,b.Y)(new Date(e))}catch(t){return e}return String(e)},N=()=>{let e=(0,x.c3)();return[{accessorKey:"deviceInfo",header:e("device")},{accessorKey:"browserInfo",header:e("browser")},{accessorKey:"updatedAt",header:e("last_activity"),cell:t=>{let{getValue:s}=t,r=s();return(0,a.jsx)("div",{className:"font-medium text-neutral-700",children:j(r,"date",e)||e("notRecorded")})}},{accessorKey:"ipAddress",header:e("ip_address")},{accessorKey:"isActive",header:e("status"),cell:t=>{let{getValue:s}=t;return s()?e("active"):e("inactive")}}]};var _=s(29350),C=s(19373),A=s(26715),k=s(5041),S=s(39953),P=s(57799),M=s(63642),R=s(59362);let E=()=>{var e;let[t,s]=(0,r.useState)(!1),{user:i,logout:u}=(0,_.A)(),d=(0,x.c3)(),h=N(),{register:y,formState:{errors:g},handleSubmit:v,getValues:b,reset:j,setError:E}=(0,m.mN)(),{data:O,isLoading:D,isError:F}=(0,C.I)({queryKey:["sessions",null==i?void 0:i.id],queryFn:S.ep,enabled:!!(null==i?void 0:i.id)}),I=(0,p.wA)(),L=(0,A.jE)(),[K,q]=(0,r.useState)(!1),H=(0,k.n)({mutationFn:S.BZ,onSuccess:async()=>{try{await L.invalidateQueries({queryKey:["profile",null==i?void 0:i.id]}),await (0,S.kH)(b("email")),s(!1),I((0,c.Ds)({message:d("email_change_success"),type:"success"})),u()}catch(e){I((0,c.Ds)({message:d("email_change_verificationFailed"),type:"warning"}))}},onError:e=>{if(e instanceof R.pe){var t,s;E(null==(t=e.response)?void 0:t.data.errorField,{message:null==(s=e.response)?void 0:s.data.message})}else I((0,c.Ds)({message:d("email_change_failed"),type:"error"}))}});(0,r.useEffect)(()=>{t||j()},[t,j]);let[G,T]=(0,r.useState)(!1);(0,r.useEffect)(()=>{T(!0)},[]);let B=async e=>{H.mutate({email:e.email})};return G?D?(0,a.jsx)(P.A,{}):F||!O?(0,a.jsxs)("p",{className:"text-sm text-red-500",children:[" ",d("error_loading_data")]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(M.R,{showModal:K,onClose:()=>q(!1),onConfirm:()=>{v(B)(),q(!1)},title:d("email_change_confirm"),description:d("email_change_warning"),confirmButtonText:d("change"),confirmButtonClass:"btn-primary"}),(0,a.jsxs)("div",{className:"flex flex-col gap-10",children:[(0,a.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(n,{className:"h-8 w-8"}),(0,a.jsx)("h2",{className:"heading-text",children:d("security_settings")})]}),(0,a.jsx)("p",{className:"sub-text",children:d("account_security_settings")})]}),(0,a.jsxs)("div",{className:"flex-col gap-10 flex",children:[(0,a.jsxs)("div",{className:"flex flex-col gap-5 shadow-sm border-muted p-4",children:[(0,a.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(l,{className:"h-5 w-5"}),(0,a.jsx)("h2",{className:"sub-heading-text",children:d("password")})]}),(0,a.jsx)("p",{className:"sub-text",children:d("account_update_password")})]}),(0,a.jsx)("div",{children:(0,a.jsx)(f,{})})]}),(0,a.jsxs)("div",{className:"flex flex-col gap-5 shadow-sm border-muted p-4",children:[(0,a.jsxs)("div",{className:"flex flex-col gap-2 ",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(o.A,{className:"h-5 w-5"}),(0,a.jsx)("h2",{className:"sub-heading-text",children:d("email_address")})]}),(0,a.jsx)("p",{className:"sub-text",children:d("account_email_usage")})]}),(0,a.jsx)("div",{children:t?(0,a.jsxs)("form",{className:"space-y-4",noValidate:!0,onSubmit:e=>e.preventDefault(),children:[(0,a.jsx)("input",{...y("email",{required:d("enter_new_email")}),type:"email",placeholder:"eg: <EMAIL>",className:"input-field"}),g.email&&(0,a.jsx)("p",{className:"text-sm text-red-500",children:"".concat(null==(e=g.email)?void 0:e.message)}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)("button",{type:"button",className:"btn-primary",onClick:()=>q(!0),children:d("save")}),(0,a.jsx)("button",{type:"button",className:"btn-outline",onClick:()=>s(!1),children:d("cancel")})]})]}):(0,a.jsxs)("div",{className:"flex justify-between items-center ",children:[(0,a.jsx)("span",{children:null==i?void 0:i.email}),(0,a.jsx)("button",{type:"button",className:"btn-primary",onClick:()=>s(!0),children:d("change")})]})})]})]}),(0,a.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,a.jsxs)("h2",{className:"heading-text",children:[d("recent_account_activity")," "]}),(0,a.jsx)(w,{columns:h,data:O})]})]})]}):null}},88524:(e,t,s)=>{"use strict";s.d(t,{A0:()=>n,BF:()=>l,Hj:()=>o,XI:()=>i,nA:()=>u,nd:()=>c});var a=s(95155);s(12115);var r=s(53999);function i(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,a.jsx)("table",{"data-slot":"table",className:(0,r.cn)("w-full caption-bottom text-sm",t),...s})})}function n(e){let{className:t,...s}=e;return(0,a.jsx)("thead",{"data-slot":"table-header",className:(0,r.cn)("[&_tr]:border-b",t),...s})}function l(e){let{className:t,...s}=e;return(0,a.jsx)("tbody",{"data-slot":"table-body",className:(0,r.cn)("[&_tr:last-child]:border-0",t),...s})}function o(e){let{className:t,...s}=e;return(0,a.jsx)("tr",{"data-slot":"table-row",className:(0,r.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",t),...s})}function c(e){let{className:t,...s}=e;return(0,a.jsx)("th",{"data-slot":"table-head",className:(0,r.cn)("text-foreground h-10 px-2 text-left align-middle font-medium neutral-100space-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...s})}function u(e){let{className:t,...s}=e;return(0,a.jsx)("td",{"data-slot":"table-cell",className:(0,r.cn)("p-2 align-middle neutral-100space-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...s})}},92657:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},97381:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>o,Le:()=>n,jB:()=>l,tQ:()=>r,x9:()=>i});let a=(0,s(51990).Z0)({name:"auth",initialState:{status:"loading",user:null,error:null},reducers:{setAuthenticatedUser:(e,t)=>{e.status="authenticated",e.user=t.payload,e.error=null},setUnauthenticated:e=>{e.status="unauthenticated",e.user=null,e.error=null},setAuthLoading:e=>{e.status="loading"},setAuthError:(e,t)=>{e.status="unauthenticated",e.error=t.payload,e.user=null}}}),{setAuthenticatedUser:r,setUnauthenticated:i,setAuthLoading:n,setAuthError:l}=a.actions,o=a.reducer}},e=>{var t=t=>e(e.s=t);e.O(0,[6453,635,1111,6967,9373,4601,1380,4277,6268,8441,1684,7358],()=>t(26437)),_N_E=e.O()}]);