import express, { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { authenticate } from "../middleware/auth";
import {
  createQuestion,
  updateQuestion,
  getAllQuestion,
  deleteQuestion,
  duplicateQuestion,
  updateQuestionPositions,
  updateUnifiedPositions,
} from "../controllers/questionController";
import { checkPermission } from "../middleware/checkPermission";
import { uploads } from "../utils/multer";

const router = express.Router();
router.patch(
  "/positions",
  authenticate,
  checkPermission([
    "manageProject",
    "editForm",
  ]) as unknown as express.RequestHandler,
  updateQuestionPositions as unknown as <PERSON><PERSON><PERSON><PERSON><PERSON>
);

// NEW UNIFIED POSITION SYSTEM: Route for updating positions using ProjectQuestionOrder schema
router.patch(
  "/unified-positions",
  authenticate,
  checkPermission([
    "manageProject",
    "editForm",
  ]) as unknown as express.RequestHandler,
  updateUnifiedPositions as unknown as <PERSON><PERSON><PERSON><PERSON><PERSON>
);

// Create a question for a project
router.post(
  "/:projectId",
  authenticate,
  uploads.single("file"), // Change from "excelFile" to "file" to match frontend
  checkPermission([
    "manageProject",
    "editForm",
  ]) as unknown as express.RequestHandler,
  createQuestion
);

// Update a specific question
router.patch(
  "/:id",
  authenticate,
  checkPermission([
    "manageProject",
    "editForm",
  ]) as unknown as express.RequestHandler,
  updateQuestion
);

// Get all questions for a project (using path parameter)
router.get(
  "/:projectId",
  authenticate,
  getAllQuestion as unknown as express.RequestHandler
);
router.delete(
  "/:id",
  authenticate,
  deleteQuestion as unknown as express.RequestHandler
);

router.post(
  "/duplicate/:id",
  authenticate,
  checkPermission([
    "manageProject",
    "editForm",
  ]) as unknown as express.RequestHandler,

  duplicateQuestion as unknown as RequestHandler
);

export default router;
