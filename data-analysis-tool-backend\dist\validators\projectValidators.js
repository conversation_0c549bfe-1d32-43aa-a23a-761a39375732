"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteMultipleProjectSchema = exports.updateProjectStatusesSchema = exports.ChangeStatusSchema = exports.ProjectSchema = void 0;
const zod_1 = require("zod");
const client_1 = require("@prisma/client");
exports.ProjectSchema = zod_1.z.object({
    name: zod_1.z.string().min(1, "Name is required"),
    description: zod_1.z.string().min(1, "Name is required"),
    sector: zod_1.z.nativeEnum(client_1.Sector),
    country: zod_1.z.string().optional(),
});
exports.ChangeStatusSchema = zod_1.z.object({
    status: zod_1.z.nativeEnum(client_1.Status),
});
exports.updateProjectStatusesSchema = zod_1.z.object({
    projectIds: zod_1.z.array(zod_1.z.number().int().positive()).nonempty({
        message: "At least one project ID is required.",
    }),
    status: zod_1.z.nativeEnum(client_1.Status),
});
exports.deleteMultipleProjectSchema = zod_1.z.object({
    projectIds: zod_1.z.array(zod_1.z.number().int().positive()).nonempty({
        message: "At least one project ID is required.",
    }),
});
