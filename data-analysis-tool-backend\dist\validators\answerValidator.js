"use strict";
// src/validators/answerValidator.js
Object.defineProperty(exports, "__esModule", { value: true });
exports.answerArraySchema = exports.updateMultipleAnswerSchema = exports.updateAnswerSchema = exports.createMultipleAnswerSchema = exports.createAnswerSchema = void 0;
const client_1 = require("@prisma/client");
const zod_1 = require("zod");
// Zod schema for validating the Answer model
exports.createAnswerSchema = zod_1.z
    .object({
    submissionId: zod_1.z.number(),
    questionId: zod_1.z.number(),
    value: zod_1.z.any(),
    imageUrl: zod_1.z.string().optional(),
    isOtherOption: zod_1.z.boolean().optional(),
    answerType: zod_1.z.nativeEnum(client_1.InputType),
    questionOptionId: zod_1.z.any(), // will be refined below
})
    .superRefine((data, ctx) => {
    const { answerType, questionOptionId } = data;
    if (answerType === "selectmany") {
        if (!Array.isArray(questionOptionId)) {
            ctx.addIssue({
                code: zod_1.z.ZodIssueCode.custom,
                path: ["questionOptionId"],
                message: "Expected array of questionOptionIds for selectmany",
            });
        }
    }
    else {
        if (Array.isArray(questionOptionId)) {
            ctx.addIssue({
                code: zod_1.z.ZodIssueCode.custom,
                path: ["questionOptionId"],
                message: "Expected a single questionOptionId",
            });
        }
        else if (questionOptionId !== undefined &&
            typeof questionOptionId !== "number") {
            ctx.addIssue({
                code: zod_1.z.ZodIssueCode.custom,
                path: ["questionOptionId"],
                message: "questionOptionId must be a number",
            });
        }
    }
});
exports.createMultipleAnswerSchema = zod_1.z
    .object({
    projectId: zod_1.z.number(),
    status: zod_1.z.string().optional(),
    deviceInfo: zod_1.z.string().optional(),
    loginRequired: zod_1.z.boolean().optional(),
    location: zod_1.z.string().optional(),
    metadata: zod_1.z.record(zod_1.z.any()).optional(),
    startedAt: zod_1.z.coerce.date().optional(),
    questionId: zod_1.z.number(),
    value: zod_1.z.any(),
    imageUrl: zod_1.z.string().optional(),
    isOtherOption: zod_1.z.boolean().optional(),
    answerType: zod_1.z.nativeEnum(client_1.InputType),
    questionOptionId: zod_1.z.any(), // will be refined below
})
    .superRefine((data, ctx) => {
    const { answerType, questionOptionId } = data;
    if (answerType === "selectmany") {
        if (!Array.isArray(questionOptionId)) {
            ctx.addIssue({
                code: zod_1.z.ZodIssueCode.custom,
                path: ["questionOptionId"],
                message: "Expected array of questionOptionIds for selectmany",
            });
        }
    }
    else {
        if (Array.isArray(questionOptionId)) {
            ctx.addIssue({
                code: zod_1.z.ZodIssueCode.custom,
                path: ["questionOptionId"],
                message: "Expected a single questionOptionId",
            });
        }
        else if (questionOptionId !== undefined &&
            typeof questionOptionId !== "number") {
            ctx.addIssue({
                code: zod_1.z.ZodIssueCode.custom,
                path: ["questionOptionId"],
                message: "questionOptionId must be a number",
            });
        }
    }
});
exports.updateAnswerSchema = zod_1.z
    .object({
    id: zod_1.z.number().optional(), // Required for identifying the answer to update
    value: zod_1.z.any(),
    imageUrl: zod_1.z.string().optional(),
    isOtherOption: zod_1.z.boolean().optional(),
    answerType: zod_1.z.nativeEnum(client_1.InputType),
    questionOptionId: zod_1.z.any().optional(),
    formSubmissionId: zod_1.z.number(),
    projectId: zod_1.z.number(),
    questionId: zod_1.z.number(),
})
    .superRefine((data, ctx) => {
    const { answerType, questionOptionId } = data;
    if (answerType === "selectmany") {
        if (questionOptionId !== undefined && !Array.isArray(questionOptionId)) {
            ctx.addIssue({
                code: zod_1.z.ZodIssueCode.custom,
                path: ["questionOptionId"],
                message: "Expected array of questionOptionIds for selectmany",
            });
        }
    }
    else {
        if (Array.isArray(questionOptionId) ||
            (questionOptionId !== undefined && typeof questionOptionId !== "number")) {
            ctx.addIssue({
                code: zod_1.z.ZodIssueCode.custom,
                path: ["questionOptionId"],
                message: "Expected a single number for questionOptionId",
            });
        }
    }
});
exports.updateMultipleAnswerSchema = zod_1.z.array(exports.updateAnswerSchema);
exports.answerArraySchema = zod_1.z.array(exports.createMultipleAnswerSchema);
