"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteLibraryQuestion = exports.duplicateTemplateQuestion = exports.updateLibraryQuestion = exports.getLibraryQuestionById = exports.getAllLibraryQuestions = exports.createLibraryQuestion = void 0;
const ApiResponse_1 = require("../utils/ApiResponse");
const libraryQuestionValidators_1 = require("../validators/libraryQuestionValidators");
const libraryQuestionRepository_1 = __importDefault(require("../repositories/libraryQuestionRepository"));
const libraryTemplateRepository_1 = __importDefault(require("../repositories/libraryTemplateRepository"));
const createLibraryQuestion = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const libraryTemplateId = Number(req.params.libraryTemplateId);
        // Check if user owns the library template
        const isOwner = yield libraryQuestionRepository_1.default.isLibraryTemplateOwner(userId, libraryTemplateId);
        if (!isOwner) {
            res.status(403).json({
                success: false,
                message: "User is not associated with this library template",
            });
            return;
        }
        // Validate input data
        const result = libraryQuestionValidators_1.libraryQuestionSchema.safeParse(req.body);
        if (!result.success) {
            res.status(400).json({
                success: false,
                errors: result.error.flatten().fieldErrors,
            });
            return;
        }
        // Create the question - transforming options and conditions to match repository format
        const questionData = result.data;
        const question = yield libraryQuestionRepository_1.default.create(Object.assign(Object.assign({}, questionData), { libraryTemplateId, questionOptions: questionData.questionOptions
                ? questionData.questionOptions.map((opt) => ({
                    label: opt.label,
                    code: opt.code,
                    nextLibraryQuestionId: opt.nextLibraryQuestionId,
                }))
                : undefined, conditions: questionData.conditions
                ? questionData.conditions.map((cond) => ({
                    operator: cond.operator,
                    value: cond.value,
                }))
                : undefined }));
        res
            .status(201)
            .json(new ApiResponse_1.ApiResponse(201, { question }, "Library question created successfully"));
        return;
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: "Error creating library question",
            error: error instanceof Error ? error.message : "Unexpected error",
        });
        return;
    }
});
exports.createLibraryQuestion = createLibraryQuestion;
const getAllLibraryQuestions = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const libraryTemplateId = Number(req.params.libraryTemplateId);
        // Check if user owns the library template
        const isOwner = yield libraryQuestionRepository_1.default.isLibraryTemplateOwner(userId, libraryTemplateId);
        if (!isOwner) {
            res.status(403).json({
                success: false,
                message: "User is not associated with this library template",
            });
            return;
        }
        // Get all questions for the template
        const questions = yield libraryQuestionRepository_1.default.findByLibraryTemplateId(libraryTemplateId);
        res.status(200).json({
            success: true,
            message: "Successfully fetched template questions",
            questions,
        });
        return;
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: "Error retrieving library questions",
            error: error instanceof Error ? error.message : "Unexpected error",
        });
        return;
    }
});
exports.getAllLibraryQuestions = getAllLibraryQuestions;
const getLibraryQuestionById = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const questionId = Number(req.params.id);
        // Get the question
        const question = yield libraryQuestionRepository_1.default.findById(questionId);
        if (!question) {
            res.status(404).json({
                success: false,
                message: "Library question not found",
            });
            return;
        }
        // Check if user owns the library template
        const isOwner = yield libraryQuestionRepository_1.default.isLibraryTemplateOwner(userId, question.libraryTemplateId);
        if (!isOwner) {
            res.status(403).json({
                success: false,
                message: "User is not associated with this library template",
            });
            return;
        }
        res
            .status(200)
            .json(new ApiResponse_1.ApiResponse(200, { question }, "Library question retrieved successfully"));
        return;
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: "Error retrieving library question",
            error: error instanceof Error ? error.message : "Unexpected error",
        });
        return;
    }
});
exports.getLibraryQuestionById = getLibraryQuestionById;
const updateLibraryQuestion = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const questionId = Number(req.params.id);
        // Get the question to check ownership
        const existingQuestion = yield libraryQuestionRepository_1.default.findById(questionId);
        if (!existingQuestion) {
            res.status(404).json({
                success: false,
                message: "Library question not found",
            });
            return;
        }
        // Check if user owns the library template
        const isOwner = yield libraryQuestionRepository_1.default.isLibraryTemplateOwner(userId, existingQuestion.libraryTemplateId);
        if (!isOwner) {
            res.status(403).json({
                success: false,
                message: "User is not associated with this library template",
            });
            return;
        }
        // Create a schema for update that omits libraryTemplateId and makes all fields optional
        const updateSchema = libraryQuestionValidators_1.libraryQuestionSchema.partial();
        // Validate input data
        const result = updateSchema.safeParse(req.body);
        if (!result.success) {
            res.status(400).json({
                success: false,
                errors: result.error.flatten().fieldErrors,
            });
            return;
        }
        // Transform options and conditions to match repository format
        const questionData = result.data;
        // Update the question
        const updatedQuestion = yield libraryQuestionRepository_1.default.update(questionId, Object.assign(Object.assign({}, questionData), { questionOptions: questionData.questionOptions
                ? questionData.questionOptions.map((opt) => ({
                    id: opt.id,
                    label: opt.label,
                    code: opt.code,
                    nextLibraryQuestionId: opt.nextLibraryQuestionId,
                }))
                : undefined, conditions: questionData.conditions
                ? questionData.conditions.map((cond) => ({
                    id: cond.id,
                    operator: cond.operator,
                    value: cond.value,
                }))
                : undefined }));
        res
            .status(200)
            .json(new ApiResponse_1.ApiResponse(200, { question: updatedQuestion }, "Library question updated successfully"));
        return;
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: "Error updating library question",
            error: error instanceof Error ? error.message : "Unexpected error",
        });
        return;
    }
});
exports.updateLibraryQuestion = updateLibraryQuestion;
const duplicateTemplateQuestion = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
        if (!userId) {
            return res
                .status(403)
                .json({ success: false, message: "Unauthorized user" });
        }
        const id = Number(req.params.id);
        if (!id) {
            return res.status(400).json({
                success: false,
                message: "Invalid request: Question ID is missing",
            });
        }
        const currentQuestion = yield libraryQuestionRepository_1.default.findById(id);
        if (!currentQuestion) {
            return res.status(404).json({
                success: false,
                message: "Question not found with the provided question id",
            });
        }
        const isTemplateOwner = yield libraryTemplateRepository_1.default.isOwner(userId, currentQuestion.libraryTemplateId);
        if (!isTemplateOwner) {
            return res.status(403).json({
                success: false,
                message: "Unauthorized user",
            });
        }
        const duplicatedQuestion = yield libraryQuestionRepository_1.default.duplicateQuestion(id, currentQuestion.libraryTemplateId);
        return res.status(200).json({
            message: "Successfully duplicated the question",
            success: true,
            duplicatedQuestion,
        });
    }
    catch (error) {
        return res.status(500).json({
            message: error instanceof Error
                ? `Failed to duplicate question: ${error.message}`
                : "An unexpected error occured while trying to duplicate question",
        });
    }
});
exports.duplicateTemplateQuestion = duplicateTemplateQuestion;
const deleteLibraryQuestion = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const questionId = Number(req.params.id);
        // Get the question to check ownership
        const existingQuestion = yield libraryQuestionRepository_1.default.findById(questionId);
        if (!existingQuestion) {
            res.status(404).json({
                success: false,
                message: "Library question not found",
            });
            return;
        }
        // Check if user owns the library template
        const isOwner = yield libraryQuestionRepository_1.default.isLibraryTemplateOwner(userId, existingQuestion.libraryTemplateId);
        if (!isOwner) {
            res.status(403).json({
                success: false,
                message: "User is not associated with this library template",
            });
            return;
        }
        // Delete the question
        yield libraryQuestionRepository_1.default.delete(questionId);
        res
            .status(200)
            .json(new ApiResponse_1.ApiResponse(200, {}, "Library question deleted successfully"));
        return;
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: "Error deleting library question",
            error: error instanceof Error ? error.message : "Unexpected error",
        });
        return;
    }
});
exports.deleteLibraryQuestion = deleteLibraryQuestion;
