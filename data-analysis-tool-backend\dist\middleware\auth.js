"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.authenticate = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const authenticate = (req, res, next) => {
    var _a;
    const token = (_a = req.cookies) === null || _a === void 0 ? void 0 : _a.token;
    if (!token) {
        res
            .status(401)
            .json({ message: "Unauthorized: No token found in cookies" });
        return;
    }
    try {
        const decoded = jsonwebtoken_1.default.verify(token, process.env.JWT_SECRET);
        const userId = typeof decoded.id === "string" ? parseInt(decoded.id) : decoded.id;
        if (!userId || isNaN(userId)) {
            res.status(401).json({
                success: false,
                message: "Unauthorized: invalid token payload / user not found",
            });
            return;
        }
        req.user = { id: userId, sessionId: decoded.sessionId };
        next();
    }
    catch (error) {
        res.status(401).json({ message: "invalid token" });
        return;
    }
};
exports.authenticate = authenticate;
