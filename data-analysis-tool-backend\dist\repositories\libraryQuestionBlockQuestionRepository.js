"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
const prisma_1 = require("../utils/prisma");
class LibraryQuestionRepository {
    findById(id) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.libraryQuestionBlockQuestion.findUnique({
                where: { id },
                include: {
                    questionOptions: true,
                    questionConditions: true,
                },
            });
        });
    }
    create(data, userId) {
        return __awaiter(this, void 0, void 0, function* () {
            const { label, inputType, hint, placeholder, isRequired, position, libraryQuestionBlockQuestionGroupId, questionOptions, conditions, } = data;
            return yield prisma_1.prisma.$transaction((tx) => __awaiter(this, void 0, void 0, function* () {
                // Create the question
                const question = yield tx.libraryQuestionBlockQuestion.create({
                    data: {
                        label,
                        inputType,
                        hint: hint !== null && hint !== void 0 ? hint : "", // fallback to empty string
                        placeholder: placeholder !== null && placeholder !== void 0 ? placeholder : "",
                        isRequired,
                        position,
                        userId,
                        libraryQuestionBlockQuestionGroupId,
                    },
                    include: {
                        questionOptions: true,
                        questionConditions: true,
                    },
                });
                // Create options if provided
                if (questionOptions && questionOptions.length > 0) {
                    yield tx.libraryQuestionBlockQuestionOption.createMany({
                        data: questionOptions.map((option) => ({
                            label: option.label,
                            code: option.code,
                            libraryQuestionBlockQuestionId: question.id,
                            // nextQuestionId: option.nextQuestionId || null,
                        })),
                    });
                }
                // Create conditions if provided
                if (conditions && conditions.length > 0) {
                    yield tx.libraryQuestionBlockQuestionCondition.createMany({
                        data: conditions.map((condition) => ({
                            operator: condition.operator,
                            value: condition.value,
                            libraryQuestionBlockQuestionId: question.id,
                        })),
                    });
                }
                // Return the created question with options and conditions
                return question;
            }));
        });
    }
    updateById(id, updateData) {
        return __awaiter(this, void 0, void 0, function* () {
            const { options, conditions } = updateData, rest = __rest(updateData, ["options", "conditions"]);
            const questionData = {
                label: rest.label,
                inputType: rest.inputType,
                hint: rest.hint,
                placeholder: rest.placeholder,
                isRequired: rest.isRequired,
                position: rest.position,
            };
            return yield prisma_1.prisma.$transaction((tx) => __awaiter(this, void 0, void 0, function* () {
                // Update the question basic data
                const updatedQuestion = yield tx.libraryQuestionBlockQuestion.update({
                    where: { id },
                    data: questionData,
                });
                // Handle options if provided
                if (options) {
                    const existingOptions = yield tx.libraryQuestionBlockQuestionOption.findMany({
                        where: { libraryQuestionBlockQuestionId: id },
                    });
                    // Identify options to add/update/delete
                    const existingIds = existingOptions.map((o) => o.id);
                    const updatedIds = options
                        .filter((o) => o.id)
                        .map((o) => o.id);
                    const idsToDelete = existingIds.filter((id) => !updatedIds.includes(id));
                    // Delete removed options
                    if (idsToDelete.length > 0) {
                        yield tx.libraryQuestionBlockQuestionOption.deleteMany({
                            where: { id: { in: idsToDelete } },
                        });
                    }
                    // Add new options and update existing ones
                    for (const option of options) {
                        if (option.id) {
                            // Update existing option
                            yield tx.libraryQuestionBlockQuestionOption.update({
                                where: { id: option.id },
                                data: {
                                    label: option.label,
                                    code: option.code,
                                    nextLibraryQuestionId: option.nextQuestionId || null,
                                },
                            });
                        }
                        else {
                            // Create new option
                            yield tx.libraryQuestionBlockQuestionOption.create({
                                data: {
                                    label: option.label,
                                    code: option.code,
                                    libraryQuestionBlockQuestionId: id,
                                    nextLibraryQuestionId: option.nextQuestionId || null,
                                },
                            });
                        }
                    }
                }
                // Handle conditions if provided
                if (conditions) {
                    const existingConditions = yield tx.libraryQuestionBlockQuestionCondition.findMany({
                        where: { libraryQuestionBlockQuestionId: id },
                    });
                    // Identify conditions to add/update/delete
                    const existingIds = existingConditions.map((c) => c.id);
                    const updatedIds = conditions
                        .filter((c) => c.id)
                        .map((c) => c.id);
                    const idsToDelete = existingIds.filter((id) => !updatedIds.includes(id));
                    // Delete removed conditions
                    if (idsToDelete.length > 0) {
                        yield tx.libraryQuestionBlockQuestionCondition.deleteMany({
                            where: { id: { in: idsToDelete } },
                        });
                    }
                    // Add new conditions and update existing ones
                    for (const condition of conditions) {
                        if (condition.id) {
                            // Check if condition exists and belongs to this question
                            const existingCondition = yield tx.libraryQuestionBlockQuestionCondition.findUnique({
                                where: {
                                    id: condition.id,
                                },
                            });
                            // Only update if condition exists and belongs to this question
                            if (existingCondition &&
                                existingCondition.libraryQuestionBlockQuestionId === id) {
                                // Update existing condition
                                yield tx.libraryQuestionBlockQuestionCondition.update({
                                    where: { id: condition.id },
                                    data: {
                                        operator: condition.operator,
                                        value: condition.value,
                                    },
                                });
                            }
                            else {
                                // Create new condition if ID doesn't exist
                                yield tx.libraryQuestionBlockQuestionCondition.create({
                                    data: {
                                        operator: condition.operator,
                                        value: condition.value,
                                        libraryQuestionBlockQuestionId: id,
                                    },
                                });
                            }
                        }
                        else {
                            // Create new condition
                            yield tx.libraryQuestionBlockQuestionCondition.create({
                                data: {
                                    operator: condition.operator,
                                    value: condition.value,
                                    libraryQuestionBlockQuestionId: id,
                                },
                            });
                        }
                    }
                }
                // Return the updated question with options and conditions
                return yield tx.libraryQuestionBlockQuestion.findUnique({
                    where: { id },
                    include: {
                        questionOptions: true,
                        questionConditions: true,
                    },
                });
            }));
        });
    }
    deleteQuestion(id) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.libraryQuestionBlockQuestion.delete({
                where: { id },
            });
        });
    }
    findAll(userId) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.libraryQuestionBlockQuestion.findMany({
                where: { userId },
                include: {
                    questionOptions: true,
                    questionConditions: true,
                },
            });
        });
    }
}
exports.default = new LibraryQuestionRepository();
