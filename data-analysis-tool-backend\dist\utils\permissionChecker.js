"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.hasPermission = void 0;
const hasPermission = (permission, actionKey, userId, resource) => {
    if (permission === null || permission === void 0 ? void 0 : permission.manageProject)
        return true;
    return (permission === null || permission === void 0 ? void 0 : permission[actionKey]) === true;
};
exports.hasPermission = hasPermission;
