"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const userController_1 = require("../controllers/userController");
const auth_1 = require("../middleware/auth");
const router = express_1.default.Router();
// User registration
router.post("/signup", userController_1.signup);
// User login
router.post("/login", userController_1.login);
router.patch("/update", auth_1.authenticate, userController_1.updateProfile);
router.get("/resetpasswordtoken/:token", userController_1.checkPasswordResetToken);
router.post("/changepassword", auth_1.authenticate, userController_1.changePassword);
router.post("/forgetpassword", userController_1.forgetPassword);
router.post("/resetPassword", userController_1.resetPassword);
router.get("/verifyemail/:token", userController_1.verifyEmail);
router.get("/", auth_1.authenticate, userController_1.findAllUsers);
router.get("/profile", auth_1.authenticate, userController_1.userProfile);
router.post("/logout", auth_1.authenticate, userController_1.logout);
router.post("/logoutall", auth_1.authenticate, userController_1.logoutFromAllDevice);
router.post("/check-email", userController_1.checkUserByEmail);
router.post("/sendverificationemail", userController_1.sendVerificationEmail);
router.get("/me", auth_1.authenticate, userController_1.getUserSession);
router.patch("/change-email", auth_1.authenticate, userController_1.changeEmail);
router.get("/sessions", auth_1.authenticate, userController_1.getUserSessions);
router.get("/:id", userController_1.findUserById);
exports.default = router;
