(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8586],{25784:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});let t=a(23464).A.create({baseURL:"http://localhost:4000/api",headers:{"Content-Type":"application/json"},withCredentials:!0});t.interceptors.request.use(e=>e,e=>Promise.reject(e)),t.interceptors.response.use(e=>e,e=>("ERR_NETWORK"===e.code&&console.error("Network error - Please check if the backend server is running"),Promise.reject(e)));let r=t},35169:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},47269:(e,s,a)=>{Promise.resolve().then(a.bind(a,59748))},59748:(e,s,a)=>{"use strict";a.d(s,{ChangePasswordPage:()=>N});var t=a(95155),r=a(71402),l=a(25784),c=a(90232),n=a(78749),o=a(92657),i=a(35169),d=a(6874),p=a.n(d),m=a(35695),h=a(12115),x=a(62177),u=a(34540),w=a(17652);let N=()=>{let{register:e,formState:{errors:s,isSubmitting:a},handleSubmit:d,getValues:N,watch:y}=(0,x.mN)(),f=y("password"),b=y("confirmPassword"),j=(0,m.useSearchParams)().get("token"),g=(0,m.useRouter)(),A=(0,u.wA)(),k=(0,w.c3)(),[v,P]=(0,h.useState)(!1),[C,M]=(0,h.useState)(!1),S=async e=>{try{await l.A.post("/users/resetpassword",{token:j,newPassword:e.password}),A((0,r.Ds)({message:k("password_changed_successfully"),type:"success"})),g.push("/")}catch(e){console.error(e)}};return j?(0,t.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,t.jsxs)("div",{className:"section flex flex-col gap-8 w-11/12 mobile:w-4/5 tablet:w-lg",children:[(0,t.jsxs)("div",{className:"flex flex-col items-center gap-2",children:[(0,t.jsx)(c.A,{size:36}),(0,t.jsx)("h1",{className:"text-2xl tablet:text-3xl font-semibold text-center",children:k("createNewPassword")}),(0,t.jsx)("p",{className:"text-neutral-700 text-center",children:k("passwordRequirementsNote")})]}),(0,t.jsxs)("form",{className:"flex flex-col gap-4 ",onSubmit:d(S),noValidate:!0,children:[(0,t.jsxs)("div",{className:"label-input-group group",children:[(0,t.jsx)("label",{htmlFor:"password",className:"label-text",children:k("newPassword")}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{...e("password",{required:k("pleaseEnterPassword"),validate:{minLength:e=>e.length>=8||k("passwordMustBeAtLeast8Characters"),hasUppercase:e=>/[A-Z]/.test(e)||k("passwordMustContainAtLeastOneUppercaseLetter"),hasNumber:e=>/\d/.test(e)||k("passwordMustContainAtLeastOneNumber"),hasSymbol:e=>/[\W_]/.test(e)||k("passwordMustContainAtLeastOneSymbol")}}),id:"password",type:v?"text":"password",className:"input-field w-full pr-10",placeholder:k("enterNewPassword")}),f&&f.length>0&&(0,t.jsxs)("button",{type:"button",tabIndex:-1,className:"absolute right-2 top-1/2 -translate-y-1/2 text-gray-500",onClick:()=>P(!v),children:[v?(0,t.jsx)(n.A,{className:"h-4 w-4"}):(0,t.jsx)(o.A,{className:"h-4 w-4"}),(0,t.jsxs)("span",{className:"sr-only",children:[v?"Hide":"Show"," password"]})]})]}),s.password&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:"".concat(s.password.message)})]}),(0,t.jsxs)("div",{className:"label-input-group group",children:[(0,t.jsx)("label",{htmlFor:"confirm-password",className:"label-text",children:k("confirmPassword")}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{...e("confirmPassword",{required:k("pleaseConfirmPassword"),validate:e=>e===N("password")||k("passwordsDoNotMatch")}),id:"confirm-password",type:C?"text":"password",className:"input-field w-full pr-10",placeholder:k("confirmYourPassword")}),b&&b.length>0&&(0,t.jsxs)("button",{type:"button",tabIndex:-1,className:"absolute right-2 top-1/2 -translate-y-1/2 text-gray-500",onClick:()=>M(!C),children:[C?(0,t.jsx)(n.A,{className:"h-4 w-4"}):(0,t.jsx)(o.A,{className:"h-4 w-4"}),(0,t.jsxs)("span",{className:"sr-only",children:[C?"Hide":"Show"," password"]})]})]}),s.confirmPassword&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:"".concat(s.confirmPassword.message)})]}),(0,t.jsx)("button",{type:"submit",className:"btn-primary",children:a?(0,t.jsxs)("span",{className:"flex items-center gap-2",children:[k("updating"),(0,t.jsx)("div",{className:"animate-spin border-x-2 border-neutral-100 rounded-full size-4"})]}):(0,t.jsx)("span",{className:"flex items-center gap-2",children:k("resetPassword")})})]}),(0,t.jsxs)(p(),{href:"/",className:"text-neutral-700 self-center flex items-center gap-2",children:[(0,t.jsx)(i.A,{size:16})," ",k("backToSignin")]})]})}):(0,t.jsx)("p",{className:"text-red-500",children:k("noResetToken")})}},71402:(e,s,a)=>{"use strict";a.d(s,{Ay:()=>c,Ds:()=>r,_b:()=>l});let t=(0,a(51990).Z0)({name:"notification",initialState:{message:"",type:"",visible:!1},reducers:{showNotification:(e,s)=>{e.message=s.payload.message,e.type=s.payload.type,e.visible=!0},hideNotification:e=>{e.message="",e.type="",e.visible=!1}}}),{showNotification:r,hideNotification:l}=t.actions,c=t.reducer},78749:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},90232:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("shield-check",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},92657:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[6453,635,1111,1380,6874,8441,1684,7358],()=>s(47269)),_N_E=e.O()}]);