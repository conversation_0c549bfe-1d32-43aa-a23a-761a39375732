(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6656],{17652:(e,r,t)=>{"use strict";t.d(r,{c3:()=>l});var a=t(46453);function s(e,r){return(...e)=>{try{return r(...e)}catch{throw Error(void 0)}}}let l=s(0,a.c3);s(0,a.kc)},19946:(e,r,t)=>{"use strict";t.d(r,{A:()=>u});var a=t(12115);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,t)=>t?t.toUpperCase():r.toLowerCase()),n=e=>{let r=l(e);return r.charAt(0).toUpperCase()+r.slice(1)},i=function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return r.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim()};var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,a.forwardRef)((e,r)=>{let{color:t="currentColor",size:s=24,strokeWidth:l=2,absoluteStrokeWidth:n,className:c="",children:u,iconNode:d,...m}=e;return(0,a.createElement)("svg",{ref:r,...o,width:s,height:s,stroke:t,strokeWidth:n?24*Number(l)/Number(s):l,className:i("lucide",c),...m},[...d.map(e=>{let[r,t]=e;return(0,a.createElement)(r,t)}),...Array.isArray(u)?u:[u]])}),u=(e,r)=>{let t=(0,a.forwardRef)((t,l)=>{let{className:o,...u}=t;return(0,a.createElement)(c,{ref:l,iconNode:r,className:i("lucide-".concat(s(n(e))),"lucide-".concat(e),o),...u})});return t.displayName=n(e),t}},35169:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(19946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},35695:(e,r,t)=>{"use strict";var a=t(18999);t.o(a,"useParams")&&t.d(r,{useParams:function(){return a.useParams}}),t.o(a,"usePathname")&&t.d(r,{usePathname:function(){return a.usePathname}}),t.o(a,"useRouter")&&t.d(r,{useRouter:function(){return a.useRouter}}),t.o(a,"useSearchParams")&&t.d(r,{useSearchParams:function(){return a.useSearchParams}})},39716:(e,r,t)=>{"use strict";t.d(r,{F:()=>i});var a=t(95155),s=t(35695),l=t(6874),n=t.n(l);t(12115);let i=e=>{let{items:r}=e,t=(0,s.usePathname)(),l=e=>t.startsWith(e);return(0,a.jsx)("div",{className:"border-y border-neutral-400 rounded-md bg-primary-500 my-4 shadow-md",children:(0,a.jsx)("div",{className:"flex items-center",children:r.map(e=>e.disabled?(0,a.jsxs)("div",{className:"flex items-center gap-2 font-medium transition-all duration-300 p-4 border-b-2 text-gray-400 cursor-not-allowed",children:[e.icon,e.label]},e.route):(0,a.jsxs)(n(),{href:e.route,className:"flex items-center gap-2 font-medium transition-all duration-300 p-4 border-b-2 text-neutral-100 ".concat(l(e.route)?"border-neutral-100":"border-transparent hover:border-neutral-400"),children:[e.icon,e.label]},e.route))})})}},49992:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(19946).A)("file-pen",[["path",{d:"M12.5 22H18a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v9.5",key:"1couwa"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M13.378 15.626a1 1 0 1 0-3.004-3.004l-5.01 5.012a2 2 0 0 0-.506.854l-.837 2.87a.5.5 0 0 0 .62.62l2.87-.837a2 2 0 0 0 .854-.506z",key:"1y4qbx"}]])},76231:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d});var a=t(95155),s=t(49992),l=t(39716);let n=()=>{let e=[{label:"Form Builder",icon:(0,a.jsx)(s.A,{size:16}),route:"/library/question-block/form-builder"}];return(0,a.jsx)(l.F,{items:e})};var i=t(6874),o=t.n(i);t(12115);var c=t(35169),u=t(17652);let d=e=>{let{children:r}=e,t=(0,u.c3)();return(0,a.jsxs)("div",{className:"section flex flex-col gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h1",{className:"heading-text capitalize",children:t("questionBlock")}),(0,a.jsxs)(o(),{href:"/library",className:"flex items-center gap-2",children:[(0,a.jsx)(c.A,{size:16}),t("backToLibrary")]})]}),(0,a.jsx)(n,{}),(0,a.jsx)("div",{className:"px-8",children:r})]})}},95742:(e,r,t)=>{Promise.resolve().then(t.bind(t,76231))}},e=>{var r=r=>e(e.s=r);e.O(0,[6453,6874,8441,1684,7358],()=>r(95742)),_N_E=e.O()}]);