"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4695],{41050:(e,t,r)=>{r.d(t,{A:()=>w});let n=e=>[...new Set(e)],o=(e,t)=>e.filter(e=>!t.includes(e)),a=(e,t)=>e.filter(e=>t.includes(e)),i=e=>"bigint"==typeof e||!Number.isNaN(Number(e))&&Math.floor(Number(e))===e,l=e=>"bigint"==typeof e||e>=0&&Number.isSafeInteger(e);function s(e,t){let r;if(0===t.length)return e;let n=[...e];for(let e=n.length-1,o=0,a=0;e>0;e--,o++){o%=t.length,a+=r=t[o].codePointAt(0);let i=(r+o+a)%e,l=n[e],s=n[i];n[i]=l,n[e]=s}return n}let u=(e,t)=>{let r=[],n=e;if("bigint"==typeof n){let e=BigInt(t.length);do r.unshift(t[Number(n%e)]),n/=e;while(n>BigInt(0))}else do r.unshift(t[n%t.length]),n=Math.floor(n/t.length);while(n>0);return r},d=(e,t)=>e.reduce((r,n)=>{let o=t.indexOf(n);if(-1===o)throw Error(`The provided ID (${e.join("")}) is invalid, as it contains characters that do not exist in the alphabet (${t.join("")})`);if("bigint"==typeof r)return r*BigInt(t.length)+BigInt(o);let a=r*t.length+o;return Number.isSafeInteger(a)?a:(v("Unable to decode the provided string, due to lack of support for BigInt numbers in the current environment"),BigInt(r)*BigInt(t.length)+BigInt(o))},0),c=/^\+?\d+$/,p=e=>{if(!c.test(e))return Number.NaN;let t=Number.parseInt(e,10);return Number.isSafeInteger(t)?t:(v("Unable to encode the provided BigInt string without loss of information due to lack of support for BigInt type in the current environment"),BigInt(e))},h=(e,t,r)=>Array.from({length:Math.ceil(e.length/t)},(n,o)=>r(e.slice(o*t,(o+1)*t))),f=e=>new RegExp(e.map(e=>m(e)).sort((e,t)=>t.length-e.length).join("|")),g=e=>RegExp(`^[${e.map(e=>m(e)).sort((e,t)=>t.length-e.length).join("")}]+$`),m=e=>e.replace(/[\s#$()*+,.?[\\\]^{|}-]/g,"\\$&"),v=(e="BigInt is not available in this environment")=>{if("function"!=typeof BigInt)throw TypeError(e)};class w{constructor(e="",t=0,r="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890",i="cfhistuCFHISTU"){let l,u;if(this.minLength=t,"number"!=typeof t)throw TypeError(`Hashids: Provided 'minLength' has to be a number (is ${typeof t})`);if("string"!=typeof e)throw TypeError(`Hashids: Provided 'salt' has to be a string (is ${typeof e})`);if("string"!=typeof r)throw TypeError(`Hashids: Provided alphabet has to be a string (is ${typeof r})`);let d=Array.from(e),c=Array.from(r),p=Array.from(i);this.salt=d;let h=n(c);if(h.length<16)throw Error(`Hashids: alphabet must contain at least 16 unique characters, provided: ${h.join("")}`);this.alphabet=o(h,p);let m=a(p,h);this.seps=s(m,d),(0===this.seps.length||this.alphabet.length/this.seps.length>3.5)&&(l=Math.ceil(this.alphabet.length/3.5))>this.seps.length&&(u=l-this.seps.length,this.seps.push(...this.alphabet.slice(0,u)),this.alphabet=this.alphabet.slice(u)),this.alphabet=s(this.alphabet,d);let v=Math.ceil(this.alphabet.length/12);this.alphabet.length<3?(this.guards=this.seps.slice(0,v),this.seps=this.seps.slice(v)):(this.guards=this.alphabet.slice(0,v),this.alphabet=this.alphabet.slice(v)),this.guardsRegExp=f(this.guards),this.sepsRegExp=f(this.seps),this.allowedCharsRegExp=g([...this.alphabet,...this.guards,...this.seps])}encode(e,...t){let r=Array.isArray(e)?e:[...null!=e?[e]:[],...t];return 0===r.length?"":(r.every(i)||(r=r.map(e=>"bigint"==typeof e||"number"==typeof e?e:p(String(e)))),r.every(l))?this._encode(r).join(""):""}decode(e){return e&&"string"==typeof e&&0!==e.length?this._decode(e):[]}encodeHex(e){let t=e;switch(typeof t){case"bigint":t=t.toString(16);break;case"string":if(!/^[\dA-Fa-f]+$/.test(t))return"";break;default:throw Error(`Hashids: The provided value is neither a string, nor a BigInt (got: ${typeof t})`)}let r=h(t,12,e=>Number.parseInt(`1${e}`,16));return this.encode(r)}decodeHex(e){return this.decode(e).map(e=>e.toString(16).slice(1)).join("")}isValidId(e){return this.allowedCharsRegExp.test(e)}_encode(e){let{alphabet:t}=this,r=e.reduce((e,t,r)=>e+("bigint"==typeof t?Number(t%BigInt(r+100)):t%(r+100)),0),n=[t[r%t.length]],o=[...n],{seps:a}=this,{guards:i}=this;if(e.forEach((r,i)=>{let l=o.concat(this.salt,t),d=u(r,t=s(t,l));if(n.push(...d),i+1<e.length){let e=d[0].codePointAt(0)+i,t="bigint"==typeof r?Number(r%BigInt(e)):r%e;n.push(a[t%a.length])}}),n.length<this.minLength){let e=(r+n[0].codePointAt(0))%i.length;if(n.unshift(i[e]),n.length<this.minLength){let e=(r+n[2].codePointAt(0))%i.length;n.push(i[e])}}let l=Math.floor(t.length/2);for(;n.length<this.minLength;){t=s(t,t),n.unshift(...t.slice(l)),n.push(...t.slice(0,l));let e=n.length-this.minLength;if(e>0){let t=e/2;n=n.slice(t,t+this.minLength)}}return n}_decode(e){if(!this.isValidId(e))throw Error(`The provided ID (${e}) is invalid, as it contains characters that do not exist in the alphabet (${this.guards.join("")}${this.seps.join("")}${this.alphabet.join("")})`);let t=e.split(this.guardsRegExp),r=+(3===t.length||2===t.length),n=t[r];if(0===n.length)return[];let o=n[Symbol.iterator]().next().value,a=n.slice(o.length).split(this.sepsRegExp),i=this.alphabet,l=[];for(let e of a){let t=[o,...this.salt,...i],r=s(i,t.slice(0,i.length));l.push(d(Array.from(e),r)),i=r}return this._encode(l).join("")!==e?[]:l}}},48698:(e,t,r)=>{r.d(t,{H_:()=>eW,UC:()=>eZ,ty:()=>eB,q7:()=>ez,VF:()=>eY,ZL:()=>eX,bL:()=>eV,l9:()=>eq});var n=r(12115),o=r(85185),a=r(6101),i=r(46081),l=r(5845),s=r(63655),u=r(37328),d=r(94315),c=r(58434),p=r(92293),h=r(25519),f=r(61285),g=r(63753),m=r(34378),v=r(28905),w=r(89196),y=r(99708),b=r(39033),x=r(38168),C=r(31114),M=r(95155),j=["Enter"," "],R=["ArrowUp","PageDown","End"],I=["ArrowDown","PageUp","Home",...R],_={ltr:[...j,"ArrowRight"],rtl:[...j,"ArrowLeft"]},D={ltr:["ArrowLeft"],rtl:["ArrowRight"]},k="Menu",[E,N,P]=(0,u.N)(k),[T,S]=(0,i.A)(k,[P,g.Bk,w.RG]),L=(0,g.Bk)(),A=(0,w.RG)(),[O,B]=T(k),[F,K]=T(k),G=e=>{let{__scopeMenu:t,open:r=!1,children:o,dir:a,onOpenChange:i,modal:l=!0}=e,s=L(t),[u,c]=n.useState(null),p=n.useRef(!1),h=(0,b.c)(i),f=(0,d.jH)(a);return n.useEffect(()=>{let e=()=>{p.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>p.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,M.jsx)(g.bL,{...s,children:(0,M.jsx)(O,{scope:t,open:r,onOpenChange:h,content:u,onContentChange:c,children:(0,M.jsx)(F,{scope:t,onClose:n.useCallback(()=>h(!1),[h]),isUsingKeyboardRef:p,dir:f,modal:l,children:o})})})};G.displayName=k;var $=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,o=L(r);return(0,M.jsx)(g.Mz,{...o,...n,ref:t})});$.displayName="MenuAnchor";var U="MenuPortal",[H,V]=T(U,{forceMount:void 0}),q=e=>{let{__scopeMenu:t,forceMount:r,children:n,container:o}=e,a=B(U,t);return(0,M.jsx)(H,{scope:t,forceMount:r,children:(0,M.jsx)(v.C,{present:r||a.open,children:(0,M.jsx)(m.Z,{asChild:!0,container:o,children:n})})})};q.displayName=U;var X="MenuContent",[Z,z]=T(X),W=n.forwardRef((e,t)=>{let r=V(X,e.__scopeMenu),{forceMount:n=r.forceMount,...o}=e,a=B(X,e.__scopeMenu),i=K(X,e.__scopeMenu);return(0,M.jsx)(E.Provider,{scope:e.__scopeMenu,children:(0,M.jsx)(v.C,{present:n||a.open,children:(0,M.jsx)(E.Slot,{scope:e.__scopeMenu,children:i.modal?(0,M.jsx)(Y,{...o,ref:t}):(0,M.jsx)(J,{...o,ref:t})})})})}),Y=n.forwardRef((e,t)=>{let r=B(X,e.__scopeMenu),i=n.useRef(null),l=(0,a.s)(t,i);return n.useEffect(()=>{let e=i.current;if(e)return(0,x.Eq)(e)},[]),(0,M.jsx)(ee,{...e,ref:l,trapFocus:r.open,disableOutsidePointerEvents:r.open,disableOutsideScroll:!0,onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>r.onOpenChange(!1)})}),J=n.forwardRef((e,t)=>{let r=B(X,e.__scopeMenu);return(0,M.jsx)(ee,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>r.onOpenChange(!1)})}),Q=(0,y.TL)("MenuContent.ScrollLock"),ee=n.forwardRef((e,t)=>{let{__scopeMenu:r,loop:i=!1,trapFocus:l,onOpenAutoFocus:s,onCloseAutoFocus:u,disableOutsidePointerEvents:d,onEntryFocus:f,onEscapeKeyDown:m,onPointerDownOutside:v,onFocusOutside:y,onInteractOutside:b,onDismiss:x,disableOutsideScroll:j,..._}=e,D=B(X,r),k=K(X,r),E=L(r),P=A(r),T=N(r),[S,O]=n.useState(null),F=n.useRef(null),G=(0,a.s)(t,F,D.onContentChange),$=n.useRef(0),U=n.useRef(""),H=n.useRef(0),V=n.useRef(null),q=n.useRef("right"),z=n.useRef(0),W=j?C.A:n.Fragment,Y=e=>{var t,r;let n=U.current+e,o=T().filter(e=>!e.disabled),a=document.activeElement,i=null==(t=o.find(e=>e.ref.current===a))?void 0:t.textValue,l=function(e,t,r){var n;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=r?e.indexOf(r):-1,i=(n=Math.max(a,0),e.map((t,r)=>e[(n+r)%e.length]));1===o.length&&(i=i.filter(e=>e!==r));let l=i.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return l!==r?l:void 0}(o.map(e=>e.textValue),n,i),s=null==(r=o.find(e=>e.textValue===l))?void 0:r.ref.current;!function e(t){U.current=t,window.clearTimeout($.current),""!==t&&($.current=window.setTimeout(()=>e(""),1e3))}(n),s&&setTimeout(()=>s.focus())};n.useEffect(()=>()=>window.clearTimeout($.current),[]),(0,p.Oh)();let J=n.useCallback(e=>{var t,r;return q.current===(null==(t=V.current)?void 0:t.side)&&function(e,t){return!!t&&function(e,t){let{x:r,y:n}=e,o=!1;for(let e=0,a=t.length-1;e<t.length;a=e++){let i=t[e],l=t[a],s=i.x,u=i.y,d=l.x,c=l.y;u>n!=c>n&&r<(d-s)*(n-u)/(c-u)+s&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)}(e,null==(r=V.current)?void 0:r.area)},[]);return(0,M.jsx)(Z,{scope:r,searchRef:U,onItemEnter:n.useCallback(e=>{J(e)&&e.preventDefault()},[J]),onItemLeave:n.useCallback(e=>{var t;J(e)||(null==(t=F.current)||t.focus(),O(null))},[J]),onTriggerLeave:n.useCallback(e=>{J(e)&&e.preventDefault()},[J]),pointerGraceTimerRef:H,onPointerGraceIntentChange:n.useCallback(e=>{V.current=e},[]),children:(0,M.jsx)(W,{...j?{as:Q,allowPinchZoom:!0}:void 0,children:(0,M.jsx)(h.n,{asChild:!0,trapped:l,onMountAutoFocus:(0,o.m)(s,e=>{var t;e.preventDefault(),null==(t=F.current)||t.focus({preventScroll:!0})}),onUnmountAutoFocus:u,children:(0,M.jsx)(c.qW,{asChild:!0,disableOutsidePointerEvents:d,onEscapeKeyDown:m,onPointerDownOutside:v,onFocusOutside:y,onInteractOutside:b,onDismiss:x,children:(0,M.jsx)(w.bL,{asChild:!0,...P,dir:k.dir,orientation:"vertical",loop:i,currentTabStopId:S,onCurrentTabStopIdChange:O,onEntryFocus:(0,o.m)(f,e=>{k.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,M.jsx)(g.UC,{role:"menu","aria-orientation":"vertical","data-state":eI(D.open),"data-radix-menu-content":"",dir:k.dir,...E,..._,ref:G,style:{outline:"none",..._.style},onKeyDown:(0,o.m)(_.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,r=e.ctrlKey||e.altKey||e.metaKey,n=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!r&&n&&Y(e.key));let o=F.current;if(e.target!==o||!I.includes(e.key))return;e.preventDefault();let a=T().filter(e=>!e.disabled).map(e=>e.ref.current);R.includes(e.key)&&a.reverse(),function(e){let t=document.activeElement;for(let r of e)if(r===t||(r.focus(),document.activeElement!==t))return}(a)}),onBlur:(0,o.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout($.current),U.current="")}),onPointerMove:(0,o.m)(e.onPointerMove,ek(e=>{let t=e.target,r=z.current!==e.clientX;e.currentTarget.contains(t)&&r&&(q.current=e.clientX>z.current?"right":"left",z.current=e.clientX)}))})})})})})})});W.displayName=X;var et=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,M.jsx)(s.sG.div,{role:"group",...n,ref:t})});et.displayName="MenuGroup";var er=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,M.jsx)(s.sG.div,{...n,ref:t})});er.displayName="MenuLabel";var en="MenuItem",eo="menu.itemSelect",ea=n.forwardRef((e,t)=>{let{disabled:r=!1,onSelect:i,...l}=e,u=n.useRef(null),d=K(en,e.__scopeMenu),c=z(en,e.__scopeMenu),p=(0,a.s)(t,u),h=n.useRef(!1);return(0,M.jsx)(ei,{...l,ref:p,disabled:r,onClick:(0,o.m)(e.onClick,()=>{let e=u.current;if(!r&&e){let t=new CustomEvent(eo,{bubbles:!0,cancelable:!0});e.addEventListener(eo,e=>null==i?void 0:i(e),{once:!0}),(0,s.hO)(e,t),t.defaultPrevented?h.current=!1:d.onClose()}}),onPointerDown:t=>{var r;null==(r=e.onPointerDown)||r.call(e,t),h.current=!0},onPointerUp:(0,o.m)(e.onPointerUp,e=>{var t;h.current||null==(t=e.currentTarget)||t.click()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=""!==c.searchRef.current;r||t&&" "===e.key||j.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});ea.displayName=en;var ei=n.forwardRef((e,t)=>{let{__scopeMenu:r,disabled:i=!1,textValue:l,...u}=e,d=z(en,r),c=A(r),p=n.useRef(null),h=(0,a.s)(t,p),[f,g]=n.useState(!1),[m,v]=n.useState("");return n.useEffect(()=>{let e=p.current;if(e){var t;v((null!=(t=e.textContent)?t:"").trim())}},[u.children]),(0,M.jsx)(E.ItemSlot,{scope:r,disabled:i,textValue:null!=l?l:m,children:(0,M.jsx)(w.q7,{asChild:!0,...c,focusable:!i,children:(0,M.jsx)(s.sG.div,{role:"menuitem","data-highlighted":f?"":void 0,"aria-disabled":i||void 0,"data-disabled":i?"":void 0,...u,ref:h,onPointerMove:(0,o.m)(e.onPointerMove,ek(e=>{i?d.onItemLeave(e):(d.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.m)(e.onPointerLeave,ek(e=>d.onItemLeave(e))),onFocus:(0,o.m)(e.onFocus,()=>g(!0)),onBlur:(0,o.m)(e.onBlur,()=>g(!1))})})})}),el=n.forwardRef((e,t)=>{let{checked:r=!1,onCheckedChange:n,...a}=e;return(0,M.jsx)(eg,{scope:e.__scopeMenu,checked:r,children:(0,M.jsx)(ea,{role:"menuitemcheckbox","aria-checked":e_(r)?"mixed":r,...a,ref:t,"data-state":eD(r),onSelect:(0,o.m)(a.onSelect,()=>null==n?void 0:n(!!e_(r)||!r),{checkForDefaultPrevented:!1})})})});el.displayName="MenuCheckboxItem";var es="MenuRadioGroup",[eu,ed]=T(es,{value:void 0,onValueChange:()=>{}}),ec=n.forwardRef((e,t)=>{let{value:r,onValueChange:n,...o}=e,a=(0,b.c)(n);return(0,M.jsx)(eu,{scope:e.__scopeMenu,value:r,onValueChange:a,children:(0,M.jsx)(et,{...o,ref:t})})});ec.displayName=es;var ep="MenuRadioItem",eh=n.forwardRef((e,t)=>{let{value:r,...n}=e,a=ed(ep,e.__scopeMenu),i=r===a.value;return(0,M.jsx)(eg,{scope:e.__scopeMenu,checked:i,children:(0,M.jsx)(ea,{role:"menuitemradio","aria-checked":i,...n,ref:t,"data-state":eD(i),onSelect:(0,o.m)(n.onSelect,()=>{var e;return null==(e=a.onValueChange)?void 0:e.call(a,r)},{checkForDefaultPrevented:!1})})})});eh.displayName=ep;var ef="MenuItemIndicator",[eg,em]=T(ef,{checked:!1}),ev=n.forwardRef((e,t)=>{let{__scopeMenu:r,forceMount:n,...o}=e,a=em(ef,r);return(0,M.jsx)(v.C,{present:n||e_(a.checked)||!0===a.checked,children:(0,M.jsx)(s.sG.span,{...o,ref:t,"data-state":eD(a.checked)})})});ev.displayName=ef;var ew=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,M.jsx)(s.sG.div,{role:"separator","aria-orientation":"horizontal",...n,ref:t})});ew.displayName="MenuSeparator";var ey=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,o=L(r);return(0,M.jsx)(g.i3,{...o,...n,ref:t})});ey.displayName="MenuArrow";var[eb,ex]=T("MenuSub"),eC="MenuSubTrigger",eM=n.forwardRef((e,t)=>{let r=B(eC,e.__scopeMenu),i=K(eC,e.__scopeMenu),l=ex(eC,e.__scopeMenu),s=z(eC,e.__scopeMenu),u=n.useRef(null),{pointerGraceTimerRef:d,onPointerGraceIntentChange:c}=s,p={__scopeMenu:e.__scopeMenu},h=n.useCallback(()=>{u.current&&window.clearTimeout(u.current),u.current=null},[]);return n.useEffect(()=>h,[h]),n.useEffect(()=>{let e=d.current;return()=>{window.clearTimeout(e),c(null)}},[d,c]),(0,M.jsx)($,{asChild:!0,...p,children:(0,M.jsx)(ei,{id:l.triggerId,"aria-haspopup":"menu","aria-expanded":r.open,"aria-controls":l.contentId,"data-state":eI(r.open),...e,ref:(0,a.t)(t,l.onTriggerChange),onClick:t=>{var n;null==(n=e.onClick)||n.call(e,t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),r.open||r.onOpenChange(!0))},onPointerMove:(0,o.m)(e.onPointerMove,ek(t=>{s.onItemEnter(t),!t.defaultPrevented&&(e.disabled||r.open||u.current||(s.onPointerGraceIntentChange(null),u.current=window.setTimeout(()=>{r.onOpenChange(!0),h()},100)))})),onPointerLeave:(0,o.m)(e.onPointerLeave,ek(e=>{var t,n;h();let o=null==(t=r.content)?void 0:t.getBoundingClientRect();if(o){let t=null==(n=r.content)?void 0:n.dataset.side,a="right"===t,i=o[a?"left":"right"],l=o[a?"right":"left"];s.onPointerGraceIntentChange({area:[{x:e.clientX+(a?-5:5),y:e.clientY},{x:i,y:o.top},{x:l,y:o.top},{x:l,y:o.bottom},{x:i,y:o.bottom}],side:t}),window.clearTimeout(d.current),d.current=window.setTimeout(()=>s.onPointerGraceIntentChange(null),300)}else{if(s.onTriggerLeave(e),e.defaultPrevented)return;s.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.m)(e.onKeyDown,t=>{let n=""!==s.searchRef.current;if(!e.disabled&&(!n||" "!==t.key)&&_[i.dir].includes(t.key)){var o;r.onOpenChange(!0),null==(o=r.content)||o.focus(),t.preventDefault()}})})})});eM.displayName=eC;var ej="MenuSubContent",eR=n.forwardRef((e,t)=>{let r=V(X,e.__scopeMenu),{forceMount:i=r.forceMount,...l}=e,s=B(X,e.__scopeMenu),u=K(X,e.__scopeMenu),d=ex(ej,e.__scopeMenu),c=n.useRef(null),p=(0,a.s)(t,c);return(0,M.jsx)(E.Provider,{scope:e.__scopeMenu,children:(0,M.jsx)(v.C,{present:i||s.open,children:(0,M.jsx)(E.Slot,{scope:e.__scopeMenu,children:(0,M.jsx)(ee,{id:d.contentId,"aria-labelledby":d.triggerId,...l,ref:p,align:"start",side:"rtl"===u.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var t;u.isUsingKeyboardRef.current&&(null==(t=c.current)||t.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>{e.target!==d.trigger&&s.onOpenChange(!1)}),onEscapeKeyDown:(0,o.m)(e.onEscapeKeyDown,e=>{u.onClose(),e.preventDefault()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),r=D[u.dir].includes(e.key);if(t&&r){var n;s.onOpenChange(!1),null==(n=d.trigger)||n.focus(),e.preventDefault()}})})})})})});function eI(e){return e?"open":"closed"}function e_(e){return"indeterminate"===e}function eD(e){return e_(e)?"indeterminate":e?"checked":"unchecked"}function ek(e){return t=>"mouse"===t.pointerType?e(t):void 0}eR.displayName=ej;var eE="DropdownMenu",[eN,eP]=(0,i.A)(eE,[S]),eT=S(),[eS,eL]=eN(eE),eA=e=>{let{__scopeDropdownMenu:t,children:r,dir:o,open:a,defaultOpen:i,onOpenChange:s,modal:u=!0}=e,d=eT(t),c=n.useRef(null),[p,h]=(0,l.i)({prop:a,defaultProp:null!=i&&i,onChange:s,caller:eE});return(0,M.jsx)(eS,{scope:t,triggerId:(0,f.B)(),triggerRef:c,contentId:(0,f.B)(),open:p,onOpenChange:h,onOpenToggle:n.useCallback(()=>h(e=>!e),[h]),modal:u,children:(0,M.jsx)(G,{...d,open:p,onOpenChange:h,dir:o,modal:u,children:r})})};eA.displayName=eE;var eO="DropdownMenuTrigger",eB=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,disabled:n=!1,...i}=e,l=eL(eO,r),u=eT(r);return(0,M.jsx)($,{asChild:!0,...u,children:(0,M.jsx)(s.sG.button,{type:"button",id:l.triggerId,"aria-haspopup":"menu","aria-expanded":l.open,"aria-controls":l.open?l.contentId:void 0,"data-state":l.open?"open":"closed","data-disabled":n?"":void 0,disabled:n,...i,ref:(0,a.t)(t,l.triggerRef),onPointerDown:(0,o.m)(e.onPointerDown,e=>{!n&&0===e.button&&!1===e.ctrlKey&&(l.onOpenToggle(),l.open||e.preventDefault())}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{!n&&(["Enter"," "].includes(e.key)&&l.onOpenToggle(),"ArrowDown"===e.key&&l.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eB.displayName=eO;var eF=e=>{let{__scopeDropdownMenu:t,...r}=e,n=eT(t);return(0,M.jsx)(q,{...n,...r})};eF.displayName="DropdownMenuPortal";var eK="DropdownMenuContent",eG=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...a}=e,i=eL(eK,r),l=eT(r),s=n.useRef(!1);return(0,M.jsx)(W,{id:i.contentId,"aria-labelledby":i.triggerId,...l,...a,ref:t,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;s.current||null==(t=i.triggerRef.current)||t.focus(),s.current=!1,e.preventDefault()}),onInteractOutside:(0,o.m)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey,n=2===t.button||r;(!i.modal||n)&&(s.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eG.displayName=eK,n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eT(r);return(0,M.jsx)(et,{...o,...n,ref:t})}).displayName="DropdownMenuGroup",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eT(r);return(0,M.jsx)(er,{...o,...n,ref:t})}).displayName="DropdownMenuLabel";var e$=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eT(r);return(0,M.jsx)(ea,{...o,...n,ref:t})});e$.displayName="DropdownMenuItem";var eU=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eT(r);return(0,M.jsx)(el,{...o,...n,ref:t})});eU.displayName="DropdownMenuCheckboxItem",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eT(r);return(0,M.jsx)(ec,{...o,...n,ref:t})}).displayName="DropdownMenuRadioGroup",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eT(r);return(0,M.jsx)(eh,{...o,...n,ref:t})}).displayName="DropdownMenuRadioItem";var eH=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eT(r);return(0,M.jsx)(ev,{...o,...n,ref:t})});eH.displayName="DropdownMenuItemIndicator",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eT(r);return(0,M.jsx)(ew,{...o,...n,ref:t})}).displayName="DropdownMenuSeparator",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eT(r);return(0,M.jsx)(ey,{...o,...n,ref:t})}).displayName="DropdownMenuArrow",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eT(r);return(0,M.jsx)(eM,{...o,...n,ref:t})}).displayName="DropdownMenuSubTrigger",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eT(r);return(0,M.jsx)(eR,{...o,...n,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}).displayName="DropdownMenuSubContent";var eV=eA,eq=eB,eX=eF,eZ=eG,ez=e$,eW=eU,eY=eH}}]);