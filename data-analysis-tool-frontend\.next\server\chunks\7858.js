"use strict";exports.id=7858,exports.ids=[7858],exports.modules={58857:(e,s,a)=>{a.d(s,{m:()=>y});var t=a(60687),l=a(43210),r=a(38587),i=a(26273),n=a(29494),d=a(71845),o=a(16189),c=a(6986),m=a(21650),u=a(86429),x=a(11860),b=a(8693),p=a(54050),h=a(54864),j=a(19150),v=a(77618);let g=({onClose:e,projectId:s,onUserAdded:a})=>{let r=(0,v.c3)(),i=[{label:r("viewForm"),value:"viewForm"},{label:r("editForm"),value:"editForm"},{label:r("viewSubmissions"),value:"viewSubmissions"},{label:r("editSubmissions"),value:"editSubmissions"},{label:r("addSubmissions"),value:"addSubmissions"},{label:r("deleteSubmissions"),value:"deleteSubmissions"},{label:r("validateSubmissions"),value:"validateSubmissions"},{label:r("manageProject"),value:"manageProject"}],[n,o]=(0,l.useState)(""),[c,m]=(0,l.useState)([]),[u,g]=(0,l.useState)(""),[f,N]=(0,l.useState)(!1),[y,w]=(0,l.useState)(null),S=(0,b.jE)(),A=(0,h.wA)(),F=(0,l.useRef)(null),k=e=>/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(e),C=e=>{m(s=>s.includes(e)?s.filter(s=>s!==e):[...s,e])},P=(0,p.n)({mutationFn:d.Oo,onSuccess:()=>{w(!0),g("")},onError:e=>{w(!1),g(e.response?.data?.message||r("userNotFound"))},onSettled:()=>{N(!1)}}),$=()=>n?k(n)?y?0===c.length?(g(r("selectPermission")),!1):(g(""),!0):(g(r("userNotFound")),!1):(g(r("invalidEmail")),!1):(g(r("emailRequired")),!1),U=(0,p.n)({mutationFn:()=>{let e=c.reduce((e,s)=>(e[s]=!0,e),{});return(0,d.wI)({projectId:s,email:n,permissions:e})},onSuccess:()=>{S.invalidateQueries({queryKey:["projectUsers",s]}),A((0,j.Ds)({message:r("userAdded"),type:"success"})),a&&a(),e()},onError:e=>{let s;s="string"==typeof e?e:e instanceof Error?e.message:e.response?.data?.message?"object"==typeof e.response.data.message?JSON.stringify(e.response.data.message):e.response.data.message:r("failedToAddUser"),A((0,j.Ds)({message:s,type:"error"})),g(s)}});return(0,t.jsxs)("div",{className:"bg-neutral-100 p-6 rounded-md",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{className:`w-full border ${u?"border-red-500":"border-neutral-300"} rounded px-3 py-2 mb-4 focus:outline-none focus:ring-2 focus:ring-primary-200 placeholder:text-neutral-500`,placeholder:r("email"),value:n,onChange:e=>{let s=e.target.value;if(o(s),w(null),g(""),s){if(!k(s))return void g(r("invalidEmail"));F.current&&clearTimeout(F.current),F.current=setTimeout(()=>{N(!0),P.mutate(s)},800)}}}),(0,t.jsx)("button",{className:"absolute right-2 top-2 text-neutral-700 hover:text-neutral-900",onClick:e,type:"button",children:(0,t.jsx)(x.A,{size:22})}),f&&(0,t.jsx)("p",{className:"text-neutral-500 text-sm mb-2",children:r("verifyingEmail")}),!0===y&&(0,t.jsx)("p",{className:"text-green-500 text-sm mb-2",children:r("userFound")}),u&&(0,t.jsx)("p",{className:"text-red-500 text-sm mb-2",children:u})]}),(0,t.jsx)("div",{className:"flex flex-col gap-2",children:i.map(e=>(0,t.jsx)("div",{className:"flex flex-col",children:(0,t.jsxs)("label",{className:"flex items-center gap-2",children:[(0,t.jsx)("input",{type:"checkbox",checked:c.includes(e.value),onChange:()=>C(e.value)}),e.label]})},e.value))}),(0,t.jsx)("button",{className:`mt-6 ${U.isPending||f?"bg-neutral-400":"bg-blue-400 hover:bg-blue-500"} text-white px-6 py-2 rounded disabled:opacity-50`,disabled:U.isPending||f||!n||0===c.length||!y,onClick:()=>{if(!s)return void g(r("projectIdRequired"));$()&&U.mutate()},children:U.isPending?r("adding"):r("grantPermissions")})]})};var f=a(12810),N=a(24934);let y=({showModal:e,onClose:s,onShare:a,selectedProject:x})=>{let{hashedId:b}=(0,o.useParams)(),{user:p}=(0,m.A)(),[h,j]=(0,l.useState)(!1),y=b?(0,c.D)(b):null,w=x?.id||y,S=(0,v.c3)(),{data:A,isLoading:F}=(0,n.I)({queryKey:["project",w],queryFn:async()=>await (0,d.kf)({projectId:w}),enabled:!!w&&!!p?.id}),[k,C]=(0,l.useState)([]),[P,$]=(0,l.useState)(!1);if((0,l.useEffect)(()=>{let s=async()=>{if(w){$(!0);try{let e=await f.A.get(`/project-users/${w}`);if(e.data&&e.data.data&&e.data.data.AllUser){let s=e.data.data.AllUser||[];C(s)}else console.warn("No users data in response:",e.data),C([])}catch(e){console.error("Error fetching project users:",e),C([])}finally{$(!1)}}};e&&w&&s()},[w,e]),F)return(0,t.jsx)(u.A,{});let U=A||x;if(!U)return(0,t.jsx)(r.A,{isOpen:e,onClose:s,className:"p-6 rounded-md",children:(0,t.jsxs)("div",{className:"text-center py-4",children:[(0,t.jsx)("p",{className:"text-red-500",children:S("projectNotFound")}),(0,t.jsx)(N.$,{onClick:s,className:"mt-4",children:S("close")})]})});let E=e=>{if(!e)return"bg-gray-500";let s=["bg-green-500","bg-blue-500","bg-red-500","bg-purple-500","bg-yellow-500","bg-pink-500","bg-indigo-500","bg-orange-500"];return s[e.charCodeAt(0)%s.length]},I=e=>e?e.charAt(0).toUpperCase():"?";return(0,t.jsxs)(r.A,{isOpen:e,onClose:s,className:"p-6 rounded-md",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold text-neutral-700",children:`${S("sharingProject")}: ${U.name||""}`}),(0,t.jsxs)("div",{className:"w-2xl mt-4 p-4 max-h-[500px] overflow-y-auto",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,t.jsx)("div",{className:"text-xl font-semibold",children:S("whoHasAccess")}),(0,t.jsxs)("div",{className:"flex items-center border rounded-md px-3 py-1.5 cursor-pointer hover:bg-gray-50",onClick:()=>j(!0),children:[(0,t.jsx)(i._rf,{size:18,className:"mr-2"}),(0,t.jsx)("div",{className:"text-sm",children:S("addUser")})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[U.user&&(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:`w-10 h-10 rounded-full ${E(U.user.name)} flex items-center justify-center text-neutral-100 font-medium mr-3`,children:I(U.user.name)}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("div",{className:"font-medium",children:U.user.name||U.user.email||S("unknownUser")}),(0,t.jsx)("div",{className:"inline-block bg-gray-100 text-xs px-2 py-0.5 rounded",children:S("owner")})]})]}),P?(0,t.jsx)("div",{className:"py-2 text-center",children:(0,t.jsx)("div",{className:"inline-block w-6 h-6 rounded-full border-2 border-t-transparent border-primary-500 animate-spin"})}):k&&k.length>0?k.map((e,s)=>{let a=e.user&&e.user.name||e.user&&e.user.email||`User ${e.userId}`;return(0,t.jsxs)("div",{className:"flex items-center mt-4",children:[(0,t.jsx)("div",{className:`w-10 h-10 rounded-full ${E(a)} flex items-center justify-center text-neutral-100 font-medium mr-3`,children:I(a)}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("div",{className:"font-medium",children:a}),(0,t.jsx)("div",{className:"flex flex-wrap gap-1 mt-1",children:e.permission&&Object.entries(e.permission).filter(([e,s])=>!0===s).map(([e])=>(0,t.jsx)("div",{className:"inline-block bg-gray-100 text-xs px-2 py-0.5 rounded",children:"viewForm"===e?S("viewForm"):"editForm"===e?S("editForm"):"viewSubmissions"===e?S("viewSubmissions"):"editSubmissions"===e?S("editSubmissions"):"addSubmissions"===e?S("addSubmissions"):"deleteSubmissions"===e?S("deleteSubmissions"):"validateSubmissions"===e?S("validateSubmissions"):"manageProject"===e?S("manageProject"):e},e))})]})]},s)}):null]}),h&&w&&(0,t.jsx)("div",{className:"mt-6",children:(0,t.jsx)(g,{onClose:()=>j(!1),projectId:w,onUserAdded:()=>{(async()=>{$(!0);try{let e=await f.A.get(`/project-users/${w}`);if(e.data&&e.data.data&&e.data.data.AllUser){let s=e.data.data.AllUser||[];C(s)}else C([])}catch(e){console.error("Error fetching project users:",e),C([])}finally{$(!1)}})()}})}),(0,t.jsx)("div",{className:"mt-8 border-t pt-6",children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:S("anonymousSubmissions")}),(0,t.jsx)("div",{className:"text-sm text-gray-500 mt-1",children:S("allowAnonymousSubmissions")})]}),(0,t.jsx)("div",{className:"w-12 h-6 bg-gray-200 rounded-full relative cursor-pointer",children:(0,t.jsx)("div",{className:"w-5 h-5 bg-neutral-100 rounded-full absolute top-0.5 left-0.5 shadow"})})]})}),(0,t.jsx)("div",{className:"mt-8",children:(0,t.jsx)("div",{className:"inline-block border rounded-md px-4 py-2 text-sm cursor-pointer hover:bg-gray-50",children:S("copyTeamFromProject")})})]})]})}},73678:(e,s,a)=>{a.d(s,{R:()=>r});var t=a(60687);a(43210);var l=a(38587);let r=({showModal:e,onClose:s,onConfirm:a,title:r,description:i,confirmButtonText:n,cancelButtonText:d,confirmButtonClass:o,children:c})=>(0,t.jsxs)(l.A,{isOpen:e,onClose:s,className:"p-6 rounded-md max-w-xl",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold text-neutral-700",children:r}),(0,t.jsx)("div",{className:"text-neutral-700 mt-2",children:i}),c&&(0,t.jsx)("div",{className:"mt-6 space-y-4",children:c}),(0,t.jsxs)("div",{className:"flex justify-end gap-4 mt-6",children:[(0,t.jsx)("button",{className:"btn-outline",onClick:s,type:"button",children:d||"Cancel"}),(0,t.jsx)("button",{className:`font-medium rounded-md shadow-md text-neutral-100 flex items-center justify-center gap-2 px-4 py-2 active:scale-95 transition-all duration-300 ${o}`,onClick:a,type:"button",children:n})]})]})}};