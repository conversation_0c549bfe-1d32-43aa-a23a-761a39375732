exports.id=5041,exports.ids=[5041],exports.modules={3984:(e,t,a)=>{"use strict";a.d(t,{I7:()=>l,J2:()=>s,QK:()=>i,Xu:()=>n,nh:()=>o});var r=a(12810);let s=async({templateId:e})=>{let{data:t}=await r.A.get(`/libraries/${e}`);return t.template},n=async e=>{let{data:t}=await r.A.post("/libraries",e);return t},i=async()=>{let{data:e}=await r.A.get("/libraries");return e.templates},l=async e=>{let{data:t}=await r.A.delete(`/libraries/${e}`);return t},o=async({templateIds:e})=>null},6986:(e,t,a)=>{"use strict";a.d(t,{D:()=>l,l:()=>i});var r=a(53907);let s=process.env.SALT||"rushan-salt",n=new r.A(s,12),i=e=>n.encode(e),l=e=>{let t=n.decode(e)[0];return"bigint"==typeof t?t<Number.MAX_SAFE_INTEGER?Number(t):null:"number"==typeof t?t:null}},15566:e=>{"use strict";e.exports=JSON.parse('["Afghanistan","Albania","Algeria","Andorra","Angola","Argentina","Armenia","Australia","Austria","Azerbaijan","Bahamas","Bahrain","Bangladesh","Barbados","Belarus","Belgium","Belize","Benin","Bhutan","Bolivia","Bosnia and Herzegovina","Botswana","Brazil","Brunei","Bulgaria","Burkina Faso","Burundi","Cabo Verde","Cambodia","Cameroon","Canada","Central African Republic","Chad","Chile","China","Colombia","Comoros","Congo (Congo-Brazzaville)","Costa Rica","Croatia","Cuba","Cyprus","Czech Republic","Denmark","Djibouti","Dominica","Dominican Republic","Ecuador","Egypt","El Salvador","Equatorial Guinea","Eritrea","Estonia","Eswatini","Ethiopia","Fiji","Finland","France","Gabon","Gambia","Georgia","Germany","Ghana","Greece","Grenada","Guatemala","Guinea","Guinea-Bissau","Guyana","Haiti","Honduras","Hungary","Iceland","India","Indonesia","Iran","Iraq","Ireland","Israel","Italy","Jamaica","Japan","Jordan","Kazakhstan","Kenya","Kiribati","Kuwait","Kyrgyzstan","Laos","Latvia","Lebanon","Lesotho","Liberia","Libya","Liechtenstein","Lithuania","Luxembourg","Madagascar","Malawi","Malaysia","Maldives","Mali","Malta","Marshall Islands","Mauritania","Mauritius","Mexico","Micronesia","Moldova","Monaco","Mongolia","Montenegro","Morocco","Mozambique","Myanmar","Namibia","Nauru","Nepal","Netherlands","New Zealand","Nicaragua","Niger","Nigeria","North Korea","North Macedonia","Norway","Oman","Pakistan","Palau","Palestine","Panama","Papua New Guinea","Paraguay","Peru","Philippines","Poland","Portugal","Qatar","Romania","Russia","Rwanda","Saint Kitts and Nevis","Saint Lucia","Saint Vincent and the Grenadines","Samoa","San Marino","Sao Tome and Principe","Saudi Arabia","Senegal","Serbia","Seychelles","Sierra Leone","Singapore","Slovakia","Slovenia","Solomon Islands","Somalia","South Africa","South Korea","South Sudan","Spain","Sri Lanka","Sudan","Suriname","Sweden","Switzerland","Syria","Taiwan","Tajikistan","Tanzania","Thailand","Timor-Leste","Togo","Tonga","Trinidad and Tobago","Tunisia","Turkey","Turkmenistan","Tuvalu","Uganda","Ukraine","United Arab Emirates","United Kingdom","United States","Uruguay","Uzbekistan","Vanuatu","Vatican City","Venezuela","Vietnam","Yemen","Zambia","Zimbabwe"]')},15616:(e,t,a)=>{"use strict";a.d(t,{T:()=>i});var r=a(60687),s=a(43210),n=a(96241);let i=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("textarea",{className:(0,n.cn)("flex min-h-[80px] w-full rounded-md border border-gray-200 bg-neutral-100 px-3 py-2 text-sm shadow-sm placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-700 dark:bg-gray-900 dark:placeholder:text-gray-500",e),ref:a,...t}));i.displayName="Textarea"},24934:(e,t,a)=>{"use strict";a.d(t,{$:()=>o});var r=a(60687);a(43210);var s=a(8730),n=a(24224),i=a(96241);let l=(0,n.F)("inline-flex items-center justify-center gap-2 neutral-100space-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-neutral-100 shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o({className:e,variant:t,size:a,asChild:n=!1,...o}){let c=n?s.DX:"button";return(0,r.jsx)(c,{"data-slot":"button",className:(0,i.cn)(l({variant:t,size:a,className:e})),...o})}},32833:(e,t,a)=>{"use strict";a.d(t,{b:()=>r});let r={information_media:"Information / Media",econommic_social_development:"Economic & Social Development",security_police_peacekeeping:"Security / Police / Peacekeeping",disarmament_and_demobilization:"Disarmament & Demobilization",environment:"Environment",private_sector:"Private Sector",humanitarian_coordination_information_management:"Humanitarian - Coordination & Info Management",humanitarian_multiple_clusters:"Humanitarian - Multiple Clusters",humanitarian_camp_management_and_coordination:"Humanitarian - Camp Management & Coordination",humanitarian_early_recovery:"Humanitarian - Early Recovery",humanitarian_education:"Humanitarian - Education",humanitarian_emergency_shelter:"Humanitarian - Emergency Shelter",humanitarian_emergency_telecoms:"Humanitarian - Emergency Telecoms",humanitarian_food_security:"Humanitarian - Food Security",humanitarian_health:"Humanitarian - Health",humanitarian_logistics:"Humanitarian - Logistics",humanitarian_nutrition:"Humanitarian - Nutrition",humanitarian_protection:"Humanitarian - Protection",humanitarian_sanitation_water_and_hygiene:"Humanitarian - Sanitation / Water / Hygiene",other:"Other"}},35523:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>es});var r=a(60687),s=a(43210),n=a(21650),i=a(40083),l=a(85814),o=a.n(l),c=a(16189),d=a(79962),u=a(64668),m=a(77618);let p=({toggleSidebar:e,navbarRef:t})=>{let[a,l]=(0,s.useState)(!1),p=(0,s.useRef)(null);(0,c.useRouter)();let x=(0,m.c3)();(0,s.useEffect)(()=>{let e=e=>{p.current&&!p.current.contains(e.target)&&l(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]);let{user:h,logout:g}=(0,n.A)();return(0,r.jsx)("header",{className:"bg-primary-800 p-4 sticky top-0 z-40",ref:t,children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)("button",{onClick:e,className:"laptop:hidden text-neutral-100 p-2 rounded hover:bg-primary-700",children:(0,r.jsx)(d.$4x,{size:24})}),(0,r.jsx)(o(),{href:"/dashboard",className:"text-neutral-100 text-2xl font-bold cursor-pointer hover:text-neutral-300 transition-all ease-in-out",children:x("dataAnalysis")})]}),(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)(u.A,{}),(0,r.jsxs)("div",{className:"relative",ref:p,children:[(0,r.jsx)("button",{onClick:()=>l(!a),className:"w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center text-neutral-100 font-semibold cursor-pointer",children:h?.name[0].toUpperCase()}),a&&(0,r.jsxs)("div",{className:"absolute right-0 mt-2 w-64 bg-neutral-100 rounded-md shadow-lg p-4 flex flex-col gap-2",children:[(0,r.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"size-10 bg-orange-500 rounded-full flex items-center justify-center text-neutral-100 text-xl font-semibold shrink-0",children:h?.name[0].toUpperCase()}),(0,r.jsxs)("div",{className:"flex flex-col min-w-0",children:[(0,r.jsx)("div",{className:"font-medium capitalize",children:h?.name}),(0,r.jsx)("div",{className:"text-sm text-neutral-700 truncate",children:h?.email})]})]}),(0,r.jsx)(o(),{href:"/account/profile",className:"btn-primary",onClick:()=>{l(!1)},children:x("profile")})]}),(0,r.jsx)("hr",{className:"border-neutral-400"}),(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsx)(o(),{onClick:()=>l(!1),href:"/terms",className:"text-sm text-neutral-700 hover:bg-neutral-700/10 px-4 py-1 rounded-md",children:x("termsOfService")}),(0,r.jsx)(o(),{onClick:()=>l(!1),href:"/policy",className:"block px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-200",children:x("privacyPolicy")})]}),(0,r.jsx)("hr",{className:"border-neutral-400"}),(0,r.jsxs)("button",{onClick:g,className:"flex items-center text-neutral-700 gap-2 px-4 py-2 hover:bg-neutral-700/10 rounded-md active:scale-95 transition-all duration-300 w-full cursor-pointer",children:[(0,r.jsx)(i.A,{size:16}),x("logout")]})]})]})]})]})})};var x=a(44255),h=a(26273),g=a(54864),b=a(58432),f=a(35790),y=a(17257),j=a(20255),v=a(69587),N=a(29494),w=a(71845),k=a(8610);let C=()=>{let{user:e}=(0,n.A)(),t=(0,k.Ym)(),a=(0,m.c3)(),{data:r,isLoading:s,isError:i}=(0,N.I)({queryKey:["projects",e?.id],queryFn:w.vj,enabled:!!e?.id}),l=[],o=[],c=[];return!s&&r&&(l=r.filter(e=>"deployed"===e.status),o=r.filter(e=>"draft"===e.status),c=r.filter(e=>"archived"===e.status)),{navItems:[{id:1,icon:y.nko,label:a("deployed"),count:l.length||0,href:`/${t}/dashboard/deployed`,category:"project",status:"deployed"},{id:2,icon:j.fzI,label:a("draft"),count:o.length||0,href:`/${t}/dashboard/draft`,category:"project",status:"draft"},{id:3,icon:v.Wlj,label:a("archived"),count:c.length||0,href:`/${t}/dashboard/archived`,category:"project",status:"archived"},{id:4,icon:h.rjU,label:a("myLibrary"),count:0,href:`/${t}/library`,category:"library"},{id:5,icon:j.Blu,label:a("collections"),count:0,href:`/${t}/library/#`,category:"library"}],deployedProjects:l,draftStatusProjects:o,archivedProjects:c}};var S=a(6986);let A=({href:e,label:t,icon:a,count:n,status:i})=>{let l=(0,c.usePathname)(),d=(0,c.useRouter)(),{deployedProjects:u,draftStatusProjects:m,archivedProjects:p}=C(),[x,h]=(0,s.useState)(!1),g=(0,k.Ym)(),b=e=>{let t=(0,S.l)(e);d.push(`/project/${t}/overview`)},f=e=>{let t=(0,S.l)(e);return`/project/${t}/overview`},y=(()=>{switch(i){case"draft":return m;case"deployed":return u;case"archived":return p;default:return[]}})();return(0,r.jsxs)("li",{children:[(0,r.jsxs)(o(),{href:e,onClick:e=>{i&&(n>0?(e.preventDefault(),h(!x)):(e.preventDefault(),d.push(`/${g}/dashboard/${i}/not-available`)))},className:"flex items-center px-4 py-3 hover:bg-primary-500 hover:text-neutral-100 transition-all duration-300 ease-in-out rounded-md text-neutral-800",children:[(0,r.jsx)(a,{className:"mr-2",size:20}),(0,r.jsx)("span",{className:"text-sm font-bold",children:t}),(0,r.jsx)("span",{className:"ml-auto bg-neutral-200 text-neutral-700 rounded-full px-2 py-0.5 text-xs",children:n})]}),x&&y.length>0&&(0,r.jsx)("div",{className:"ml-6 mt-1 space-y-1",children:y.map(e=>(0,r.jsx)("div",{onClick:()=>b(e.id),className:`flex items-center px-4 py-2 text-sm hover:bg-primary-500 hover:text-neutral-100 transition-all duration-300 ease-in-out rounded-md cursor-pointer ${l===f(e.id)?"bg-primary-500 text-neutral-100":""}`,children:e.name},e.id))})]})},z=({isOpen:e,topOffset:t,onNewProject:a})=>{let s=(0,c.usePathname)(),n=(0,g.wA)(),{navItems:i,deployedProjects:l,draftStatusProjects:d,archivedProjects:u}=C(),p=(0,k.Ym)(),y=(0,m.c3)(),j=s.includes("/library")?"library":"project",v=i.filter(e=>e.category===j);return(0,r.jsxs)("aside",{style:{top:`${t}px`,height:`calc(100vh - ${t}px)`},className:`
        ${e?"translate-x-0":"-translate-x-full"}
        fixed left-0 h-full w-64 shadow-xl bg-neutral-100 z-30
        transform transition-all duration-300 ease-in-out
        laptop:translate-x-0 laptop:static laptop:flex flex
      `,children:[(0,r.jsx)("div",{className:"w-1/5 bg-neutral-200 p-4",children:(0,r.jsxs)("div",{className:"flex flex-col gap-5 items-center",children:[(0,r.jsx)(o(),{href:`/${p}/dashboard`,"aria-label":"Projects",className:`p-2  hover:bg-primary-500 hover:rounded-full transition-all duration-300 ease-in-out cursor-pointer
              ${"project"===j?"bg-primary-500 text-neutral-100 rounded-full":"text-neutral-700 hover:bg-primary-500 hover:text-neutral-100"}
            `,children:(0,r.jsx)(x._zY,{size:20,title:y("project")})}),(0,r.jsx)(o(),{href:`/${p}/library`,"aria-label":"Library",className:`p-2 hover:bg-primary-500 hover:rounded-full transition-all duration-300 ease-in-out cursor-pointer
              ${"library"===j?"bg-primary-500 text-neutral-100 rounded-full":"text-neutral-700 hover:bg-primary-500 hover:text-neutral-100"}
            `,children:(0,r.jsx)(h.rjU,{size:20,title:y("library")})})]})}),(0,r.jsxs)("div",{className:"w-4/5 bg-neutral-100 flex-1",children:[(0,r.jsx)("div",{className:"p-4",children:(0,r.jsx)("button",{onClick:()=>{"project"===j?a?a():n((0,b.Gl)()):n((0,f.yg)())},className:"btn-primary w-full",children:y("project"===j?"newProject":"newItem")})}),(0,r.jsx)("nav",{className:"mt-2",children:(0,r.jsx)("ul",{className:"space-y-1",children:v.map(e=>(0,r.jsx)(A,{href:e.href,label:e.label,icon:e.icon,count:e.count,status:e.status},e.id))})})]})]})};var E=a(38587),P=a(27605),_=a(68292),F=a(15566),$=a(32833),L=a(10022),R=a(11437),M=a(57800),q=a(12810),I=a(8693),O=a(54050),D=a(19150),T=a(40480);let H=async({name:e,description:t,sector:a,country:r})=>{let{data:s}=await q.A.post("/projects",{name:e,description:t,sector:a,country:r});return s},V=({isOpen:e,onClose:t,onBack:a}={})=>{let n=(0,g.d4)(e=>e.createProject.visible),i=(0,g.wA)(),l=(0,m.c3)(),{register:o,formState:{isSubmitting:d,errors:u,isSubmitted:p},handleSubmit:x,setValue:h}=(0,P.mN)(),[f,y]=(0,s.useState)(null),[j,v]=(0,s.useState)(null);(0,s.useEffect)(()=>{o("country",{required:l("pleaseSelectCountry")}),o("sector",{required:l("pleaseSelectSector")})},[o]),(0,s.useEffect)(()=>{h("country",f,{shouldValidate:p}),h("sector",j,{shouldValidate:p})},[h,f,j]);let[N,w]=(0,s.useState)(!1),k=()=>{w(!0),setTimeout(()=>{i((0,b.th)())},300)},C=(0,c.useRouter)(),S=(0,I.jE)(),A=(0,O.n)({mutationFn:H,onSuccess:()=>{S.invalidateQueries({queryKey:["projects"],exact:!1}),k(),C.push("/dashboard"),i((0,D.Ds)({message:l("projectCreatedSuccess"),type:"success"}))},onError:()=>{i((0,D.Ds)({message:l("projectCreateFailed"),type:"error"}))}}),z=async e=>{A.mutate({name:e.projectName,description:e.description,sector:e.sector,country:e.country})};return(0,r.jsxs)(E.A,{isOpen:n&&!N,onClose:k,className:"w-4/5 laptop:w-3/5 ",children:[(0,r.jsx)("h1",{className:"heading-text",children:l("createNewProject")}),(0,r.jsx)("form",{className:"flex flex-col gap-8 max-h-[600px] overflow-y-auto p-4",onSubmit:x(z),children:(0,r.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsxs)("label",{htmlFor:"project-name",className:"label-text",children:[(0,r.jsx)(L.A,{size:16})," ",l("projectName")]}),(0,r.jsx)("input",{...o("projectName",{required:l("projectNameRequired")}),id:"project-name",type:"text",className:"input-field",placeholder:l("enterProjectName")}),u.projectName&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:`${u.projectName.message}`})]}),(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsx)("label",{htmlFor:"description",className:"label-text",children:l("description")}),(0,r.jsx)("textarea",{id:"description",...o("description",{required:l("enterProjectDescription")}),className:"input-field resize-none",cols:4,placeholder:l("enterProjectDescription")}),u.description&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:`${u.description.message}`})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsxs)("label",{htmlFor:"country",className:"label-text",children:[(0,r.jsx)(R.A,{size:16}),l("country")]}),(0,r.jsx)(_.l,{id:"country",options:F,value:f||l("selectOption"),onChange:y}),u.country&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:`${u.country.message}`})]}),(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsxs)("label",{htmlFor:"sector",className:"label-text",children:[(0,r.jsx)(M.A,{size:16})," ",l("sector")]}),(0,r.jsx)(_.l,{id:"sector",options:Object.values($.b),value:j&&$.b[j]?$.b[j]:l("selectOption"),onChange:e=>{v((0,T.H)(e,$.b))}}),u.sector&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:`${u.sector.message}`})]})]}),(0,r.jsxs)("div",{className:"flex justify-end gap-3 mt-4",children:[a&&(0,r.jsx)("button",{type:"button",onClick:()=>{a&&a()},className:"btn-outline",children:l("back")}),(0,r.jsx)("button",{type:"submit",className:"btn-primary",children:d?(0,r.jsxs)("span",{className:"flex items-center gap-2",children:[l("creating"),(0,r.jsx)("div",{className:"size-4 animate-spin border-x border-neutral-100 rounded-full"})]}):l("createProject")})]})]})})]})};var B=a(89011),K=a(3984);let U=({handleClose:e})=>{let t=(0,g.wA)(),{register:a,formState:{isSubmitting:i,errors:l,isSubmitted:o},handleSubmit:c,setValue:d}=(0,P.mN)(),[u,p]=(0,s.useState)(null),[x,h]=(0,s.useState)(null);(0,s.useEffect)(()=>{a("country",{required:y("pleaseSelectCountry")}),a("sector",{required:y("pleaseSelectSector")})},[a]),(0,s.useEffect)(()=>{d("country",u,{shouldValidate:o}),d("sector",x,{shouldValidate:o})},[d,u,x]);let b=(0,I.jE)(),{user:f}=(0,n.A)(),y=(0,m.c3)(),j=(0,O.n)({mutationFn:K.Xu,onSuccess:()=>{b.invalidateQueries({queryKey:["templates",f?.id]}),e(),t((0,D.Ds)({type:"success",message:y("templateCreated")}))},onError:()=>{t((0,D.Ds)({type:"error",message:y("templateCreationFailed")}))}}),v=async e=>{let t={name:e.name,description:e.description,sector:e.sector,country:e.country};j.mutate(t)};return(0,r.jsxs)("form",{className:"flex flex-col gap-8",onSubmit:c(v),children:[(0,r.jsx)("h1",{className:"heading-text capitalize",children:y("createNewProjectTemplate")}),(0,r.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsxs)("label",{htmlFor:"project-name",className:"label-text",children:[(0,r.jsx)(L.A,{size:16})," ",y("templateName")]}),(0,r.jsx)("input",{...a("name",{required:y("templateNameRequired")}),id:"project-name",type:"text",className:"input-field",placeholder:y("enterProjectName")}),l.name&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:`${l.name.message}`})]}),(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsx)("label",{htmlFor:"description",className:"label-text",children:y("description")}),(0,r.jsx)("textarea",{id:"description",...a("description",{required:y("enterTemplateDescription")}),className:"input-field resize-none",cols:4,placeholder:y("enterProjectDescription")}),l.description&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:`${l.description.message}`})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsxs)("label",{htmlFor:"country",className:"label-text",children:[(0,r.jsx)(R.A,{size:16}),y("country")]}),(0,r.jsx)(_.l,{id:"country",options:F,value:u||y("selectOption"),onChange:p}),l.country&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:`${l.country.message}`})]}),(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsxs)("label",{htmlFor:"sector",className:"label-text",children:[(0,r.jsx)(M.A,{size:16})," ",y("sector")]}),(0,r.jsx)(_.l,{id:"sector",options:Object.values($.b),value:x&&$.b[x]?$.b[x]:y("selectOption"),onChange:e=>{h((0,T.H)(e,$.b))}}),l.sector&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:`${l.sector.message}`})]})]}),(0,r.jsx)("button",{type:"submit",className:"btn-primary self-end",children:i?(0,r.jsxs)("span",{className:"flex items-center gap-2",children:[y("creating"),(0,r.jsx)("div",{className:"size-4 animate-spin border-x border-neutral-100 rounded-full"})]}):y("createProject")})]})]})},G=()=>{let{visible:e,option:t}=(0,g.d4)(e=>e.createLibraryItem),a=(0,g.wA)(),[n,i]=(0,s.useState)(!1),l=()=>{i(!0),setTimeout(()=>{a((0,B.g7)())},300)};return(0,r.jsx)(E.A,{isOpen:e&&!n,onClose:l,className:"w-3/5",children:(()=>{switch(t){case"question-block":return(0,r.jsx)("div",{children:"Question Block"});case"template":return(0,r.jsx)(U,{handleClose:l});case"upload":return(0,r.jsx)("div",{children:"Upload"});case"collection":return(0,r.jsx)("div",{children:"Collection"});default:return null}})()})};var J=a(90471),Q=a(86429),Y=a(93617),Z=a(43782);let W=({selectedRowId:e,setSelectedRowId:t})=>{let a=(0,m.c3)();return[{id:"select",header:"",cell:({row:a})=>{let s=a.original.id;return(0,r.jsx)(Z.Sc,{className:"w-6 h-6 bg-neutral-100 rounded border border-neutral-400 data-[state=checked]:bg-neutral-100 data-[state=checked]:text-primary-500 data-[state=checked]:border-primary-500 cursor-pointer",checked:s===e,onCheckedChange:e=>t(e?s:null),"aria-label":"Select row"})}},{accessorKey:"name",header:a("name")},{id:"owner",accessorFn:e=>e.user?.name??"unknown",header:a("owner"),cell:({getValue:e})=>e()},{id:"questions",accessorFn:e=>e.libraryQuestions?.length.toString()??"0",header:a("questions"),cell:({getValue:e})=>e()},{accessorKey:"updatedAt",header:a("lastModified")}]},X=({showModal:e,closeModal:t,back:a,templateId:i})=>{let{register:l,formState:{isSubmitting:o,errors:d,isSubmitted:u},handleSubmit:m,setValue:p,reset:x}=(0,P.mN)(),h=(0,g.wA)(),b=(0,c.useRouter)(),[f,y]=(0,s.useState)(null),[j,v]=(0,s.useState)(null);(0,s.useEffect)(()=>{l("country",{required:"Please select a country"}),l("sector",{required:"Please select a sector"})},[l]),(0,s.useEffect)(()=>{p("country",f,{shouldValidate:u}),p("sector",j,{shouldValidate:u})},[p,f,j]);let{user:k,isLoading:C}=(0,n.A)(),{data:S,isLoading:A,isError:z}=(0,N.I)({queryKey:["templates",k?.id,i],queryFn:()=>(0,K.J2)({templateId:i}),enabled:!!k?.id});(0,s.useEffect)(()=>{S&&(x({projectName:S.name,description:S.description,sector:S.sector,country:S.country}),y(S.country),v(S.sector))},[S,x]);let q=(0,I.jE)(),H=(0,O.n)({mutationFn:w.c3,onSuccess:()=>{q.invalidateQueries({queryKey:["projects"],exact:!1}),t(),b.push("/dashboard"),h((0,D.Ds)({message:"Project has been created successfully.",type:"success"}))},onError:e=>{h((0,D.Ds)({message:"Failed to create project"+e.message,type:"error"}))}}),V=async e=>{let t={templateId:i,name:e.projectName,description:e.description,sector:e.sector,country:e.country};H.mutate(t)};return C||A||z?null:(0,r.jsx)(E.A,{isOpen:e,onClose:t,className:"w-3/6",children:(0,r.jsxs)("form",{className:"flex flex-col gap-8",onSubmit:m(V),children:[(0,r.jsx)("h1",{className:"heading-text",children:"Create a new project"}),(0,r.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsxs)("label",{htmlFor:"project-name",className:"label-text",children:[(0,r.jsx)(L.A,{size:16})," Project Name"]}),(0,r.jsx)("input",{...l("projectName",{required:"Project name is required."}),id:"project-name",type:"text",className:"input-field",placeholder:"Enter a project name"}),d.projectName&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:`${d.projectName.message}`})]}),(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsx)("label",{htmlFor:"description",className:"label-text",children:"Description"}),(0,r.jsx)("textarea",{id:"description",...l("description",{required:"Please enter the project description"}),className:"input-field resize-none",cols:4,placeholder:"Enter the project description"}),d.description&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:`${d.description.message}`})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsxs)("label",{htmlFor:"country",className:"label-text",children:[(0,r.jsx)(R.A,{size:16}),"Country"]}),(0,r.jsx)(_.l,{id:"country",options:F,value:f,onChange:y}),d.country&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:`${d.country.message}`})]}),(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsxs)("label",{htmlFor:"sector",className:"label-text",children:[(0,r.jsx)(M.A,{size:16})," Sector"]}),(0,r.jsx)(_.l,{id:"sector",options:Object.values($.b),value:j&&$.b[j]?$.b[j]:"Select an option",onChange:e=>{v((0,T.H)(e,$.b))}}),d.sector&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:`${d.sector.message}`})]})]}),(0,r.jsxs)("div",{className:"flex justify-end gap-3 mt-4",children:[(0,r.jsx)("button",{type:"button",onClick:a,className:"btn-outline",children:"Back"}),(0,r.jsx)("button",{type:"submit",className:"btn-primary",children:o?(0,r.jsxs)("span",{className:"flex items-center gap-2",children:["Creating"," ",(0,r.jsx)("div",{className:"size-4 animate-spin border-x border-neutral-100 rounded-full"})]}):"Create Project"})]})]})]})})},ee=({isOpen:e,back:t,onClose:a})=>{let{user:i}=(0,n.A)(),{data:l,isLoading:o,isError:c}=(0,N.I)({queryFn:K.QK,queryKey:["templates",i?.id],enabled:!!i?.id}),[d,u]=(0,s.useState)(null),[p,x]=(0,s.useState)(!1),[h,g]=(0,s.useState)(!1),b=(0,m.c3)(),f=W({selectedRowId:d,setSelectedRowId:u}),y=e=>{g(!0),setTimeout(()=>{x(!1),u(null),"close"===e&&a(),g(!1)},300)};return l?o?(0,r.jsx)(Q.A,{}):c?(0,r.jsx)("div",{children:b("error_loading_data")}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(E.A,{isOpen:e&&!p,onClose:()=>{u(null),a()},className:"bg-neutral-100 p-4 mobile:p-6 rounded-lg w-[95%] mobile:w-[85%] tablet:w-[75%] laptop:w-[60%] desktop:w-[50%] max-w-4xl mx-auto",children:(0,r.jsxs)("div",{className:"flex flex-col gap-6",children:[(0,r.jsx)("div",{className:"flex justify-between items-center",children:(0,r.jsx)("h2",{className:"text-xl font-semibold text-neutral-700",children:b("selectTemplate")})}),(0,r.jsx)(Y.x,{data:l,columns:f}),(0,r.jsxs)("div",{className:"flex justify-end gap-3 mt-4",children:[(0,r.jsx)("button",{onClick:()=>{u(null),t()},className:"btn-outline",children:b("back")}),(0,r.jsxs)("button",{type:"button",disabled:!d,className:"btn-primary",onClick:()=>{d&&x(!0)},children:[b("next"),(0,r.jsx)(J.ZK4,{})]})]})]})}),(p||h)&&null!==d&&(0,r.jsx)(X,{showModal:p&&!h,closeModal:()=>y("close"),back:()=>y("back"),templateId:d})]}):null},et=({isOpen:e,onClose:t})=>{let a=(0,g.wA)(),n=(0,m.c3)(),[i,l]=(0,s.useState)(!1),o=e=>{l(!1),"close"===e&&t()};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(E.A,{isOpen:e&&!i,onClose:t,className:"bg-neutral-100 p-4 mobile:p-6 rounded-lg w-[95%] mobile:w-[85%] tablet:w-[75%] laptop:w-[60%] desktop:w-[50%] max-w-3xl mx-auto",children:(0,r.jsxs)("div",{className:"flex flex-col gap-4 mobile:gap-6",children:[(0,r.jsx)("div",{className:"flex justify-between items-center",children:(0,r.jsx)("h2",{className:"text-lg mobile:text-xl font-semibold text-neutral-700",children:n("createProjectChooseSource")})}),(0,r.jsx)("p",{className:"text-sm mobile:text-base text-neutral-600",children:n("chooseOptionToContinue")}),(0,r.jsxs)("div",{className:"grid grid-cols-1 mobile:grid-cols-2 gap-3 mobile:gap-4",children:[(0,r.jsxs)("div",{onClick:()=>{t(),a((0,b.Gl)())},className:"bg-neutral-200 rounded-lg p-4 mobile:p-6 tablet:p-8 flex flex-col items-center justify-center gap-2 mobile:gap-3 cursor-pointer hover:bg-primary-500 hover:text-neutral-100 transition-all duration-300",children:[(0,r.jsx)(j.Yvo,{className:"w-5 h-5 mobile:w-6 mobile:h-6"}),(0,r.jsx)("span",{className:"text-sm mobile:text-base text-center",children:n("buildFromScratch")})]}),(0,r.jsxs)("div",{onClick:()=>{l(!0)},className:"bg-neutral-200 rounded-lg p-4 mobile:p-6 tablet:p-8 flex flex-col items-center justify-center gap-2 mobile:gap-3 cursor-pointer hover:bg-primary-500 hover:text-neutral-100 transition-all duration-300",children:[(0,r.jsx)(h.S1H,{className:"w-5 h-5 mobile:w-6 mobile:h-6"}),(0,r.jsx)("span",{className:"text-sm mobile:text-base text-center",children:n("useTemplate")})]})]})]})}),(0,r.jsx)(ee,{isOpen:i,onClose:()=>o("close"),back:()=>o("back")})]})};var ea=a(17019);let er=()=>{let e=(0,g.wA)(),t=(0,g.d4)(e=>e.createLibrary.visible),[a,n]=(0,s.useState)(!1),i=()=>{n(!0),setTimeout(()=>{e((0,f.l)())},300)},l=(0,c.useRouter)(),o=(0,m.c3)(),u=[{id:"question-block",title:o("questionBlock"),icon:x.Kt4,onClick:()=>{i(),l.push("/library/question-block/form-builder")}},{id:"template",title:o("templates"),icon:d.zFA,onClick:()=>{i(),e((0,B.dQ)("template"))}},{id:"upload",title:o("upload"),icon:ea.B88,onClick:()=>{i()}},{id:"collection",title:o("collections"),icon:v.M1W,onClick:()=>{i()}}];return(0,r.jsxs)(E.A,{isOpen:t&&!a,onClose:i,className:"p-6 rounded-md w-3/5",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-neutral-700 mb-4",children:o("createLibraryItem")}),(0,r.jsx)("div",{className:"grid grid-cols-2 gap-4",children:u.map(e=>(0,r.jsxs)("button",{onClick:e.onClick,className:"flex flex-col gap-2 items-center justify-center p-6 bg-neutral-200 rounded-md hover:bg-primary-500 hover:text-neutral-100 cursor-pointer transition-all duration-300",children:[(0,r.jsx)(e.icon,{size:24,className:""}),(0,r.jsx)("span",{className:"",children:e.title})]},e.id))})]})};function es({children:e}){let[t,a]=(0,s.useState)(!1),[n,i]=(0,s.useState)(0),[l,o]=(0,s.useState)(!1),c=(0,s.useRef)(null),d=()=>a(!t),u=(0,g.d4)(e=>e.createProject.visible),m=(0,g.d4)(e=>e.createLibrary.visible),x=(0,g.d4)(e=>e.createLibraryItem.visible);return(0,r.jsxs)("div",{className:"min-h-screen flex flex-col",children:[u&&(0,r.jsx)(V,{}),m&&(0,r.jsx)(er,{}),x&&(0,r.jsx)(G,{}),(0,r.jsx)(et,{isOpen:l,onClose:()=>o(!1)}),(0,r.jsx)(p,{toggleSidebar:d,isSidebarOpen:t,navbarRef:c}),(0,r.jsxs)("div",{className:"flex flex-1 overflow-hidden",children:[(0,r.jsx)(z,{isOpen:t,toggleSidebar:d,topOffset:n,onNewProject:()=>{o(!0)}}),(0,r.jsx)("main",{className:"flex-1 p-6 overflow-y-auto",style:{height:`calc(100vh - ${n}px)`},children:e})]})]})}},36039:(e,t,a)=>{Promise.resolve().then(a.bind(a,45196))},38587:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var r=a(60687),s=a(88920),n=a(57101),i=a(74699),l=a(11860);a(43210);let o=({children:e,className:t,isOpen:a,onClose:o,preventOutsideClick:c=!1})=>(0,r.jsx)(s.N,{children:a&&(0,r.jsx)(n.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-neutral-900/50 flex items-center justify-center z-40 p-4 overflow-y-auto",onClick:e=>{c||o()},children:(0,r.jsxs)(n.P.div,{initial:{scale:.6,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.6,opacity:0},transition:{duration:.3,ease:i.am},className:`relative section flex flex-col max-w-4xl max-h-[90vh] overflow-y-auto ${t}`,onClick:e=>e.stopPropagation(),children:[(0,r.jsx)(l.A,{onClick:o,className:"absolute self-end cursor-pointer text-neutral-700 hover:text-neutral-900 active:scale-90 transition-all duration-300"}),e]})})})},39390:(e,t,a)=>{"use strict";a.d(t,{J:()=>l});var r=a(60687),s=a(43210),n=a(78148),i=a(96241);let l=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(n.b,{ref:a,className:(0,i.cn)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",e),...t}));l.displayName=n.b.displayName},40347:(e,t,a)=>{"use strict";a.d(t,{C:()=>c,z:()=>o});var r=a(60687),s=a(43210),n=a(14555),i=a(65822),l=a(96241);let o=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(n.bL,{className:(0,l.cn)("grid gap-2",e),...t,ref:a}));o.displayName=n.bL.displayName;let c=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(n.q7,{ref:a,className:(0,l.cn)("aspect-square h-4 w-4 rounded-full border text-gray-900 shadow focus:outline-none focus-visible:ring-1 focus-visible:ring-gray-950 disabled:cursor-not-allowed disabled:opacity-50 dark:text-gray-50 dark:focus-visible:ring-gray-300",e),...t,children:(0,r.jsx)(n.C1,{className:"flex items-center justify-center",children:(0,r.jsx)(i.A,{className:"h-2.5 w-2.5 fill-current text-current"})})}));c.displayName=n.q7.displayName},40480:(e,t,a)=>{"use strict";a.d(t,{H:()=>r});let r=(e,t)=>{let a=Object.entries(t).find(([t,a])=>a===e);return a?a[0]:null}},43782:(e,t,a)=>{"use strict";a.d(t,{Sc:()=>x.S,dO:()=>p}),a(24934),a(68988),a(39390),a(15616);var r=a(60687),s=a(43210),n=a(97822),i=a(78272),l=a(3589),o=a(13964),c=a(96241);n.bL,n.YJ,n.WT,s.forwardRef(({className:e,children:t,...a},s)=>(0,r.jsxs)(n.l9,{ref:s,className:(0,c.cn)("flex h-10 w-full items-center justify-between rounded-md border border-gray-200 bg-neutral-100 px-3 py-2 text-sm shadow-sm ring-offset-neutral-100 placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1 dark:border-gray-700 dark:bg-gray-900 dark:ring-offset-gray-900 dark:placeholder:text-gray-500",e),...a,children:[t,(0,r.jsx)(n.In,{asChild:!0,children:(0,r.jsx)(i.A,{className:"h-4 w-4 opacity-50"})})]})).displayName=n.l9.displayName;let d=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(n.PP,{ref:a,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,r.jsx)(l.A,{className:"h-4 w-4"})}));d.displayName=n.PP.displayName;let u=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(n.wn,{ref:a,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,r.jsx)(i.A,{className:"h-4 w-4"})}));u.displayName=n.wn.displayName,s.forwardRef(({className:e,children:t,position:a="popper",...s},i)=>(0,r.jsx)(n.ZL,{children:(0,r.jsxs)(n.UC,{ref:i,className:(0,c.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border border-gray-200 bg-neutral-100 text-slate-700 shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 dark:border-gray-700 dark:bg-gray-900 dark:text-slate-200","popper"===a&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:a,...s,children:[(0,r.jsx)(d,{}),(0,r.jsx)(n.LM,{className:(0,c.cn)("p-1","popper"===a&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),(0,r.jsx)(u,{})]})})).displayName=n.UC.displayName,s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(n.JU,{ref:a,className:(0,c.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t})).displayName=n.JU.displayName,s.forwardRef(({className:e,children:t,...a},s)=>(0,r.jsxs)(n.q7,{ref:s,className:(0,c.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-gray-100 focus:text-gray-900 data-[disabled]:pointer-events-none data-[disabled]:opacity-50 dark:focus:bg-gray-800 dark:focus:text-gray-50",e),...a,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(n.VF,{children:(0,r.jsx)(o.A,{className:"h-4 w-4"})})}),(0,r.jsx)(n.p4,{children:t})]})).displayName=n.q7.displayName,s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(n.wv,{ref:a,className:(0,c.cn)("-mx-1 my-1 h-px bg-gray-200 dark:bg-gray-700",e),...t})).displayName=n.wv.displayName;var m=a(90270);let p=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(m.bL,{className:(0,c.cn)("peer inline-flex h-[24px] w-[44px] shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 focus-visible:ring-offset-neutral-100 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary-500 data-[state=unchecked]:bg-neutral-100 dark:focus-visible:ring-offset-gray-900",e),...t,ref:a,children:(0,r.jsx)(m.zi,{className:(0,c.cn)("pointer-events-none block h-5 w-5 rounded-full shadow-lg ring-0 transition-transform data-[state=checked]:bg-neutral-100 data-[state=unchecked]:bg-primary-500 data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));p.displayName=m.bL.displayName;var x=a(93437);a(40347)},52911:(e,t,a)=>{Promise.resolve().then(a.bind(a,80994))},55629:(e,t,a)=>{"use strict";a.d(t,{SQ:()=>c,_2:()=>d,hO:()=>u,rI:()=>l,ty:()=>o});var r=a(60687);a(43210);var s=a(26312),n=a(13964),i=a(96241);function l({...e}){return(0,r.jsx)(s.bL,{"data-slot":"dropdown-menu",...e})}function o({...e}){return(0,r.jsx)(s.l9,{"data-slot":"dropdown-menu-trigger",...e})}function c({className:e,sideOffset:t=4,...a}){return(0,r.jsx)(s.ZL,{children:(0,r.jsx)(s.UC,{"data-slot":"dropdown-menu-content",sideOffset:t,className:(0,i.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",e),...a})})}function d({className:e,inset:t,variant:a="default",...n}){return(0,r.jsx)(s.q7,{"data-slot":"dropdown-menu-item","data-inset":t,"data-variant":a,className:(0,i.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...n})}function u({className:e,children:t,checked:a,...l}){return(0,r.jsxs)(s.H_,{"data-slot":"dropdown-menu-checkbox-item",className:(0,i.cn)("focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),checked:a,...l,children:[(0,r.jsx)("span",{className:"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center",children:(0,r.jsx)(s.VF,{children:(0,r.jsx)(n.A,{className:"size-4"})})}),t]})}},64668:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var r=a(60687),s=a(8610),n=a(16189),i=a(85814),l=a.n(i);function o(){let e=(0,s.Ym)(),t=(0,n.usePathname)(),a=e=>{let a=t.replace(/^\/(en|ne)/,"");return`/${e}${a}`};return(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(l(),{href:a("en"),className:`px-3 py-1 rounded ${"en"===e?"bg-primary-600 text-white":"bg-gray-200"}`,children:"English"}),(0,r.jsx)(l(),{href:a("ne"),className:`px-3 py-1 rounded ${"ne"===e?"bg-primary-600 text-white":"bg-gray-200"}`,children:"नेपाली"})]})}},67985:(e,t,a)=>{Promise.resolve().then(a.bind(a,35523))},68292:(e,t,a)=>{"use strict";a.d(t,{l:()=>i});var r=a(60687),s=a(78272),n=a(43210);let i=({id:e,options:t,value:a,onChange:i})=>{let[l,o]=(0,n.useState)(!1),c=(0,n.useRef)(null),d=(0,n.useRef)([]),u=(0,n.useRef)(null);(0,n.useEffect)(()=>{let e=e=>{u.current&&!u.current.contains(e.target)&&o(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]);let m=e=>{if(!l)return;let a=e.key.toLowerCase();if(a.match(/[a-z]/)){let e=t.findIndex(e=>e.toLowerCase().startsWith(a));-1!==e&&d.current[e]&&d.current[e]?.scrollIntoView({behavior:"auto",block:"nearest"})}};return(0,n.useEffect)(()=>(document.addEventListener("keydown",m),()=>{document.removeEventListener("keydown",m)}),[l,t]),(0,r.jsxs)("div",{className:"relative",ref:u,children:[(0,r.jsxs)("button",{id:e,type:"button",className:"px-4 py-2 flex items-center justify-between rounded-md border border-neutral-400 focus:border-primary-500 duration-300 w-full text-left cursor-pointer",onClick:()=>{o(!l)},children:[(0,r.jsx)("span",{children:a||"Select an option"}),(0,r.jsx)(s.A,{})]}),l&&(0,r.jsx)("ul",{className:"absolute z-10 max-h-[180px] overflow-auto border border-neutral-400 rounded-md bg-neutral-100 w-full mt-1 flex flex-col",ref:c,children:t.map((e,t)=>(0,r.jsx)("li",{ref:e=>{d.current[t]=e},className:"cursor-pointer bg-neutral-100 hover:bg-neutral-200 px-4 py-2",onClick:()=>{i(e),o(!1)},children:e},t))})]})}},68988:(e,t,a)=>{"use strict";a.d(t,{p:()=>n});var r=a(60687);a(43210);var s=a(96241);function n({className:e,type:t,...a}){return(0,r.jsx)("input",{type:t,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-primary-500 focus-visible:ring-[1px]","focus-visible:outline-none","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...a})}},71845:(e,t,a)=>{"use strict";a.d(t,{D_:()=>u,Im:()=>c,Oo:()=>m,c3:()=>n,kf:()=>s,lj:()=>x,or:()=>o,pf:()=>d,vj:()=>i,wI:()=>p,xx:()=>l});var r=a(12810);let s=async({projectId:e})=>{let{data:t}=await r.A.get(`/projects/${e}`);return t.project},n=async e=>{let{data:t}=await r.A.post("/projects/from-template",e);return t},i=async()=>{try{let{data:e}=await r.A.get("/projects");return e.projects}catch(e){throw console.error("Error fetching projects:",e),e}},l=async e=>{let{data:t}=await r.A.delete(`/projects/delete/${e}`);return t},o=async e=>{try{let{data:t}=await r.A.delete("/projects/delete-multiple",{data:{projectIds:e}});return t}catch(e){throw console.error("Error deleting multiple projects:",e),e}},c=async e=>{try{let{data:t}=await r.A.patch(`/projects/change-status/${e}`,{status:"archived"});return t}catch(e){throw console.error("Error archiving project:",e),e}},d=async(e,t=!1)=>{try{let{data:t}=await r.A.patch(`/projects/change-status/${e}`,{status:"deployed"});return t}catch(e){throw console.error("Error deploying project:",e),e}},u=async e=>{try{let{data:t}=await r.A.patch("/projects/update-many-status",{projectIds:e,status:"archived"});return t}catch(e){throw console.error("Error archiving multiple projects:",e),e}},m=async e=>{try{let{data:t}=await r.A.post("/users/check-email",{email:e});return t}catch(e){throw Error("object"==typeof e.response?.data?.message?JSON.stringify(e.response?.data?.message):e.response?.data?.message||e.message||"Failed to check user")}},p=async({projectId:e,email:t,permissions:a})=>{try{let s=await m(t);if(!s||!s.success)throw Error(s?.message||"User not found");let{data:n}=await r.A.post("/project-users",{userId:s.user.id,projectId:e,permission:a});return n}catch(e){throw console.error("Error adding user to project:",e),Error("object"==typeof e.response?.data?.message?JSON.stringify(e.response?.data?.message):e.response?.data?.message||e.message||"Failed to add user")}},x=async e=>{try{let{data:t}=await r.A.post("/answers/multiple",e);return t}catch(e){throw console.error("Error creating answer submission:",e),e}}},72121:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>c,generateStaticParams:()=>o});var r=a(37413),s=a(60958),n=a(39916),i=a(81015);let l=["en","ne"];function o(){return l.map(e=>({locale:e}))}async function c({children:e,params:t}){let{locale:a}=await t;l.includes(a)||(0,n.notFound)();let o=await (0,i.V)(a);return(0,r.jsx)(s.A,{locale:a,messages:o,children:e})}},76565:(e,t,a)=>{var r={"./en.json":[87368,7368],"./ne.json":[3018,3018]};function s(e){if(!a.o(r,e))return Promise.resolve().then(()=>{var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t});var t=r[e],s=t[0];return a.e(t[1]).then(()=>a.t(s,19))}s.keys=()=>Object.keys(r),s.id=76565,e.exports=s},77713:(e,t,a)=>{Promise.resolve().then(a.bind(a,84606))},81015:(e,t,a)=>{"use strict";a.d(t,{A:()=>i,V:()=>n});var r=a(35471);let s=["en","ne"];async function n(e){s.includes(e)||(console.warn(`Unsupported locale: ${e}, falling back to 'en'`),e="en");try{let t=(await a(76565)(`./${e}.json`)).default;if(!t||"object"!=typeof t)throw Error(`Invalid messages format for locale: ${e}`);return t}catch(t){if(console.error(`Failed to load messages for locale: ${e}`,t),"en"!==e)try{return console.log("Falling back to English messages"),(await a.e(7368).then(a.t.bind(a,87368,19))).default}catch(e){console.error("Failed to load fallback English messages",e)}return{}}}let i=(0,r.A)(async({locale:e})=>{let t=e?.toString()||"en";return{locale:t,messages:await n(t),timeZone:"Asia/Kathmandu",formats:{dateTime:{short:{day:"numeric",month:"short",year:"numeric"},medium:{day:"numeric",month:"long",year:"numeric"},long:{weekday:"long",day:"numeric",month:"long",year:"numeric"}},number:{currency:{style:"currency",currency:"NPR"}}}}})},84606:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});let r=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Kobo-project\\\\data-analysis-tool-frontend\\\\app\\\\[locale]\\\\(main)\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\layout.tsx","default")},86429:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var r=a(60687);a(43210);let s=()=>(0,r.jsx)("div",{className:"w-full flex items-center justify-center",children:(0,r.jsx)("div",{className:"size-8 rounded-full border-x-2 border-primary-500 animate-spin my-16"})})},93437:(e,t,a)=>{"use strict";a.d(t,{S:()=>l});var r=a(60687);a(43210);var s=a(40211),n=a(13964),i=a(96241);function l({className:e,...t}){return(0,r.jsx)(s.bL,{"data-slot":"checkbox",className:(0,i.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:(0,r.jsx)(s.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,r.jsx)(n.A,{className:"size-3.5"})})})}},93617:(e,t,a)=>{"use strict";a.d(t,{x:()=>p});var r=a(60687),s=a(43210),n=a.n(s),i=a(56090),l=a(93772),o=a(96752),c=a(55629),d=a(24934),u=a(69587),m=a(77618);let p=({columns:e,data:t,globalFilter:a,setGlobalFilter:s,onTableInit:p,columnVisibility:x,setColumnVisibility:h,onRowSelectionChange:g,rowSelection:b,onRowClick:f})=>{let[y,j]=n().useState({pageIndex:0,pageSize:8}),[v,N]=n().useState([]),[w,k]=n().useState([]),C=(0,m.c3)(),[S,A]=n().useState({}),[z,E]=n().useState({}),P=void 0!==b?b:z,_=(0,i.N4)({data:t,columns:e,onPaginationChange:j,onColumnFiltersChange:N,onGlobalFilterChange:s,onColumnVisibilityChange:h??A,onRowSelectionChange:e=>{let t="function"==typeof e?e(P):e;void 0===b&&E(t),g&&g(t)},onSortingChange:k,getCoreRowModel:(0,l.HT)(),getFilteredRowModel:(0,l.hM)(),getPaginationRowModel:(0,l.kW)(),getSortedRowModel:(0,l.h5)(),enableRowSelection:!0,enableSorting:!0,enableSortingRemoval:!0,state:{pagination:y,columnFilters:v,globalFilter:a,columnVisibility:x??S,rowSelection:P,sorting:w}});return n().useEffect(()=>{p&&p(_)},[p,_]),n().useEffect(()=>{void 0!==b&&_.setRowSelection(b)},[b,_]),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"rounded-md border border-neutral-400 overflow-hidden",children:(0,r.jsxs)(o.XI,{className:"min-w-full",children:[(0,r.jsx)(o.A0,{className:"h-20",children:_.getHeaderGroups().map(e=>(0,r.jsx)(o.Hj,{className:"text-sm border-neutral-400",children:e.headers.map(t=>(0,r.jsxs)(o.nd,{className:`py-1 px-6 text-left bg-primary-500 text-neutral-100 font-semibold ${0===t.index?"w-12 py-3 px-6":""}`,style:{cursor:t.column.getCanSort()?"pointer":"default"},children:[(0,r.jsx)("div",{onClick:t.column.getToggleSortingHandler(),children:(0,r.jsx)("div",{children:t.isPlaceholder?null:(0,i.Kv)(t.column.columnDef.header,t.getContext())})}),"validation"===t.column.id?(0,r.jsxs)(c.rI,{children:[(0,r.jsx)(c.ty,{asChild:!0,children:(0,r.jsxs)(d.$,{variant:"outline",className:"h-8 my-1 text-neutral-700 cursor-pointer",children:[C("filter"),(0,r.jsx)(u.Vr3,{})]})}),(0,r.jsxs)(c.SQ,{className:"bg-neutral-100 border border-neutral-200 shadow-md cursor-pointer",children:[(0,r.jsx)(c._2,{className:"cursor-pointer hover:bg-neutral-300",onClick:()=>t.column.setFilterValue("Valid"),children:C("valid")}),(0,r.jsx)(c._2,{className:"cursor-pointer hover:bg-neutral-300",onClick:()=>t.column.setFilterValue("Not Valid"),children:C("notValid")}),(0,r.jsx)(c._2,{className:"cursor-pointer hover:bg-neutral-300",onClick:()=>t.column.setFilterValue(""),children:C("clearFilter")})]})]}):t.column.getCanFilter()&&(0,r.jsx)("input",{placeholder:C("search"),value:t.column.getFilterValue()||"",onChange:e=>t.column.setFilterValue(e.target.value),className:"input-field placeholder:font-semibold max-w-48 text-sm my-1 px-2 py-1 bg-neutral-100 text-neutral-700  border-none rounded-md"})]},`${e.id}_${t.id}_${t.index}`))},e.id))}),(0,r.jsx)(o.BF,{children:_.getPaginationRowModel().rows.length?_.getPaginationRowModel().rows.map(e=>(0,r.jsx)(o.Hj,{"data-state":e.getIsSelected()&&"selected",className:"hover:bg-neutral-50 text-sm border-neutral-400",onClick:()=>f?.(e.original),children:e.getVisibleCells().map((t,a)=>(0,r.jsx)(o.nA,{className:`py-4 px-6 max-w-48  ${0===a?"py-3 px-6":""} text-neutral-700 `,children:(0,i.Kv)(t.column.columnDef.cell,t.getContext())},`${e.id}_${t.id}_${a}`))},e.id)):(0,r.jsx)(o.Hj,{children:(0,r.jsx)(o.nA,{colSpan:e.length,className:"h-24 text-center",children:C("noResults")})})})]})}),(0,r.jsxs)("div",{className:"flex items-center justify-end space-x-2 py-4",children:[(0,r.jsxs)("div",{className:"flex-1 text-sm text-muted-foreground",children:[_.getFilteredSelectedRowModel().rows.length," of"," ",_.getFilteredRowModel().rows.length," ",C("rowSelected")]}),t.length>y.pageSize&&(0,r.jsxs)("div",{className:"flex items-center justify-end space-x-2 py-4",children:[(0,r.jsx)("button",{className:"btn-primary",onClick:()=>_.previousPage(),disabled:!_.getCanPreviousPage(),children:C("previous")}),(0,r.jsx)("button",{className:"btn-primary",onClick:()=>_.nextPage(),disabled:!_.getCanNextPage(),children:C("next")})]})]})]})}},96241:(e,t,a)=>{"use strict";a.d(t,{Y:()=>i,cn:()=>n});var r=a(49384),s=a(82348);function n(...e){return(0,s.QP)((0,r.$)(e))}function i(e,t="short"){if(!e)return"";try{let a="string"==typeof e?new Date(e):e;if(isNaN(a.getTime()))return"";switch(t){case"short":return a.toLocaleDateString(void 0,{year:"numeric",month:"short",day:"numeric"});case"long":return a.toLocaleDateString(void 0,{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"});case"full":return a.toLocaleDateString(void 0,{year:"numeric",month:"long",day:"numeric",weekday:"long",hour:"2-digit",minute:"2-digit",second:"2-digit"});default:return a.toLocaleDateString()}}catch(t){return console.error("Error formatting date:",t),String(e)}}},96752:(e,t,a)=>{"use strict";a.d(t,{A0:()=>i,BF:()=>l,Hj:()=>o,XI:()=>n,nA:()=>d,nd:()=>c});var r=a(60687);a(43210);var s=a(96241);function n({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,r.jsx)("table",{"data-slot":"table",className:(0,s.cn)("w-full caption-bottom text-sm",e),...t})})}function i({className:e,...t}){return(0,r.jsx)("thead",{"data-slot":"table-header",className:(0,s.cn)("[&_tr]:border-b",e),...t})}function l({className:e,...t}){return(0,r.jsx)("tbody",{"data-slot":"table-body",className:(0,s.cn)("[&_tr:last-child]:border-0",e),...t})}function o({className:e,...t}){return(0,r.jsx)("tr",{"data-slot":"table-row",className:(0,s.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...t})}function c({className:e,...t}){return(0,r.jsx)("th",{"data-slot":"table-head",className:(0,s.cn)("text-foreground h-10 px-2 text-left align-middle font-medium neutral-100space-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}function d({className:e,...t}){return(0,r.jsx)("td",{"data-slot":"table-cell",className:(0,s.cn)("p-2 align-middle neutral-100space-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}}};