{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/modals/ConfirmationModal.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport Modal from \"./Modal\";\r\n\r\nconst ConfirmationModal = ({\r\n  showModal,\r\n  onClose,\r\n  onConfirm,\r\n  title,\r\n  description,\r\n  confirmButtonText,\r\n  cancelButtonText,\r\n  confirmButtonClass,\r\n  children,\r\n}: {\r\n  showModal: boolean;\r\n  onClose: () => void;\r\n  onConfirm: () => void;\r\n  title: string;\r\n  description: React.ReactNode; // Accept ReactNode for flexible content\r\n  confirmButtonText: string;\r\n  cancelButtonText?: string;\r\n  confirmButtonClass?: string;\r\n  children?: React.ReactNode; // Additional content like warnings or icons\r\n}) => {\r\n  return (\r\n    <Modal\r\n      isOpen={showModal}\r\n      onClose={onClose}\r\n      className=\"p-6 rounded-md max-w-xl\"\r\n    >\r\n      <h2 className=\"text-lg font-semibold text-neutral-700\">{title}</h2>\r\n      <div className=\"text-neutral-700 mt-2\">{description}</div>\r\n      {children && <div className=\"mt-6 space-y-4\">{children}</div>}\r\n      <div className=\"flex justify-end gap-4 mt-6\">\r\n        <button className=\"btn-outline\" onClick={onClose} type=\"button\">\r\n          {cancelButtonText || \"Cancel\"}\r\n        </button>\r\n        <button\r\n          className={`font-medium rounded-md shadow-md text-neutral-100 flex items-center justify-center gap-2 px-4 py-2 active:scale-95 transition-all duration-300 ${confirmButtonClass}`}\r\n          onClick={onConfirm}\r\n          type=\"button\"\r\n        >\r\n          {confirmButtonText}\r\n        </button>\r\n      </div>\r\n    </Modal>\r\n  );\r\n};\r\n\r\nexport { ConfirmationModal };\r\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKA,MAAM,oBAAoB,CAAC,EACzB,SAAS,EACT,OAAO,EACP,SAAS,EACT,KAAK,EACL,WAAW,EACX,iBAAiB,EACjB,gBAAgB,EAChB,kBAAkB,EAClB,QAAQ,EAWT;IACC,qBACE,6LAAC,iIAAA,CAAA,UAAK;QACJ,QAAQ;QACR,SAAS;QACT,WAAU;;0BAEV,6LAAC;gBAAG,WAAU;0BAA0C;;;;;;0BACxD,6LAAC;gBAAI,WAAU;0BAAyB;;;;;;YACvC,0BAAY,6LAAC;gBAAI,WAAU;0BAAkB;;;;;;0BAC9C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAO,WAAU;wBAAc,SAAS;wBAAS,MAAK;kCACpD,oBAAoB;;;;;;kCAEvB,6LAAC;wBACC,WAAW,CAAC,+IAA+I,EAAE,oBAAoB;wBACjL,SAAS;wBACT,MAAK;kCAEJ;;;;;;;;;;;;;;;;;;AAKX;KA5CM", "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/tables/columns/TemplateListColumns.tsx"], "sourcesContent": ["import { Checkbox } from \"@/components/ui\";\r\nimport { encode } from \"@/lib/encodeDecode\";\r\nimport { Template } from \"@/types\";\r\nimport { ColumnDef } from \"@tanstack/react-table\";\r\nimport Link from \"next/link\";\r\n\r\nconst TemplateListColumns: ColumnDef<Template>[] = [\r\n  {\r\n    id: \"select\",\r\n    header: ({ table }) => (\r\n      <Checkbox\r\n        className=\"w-6 h-6 data-[state=checked]:bg-primary-500 data-[state=checked]:text-neutral-500 rounded border border-neutral-100 data-[state=checked]:border-neutral-100 cursor-pointer\"\r\n        checked={\r\n          table.getIsAllPageRowsSelected() ||\r\n          (table.getIsSomePageRowsSelected() && \"indeterminate\")\r\n        }\r\n        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}\r\n        aria-label=\"Select all\"\r\n      />\r\n    ),\r\n    cell: ({ row }) => (\r\n      <Checkbox\r\n        className=\"w-6 h-6 bg-neutral-100 rounded border border-neutral-400 data-[state=checked]:bg-neutral-100 data-[state=checked]:text-primary-500 data-[state=checked]:border-primary-500 cursor-pointer\"\r\n        checked={row.getIsSelected()}\r\n        onCheckedChange={(value) => row.toggleSelected(!!value)}\r\n        aria-label=\"Select row\"\r\n      />\r\n    ),\r\n    enableHiding: false,\r\n    enableSorting: false,\r\n  },\r\n  {\r\n    accessorKey: \"name\",\r\n    header: \"Template Name\",\r\n    cell: ({ row }) => {\r\n      const templateId = row.original.id;\r\n      const encryptedTemplateId = encode(templateId);\r\n      return (\r\n        <Link\r\n          href={`library/template/${encryptedTemplateId}/form-builder`}\r\n          className=\"cursor-pointer text-primary-500 hover:text-primary-600 hover:underline transition-all duration-300\"\r\n        >\r\n          {row.getValue(\"name\")}\r\n        </Link>\r\n      );\r\n    },\r\n  },\r\n  {\r\n    accessorKey: \"description\",\r\n    header: \"Description\",\r\n  },\r\n  {\r\n    id: \"owner\",\r\n    accessorFn: (row) => row.user?.name ?? \"unknown\", // So filtering works\r\n    header: \"Owner\",\r\n    cell: ({ getValue }) => getValue(), // You could use `getValue` directly\r\n  },\r\n  {\r\n    accessorKey: \"sector\",\r\n    header: \"Sector\",\r\n  },\r\n  {\r\n    accessorKey: \"country\",\r\n    header: \"Country\",\r\n  },\r\n  {\r\n    accessorKey: \"updatedAt\",\r\n    header: \"Last Modified\",\r\n  },\r\n];\r\n\r\nexport { TemplateListColumns };\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AAGA;;;;;AAEA,MAAM,sBAA6C;IACjD;QACE,IAAI;QACJ,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,gIAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,SACE,MAAM,wBAAwB,MAC7B,MAAM,yBAAyB,MAAM;gBAExC,iBAAiB,CAAC,QAAU,MAAM,yBAAyB,CAAC,CAAC,CAAC;gBAC9D,cAAW;;;;;;QAGf,MAAM,CAAC,EAAE,GAAG,EAAE,iBACZ,6LAAC,gIAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,SAAS,IAAI,aAAa;gBAC1B,iBAAiB,CAAC,QAAU,IAAI,cAAc,CAAC,CAAC,CAAC;gBACjD,cAAW;;;;;;QAGf,cAAc;QACd,eAAe;IACjB;IACA;QACE,aAAa;QACb,QAAQ;QACR,MAAM,CAAC,EAAE,GAAG,EAAE;YACZ,MAAM,aAAa,IAAI,QAAQ,CAAC,EAAE;YAClC,MAAM,sBAAsB,CAAA,GAAA,sHAAA,CAAA,SAAM,AAAD,EAAE;YACnC,qBACE,6LAAC,+JAAA,CAAA,UAAI;gBACH,MAAM,CAAC,iBAAiB,EAAE,oBAAoB,aAAa,CAAC;gBAC5D,WAAU;0BAET,IAAI,QAAQ,CAAC;;;;;;QAGpB;IACF;IACA;QACE,aAAa;QACb,QAAQ;IACV;IACA;QACE,IAAI;QACJ,YAAY,CAAC,MAAQ,IAAI,IAAI,EAAE,QAAQ;QACvC,QAAQ;QACR,MAAM,CAAC,EAAE,QAAQ,EAAE,GAAK;IAC1B;IACA;QACE,aAAa;QACb,QAAQ;IACV;IACA;QACE,aAAa;QACb,QAAQ;IACV;IACA;QACE,aAAa;QACb,QAAQ;IACV;CACD", "debugId": null}}, {"offset": {"line": 182, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/library/TemplateList.tsx"], "sourcesContent": ["import { useAuth } from \"@/hooks/useAuth\";\r\nimport { deleteTemplates, fetchTemplates } from \"@/lib/api/templates\";\r\nimport { Template } from \"@/types\";\r\nimport { useMutation, useQuery, useQueryClient } from \"@tanstack/react-query\";\r\nimport { Table, VisibilityState } from \"@tanstack/react-table\";\r\nimport React, { useEffect, useState } from \"react\";\r\nimport Spinner from \"../general/Spinner\";\r\nimport { ConfirmationModal } from \"../modals/ConfirmationModal\";\r\nimport { GeneralTable } from \"../tables/GeneralTable\";\r\nimport { ChevronDown, ChevronUp, Trash } from \"lucide-react\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuContent,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport { DropdownMenuTrigger } from \"@radix-ui/react-dropdown-menu\";\r\nimport { TemplateListColumns } from \"../tables/columns/TemplateListColumns\";\r\nimport { useTranslations } from \"next-intl\";\r\n\r\nconst STORAGE_KEY = \"data-table-column-visibility\";\r\n\r\nconst TemplateList = () => {\r\n  const { user } = useAuth();\r\n  const t = useTranslations();\r\n\r\n  const [showConfirmationModal, setShowConfirmationModal] = useState(false);\r\n\r\n  const {\r\n    data: templateData,\r\n    isLoading: templateLoading,\r\n    isError: templateError,\r\n  } = useQuery<Template[]>({\r\n    queryKey: [\"templates\", user?.id],\r\n    queryFn: fetchTemplates,\r\n    enabled: !!user?.id,\r\n  });\r\n\r\n  const [templateFilter, setTemplateFilter] = useState<string>(\"\");\r\n  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});\r\n  const [tableInstance, setTableInstance] = useState<Table<Template> | null>(\r\n    null\r\n  );\r\n  const [isDropdownOpen, setIsDropdownOpen] = useState<boolean>(false);\r\n  const [templateRowSelection, setTemplateRowSelection] = useState({});\r\n\r\n  const queryClient = useQueryClient();\r\n\r\n  // Load column visibility from localStorage\r\n  useEffect(() => {\r\n    const savedVisibility = localStorage.getItem(STORAGE_KEY);\r\n    if (savedVisibility) {\r\n      try {\r\n        setColumnVisibility(JSON.parse(savedVisibility));\r\n      } catch (error) {\r\n        console.error(\"Failed to parse saved column visibility\", error);\r\n      }\r\n    }\r\n  }, []);\r\n\r\n  // Save column visibility to localStorage\r\n  useEffect(() => {\r\n    if (Object.keys(columnVisibility).length > 0) {\r\n      localStorage.setItem(STORAGE_KEY, JSON.stringify(columnVisibility));\r\n    }\r\n  }, [columnVisibility]);\r\n\r\n  const deleteTemplateMutation = useMutation({\r\n    mutationFn: deleteTemplates,\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"templates\", user?.id] });\r\n      setShowConfirmationModal(false);\r\n    },\r\n    onError: () => {},\r\n  });\r\n\r\n  const handleDeleteClick = () => {\r\n    if (selectedTemplateIds.length === 0) {\r\n      return;\r\n    }\r\n    setShowConfirmationModal(true);\r\n  };\r\n\r\n  const selectedTemplateIds = React.useMemo(\r\n    () =>\r\n      Object.keys(templateRowSelection)\r\n        .filter(\r\n          (id) => templateRowSelection[id as keyof typeof templateRowSelection]\r\n        )\r\n        .map((id) => parseInt(id, 10)), // Convert string IDs to numbers\r\n    [templateRowSelection]\r\n  );\r\n\r\n  if (templateLoading || !templateData) {\r\n    return <Spinner />;\r\n  }\r\n\r\n  if (templateError) {\r\n    return <p className=\"text-red-500\">{t('error_loading_data')}</p>;\r\n  }\r\n\r\n  return (\r\n    <div>\r\n      <ConfirmationModal\r\n        showModal={showConfirmationModal}\r\n        onClose={() => setShowConfirmationModal(false)}\r\n        title={t('deleteTemplates')}\r\n        description={t('confirmDeleteTemplates')}\r\n        confirmButtonText={t('delete')}\r\n        confirmButtonClass=\"btn-danger\"\r\n        onConfirm={() =>\r\n          deleteTemplateMutation.mutate({ templateIds: selectedTemplateIds })\r\n        }\r\n      />\r\n      <div className=\"flex flex-col gap-4\">\r\n        <div className=\"flex items-center gap-4\">\r\n          <h1 className=\"sub-heading-text\">{t('templates')}</h1>\r\n          <div>\r\n            <input\r\n              type=\"text\"\r\n              value={templateFilter}\r\n              onChange={(e) => setTemplateFilter(e.target.value)}\r\n              placeholder={t('searchTemplates')}\r\n              className=\"input-field text-sm\"\r\n            />\r\n          </div>\r\n          {tableInstance && (\r\n            <DropdownMenu\r\n              open={isDropdownOpen}\r\n              onOpenChange={(open) => setIsDropdownOpen(open)}\r\n            >\r\n              <DropdownMenuTrigger asChild>\r\n                <button className=\"btn-outline text-sm text-neutral-700 border-neutral-400 font-normal\">\r\n                  {t('showHideColumns')}\r\n                  {isDropdownOpen ? (\r\n                    <ChevronDown size={16} />\r\n                  ) : (\r\n                    <ChevronUp size={16} />\r\n                  )}\r\n                </button>\r\n              </DropdownMenuTrigger>\r\n\r\n              <DropdownMenuContent\r\n                align=\"start\"\r\n                className=\"border bg-neutral-100 border-neutral-200 shadow-md px-2\"\r\n              >\r\n                {tableInstance\r\n                  .getAllColumns()\r\n                  .filter((column) => column.getCanHide())\r\n                  .map((column) => (\r\n                    <DropdownMenuCheckboxItem\r\n                      key={column.id}\r\n                      className=\"capitalize cursor-pointer hover:bg-neutral-200\"\r\n                      checked={columnVisibility[column.id] ?? true}\r\n                      onCheckedChange={(value) =>\r\n                        setColumnVisibility((prev) => ({\r\n                          ...prev,\r\n                          [column.id]: value,\r\n                        }))\r\n                      }\r\n                    >\r\n                      {column.id}\r\n                    </DropdownMenuCheckboxItem>\r\n                  ))}\r\n              </DropdownMenuContent>\r\n            </DropdownMenu>\r\n          )}\r\n          <button\r\n            type=\"button\"\r\n            className={`ml-auto btn-danger text-sm`}\r\n            onClick={handleDeleteClick}\r\n            disabled={selectedTemplateIds.length === 0}\r\n          >\r\n            {t('delete')} <Trash size={16} />\r\n          </button>\r\n        </div>\r\n        {templateData.length > 0 ? (\r\n          <GeneralTable\r\n            columns={TemplateListColumns}\r\n            data={templateData}\r\n            globalFilter={templateFilter}\r\n            setGlobalFilter={setTemplateFilter}\r\n            onTableInit={(instance) => setTableInstance(instance)}\r\n            onRowSelectionChange={setTemplateRowSelection}\r\n            columnVisibility={columnVisibility}\r\n            setColumnVisibility={setColumnVisibility}\r\n          />\r\n        ) : (\r\n          <div className=\"text-center py-16 space-y-4\">\r\n            <p className=\"text-lg\">\r\n              {t('getStarted')}\r\n            </p>\r\n            <p className=\"text-sm text-gray-500\">\r\n             {t('advancedUsers')}\r\n            </p>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport { TemplateList };\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAEA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAKA;AACA;AACA;;;;;;;;;;;;;;;AAEA,MAAM,cAAc;AAEpB,MAAM,eAAe;;IACnB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,oHAAA,CAAA,UAAO,AAAD;IACvB,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD;IAExB,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnE,MAAM,EACJ,MAAM,YAAY,EAClB,WAAW,eAAe,EAC1B,SAAS,aAAa,EACvB,GAAG,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAc;QACvB,UAAU;YAAC;YAAa,MAAM;SAAG;QACjC,SAAS,0HAAA,CAAA,iBAAc;QACvB,SAAS,CAAC,CAAC,MAAM;IACnB;IAEA,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB,CAAC;IAC3E,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAC/C;IAEF,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAC9D,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAElE,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,2CAA2C;IAC3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM,kBAAkB,aAAa,OAAO,CAAC;YAC7C,IAAI,iBAAiB;gBACnB,IAAI;oBACF,oBAAoB,KAAK,KAAK,CAAC;gBACjC,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,2CAA2C;gBAC3D;YACF;QACF;iCAAG,EAAE;IAEL,yCAAyC;IACzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,OAAO,IAAI,CAAC,kBAAkB,MAAM,GAAG,GAAG;gBAC5C,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;YACnD;QACF;iCAAG;QAAC;KAAiB;IAErB,MAAM,yBAAyB,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACzC,YAAY,0HAAA,CAAA,kBAAe;QAC3B,SAAS;gEAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAa,MAAM;qBAAG;gBAAC;gBAClE,yBAAyB;YAC3B;;QACA,OAAO;gEAAE,KAAO;;IAClB;IAEA,MAAM,oBAAoB;QACxB,IAAI,oBAAoB,MAAM,KAAK,GAAG;YACpC;QACF;QACA,yBAAyB;IAC3B;IAEA,MAAM,sBAAsB,6JAAA,CAAA,UAAK,CAAC,OAAO;qDACvC,IACE,OAAO,IAAI,CAAC,sBACT,MAAM;6DACL,CAAC,KAAO,oBAAoB,CAAC,GAAwC;4DAEtE,GAAG;6DAAC,CAAC,KAAO,SAAS,IAAI;;oDAC9B;QAAC;KAAqB;IAGxB,IAAI,mBAAmB,CAAC,cAAc;QACpC,qBAAO,6LAAC,oIAAA,CAAA,UAAO;;;;;IACjB;IAEA,IAAI,eAAe;QACjB,qBAAO,6LAAC;YAAE,WAAU;sBAAgB,EAAE;;;;;;IACxC;IAEA,qBACE,6LAAC;;0BACC,6LAAC,6IAAA,CAAA,oBAAiB;gBAChB,WAAW;gBACX,SAAS,IAAM,yBAAyB;gBACxC,OAAO,EAAE;gBACT,aAAa,EAAE;gBACf,mBAAmB,EAAE;gBACrB,oBAAmB;gBACnB,WAAW,IACT,uBAAuB,MAAM,CAAC;wBAAE,aAAa;oBAAoB;;;;;;0BAGrE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAoB,EAAE;;;;;;0CACpC,6LAAC;0CACC,cAAA,6LAAC;oCACC,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;oCACjD,aAAa,EAAE;oCACf,WAAU;;;;;;;;;;;4BAGb,+BACC,6LAAC,wIAAA,CAAA,eAAY;gCACX,MAAM;gCACN,cAAc,CAAC,OAAS,kBAAkB;;kDAE1C,6LAAC,+KAAA,CAAA,sBAAmB;wCAAC,OAAO;kDAC1B,cAAA,6LAAC;4CAAO,WAAU;;gDACf,EAAE;gDACF,+BACC,6LAAC,uNAAA,CAAA,cAAW;oDAAC,MAAM;;;;;yEAEnB,6LAAC,mNAAA,CAAA,YAAS;oDAAC,MAAM;;;;;;;;;;;;;;;;;kDAKvB,6LAAC,wIAAA,CAAA,sBAAmB;wCAClB,OAAM;wCACN,WAAU;kDAET,cACE,aAAa,GACb,MAAM,CAAC,CAAC,SAAW,OAAO,UAAU,IACpC,GAAG,CAAC,CAAC,uBACJ,6LAAC,wIAAA,CAAA,2BAAwB;gDAEvB,WAAU;gDACV,SAAS,gBAAgB,CAAC,OAAO,EAAE,CAAC,IAAI;gDACxC,iBAAiB,CAAC,QAChB,oBAAoB,CAAC,OAAS,CAAC;4DAC7B,GAAG,IAAI;4DACP,CAAC,OAAO,EAAE,CAAC,EAAE;wDACf,CAAC;0DAGF,OAAO,EAAE;+CAVL,OAAO,EAAE;;;;;;;;;;;;;;;;0CAgB1B,6LAAC;gCACC,MAAK;gCACL,WAAW,CAAC,0BAA0B,CAAC;gCACvC,SAAS;gCACT,UAAU,oBAAoB,MAAM,KAAK;;oCAExC,EAAE;oCAAU;kDAAC,6LAAC,uMAAA,CAAA,QAAK;wCAAC,MAAM;;;;;;;;;;;;;;;;;;oBAG9B,aAAa,MAAM,GAAG,kBACrB,6LAAC,wIAAA,CAAA,eAAY;wBACX,SAAS,0JAAA,CAAA,sBAAmB;wBAC5B,MAAM;wBACN,cAAc;wBACd,iBAAiB;wBACjB,aAAa,CAAC,WAAa,iBAAiB;wBAC5C,sBAAsB;wBACtB,kBAAkB;wBAClB,qBAAqB;;;;;6CAGvB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CACV,EAAE;;;;;;0CAEL,6LAAC;gCAAE,WAAU;0CACX,EAAE;;;;;;;;;;;;;;;;;;;;;;;;AAOhB;GAlLM;;QACa,oHAAA,CAAA,UAAO;QACd,yMAAA,CAAA,kBAAe;QAQrB,8KAAA,CAAA,WAAQ;QAcQ,yLAAA,CAAA,iBAAc;QAqBH,iLAAA,CAAA,cAAW;;;KA7CtC", "debugId": null}}, {"offset": {"line": 517, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/tables/columns/QuestionBlockListColumns.tsx"], "sourcesContent": ["import { Checkbox } from \"@/components/ui\";\r\nimport { Question } from \"@/types/formBuilder\";\r\nimport { ColumnDef } from \"@tanstack/react-table\";\r\nimport { InputType } from \"@/constants/inputType\";\r\nimport { useTranslations } from \"next-intl\";\r\n\r\nexport const QuestionBlockListColumns= (): ColumnDef<Question>[] =>{\r\n\r\nconst t = useTranslations();\r\n  return [\r\n  {\r\n    id: \"sno\",\r\n    header: t('sn'),\r\n    cell: ({ row }) => row.index + 1,\r\n  },\r\n  {\r\n    accessorKey: \"label\",\r\n    header: t('question'),\r\n  },\r\n  {\r\n    accessorKey: \"inputType\",\r\n    header: t('inputType'),\r\n    cell: ({ row }) => {\r\n      const inputType = row.getValue(\"inputType\") as InputType;\r\n      return inputType.charAt(0).toUpperCase() + inputType.slice(1);\r\n    },\r\n  },\r\n  {\r\n    accessorKey: \"questionOptions\",\r\n    header: t('options'),\r\n    cell: ({ row }) => {\r\n      const inputType = row.original.inputType;\r\n      if (inputType === \"selectone\" || inputType === \"selectmany\") {\r\n        const options = row.original.questionOptions || [];\r\n        return (\r\n          <div className=\"space-y-1\">\r\n            {options.map((option, index) => (\r\n              <div key={index} className=\"text-sm\">\r\n                <span className=\"font-medium\">-{option.label}</span>\r\n                {option.sublabel && (\r\n                  <span className=\"text-neutral-500 ml-2\">({option.sublabel})</span>\r\n                )}\r\n              </div>\r\n            ))}\r\n          </div>\r\n        );\r\n      }\r\n      return null;\r\n    },\r\n  },\r\n  {\r\n    accessorKey: \"hint\",\r\n    header: t('hint'),\r\n  },\r\n  {\r\n    accessorKey: \"isRequired\",\r\n    header: t('required'),\r\n    cell: ({ row }) => {\r\n      const isRequired = row.getValue(\"isRequired\") as boolean;\r\n      return isRequired ? \"Yes\" : \"No\";\r\n    },\r\n  },\r\n  {\r\n    accessorKey: \"updatedAt\",\r\n    header: t('lastModified'),\r\n    cell: ({ row }) => {\r\n      const date = new Date(row.getValue(\"updatedAt\"));\r\n      return date.toLocaleDateString();\r\n    },\r\n  },\r\n];\r\n}\r\n\r\n"], "names": [], "mappings": ";;;;AAIA;;;;AAEO,MAAM,2BAA0B;;IAEvC,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD;IACtB,OAAO;QACP;YACE,IAAI;YACJ,QAAQ,EAAE;YACV,MAAM,CAAC,EAAE,GAAG,EAAE,GAAK,IAAI,KAAK,GAAG;QACjC;QACA;YACE,aAAa;YACb,QAAQ,EAAE;QACZ;QACA;YACE,aAAa;YACb,QAAQ,EAAE;YACV,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,YAAY,IAAI,QAAQ,CAAC;gBAC/B,OAAO,UAAU,MAAM,CAAC,GAAG,WAAW,KAAK,UAAU,KAAK,CAAC;YAC7D;QACF;QACA;YACE,aAAa;YACb,QAAQ,EAAE;YACV,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,YAAY,IAAI,QAAQ,CAAC,SAAS;gBACxC,IAAI,cAAc,eAAe,cAAc,cAAc;oBAC3D,MAAM,UAAU,IAAI,QAAQ,CAAC,eAAe,IAAI,EAAE;oBAClD,qBACE,6LAAC;wBAAI,WAAU;kCACZ,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,6LAAC;gCAAgB,WAAU;;kDACzB,6LAAC;wCAAK,WAAU;;4CAAc;4CAAE,OAAO,KAAK;;;;;;;oCAC3C,OAAO,QAAQ,kBACd,6LAAC;wCAAK,WAAU;;4CAAwB;4CAAE,OAAO,QAAQ;4CAAC;;;;;;;;+BAHpD;;;;;;;;;;gBASlB;gBACA,OAAO;YACT;QACF;QACA;YACE,aAAa;YACb,QAAQ,EAAE;QACZ;QACA;YACE,aAAa;YACb,QAAQ,EAAE;YACV,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,aAAa,IAAI,QAAQ,CAAC;gBAChC,OAAO,aAAa,QAAQ;YAC9B;QACF;QACA;YACE,aAAa;YACb,QAAQ,EAAE;YACV,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,OAAO,IAAI,KAAK,IAAI,QAAQ,CAAC;gBACnC,OAAO,KAAK,kBAAkB;YAChC;QACF;KACD;AACD;GAjEa;;QAEH,yMAAA,CAAA,kBAAe;;;KAFZ", "debugId": null}}, {"offset": {"line": 635, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/tables/QuestionListTable.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport {\r\n  ColumnDef,\r\n  VisibilityState,\r\n  ColumnFiltersState,\r\n  flexRender,\r\n  getCoreRowModel,\r\n  getFilteredRowModel,\r\n  getPaginationRowModel,\r\n  getSortedRowModel,\r\n  useReactTable,\r\n  OnChangeFn,\r\n  SortingState,\r\n  RowSelectionState,\r\n} from \"@tanstack/react-table\";\r\n\r\nimport {\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow,\r\n} from \"@/components/ui/table\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { FaChevronDown } from \"react-icons/fa\";\r\nimport { useTranslations } from \"next-intl\";\r\n\r\ninterface GeneralTableProps<TData, TValue> {\r\n  columns: ColumnDef<TData, TValue>[];\r\n  data: TData[];\r\n  globalFilter?: string;\r\n  setGlobalFilter?: (filterValue: string) => void;\r\n  onTableInit?: (table: import(\"@tanstack/react-table\").Table<TData>) => void;\r\n  columnVisibility?: VisibilityState;\r\n  setColumnVisibility?: (state: VisibilityState) => void;\r\n  onRowSelectionChange?: (rowSelection: RowSelectionState) => void;\r\n  rowSelection?: RowSelectionState;\r\n  onRowClick?: (row: TData) => void;\r\n}\r\n\r\nconst QuestionListTable = <TData, TValue>({\r\n  columns,\r\n  data,\r\n  globalFilter,\r\n  setGlobalFilter,\r\n  onTableInit,\r\n  columnVisibility: externalColumnVisibility,\r\n  setColumnVisibility: setExternalColumnVisibility,\r\n  onRowSelectionChange,\r\n  rowSelection: externalRowSelection,\r\n  onRowClick,\r\n}: GeneralTableProps<TData, TValue>) => {\r\n  const [pagination, setPagination] = React.useState({\r\n    pageIndex: 0,\r\n    pageSize: 8,\r\n  });\r\n\r\n  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(\r\n    []\r\n  );\r\n  const [sorting, setSorting] = React.useState<SortingState>([]);\r\n\r\n  const [internalColumnVisibility, setInternalColumnVisibility] =\r\n    React.useState<VisibilityState>({});\r\n\r\n  const effectiveColumnVisibility =\r\n    externalColumnVisibility ?? internalColumnVisibility;\r\n  const setEffectiveColumnVisibility =\r\n    setExternalColumnVisibility ?? setInternalColumnVisibility;\r\n\r\n  const [internalRowSelection, setInternalRowSelection] = React.useState<RowSelectionState>({});\r\n  \r\n  // Use external row selection if provided, otherwise use internal\r\n  const effectiveRowSelection = externalRowSelection !== undefined \r\n    ? externalRowSelection \r\n    : internalRowSelection;\r\n\r\n  const table = useReactTable({\r\n    data,\r\n    columns,\r\n    onPaginationChange: setPagination,\r\n    onColumnFiltersChange: setColumnFilters,\r\n    onGlobalFilterChange: setGlobalFilter,\r\n    onColumnVisibilityChange:\r\n      setEffectiveColumnVisibility as OnChangeFn<VisibilityState>,\r\n    onRowSelectionChange: (updater) => {\r\n      const newRowSelection =\r\n        typeof updater === \"function\" ? updater(effectiveRowSelection) : updater;\r\n      \r\n      // Only update internal state if we're not using external state\r\n      if (externalRowSelection === undefined) {\r\n        setInternalRowSelection(newRowSelection);\r\n      }\r\n      \r\n      if (onRowSelectionChange) {\r\n        onRowSelectionChange(newRowSelection);\r\n      }\r\n    },\r\n    onSortingChange: setSorting,\r\n\r\n    getCoreRowModel: getCoreRowModel(),\r\n    getFilteredRowModel: getFilteredRowModel(), // Ensure filtering is applied to the entire dataset\r\n    getPaginationRowModel: getPaginationRowModel(),\r\n    getSortedRowModel: getSortedRowModel(),\r\n    enableRowSelection: true,\r\n    enableSorting: true,\r\n    enableSortingRemoval: true,\r\n\r\n    state: {\r\n      pagination,\r\n      columnFilters,\r\n      globalFilter,\r\n      columnVisibility: effectiveColumnVisibility,\r\n      rowSelection: effectiveRowSelection,\r\n      sorting,\r\n    },\r\n  });\r\n\r\n  const t = useTranslations();\r\n\r\n  React.useEffect(() => {\r\n    if (onTableInit) {\r\n      onTableInit(table);\r\n    }\r\n  }, [onTableInit, table]);\r\n\r\n  // Effect to handle external row selection changes\r\n  React.useEffect(() => {\r\n    if (externalRowSelection !== undefined) {\r\n      // If we receive new external selection data, reset the table's row selection state\r\n      table.setRowSelection(externalRowSelection);\r\n    }\r\n  }, [externalRowSelection, table]);\r\n\r\n  return (\r\n    <div>\r\n      <div className=\"rounded-md border border-neutral-400 overflow-hidden\">\r\n        <Table className=\"min-w-full\">\r\n          <TableHeader className=\"h-20\">\r\n            {table.getHeaderGroups().map((headerGroup) => (\r\n              <TableRow\r\n                key={headerGroup.id}\r\n                className=\"text-sm border-neutral-400\"\r\n              >\r\n                {headerGroup.headers.map((header) => (\r\n                  <TableHead\r\n                    key={header.id}\r\n                    className={`py-1 px-6 text-left bg-primary-500 text-neutral-100 font-semibold ${\r\n                      header.index === 0 ? \"w-12 py-3 px-6\" : \"\"\r\n                    }`}\r\n                    style={{\r\n                      cursor: header.column.getCanSort()\r\n                        ? \"pointer\"\r\n                        : \"default\",\r\n                    }}\r\n                  >\r\n                    <div onClick={header.column.getToggleSortingHandler()}>\r\n                      <div>\r\n                        {header.isPlaceholder\r\n                          ? null\r\n                          : flexRender(\r\n                              header.column.columnDef.header,\r\n                              header.getContext()\r\n                            )}\r\n                      </div>\r\n                    </div>\r\n                    { \r\n                      header.column.getCanFilter() && (\r\n                        <input\r\n                          placeholder={t('search')}\r\n                          value={\r\n                            (header.column.getFilterValue() as string) || \"\"\r\n                          }\r\n                          onChange={(e) =>\r\n                            header.column.setFilterValue(e.target.value)\r\n                          }\r\n                          className=\"input-field max-w-48 text-sm my-1 px-2 py-1 bg-neutral-100 text-neutral-700 font-light border-none rounded-md\"\r\n                        />\r\n                      )\r\n                    }\r\n                  </TableHead>\r\n                ))}\r\n              </TableRow>\r\n            ))}\r\n          </TableHeader>\r\n          <TableBody>\r\n            {table.getPaginationRowModel().rows.length ? (\r\n              table.getPaginationRowModel().rows.map((row) => (\r\n                <TableRow\r\n                  key={row.id}\r\n                  data-state={row.getIsSelected() && \"selected\"}\r\n                  className=\"hover:bg-neutral-50 text-sm border-neutral-400\"\r\n                  onClick={() => onRowClick?.(row.original)}\r\n                >\r\n                  {row.getVisibleCells().map((cell, index) => {\r\n                    return (\r\n                      <TableCell\r\n                        key={cell.id}\r\n                        className={`py-4 px-6 max-w-48  ${\r\n                          index === 0 ? \"py-3 px-6\" : \"\"\r\n                        } text-neutral-700 `}\r\n                      >\r\n                        {flexRender(\r\n                          cell.column.columnDef.cell,\r\n                          cell.getContext()\r\n                        )}\r\n                      </TableCell>\r\n                    );\r\n                  })}\r\n                </TableRow>\r\n              ))\r\n            ) : (\r\n              <TableRow>\r\n                <TableCell\r\n                  colSpan={columns.length}\r\n                  className=\"h-24 text-center\"\r\n                >\r\n                 {t('no_results')}\r\n                </TableCell>\r\n              </TableRow>\r\n            )}\r\n          </TableBody>\r\n        </Table>\r\n      </div>\r\n\r\n      <div className=\"flex items-center justify-end space-x-2 py-4\">\r\n\r\n        {data.length > pagination.pageSize && (\r\n          <div className=\"flex items-center justify-end space-x-2 py-4\">\r\n            <button\r\n              className=\"btn-primary\"\r\n              onClick={() => table.previousPage()}\r\n              disabled={!table.getCanPreviousPage()}\r\n            >\r\n              {t('previous')}\r\n            </button>\r\n            <button\r\n              className=\"btn-primary\"\r\n              onClick={() => table.nextPage()}\r\n              disabled={!table.getCanNextPage()}\r\n            >\r\n              {t('next')}\r\n            </button>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport { QuestionListTable };\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAeA;AAgBA;;;AAlCA;;;;;AAiDA,MAAM,oBAAoB,CAAgB,EACxC,OAAO,EACP,IAAI,EACJ,YAAY,EACZ,eAAe,EACf,WAAW,EACX,kBAAkB,wBAAwB,EAC1C,qBAAqB,2BAA2B,EAChD,oBAAoB,EACpB,cAAc,oBAAoB,EAClC,UAAU,EACuB;;IACjC,MAAM,CAAC,YAAY,cAAc,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;QACjD,WAAW;QACX,UAAU;IACZ;IAEA,MAAM,CAAC,eAAe,iBAAiB,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CACtD,EAAE;IAEJ,MAAM,CAAC,SAAS,WAAW,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAe,EAAE;IAE7D,MAAM,CAAC,0BAA0B,4BAA4B,GAC3D,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAkB,CAAC;IAEnC,MAAM,4BACJ,4BAA4B;IAC9B,MAAM,+BACJ,+BAA+B;IAEjC,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAoB,CAAC;IAE3F,iEAAiE;IACjE,MAAM,wBAAwB,yBAAyB,YACnD,uBACA;IAEJ,MAAM,QAAQ,CAAA,GAAA,yLAAA,CAAA,gBAAa,AAAD,EAAE;QAC1B;QACA;QACA,oBAAoB;QACpB,uBAAuB;QACvB,sBAAsB;QACtB,0BACE;QACF,oBAAoB;sDAAE,CAAC;gBACrB,MAAM,kBACJ,OAAO,YAAY,aAAa,QAAQ,yBAAyB;gBAEnE,+DAA+D;gBAC/D,IAAI,yBAAyB,WAAW;oBACtC,wBAAwB;gBAC1B;gBAEA,IAAI,sBAAsB;oBACxB,qBAAqB;gBACvB;YACF;;QACA,iBAAiB;QAEjB,iBAAiB,CAAA,GAAA,wKAAA,CAAA,kBAAe,AAAD;QAC/B,qBAAqB,CAAA,GAAA,wKAAA,CAAA,sBAAmB,AAAD;QACvC,uBAAuB,CAAA,GAAA,wKAAA,CAAA,wBAAqB,AAAD;QAC3C,mBAAmB,CAAA,GAAA,wKAAA,CAAA,oBAAiB,AAAD;QACnC,oBAAoB;QACpB,eAAe;QACf,sBAAsB;QAEtB,OAAO;YACL;YACA;YACA;YACA,kBAAkB;YAClB,cAAc;YACd;QACF;IACF;IAEA,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD;IAExB,6JAAA,CAAA,UAAK,CAAC,SAAS;uCAAC;YACd,IAAI,aAAa;gBACf,YAAY;YACd;QACF;sCAAG;QAAC;QAAa;KAAM;IAEvB,kDAAkD;IAClD,6JAAA,CAAA,UAAK,CAAC,SAAS;uCAAC;YACd,IAAI,yBAAyB,WAAW;gBACtC,mFAAmF;gBACnF,MAAM,eAAe,CAAC;YACxB;QACF;sCAAG;QAAC;QAAsB;KAAM;IAEhC,qBACE,6LAAC;;0BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,6HAAA,CAAA,QAAK;oBAAC,WAAU;;sCACf,6LAAC,6HAAA,CAAA,cAAW;4BAAC,WAAU;sCACpB,MAAM,eAAe,GAAG,GAAG,CAAC,CAAC,4BAC5B,6LAAC,6HAAA,CAAA,WAAQ;oCAEP,WAAU;8CAET,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,uBACxB,6LAAC,6HAAA,CAAA,YAAS;4CAER,WAAW,CAAC,kEAAkE,EAC5E,OAAO,KAAK,KAAK,IAAI,mBAAmB,IACxC;4CACF,OAAO;gDACL,QAAQ,OAAO,MAAM,CAAC,UAAU,KAC5B,YACA;4CACN;;8DAEA,6LAAC;oDAAI,SAAS,OAAO,MAAM,CAAC,uBAAuB;8DACjD,cAAA,6LAAC;kEACE,OAAO,aAAa,GACjB,OACA,CAAA,GAAA,yLAAA,CAAA,aAAU,AAAD,EACP,OAAO,MAAM,CAAC,SAAS,CAAC,MAAM,EAC9B,OAAO,UAAU;;;;;;;;;;;gDAKzB,OAAO,MAAM,CAAC,YAAY,oBACxB,6LAAC;oDACC,aAAa,EAAE;oDACf,OACE,AAAC,OAAO,MAAM,CAAC,cAAc,MAAiB;oDAEhD,UAAU,CAAC,IACT,OAAO,MAAM,CAAC,cAAc,CAAC,EAAE,MAAM,CAAC,KAAK;oDAE7C,WAAU;;;;;;;2CA9BX,OAAO,EAAE;;;;;mCALb,YAAY,EAAE;;;;;;;;;;sCA4CzB,6LAAC,6HAAA,CAAA,YAAS;sCACP,MAAM,qBAAqB,GAAG,IAAI,CAAC,MAAM,GACxC,MAAM,qBAAqB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,oBACtC,6LAAC,6HAAA,CAAA,WAAQ;oCAEP,cAAY,IAAI,aAAa,MAAM;oCACnC,WAAU;oCACV,SAAS,IAAM,aAAa,IAAI,QAAQ;8CAEvC,IAAI,eAAe,GAAG,GAAG,CAAC,CAAC,MAAM;wCAChC,qBACE,6LAAC,6HAAA,CAAA,YAAS;4CAER,WAAW,CAAC,oBAAoB,EAC9B,UAAU,IAAI,cAAc,GAC7B,kBAAkB,CAAC;sDAEnB,CAAA,GAAA,yLAAA,CAAA,aAAU,AAAD,EACR,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,EAC1B,KAAK,UAAU;2CAPZ,KAAK,EAAE;;;;;oCAWlB;mCAnBK,IAAI,EAAE;;;;0DAuBf,6LAAC,6HAAA,CAAA,WAAQ;0CACP,cAAA,6LAAC,6HAAA,CAAA,YAAS;oCACR,SAAS,QAAQ,MAAM;oCACvB,WAAU;8CAEV,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQd,6LAAC;gBAAI,WAAU;0BAEZ,KAAK,MAAM,GAAG,WAAW,QAAQ,kBAChC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,WAAU;4BACV,SAAS,IAAM,MAAM,YAAY;4BACjC,UAAU,CAAC,MAAM,kBAAkB;sCAElC,EAAE;;;;;;sCAEL,6LAAC;4BACC,WAAU;4BACV,SAAS,IAAM,MAAM,QAAQ;4BAC7B,UAAU,CAAC,MAAM,cAAc;sCAE9B,EAAE;;;;;;;;;;;;;;;;;;;;;;;AAOjB;GAhNM;;QAqCU,yLAAA,CAAA,gBAAa;QAyCjB,yMAAA,CAAA,kBAAe;;;KA9ErB", "debugId": null}}, {"offset": {"line": 893, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/library/QuestionBlockList.tsx"], "sourcesContent": ["import { Question } from \"@/types/formBuilder\";\r\nimport { QuestionBlockListColumns } from \"@/components/tables/columns/QuestionBlockListColumns\";\r\nimport { QuestionListTable } from \"../tables/QuestionListTable\";\r\nimport { useState } from \"react\";\r\n\r\ninterface QuestionBlockListProps {\r\n  questions: Question[];\r\n}\r\n\r\nexport const QuestionBlockList = ({ questions }: QuestionBlockListProps) => {\r\n  const [globalFilter, setGlobalFilter] = useState(\"\");\r\n  const columns = QuestionBlockListColumns();\r\n\r\n  return (\r\n    <div className=\"space-y-4\">\r\n      <QuestionListTable\r\n        columns={columns}\r\n        data={questions}\r\n        globalFilter={globalFilter}\r\n        setGlobalFilter={setGlobalFilter}\r\n      />\r\n    </div>\r\n  );\r\n}; "], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;;;AAMO,MAAM,oBAAoB,CAAC,EAAE,SAAS,EAA0B;;IACrE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,UAAU,CAAA,GAAA,+JAAA,CAAA,2BAAwB,AAAD;IAEvC,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,6IAAA,CAAA,oBAAiB;YAChB,SAAS;YACT,MAAM;YACN,cAAc;YACd,iBAAiB;;;;;;;;;;;AAIzB;GAda;KAAA", "debugId": null}}, {"offset": {"line": 940, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/lib/api/form-builder.ts"], "sourcesContent": ["import axios from \"@/lib/axios\";\r\nimport { ContextType } from \"@/types\";\r\n\r\nconst getQuestionsEndPoint = (contextType: ContextType) => {\r\n  if (contextType === \"project\") return \"/questions\";\r\n  else if (contextType === \"template\") return \"/template-questions\";\r\n  else if (contextType === \"questionBlock\") return \"/question-blocks\";\r\n  throw new Error(\"Unsupported context type\");\r\n};\r\n\r\nconst fetchQuestions = async ({ projectId }: { projectId: number }) => {\r\n  const { data } = await axios.get(`/questions/${projectId}`);\r\n  return data.questions;\r\n};\r\n\r\nconst fetchTemplateQuestions = async ({\r\n  templateId,\r\n}: {\r\n  templateId: number;\r\n}) => {\r\n  const { data } = await axios.get(`/template-questions/${templateId}`);\r\n  return data.questions;\r\n};\r\n\r\nconst addQuestion = async ({\r\n  contextType,\r\n  contextId,\r\n  dataToSend,\r\n  position,\r\n}: {\r\n  contextType: ContextType;\r\n  contextId: number;\r\n  dataToSend: {\r\n    label: string;\r\n    isRequired: boolean;\r\n    hint?: string;\r\n    placeholder?: string;\r\n    inputType: string;\r\n    questionOptions?: {\r\n      label: string;\r\n      sublabel?: string;\r\n      code: string;\r\n      nextQuestionId?: number | null;\r\n    }[];\r\n    file?: File;\r\n  };\r\n  position?: number;\r\n}) => {\r\n  const url =\r\n    contextType === \"questionBlock\"\r\n      ? `${getQuestionsEndPoint(contextType)}`\r\n      : `${getQuestionsEndPoint(contextType)}/${contextId}`;\r\n\r\n  // Validate required fields\r\n  if (!dataToSend.label || !dataToSend.inputType) {\r\n    throw new Error(\"Label and inputType are required\");\r\n  }\r\n\r\n  // Check if this input type requires options\r\n  const needsOptions = [\"selectone\", \"selectmany\"].includes(\r\n    dataToSend.inputType\r\n  );\r\n  const hasFile = dataToSend.file instanceof File;\r\n  const hasOptions =\r\n    Array.isArray(dataToSend.questionOptions) &&\r\n    dataToSend.questionOptions.length > 0;\r\n\r\n  // Validate options based on input type and upload method\r\n  if (needsOptions && !hasFile && !hasOptions) {\r\n    throw new Error(\"Options are required for select input types\");\r\n  }\r\n\r\n  if (hasFile) {\r\n    const formData = new FormData();\r\n\r\n    // Add basic question data\r\n    formData.append(\"label\", dataToSend.label);\r\n    // Convert boolean to string in a way backend can parse\r\n    formData.append(\"isRequired\", dataToSend.isRequired ? \"true\" : \"false\");\r\n    formData.append(\"inputType\", dataToSend.inputType);\r\n    if (dataToSend.hint) formData.append(\"hint\", dataToSend.hint);\r\n    if (dataToSend.placeholder)\r\n      formData.append(\"placeholder\", dataToSend.placeholder);\r\n    // Convert number to string\r\n    formData.append(\"position\", String(position || 1));\r\n\r\n    // Add file with the correct field name\r\n    formData.append(\"file\", dataToSend.file as File);\r\n\r\n    // Important: Do NOT include questionOptions when uploading a file\r\n    // They will be parsed from the file on the server\r\n\r\n    try {\r\n      const { data } = await axios.post(url, formData, {\r\n        headers: {\r\n          \"Content-Type\": \"multipart/form-data\",\r\n        },\r\n      });\r\n      return data;\r\n    } catch (error: any) {\r\n      console.error(\r\n        \"Upload error details:\",\r\n        error.response?.data || error.message\r\n      );\r\n      throw new Error(\r\n        `Failed to upload question with file: ${\r\n          error.response?.data?.message || error.message\r\n        }`\r\n      );\r\n    }\r\n  } else {\r\n    // Regular JSON request (no file)\r\n    try {\r\n      const { data } = await axios.post(url, {\r\n        label: dataToSend.label,\r\n        isRequired: dataToSend.isRequired,\r\n        hint: dataToSend.hint,\r\n        placeholder: dataToSend.placeholder,\r\n        inputType: dataToSend.inputType,\r\n        questionOptions: dataToSend.questionOptions,\r\n        position: position || 1,\r\n      });\r\n      return data;\r\n    } catch (error: any) {\r\n      console.error(\r\n        \"API error details:\",\r\n        error.response?.data || error.message\r\n      );\r\n      throw new Error(\r\n        `Failed to add question: ${\r\n          error.response?.data?.message || error.message\r\n        }`\r\n      );\r\n    }\r\n  }\r\n};\r\nconst deleteQuestion = async ({\r\n  contextType,\r\n  id,\r\n  projectId,\r\n}: {\r\n  contextType: ContextType;\r\n  id: number;\r\n  projectId: number;\r\n}) => {\r\n  const { data } = await axios.delete(\r\n    `${getQuestionsEndPoint(contextType)}/${id}?projectId=${projectId}`\r\n  );\r\n  return data;\r\n};\r\n\r\nconst duplicateQuestion = async ({\r\n  id,\r\n  contextType,\r\n  contextId,\r\n}: {\r\n  id: number;\r\n  contextType: ContextType;\r\n  contextId: number;\r\n}) => {\r\n  // For question blocks, we don't need to send the contextId in the body\r\n  // The userId is taken from the authenticated user in the backend\r\n  const requestBody =\r\n    contextType === \"questionBlock\"\r\n      ? {}\r\n      : contextType === \"project\"\r\n        ? { projectId: contextId }\r\n        : { templateId: contextId };\r\n\r\n  const { data } = await axios.post(\r\n    `${getQuestionsEndPoint(\r\n      contextType\r\n    )}/duplicate/${id}?projectId=${contextId}`,\r\n    requestBody\r\n  );\r\n\r\n  return data;\r\n};\r\n\r\nconst updateQuestion = async ({\r\n  id,\r\n  contextType,\r\n  dataToSend,\r\n  contextId,\r\n}: {\r\n  id: number;\r\n  contextType: ContextType;\r\n  dataToSend: {\r\n    label: string;\r\n    isRequired: boolean;\r\n    hint: string;\r\n    placeholder: string;\r\n    position?: number; // Optional position field to preserve question order\r\n    questionOptions?: {\r\n      label: string;\r\n      sublabel?: string;\r\n      code: string;\r\n      nextQuestionId?: number | null;\r\n    }[];\r\n  };\r\n  contextId: number;\r\n}) => {\r\n  const { data } = await axios.patch(\r\n    `${getQuestionsEndPoint(contextType)}/${id}?projectId=${contextId}`,\r\n    dataToSend\r\n  );\r\n  return data;\r\n};\r\n\r\nconst fetchQuestionBlockQuestions = async () => {\r\n  try {\r\n    const response = await axios.get(`/question-blocks`);\r\n    return response.data.questions || [];\r\n  } catch (error) {\r\n    console.error(\"Error fetching question block questions:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nconst updateQuestionPositions = async ({\r\n  contextType,\r\n  contextId,\r\n  questionPositions,\r\n}: {\r\n  contextType: ContextType;\r\n  contextId: number;\r\n  questionPositions: { id: number; position: number }[];\r\n}) => {\r\n  // Only support position updates for projects currently\r\n  if (contextType !== \"project\") {\r\n    throw new Error(\r\n      \"Question position updates are only supported for projects\"\r\n    );\r\n  }\r\n\r\n  const url = `${getQuestionsEndPoint(\r\n    contextType\r\n  )}/positions?projectId=${contextId}`;\r\n  const payload = { questionPositions };\r\n\r\n  try {\r\n    const { data } = await axios.patch(url, payload);\r\n    return data;\r\n  } catch (error: any) {\r\n    console.error(\"Update failed - Full error:\", error);\r\n    console.error(\"Update failed - Error details:\", {\r\n      status: error.response?.status,\r\n      statusText: error.response?.statusText,\r\n      data: error.response?.data,\r\n      message: error.message,\r\n      config: {\r\n        url: error.config?.url,\r\n        method: error.config?.method,\r\n        data: error.config?.data,\r\n      },\r\n    });\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Fetch form builder data with ordered structure (groups and questions)\r\nconst fetchFormBuilderData = async ({ projectId }: { projectId: number }) => {\r\n  const { data } = await axios.get(`/projects/getalldata/${projectId}`);\r\n  return data.data;\r\n};\r\n\r\nexport {\r\n  fetchQuestions,\r\n  fetchTemplateQuestions,\r\n  addQuestion,\r\n  deleteQuestion,\r\n  duplicateQuestion,\r\n  updateQuestion,\r\n  fetchQuestionBlockQuestions,\r\n  updateQuestionPositions,\r\n  fetchFormBuilderData,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;;AAGA,MAAM,uBAAuB,CAAC;IAC5B,IAAI,gBAAgB,WAAW,OAAO;SACjC,IAAI,gBAAgB,YAAY,OAAO;SACvC,IAAI,gBAAgB,iBAAiB,OAAO;IACjD,MAAM,IAAI,MAAM;AAClB;AAEA,MAAM,iBAAiB,OAAO,EAAE,SAAS,EAAyB;IAChE,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,WAAW;IAC1D,OAAO,KAAK,SAAS;AACvB;AAEA,MAAM,yBAAyB,OAAO,EACpC,UAAU,EAGX;IACC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,YAAY;IACpE,OAAO,KAAK,SAAS;AACvB;AAEA,MAAM,cAAc,OAAO,EACzB,WAAW,EACX,SAAS,EACT,UAAU,EACV,QAAQ,EAmBT;IACC,MAAM,MACJ,gBAAgB,kBACZ,GAAG,qBAAqB,cAAc,GACtC,GAAG,qBAAqB,aAAa,CAAC,EAAE,WAAW;IAEzD,2BAA2B;IAC3B,IAAI,CAAC,WAAW,KAAK,IAAI,CAAC,WAAW,SAAS,EAAE;QAC9C,MAAM,IAAI,MAAM;IAClB;IAEA,4CAA4C;IAC5C,MAAM,eAAe;QAAC;QAAa;KAAa,CAAC,QAAQ,CACvD,WAAW,SAAS;IAEtB,MAAM,UAAU,WAAW,IAAI,YAAY;IAC3C,MAAM,aACJ,MAAM,OAAO,CAAC,WAAW,eAAe,KACxC,WAAW,eAAe,CAAC,MAAM,GAAG;IAEtC,yDAAyD;IACzD,IAAI,gBAAgB,CAAC,WAAW,CAAC,YAAY;QAC3C,MAAM,IAAI,MAAM;IAClB;IAEA,IAAI,SAAS;QACX,MAAM,WAAW,IAAI;QAErB,0BAA0B;QAC1B,SAAS,MAAM,CAAC,SAAS,WAAW,KAAK;QACzC,uDAAuD;QACvD,SAAS,MAAM,CAAC,cAAc,WAAW,UAAU,GAAG,SAAS;QAC/D,SAAS,MAAM,CAAC,aAAa,WAAW,SAAS;QACjD,IAAI,WAAW,IAAI,EAAE,SAAS,MAAM,CAAC,QAAQ,WAAW,IAAI;QAC5D,IAAI,WAAW,WAAW,EACxB,SAAS,MAAM,CAAC,eAAe,WAAW,WAAW;QACvD,2BAA2B;QAC3B,SAAS,MAAM,CAAC,YAAY,OAAO,YAAY;QAE/C,uCAAuC;QACvC,SAAS,MAAM,CAAC,QAAQ,WAAW,IAAI;QAEvC,kEAAkE;QAClE,kDAAkD;QAElD,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,KAAK,UAAU;gBAC/C,SAAS;oBACP,gBAAgB;gBAClB;YACF;YACA,OAAO;QACT,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CACX,yBACA,MAAM,QAAQ,EAAE,QAAQ,MAAM,OAAO;YAEvC,MAAM,IAAI,MACR,CAAC,qCAAqC,EACpC,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,EAC9C;QAEN;IACF,OAAO;QACL,iCAAiC;QACjC,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,KAAK;gBACrC,OAAO,WAAW,KAAK;gBACvB,YAAY,WAAW,UAAU;gBACjC,MAAM,WAAW,IAAI;gBACrB,aAAa,WAAW,WAAW;gBACnC,WAAW,WAAW,SAAS;gBAC/B,iBAAiB,WAAW,eAAe;gBAC3C,UAAU,YAAY;YACxB;YACA,OAAO;QACT,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CACX,sBACA,MAAM,QAAQ,EAAE,QAAQ,MAAM,OAAO;YAEvC,MAAM,IAAI,MACR,CAAC,wBAAwB,EACvB,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,EAC9C;QAEN;IACF;AACF;AACA,MAAM,iBAAiB,OAAO,EAC5B,WAAW,EACX,EAAE,EACF,SAAS,EAKV;IACC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,MAAM,CACjC,GAAG,qBAAqB,aAAa,CAAC,EAAE,GAAG,WAAW,EAAE,WAAW;IAErE,OAAO;AACT;AAEA,MAAM,oBAAoB,OAAO,EAC/B,EAAE,EACF,WAAW,EACX,SAAS,EAKV;IACC,uEAAuE;IACvE,iEAAiE;IACjE,MAAM,cACJ,gBAAgB,kBACZ,CAAC,IACD,gBAAgB,YACd;QAAE,WAAW;IAAU,IACvB;QAAE,YAAY;IAAU;IAEhC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,IAAI,CAC/B,GAAG,qBACD,aACA,WAAW,EAAE,GAAG,WAAW,EAAE,WAAW,EAC1C;IAGF,OAAO;AACT;AAEA,MAAM,iBAAiB,OAAO,EAC5B,EAAE,EACF,WAAW,EACX,UAAU,EACV,SAAS,EAkBV;IACC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,KAAK,CAChC,GAAG,qBAAqB,aAAa,CAAC,EAAE,GAAG,WAAW,EAAE,WAAW,EACnE;IAEF,OAAO;AACT;AAEA,MAAM,8BAA8B;IAClC,IAAI;QACF,MAAM,WAAW,MAAM,+GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,gBAAgB,CAAC;QACnD,OAAO,SAAS,IAAI,CAAC,SAAS,IAAI,EAAE;IACtC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4CAA4C;QAC1D,MAAM;IACR;AACF;AAEA,MAAM,0BAA0B,OAAO,EACrC,WAAW,EACX,SAAS,EACT,iBAAiB,EAKlB;IACC,uDAAuD;IACvD,IAAI,gBAAgB,WAAW;QAC7B,MAAM,IAAI,MACR;IAEJ;IAEA,MAAM,MAAM,GAAG,qBACb,aACA,qBAAqB,EAAE,WAAW;IACpC,MAAM,UAAU;QAAE;IAAkB;IAEpC,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,KAAK,CAAC,KAAK;QACxC,OAAO;IACT,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,QAAQ,KAAK,CAAC,kCAAkC;YAC9C,QAAQ,MAAM,QAAQ,EAAE;YACxB,YAAY,MAAM,QAAQ,EAAE;YAC5B,MAAM,MAAM,QAAQ,EAAE;YACtB,SAAS,MAAM,OAAO;YACtB,QAAQ;gBACN,KAAK,MAAM,MAAM,EAAE;gBACnB,QAAQ,MAAM,MAAM,EAAE;gBACtB,MAAM,MAAM,MAAM,EAAE;YACtB;QACF;QACA,MAAM;IACR;AACF;AAEA,wEAAwE;AACxE,MAAM,uBAAuB,OAAO,EAAE,SAAS,EAAyB;IACtE,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,qBAAqB,EAAE,WAAW;IACpE,OAAO,KAAK,IAAI;AAClB", "debugId": null}}, {"offset": {"line": 1100, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/hooks/useQuestionBlockQuestions.ts"], "sourcesContent": ["import { useQuery } from \"@tanstack/react-query\";\r\nimport { fetchQuestionBlockQuestions } from \"@/lib/api/form-builder\";\r\nimport { Question } from \"@/types/formBuilder\";\r\nimport { useAuth } from \"./useAuth\";\r\n\r\nexport const useQuestionBlockQuestions = () => {\r\n  const { user } = useAuth();\r\n  const userId = user?.id;\r\n\r\n  const {\r\n    data: questionsData,\r\n    isLoading,\r\n    isError,\r\n    error,\r\n    refetch\r\n  } = useQuery<Question[]>({\r\n    queryKey: [\"questionBlockQuestions\", userId],\r\n    queryFn: () => fetchQuestionBlockQuestions(),\r\n    enabled: !!userId,\r\n    retry: 1\r\n  });\r\n\r\n  // Ensure questionsData is always an array\r\n  const safeQuestionsData = Array.isArray(questionsData) ? questionsData : [];\r\n\r\n  return {\r\n    questions: safeQuestionsData,\r\n    isLoading,\r\n    isError,\r\n    error,\r\n    refetch,\r\n    userId\r\n  };\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AAEA;;;;;AAEO,MAAM,4BAA4B;;IACvC,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,oHAAA,CAAA,UAAO,AAAD;IACvB,MAAM,SAAS,MAAM;IAErB,MAAM,EACJ,MAAM,aAAa,EACnB,SAAS,EACT,OAAO,EACP,KAAK,EACL,OAAO,EACR,GAAG,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAc;QACvB,UAAU;YAAC;YAA0B;SAAO;QAC5C,OAAO;kDAAE,IAAM,CAAA,GAAA,gIAAA,CAAA,8BAA2B,AAAD;;QACzC,SAAS,CAAC,CAAC;QACX,OAAO;IACT;IAEA,0CAA0C;IAC1C,MAAM,oBAAoB,MAAM,OAAO,CAAC,iBAAiB,gBAAgB,EAAE;IAE3E,OAAO;QACL,WAAW;QACX;QACA;QACA;QACA;QACA;IACF;AACF;GA5Ba;;QACM,oHAAA,CAAA,UAAO;QASpB,8KAAA,CAAA,WAAQ", "debugId": null}}, {"offset": {"line": 1151, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/app/%5Blocale%5D/%28main%29/library/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Template } from \"@/types\";\r\nimport { GeneralTable } from \"@/components/tables/GeneralTable\";\r\nimport { TemplateList } from \"@/components/library/TemplateList\";\r\nimport { TemplateListColumns } from \"@/components/tables/columns/TemplateListColumns\";\r\nimport { QuestionBlockList } from \"@/components/library/QuestionBlockList\";\r\nimport { useQuestionBlockQuestions } from \"@/hooks/useQuestionBlockQuestions\";\r\nimport { use } from \"react\";\r\nimport Link from \"next/link\";\r\nimport { useTranslations } from \"next-intl\";\r\n\r\nexport default function LibraryPage() {\r\n  // change this after creating question blocks\r\n  const questionBlockData: Template[] = [];\r\n  const { questions} = useQuestionBlockQuestions()\r\n  const t = useTranslations();\r\n\r\n  return (\r\n    <div className=\"p-6 space-y-6 section gap-8\">\r\n      <h1 className=\"text-2xl font-semibold\">{t('myLibrary')}</h1>\r\n      <TemplateList />\r\n      {/* change this after creating question blocks */}\r\n      <div className=\"flex flex-col gap-4\">\r\n        <h1 className=\"sub-heading-text hover:text-neutral-700\">\r\n          <Link href={'/library/question-block/form-builder'}>\r\n         {t('questionBlocks')}\r\n\r\n          </Link>\r\n          </h1>\r\n        {questions.length > 0 ? (\r\n          <QuestionBlockList questions={questions} />\r\n        ) : (\r\n          <div className=\"text-center py-16 space-y-4\">\r\n            <p className=\"text-lg\">\r\n              {t('getStarted')}\r\n            </p>\r\n            <p className=\"text-sm text-gray-500\">\r\n             {t('advancedUsers')}\r\n            </p>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAIA;AAEA;AACA;AAEA;AACA;;;AAVA;;;;;;AAYe,SAAS;;IACtB,6CAA6C;IAC7C,MAAM,oBAAgC,EAAE;IACxC,MAAM,EAAE,SAAS,EAAC,GAAG,CAAA,GAAA,qIAAA,CAAA,4BAAyB,AAAD;IAC7C,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD;IAExB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAA0B,EAAE;;;;;;0BAC1C,6LAAC,yIAAA,CAAA,eAAY;;;;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCACZ,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAM;sCACZ,EAAE;;;;;;;;;;;oBAIH,UAAU,MAAM,GAAG,kBAClB,6LAAC,8IAAA,CAAA,oBAAiB;wBAAC,WAAW;;;;;6CAE9B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CACV,EAAE;;;;;;0CAEL,6LAAC;gCAAE,WAAU;0CACX,EAAE;;;;;;;;;;;;;;;;;;;;;;;;AAOhB;GAjCwB;;QAGD,qIAAA,CAAA,4BAAyB;QACpC,yMAAA,CAAA,kBAAe;;;KAJH", "debugId": null}}, {"offset": {"line": 1270, "column": 0}, "map": {"version": 3, "file": "trash.js", "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/node_modules/lucide-react/src/icons/trash.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M3 6h18', key: 'd0wm0j' }],\n  ['path', { d: 'M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6', key: '4alrt4' }],\n  ['path', { d: 'M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2', key: 'v07s0e' }],\n];\n\n/**\n * @component @name Trash\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyA2aDE4IiAvPgogIDxwYXRoIGQ9Ik0xOSA2djE0YzAgMS0xIDItMiAySDdjLTEgMC0yLTEtMi0yVjYiIC8+CiAgPHBhdGggZD0iTTggNlY0YzAtMSAxLTIgMi0yaDRjMSAwIDIgMSAyIDJ2MiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/trash\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Trash = createLucideIcon('trash', __iconNode);\n\nexport default Trash;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAyC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IACtE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAsC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CACrE,CAAA;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}