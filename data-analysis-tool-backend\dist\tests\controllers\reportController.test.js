"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const reportController = __importStar(require("../../controllers/reportController"));
const reportRepository_1 = __importDefault(require("../../repositories/reportRepository"));
const formSubmissionRepository_1 = __importDefault(require("../../repositories/formSubmissionRepository"));
// Mock dependencies
jest.mock("../../repositories/reportRepository");
jest.mock("../../repositories/formSubmissionRepository");
// Mock the hashids decode function
const mockDecode = jest.fn().mockReturnValue([123]);
reportController.hashids.decode = mockDecode;
// Get the getProjectReport function
const { getProjectReport } = reportController;
describe("Report Controller", () => {
    let mockRequest;
    let mockResponse;
    let responseObject = {};
    beforeEach(() => {
        // Reset mocks before each test
        jest.clearAllMocks();
        jest.resetAllMocks();
        // Setup mock response
        mockResponse = {
            status: jest.fn().mockReturnThis(),
            json: jest.fn().mockImplementation((result) => {
                responseObject = result;
                return mockResponse;
            }),
        };
        // Reset response object
        responseObject = {};
        // Setup default authenticated user
        mockRequest = {
            user: {
                id: 1,
            },
            params: {
                projectId: "hashedProjectId",
            },
            query: {},
        };
        // Reset the mockDecode function
        mockDecode.mockReturnValue([123]);
    });
    describe("getProjectReport", () => {
        it("should generate a project report successfully", () => __awaiter(void 0, void 0, void 0, function* () {
            // Mock data
            const mockReport = {
                summary: {
                    totalSubmissions: 10,
                    totalQuestions: 2, // Only selectone and selectmany questions
                    averageResponseRate: 80,
                },
                data: [
                    {
                        question: "Select One Question",
                        type: "selectone",
                        answered: 8,
                        total: 10,
                    },
                    {
                        question: "Select Many Question",
                        type: "selectmany",
                        answered: 7,
                        total: 10,
                    },
                ],
                metadata: {
                    projectName: "Test Project",
                    generatedAt: new Date(),
                },
            };
            // Mock repository responses
            formSubmissionRepository_1.default.isProjectAccessible.mockResolvedValue(true);
            reportRepository_1.default.generateReport.mockResolvedValue(mockReport);
            yield getProjectReport(mockRequest, mockResponse);
            expect(formSubmissionRepository_1.default.isProjectAccessible).toHaveBeenCalledWith(1, 123);
            expect(reportRepository_1.default.generateReport).toHaveBeenCalledWith(123, {
                type: "default",
                startDate: undefined,
                endDate: undefined,
            });
            expect(mockResponse.status).toHaveBeenCalledWith(200);
            expect(responseObject).toHaveProperty("statusCode", 200);
            expect(responseObject).toHaveProperty("data", mockReport);
            expect(responseObject).toHaveProperty("message", "Report generated successfully");
        }));
        it("should generate a report with date filters", () => __awaiter(void 0, void 0, void 0, function* () {
            // Setup date filters
            const startDate = "2023-01-01";
            const endDate = "2023-12-31";
            mockRequest.query = {
                startDate,
                endDate,
                type: "detailed",
            };
            // Mock data
            const mockReport = {
                summary: { totalSubmissions: 5 },
                data: [],
                metadata: { projectName: "Test Project" },
            };
            // Mock repository responses
            formSubmissionRepository_1.default.isProjectAccessible.mockResolvedValue(true);
            reportRepository_1.default.generateReport.mockResolvedValue(mockReport);
            yield getProjectReport(mockRequest, mockResponse);
            expect(reportRepository_1.default.generateReport).toHaveBeenCalledWith(123, {
                type: "detailed",
                startDate: new Date(startDate),
                endDate: new Date(endDate),
            });
            expect(mockResponse.status).toHaveBeenCalledWith(200);
            expect(responseObject).toHaveProperty("data", mockReport);
        }));
        it("should return 401 when user is not authenticated", () => __awaiter(void 0, void 0, void 0, function* () {
            // User not authenticated
            mockRequest.user = undefined;
            yield getProjectReport(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(401);
            expect(responseObject).toHaveProperty("statusCode", 401);
            expect(responseObject).toHaveProperty("message", "Unauthorized");
            expect(reportRepository_1.default.generateReport).not.toHaveBeenCalled();
        }));
        it("should return 400 when project ID is invalid", () => __awaiter(void 0, void 0, void 0, function* () {
            // Mock invalid project ID decoding
            mockDecode.mockReturnValue([]);
            yield getProjectReport(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(400);
            expect(responseObject).toHaveProperty("statusCode", 400);
            expect(responseObject).toHaveProperty("message", "Invalid project ID");
            expect(reportRepository_1.default.generateReport).not.toHaveBeenCalled();
        }));
        it("should return 403 when user doesn't have access to the project", () => __awaiter(void 0, void 0, void 0, function* () {
            // Mock no access to project
            formSubmissionRepository_1.default.isProjectAccessible.mockResolvedValue(false);
            yield getProjectReport(mockRequest, mockResponse);
            expect(formSubmissionRepository_1.default.isProjectAccessible).toHaveBeenCalledWith(1, 123);
            expect(mockResponse.status).toHaveBeenCalledWith(403);
            expect(responseObject).toHaveProperty("statusCode", 403);
            expect(responseObject).toHaveProperty("message", "You don't have access to this project");
            expect(reportRepository_1.default.generateReport).not.toHaveBeenCalled();
        }));
        it("should handle errors and return 500", () => __awaiter(void 0, void 0, void 0, function* () {
            // Mock repository error
            formSubmissionRepository_1.default.isProjectAccessible.mockResolvedValue(true);
            reportRepository_1.default.generateReport.mockRejectedValue(new Error("Database error"));
            yield getProjectReport(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(500);
            expect(responseObject).toHaveProperty("statusCode", 500);
            expect(responseObject.message).toContain("Error generating report");
        }));
        it("should include all question types in the report", () => __awaiter(void 0, void 0, void 0, function* () {
            // Mock data with mixed question types
            const mockReport = {
                summary: {
                    totalSubmissions: 5,
                    totalQuestions: 4, // All question types
                    averageResponseRate: 90,
                },
                data: [
                    {
                        question: "Select One Question",
                        type: "selectone",
                        answered: 4,
                        total: 5,
                    },
                    {
                        question: "Select Many Question",
                        type: "selectmany",
                        answered: 5,
                        total: 5,
                    },
                    {
                        question: "Text Question",
                        type: "text",
                        answered: 3,
                        total: 5,
                    },
                    {
                        question: "Number Question",
                        type: "number",
                        answered: 4,
                        total: 5,
                    },
                ],
                metadata: {
                    projectName: "Test Project",
                    generatedAt: new Date(),
                },
            };
            // Mock repository responses
            formSubmissionRepository_1.default.isProjectAccessible.mockResolvedValue(true);
            reportRepository_1.default.generateReport.mockResolvedValue(mockReport);
            yield getProjectReport(mockRequest, mockResponse);
            // Verify the report contains all question types
            expect(responseObject.data.data.length).toBe(4);
            // Verify each question type is included
            const selectOneQuestion = responseObject.data.data.find((item) => item.type === "selectone");
            const selectManyQuestion = responseObject.data.data.find((item) => item.type === "selectmany");
            const textQuestion = responseObject.data.data.find((item) => item.type === "text");
            const numberQuestion = responseObject.data.data.find((item) => item.type === "number");
            expect(selectOneQuestion).toBeDefined();
            expect(selectManyQuestion).toBeDefined();
            expect(textQuestion).toBeDefined();
            expect(numberQuestion).toBeDefined();
        }));
    });
});
