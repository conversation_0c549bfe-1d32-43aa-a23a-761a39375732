"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.copyProjectUsers = exports.getAllProjectUser = exports.deletProjectUser = exports.updateProjectUser = exports.createProjectUser = void 0;
const projectUserRepository_1 = __importDefault(require("../repositories/projectUserRepository"));
const projectUserValidator_1 = require("../validators/projectUserValidator");
const projectRepository_1 = __importDefault(require("../repositories/projectRepository"));
const projectUserRepository_2 = __importDefault(require("../repositories/projectUserRepository"));
const userRepository_1 = __importDefault(require("../repositories/userRepository"));
const createProjectUser = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        if (!req.user || !req.user.id) {
            return res.status(404).json({
                success: false,
                message: "user not found",
            });
        }
        const owner = req.user.id;
        const result = projectUserValidator_1.ProjectUserSchema.safeParse(req.body);
        if (!result.success) {
            res.status(400).json({
                success: false,
                message: result.error.flatten().fieldErrors,
            });
            return;
        }
        const { userId, projectId, permission } = req.body;
        const user = yield userRepository_1.default.findById(userId);
        if (!user) {
            return res.status(404).json({
                success: false,
                message: "user not found",
            });
        }
        if (userId === owner) {
            return res.status(400).json({
                success: false,
                message: "You are the owner of the project. No need to add yourself",
            });
        }
        const project = yield projectRepository_1.default.findById(projectId);
        if (!project) {
            return res.status(404).json({
                success: false,
                message: "project not found",
            });
        }
        const existanceUser = yield projectUserRepository_2.default.findUserProject(userId, projectId);
        if (existanceUser) {
            return res.status(400).json({
                success: false,
                message: "user already associated with project",
            });
        }
        const userProject = yield projectUserRepository_1.default.create(userId, projectId, permission);
        return res.status(200).json({
            success: true,
            message: "user added successly to project",
            data: { userProject },
        });
    }
    catch (error) {
        return res.status(500).json({
            success: false,
            message: "error creating qustion",
            error: error instanceof Error ? error.message : "unexpected error",
        });
    }
});
exports.createProjectUser = createProjectUser;
const updateProjectUser = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const result = projectUserValidator_1.ProjectUserSchema.safeParse(req.body);
        if (!result.success) {
            res.status(400).json({
                success: false,
                message: result.error.flatten().fieldErrors,
            });
            return;
        }
        const { userId, projectId, permission } = req.body;
        const updatedProject = yield projectUserRepository_1.default.updateUserPermission(userId, projectId, permission);
        return res.status(200).json({
            success: false,
            message: "permission updated success",
            data: { updatedProject },
        });
    }
    catch (error) {
        return res.status(500).json({
            success: false,
            message: "error creating qustion",
            error: error instanceof Error ? error.message : "unexpected error",
        });
    }
});
exports.updateProjectUser = updateProjectUser;
const deletProjectUser = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { userId, projectId } = req.body;
        yield projectUserRepository_2.default.deleteUserFromProject(userId, projectId);
        return res.status(200).json({
            success: false,
            message: "user delete succes",
        });
    }
    catch (error) {
        return res.status(500).json({
            success: false,
            message: "error creating qustion",
            error: error instanceof Error ? error.message : "unexpected error",
        });
    }
});
exports.deletProjectUser = deletProjectUser;
const getAllProjectUser = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const projectId = Number(req.params.projectId);
        const AllUser = yield projectUserRepository_2.default.allUsers(projectId);
        return res.status(200).json({
            success: true,
            message: "project user fetched success",
            data: { AllUser },
        });
    }
    catch (error) {
        return res.status(500).json({
            success: false,
            message: "error fetching users",
            error: error instanceof Error ? error.message : "unexpected error",
        });
    }
});
exports.getAllProjectUser = getAllProjectUser;
const copyProjectUsers = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { sourceProjectId, targetProjectId } = req.body;
        if (!req.user || !req.user.id) {
            return res.status(404).json({
                success: false,
                message: "User not found",
            });
        }
        const ownerId = req.user.id;
        // Ensure both projects exist
        const sourceProject = yield projectRepository_1.default.findById(sourceProjectId);
        const targetProject = yield projectRepository_1.default.findById(targetProjectId);
        if (!sourceProject || !targetProject) {
            return res.status(404).json({
                success: false,
                message: "One or both projects not found",
            });
        }
        if ((targetProject === null || targetProject === void 0 ? void 0 : targetProject.user.id) !== ownerId) {
            return res.status(403).json({
                success: false,
                message: "You are not the owner of the target project",
            });
        }
        const sourceUsers = yield projectUserRepository_2.default.allUsers(sourceProjectId);
        let addedUsers = [];
        for (const projectUser of sourceUsers) {
            const { userId, permission } = projectUser;
            if (userId == ownerId)
                continue;
            const alreadyExist = yield projectUserRepository_2.default.findUserProject(userId, targetProjectId);
            if (alreadyExist)
                continue;
            if (permission &&
                typeof permission === "object" &&
                !Array.isArray(permission)) {
                const typedPermission = permission;
                const newUser = yield projectUserRepository_2.default.create(userId, targetProjectId, typedPermission);
                addedUsers.push(newUser);
            }
        }
        return res.status(200).json({
            success: true,
            message: `${addedUsers.length} users copied to target project successfully.`,
            data: addedUsers,
        });
    }
    catch (error) {
        return res.status(500).json({
            success: false,
            message: "error copying project user",
            error: error instanceof Error ? error.message : "unexpected error",
        });
    }
});
exports.copyProjectUsers = copyProjectUsers;
