"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const auth_1 = require("../middleware/auth");
const formSubmissionController_1 = require("../controllers/formSubmissionController");
const checkPermission_1 = require("../middleware/checkPermission");
const router = express_1.default.Router();
// Protect all routes with authentication
router.use(auth_1.authenticate);
// Create a new form submission
router.post("/", (0, checkPermission_1.checkPermission)([
    "manageProject",
    "addSubmissions",
]), formSubmissionController_1.createFormSubmission);
router.delete("/deleteMultiple", (0, checkPermission_1.checkPermission)([
    "manageProject",
    "deleteSubmissions",
]), formSubmissionController_1.DeleteManyFormSubmission);
// Get submissions for a project
router.get("/:projectId", (0, checkPermission_1.checkPermission)([
    "manageProject",
    "viewSubmissions",
]), formSubmissionController_1.getProjectFormSubmissions);
// Get a specific submission
router.get("/:id", (0, checkPermission_1.checkPermission)([
    "manageProject",
    "viewSubmissions",
]), formSubmissionController_1.getFormSubmission);
// Update a submission
router.patch("/:id", (0, checkPermission_1.checkPermission)([
    "manageProject",
    "editSubmissions",
]), formSubmissionController_1.updateFormSubmission);
router.delete("/:id", (0, checkPermission_1.checkPermission)([
    "manageProject",
    "deleteSubmissions",
]), formSubmissionController_1.deleteFormSubmission);
router.patch("/change-login-required/:id", (0, checkPermission_1.checkPermission)([
    "manageProject",
    "editSubmissions",
]), formSubmissionController_1.ToggleFormSubmissionLoginRequired);
// Delete a submission
exports.default = router;
