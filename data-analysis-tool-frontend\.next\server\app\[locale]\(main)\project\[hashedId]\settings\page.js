(()=>{var e={};e.id=3799,e.ids=[3799],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7182:(e,t,s)=>{Promise.resolve().then(s.bind(s,90541))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},13240:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Kobo-project\\\\data-analysis-tool-frontend\\\\app\\\\[locale]\\\\(main)\\\\project\\\\[hashedId]\\\\settings\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\project\\[hashedId]\\settings\\page.tsx","default")},14262:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>M});var r=s(60687),a=s(43210),o=s(27605),i=s(12810),n=s(16189),l=s(6986),c=s(8693),d=s(29494),u=s(54050),p=s(71845),m=s(54864),h=s(19150),x=s(68292),j=s(10022),b=s(11437),y=s(57800),f=s(86429),v=s(32833),g=s(15566),N=s(40480),P=s(21650),k=s(58857),C=s(73678),q=s(77618);let A=async({projectId:e,dataToSend:t})=>{let{data:s}=await i.A.patch(`/projects/${e}`,t);return s},M=()=>{let[e,t]=(0,a.useState)(!1);(0,a.useEffect)(()=>{t(!0)},[]);let{register:s,formState:{isSubmitting:i,errors:M,isSubmitted:w},handleSubmit:D,setValue:E,reset:S}=(0,o.mN)(),F=(0,n.useRouter)(),[I,K]=(0,a.useState)(!1),z=(0,q.c3)(),[U,_]=(0,a.useState)(null),[O,B]=(0,a.useState)(null),[T,$]=(0,a.useState)(!1),[Q,R]=(0,a.useState)(!1),[V,G]=(0,a.useState)(null);(0,a.useEffect)(()=>{s("country",{required:"Please select a country"}),s("sector",{required:"Please select a sector"})},[s]),(0,a.useEffect)(()=>{E("country",U,{shouldValidate:w}),E("sector",O,{shouldValidate:w})},[E,U,O]);let{hashedId:H}=(0,n.useParams)(),W=(0,l.D)(H),{user:L}=(0,P.A)(),X=(0,c.jE)();(0,a.useEffect)(()=>()=>{W&&L?.id&&X.cancelQueries({queryKey:["projects",L.id,W]})},[W,L?.id,X]);let{data:J,isLoading:Y,isError:Z}=(0,d.I)({queryKey:["projects",L?.id,W],queryFn:()=>(0,p.kf)({projectId:W}),enabled:!!W&&!!L?.id&&!I});(0,a.useEffect)(()=>{J&&(S({projectName:J.name||"",description:J.description||"",country:J.country||"",sector:J.sector||""}),_(J.country||null),B(J.sector||null))},[J,S]);let ee=(0,m.wA)(),et=(0,u.n)({mutationFn:A,onSuccess:()=>{X.invalidateQueries({queryKey:["projects",L?.id],exact:!1}),ee((0,h.Ds)({message:z("projectUpdated"),type:"success"}))},onError:e=>{ee((0,h.Ds)({message:z("projectUpdateFailed")+e.message,type:"error"}))}}),es=(0,u.n)({mutationFn:e=>(0,p.pf)(W,e?.isUnarchive||!1),onSuccess:(e,t)=>{let s=t?.isUnarchive||!1;X.invalidateQueries({queryKey:["projects",L?.id]}),ee((0,h.Ds)({message:s?z("projectUnarchived"):z("projectDeployed"),type:"success"})),R(!1)},onError:e=>{ee((0,h.Ds)({message:z("projectDeployFailed"),type:"error"})),R(!1)}}),er=(0,u.n)({mutationFn:()=>(0,p.Im)(W),onSuccess:()=>{X.invalidateQueries({queryKey:["projects",L?.id,W]}),ee((0,h.Ds)({message:z("projectArchived"),type:"success"})),R(!1),F.push("/dashboard"),X.invalidateQueries({queryKey:["projects",L?.id]})},onError:e=>{console.error("Project archive error:",e),ee((0,h.Ds)({message:z("projectArchiveFailed"),type:"error"})),R(!1)}}),ea=(0,u.n)({mutationFn:()=>(0,p.xx)(W),onSuccess:()=>{K(!0),R(!1),X.cancelQueries({queryKey:["projects",L?.id,W]}),X.removeQueries({queryKey:["projects",L?.id,W]}),X.invalidateQueries({queryKey:["projects",L?.id]}),ee((0,h.Ds)({message:z("projectDeleted"),type:"success"})),setTimeout(()=>{F.push("/dashboard")},1e3)},onError:e=>{R(!1),console.error("Project deletion error:",e),ee((0,h.Ds)({message:z("projectDeleteFailed"),type:"error"}))}}),eo=()=>{let e=J?.status==="archived",t=z("confirmDeployMessage");J?.status==="deployed"?t=z("confirmRedeployMessage"):J?.status==="archived"&&(t=z("confirmDeployMessage")),G({title:e?z("confirmUnarchive"):z("confirmDeploy"),description:t,confirmButtonText:e?z("unarchive"):z("deploy"),confirmButtonClass:"btn-primary",onConfirm:()=>{es.mutate({isUnarchive:e})}}),R(!0)},ei=async e=>{et.mutate({projectId:W,dataToSend:{name:e.projectName,description:e.description,country:e.country,sector:e.sector}})};return e?I||Y?(0,r.jsx)(f.A,{}):H&&null!==W?Z&&!I?(0,r.jsx)("p",{className:"text-red-500",children:z("fetchProjectFailed")}):(0,r.jsxs)("form",{className:"flex flex-col gap-8",onSubmit:D(ei),children:[(0,r.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsxs)("label",{htmlFor:"project-name",className:"label-text",children:[(0,r.jsx)(j.A,{size:16})," ",z("projectName")]}),(0,r.jsx)("input",{...s("projectName",{required:z("projectNameRequired")}),id:"project-name",type:"text",className:"input-field",placeholder:z("enterProjectName")}),M.projectName&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:`${M.projectName.message}`})]}),(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsx)("label",{htmlFor:"description",className:"label-text",children:z("description")}),(0,r.jsx)("textarea",{id:"description",...s("description"),className:"input-field resize-none",cols:4,placeholder:z("enterProjectDescription")})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsxs)("label",{htmlFor:"country",className:"label-text",children:[(0,r.jsx)(b.A,{size:16}),z("country")]}),(0,r.jsx)(x.l,{id:"country",options:g,value:U,onChange:_}),M.country&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:`${M.country.message}`})]}),(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsxs)("label",{htmlFor:"sector",className:"label-text",children:[(0,r.jsx)(y.A,{size:16})," ",z("sector")]}),(0,r.jsx)(x.l,{id:"sector",options:Object.values(v.b),value:O&&v.b[O]?v.b[O]:z("selectOption"),onChange:e=>{B((0,N.H)(e,v.b))}}),M.sector&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:`${M.sector.message}`})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[J?.status==="deployed"&&(0,r.jsx)("button",{onClick:()=>{G({title:z("confirmArchive"),description:(0,r.jsx)(r.Fragment,{children:z("confirmArchiveMessage")}),confirmButtonText:z("archive"),confirmButtonClass:"btn-primary",onConfirm:()=>{er.mutate()}}),R(!0)},type:"button",className:"btn-outline",children:z("archive")}),J?.status==="deployed"&&(0,r.jsx)("button",{onClick:eo,type:"button",className:"btn-outline",children:z("redeploy")}),J?.status==="archived"&&(0,r.jsx)("button",{onClick:eo,type:"button",className:"btn-outline",children:z("deploy")}),J?.status==="draft"&&(0,r.jsx)("button",{onClick:eo,type:"button",className:"btn-outline",children:z("deploy")}),(0,r.jsx)("button",{type:"button",className:"btn-outline",onClick:()=>{$(!0)},children:z("share")}),(0,r.jsx)("button",{onClick:()=>{G({title:z("confirmDelete"),description:(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("p",{children:z("confirmDeleteMessage")}),(0,r.jsxs)("ul",{className:"list-disc list-inside flex flex-col gap-2 mt-2 text-neutral-700",children:[(0,r.jsx)("li",{children:z("deleteProjectWarning1")}),(0,r.jsx)("li",{children:z("deleteProjectWarning2")}),(0,r.jsx)("li",{children:z("deleteProjectWarning3")})]})]}),confirmButtonText:z("delete"),confirmButtonClass:"btn-danger",onConfirm:()=>{ea.mutate()}}),R(!0)},type:"button",className:"btn-danger",children:z("delete")})]}),(0,r.jsx)("button",{type:"submit",className:"btn-primary self-end",children:i?(0,r.jsxs)("span",{className:"flex items-center gap-2",children:[z("saving"),(0,r.jsx)("div",{className:"size-4 animate-spin border-x border-neutral-100 rounded-full"})]}):z("saveChanges")})]})]}),(0,r.jsx)(k.m,{showModal:T,onClose:()=>$(!1),onShare:()=>{$(!1)}}),V&&(0,r.jsx)(C.R,{showModal:Q,onClose:()=>R(!1),title:V.title,description:V.description,confirmButtonText:V.confirmButtonText,confirmButtonClass:V.confirmButtonClass,onConfirm:V.onConfirm})]}):(0,r.jsxs)("div",{className:"error-message",children:[(0,r.jsx)("h1",{className:"text-red-500",children:z("invalidProjectIdError")}),(0,r.jsx)("p",{className:"text-neutral-700",children:z("invalidProjectIdMessage")})]}):null}},17090:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("file-pen",[["path",{d:"M12.5 22H18a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v9.5",key:"1couwa"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M13.378 15.626a1 1 0 1 0-3.004-3.004l-5.01 5.012a2 2 0 0 0-.506.854l-.837 2.87a.5.5 0 0 0 .62.62l2.87-.837a2 2 0 0 0 .854-.506z",key:"1y4qbx"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20174:(e,t,s)=>{"use strict";s.d(t,{F:()=>n});var r=s(60687),a=s(16189),o=s(85814),i=s.n(o);s(43210);let n=({items:e})=>{let t=(0,a.usePathname)(),s=e=>t.startsWith(e);return(0,r.jsx)("div",{className:"border-y border-neutral-400 rounded-md bg-primary-500 my-4 shadow-md",children:(0,r.jsx)("div",{className:"flex items-center",children:e.map(e=>e.disabled?(0,r.jsxs)("div",{className:"flex items-center gap-2 font-medium transition-all duration-300 p-4 border-b-2 text-gray-400 cursor-not-allowed",children:[e.icon,e.label]},e.route):(0,r.jsxs)(i(),{href:e.route,className:`flex items-center gap-2 font-medium transition-all duration-300 p-4 border-b-2 text-neutral-100 ${s(e.route)?"border-neutral-100":"border-transparent hover:border-neutral-400"}`,children:[e.icon,e.label]},e.route))})})}},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28559:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},37446:(e,t,s)=>{Promise.resolve().then(s.bind(s,51129))},39874:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var r=s(65239),a=s(48088),o=s(88170),i=s.n(o),n=s(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);s.d(t,l);let c={children:["",{children:["[locale]",{children:["(main)",{children:["project",{children:["[hashedId]",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,13240)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\project\\[hashedId]\\settings\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,51129)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\project\\[hashedId]\\layout.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,84606)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,72121)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,58014)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\project\\[hashedId]\\settings\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/(main)/project/[hashedId]/settings/page",pathname:"/[locale]/project/[hashedId]/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},51129:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Kobo-project\\\\data-analysis-tool-frontend\\\\app\\\\[locale]\\\\(main)\\\\project\\\\[hashedId]\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\project\\[hashedId]\\layout.tsx","default")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},61611:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64792:(e,t,s)=>{Promise.resolve().then(s.bind(s,13240))},74075:e=>{"use strict";e.exports=require("zlib")},74520:(e,t,s)=>{Promise.resolve().then(s.bind(s,14262))},78407:(e,t,s)=>{"use strict";s.d(t,{F:()=>a});var r=s(43210);let a=({projectData:e,user:t})=>(0,r.useMemo)(()=>{let s=t?.id===e?.user?.id,r=e?.projectUser?.[0],a=r?.permission||{};return{viewForm:s||a.viewForm||!1,editForm:s||a.editForm||!1,viewSubmissions:s||a.viewSubmissions||!1,addSubmissions:s||a.addSubmissions||!1,deleteSubmissions:s||a.deleteSubmissions||!1,validateSubmissions:s||a.validateSubmissions||!1,editSubmissions:s||a.editSubmissions||!1,manageProject:s||a.manageProject||!1}},[t?.id,e])},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84027:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},86757:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("panels-top-left",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M9 21V9",key:"1oto5p"}]])},90541:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>N});var r=s(60687),a=s(86429),o=s(86757),i=s(17090),n=s(61611),l=s(84027),c=s(16189),d=s(20174),u=s(77618);let p=({permissions:e})=>{let{hashedId:t}=(0,c.useParams)(),s=(0,u.c3)(),a=e.manageProject,p=a||e.viewForm||e.editForm,m=a||e.viewSubmissions||e.editSubmissions||e.addSubmissions||e.deleteSubmissions,h=[{label:s("overview"),icon:(0,r.jsx)(o.A,{size:16}),route:`/project/${t}/overview`,disabled:!1},{label:s("formBuilder"),icon:(0,r.jsx)(i.A,{size:16}),route:`/project/${t}/form-builder`,disabled:!p},{label:s("data"),icon:(0,r.jsx)(n.A,{size:16}),route:`/project/${t}/data`,disabled:!m},{label:s("settings"),icon:(0,r.jsx)(l.A,{size:16}),route:`/project/${t}/settings`,disabled:!a}];return(0,r.jsx)(d.F,{items:h})};var m=s(21650),h=s(78407),x=s(71845),j=s(6986),b=s(29494),y=s(28559),f=s(85814),v=s.n(f),g=s(43210);let N=({children:e})=>{let{hashedId:t}=(0,c.useParams)(),{user:s}=(0,m.A)(),o=(0,u.c3)(),i=(0,g.useMemo)(()=>(0,j.D)(t),[t]),{data:n,isLoading:l,isError:d}=(0,b.I)({queryKey:["projects",s?.id,i],queryFn:()=>(0,x.kf)({projectId:i}),enabled:!!i&&!!s?.id}),f=(0,h.F)({projectData:n,user:s});return t&&null!==i?l?(0,r.jsx)(a.A,{}):d?(0,r.jsx)("p",{className:"text-red-500",children:o("fetchProjectFailed")}):(0,r.jsxs)("div",{className:"section flex flex-col gap-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h1",{className:"heading-text capitalize",children:n?.name}),(0,r.jsxs)(v(),{href:"/dashboard",className:"flex items-center gap-2",children:[(0,r.jsx)(y.A,{size:16}),o("backToDashboard")]})]}),(0,r.jsx)(p,{permissions:f}),(0,r.jsx)("div",{className:"px-8",children:e})]}):(0,r.jsxs)("div",{className:"error-message",children:[(0,r.jsx)("h1",{className:"text-red-500",children:o("invalidProjectIdError")}),(0,r.jsx)("p",{className:"text-neutral-700",children:o("invalidProjectIdMessage")})]})}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,7404,1658,6560,8610,5374,2198,5814,3851,8581,5841,5041,7858],()=>s(39874));module.exports=r})();