"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
// src/app.ts
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const userRouter_1 = __importDefault(require("./routes/userRouter"));
const projectRouter_1 = __importDefault(require("./routes/projectRouter"));
const questionRouter_1 = __importDefault(require("./routes/questionRouter"));
const libraryTemplateRouter_1 = __importDefault(require("./routes/libraryTemplateRouter"));
const libraryQuestionRouter_1 = __importDefault(require("./routes/libraryQuestionRouter"));
const projectUserRouter_1 = __importDefault(require("./routes/projectUserRouter"));
const questionGroupRouter_1 = __importDefault(require("./routes/questionGroupRouter"));
const answerRouter_1 = __importDefault(require("./routes/answerRouter"));
const formSubmissionRouter_1 = __importDefault(require("./routes/formSubmissionRouter"));
const cookie_parser_1 = __importDefault(require("cookie-parser"));
const reportRouter_1 = __importDefault(require("./routes/reportRouter"));
const exportFileRouter_1 = __importDefault(require("./routes/exportFileRouter"));
const tableQuestionRouter_1 = __importDefault(require("./routes/tableQuestionRouter"));
const helmet_1 = __importDefault(require("helmet"));
const libraryQuestionBlockQuestionRouter_1 = __importDefault(require("./routes/libraryQuestionBlockQuestionRouter"));
const libraryQuestionBlockQuestionGroupRouter_1 = __importDefault(require("./routes/libraryQuestionBlockQuestionGroupRouter"));
const app = (0, express_1.default)();
// Middleware
app.use((0, cors_1.default)({
    origin: process.env.CLIENT_URL,
    credentials: true,
}));
app.use((0, helmet_1.default)());
app.use(express_1.default.json());
app.use(express_1.default.urlencoded({ extended: true }));
app.use((0, cookie_parser_1.default)());
// Routes
app.use("/api/users", userRouter_1.default);
app.use("/api/projects", projectRouter_1.default);
app.use("/api/questions", questionRouter_1.default);
app.use("/api/libraries", libraryTemplateRouter_1.default);
app.use("/api/template-questions", libraryQuestionRouter_1.default);
app.use("/api/project-users", projectUserRouter_1.default);
app.use("/api/question-groups", questionGroupRouter_1.default);
app.use("/api/answers", answerRouter_1.default);
app.use("/api/form-submissions", formSubmissionRouter_1.default);
app.use("/api", reportRouter_1.default);
app.use("/api/export", exportFileRouter_1.default);
app.use("/api/question-blocks", libraryQuestionBlockQuestionRouter_1.default);
app.use("/api/question-block-group", libraryQuestionBlockQuestionGroupRouter_1.default);
app.use("/api/table-questions", tableQuestionRouter_1.default);
// Error handler
app.use((err, req, res, next) => {
    res.status(500).json({
        success: false,
        message: "Something went wrong!",
        error: process.env.NODE_ENV === "development" ? err.message : undefined,
    });
});
exports.default = app;
