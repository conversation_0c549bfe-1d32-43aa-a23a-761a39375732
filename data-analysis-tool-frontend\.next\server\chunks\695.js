"use strict";exports.id=695,exports.ids=[695],exports.modules={20174:(e,t,s)=>{s.d(t,{F:()=>a});var r=s(60687),l=s(16189),i=s(85814),n=s.n(i);s(43210);let a=({items:e})=>{let t=(0,l.usePathname)(),s=e=>t.startsWith(e);return(0,r.jsx)("div",{className:"border-y border-neutral-400 rounded-md bg-primary-500 my-4 shadow-md",children:(0,r.jsx)("div",{className:"flex items-center",children:e.map(e=>e.disabled?(0,r.jsxs)("div",{className:"flex items-center gap-2 font-medium transition-all duration-300 p-4 border-b-2 text-gray-400 cursor-not-allowed",children:[e.icon,e.label]},e.route):(0,r.jsxs)(n(),{href:e.route,className:`flex items-center gap-2 font-medium transition-all duration-300 p-4 border-b-2 text-neutral-100 ${s(e.route)?"border-neutral-100":"border-transparent hover:border-neutral-400"}`,children:[e.icon,e.label]},e.route))})})}},31207:(e,t,s)=>{s.d(t,{XV:()=>l,cZ:()=>a,ru:()=>i,yi:()=>r});let r=(e,t)=>{let s=new Map;e.forEach(e=>{let r=t.filter(t=>t.questionGroupId===e.id).sort((e,t)=>e.position-t.position);s.set(e.id,{...e,subGroups:[],question:r})});let r=[];return e.forEach(e=>{let t=s.get(e.id);if(e.parentGroupId){let r=s.get(e.parentGroupId);r&&(r.subGroups=r.subGroups||[],r.subGroups.push(t))}else r.push(t)}),r},l=(e,t)=>{let s=[];return e.forEach(e=>{let t=e=>[...e.question||[],...(e.subGroups||[]).flatMap(t)],r=t(e),l=r.length>0?Math.min(...r.map(e=>e.position)):e.order;s.push({type:"group",data:e,order:l,originalPosition:l})}),t.forEach(e=>{s.push({type:"question",data:e,order:e.position,originalPosition:e.position})}),s.sort((e,t)=>e.order===t.order?(e.originalPosition||e.order)-(t.originalPosition||t.order):e.order-t.order)},i=e=>e.filter(e=>null===e.questionGroupId||void 0===e.questionGroupId),n=e=>{let t=[];return e.forEach(e=>{t.push(e.id),e.subGroups&&e.subGroups.length>0&&t.push(...n(e.subGroups))}),t},a=(e,t=!0)=>{let s={};return n(e).forEach(e=>{s[e]=t}),s}},44305:(e,t,s)=>{s.d(t,{A:()=>d});var r=s(60687),l=s(43210),i=s(78272),n=s(14952),a=s(69396);let o=({group:e,nestingLevel:t=0,visibleQuestions:s,nestedQuestions:d,renderQuestionInput:u,errors:c,onToggleExpansion:m,isExpanded:p,expandedGroups:x,className:h=""})=>{let[b,j]=(0,l.useState)(!0),g=void 0!==p?p:b,v=e.question||[],f=v.filter(e=>s.some(t=>t.id===e.id)),y=(e.subGroups||[]).filter(e=>(e.question||[]).some(e=>s.some(t=>t.id===e.id)));return 0===f.length&&0===y.length?null:(0,r.jsxs)("div",{className:`border border-neutral-400 rounded-md bg-card shadow-sm mb-4 ${t>0?"ml-8 border-l-4 border-l-primary-300":""} ${h}`,children:[(0,r.jsx)("div",{className:"flex items-center justify-between p-4 bg-neutral-100 border-b border-neutral-300 rounded-t-md cursor-pointer hover:bg-neutral-200 dark:bg-gray-700 dark:border-gray-600 dark:hover:bg-gray-600",onClick:()=>{m?m(e.id):j(!b)},children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[g?(0,r.jsx)(i.A,{className:"h-5 w-5 text-neutral-700 dark:text-neutral-300"}):(0,r.jsx)(n.A,{className:"h-5 w-5 text-neutral-700 dark:text-neutral-300"}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-neutral-900 dark:text-neutral-100",children:e.title}),(0,r.jsxs)("span",{className:"text-sm text-neutral-700 dark:text-neutral-400",children:["(",f.length+y.reduce((e,t)=>e+(t.question?.length||0),0)," visible question",f.length+y.reduce((e,t)=>e+(t.question?.length||0),0)!==1?"s":"",")"]})]})}),g&&(0,r.jsxs)("div",{className:"p-4 space-y-4",children:[y.sort((e,t)=>e.order-t.order).map(e=>{let l=x?x[e.id]:void 0;return(0,r.jsx)(o,{group:e,nestingLevel:t+1,visibleQuestions:s,nestedQuestions:d,renderQuestionInput:u,errors:c,onToggleExpansion:m,isExpanded:l,expandedGroups:x,className:h},e.id)}),d.filter(e=>v.some(t=>t.id===e.question.id)).map(e=>(0,r.jsx)(a.A,{questionGroup:e,renderQuestionInput:u,errors:c,className:""},e.question.id))]})]})},d=o},48661:(e,t,s)=>{s.d(t,{V:()=>N,F:()=>Q});var r=s(60687),l=s(43210),i=s.n(l),n=s(24934),a=s(39390),o=s(68988),d=s(15616),u=s(93437),c=s(40347),m=s(40228),p=s(48730),x=s(47033),h=s(11860),b=s(78272),j=s(14952),g=s(13784);s(24527);var v=s(69396),f=s(31207),y=s(77618);function N({questions:e,questionGroups:t=[],contextType:s="project",onClose:N,hashedId:q}){let[w,C]=(0,l.useState)({}),[I,Q]=(0,l.useState)({}),[k,S]=(0,l.useState)([]),[E,A]=(0,l.useState)([]),[G,T]=(0,l.useState)({}),D=(0,y.c3)(),F=i().useMemo(()=>(0,f.yi)(t,e),[t,e]),$=i().useMemo(()=>(0,f.ru)(e),[e]),M=i().useMemo(()=>"project"===s?(0,f.XV)(F,$):e.map(e=>({type:"question",data:e,order:e.position,originalPosition:e.position})),[F,$,e,s]),z=e=>{T(t=>({...t,[e]:!t[e]}))},O=(e,t)=>{C(s=>({...s,[e]:t})),Q(t=>({...t,[e]:""}))},R=e=>(0,r.jsxs)("div",{className:"border border-neutral-500 dark:border-neutral-700 rounded-md p-4",children:[(0,r.jsxs)("div",{className:"mb-2",children:[(0,r.jsxs)(a.J,{className:"text-base font-medium",children:[e.label,e.isRequired&&(0,r.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),e.hint&&(0,r.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:e.hint}),I[e.id]&&(0,r.jsx)("p",{className:"text-sm text-red-500 mt-1",children:I[e.id]})]}),(0,r.jsx)("div",{className:"mt-2",children:P(e)})]},e.id),P=e=>{let t=w[e.id]??("selectmany"===e.inputType?[]:"");switch(e.inputType){case"text":if(e.hint?.includes("multiline"))return(0,r.jsx)(d.T,{value:t,onChange:t=>O(e.id,t.target.value),placeholder:e.hint||D("yourAnswer"),required:e.isRequired});return(0,r.jsx)(o.p,{value:t,onChange:t=>O(e.id,t.target.value),placeholder:e.hint||D("yourAnswer"),required:e.isRequired});case"number":return(0,r.jsx)(o.p,{type:"number",value:t,onChange:t=>O(e.id,t.target.value),placeholder:e.hint||D("yourAnswer"),required:e.isRequired});case"decimal":return(0,r.jsx)(o.p,{type:"number",step:"any",value:t,onChange:t=>O(e.id,t.target.value),placeholder:e.hint||D("yourAnswer"),required:e.isRequired});case"selectone":return(0,r.jsx)(c.z,{value:t,onValueChange:t=>O(e.id,t),required:e.isRequired,children:(0,r.jsx)("div",{className:"space-y-2",children:e.questionOptions?.map((e,t)=>(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(c.C,{value:e.label,id:`option-${e.id}`}),(0,r.jsx)(a.J,{htmlFor:`option-${e.id}`,className:"cursor-pointer",children:e.label}),e.sublabel&&(0,r.jsx)("p",{className:"text-sm text-neutral-700 ml-4",children:`(${e.sublabel})`})]},t))})});case"selectmany":return(0,r.jsx)("div",{className:"space-y-2",children:e.questionOptions?.map(s=>(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(u.S,{className:"w-5 h-5 border border-neutral-500",id:`option-${s.id}`,checked:(t||[]).includes(s.label),onCheckedChange:r=>{let l=t||[],i=r?[...l,s.label]:l.filter(e=>e!==s.label);O(e.id,i)}}),(0,r.jsxs)(a.J,{htmlFor:`option-${s.id}`,className:"cursor-pointer",children:[s.label," ",s.sublabel]})]},s.id))});case"date":return(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(o.p,{type:"date",value:t,onChange:t=>O(e.id,t.target.value),placeholder:e.hint||D("selectDate"),required:e.isRequired}),(0,r.jsx)(m.A,{className:"absolute top-3 right-3 h-4 w-4 text-muted-foreground pointer-events-none"})]});case"dateandtime":return(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(o.p,{type:"time",value:t,onChange:t=>O(e.id,t.target.value),placeholder:e.hint||D("selectTime"),required:e.isRequired}),(0,r.jsx)(p.A,{className:"absolute top-3 right-3 h-4 w-4 text-muted-foreground pointer-events-none"})]});case"table":return(0,r.jsx)(g.N,{questionId:e.id,value:t,onChange:t=>O(e.id,t),required:e.isRequired,tableLabel:e.label});default:return null}};return(0,r.jsxs)("div",{className:"bg-neutral-100 dark:bg-neutral-800 rounded-md shadow-sm border border-neutral-500 dark:border-neutral-700",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-neutral-500 dark:border-neutral-700",children:[(0,r.jsx)(n.$,{variant:"ghost",size:"icon",onClick:N,children:(0,r.jsx)(x.A,{className:"h-5 w-5"})}),(0,r.jsx)("h2",{className:"text-lg font-semibold",children:D("formPreview")}),(0,r.jsx)(n.$,{className:"cursor-pointer hover:bg-neutral-200",variant:"ghost",size:"icon",onClick:N,children:(0,r.jsx)(h.A,{className:"h-5 w-5"})})]}),(0,r.jsx)("div",{className:"p-4 md:p-6",children:(0,r.jsxs)("div",{className:"space-y-6",children:[M.map(t=>{if("group"===t.type){let s=t.data,l=G[s.id],i=e.filter(e=>e.questionGroupId===s.id),n=i.filter(e=>k.some(t=>t.id===e.id));return(0,r.jsxs)("div",{className:"border border-neutral-500 dark:border-neutral-600 rounded-lg bg-neutral-100 dark:bg-neutral-800 overflow-hidden",children:[(0,r.jsx)("div",{className:"flex items-center justify-between p-4 border-b border-neutral-500 dark:border-neutral-700 cursor-pointer hover:bg-neutral-200 dark:hover:bg-neutral-700",onClick:()=>z(s.id),children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[l?(0,r.jsx)(b.A,{className:"h-5 w-5 text-neutral-500"}):(0,r.jsx)(j.A,{className:"h-5 w-5 text-neutral-500"}),(0,r.jsx)("h3",{className:"text-lg font-semibold dark:text-neutral-100",children:s.title}),(0,r.jsxs)("span",{className:"text-sm text-neutral-700 dark:text-neutral-400",children:["(",n.length," ",D("visibleQuestion"),1!==n.length?D("s"):"",")"]})]})}),l&&(0,r.jsx)("div",{className:"p-4 space-y-4",children:E.filter(e=>i.some(t=>t.id===e.question.id)).map(e=>(0,r.jsx)(v.A,{questionGroup:e,renderQuestionInput:P,errors:I,className:""},e.question.id))})]},`group-${s.id}`)}{let e=t.data;if(!k.some(t=>t.id===e.id))return null;let s=E.find(t=>t.question.id===e.id);return s?(0,r.jsx)(v.A,{questionGroup:s,renderQuestionInput:P,errors:I,className:""},e.id):R(e)}}),0===e.length&&(0,r.jsx)("div",{className:"text-center py-12",children:(0,r.jsx)("p",{className:"text-muted-foreground",children:D("noFormQuestionsYet")})}),e.length>0&&0===k.length&&(0,r.jsx)("div",{className:"text-center py-12",children:(0,r.jsx)("p",{className:"text-muted-foreground",children:D("noVisibleQuestions")})})]})})]})}var q=s(41862),w=s(44305),C=s(29494),I=s(75531);function Q({projectId:e,onClose:t,hashedId:s}){let[i,b]=(0,l.useState)({}),[j,N]=(0,l.useState)({}),[Q,k]=(0,l.useState)([]),[S,E]=(0,l.useState)([]),[A,G]=(0,l.useState)({}),T=(0,y.c3)(),{data:D,isLoading:F,isError:$,error:M}=(0,C.I)({queryKey:["formBuilderData",e],queryFn:()=>(0,I.gf)({projectId:e}),enabled:!!e}),z=(0,l.useMemo)(()=>{if(!D?.items)return[];let e=[];return D.items.forEach(t=>{"question"===t.type?e.push(t):"group"===t.type&&t.questions&&e.push(...t.questions)}),e.sort((e,t)=>e.position-t.position)},[D]),O=(0,l.useMemo)(()=>D?.items?D.items.filter(e=>"group"===e.type).map(e=>({...e,question:e.questions||[]})):[],[D]),R=(0,l.useMemo)(()=>(0,f.yi)(O,z),[O,z]),P=(0,l.useMemo)(()=>(0,f.ru)(z),[z]),K=(0,l.useMemo)(()=>(0,f.XV)(R,P),[R,P]),L=e=>{G(t=>({...t,[e]:!t[e]}))},B=(e,t)=>{b(s=>({...s,[e]:t})),N(t=>({...t,[e]:""}))},V=e=>(0,r.jsxs)("div",{className:"border border-neutral-500 dark:border-neutral-700 rounded-md p-4",children:[(0,r.jsxs)("div",{className:"mb-2",children:[(0,r.jsxs)(a.J,{className:"text-base font-medium",children:[e.label,e.isRequired&&(0,r.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),e.hint&&(0,r.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:e.hint}),j[e.id]&&(0,r.jsx)("p",{className:"text-sm text-red-500 mt-1",children:j[e.id]})]}),(0,r.jsx)("div",{className:"mt-2",children:U(e)})]},e.id),U=e=>{let t=i[e.id]??("selectmany"===e.inputType?[]:"");switch(e.inputType){case"text":if(e.hint?.includes("multiline"))return(0,r.jsx)(d.T,{value:t,onChange:t=>B(e.id,t.target.value),placeholder:e.hint||T("yourAnswer"),required:e.isRequired});return(0,r.jsx)(o.p,{value:t,onChange:t=>B(e.id,t.target.value),placeholder:e.hint||T("yourAnswer"),required:e.isRequired});case"number":return(0,r.jsx)(o.p,{type:"number",value:t,onChange:t=>B(e.id,t.target.value),placeholder:e.hint||T("yourAnswer"),required:e.isRequired});case"decimal":return(0,r.jsx)(o.p,{type:"number",step:"any",value:t,onChange:t=>B(e.id,t.target.value),placeholder:e.hint||T("yourAnswer"),required:e.isRequired});case"selectone":return(0,r.jsx)(c.z,{value:t,onValueChange:t=>B(e.id,t),required:e.isRequired,children:(0,r.jsx)("div",{className:"space-y-2",children:e.questionOptions?.map((e,t)=>(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(c.C,{value:e.label,id:`option-${e.id}`}),(0,r.jsx)(a.J,{htmlFor:`option-${e.id}`,className:"cursor-pointer",children:e.label}),e.sublabel&&(0,r.jsx)("p",{className:"text-sm text-neutral-700 ml-4",children:`(${e.sublabel})`})]},t))})});case"selectmany":return(0,r.jsx)("div",{className:"space-y-2",children:e.questionOptions?.map(s=>(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(u.S,{className:"w-5 h-5 border border-neutral-500",id:`option-${s.id}`,checked:(t||[]).includes(s.label),onCheckedChange:r=>{let l=t||[],i=r?[...l,s.label]:l.filter(e=>e!==s.label);B(e.id,i)}}),(0,r.jsxs)(a.J,{htmlFor:`option-${s.id}`,className:"cursor-pointer",children:[s.label," ",s.sublabel]})]},s.id))});case"date":return(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(o.p,{type:"date",value:t,onChange:t=>B(e.id,t.target.value),placeholder:e.hint||T("selectDate"),required:e.isRequired}),(0,r.jsx)(m.A,{className:"absolute top-3 right-3 h-4 w-4 text-muted-foreground pointer-events-none"})]});case"dateandtime":return(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(o.p,{type:"time",value:t,onChange:t=>B(e.id,t.target.value),placeholder:e.hint||T("selectTime"),required:e.isRequired}),(0,r.jsx)(p.A,{className:"absolute top-3 right-3 h-4 w-4 text-muted-foreground pointer-events-none"})]});case"table":return(0,r.jsx)(g.N,{questionId:e.id,value:t,onChange:t=>B(e.id,t),required:e.isRequired,tableLabel:e.label});default:return null}};return F?(0,r.jsxs)("div",{className:"bg-neutral-100 dark:bg-neutral-800 rounded-md shadow-sm border border-neutral-500 dark:border-neutral-700",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-neutral-500 dark:border-neutral-700",children:[(0,r.jsx)(n.$,{variant:"ghost",size:"icon",onClick:t,children:(0,r.jsx)(x.A,{className:"h-5 w-5"})}),(0,r.jsx)("h2",{className:"text-lg font-semibold",children:T("formPreview")}),(0,r.jsx)(n.$,{className:"cursor-pointer hover:bg-neutral-200",variant:"ghost",size:"icon",onClick:t,children:(0,r.jsx)(h.A,{className:"h-5 w-5"})})]}),(0,r.jsx)("div",{className:"p-4 md:p-6",children:(0,r.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(q.A,{className:"h-6 w-6 animate-spin"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:T("loadingForm")})]})})})]}):$?(0,r.jsxs)("div",{className:"bg-neutral-100 dark:bg-neutral-800 rounded-md shadow-sm border border-neutral-500 dark:border-neutral-700",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-neutral-500 dark:border-neutral-700",children:[(0,r.jsx)(n.$,{variant:"ghost",size:"icon",onClick:t,children:(0,r.jsx)(x.A,{className:"h-5 w-5"})}),(0,r.jsx)("h2",{className:"text-lg font-semibold",children:T("formPreview")}),(0,r.jsx)(n.$,{className:"cursor-pointer hover:bg-neutral-200",variant:"ghost",size:"icon",onClick:t,children:(0,r.jsx)(h.A,{className:"h-5 w-5"})})]}),(0,r.jsx)("div",{className:"p-4 md:p-6",children:(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("p",{className:"text-red-500 mb-2",children:T("errorLoadingForm")}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:M instanceof Error?M.message:T("unknownError")})]})})]}):(0,r.jsxs)("div",{className:"bg-neutral-100 dark:bg-neutral-800 rounded-md shadow-sm border border-neutral-500 dark:border-neutral-700",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-neutral-500 dark:border-neutral-700",children:[(0,r.jsx)(n.$,{variant:"ghost",size:"icon",onClick:t,children:(0,r.jsx)(x.A,{className:"h-5 w-5"})}),(0,r.jsx)("h2",{className:"text-lg font-semibold",children:T("formPreview")}),(0,r.jsx)(n.$,{className:"cursor-pointer hover:bg-neutral-200",variant:"ghost",size:"icon",onClick:t,children:(0,r.jsx)(h.A,{className:"h-5 w-5"})})]}),(0,r.jsx)("div",{className:"p-4 md:p-6",children:(0,r.jsxs)("div",{className:"space-y-6",children:[K.map(e=>{if("group"===e.type){let t=e.data,s=A[t.id];return(0,r.jsx)(w.A,{group:t,nestingLevel:0,visibleQuestions:Q,nestedQuestions:S,renderQuestionInput:U,errors:j,onToggleExpansion:L,isExpanded:s,expandedGroups:A,className:""},`group-${t.id}`)}{let t=e.data;if(!Q.some(e=>e.id===t.id))return null;let s=S.find(e=>e.question.id===t.id);return s?(0,r.jsx)(v.A,{questionGroup:s,renderQuestionInput:U,errors:j,className:""},t.id):V(t)}}),0===z.length&&(0,r.jsx)("div",{className:"text-center py-12",children:(0,r.jsx)("p",{className:"text-muted-foreground",children:T("noFormQuestionsYet")})}),z.length>0&&0===Q.length&&(0,r.jsx)("div",{className:"text-center py-12",children:(0,r.jsx)("p",{className:"text-muted-foreground",children:T("noVisibleQuestions")})})]})})]})}},73678:(e,t,s)=>{s.d(t,{R:()=>i});var r=s(60687);s(43210);var l=s(38587);let i=({showModal:e,onClose:t,onConfirm:s,title:i,description:n,confirmButtonText:a,cancelButtonText:o,confirmButtonClass:d,children:u})=>(0,r.jsxs)(l.A,{isOpen:e,onClose:t,className:"p-6 rounded-md max-w-xl",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-neutral-700",children:i}),(0,r.jsx)("div",{className:"text-neutral-700 mt-2",children:n}),u&&(0,r.jsx)("div",{className:"mt-6 space-y-4",children:u}),(0,r.jsxs)("div",{className:"flex justify-end gap-4 mt-6",children:[(0,r.jsx)("button",{className:"btn-outline",onClick:t,type:"button",children:o||"Cancel"}),(0,r.jsx)("button",{className:`font-medium rounded-md shadow-md text-neutral-100 flex items-center justify-center gap-2 px-4 py-2 active:scale-95 transition-all duration-300 ${d}`,onClick:s,type:"button",children:a})]})]})},88678:(e,t,s)=>{s.d(t,{o:()=>eh});var r=s(60687),l=s(43210),i=s.n(l),n=s(57601),a=s(51358),o=s(62478),d=s(81381),u=s(17804),c=s(96362),m=s(57175),p=s(77618);let x=({question:e,onEdit:t,onDelete:s,onDuplicate:l,isSelected:i=!1,onToggleSelect:n,selectionMode:x=!1})=>{let{attributes:h,listeners:b,setNodeRef:j,transform:g,transition:v,isDragging:f}=(0,a.gl)({id:e.id,data:{type:"question",questionId:e.id,questionGroupId:"questionGroupId"in e?e.questionGroupId:void 0}}),y={transform:o.Ks.Transform.toString(g),transition:v,opacity:f?.5:1},N=(0,p.c3)();return(0,r.jsx)("div",{ref:j,style:y,className:"border border-neutral-400 rounded-md bg-card shadow-sm",children:(0,r.jsxs)("div",{className:"flex items-center p-4",children:[x&&(0,r.jsx)("div",{className:"mr-2",children:(0,r.jsx)("input",{type:"checkbox",checked:i,onChange:()=>n&&n(),className:"h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"})}),(0,r.jsx)("div",{...h,...b,className:"cursor-move mr-3 hover:text-primary",children:(0,r.jsx)(d.A,{className:"h-5 w-5"})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h3",{className:"text-xl font-semibold",children:e.label||(0,r.jsx)("span",{className:"text-muted-foreground italic",children:N("emptyQuestion")})}),e.hint&&(0,r.jsx)("p",{className:"text-sm sub-text mt-1",children:e.hint})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)("button",{onClick:e=>{e.stopPropagation(),l()},title:N("duplicate"),className:"cursor-pointer p-2 rounded-full hover:bg-primary-100 text-neutral-700 hover:text-primary-500 transition-colors",children:(0,r.jsx)(u.A,{size:16})}),(0,r.jsx)("button",{onClick:e=>{e.stopPropagation(),s()},title:N("delete"),className:"cursor-pointer p-2 rounded-full hover:bg-destructive/10 text-neutral-700 hover:text-destructive transition-colors",children:(0,r.jsx)(c.A,{size:16})}),(0,r.jsx)("button",{onClick:e=>{e.stopPropagation(),t()},title:N("edit"),className:"cursor-pointer p-2 rounded-full hover:bg-primary-100 text-neutral-700 hover:text-primary-500 transition-colors",children:(0,r.jsx)(m.A,{size:16})})]})]})})};var h=s(78272),b=s(14952),j=s(80675),g=s(96474),v=s(63143);let f=({id:e,title:t,questions:s,subGroups:i=[],parentGroupId:u,nestingLevel:m=0,onEditGroup:y,onDeleteGroup:N,onAddQuestionToGroup:q,onEditQuestion:w,onDeleteQuestion:C,onDuplicateQuestion:I,onReorderQuestions:Q,onMoveQuestionBetweenGroups:k,onMoveGroupInsideGroup:S,isEditing:E=!1,onStartEditing:A,onSaveGroupName:G,onCancelEditing:T,editingName:D="",onEditingNameChange:F,selectionMode:$=!1,isDraggable:M=!0,selectedQuestionIds:z=[],onToggleQuestionSelect:O,onCreateSubgroup:R})=>{let[P,K]=(0,l.useState)(!0),L=s.filter(e=>z.includes(e.id)),{attributes:B,listeners:V,setNodeRef:U,transform:H,transition:J,isDragging:W}=(0,a.gl)({id:`group-${e}`,data:{type:"group",groupId:e,parentGroupId:u}}),{setNodeRef:_,isOver:X}=(0,n.zM)({id:`group-drop-${e}`,data:{type:"group-drop",groupId:e}}),Y={transform:o.Ks.Transform.toString(H),transition:J,opacity:W?.5:1},Z=(0,p.c3)(),ee=(0,n.FR)((0,n.MS)(n.AN,{activationConstraint:{distance:8}}),(0,n.MS)(n.uN));return(0,r.jsxs)("div",{ref:e=>{U(e),_(e)},style:Y,className:`border border-neutral-400 rounded-md bg-card shadow-sm mb-4 ${X?"ring-2 ring-primary-500 ring-opacity-50":""} ${m>0?"ml-8 border-l-4 border-l-primary-300":""}`,children:[(0,r.jsxs)("div",{className:"flex items-center p-4 bg-neutral-100 border-b border-neutral-300 rounded-t-md",children:[M&&(0,r.jsx)("div",{...B,...V,className:"cursor-move mr-2 hover:text-primary-500 transition-colors",title:"Drag to reorder group",children:(0,r.jsx)(d.A,{className:"h-5 w-5"})}),(0,r.jsx)("button",{onClick:()=>K(!P),className:"mr-2 text-neutral-700 hover:text-primary-500 transition-colors","aria-label":Z(P?"collapseGroup":"expandGroup"),children:P?(0,r.jsx)(h.A,{className:"h-5 w-5"}):(0,r.jsx)(b.A,{className:"h-5 w-5"})}),E?(0,r.jsx)("div",{className:"flex-1 mr-4",children:(0,r.jsx)("input",{type:"text",value:D,onChange:e=>F&&F(e.target.value),className:"w-full p-2 border border-gray-300 rounded",autoFocus:!0,onKeyDown:t=>{"Enter"===t.key?G&&G(e):"Escape"===t.key&&T&&T()},placeholder:Z("enterGroupName")})}):(0,r.jsx)("h3",{className:"flex-1 font-medium text-lg cursor-pointer hover:text-primary-500",onClick:()=>A&&A(e,t),title:Z("clickToEditGroupName"),children:t}),(0,r.jsx)("div",{className:"flex items-center space-x-3",children:E?(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)("button",{onClick:()=>T&&T(),title:Z("cancelEditing"),className:"cursor-pointer px-3 py-1 rounded btn-outline",children:Z("cancel")}),(0,r.jsx)("button",{onClick:()=>G&&G(e),title:Z("saveGroupName"),className:"cursor-pointer px-3 py-1 rounded btn-primary",children:Z("save")})]}):(0,r.jsxs)(r.Fragment,{children:[$&&L.length>0&&(0,r.jsxs)("button",{onClick:()=>{L.length>0&&R&&R(e,L.map(e=>e.id))},title:`Create Subgroup (${L.length} questions)`,className:"cursor-pointer px-3 py-1 rounded btn-primary text-sm flex items-center gap-1",children:[(0,r.jsx)(j.A,{size:14}),"Create Subgroup (",L.length,")"]}),(0,r.jsx)("button",{onClick:()=>q(e),title:Z("addQuestionToGroup"),className:"cursor-pointer p-2 rounded-full hover:bg-primary-100 text-neutral-700 hover:text-primary-500 transition-colors",children:(0,r.jsx)(g.A,{size:16})}),(0,r.jsx)("button",{onClick:()=>A&&A(e,t),title:Z("editGroupName"),className:"cursor-pointer p-2 rounded-full hover:bg-primary-100 text-neutral-700 hover:text-primary-500 transition-colors",children:(0,r.jsx)(v.A,{size:16})}),(0,r.jsx)("button",{onClick:()=>N(e),title:Z("deleteGroup"),className:"cursor-pointer p-2 rounded-full hover:bg-destructive/10 text-neutral-700 hover:text-destructive transition-colors",children:(0,r.jsx)(c.A,{size:16})})]})})]}),P&&(0,r.jsxs)("div",{className:"p-4 space-y-4",children:[i&&i.length>0&&(0,r.jsx)("div",{className:"space-y-4",children:i.sort((e,t)=>e.order-t.order).map(t=>(0,r.jsx)(f,{id:t.id,title:t.title,questions:t.question||[],subGroups:t.subGroups,parentGroupId:e,nestingLevel:m+1,onEditGroup:y,onDeleteGroup:N,onAddQuestionToGroup:q,onEditQuestion:w,onDeleteQuestion:C,onDuplicateQuestion:I,onReorderQuestions:Q,onMoveQuestionBetweenGroups:k,onMoveGroupInsideGroup:S,selectionMode:$,isDraggable:M,selectedQuestionIds:z,onToggleQuestionSelect:O,onCreateSubgroup:R},t.id))}),s.length>0?(0,r.jsx)(n.Mp,{sensors:ee,collisionDetection:n.fp,onDragEnd:e=>{let{active:t,over:r}=e;if(!r||t.id===r.id)return;let l=t.data.current,i=r.data.current;if(l?.type==="question"&&i?.type==="question"&&Q){let e=[...s].sort((e,t)=>e.position-t.position),l=e.findIndex(e=>e.id===t.id),i=e.findIndex(e=>e.id===r.id);if(-1===l||-1===i)return;Q((0,a.be)(e,l,i).map((e,t)=>({id:Number(e.id),position:t+1})))}if(l?.type==="question"&&i?.type==="group-drop"&&k){let e=Number(t.id),s=l.questionGroupId||null,r=i.groupId;s!==r&&k(e,s,r)}if(l?.type==="group"&&i?.type==="group-drop"&&S){let e=l.groupId,t=i.groupId;e!==t&&S(e,t)}},children:(0,r.jsx)(a.gB,{items:s.map(e=>e.id),strategy:a._G,children:s.sort((e,t)=>e.position-t.position).map(e=>(0,r.jsx)("div",{className:"mb-4",children:(0,r.jsx)(x,{question:e,onEdit:()=>w(e),onDelete:()=>C(e),onDuplicate:()=>I(e),selectionMode:$,isSelected:z.includes(e.id),onToggleSelect:()=>O&&O(e.id)})},e.id))})}):(!i||0===i.length)&&(0,r.jsxs)("div",{className:"text-center py-4 text-neutral-500",children:[Z("noQuestionsInGroup"),"              "]})]})]})};var y=s(13861),N=s(82080),q=s(1303),w=s(38587),C=s(27605),I=s(68292),Q=s(40480);let k={text:"Text",number:"Number",decimal:"Decimal",selectone:"Select one",selectmany:"Select many",date:"Date",dateandtime:"Date and time",table:"Table"};Object.keys(k);var S=s(43782),E=s(8693),A=s(54050),G=s(75531),T=s(29494);let D=({contextType:e,contextId:t,value:s,onChange:l,currentQuestionId:i,placeholder:n="Select next question (optional)"})=>{let{data:a=[],isLoading:o,error:d}=(0,T.I)({queryKey:"project"===e?["questions",t]:"template"===e?["templateQuestions",t]:["questionBlockQuestions",t],queryFn:()=>"project"===e?(0,G.K4)({projectId:t}):"template"===e?(0,G.ej)({templateId:t}):"questionBlock"===e?(0,G.dI)():[],enabled:!!t}),u=a.filter(e=>e.id!==i),c=u.find(e=>e.id===s);return(0,r.jsxs)("div",{className:"flex flex-col gap-1",children:[(0,r.jsxs)("select",{value:s||"",onChange:e=>{let t=e.target.value;l(t?parseInt(t):null)},className:"input-field text-sm",disabled:o,children:[(0,r.jsx)("option",{value:"",children:o?"Loading questions...":n}),u.map(e=>(0,r.jsx)("option",{value:e.id,children:e.label||`Question ${e.id}`},e.id))]}),c&&(0,r.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:["Type: ",c.inputType]})]})},F=({contextType:e,contextId:t,currentQuestionId:s,inputType:i})=>{let{control:n,register:a,formState:{errors:o},setValue:d,watch:u}=(0,C.xW)(),{fields:m,append:p,remove:x}=(0,C.jz)({control:n,name:"questionOptions"});(0,l.useEffect)(()=>{0===m.length&&p({label:"",sublabel:"",code:"",nextQuestionId:null})},[m,p]);let h="selectone"===i||"selectmany"===i;return(0,r.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,r.jsx)("label",{className:"label-text",children:"Options"}),(0,r.jsxs)("div",{className:"flex flex-col gap-2",children:[m.map((l,i)=>(0,r.jsxs)("div",{className:"border  border-gray-400 rounded-lg p-3 space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("input",{...a(`questionOptions.${i}.label`),placeholder:`Option ${i+1}`,className:"input-field flex-1 min-w-[150px]"}),(0,r.jsx)("input",{...a(`questionOptions.${i}.sublabel`),placeholder:"Sub Options",className:"input-field flex-1 min-w-[150px]"}),(0,r.jsx)("input",{...a(`questionOptions.${i}.code`),placeholder:"Code",className:"w-28 input-field"}),(0,r.jsx)("button",{type:"button",onClick:()=>x(i),className:"p-2 rounded-full hover:bg-red-500/10 text-neutral-700 hover:text-red-500 transition-colors cursor-pointer duration-300",children:(0,r.jsx)(c.A,{size:16})})]}),h&&e&&t&&(0,r.jsxs)("div",{className:"ml-2",children:[(0,r.jsx)("label",{className:"text-xs text-gray-600 mb-1 block",children:"Next Question (when this option is selected):"}),(0,r.jsx)(D,{contextType:e,contextId:t,currentQuestionId:s,value:u(`questionOptions.${i}.nextQuestionId`),onChange:e=>{d(`questionOptions.${i}.nextQuestionId`,e)},placeholder:"No follow-up question"})]})]},i)),o.questionOptions&&(0,r.jsx)("p",{className:"text-sm text-red-500",children:`${o.questionOptions.root?.message}`}),(0,r.jsxs)("button",{type:"button",onClick:()=>p({label:"",sublabel:"",code:"",nextQuestionId:null}),className:"btn-outline mt-2 flex items-center justify-center gap-2",children:[(0,r.jsx)(g.A,{size:16}),"Add Option"]})]})]})};var $=s(63442);let M=e=>["selectone","selectmany"].includes(e);var z=s(45880);let O=Object.keys(k),R=z.z.object({label:z.z.string().min(1,"Question name is required"),inputType:z.z.enum(O),hint:z.z.string().optional(),placeholder:z.z.string().optional(),questionOptions:z.z.array(z.z.object({label:z.z.string(),sublabel:z.z.string().optional(),code:z.z.string(),nextQuestionId:z.z.number().optional().nullable()})).optional()}).superRefine((e,t)=>{M(e.inputType)});var P=s(86429);let K=()=>(0,r.jsx)("div",{className:"fixed top-0 left-0 h-screen w-screen bg-neutral-900/20 z-50 flex items-center justify-center",onClick:e=>{e.stopPropagation()},children:(0,r.jsx)(P.A,{})});var L=s(24934),B=s(68988),V=s(39390),U=s(12597),H=s(15695),J=s(96752),W=s(96241);function _({projectId:e,onTableCreated:t,isInModal:s=!1,isEditMode:n=!1,existingTableData:a}){let{toast:o}=function(){let[e,t]=(0,l.useState)([]);return{toast:e=>{t(t=>[...t,e]),setTimeout(()=>{t(t=>t.filter(t=>t!==e))},3e3)},toasts:e}}(),[d,u]=(0,l.useState)(a?.label||""),[m,p]=(0,l.useState)(()=>{if(a?.tableColumns){let e=[];return a.tableColumns.forEach(t=>{let s={id:`col-${t.id}`,columnName:t.columnName,level:0,parentColumnId:void 0};e.push(s),t.childColumns&&t.childColumns.length>0&&(console.error(`Processing ${t.childColumns.length} child columns for parent "${t.columnName}"`),t.childColumns.forEach(s=>{let r={id:`col-${s.id}`,columnName:s.columnName,level:1,parentId:`col-${t.id}`,parentColumnId:t.id};e.push(r)}))}),e.length>0?e:[{id:"col-1",columnName:"",level:0}]}return[{id:"col-1",columnName:"",level:0}]}),[x,h]=(0,l.useState)(()=>a?.tableRows&&a.tableRows.length>0?[...a.tableRows].sort((e,t)=>e.id-t.id).map(e=>({id:e.id,rowsName:e.rowsName})):[{rowsName:"Row 1"}]),[b,j]=(0,l.useState)(!1),[v,f]=(0,l.useState)(!0),N=()=>`col-${Date.now()}-${Math.floor(1e3*Math.random())}`,q=()=>{p([...m,{id:N(),columnName:"",level:0}])},w=e=>{let t=C(e);if(!t)return;if(t.level>0)return void o({title:"Cannot add child column",description:"Cannot create more than 2 levels of nested columns (parent → child → grandchild)",variant:"destructive"});if(m.filter(t=>t.parentId===e).length>=2)return void o({title:"Cannot add more child columns",description:`Parent column "${t.columnName||"Unnamed"}" cannot have more than 2 child columns`,variant:"destructive"});let s=e.match(/^col-(\d+)/),r=s?parseInt(s[1],10):void 0,l={id:N(),columnName:"",parentId:e,level:t.level+1,parentColumnId:r},i=[...m],n=I(e);if(-1===n||n===m.findIndex(t=>t.id===e)){let t=m.findIndex(t=>t.id===e);i.splice(t+1,0,l)}else i.splice(n+1,0,l);p(i)},C=e=>m.find(t=>t.id===e),I=e=>{let t=m.findIndex(t=>t.id===e);if(-1===t)return -1;let s=t,r=!1;for(let l=t+1;l<m.length;l++)if(m[l].parentId===e)s=l,r=!0;else if(Q(m[l],e))s=l,r=!0;else break;return r?s:t},Q=(e,t)=>{if(e.parentId===t)return!0;if(!e.parentId)return!1;let s=C(e.parentId);return!!s&&Q(s,t)},k=()=>{let e=x.length+1;h([...x,{rowsName:`Row ${e}`}])},S=e=>{if(m.length<=1)return;let t=new Set([e]);m.forEach(s=>{Q(s,e)&&t.add(s.id)});let s=m.filter(e=>!t.has(e.id));0===s.length&&s.push({id:N(),columnName:"",level:0}),p(s)},E=e=>{let t=[...x];t.splice(e,1),h(t)},A=(e,t)=>{p(m.map(s=>s.id===e?{...s,columnName:t}:s))},G=(e,t)=>{let s=[...x];s[e]={...s[e],rowsName:t},h(s);let r=document.querySelectorAll(".row-input");r&&r[e]&&(t.trim()?r[e].classList.remove("border-red-500"):r[e].classList.add("border-red-500"))},T=()=>{let e=n||a?.id;console.log("Is editing mode:",e);let t=new Map;e&&a?.tableColumns&&a.tableColumns.forEach(e=>{let s=`col-${e.id}`;t.set(s,e.id)});let s=new Map;m.forEach(e=>{e.parentId&&(s.has(e.parentId)||s.set(e.parentId,[]),s.get(e.parentId)?.push(e.id))}),e&&a?.tableColumns&&m.forEach(e=>{if(e.columnName.trim()){let s=e.id.match(/^col-(\d+)/);if(s&&s[1]){let r=parseInt(s[1],10);a.tableColumns.find(e=>e.id===r)&&t.set(e.id,r)}}});let r=Math.max(...Array.from(t.values(),e=>e||0),...a?.tableColumns?.map(e=>e.id)||[0],0)+1;m.forEach(e=>{e.columnName.trim()&&!t.has(e.id)&&t.set(e.id,r++)});let l=[],i=new Map,d=Math.max(...m.map(e=>e.level||0),0);for(let s=0;s<=d;s++)m.filter(e=>e.level===s&&e.columnName.trim()).forEach(s=>{let r=t.get(s.id),n={columnName:s.columnName.trim()};if(e&&r&&(n.id=r),s.parentId)if(e){m.find(e=>e.id===s.parentId);let e=t.get(s.parentId);e?n.parentColumnId=e:(console.warn(`Could not find parent DB ID for column "${s.columnName}" (parentId: ${s.parentId})`),o({title:"Warning",description:`Column "${s.columnName}" had a missing parent reference and was converted to a top-level column.`,variant:"destructive"}))}else{let e=i.get(s.parentId);void 0!==e?n.parentColumnId=e+1:(console.warn(`Could not find parent position for column "${s.columnName}" (parentId: ${s.parentId})`),o({title:"Warning",description:`Column "${s.columnName}" had a missing parent reference and was converted to a top-level column.`,variant:"destructive"}))}let a=l.length;l.push(n),i.set(s.id,a)});let u=l.filter(t=>{if(void 0===t.parentColumnId)return!1;if(e){if(t.parentColumnId<=0)return!0}else{if(t.parentColumnId<=0||t.parentColumnId>l.length)return!0;let e=l[t.parentColumnId-1];if(e&&void 0!==e.parentColumnId)return!0}return!1});return u.length>0&&(console.error("Found invalid parent column references:",u),u.forEach(e=>{e.parentColumnId=void 0}),o({title:"Warning",description:`Fixed ${u.length} invalid column relationships. Some child columns were converted to top-level columns.`,variant:"destructive"})),l},D=async s=>{if(s&&s.preventDefault(),!d.trim()){console.log("Validation failed: Empty label"),o({title:"Error",description:"Please enter a table label",variant:"destructive"});return}let r=m.filter(e=>e.columnName.trim()),l=x.filter(e=>e.rowsName.trim()),i=document.querySelectorAll(".row-input");if(x.forEach((e,t)=>{i&&i[t]&&(e.rowsName.trim()?i[t].classList.remove("border-red-500"):i[t].classList.add("border-red-500"))}),0===r.length){console.log("Validation failed: No valid columns"),o({title:"Error",description:"Please add at least one column with a name",variant:"destructive"});return}if(0===l.length){console.log("Validation failed: No valid rows"),o({title:"Error",description:"Please add at least one row with a name",variant:"destructive"});return}console.log("Validation passed, proceeding with submission"),j(!0);try{let s;console.log("Preparing columns for submission");let r=T();console.log("Prepared API columns:",r);let i=[...l].sort((e,t)=>e.id&&t.id?e.id-t.id:e.id?-1:t.id?1:l.indexOf(e)-l.indexOf(t)).map(e=>{let t={rowsName:e.rowsName.trim()};return(n||a?.id)&&e.id&&a?.tableRows?.some(t=>t.id===e.id)&&(t.id=e.id),t});a?.id?(s=await (0,H.am)(a.id,d.trim(),r,i),o({title:"Success",description:"Table question updated successfully"})):(s=await (0,H.ZR)(d.trim(),e,r,i),o({title:"Success",description:"Table question created successfully"}),u(""),p([{id:N(),columnName:"",level:0}]),h([])),t&&s?.id&&t(s.id)}catch(t){console.error("Error with table operation:",t);let e=a?.id?"Failed to update table question":"Failed to create table question";t.response?.data?.message?e=t.response.data.message:t.message&&(e=t.message),o({title:"Error",description:e,variant:"destructive"})}finally{j(!1)}},F=i().useRef(null);return(0,r.jsxs)("div",{ref:F,className:"space-y-6 p-4 border border-gray-200 rounded-md w-full table-question-builder",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("h3",{className:"text-lg font-medium",children:n||a?.id?"Edit Table Question":"Create Table Question"}),(0,r.jsx)(L.$,{type:"button",variant:"outline",size:"sm",onClick:()=>f(!v),className:"flex items-center gap-1",children:v?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(U.A,{className:"h-4 w-4"}),"Hide Preview"]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(y.A,{className:"h-4 w-4"}),"Show Preview"]})})]}),(0,r.jsxs)("div",{className:"bg-blue-50 p-3 rounded-md text-sm text-blue-800 border border-blue-200",children:[(0,r.jsx)("p",{className:"font-medium mb-1",children:"Table Structure Guidelines:"}),(0,r.jsxs)("ul",{className:"list-disc pl-5 space-y-1",children:[(0,r.jsxs)("li",{children:["Create multiple ",(0,r.jsx)("span",{className:"font-medium",children:"parent columns"})," ",'using the "Add Top-Level Column" button']}),(0,r.jsxs)("li",{children:["Add up to 2 ",(0,r.jsx)("span",{className:"font-medium",children:"child columns"}),' under each parent using the "+" button']}),(0,r.jsx)("li",{children:"Child columns cannot have their own children (maximum 2 levels)"})]})]}),s?(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(V.J,{htmlFor:"table-label",children:"Table Label"}),(0,r.jsx)(B.p,{id:"table-label",value:d,onChange:e=>u(e.target.value),placeholder:"Enter table question label",required:!0})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 max-h-[60vh] overflow-y-auto",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(V.J,{children:"Columns"}),m.map(e=>(0,r.jsxs)("div",{className:(0,W.cn)("flex items-center gap-2 p-2 rounded-md",0===e.level?"bg-gray-50":"bg-white border-l-2 border-gray-300"),style:{marginLeft:`${20*e.level}px`},children:[(0,r.jsxs)("div",{className:"flex-1 flex items-center gap-2",children:[e.level>0&&(0,r.jsx)("div",{className:"w-4 h-4 flex items-center justify-center",children:(0,r.jsx)("div",{className:"w-2 h-2 bg-blue-400 rounded-full"})}),(0,r.jsx)(B.p,{value:e.columnName,onChange:t=>A(e.id,t.target.value),placeholder:`${0===e.level?"Parent":"Child"} Column`,className:(0,W.cn)("flex-1",0===e.level?"border-blue-200 bg-white":"border-dashed")}),0===e.level&&(0,r.jsx)("div",{className:"text-xs text-blue-500 font-medium",children:"Parent"}),e.level>0&&(0,r.jsx)("div",{className:"text-xs text-gray-500 font-medium",children:"Child"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(L.$,{type:"button",variant:"ghost",size:"icon",onClick:()=>w(e.id),title:e.level>0?"Child columns cannot have their own children":m.filter(t=>t.parentId===e.id).length>=2?"Maximum 2 child columns allowed":"Add child column",disabled:e.level>0||m.filter(t=>t.parentId===e.id).length>=2,children:(0,r.jsx)(g.A,{className:(0,W.cn)("h-4 w-4",(e.level>0||m.filter(t=>t.parentId===e.id).length>=2)&&"text-gray-300")})}),(0,r.jsx)(L.$,{type:"button",variant:"ghost",size:"icon",onClick:()=>S(e.id),disabled:m.length<=1,title:"Remove column",children:(0,r.jsx)(c.A,{className:"h-4 w-4"})})]})]},e.id)),(0,r.jsxs)(L.$,{type:"button",variant:"outline",size:"sm",onClick:q,className:"mt-2",children:[(0,r.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Add Top-Level Column"]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(V.J,{children:"Rows"}),x.map((e,t)=>(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(B.p,{value:e.rowsName,onChange:e=>G(t,e.target.value),placeholder:`Row ${t+1}`,className:`row-input ${!e.rowsName.trim()?"border-red-500":""}`}),(0,r.jsx)(L.$,{type:"button",variant:"ghost",size:"icon",onClick:()=>E(t),disabled:!1,children:(0,r.jsx)(c.A,{className:"h-4 w-4"})})]},t)),(0,r.jsxs)(L.$,{type:"button",variant:"outline",size:"sm",onClick:k,className:"mt-2",children:[(0,r.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Add Row"]})]})]}),v&&(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(V.J,{children:"Table Preview"}),(0,r.jsx)("div",{className:"border rounded-md p-4 overflow-x-auto max-h-[300px] overflow-y-auto",children:(0,r.jsxs)(J.XI,{children:[(0,r.jsx)(J.A0,{children:(()=>{let e=Math.max(...m.map(e=>e.level),0),t=[];t.push((0,r.jsx)(J.Hj,{children:m.filter(e=>0===e.level).map(e=>{let t=e=>{let s=m.filter(t=>t.parentId===e),r=[...s];return s.forEach(e=>{r=[...r,...t(e.id)]}),r},s=t(e.id),l=s.filter(e=>!m.some(t=>t.parentId===e.id)),i=s.length>0&&l.length||1;return(0,r.jsx)(J.nd,{colSpan:i,className:"text-center border-b",children:e.columnName||"Column"},e.id)})},"header-row-0"));for(let s=1;s<=e;s++)t.push((0,r.jsx)(J.Hj,{children:m.filter(e=>e.level===s-1).map(e=>{let t=m.filter(t=>t.parentId===e.id);return 0===t.length?(0,r.jsx)(J.nd,{className:"text-center border-b"},`empty-${e.id}`):t.map(e=>{let t=e=>{let s=m.filter(t=>t.parentId===e),r=[...s];return s.forEach(e=>{r=[...r,...t(e.id)]}),r},s=t(e.id),l=s.filter(e=>!m.some(t=>t.parentId===e.id)),i=s.length>0&&l.length||1;return(0,r.jsx)(J.nd,{colSpan:i,className:"text-center border-b",children:e.columnName||"Child Column"},e.id)})})},`header-row-${s}`));return t})()}),(0,r.jsx)(J.BF,{children:x.length>0?x.map((e,t)=>(0,r.jsx)(J.Hj,{children:m.filter(e=>{let t=m.some(t=>t.parentId===e.id);return 0===e.level&&!t||e.level>0}).map(e=>(0,r.jsx)(J.nA,{className:"bg-gray-50",children:(0,r.jsx)("div",{className:"h-8 flex items-center justify-center text-gray-400 text-xs",children:"Input field"})},e.id))},t)):(0,r.jsx)(J.Hj,{children:m.filter(e=>{let t=m.some(t=>t.parentId===e.id);return 0===e.level&&!t||e.level>0}).map(e=>(0,r.jsx)(J.nA,{className:"bg-gray-50",children:(0,r.jsx)("div",{className:"h-8 flex items-center justify-center text-gray-400 text-xs",children:"Input field"})},e.id))})})]})}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-2",children:"This preview shows how the table will appear to users filling out the form."})]})]})]}):(0,r.jsxs)("form",{onSubmit:D,className:"space-y-4",children:[(0,r.jsxs)("div",{className:"bg-blue-50 p-3 rounded-md text-sm text-blue-800 border border-blue-200 mb-4",children:[(0,r.jsx)("p",{className:"font-medium mb-1",children:"Table Structure Guidelines:"}),(0,r.jsxs)("ul",{className:"list-disc pl-5 space-y-1",children:[(0,r.jsxs)("li",{children:["Create multiple"," ",(0,r.jsx)("span",{className:"font-medium",children:"parent columns"}),' using the "Add Top-Level Column" button']}),(0,r.jsxs)("li",{children:["Add up to 2 ",(0,r.jsx)("span",{className:"font-medium",children:"child columns"})," ",'under each parent using the "+" button']}),(0,r.jsx)("li",{children:"Child columns cannot have their own children (maximum 2 levels)"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(V.J,{htmlFor:"table-label",children:"Table Label"}),(0,r.jsx)(B.p,{id:"table-label",value:d,onChange:e=>u(e.target.value),placeholder:"Enter table question label",required:!0})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(V.J,{children:"Columns"}),m.map(e=>(0,r.jsxs)("div",{className:(0,W.cn)("flex items-center gap-2 p-2 rounded-md",0===e.level?"bg-gray-50":"bg-white border-l-2 border-gray-300"),style:{marginLeft:`${20*e.level}px`},children:[(0,r.jsxs)("div",{className:"flex-1 flex items-center gap-2",children:[e.level>0&&(0,r.jsx)("div",{className:"w-4 h-4 flex items-center justify-center",children:(0,r.jsx)("div",{className:"w-2 h-2 bg-blue-400 rounded-full"})}),(0,r.jsx)(B.p,{value:e.columnName,onChange:t=>A(e.id,t.target.value),placeholder:`${0===e.level?"Parent":"Child"} Column`,className:(0,W.cn)("flex-1",0===e.level?"border-blue-200 bg-white":"border-dashed")}),0===e.level&&(0,r.jsx)("div",{className:"text-xs text-blue-500 font-medium",children:"Parent"}),e.level>0&&(0,r.jsx)("div",{className:"text-xs text-gray-500 font-medium",children:"Child"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(L.$,{type:"button",variant:"ghost",size:"icon",onClick:()=>w(e.id),title:e.level>0?"Child columns cannot have their own children":m.filter(t=>t.parentId===e.id).length>=2?"Maximum 2 child columns allowed":"Add child column",disabled:e.level>0||m.filter(t=>t.parentId===e.id).length>=2,children:(0,r.jsx)(g.A,{className:(0,W.cn)("h-4 w-4",(e.level>0||m.filter(t=>t.parentId===e.id).length>=2)&&"text-gray-300")})}),(0,r.jsx)(L.$,{type:"button",variant:"ghost",size:"icon",onClick:()=>S(e.id),disabled:m.length<=1,title:"Remove column",children:(0,r.jsx)(c.A,{className:"h-4 w-4"})})]})]},e.id)),(0,r.jsxs)(L.$,{type:"button",variant:"outline",size:"sm",onClick:q,className:"mt-2",children:[(0,r.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Add Top-Level Column"]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(V.J,{children:"Rows"}),x.map((e,t)=>(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(B.p,{value:e.rowsName,onChange:e=>G(t,e.target.value),placeholder:`Row ${t+1}`,className:`row-input ${!e.rowsName.trim()?"border-red-500":""}`}),(0,r.jsx)(L.$,{type:"button",variant:"ghost",size:"icon",onClick:()=>E(t),disabled:!1,children:(0,r.jsx)(c.A,{className:"h-4 w-4"})})]},t)),(0,r.jsxs)(L.$,{type:"button",variant:"outline",size:"sm",onClick:k,className:"mt-2",children:[(0,r.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Add Row"]})]})]}),v&&(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(V.J,{children:"Table Preview"}),(0,r.jsx)("div",{className:"border rounded-md p-4 overflow-x-auto",children:(0,r.jsxs)(J.XI,{children:[(0,r.jsx)(J.A0,{children:(()=>{let e=Math.max(...m.map(e=>e.level),0),t=[];t.push((0,r.jsx)(J.Hj,{children:m.filter(e=>0===e.level).map(e=>{let t=e=>{let s=m.filter(t=>t.parentId===e),r=[...s];return s.forEach(e=>{r=[...r,...t(e.id)]}),r},s=t(e.id),l=s.filter(e=>!m.some(t=>t.parentId===e.id)),i=s.length>0&&l.length||1;return(0,r.jsx)(J.nd,{colSpan:i,className:"text-center border-b",children:e.columnName||"Column"},e.id)})},"header-row-0"));for(let s=1;s<=e;s++)t.push((0,r.jsx)(J.Hj,{children:m.filter(e=>e.level===s-1).map(e=>{let t=m.filter(t=>t.parentId===e.id);return 0===t.length?(0,r.jsx)(J.nd,{className:"text-center border-b"},`empty-${e.id}`):t.map(e=>{let t=e=>{let s=m.filter(t=>t.parentId===e),r=[...s];return s.forEach(e=>{r=[...r,...t(e.id)]}),r},s=t(e.id),l=s.filter(e=>!m.some(t=>t.parentId===e.id)),i=s.length>0&&l.length||1;return(0,r.jsx)(J.nd,{colSpan:i,className:"text-center border-b",children:e.columnName||"Child Column"},e.id)})})},`header-row-${s}`));return t})()}),(0,r.jsx)(J.BF,{children:x.length>0?x.map((e,t)=>(0,r.jsx)(J.Hj,{children:m.filter(e=>{let t=m.some(t=>t.parentId===e.id);return 0===e.level&&!t||e.level>0}).map(e=>(0,r.jsx)(J.nA,{className:"bg-gray-50",children:(0,r.jsx)("div",{className:"h-8 flex items-center justify-center text-gray-400 text-xs",children:"Input field"})},e.id))},t)):(0,r.jsx)(J.Hj,{children:m.filter(e=>{let t=m.some(t=>t.parentId===e.id);return 0===e.level&&!t||e.level>0}).map(e=>(0,r.jsx)(J.nA,{className:"bg-gray-50",children:(0,r.jsx)("div",{className:"h-8 flex items-center justify-center text-gray-400 text-xs",children:"Input field"})},e.id))})})]})}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-2",children:"This preview shows how the table will appear to users filling out the form."})]})]}),(0,r.jsx)("div",{className:"flex items-center justify-end space-x-4 mt-6",children:(0,r.jsx)(L.$,{type:"submit",disabled:b,className:"bg-primary-500 text-white hover:bg-primary-600",children:b?"Saving...":n||a?.id?"Update":"Save"})})]})]})}var X=s(76847),Y=s(33103);let Z=e=>new Promise(t=>{if(!["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","application/vnd.ms-excel"].includes(e.type))return void t({isValid:!1,error:"Please select a valid Excel file (.xlsx or .xls)"});if(e.size>5242880)return void t({isValid:!1,error:"File size must be less than 5MB"});let s=new FileReader;s.onload=e=>{try{let s=new Uint8Array(e.target?.result),r=Y.LF(s,{type:"array"}),l=r.Sheets[r.SheetNames[0]],i=Y.Wp.sheet_to_json(l,{header:1});if(i.length<2)return void t({isValid:!1,error:"Excel file is empty or has no valid data"});let n=i[0].map(e=>e?.toString().trim());if(!n[0]?.includes("label")||!n[1]?.includes("code"))return void t({isValid:!1,error:"Invalid Excel format: Missing required headers (Label, Code)"});let a=i.slice(1);if(0===a.length)return void t({isValid:!1,error:"Excel file contains no valid options"});for(let e=0;e<a.length;e++){let s=a[e];if(!s[0]||!s[1])return void t({isValid:!1,error:`Invalid data in row ${e+2}: Label and Code are required`})}t({isValid:!0})}catch(e){t({isValid:!1,error:"Failed to parse Excel file"})}},s.onerror=()=>{t({isValid:!1,error:"Error reading Excel file"})},s.readAsArrayBuffer(e)}),ee=({file:e,onRemove:t,error:s})=>(0,r.jsxs)("div",{className:`flex items-center justify-between p-3 rounded-lg ${s?"bg-red-50 border border-red-200":"bg-green-50 border border-green-200"}`,children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[s?(0,r.jsx)(X.wew,{className:"text-red-500"}):(0,r.jsx)(X.qGT,{className:"text-green-500"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-sm font-medium",children:e.name}),(0,r.jsxs)("div",{className:"text-xs text-gray-500",children:[(e.size/1024).toFixed(1)," KB"]}),s&&(0,r.jsx)("div",{className:"text-xs text-red-600",children:s})]})]}),(0,r.jsx)("button",{type:"button",onClick:t,className:"text-red-500 hover:text-red-700 p-1",title:"Remove file",children:(0,r.jsx)(X.id1,{})})]}),et=()=>{let e=new Blob(["Label,Code,Next Question ID\nOption 1,opt1,\nOption 2,opt2,\nOption 3,opt3,"],{type:"text/csv"}),t=window.URL.createObjectURL(e),s=document.createElement("a");s.href=t,s.download="question_options_template.csv",document.body.appendChild(s),s.click(),window.URL.revokeObjectURL(t),document.body.removeChild(s)},es=({isOpen:e,onConfirm:t,onCancel:s})=>{if(!e)return null;let l=(0,p.c3)();return(0,r.jsx)("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-md w-full",children:[(0,r.jsx)("h2",{className:"text-lg text-neutral-700 font-semibold mb-4",children:l("unsavedChanges")}),(0,r.jsx)("p",{className:"mb-6 text-neutral-700",children:l("unsavedChangesWarning")}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,r.jsx)("button",{onClick:s,className:"btn-outline",children:l("cancel")}),(0,r.jsx)("button",{onClick:t,className:"btn-danger",children:l("discardChanges")})]})]})})},er=({showModal:e,setShowModal:t,contextType:s,contextId:i,position:n})=>{let a=(0,C.mN)({resolver:(0,$.u)(R),defaultValues:{label:"",inputType:"",hint:"",placeholder:"",questionOptions:[]}}),o=(0,p.c3)(),{register:d,formState:{errors:u,isSubmitted:c,isDirty:m},setValue:x,handleSubmit:h,reset:b,watch:j}=a,[g,v]=(0,l.useState)(!1),[f,y]=(0,l.useState)(null),[N,q]=(0,l.useState)("form"),[T,D]=(0,l.useState)(""),[z,O]=(0,l.useState)(!1),[P,L]=(0,l.useState)(""),[B,V]=(0,l.useState)(!1),U=(0,l.useRef)(null),H=(0,E.jE)(),J="project"===s?["questions",i]:"template"===s?["templateQuestions",i]:["questionBlockQuestions",i],W=(0,A.n)({mutationFn:G.Af,onSuccess:()=>{H.invalidateQueries({queryKey:J}),"project"===s&&H.invalidateQueries({queryKey:["formBuilderData",i]}),ea()},onError:e=>{D(e.message||"Failed to add question"),V(!1)}});(0,l.useEffect)(()=>{d("inputType",{required:o("selectInputType")})},[d]),(0,l.useEffect)(()=>{x("inputType",P,{shouldValidate:c})},[P,x,c]),(0,l.useEffect)(()=>{e&&V(!1)},[e]);let Y=async e=>{let t=e.target.files?.[0];if(!t)return;let s=await Z(t);if(!s.isValid){D(s.error||"Invalid file"),y(null);return}D(""),y(t)},er=()=>{y(null),D(""),U.current&&(U.current.value="")},el=e=>{q(e),"form"===e&&er()},ei=()=>{let e=j("questionOptions");return m||!!j("label")||!!j("hint")||!!j("placeholder")||!!P||!!f||e&&Array.isArray(e)&&e.length>0},en=()=>{ei()?v(!0):ea()},ea=()=>{b(),L(""),y(null),q("form"),D(""),V(!1),v(!1),t(!1)},eo=async e=>{if(B)return;if("table"===P){let e=document.querySelector(".table-question-builder");e?e.dispatchEvent(new CustomEvent("submitTable")):console.error("TableQuestionBuilder not found");return}if(M(P)){if("excel"===N){if(!f)return void D("Please select an Excel file")}else if(0===(e.questionOptions||[]).length)return void a.setError("questionOptions",{type:"custom",message:o("atLeastOneOptionRequired")})}V(!0);let t=f??void 0,r={label:e.label,isRequired:z,hint:e.hint,placeholder:e.placeholder,inputType:P,questionOptions:"form"===N?e.questionOptions:void 0,file:"excel"===N?t:void 0};W.mutate({contextType:s,contextId:i,dataToSend:r,position:n})},ed=M(P);return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(w.A,{isOpen:e,onClose:en,className:"w-11/12 tablet:w-4/5 desktop:w-4/5 bg-neutral-100 rounded-lg p-6",children:[(0,r.jsx)("h1",{className:"heading-text capitalize mb-4",children:o("addQuestion")}),W.isPending&&(0,r.jsx)(K,{}),(0,r.jsx)(C.Op,{...a,children:(0,r.jsxs)("form",{className:"space-y-4 max-h-[500px] overflow-y-auto p-4",onSubmit:h(eo),children:[(0,r.jsxs)("div",{className:"label-input-group group ",children:[(0,r.jsx)("input",{...d("label",{required:o("questionNameRequired")}),className:"input-field",placeholder:o("enterQuestion")}),u.label&&(0,r.jsx)("p",{className:"text-sm text-red-500",children:`${u.label.message}`})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsx)("label",{htmlFor:"question-type",className:"label-text",children:o("inputType")}),(0,r.jsx)("div",{className:"mt-1",children:(0,r.jsx)(I.l,{id:"question-type",options:Object.values(k),value:P&&k[P]?k[P]:o("selectOption"),onChange:e=>{L((0,Q.H)(e,k)??""),q("form"),er()}})}),u.inputType&&(0,r.jsx)("p",{className:"text-sm text-red-500",children:`${u.inputType.message}`})]}),(0,r.jsx)("div",{className:"flex items-end",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(S.dO,{id:"required",checked:z,onCheckedChange:()=>O(e=>!e),className:"data-[state=checked]:bg-primary-500 data-[state=unchecked]:bg-neutral-100 border border-primary-500"}),(0,r.jsx)("label",{htmlFor:"required",className:"label-text",children:o("required")})]})})]}),(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsx)("label",{htmlFor:"hint",className:"label-text",children:o("helpText")}),(0,r.jsx)("textarea",{...d("hint"),id:"hint",placeholder:o("helpTextHint"),className:"input-field resize-none"})]}),(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsx)("label",{htmlFor:"placeholder",className:"label-text",children:o("placeholderText")}),(0,r.jsx)("input",{...d("placeholder"),id:"placeholder",placeholder:o("placeholderHint"),className:"input-field"})]}),ed&&(0,r.jsx)("div",{className:"space-y-4",children:(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsx)("label",{className:"label-text",children:o("questionOptions")}),(0,r.jsxs)("div",{className:"flex space-x-4 mb-4",children:[(0,r.jsxs)("label",{className:"flex items-center space-x-2 cursor-pointer",children:[(0,r.jsx)("input",{type:"radio",value:"form",checked:"form"===N,onChange:e=>el(e.target.value),className:"text-primary-500"}),(0,r.jsx)("span",{children:o("manualEntry")})]}),(0,r.jsxs)("label",{className:"flex items-center space-x-2 cursor-pointer",children:[(0,r.jsx)("input",{type:"radio",value:"excel",checked:"excel"===N,onChange:e=>el(e.target.value),className:"text-primary-500"}),(0,r.jsx)("span",{children:o("excelUpload")})]})]}),"excel"===N&&(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-4 hover:border-gray-400 transition-colors",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(X.tAF,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,r.jsxs)("div",{className:"mt-2",children:[(0,r.jsxs)("label",{htmlFor:"excel-file",className:"cursor-pointer",children:[(0,r.jsx)("span",{className:"mt-2 block text-sm font-medium text-gray-900",children:o("uploadExcel")}),(0,r.jsxs)("span",{className:"mt-1 block text-xs text-gray-500",children:[o("supportedFormats"),": .xlsx, .xls (max 5MB)"]})]}),(0,r.jsx)("input",{ref:U,id:"excel-file",type:"file",accept:".xlsx,.xls",onChange:Y,className:"sr-only"})]}),(0,r.jsxs)("div",{className:"mt-3 flex justify-center space-x-2",children:[(0,r.jsxs)("button",{type:"button",onClick:()=>U.current?.click(),className:"btn-outline inline-flex items-center",children:[(0,r.jsx)(X.bh6,{className:"mr-2"}),o("chooseExcel")]}),(0,r.jsxs)("button",{type:"button",onClick:et,className:"btn-outline inline-flex items-center",title:o("downloadTemplate"),children:[(0,r.jsx)(X.Ah9,{className:"mr-2"}),o("downloadTemplate")]})]})]})}),f&&(0,r.jsx)(ee,{file:f,onRemove:er,error:T}),T&&!f&&(0,r.jsx)("div",{className:"text-sm text-red-600 bg-red-50 p-2 rounded",children:T}),(0,r.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-3",children:[(0,r.jsx)("p",{className:"text-sm text-blue-800 font-medium mb-2",children:o("excelFormatRequirements")}),(0,r.jsxs)("ul",{className:"text-xs text-blue-700 space-y-1",children:[(0,r.jsxs)("li",{children:["• ",o("columnA"),": ",(0,r.jsx)("strong",{children:o("label")})," (",o("required"),") -",o("optionDisplayText")]}),(0,r.jsxs)("li",{children:["• ",o("columnB"),": ",(0,r.jsx)("strong",{children:o("code")})," (",o("required"),") -",o("uniqueIdentifiers")]}),(0,r.jsxs)("li",{children:["• ",o("ColumnC"),": ",(0,r.jsx)("strong",{children:o("nextQuestionId")})," ","(",o("optional"),") - ",o("forConditionalLogic")]}),(0,r.jsxs)("li",{children:["• ",o("firstRowHeaders")]}),(0,r.jsxs)("li",{children:["• ",o("eachRowOption")]})]})]})]}),"form"===N&&(0,r.jsx)(F,{contextType:s,contextId:i,inputType:P})]})}),"table"===P&&(0,r.jsx)("div",{className:"mt-4",children:(0,r.jsx)(_,{projectId:i,isInModal:!0,onTableCreated:e=>{-1!==e&&(H.invalidateQueries({queryKey:J}),"project"===s&&H.invalidateQueries({queryKey:["formBuilderData",i]})),ea()}})}),(0,r.jsxs)("div",{className:"flex items-center justify-end space-x-4 pt-4",children:[(0,r.jsx)("button",{type:"button",onClick:en,className:"btn-outline",disabled:B,children:o("cancel")}),(0,r.jsx)("button",{type:"submit",className:"btn-primary flex items-center justify-center gap-2",onClick:e=>{if("table"===P){e.preventDefault();let t=document.querySelector(".table-question-builder");t&&t.dispatchEvent(new CustomEvent("submitTable"))}},disabled:B||"excel"===N&&(!f||!!T),children:B?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("span",{className:"animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full"}),o("saving"),"..."]}):o("save")})]})]})})]}),(0,r.jsx)(es,{isOpen:g,onConfirm:ea,onCancel:()=>v(!1)})]})};var el=s(19150),ei=s(54864);let en=({showModal:e,setShowModal:t,contextType:s,question:i,contextId:n})=>{let a=(0,C.mN)({}),o=(0,p.c3)(),{register:d,formState:{errors:u,isSubmitted:c},setValue:m,handleSubmit:x,reset:h}=a,b=(e,t)=>i[e]??t,[j,g]=(0,l.useState)(!1),[v,f]=(0,l.useState)("");(0,l.useEffect)(()=>{let e=R.safeParse(i);if(e.success)h(e.data),f(e.data.inputType||""),e.data.questionOptions&&e.data.questionOptions.length>0&&m("questionOptions",e.data.questionOptions);else{console.warn("Schema parsing failed, using raw question data:",e.error),m("label",b("label","")),m("hint",b("hint","")),m("placeholder",b("placeholder","")),m("inputType",b("inputType","")),f(b("inputType",""));let t=b("questionOptions",[]);Array.isArray(t)&&t.length>0&&m("questionOptions",t)}g(b("isRequired",!1))},[i,h,m]),(0,l.useEffect)(()=>{d("inputType",{required:"Please select an input type"})},[d]),(0,l.useEffect)(()=>{m("inputType",v,{shouldValidate:c})},[v,m,c]);let y=(0,E.jE)(),N=(0,ei.wA)(),q="project"===s?["questions"]:"template"===s?["templateQuestions"]:["questionBlockQuestions"],I=()=>{g(!1),f(""),t(!1)},Q=(0,A.n)({mutationFn:G.sr,onSuccess:()=>{y.invalidateQueries({queryKey:q,exact:!1}),"project"===s&&y.invalidateQueries({queryKey:["formBuilderData",n]}),N((0,el.Ds)({message:o("questionsUpdated"),type:"success"})),I()},onError:()=>{N((0,el.Ds)({message:o("questionUpdateFailed"),type:"error"}))}}),T=async e=>{if("table"===v||"inputType"in i&&"table"===i.inputType){let e=document.querySelector(".table-question-builder");if(e)return void e.dispatchEvent(new CustomEvent("submitTable"))}let t={label:e.label,isRequired:j,hint:e.hint,placeholder:e.placeholder,inputType:v||("inputType"in i?i.inputType:""),questionOptions:e.questionOptions,..."position"in i&&{position:i.position}};Q.mutate({id:i.id,contextType:s,dataToSend:t,contextId:n})};return(0,r.jsxs)(w.A,{isOpen:e,onClose:I,className:"w-11/12 tablet:w-4/5 desktop:w-3/5",children:[(0,r.jsx)("h1",{className:"heading-text capitalize mb-4",children:o("editQuestion")}),Q.isPending&&(0,r.jsx)(K,{}),(0,r.jsx)(C.Op,{...a,children:(0,r.jsxs)("form",{className:"space-y-4 max-h-[500px] overflow-y-auto p-4",onSubmit:x(T),children:[(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsx)("input",{...d("label",{required:o("questionNameRequired")}),className:"input-field",placeholder:o("enterQuestion")}),u.label&&(0,r.jsx)("p",{className:"text-sm text-red-500",children:`${u.label.message}`})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsx)("label",{htmlFor:"question-type",className:"label-text",children:o("inputType")}),(0,r.jsx)("input",{id:"question-type",className:"input-field bg-gray-100 ",value:v&&k[v]?k[v]:"N/A",disabled:!0})]}),(0,r.jsx)("div",{className:"flex items-end",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(S.dO,{id:"required",checked:j,onCheckedChange:()=>g(e=>!e),className:"data-[state=checked]:bg-primary-500 data-[state=unchecked]:bg-neutral-100 border border-primary-500"}),(0,r.jsx)("label",{htmlFor:"required",className:"label-text",children:o("required")})]})}),u.inputType&&(0,r.jsx)("p",{className:"text-sm text-red-500",children:`${u.inputType.message}`})]}),(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsx)("label",{htmlFor:"hint",className:"label-text",children:o("helpText")}),(0,r.jsx)("textarea",{...d("hint"),id:"hint",placeholder:o("helpTextHint"),className:"input-field resize-none"})]}),(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsx)("label",{htmlFor:"placeholder",className:"label-text",children:o("placeholderText")}),(0,r.jsx)("input",{...d("placeholder"),id:"placeholder",placeholder:o("placeholderHint"),className:"input-field"})]}),M(v)&&(0,r.jsx)(F,{contextType:s,contextId:n,currentQuestionId:i.id,inputType:v},`${i.id}-${v}`),(0,r.jsxs)("div",{className:"flex items-center justify-end space-x-4",children:[(0,r.jsx)("button",{type:"button",onClick:I,className:"btn-outline",children:o("cancel")}),(0,r.jsx)("button",{onClick:x(T),className:"btn-primary",children:o("saveEdit")})]})]})})]})},ea=({showModal:e,setShowModal:t,contextType:s,question:n,contextId:a})=>{let o=(0,E.jE)(),d=(0,ei.wA)(),u=(0,p.c3)(),{data:c,isLoading:m,error:x}=(0,T.I)({queryKey:["tableQuestion",n.id],queryFn:async()=>{try{return await (0,H.q7)(n.id)}catch(e){throw console.error("Error fetching table data:",e),e}},enabled:e&&n.id>0&&"table"===n.inputType}),h=i().useMemo(()=>c?{id:c.id,label:c.label,tableColumns:c.tableColumns.map(e=>({id:e.id,columnName:e.columnName,parentColumnId:e.parentColumnId,childColumns:e.childColumns?.map(t=>({id:t.id,columnName:t.columnName,parentColumnId:t.parentColumnId||e.id}))||[]})),tableRows:c.tableRows.map(e=>({id:e.id,rowsName:e.rowsName}))}:null,[c]);(0,l.useEffect)(()=>{},[e,n]);let b="project"===s?["questions"]:"template"===s?["templateQuestions"]:["questionBlockQuestions"];x&&(console.error("Error fetching table data:",x),d((0,el.Ds)({message:u("tableDataLoadFailed"),type:"error"})));let j=i().useRef(null);return(0,r.jsxs)(w.A,{isOpen:e,onClose:()=>{window.confirm(u("unsavedChangesCloseConfirm"))&&t(!1)},className:"w-11/12 tablet:w-4/5 desktop:w-3/5",preventOutsideClick:!0,children:[m&&(0,r.jsx)(K,{}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("h1",{className:"heading-text capitalize mb-4",children:u("editTableQuestion")}),h?(0,r.jsxs)("div",{ref:j,className:"table-question-builder-container",children:[(0,r.jsx)(_,{projectId:a,isInModal:!0,isEditMode:!0,existingTableData:h,onTableCreated:e=>{o.invalidateQueries({queryKey:b,exact:!1}),o.invalidateQueries({queryKey:["tableQuestion",n.id],exact:!0}),"project"===s&&o.invalidateQueries({queryKey:["formBuilderData",a]}),d((0,el.Ds)({message:u("tableQuestionUpdated"),type:"success"})),setTimeout(()=>{t(!1)},100)}}),(0,r.jsxs)("div",{className:"flex items-center justify-end space-x-4 mt-6",children:[(0,r.jsx)("button",{type:"button",onClick:()=>t(!1),className:"btn-outline",children:u("cancel")}),(0,r.jsx)("button",{type:"button",onClick:()=>{let e=null;if(j.current&&(e=j.current),!e){let t=document.querySelectorAll(".table-question-builder");t.length>0&&(e=t[0])}if(!e&&j.current){let t=j.current.querySelector(".table-question-builder");t&&(e=t)}if(e){let t=new CustomEvent("submitTable",{bubbles:!0,cancelable:!0,detail:{timestamp:Date.now()}});e.dispatchEvent(t)}else{console.error("Could not find any table builder element to dispatch event to");let e=document.querySelector("[class*='table']");if(e){let t=new CustomEvent("submitTable",{bubbles:!0,cancelable:!0,detail:{timestamp:Date.now(),isLastResort:!0}});e.dispatchEvent(t)}}},className:"btn-primary",children:u("saveChanges")})]})]}):m?null:(0,r.jsx)("div",{className:"p-4 text-center",children:(0,r.jsx)("p",{className:"text-red-500",children:u("tableDataLoadErrorRetry")})})]})]})};var eo=s(73678),ed=s(11860),eu=s(96);let ec=({showModal:e,setShowModal:t,contextType:s,contextId:i,existingGroup:n,questions:a,questionGroups:o=[]})=>{let[d,u]=(0,l.useState)(""),[c,m]=(0,l.useState)([]),x=(0,ei.wA)(),h=(0,E.jE)(),b=(0,p.c3)();(0,l.useEffect)(()=>{e&&(n?(u(n.title),m(a.filter(e=>e.questionGroupId===n.id).map(e=>e.id))):(u(""),m([])))},[e,n,a]);let j=(0,A.n)({mutationFn:eu.IF,onSuccess:e=>{h.invalidateQueries({queryKey:["questionGroups",i]}),h.invalidateQueries({queryKey:["questions",i]}),"project"===s&&h.invalidateQueries({queryKey:["formBuilderData",i]}),x((0,el.Ds)({message:b("questionGroupCreated"),type:"success"})),t(!1)},onError:e=>{console.error("Error creating question group:",e),x((0,el.Ds)({message:b("questionGroupCreationFailed"),type:"error"}))}}),g=(0,A.n)({mutationFn:eu.lr,onSuccess:e=>{h.invalidateQueries({queryKey:["questionGroups",i]}),h.invalidateQueries({queryKey:["questions",i]}),"project"===s&&h.invalidateQueries({queryKey:["formBuilderData",i]}),x((0,el.Ds)({message:b("questionGroupUpdated"),type:"success"})),t(!1)},onError:e=>{console.error("Error updating question group:",e),x((0,el.Ds)({message:b("questionGroupUpdateFailed"),type:"error"}))}});return e?(0,r.jsx)("div",{className:"fixed inset-0 bg-neutral-900/50 flex items-center justify-center z-40",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-lg w-full max-w-2xl max-h-[90vh] overflow-auto p-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center p-4 ",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold",children:n?b("editQuestionGroup"):b("createQuestionGroup")}),(0,r.jsx)("button",{onClick:()=>t(!1),className:"text-gray-500 hover:text-gray-700 cursor-pointer",children:(0,r.jsx)(ed.A,{size:20})})]}),(0,r.jsxs)("form",{onSubmit:e=>{if(e.preventDefault(),!d.trim())return void x((0,el.Ds)({message:b("groupTitleRequired"),type:"error"}));n?g.mutate({id:n.id,title:d,order:n.order,selectedQuestionIds:c}):j.mutate({title:d,order:o.length+1,projectId:i,selectedQuestionIds:c})},className:"p-4",children:[(0,r.jsxs)("div",{className:"group label-input-group ",children:[(0,r.jsx)("label",{htmlFor:"title",children:b("groupTitle")}),(0,r.jsx)("input",{type:"text",id:"title",value:d,onChange:e=>u(e.target.value),className:" input-field w-full",placeholder:b("enterGroupTitle"),required:!0})]}),(0,r.jsxs)("div",{className:"mt-8 label-input-group",children:[(0,r.jsx)("label",{children:b("selectQuestionsForGroup")}),(0,r.jsx)("div",{className:"border border-neutral-300 rounded-md p-2 max-h-60 overflow-y-auto",children:a.length>0?a.map(e=>{let t=e.questionGroupId?o.find(t=>t.id===e.questionGroupId):null;return(0,r.jsxs)("div",{className:"flex gap-2 items-center mb-3 p-2 border-b border-neutral-300",children:[(0,r.jsx)("input",{type:"checkbox",id:`question-${e.id}`,checked:c.includes(e.id),onChange:t=>{t.target.checked?m([...c,e.id]):m(c.filter(t=>t!==e.id))},className:"mr-2 cursor-pointer w-5 h-5"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:`question-${e.id}`,className:"text-sm",children:e.label}),t&&t.id!==(n?.id||-1)&&(0,r.jsxs)("div",{className:"text-xs text-neutral-700 mt-1",children:[b("currentlyInGroup"),": ",(0,r.jsx)("span",{className:"font-medium text-amber-600",children:t.title}),(0,r.jsxs)("span",{className:"ml-1 text-neutral-700",children:["(",b("willBeMovedToThisGroup"),")"]})]})]})]},e.id)}):(0,r.jsx)("p",{className:"text-gray-500 text-sm p-2",children:b("noAvailableQuestions")})})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-2 mt-4",children:[(0,r.jsx)("button",{type:"button",onClick:()=>t(!1),className:"btn-outline",children:b("cancel")}),(0,r.jsx)("button",{type:"submit",className:"px-4 py-2 btn-primary",disabled:j.isPending||g.isPending,children:j.isPending||g.isPending?b("saving"):n?b("updateGroup"):b("createGroup")})]})]})]})}):null},em=({showModal:e,setShowModal:t,onConfirmDelete:s,onConfirmDeleteWithQuestions:l,isDeleting:i})=>{let n=(0,p.c3)();return e?(0,r.jsx)("div",{className:"fixed inset-0 bg-neutral-900/50 flex items-center justify-center z-40",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-lg w-full max-w-md",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center p-4 border-b",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold",children:n("deleteQuestionGroup")}),(0,r.jsx)("button",{onClick:()=>t(!1),className:"text-gray-500 hover:text-gray-700",disabled:i,children:(0,r.jsx)(ed.A,{size:20})})]}),(0,r.jsxs)("div",{className:"p-4",children:[(0,r.jsx)("p",{className:"mb-4",children:n("deleteQuestionGroupPrompt")}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("button",{onClick:s,className:"w-full px-4 py-2 bg-amber-500 text-white rounded-md hover:bg-amber-600",disabled:i,children:n(i?"deleting":"deleteGroupOnly")}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:n("deleteGroupKeepQuestions")}),(0,r.jsx)("button",{onClick:l,className:"w-full px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600",disabled:i,children:n(i?"deleting":"deleteGroupQuestions")}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:n("deleteGroupAndQuestions")}),(0,r.jsx)("button",{onClick:()=>t(!1),className:"w-full px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",disabled:i,children:n("cancel")})]})]})]})}):null};var ep=s(99270);let ex=({isOpen:e,onClose:t,onAddQuestions:s})=>{let[i,n]=(0,l.useState)(""),[a,o]=(0,l.useState)([]),[d,u]=(0,l.useState)(!0),c=(0,p.c3)(),{data:m,isLoading:x,isError:h}=(0,T.I)({queryKey:["libraryQuestions"],queryFn:()=>(0,G.dI)(),enabled:e}),b=m?m.filter(e=>e.label.toLowerCase().includes(i.toLowerCase())):[],j=e=>{a.some(t=>t.id===e.id)?o(a.filter(t=>t.id!==e.id)):o([...a,e])};return((0,l.useEffect)(()=>{e||(o([]),n(""))},[e]),e)?(0,r.jsxs)("div",{className:"fixed inset-0 z-50 flex justify-end",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-neutral-900/50",onClick:t}),(0,r.jsxs)("div",{className:"relative w-full max-w-md bg-neutral-50 h-full overflow-auto shadow-xl",children:[(0,r.jsxs)("div",{className:"p-4 border-b border-neutral-200",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,r.jsx)("h2",{className:"text-xl font-bold",children:c("searchLibrary")}),(0,r.jsx)("button",{onClick:t,className:"self-end cursor-pointer text-neutral-700 hover:text-neutral-900 active:scale-90 transition-all duration-300",children:(0,r.jsx)(ed.A,{size:20})})]}),(0,r.jsxs)("div",{className:"relative mb-4",children:[(0,r.jsx)("input",{type:"text",placeholder:c("searchPlaceholder"),className:"input-field w-full p-2 pl-10",value:i,onChange:e=>n(e.target.value)}),(0,r.jsx)(ep.A,{className:"absolute left-3 top-2.5 ",size:18})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,r.jsxs)("span",{children:[b.length," ",c("asset"),1!==b.length?c("s"):""," ",c("found")]}),(0,r.jsxs)("label",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",checked:d,onChange:()=>u(!d),className:"mr-2"}),c("expandDetails")]})]})]}),(0,r.jsx)("div",{className:"p-4",children:x?(0,r.jsx)("div",{className:"flex justify-center p-8",children:(0,r.jsx)(P.A,{})}):h?(0,r.jsx)("div",{className:"text-red-500 p-4 text-center",children:c("libraryLoadError")}):0===b.length?(0,r.jsx)("div",{className:"text-neutral-700 p-4 text-center",children:c("noQuestionsFound")}):(0,r.jsx)("div",{className:"space-y-2",children:b.map(e=>(0,r.jsx)("div",{className:"border border-neutral-500 rounded-md p-3",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",checked:a.some(t=>t.id===e.id),onChange:()=>j(e),className:"mr-3 h-5 w-5 cursor-pointer"}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:"font-medium",children:e.label}),d&&(0,r.jsxs)("div",{className:"text-sm text-neutral-700 mt-1",children:[c("type"),": ",String(e.inputType),e.hint&&(0,r.jsxs)("div",{children:[c("hint"),": ",e.hint]})]})]})]})},e.id))})}),(0,r.jsx)("div",{className:"border-t border-gray-200 p-4 sticky bottom-0 bg-neutral-50",children:(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("button",{onClick:t,className:"btn-outline",children:c("cancel")}),(0,r.jsxs)("button",{onClick:()=>{s(a),o([]),t()},disabled:0===a.length,className:`px-4 py-2 rounded-md ${a.length>0?"btn-primary":"bg-gray-200 text-gray-500 pointer-events-none"}`,children:[c("addSelected")," (",a.length,")"]})]})})]})]}):null},eh=({setIsPreviewMode:e,contextType:t,contextId:s,permissions:o})=>{let d=(0,n.FR)((0,n.MS)(n.AN,{activationConstraint:{distance:8}}),(0,n.MS)(n.uN)),u=(0,p.c3)(),c=o.manageProject||o.editForm,[m,h]=(0,l.useState)(!1),[b,g]=(0,l.useState)(!1),[v,w]=(0,l.useState)(!1),[C,I]=(0,l.useState)(!1),[Q,k]=(0,l.useState)(!1),[S,D]=(0,l.useState)(null),[F,$]=(0,l.useState)(!1),[M,z]=(0,l.useState)(!1),[O,R]=(0,l.useState)(!1),[P,L]=(0,l.useState)(!1),[B,V]=(0,l.useState)(null),[U,H]=(0,l.useState)([]),[J,W]=(0,l.useState)(null),[_,X]=(0,l.useState)(""),[Y,Z]=(0,l.useState)(!1),[ee,et]=(0,l.useState)(!1),[es,ed]=(0,l.useState)(null),[ep,eh]=(0,l.useState)([]),[eb,ej]=(0,l.useState)(""),[eg,ev]=(0,l.useState)(!1),ef=(0,ei.wA)(),ey=(0,E.jE)(),eN="project"===t?["questions",s]:"template"===t?["templateQuestions",s]:["questionBlockQuestions",s],eq=["questionGroups",s],ew=["formBuilderData",s],{data:eC,isLoading:eI,isError:eQ}=(0,T.I)({queryKey:ew,queryFn:()=>(0,G.gf)({projectId:s}),enabled:"project"===t}),{data:ek=[],isLoading:eS,isError:eE}=(0,T.I)({queryKey:eN,queryFn:()=>"template"===t?(0,G.ej)({templateId:s}):"questionBlock"===t?(0,G.dI)():[],enabled:"project"!==t}),eA=i().useMemo(()=>{if("project"!==t)return ek;{if(!eC?.items)return[];let e=[];return eC.items.forEach(t=>{"question"===t.type?e.push(t):"group"===t.type&&t.questions&&t.questions.forEach(t=>{e.push(t)})}),e}},[eC,t,ek]),eG=i().useMemo(()=>"project"===t&&eC?.items?eC.items.filter(e=>"group"===e.type).map(e=>({...e,question:e.questions||[]})):[],[eC,t]),{data:eT=[]}=(0,T.I)({queryKey:eq,queryFn:()=>(0,eu.pr)({projectId:s}),enabled:"project"===t&&!eC});eG.reduce((e,t)=>(e[t.id]=eA.filter(e=>e.questionGroupId===t.id).sort((e,t)=>e.position-t.position),e),{});let eD=eA.filter(e=>!e.questionGroupId),eF=e=>{let t=new Map;e.forEach(e=>{let s=eA.filter(t=>t.questionGroupId===e.id).sort((e,t)=>e.position-t.position);t.set(e.id,{...e,subGroups:[],question:s})});let s=[];return e.forEach(e=>{let r=t.get(e.id);if(e.parentGroupId){let s=t.get(e.parentGroupId);s&&(s.subGroups=s.subGroups||[],s.subGroups.push(r))}else s.push(r)}),s},e$=i().useMemo(()=>eF(eG),[eG,eA]),eM=()=>{let e=[];return"project"===t&&e$.forEach(t=>{let s=eA.filter(e=>e.questionGroupId===t.id).sort((e,t)=>e.position-t.position),r=s.length>0?Math.min(...s.map(e=>e.position)):t.order;e.push({type:"group",data:t,order:r,originalPosition:r})}),("project"===t?eD:eA).forEach(t=>{e.push({type:"question",data:t,order:t.position,originalPosition:t.position})}),e.sort((e,t)=>e.order===t.order?(e.originalPosition||e.order)-(t.originalPosition||t.order):e.order-t.order)},ez=i().useMemo(()=>eM(),[e$,eD,t]);i().useEffect(()=>{let e=eA.some(e=>null!==e.questionGroupId&&void 0!==e.questionGroupId),s=0===eG.length;e&&s&&"project"===t&&ey.invalidateQueries({queryKey:eq})},[eA,eG,t,ey,eq]);let eO=(0,A.n)({mutationFn:G.ul,onSuccess:()=>{ey.invalidateQueries({queryKey:eN}),"project"===t&&ey.invalidateQueries({queryKey:ew}),ef((0,el.Ds)({message:u("questionDeleted"),type:"success"}))},onError:()=>{ef((0,el.Ds)({message:u("questionDeleteFailed"),type:"error"}))},onSettled:()=>{$(!1)}}),eR=(0,A.n)({mutationFn:G.ku,onSuccess:()=>{ey.invalidateQueries({queryKey:eN}),"project"===t&&ey.invalidateQueries({queryKey:ew}),ef((0,el.Ds)({message:u("questionDuplicated"),type:"success"}))},onError:()=>{ef((0,el.Ds)({message:u("questionDuplicateFailed"),type:"error"}))},onSettled:()=>{$(!1)}}),eP=(0,A.n)({mutationFn:eu.BU,onSuccess:(e,s)=>{let r=eG.find(e=>e.id===s.id);if(r&&eA.filter(e=>e.questionGroupId===r.id).length>0){let e=eA.map(e=>e.questionGroupId===r.id?{...e,questionGroupId:void 0}:e);ey.setQueryData(eN,e);let t=eG.filter(e=>e.id!==s.id);ey.setQueryData(eq,t)}ey.invalidateQueries({queryKey:eq}),ey.invalidateQueries({queryKey:eN}),"project"===t&&ey.invalidateQueries({queryKey:ew}),ef((0,el.Ds)({message:u("groupDeleted"),type:"success"})),L(!1),ev(!1)},onError:e=>{console.error(u("groupDeleteError"),e),ef((0,el.Ds)({message:u("groupDeleteFailed"),type:"error"})),ev(!1)}}),eK=(0,A.n)({mutationFn:eu.yb,onSuccess:(e,s)=>{let r=eG.find(e=>e.id===s.id);if(r){let e=eG.filter(e=>e.id!==s.id);ey.setQueryData(eq,e);let t=eA.filter(e=>e.questionGroupId!==r.id);ey.setQueryData(eN,t)}ey.invalidateQueries({queryKey:eq}),ey.invalidateQueries({queryKey:eN}),"project"===t&&ey.invalidateQueries({queryKey:ew}),ef((0,el.Ds)({message:u("groupAndQuestionsDeleted"),type:"success"})),L(!1),ev(!1)},onError:e=>{console.error(u("groupAndQuestionsDeleteError"),e),ef((0,el.Ds)({message:u("groupAndQuestionsDeleteFailed"),type:"error"})),ev(!1)}}),eL=(0,A.n)({mutationFn:G.ae,onMutate:async e=>{await ey.cancelQueries({queryKey:eN});let t=ey.getQueryData(eN);if(t&&e.questionPositions){let s=t.map(t=>{let s=e.questionPositions.find(e=>e.id===t.id);return s?{...t,position:s.position}:t});ey.setQueryData(eN,s)}return{previousQuestions:t}},onSuccess:()=>{ey.invalidateQueries({queryKey:eN}),"project"===t&&ey.invalidateQueries({queryKey:ew}),ef((0,el.Ds)({message:u("questionOrderUpdated"),type:"success"}))},onError:(e,t,s)=>{s?.previousQuestions&&ey.setQueryData(eN,s.previousQuestions),console.error("Failed to update question positions:",e),console.error("Error response:",e.response?.data),ef((0,el.Ds)({message:`${u("questionOrderUpdateFailed")}: ${e.response?.data?.message||e.message||u("tryAgain")}`,type:"error"}))},onSettled:()=>{ey.invalidateQueries({queryKey:eN})}}),eB=(0,A.n)({mutationFn:eu._U,onSuccess:()=>{ey.invalidateQueries({queryKey:eN}),ey.invalidateQueries({queryKey:eq}),"project"===t&&ey.invalidateQueries({queryKey:ew}),ef((0,el.Ds)({message:"Question moved successfully",type:"success"}))},onError:e=>{console.error("Failed to move question between groups:",e),ef((0,el.Ds)({message:`Failed to move question: ${e.response?.data?.message||e.message||"Please try again"}`,type:"error"}))}}),eV=(0,A.n)({mutationFn:eu.kO,onSuccess:()=>{ey.invalidateQueries({queryKey:eq}),"project"===t&&ey.invalidateQueries({queryKey:ew}),ef((0,el.Ds)({message:"Group moved successfully",type:"success"}))},onError:e=>{console.error("Failed to move group:",e),ef((0,el.Ds)({message:`Failed to move group: ${e.response?.data?.message||e.message||"Please try again"}`,type:"error"}))}});(0,A.n)({mutationFn:eu.Vq,onSuccess:()=>{ey.invalidateQueries({queryKey:eq}),ef((0,el.Ds)({message:"Group order updated successfully",type:"success"}))},onError:e=>{console.error("Failed to update group positions:",e),ef((0,el.Ds)({message:`Failed to update group order: ${e.response?.data?.message||e.message||"Please try again"}`,type:"error"}))},onSettled:()=>{ey.invalidateQueries({queryKey:eN})}});let eU=(e,t,s)=>{t!==s&&eB.mutate({questionId:e,groupId:t||0,newGroupId:s||0})},eH=e=>{let r=eA.map(t=>{let s=e.find(e=>e.id===t.id);return s?{...t,position:s.position}:t});ey.setQueryData(eN,r),eL.mutate({contextType:t,contextId:s,questionPositions:e})},eJ=(e,t)=>{t&&eV.mutate({childGroupId:e,parentGroupId:t})},eW=(e,t)=>{ed(e),eh(t),et(!0)},e_=()=>{if(!eb.trim()||!es||0===ep.length)return;let e=eG.length>0?Math.max(...eG.map(e=>e.order)):0;e2.mutate({title:eb.trim(),order:e+1,projectId:s,selectedQuestionIds:ep,parentGroupId:es}),et(!1),ej(""),ed(null),eh([]),H(e=>e.filter(e=>!ep.includes(e)))},eX=()=>{et(!1),ej(""),ed(null),eh([])},eY=e=>{V(e),R(!0)},eZ=e=>{V(e),L(!0)},e0=e=>{V(e),R(!0)},e1=e=>{H(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])},e2=(0,A.n)({mutationFn:eu.IF,onSuccess:(e,s)=>{let r=e.data?.questionGroup?.id;if(r&&s.selectedQuestionIds){let e=eA.map(e=>s.selectedQuestionIds?.includes(e.id)?{...e,questionGroupId:r}:e);ey.setQueryData(eN,e);let t={id:r,title:s.title,order:s.order,projectId:s.projectId,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),question:e.filter(e=>e.questionGroupId===r)};ey.setQueryData(eq,[...eG,t])}ey.invalidateQueries({queryKey:eq}),ey.invalidateQueries({queryKey:eN}),"project"===t&&ey.invalidateQueries({queryKey:ew}),ef((0,el.Ds)({message:u("groupCreated"),type:"success"})),H([]),Z(!1),ev(!1)},onError:e=>{console.error(u("groupCreateError"),e),ef((0,el.Ds)({message:u("groupCreateFailed"),type:"error"})),ev(!1)}}),e4=(e,t)=>{W(e),X(t)},e5=()=>{W(null),X("")},e3=(0,A.n)({mutationFn:eu.lr,onSuccess:()=>{ey.invalidateQueries({queryKey:eq}),"project"===t&&ey.invalidateQueries({queryKey:ew}),ef((0,el.Ds)({message:u("groupNameUpdated"),type:"success"})),W(null),X(""),ev(!1)},onError:()=>{ef((0,el.Ds)({message:u("groupNameUpdateFailed"),type:"error"})),ev(!1)}}),e6=e=>{if(!_.trim())return void ef((0,el.Ds)({message:u("groupNameEmpty"),type:"warning"}));ev(!0);let t=eG.find(t=>t.id===e);if(!t)return;let s=eG.map(t=>t.id===e?{...t,title:_}:t);ey.setQueryData(eq,s),e3.mutate({id:e,title:_,order:t.order})};(0,A.n)({mutationFn:G.Af,onSuccess:()=>{ey.invalidateQueries({queryKey:eN})},onError:e=>{console.error(u("addQuestionError"),e),ef((0,el.Ds)({message:u("addQuestionFailed"),type:"error"}))}});let e7=async e=>{if(0!==e.length){k(!0);try{let r=eA.length>0?Math.max(...eA.map(e=>e.position)):0;for(let l=0;l<e.length;l++){let i=e[l],n={label:i.label,isRequired:i.isRequired,hint:i.hint||"",placeholder:i.placeholder||"",inputType:String(i.inputType),questionOptions:i.questionOptions||[]};await (0,G.Af)({contextType:t,contextId:s,dataToSend:n,position:r+l+1})}ey.invalidateQueries({queryKey:eN}),"project"===t&&ey.invalidateQueries({queryKey:ew}),ef((0,el.Ds)({message:`${e.length} ${u("questionsAdded")}`,type:"success"}))}catch(e){console.error("Error adding questions:",e),ef((0,el.Ds)({message:u("addFromLibraryFailed"),type:"error"}))}finally{k(!1)}}},e8="project"===t?eI:eS,e9="project"===t?eQ:eE;return e8?(0,r.jsx)("div",{className:"min-h-[60vh] flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:u("loadingFormData")})]})}):e9?(0,r.jsx)("div",{className:"min-h-[60vh] flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-red-500 mb-4",children:u("formLoadError")}),(0,r.jsx)("button",{onClick:()=>{"project"===t?ey.invalidateQueries({queryKey:ew}):ey.invalidateQueries({queryKey:eN})},className:"btn-primary",children:u("retry")})]})}):(0,r.jsxs)("div",{className:"min-h-[60vh] relative",children:[(eO.isPending||eR.isPending||eP.isPending||eK.isPending||eL.isPending||Q)&&(0,r.jsx)(K,{}),(0,r.jsx)(er,{showModal:m,setShowModal:h,contextType:t,contextId:s,position:eA.length>0?Math.max(...eA.map(e=>e.position))+1:1}),S&&"table"!==S.inputType&&(0,r.jsx)(en,{showModal:b,setShowModal:g,contextType:t,question:S,contextId:s}),S&&"table"===S.inputType&&(0,r.jsx)(ea,{showModal:v,setShowModal:w,contextType:t,question:S,contextId:s}),(0,r.jsx)(ec,{showModal:M,setShowModal:z,contextType:t,contextId:s,questions:eA,questionGroups:eG}),B&&(0,r.jsx)(ec,{showModal:O,setShowModal:R,contextType:t,contextId:s,existingGroup:eG.find(e=>e.id===B),questions:eA,questionGroups:eG}),(0,r.jsx)(eo.R,{showModal:F,onClose:()=>$(!1),onConfirm:()=>{S&&S.id&&eO.mutate({contextType:t,id:S?.id,projectId:s})},title:u("deleteQuestion"),description:u("confirmDeleteQuestion"),confirmButtonText:u("delete"),cancelButtonText:u("cancel"),confirmButtonClass:"btn-danger"}),(0,r.jsx)(em,{showModal:P,setShowModal:L,onConfirmDelete:()=>{B&&(ev(!0),eP.mutate({id:B}))},onConfirmDeleteWithQuestions:()=>{B&&(ev(!0),eK.mutate({id:B}))},isDeleting:eP.isPending||eK.isPending}),(0,r.jsx)(ex,{isOpen:C,onClose:()=>I(!1),onAddQuestions:e7}),(0,r.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsxs)("h1",{className:"heading-text mr-4",children:[" ",u("formBuilder")," "]}),U.length>0?(0,r.jsxs)("button",{className:"btn-primary flex items-center gap-2",onClick:()=>{if(0===U.length)return void ef((0,el.Ds)({message:u("selectAtLeastOneQuestion"),type:"warning"}));ev(!0);let e=eA.filter(e=>U.includes(e.id)),t=e.length>0?Math.min(...e.map(e=>e.position)):eG.length+1;e2.mutate({title:u("newGroup"),order:t,projectId:s,selectedQuestionIds:U})},disabled:eg,children:[(0,r.jsx)(j.A,{size:16}),u("createGroup")," (",U.length,")"]}):(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsxs)("button",{className:"btn-outline flex items-center gap-2",onClick:()=>{Y?(Z(!1),H([])):Z(!0)},children:[(0,r.jsx)(j.A,{size:16}),Y?u("cancelSelection"):u("selectQuestions")]}),(0,r.jsxs)("button",{className:"btn-outline flex items-center gap-2",onClick:()=>z(!0),children:[(0,r.jsx)(j.A,{size:16}),u("createEmptyGroup")]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("button",{className:"btn-outline p-2",onClick:()=>e(!0),title:u("previewForm"),children:(0,r.jsx)(y.A,{size:16})}),(0,r.jsx)("button",{className:"btn-outline p-2",onClick:()=>I(!0),title:u("questionLibrary"),children:(0,r.jsx)(N.A,{size:16})})]})]}),(0,r.jsx)("div",{className:"section shadow-none border border-neutral-400",children:(0,r.jsx)(n.Mp,{sensors:d,collisionDetection:n.fp,onDragEnd:e=>{let{active:r,over:l}=e;if(!l||r.id===l.id||"project"!==t)return;let i=r.data.current,n=l.data.current;if(i?.type==="question"&&n?.type==="question"){let e=eA.find(e=>e.id===r.id),i=eA.find(e=>e.id===l.id);if(!e||!i||null!==e.questionGroupId&&void 0!==e.questionGroupId||null!==i.questionGroupId&&void 0!==i.questionGroupId||e.questionGroupId!==i.questionGroupId)return;let n=eA.filter(e=>null===e.questionGroupId||void 0===e.questionGroupId).sort((e,t)=>e.position-t.position),o=n.findIndex(e=>e.id===r.id),d=n.findIndex(e=>e.id===l.id);if(-1===o||-1===d)return;let u=(0,a.be)(n,o,d).map((e,t)=>({id:Number(e.id),position:t+1})),c=eA.map(e=>{let t=u.find(t=>t.id===e.id);return t?{...e,position:t.position}:e});ey.setQueryData(eN,c),eL.mutate({contextType:t,contextId:s,questionPositions:u})}if(i?.type==="question"&&n?.type==="group-drop"){let e=Number(r.id);eU(e,i.questionGroupId||null,n.groupId)}if(i?.type==="group"&&n?.type==="group-drop"){let e=i.groupId,t=n.groupId;e!==t&&eJ(e,t)}i?.type==="group"&&n?.type},children:(0,r.jsx)(a.gB,{items:[...eA.map(e=>e.id),...eG.map(e=>`group-${e.id}`)],strategy:a._G,children:(0,r.jsx)("div",{className:"space-y-4",children:0===eA.length?(0,r.jsxs)("div",{className:"text-center py-16 px-4",children:[(0,r.jsx)("h3",{className:"heading-text text-muted-foreground",children:u("noQuestionsYet")}),(0,r.jsx)("p",{className:"mt-1 text-sm sub-text",children:u("addFirstQuestion")}),(0,r.jsx)("div",{className:"p-4 flex justify-center",children:(0,r.jsxs)("button",{onClick:()=>h(!0),className:"btn-primary",disabled:!c,children:[(0,r.jsx)(q.A,{size:16}),u("addFirst")]})})]}):ez.map(e=>{if("group"===e.type){let l=e.data,i=l.question||[];return(0,r.jsx)("div",{className:"mb-4",children:(0,r.jsx)(f,{id:l.id,title:l.title,questions:i,subGroups:l.subGroups,parentGroupId:l.parentGroupId,nestingLevel:0,onEditGroup:eY,onDeleteGroup:eZ,onAddQuestionToGroup:e0,onEditQuestion:e=>{D(e),"table"===e.inputType?w(!0):g(!0)},onDeleteQuestion:e=>{D(e),$(!0)},onDuplicateQuestion:e=>{D(e),eR.mutate({id:e.id,contextType:t,contextId:s})},onReorderQuestions:eH,onMoveQuestionBetweenGroups:eU,onMoveGroupInsideGroup:eJ,isEditing:J===l.id,onStartEditing:e4,onSaveGroupName:e6,onCancelEditing:e5,editingName:_,onEditingNameChange:X,selectionMode:Y,isDraggable:!0,selectedQuestionIds:U,onToggleQuestionSelect:e1,onCreateSubgroup:eW})},`group-${l.id}`)}{let l=e.data;return(0,r.jsx)("div",{className:"mb-4",children:(0,r.jsx)(x,{question:l,onEdit:()=>{D(l),"table"===l.inputType?w(!0):g(!0)},onDelete:()=>{D(l),$(!0)},onDuplicate:()=>{D(l),eR.mutate({id:l.id,contextType:t,contextId:s})},selectionMode:Y,isSelected:U.includes(l.id),onToggleSelect:()=>e1(l.id)})},`question-${l.id}`)}})})})})}),ee&&(0,r.jsx)("div",{className:"fixed inset-0 bg-neutral-900/50 flex items-center justify-center z-40",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-md mx-4",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Create Subgroup"}),(0,r.jsxs)("p",{className:"text-sm text-neutral-600 mb-4",children:["Creating a subgroup with ",ep.length," selected question(s)."]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{htmlFor:"subgroup-name",className:"block text-sm font-medium mb-2",children:"Subgroup Name"}),(0,r.jsx)("input",{id:"subgroup-name",type:"text",value:eb,onChange:e=>ej(e.target.value),placeholder:"Enter subgroup name",className:"w-full px-3 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",autoFocus:!0,onKeyDown:e=>{"Enter"===e.key?e_():"Escape"===e.key&&eX()}})]}),(0,r.jsxs)("div",{className:"flex justify-end gap-3",children:[(0,r.jsx)("button",{onClick:eX,className:"px-4 py-2 text-neutral-600 hover:text-neutral-800 transition-colors",children:"Cancel"}),(0,r.jsx)("button",{onClick:e_,disabled:!eb.trim(),className:"px-4 py-2 bg-primary-500 text-white rounded-md hover:bg-primary-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:"Create Subgroup"})]})]})}),eA.length>0&&(0,r.jsx)("div",{className:"sticky bottom-0 p-4 flex justify-center",children:(0,r.jsxs)("button",{className:`btn-primary  max-w-md flex items-center justify-center gap-2 ${!c&&"text-gray-400 cursor-not-allowed"}`,onClick:()=>h(!0),disabled:!c,children:[(0,r.jsx)(q.A,{size:16}),u("addQuestion")]})})]})}}};