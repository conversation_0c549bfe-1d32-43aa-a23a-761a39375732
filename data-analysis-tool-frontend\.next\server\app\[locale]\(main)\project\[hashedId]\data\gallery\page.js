(()=>{var e={};e.id=8295,e.ids=[8295],e.modules={1933:(e,t)=>{"use strict";function r(e){var t;let{config:r,src:s,width:a,quality:i}=e,n=i||(null==(t=r.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return r.path+"?url="+encodeURIComponent(s)+"&w="+a+"&q="+n+(s.startsWith("/_next/static/media/"),"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return s}}),r.__next_img_default=!0;let s=r},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},12756:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{VALID_LOADERS:function(){return r},imageConfigDefault:function(){return s}});let r=["default","imgix","cloudinary","akamai","custom"],s={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},14959:(e,t,r)=>{"use strict";e.exports=r(94041).vendored.contexts.AmpContext},15535:(e,t,r)=>{Promise.resolve().then(r.bind(r,71638))},17903:(e,t,r)=>{"use strict";e.exports=r(94041).vendored.contexts.ImageConfigContext},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30512:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return m},defaultHead:function(){return c}});let s=r(14985),a=r(40740),i=r(60687),n=a._(r(43210)),o=s._(r(47755)),l=r(14959),d=r(89513),u=r(34604);function c(e){void 0===e&&(e=!1);let t=[(0,i.jsx)("meta",{charSet:"utf-8"},"charset")];return e||t.push((0,i.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),t}function p(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===n.default.Fragment?e.concat(n.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}r(50148);let f=["name","httpEquiv","charSet","itemProp"];function h(e,t){let{inAmpMode:r}=t;return e.reduce(p,[]).reverse().concat(c(r).reverse()).filter(function(){let e=new Set,t=new Set,r=new Set,s={};return a=>{let i=!0,n=!1;if(a.key&&"number"!=typeof a.key&&a.key.indexOf("$")>0){n=!0;let t=a.key.slice(a.key.indexOf("$")+1);e.has(t)?i=!1:e.add(t)}switch(a.type){case"title":case"base":t.has(a.type)?i=!1:t.add(a.type);break;case"meta":for(let e=0,t=f.length;e<t;e++){let t=f[e];if(a.props.hasOwnProperty(t))if("charSet"===t)r.has(t)?i=!1:r.add(t);else{let e=a.props[t],r=s[t]||new Set;("name"!==t||!n)&&r.has(e)?i=!1:(r.add(e),s[t]=r)}}}return i}}()).reverse().map((e,t)=>{let s=e.key||t;if(process.env.__NEXT_OPTIMIZE_FONTS&&!r&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props.href.startsWith(t))){let t={...e.props||{}};return t["data-href"]=t.href,t.href=void 0,t["data-optimized-fonts"]=!0,n.default.cloneElement(e,t)}return n.default.cloneElement(e,{key:s})})}let m=function(e){let{children:t}=e,r=(0,n.useContext)(l.AmpStateContext),s=(0,n.useContext)(d.HeadManagerContext);return(0,i.jsx)(o.default,{reduceComponentsToState:h,headManager:s,inAmpMode:(0,u.isInAmpMode)(r),children:t})};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},31261:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return l},getImageProps:function(){return o}});let s=r(14985),a=r(44953),i=r(46533),n=s._(r(1933));function o(e){let{props:t}=(0,a.getImgProps)(e,{defaultLoader:n.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let l=i.Image},33873:e=>{"use strict";e.exports=require("path")},34604:(e,t)=>{"use strict";function r(e){let{ampFirst:t=!1,hybrid:r=!1,hasQuery:s=!1}=void 0===e?{}:e;return t||r&&s}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return r}})},41480:(e,t)=>{"use strict";function r(e){let{widthInt:t,heightInt:r,blurWidth:s,blurHeight:a,blurDataURL:i,objectFit:n}=e,o=s?40*s:t,l=a?40*a:r,d=o&&l?"viewBox='0 0 "+o+" "+l+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+d+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(d?"none":"contain"===n?"xMidYMid":"cover"===n?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+i+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return r}})},44953:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return l}}),r(50148);let s=r(41480),a=r(12756),i=["-moz-initial","fill","none","scale-down",void 0];function n(e){return void 0!==e.default}function o(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function l(e,t){var r,l;let d,u,c,{src:p,sizes:f,unoptimized:h=!1,priority:m=!1,loading:g,className:x,quality:v,width:b,height:y,fill:j=!1,style:w,overrideSrc:_,onLoad:P,onLoadingComplete:C,placeholder:N="empty",blurDataURL:E,fetchPriority:S,decoding:O="async",layout:M,objectFit:k,objectPosition:I,lazyBoundary:R,lazyRoot:z,...A}=e,{imgConf:q,showAltText:D,blurComplete:U,defaultLoader:T}=t,G=q||a.imageConfigDefault;if("allSizes"in G)d=G;else{let e=[...G.deviceSizes,...G.imageSizes].sort((e,t)=>e-t),t=G.deviceSizes.sort((e,t)=>e-t),s=null==(r=G.qualities)?void 0:r.sort((e,t)=>e-t);d={...G,allSizes:e,deviceSizes:t,qualities:s}}if(void 0===T)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let F=A.loader||T;delete A.loader,delete A.srcSet;let L="__next_img_default"in F;if(L){if("custom"===d.loader)throw Object.defineProperty(Error('Image with src "'+p+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=F;F=t=>{let{config:r,...s}=t;return e(s)}}if(M){"fill"===M&&(j=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[M];e&&(w={...w,...e});let t={responsive:"100vw",fill:"100vw"}[M];t&&!f&&(f=t)}let K="",B=o(b),$=o(y);if((l=p)&&"object"==typeof l&&(n(l)||void 0!==l.src)){let e=n(p)?p.default:p;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(u=e.blurWidth,c=e.blurHeight,E=E||e.blurDataURL,K=e.src,!j)if(B||$){if(B&&!$){let t=B/e.width;$=Math.round(e.height*t)}else if(!B&&$){let t=$/e.height;B=Math.round(e.width*t)}}else B=e.width,$=e.height}let W=!m&&("lazy"===g||void 0===g);(!(p="string"==typeof p?p:K)||p.startsWith("data:")||p.startsWith("blob:"))&&(h=!0,W=!1),d.unoptimized&&(h=!0),L&&!d.dangerouslyAllowSVG&&p.split("?",1)[0].endsWith(".svg")&&(h=!0);let X=o(v),H=Object.assign(j?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:k,objectPosition:I}:{},D?{}:{color:"transparent"},w),V=U||"empty"===N?null:"blur"===N?'url("data:image/svg+xml;charset=utf-8,'+(0,s.getImageBlurSvg)({widthInt:B,heightInt:$,blurWidth:u,blurHeight:c,blurDataURL:E||"",objectFit:H.objectFit})+'")':'url("'+N+'")',J=i.includes(H.objectFit)?"fill"===H.objectFit?"100% 100%":"cover":H.objectFit,Y=V?{backgroundSize:J,backgroundPosition:H.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:V}:{},Z=function(e){let{config:t,src:r,unoptimized:s,width:a,quality:i,sizes:n,loader:o}=e;if(s)return{src:r,srcSet:void 0,sizes:void 0};let{widths:l,kind:d}=function(e,t,r){let{deviceSizes:s,allSizes:a}=e;if(r){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let s;s=e.exec(r);)t.push(parseInt(s[2]));if(t.length){let e=.01*Math.min(...t);return{widths:a.filter(t=>t>=s[0]*e),kind:"w"}}return{widths:a,kind:"w"}}return"number"!=typeof t?{widths:s,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>a.find(t=>t>=e)||a[a.length-1]))],kind:"x"}}(t,a,n),u=l.length-1;return{sizes:n||"w"!==d?n:"100vw",srcSet:l.map((e,s)=>o({config:t,src:r,quality:i,width:e})+" "+("w"===d?e:s+1)+d).join(", "),src:o({config:t,src:r,quality:i,width:l[u]})}}({config:d,src:p,unoptimized:h,width:B,quality:X,sizes:f,loader:F});return{props:{...A,loading:W?"lazy":g,fetchPriority:S,width:B,height:$,decoding:O,className:x,style:{...H,...Y},sizes:Z.sizes,srcSet:Z.srcSet,src:_||Z.src},meta:{unoptimized:h,priority:m,placeholder:N,fill:j}}}},45832:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Kobo-project\\\\data-analysis-tool-frontend\\\\app\\\\[locale]\\\\(main)\\\\project\\\\[hashedId]\\\\data\\\\gallery\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\project\\[hashedId]\\data\\gallery\\page.tsx","default")},46533:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Image",{enumerable:!0,get:function(){return y}});let s=r(14985),a=r(40740),i=r(60687),n=a._(r(43210)),o=s._(r(51215)),l=s._(r(30512)),d=r(44953),u=r(12756),c=r(17903);r(50148);let p=r(69148),f=s._(r(1933)),h=r(53038),m={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1};function g(e,t,r,s,a,i,n){let o=null==e?void 0:e.src;e&&e["data-loaded-src"]!==o&&(e["data-loaded-src"]=o,("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(e.parentElement&&e.isConnected){if("empty"!==t&&a(!0),null==r?void 0:r.current){let t=new Event("load");Object.defineProperty(t,"target",{writable:!1,value:e});let s=!1,a=!1;r.current({...t,nativeEvent:t,currentTarget:e,target:e,isDefaultPrevented:()=>s,isPropagationStopped:()=>a,persist:()=>{},preventDefault:()=>{s=!0,t.preventDefault()},stopPropagation:()=>{a=!0,t.stopPropagation()}})}(null==s?void 0:s.current)&&s.current(e)}}))}function x(e){return n.use?{fetchPriority:e}:{fetchpriority:e}}globalThis.__NEXT_IMAGE_IMPORTED=!0;let v=(0,n.forwardRef)((e,t)=>{let{src:r,srcSet:s,sizes:a,height:o,width:l,decoding:d,className:u,style:c,fetchPriority:p,placeholder:f,loading:m,unoptimized:v,fill:b,onLoadRef:y,onLoadingCompleteRef:j,setBlurComplete:w,setShowAltText:_,sizesInput:P,onLoad:C,onError:N,...E}=e,S=(0,n.useCallback)(e=>{e&&(N&&(e.src=e.src),e.complete&&g(e,f,y,j,w,v,P))},[r,f,y,j,w,N,v,P]),O=(0,h.useMergedRef)(t,S);return(0,i.jsx)("img",{...E,...x(p),loading:m,width:l,height:o,decoding:d,"data-nimg":b?"fill":"1",className:u,style:c,sizes:a,srcSet:s,src:r,ref:O,onLoad:e=>{g(e.currentTarget,f,y,j,w,v,P)},onError:e=>{_(!0),"empty"!==f&&w(!0),N&&N(e)}})});function b(e){let{isAppRouter:t,imgAttributes:r}=e,s={as:"image",imageSrcSet:r.srcSet,imageSizes:r.sizes,crossOrigin:r.crossOrigin,referrerPolicy:r.referrerPolicy,...x(r.fetchPriority)};return t&&o.default.preload?(o.default.preload(r.src,s),null):(0,i.jsx)(l.default,{children:(0,i.jsx)("link",{rel:"preload",href:r.srcSet?void 0:r.src,...s},"__nimg-"+r.src+r.srcSet+r.sizes)})}let y=(0,n.forwardRef)((e,t)=>{let r=(0,n.useContext)(p.RouterContext),s=(0,n.useContext)(c.ImageConfigContext),a=(0,n.useMemo)(()=>{var e;let t=m||s||u.imageConfigDefault,r=[...t.deviceSizes,...t.imageSizes].sort((e,t)=>e-t),a=t.deviceSizes.sort((e,t)=>e-t),i=null==(e=t.qualities)?void 0:e.sort((e,t)=>e-t);return{...t,allSizes:r,deviceSizes:a,qualities:i}},[s]),{onLoad:o,onLoadingComplete:l}=e,h=(0,n.useRef)(o);(0,n.useEffect)(()=>{h.current=o},[o]);let g=(0,n.useRef)(l);(0,n.useEffect)(()=>{g.current=l},[l]);let[x,y]=(0,n.useState)(!1),[j,w]=(0,n.useState)(!1),{props:_,meta:P}=(0,d.getImgProps)(e,{defaultLoader:f.default,imgConf:a,blurComplete:x,showAltText:j});return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(v,{..._,unoptimized:P.unoptimized,placeholder:P.placeholder,fill:P.fill,onLoadRef:h,onLoadingCompleteRef:g,setBlurComplete:y,setShowAltText:w,sizesInput:e.sizes,ref:t}),P.priority?(0,i.jsx)(b,{isAppRouter:!r,imgAttributes:_}):null]})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},47755:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n}});let s=r(43210),a=()=>{},i=()=>{};function n(e){var t;let{headManager:r,reduceComponentsToState:n}=e;function o(){if(r&&r.mountedInstances){let t=s.Children.toArray(Array.from(r.mountedInstances).filter(Boolean));r.updateHead(n(t,e))}}return null==r||null==(t=r.mountedInstances)||t.add(e.children),o(),a(()=>{var t;return null==r||null==(t=r.mountedInstances)||t.add(e.children),()=>{var t;null==r||null==(t=r.mountedInstances)||t.delete(e.children)}}),a(()=>(r&&(r._pendingUpdate=o),()=>{r&&(r._pendingUpdate=o)})),i(()=>(r&&r._pendingUpdate&&(r._pendingUpdate(),r._pendingUpdate=null),()=>{r&&r._pendingUpdate&&(r._pendingUpdate(),r._pendingUpdate=null)})),null}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},62391:(e,t,r)=>{Promise.resolve().then(r.bind(r,45832))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69148:(e,t,r)=>{"use strict";e.exports=r(94041).vendored.contexts.RouterContext},71638:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h});var s=r(60687),a=r(78122),i=r(62688);let n=(0,i.A)("grid-3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]]),o=(0,i.A)("list-filter",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M7 12h10",key:"b7w52i"}],["path",{d:"M10 18h4",key:"1ulq68"}]]);var l=r(80462),d=r(99270),u=r(43210),c=r(31261),p=r.n(c);let f=[{id:1,title:"Mountain Landscape",url:"https://images.unsplash.com/photo-1506905925346-21bda4d32df4",date:"2023-05-15",author:"Unsplash"},{id:2,title:"Ocean Sunset",url:"https://images.unsplash.com/photo-1503803548695-c2a7b4a5b875",date:"2023-05-16",author:"Unsplash"},{id:3,title:"Forest Path",url:"https://images.unsplash.com/photo-1448375240586-882707db888b",date:"2023-05-17",author:"Unsplash"},{id:4,title:"City Skyline",url:"https://images.unsplash.com/photo-1514565131-fce0801e5785",date:"2023-05-18",author:"Unsplash"},{id:5,title:"Desert Dunes",url:"https://images.unsplash.com/photo-1509316785289-025f5b846b35",date:"2023-05-19",author:"Unsplash"},{id:6,title:"Snowy Mountains",url:"https://images.unsplash.com/photo-1483728642387-6c3bdd6c93e5",date:"2023-05-20",author:"Unsplash"}];function h(){let[e,t]=(0,u.useState)(""),[r,i]=(0,u.useState)("grid"),[c,h]=(0,u.useState)(f),[m,g]=(0,u.useState)(!0);return(0,s.jsxs)("div",{className:"flex flex-col space-y-6",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("h1",{className:"text-2xl font-semibold text-neutral-800",children:"Gallery"}),(0,s.jsx)("div",{className:"flex gap-2",children:(0,s.jsxs)("button",{className:"btn-primary",title:"Refresh gallery",onClick:()=>g(!0),children:[(0,s.jsx)(a.A,{className:`w-4 h-4 ${m?"animate-spin":""}`}),(0,s.jsx)("span",{children:"Refresh"})]})})]}),(0,s.jsxs)("div",{className:"flex justify-between gap-4 flex-wrap",children:[(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsxs)("button",{className:`btn-primary ${"grid"===r?"bg-primary-600 text-neutral-100":"bg-primary-500 hover:bg-primary-600 text-neutral-100"}`,onClick:()=>i("grid"),children:[(0,s.jsx)(n,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"Grid"})]}),(0,s.jsxs)("button",{className:`btn-primary ${"list"===r?"bg-primary-600 text-neutral-100":"bg-primary-500 hover:bg-primary-600 text-neutral-100"}`,onClick:()=>i("list"),children:[(0,s.jsx)(o,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"List"})]}),(0,s.jsxs)("button",{className:"btn-primary",children:[(0,s.jsx)(l.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"Filter"})]})]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{type:"text",placeholder:"Search images...",className:"pl-9 pr-4 py-2 border border-neutral-300 rounded w-64 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 focus:outline-none transition-colors",value:e,onChange:e=>t(e.target.value)}),(0,s.jsx)(d.A,{className:"absolute left-3 top-2.5 w-4 h-4 text-neutral-400"})]})]}),m?(0,s.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:[1,2,3,4,5,6].map(e=>(0,s.jsxs)("div",{className:"bg-neutral-100 border border-neutral-200 rounded-md overflow-hidden shadow-sm",children:[(0,s.jsx)("div",{className:"aspect-video bg-neutral-200 animate-pulse"}),(0,s.jsxs)("div",{className:"p-3",children:[(0,s.jsx)("div",{className:"h-4 bg-neutral-200 rounded animate-pulse w-3/4 mb-2"}),(0,s.jsx)("div",{className:"h-3 bg-neutral-200 rounded animate-pulse w-1/2"})]})]},e))}):(0,s.jsx)(s.Fragment,{children:"grid"===r?(0,s.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:c.map(e=>(0,s.jsxs)("div",{className:"bg-neutral-100 border border-neutral-200 rounded-md overflow-hidden shadow-sm hover:shadow-md transition-shadow",children:[(0,s.jsx)("div",{className:"aspect-video overflow-hidden bg-neutral-100 relative",children:(0,s.jsx)(p(),{src:`${e.url}?w=600&h=400&auto=format&fit=crop`,alt:e.title,fill:!0,sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",className:"object-cover hover:scale-105 transition-transform duration-300"})}),(0,s.jsxs)("div",{className:"p-3",children:[(0,s.jsx)("h3",{className:"font-medium text-neutral-800",children:e.title}),(0,s.jsxs)("p",{className:"text-xs text-neutral-500 mt-1",children:[e.date," • Photo by ",e.author]})]})]},e.id))}):(0,s.jsx)("div",{className:"bg-neutral-100 border border-neutral-200 rounded-md overflow-hidden shadow-sm",children:(0,s.jsx)("ul",{className:"divide-y divide-neutral-200",children:c.map(e=>(0,s.jsxs)("li",{className:"flex items-center gap-4 p-3 hover:bg-neutral-50",children:[(0,s.jsx)("div",{className:"w-20 h-16 bg-neutral-100 overflow-hidden rounded relative",children:(0,s.jsx)(p(),{src:`${e.url}?w=160&h=120&auto=format&fit=crop`,alt:e.title,fill:!0,sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",className:"object-cover"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-medium text-neutral-800",children:e.title}),(0,s.jsxs)("p",{className:"text-xs text-neutral-500 mt-1",children:[e.date," • Photo by ",e.author]})]})]},e.id))})})})]})}},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},85594:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>c,pages:()=>u,routeModule:()=>p,tree:()=>d});var s=r(65239),a=r(48088),i=r(88170),n=r.n(i),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["[locale]",{children:["(main)",{children:["project",{children:["[hashedId]",{children:["data",{children:["gallery",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,45832)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\project\\[hashedId]\\data\\gallery\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,87282)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\project\\[hashedId]\\data\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,51129)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\project\\[hashedId]\\layout.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,84606)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,72121)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\project\\[hashedId]\\data\\gallery\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/(main)/project/[hashedId]/data/gallery/page",pathname:"/[locale]/project/[hashedId]/data/gallery",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},89513:(e,t,r)=>{"use strict";e.exports=r(94041).vendored.contexts.HeadManagerContext},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,7404,1658,6560,8610,5374,2198,5814,3851,8581,5841,5041,8626],()=>r(85594));module.exports=s})();