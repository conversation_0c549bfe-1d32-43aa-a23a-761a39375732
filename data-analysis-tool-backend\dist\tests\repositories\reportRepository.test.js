"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const reportRepository_1 = __importDefault(require("../../repositories/reportRepository"));
const prisma_1 = require("../../utils/prisma");
// Mock the prisma client
jest.mock("../../utils/prisma", () => ({
    prisma: {
        project: {
            findUnique: jest.fn(),
        },
        formSubmission: {
            findMany: jest.fn(),
        },
    },
}));
describe("Report Repository", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });
    describe("generateReport", () => {
        it("should include all question types in the report", () => __awaiter(void 0, void 0, void 0, function* () {
            // Mock project with different question types
            const mockProject = {
                id: 1,
                name: "Test Project",
                questions: [
                    {
                        id: 1,
                        label: "Select One Question",
                        inputType: "selectone",
                        questionOptions: [
                            { id: 1, label: "Option 1" },
                            { id: 2, label: "Option 2" },
                        ],
                    },
                    {
                        id: 2,
                        label: "Select Many Question",
                        inputType: "selectmany",
                        questionOptions: [
                            { id: 3, label: "Option A" },
                            { id: 4, label: "Option B" },
                        ],
                    },
                    {
                        id: 3,
                        label: "Text Question",
                        inputType: "text",
                        questionOptions: [],
                    },
                    {
                        id: 4,
                        label: "Number Question",
                        inputType: "number",
                        questionOptions: [],
                    },
                ],
            };
            // Mock form submissions
            const mockSubmissions = [
                {
                    id: 1,
                    createdAt: new Date(),
                    answers: [
                        {
                            questionId: 1,
                            questionOptionId: 1,
                            value: "",
                        },
                        {
                            questionId: 2,
                            questionOptionId: null,
                            value: "[3, 4]",
                        },
                        {
                            questionId: 3,
                            questionOptionId: null,
                            value: "Text answer",
                        },
                        {
                            questionId: 4,
                            questionOptionId: null,
                            value: "42",
                        },
                    ],
                },
            ];
            // Setup mocks
            prisma_1.prisma.project.findUnique.mockResolvedValue(mockProject);
            prisma_1.prisma.formSubmission.findMany.mockResolvedValue(mockSubmissions);
            // Call the method
            const report = yield reportRepository_1.default.generateReport(1, {});
            // Verify all questions are included
            expect(report.data.length).toBe(4);
            // Verify each question type is included
            const selectOneQuestion = report.data.find(item => item.question === "Select One Question");
            const selectManyQuestion = report.data.find(item => item.question === "Select Many Question");
            const textQuestion = report.data.find(item => item.question === "Text Question");
            const numberQuestion = report.data.find(item => item.question === "Number Question");
            expect(selectOneQuestion).toBeDefined();
            expect(selectManyQuestion).toBeDefined();
            expect(textQuestion).toBeDefined();
            expect(numberQuestion).toBeDefined();
            // Verify question types are correct
            expect(selectOneQuestion === null || selectOneQuestion === void 0 ? void 0 : selectOneQuestion.type).toBe("selectone");
            expect(selectManyQuestion === null || selectManyQuestion === void 0 ? void 0 : selectManyQuestion.type).toBe("selectmany");
            expect(textQuestion === null || textQuestion === void 0 ? void 0 : textQuestion.type).toBe("text");
            expect(numberQuestion === null || numberQuestion === void 0 ? void 0 : numberQuestion.type).toBe("number");
            // Verify summary statistics are correct
            expect(report.summary.totalQuestions).toBe(4);
        }));
        it("should handle empty questions array", () => __awaiter(void 0, void 0, void 0, function* () {
            // Mock project with no questions
            const mockProject = {
                id: 1,
                name: "Test Project",
                questions: [],
            };
            // Mock empty submissions
            const mockSubmissions = [];
            // Setup mocks
            prisma_1.prisma.project.findUnique.mockResolvedValue(mockProject);
            prisma_1.prisma.formSubmission.findMany.mockResolvedValue(mockSubmissions);
            // Call the method
            const report = yield reportRepository_1.default.generateReport(1, {});
            // Verify empty data array
            expect(report.data).toEqual([]);
            // Verify summary statistics
            expect(report.summary.totalQuestions).toBe(0);
            expect(report.summary.totalSubmissions).toBe(0);
            expect(report.summary.averageResponseRate).toBe(0);
        }));
    });
});
