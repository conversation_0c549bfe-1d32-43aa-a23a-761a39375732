(()=>{var e={};e.id=4040,e.ids=[4040],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11822:(e,t,r)=>{Promise.resolve().then(r.bind(r,71667))},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29848:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>w});var s=r(60687),a=r(12810);let o=async(e,t)=>{try{let{data:r}=await a.A.post(`/export/${e}?type=${t}`,{});return r}catch(e){throw console.error("Error exporting data:",e),e}},n=async e=>{try{let{data:t}=await a.A.get(`/export?projectId=${e}`);return t}catch(e){throw console.error("Error fetching export data:",e),e}},l=async(e,t)=>{try{let r=await a.A.get(`/export/download/${e}`,{responseType:"blob"}),s=r.headers["content-disposition"];if(s){let e=s.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);e&&e[1]&&(t=e[1].replace(/['"]/g,""))}let o=new Blob([r.data]),n=window.URL.createObjectURL(o),l=document.createElement("a");l.href=n,l.download=t,document.body.appendChild(l),l.click(),l.remove(),window.URL.revokeObjectURL(n)}catch(e){throw console.error("Error downloading data:",e),e}};var i=r(6986),d=r(8693),c=r(54050),p=r(29494),u=r(54864),x=r(19150),m=r(26273),h=r(78122),f=r(31158),y=r(80462),b=r(99270),j=r(16189),v=r(43210),g=r(77618);function w(){let e=(0,u.wA)(),t=(0,d.jE)(),[r,a]=(0,v.useState)(""),[w,N]=(0,v.useState)(""),{hashedId:q}=(0,j.useParams)(),P=(0,i.D)(q),E=(0,g.c3)(),[k,C]=(0,v.useState)(null),D=e=>new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric"}).format(new Date(e)),A=(0,c.n)({mutationFn:()=>o([P],w),onSuccess:()=>{e((0,x.Ds)({message:E("exportedSuccessfully"),type:"success"})),t.invalidateQueries({queryKey:["exportData",P]})},onError:t=>{e((0,x.Ds)({message:E("errorExporting"),type:"error"}))}}),_=async(t,r)=>{C(t);try{null!==t&&r?await l(t.toString(),r):e((0,x.Ds)({message:E("noFileSelected"),type:"error"}))}catch(t){e((0,x.Ds)({message:E("downloadFailed"),type:"error"}))}finally{C(null)}},{data:U,isLoading:I}=(0,p.I)({queryKey:["exportData",P],queryFn:()=>n([P]),enabled:!!P});return(0,s.jsxs)("div",{className:"flex flex-col space-y-6",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("h1",{className:"text-2xl font-semibold text-neutral-800",children:E("downloads")}),(0,s.jsx)("div",{className:"flex gap-2",children:(0,s.jsxs)("button",{className:"btn-primary",title:E("refreshDownloads"),children:[(0,s.jsx)(h.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:E("refresh")})]})})]}),(0,s.jsxs)("div",{className:"flex justify-between gap-4 flex-wrap",children:[(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsxs)("select",{value:w,onChange:e=>N(e.target.value),className:"border border-neutral-300 rounded px-3 py-2 bg-neutral-100 text-neutral-800 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 focus:outline-none transition-colors cursor-pointer",children:[(0,s.jsx)("option",{children:E("exportFormat")}),(0,s.jsx)("option",{children:"csv"}),(0,s.jsx)("option",{children:"excel"})]}),(0,s.jsxs)("button",{onClick:()=>{A.mutate()},className:"btn-primary",children:[(0,s.jsx)(f.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:E("export")})]}),(0,s.jsxs)("button",{className:"border btn-primary",children:[(0,s.jsx)(y.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:E("filter")})]})]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{type:"text",placeholder:E("searchFiles"),className:"pl-9 pr-4 py-2 border border-neutral-300 rounded w-64 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 focus:outline-none transition-colors",value:r,onChange:e=>a(e.target.value)}),(0,s.jsx)(b.A,{className:"absolute left-3 top-2.5 w-4 h-4 text-neutral-400"})]})]}),(0,s.jsxs)("div",{className:"bg-neutral-100 border border-neutral-200 rounded-md overflow-hidden shadow-sm",children:[(0,s.jsxs)("div",{className:"grid grid-cols-12 py-2 px-4 bg-primary-50 border-b border-neutral-200",children:[(0,s.jsx)("div",{className:"col-span-6 text-xs font-medium text-primary-700 uppercase",children:E("name")}),(0,s.jsx)("div",{className:"col-span-2 text-xs font-medium text-primary-700 uppercase",children:E("type")}),(0,s.jsx)("div",{className:"col-span-2 text-xs font-medium text-primary-700 uppercase",children:E("date")})]}),(0,s.jsx)("ul",{className:"divide-y divide-neutral-200",children:U?.data?.files?.map(e=>(0,s.jsxs)("li",{className:"grid grid-cols-12 py-3 px-4 hover:bg-primary-50 items-center",children:[(0,s.jsxs)("div",{className:"col-span-6 flex items-center gap-3",children:[(0,s.jsx)(m.t2D,{className:"w-5 h-5 text-neutral-400"}),(0,s.jsx)("span",{className:"font-medium text-neutral-800",children:e.fileName})]}),(0,s.jsx)("div",{className:"col-span-2 text-sm text-neutral-600 uppercase",children:e.fileType}),(0,s.jsx)("div",{className:"col-span-1 text-sm text-neutral-600",children:D(e.createdAt)}),(0,s.jsx)("div",{className:"col-span-1",children:(0,s.jsx)("button",{onClick:()=>{_(e.id,e.fileName)},className:"p-1 rounded-full hover:bg-primary-500 hover:text-neutral-100 text-neutral-700 transition-colors cursor-pointer",title:E("downloadFile"),children:(0,s.jsx)(f.A,{className:"w-4 h-4"})})})]},e.id))})]})]})}},33873:e=>{"use strict";e.exports=require("path")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71667:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Kobo-project\\\\data-analysis-tool-frontend\\\\app\\\\[locale]\\\\(main)\\\\project\\\\[hashedId]\\\\data\\\\downloads\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\project\\[hashedId]\\data\\downloads\\page.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},74870:(e,t,r)=>{Promise.resolve().then(r.bind(r,29848))},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84396:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d});var s=r(65239),a=r(48088),o=r(88170),n=r.n(o),l=r(30893),i={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);r.d(t,i);let d={children:["",{children:["[locale]",{children:["(main)",{children:["project",{children:["[hashedId]",{children:["data",{children:["downloads",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,71667)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\project\\[hashedId]\\data\\downloads\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,87282)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\project\\[hashedId]\\data\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,51129)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\project\\[hashedId]\\layout.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,84606)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,72121)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\project\\[hashedId]\\data\\downloads\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/(main)/project/[hashedId]/data/downloads/page",pathname:"/[locale]/project/[hashedId]/data/downloads",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,7404,1658,6560,7618,63,7605,3851,8581,6226,5233,8626],()=>r(84396));module.exports=s})();