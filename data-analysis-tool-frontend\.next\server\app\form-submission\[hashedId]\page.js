(()=>{var e={};e.id=7893,e.ids=[7893],e.modules={1510:(e,t,r)=>{"use strict";r.d(t,{F0:()=>p,pe:()=>i});let{Axios:s,AxiosError:i,CanceledError:a,isCancel:n,CancelToken:o,VERSION:l,all:d,Cancel:u,isAxiosError:p,spread:c,toFormData:m,AxiosHeaders:h,HttpStatusCode:x,formToJSON:f,getAdapter:b,mergeConfig:y}=r(51060).A},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21650:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var s=r(42895),i=r(1510),a=r(12810),n=r(16189),o=r(43210),l=r(54864);let d=e=>{let t=(0,l.wA)(),r=(0,n.useRouter)(),d=(0,n.usePathname)(),{status:u,user:p,error:c}=(0,l.d4)(e=>e.auth),m=async()=>{try{t((0,s.Le)());let e=(await a.A.get("/users/me")).data;t((0,s.tQ)(e))}catch(e){if(t((0,s.x9)()),(0,i.F0)(e))if(console.error("Auth error:",e.response?.status,e.response?.data),e.response?.status===401){if(d.startsWith("/form-submission"))return;r.push("/")}else t((0,s.jB)(e.response?.data?.message||e.message));else t((0,s.jB)(e instanceof Error?e.message:"An unknown error occurred."))}};return(0,o.useEffect)(()=>{e?.skipFetchUser||m()},[e?.skipFetchUser]),(0,o.useEffect)(()=>{let e=e=>{if("logout"===e.key&&"true"===e.newValue)if(t((0,s.x9)()),d.startsWith("/form-submission")){let e=d.split("/")[2];e?r.push(`/form-submission/${e}/sign-in`):r.push("/")}else r.push("/")};return window.addEventListener("storage",e),()=>{window.removeEventListener("storage",e)}},[t,r,d]),{status:u,user:p,error:c,isAuthenticated:"authenticated"===u,isLoading:"loading"===u,refreshAuthState:()=>{m()},signin:async(e,t,r)=>{try{await a.A.post("/users/login",e),await m(),t?.()}catch(e){if(e instanceof i.pe){let t=e.response?.data?.errorType;r?.(t)}else r?.()}},logout:async()=>{try{await a.A.post("/users/logout"),localStorage.setItem("logout","true"),setTimeout(()=>localStorage.removeItem("logout"),100)}finally{if(t((0,s.x9)()),d.startsWith("/form-submission")){let e=d.split("/")[2];e?r.push(`/form-submission/${e}/sign-in`):r.push("/")}else r.push("/")}}}}},21820:e=>{"use strict";e.exports=require("os")},24746:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Kobo-project\\\\data-analysis-tool-frontend\\\\app\\\\form-submission\\\\[hashedId]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\form-submission\\[hashedId]\\page.tsx","default")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},51401:(e,t,r)=>{Promise.resolve().then(r.bind(r,24746))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58025:(e,t,r)=>{Promise.resolve().then(r.bind(r,93044))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},93044:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>k});var s=r(60687),i=r(43210),a=r(29494),n=r(54050),o=r(16189),l=r(6986),d=r(75531),u=r(96),p=r(71845),c=r(86429),m=r(39390),h=r(15616),x=r(93437),f=r(40347),b=r(70334),y=r(54864),g=r(19150),v=r(13784);r(24527);var j=r(69396),q=r(44305),N=r(31207),w=r(21650);function k(){let e=(0,y.wA)();(0,o.useRouter)();let{hashedId:t}=(0,o.useParams)(),r=(0,l.D)(t),{status:k,isAuthenticated:A,isLoading:I}=(0,w.A)(),[C,E]=(0,i.useState)({}),[S,P]=(0,i.useState)({}),[O,T]=(0,i.useState)(!1),[_,F]=(0,i.useState)([]),[R,$]=(0,i.useState)([]),[D,M]=(0,i.useState)({}),{data:K,isLoading:G,isError:L}=(0,a.I)({queryKey:["questions",r],queryFn:()=>(0,d.K4)({projectId:r}),enabled:!!r}),{data:Q=[]}=(0,a.I)({queryKey:["questionGroups",r],queryFn:()=>(0,u.pr)({projectId:r}),enabled:!!r}),{data:U}=(0,a.I)({queryKey:["project",r],queryFn:()=>(0,p.kf)({projectId:r}),enabled:!!r}),z=(0,i.useMemo)(()=>(0,N.yi)(Q,K||[]),[Q,K]),J=(0,i.useMemo)(()=>(0,N.ru)(K||[]),[K]),Y=(0,i.useMemo)(()=>(0,N.XV)(z,J),[z,J]),V=(0,i.useCallback)(e=>{M(t=>({...t,[e]:!t[e]}))},[]),W=(0,n.n)({mutationFn:async e=>{let t=K?.map(t=>{let s,i,a,n=e[t.id],o="selectmany"===t.inputType,l="selectone"===t.inputType;if(!o&&!l&&(null==n||""===n)||l&&(!n||""===n.trim()))return null;if(o&&Array.isArray(n)&&t.questionOptions){let e=n.map(e=>{let r=t.questionOptions.find(t=>t.label===e);return r?.id}).filter(e=>void 0!==e);s=e.length>0?e:[]}else if(l&&n&&t.questionOptions){let e=t.questionOptions.find(e=>e.label===n);if(void 0===(s=e?.id))return console.warn(`Could not find option ID for selectone question ${t.id} with value "${n}"`),null}if(null==(i=o?Array.isArray(n)?n.join(", "):"":"number"===t.inputType||"decimal"===t.inputType?n?Number(n):void 0:"date"===t.inputType||"dateandtime"===t.inputType?n||void 0:"table"===t.inputType?Array.isArray(n)&&n.length>0?JSON.stringify(n):void 0:n?String(n):void 0))return null;a=o?Array.isArray(s)?s:[]:l&&"number"==typeof s?s:void 0;let d={projectId:Number(r),questionId:t.id,answerType:String(t.inputType),value:i,isOtherOption:!1};return void 0!==a&&(d.questionOptionId=a),d}).filter(e=>null!==e)||[];if(0===t.length)throw Error("No valid answers to submit. Please fill out at least one field.");return await (0,p.lj)(t)},onSuccess:()=>{e((0,g.Ds)({message:"Form submitted successfully",type:"success"})),E({}),window.dispatchEvent(new Event("form-submitted")),localStorage.setItem("form_submitted",Date.now().toString())},onError:t=>{e((0,g.Ds)({message:"Failed to submit form. Please try again.",type:"error"})),console.error("Submission Error:",t)},onSettled:()=>{T(!1)}}),B=(0,i.useCallback)((e,t)=>{E(r=>({...r,[e]:t})),P(t=>({...t,[e]:""}))},[]),X=()=>{let e={};return _.forEach(t=>{if(t.isRequired){let r=C[t.id];("string"==typeof r&&!r.trim()||Array.isArray(r)&&0===r.length||null==r)&&(e[t.id]=`${t.label} is required`)}}),P(e),0===Object.keys(e).length},H=async e=>{e.preventDefault(),X()&&(T(!0),W.mutate(C))},Z=e=>!!K&&K.some(t=>t.questionOptions?.some(t=>t.nextQuestionId===e)),ee=e=>e.questionOptions?.some(e=>e.nextQuestionId)||!1,et=e=>{let t=C[e.id]??("selectmany"===e.inputType?[]:"");switch(e.inputType){case"text":if(e.hint?.includes("multiline"))return(0,s.jsx)(h.T,{value:t,onChange:t=>B(e.id,t.target.value),placeholder:e.placeholder||"Your answer",required:e.isRequired});return(0,s.jsx)("input",{className:"input-field w-full",value:t,onChange:t=>B(e.id,t.target.value),placeholder:e.placeholder||"Your answer",required:e.isRequired});case"number":return(0,s.jsx)("input",{className:"input-field w-full",type:"number",value:t,onChange:t=>B(e.id,t.target.value),placeholder:e.placeholder||"Your answer",required:e.isRequired});case"decimal":return(0,s.jsx)("input",{className:"input-field w-full",type:"number",step:"any",value:t,onChange:t=>B(e.id,t.target.value),placeholder:e.placeholder||"Your answer",required:e.isRequired});case"selectone":return(0,s.jsx)(f.z,{value:t,onValueChange:t=>B(e.id,t),required:e.isRequired,children:(0,s.jsx)("div",{className:"space-y-2",children:e.questionOptions?.map((e,t)=>(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(f.C,{value:e.label,id:`option-${e.id}`}),(0,s.jsx)(m.J,{htmlFor:`option-${e.id}`,className:"cursor-pointer",children:e.label}),e.sublabel&&(0,s.jsx)("p",{className:"text-sm text-neutral-700 ml-4",children:`(${e.sublabel})`})]},t))})});case"selectmany":return(0,s.jsx)("div",{className:"space-y-2",children:e.questionOptions?.map(r=>(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(x.S,{id:`option-${r.id}`,checked:(t||[]).includes(r.label),onCheckedChange:s=>{let i=t||[],a=s?[...i,r.label]:i.filter(e=>e!==r.label);B(e.id,a)}}),(0,s.jsx)(m.J,{htmlFor:`option-${r.id}`,className:"cursor-pointer",children:r.label})]},r.id))});case"date":return(0,s.jsx)("div",{className:"relative",children:(0,s.jsx)("input",{className:"input-field w-full",type:"date",value:t,onChange:t=>B(e.id,t.target.value),placeholder:e.placeholder||"Select date",required:e.isRequired})});case"dateandtime":return(0,s.jsx)("div",{className:"relative",children:(0,s.jsx)("input",{className:"input-field w-full",type:"time",value:t,onChange:t=>B(e.id,t.target.value),placeholder:e.placeholder||"Select time",required:e.isRequired})});case"table":return(0,s.jsx)(v.N,{questionId:e.id,value:t,onChange:t=>B(e.id,t),required:e.isRequired,tableLabel:e.label});default:return null}},er=e=>{let t=Z(e.id),r=ee(e);return(0,s.jsxs)("div",{className:`border rounded-md p-4 ${t?"border-primary-200 dark:border-primary-700 bg-primary-100 dark:bg-primary-900/20":"border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800"}`,children:[(0,s.jsxs)("div",{className:"mb-2",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsxs)(m.J,{className:"text-base font-medium",children:[e.label,e.isRequired&&(0,s.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),t&&(0,s.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-200 text-primary-800 dark:bg-primary-900 dark:text-primary-200",children:[(0,s.jsx)(b.A,{className:"w-3 h-3 mr-1"}),"Follow-up"]}),r&&(0,s.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-accent-200 text-accent-800 dark:bg-accent-700/20 dark:text-accent-200",children:"Has conditions"})]}),e.hint&&(0,s.jsx)("p",{className:`text-sm mt-1 ${t?"text-primary-700 dark:text-primary-300":"text-muted-foreground"}`,children:e.hint}),S[e.id]&&(0,s.jsx)("p",{className:"text-sm text-red-500 mt-1",children:S[e.id]})]}),(0,s.jsx)("div",{className:"mt-2",children:et(e)})]},e.id)};return I||G?(0,s.jsx)(c.A,{}):A?L||!K?(0,s.jsx)("p",{className:"text-sm text-red-500",children:"Error loading form. Please try again."}):(0,s.jsx)("div",{className:"min-h-screen w-full bg-neutral-100 dark:bg-gray-900 flex flex-col items-center p-4 md:p-6",children:(0,s.jsxs)("div",{className:"w-full max-w-screen-lg bg-neutral-100 dark:bg-gray-800 rounded-md shadow-sm border border-gray-200 dark:border-gray-700",children:[(0,s.jsxs)("h2",{className:"text-2xl font-semibold p-6 border-b border-gray-200 dark:border-gray-700",children:["Form Submission ",U?.name?` for ${U.name}`:""]}),(0,s.jsx)("form",{onSubmit:H,className:"p-6",children:(0,s.jsxs)("div",{className:"space-y-6",children:[K&&0!==K.length?Y.map(e=>{if("group"===e.type){let t=e.data,r=D[t.id];return(0,s.jsx)(q.A,{group:t,nestingLevel:0,visibleQuestions:_,nestedQuestions:R,renderQuestionInput:et,errors:S,onToggleExpansion:V,isExpanded:r,expandedGroups:D,className:""},`group-${t.id}`)}{let t=e.data;if(!_.some(e=>e.id===t.id))return null;let r=R.find(e=>e.question.id===t.id);return r?(0,s.jsx)(j.A,{questionGroup:r,renderQuestionInput:et,errors:S,className:""},t.id):er(t)}}):(0,s.jsx)("div",{className:"text-center py-12",children:(0,s.jsx)("p",{className:"text-muted-foreground",children:"This form has no questions yet."})}),K.length>0&&(0,s.jsx)("div",{className:"mt-6 flex justify-end",children:(0,s.jsx)("button",{className:"btn-primary",type:"submit",disabled:O,children:O?"Submitting...":"Submit Form"})}),0===K.length&&(0,s.jsx)("div",{className:"text-center py-12",children:(0,s.jsx)("p",{className:"text-muted-foreground",children:"This form has no questions yet."})}),K&&K.length>0&&0===_.length&&(0,s.jsx)("div",{className:"text-center py-12",children:(0,s.jsx)("p",{className:"text-muted-foreground",children:"No questions are currently visible. Please check your form configuration."})})]})})]})}):null}},94735:e=>{"use strict";e.exports=require("events")},99956:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>u,routeModule:()=>c,tree:()=>d});var s=r(65239),i=r(48088),a=r(88170),n=r.n(a),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["form-submission",{children:["[hashedId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,24746)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\form-submission\\[hashedId]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\form-submission\\[hashedId]\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},c=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/form-submission/[hashedId]/page",pathname:"/form-submission/[hashedId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,7404,1658,6560,3851,3341,3571],()=>r(99956));module.exports=s})();