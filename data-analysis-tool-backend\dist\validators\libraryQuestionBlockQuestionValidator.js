"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LibraryQuestionBlockQuestionSchema = exports.LibraryQuestionBlockQuestionGroupSchema = exports.LibraryQuestionBlockQuestionConditionSchema = exports.LibraryQuestionBlockQuestionOptionSchema = void 0;
const zod_1 = require("zod");
const client_1 = require("@prisma/client");
// Example enum for InputType and Operator
// LibraryQuestionBlockQuestionOption Schema
exports.LibraryQuestionBlockQuestionOptionSchema = zod_1.z.object({
    id: zod_1.z.number().int().optional(), // optional if creating
    label: zod_1.z.string(),
    code: zod_1.z.string(),
    nextLibraryQuestionId: zod_1.z.number().int().nullable().optional(),
    libraryQuestionBlockQuestionId: zod_1.z.number().int(),
});
// LibraryQuestionBlockQuestionCondition Schema
exports.LibraryQuestionBlockQuestionConditionSchema = zod_1.z.object({
    id: zod_1.z.number().int().optional(),
    operator: zod_1.z.nativeEnum(client_1.Operator, {
        errorMap: () => ({ message: "Invalid operator selected" }),
    }),
    value: zod_1.z.string(),
    libraryQuestionBlockQuestionId: zod_1.z.number().int(),
});
// LibraryQuestionBlockQuestionGroup Schema
exports.LibraryQuestionBlockQuestionGroupSchema = zod_1.z.object({
    id: zod_1.z.number().int().optional(),
    title: zod_1.z.string(),
    order: zod_1.z.number().int().optional(),
    parentGroupId: zod_1.z.number().int().nullable().optional(),
});
// LibraryQuestionBlockQuestion Schema
exports.LibraryQuestionBlockQuestionSchema = zod_1.z
    .object({
    id: zod_1.z.number().int().optional(),
    label: zod_1.z.string(),
    inputType: zod_1.z.nativeEnum(client_1.InputType),
    hint: zod_1.z.string().optional().nullable(),
    placeholder: zod_1.z.string().optional().nullable(),
    isRequired: zod_1.z.boolean().default(false),
    position: zod_1.z.number().int(),
    userId: zod_1.z.number().int(),
    // libraryQuestionBlockQuestionGroupId: z.number().int().nullable().optional(),
    questionOptions: zod_1.z
        .array(zod_1.z.object({
        id: zod_1.z.number().optional(),
        label: zod_1.z.string(),
        code: zod_1.z.string(),
        nextQuestionId: zod_1.z.number().optional(),
    }))
        .optional(),
    conditions: zod_1.z
        .array(exports.LibraryQuestionBlockQuestionConditionSchema)
        .optional()
        .nullable(),
})
    .superRefine((data, ctx) => {
    if ((data.inputType === client_1.InputType.selectone ||
        data.inputType === client_1.InputType.selectmany) &&
        (!data.questionOptions || data.questionOptions.length === 0)) {
        ctx.addIssue({
            code: zod_1.z.ZodIssueCode.custom,
            path: ["questionOptions"],
            message: "Options are required for select input types.",
        });
    }
});
