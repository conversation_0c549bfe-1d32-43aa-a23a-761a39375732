"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const questionGroupController_1 = require("../controllers/questionGroupController");
const router = express_1.default.Router();
router.get("/", questionGroupController_1.findAllProjectGroup);
router.post("/", questionGroupController_1.createQuestionGroup);
router.patch("/", questionGroupController_1.updateQuestionGroup);
router.delete("/:id", questionGroupController_1.deleteQuestionGroup);
router.delete("/group/question/:id", questionGroupController_1.deleteQuestionAndGroup);
router.patch("/question/remove", questionGroupController_1.removeQuestionIdFromGroup);
router.patch("/question/move", questionGroupController_1.updateQuestionFromOneGroupToAnother);
router.patch("/group/add", questionGroupController_1.updateOneGroupInsideAnotherGroup);
router.patch("/group/remove", questionGroupController_1.removeGroupFromParentGroup);
router.patch("/positions", questionGroupController_1.updateGroupPositions);
exports.default = router;
