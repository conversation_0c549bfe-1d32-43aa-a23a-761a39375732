(()=>{var e={};e.id=7042,e.ids=[7042],e.modules={22:(e,t,r)=>{var n=r(75254),o=r(20623),i=r(48169),a=r(40542),s=r(45058);e.exports=function(e){return"function"==typeof e?e:null==e?i:"object"==typeof e?a(e)?o(e[0],e[1]):n(e):s(e)}},658:(e,t,r)=>{e.exports=r(41547)(r(85718),"Map")},1566:(e,t,r)=>{var n=r(89167),o=r(658),i=r(30401),a=r(34772),s=r(17830),l=r(29395),c=r(12290),u="[object Map]",f="[object Promise]",p="[object Set]",d="[object WeakMap]",h="[object DataView]",y=c(n),v=c(o),m=c(i),b=c(a),g=c(s),x=l;(n&&x(new n(new ArrayBuffer(1)))!=h||o&&x(new o)!=u||i&&x(i.resolve())!=f||a&&x(new a)!=p||s&&x(new s)!=d)&&(x=function(e){var t=l(e),r="[object Object]"==t?e.constructor:void 0,n=r?c(r):"";if(n)switch(n){case y:return h;case v:return u;case m:return f;case b:return p;case g:return d}return t}),e.exports=x},1707:(e,t,r)=>{var n=r(35142),o=r(46436);e.exports=function(e,t){t=n(t,e);for(var r=0,i=t.length;null!=e&&r<i;)e=e[o(t[r++])];return r&&r==i?e:void 0}},1944:e=>{e.exports=function(){return!1}},2408:e=>{e.exports=function(e){var t=-1,r=Array(e.size);return e.forEach(function(e){r[++t]=e}),r}},2896:(e,t,r)=>{var n=r(81488),o=r(59467);e.exports=function(e,t){return null!=e&&o(e,t,n)}},2984:(e,t,r)=>{var n=r(49227);e.exports=function(e,t,r){for(var o=-1,i=e.length;++o<i;){var a=e[o],s=t(a);if(null!=s&&(void 0===l?s==s&&!n(s):r(s,l)))var l=s,c=a}return c}},3105:e=>{e.exports=function(e){return e.split("")}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3945:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Kobo-project\\\\data-analysis-tool-frontend\\\\app\\\\[locale]\\\\(main)\\\\project\\\\[hashedId]\\\\data\\\\reports\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\project\\[hashedId]\\data\\reports\\page.tsx","default")},4999:(e,t,r)=>{e.exports=r(85718).Uint8Array},5231:(e,t,r)=>{var n=r(29395),o=r(55048);e.exports=function(e){if(!o(e))return!1;var t=n(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t}},5359:e=>{e.exports=function(e){var t=null==e?0:e.length;return t?e[t-1]:void 0}},5566:(e,t,r)=>{var n=r(41011),o=r(34117),i=r(66713),a=r(42403);e.exports=function(e){return function(t){var r=o(t=a(t))?i(t):void 0,s=r?r[0]:t.charAt(0),l=r?n(r,1).join(""):t.slice(1);return s[e]()+l}}},6053:e=>{var t=/\s/;e.exports=function(e){for(var r=e.length;r--&&t.test(e.charAt(r)););return r}},6330:e=>{e.exports=function(){return[]}},7182:(e,t,r)=>{Promise.resolve().then(r.bind(r,90541))},7383:(e,t,r)=>{var n=r(67009),o=r(32269),i=r(38428),a=r(55048);e.exports=function(e,t,r){if(!a(r))return!1;var s=typeof t;return("number"==s?!!(o(r)&&i(t,r.length)):"string"==s&&t in r)&&n(r[t],e)}},7651:(e,t,r)=>{var n=r(82038),o=r(52931),i=r(32269);e.exports=function(e){return i(e)?n(e):o(e)}},8336:(e,t,r)=>{var n=r(45803);e.exports=function(e,t){var r=e.__data__;return n(t)?r["string"==typeof t?"string":"hash"]:r.map}},8852:(e,t,r)=>{var n=r(1707);e.exports=function(e){return function(t){return n(t,e)}}},10034:(e,t,r)=>{var n=r(2984),o=r(22),i=r(46063);e.exports=function(e,t){return e&&e.length?n(e,o(t,2),i):void 0}},10090:(e,t,r)=>{var n=r(80458),o=r(89624),i=r(47282),a=i&&i.isTypedArray;e.exports=a?o(a):n},10653:(e,t,r)=>{var n=r(21456),o=r(63979),i=r(7651);e.exports=function(e){return n(e,i,o)}},10663:e=>{e.exports="object"==typeof global&&global&&global.Object===Object&&global},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11117:e=>{"use strict";var t=Object.prototype.hasOwnProperty,r="~";function n(){}function o(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function i(e,t,n,i,a){if("function"!=typeof n)throw TypeError("The listener must be a function");var s=new o(n,i||e,a),l=r?r+t:t;return e._events[l]?e._events[l].fn?e._events[l]=[e._events[l],s]:e._events[l].push(s):(e._events[l]=s,e._eventsCount++),e}function a(e,t){0==--e._eventsCount?e._events=new n:delete e._events[t]}function s(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1)),s.prototype.eventNames=function(){var e,n,o=[];if(0===this._eventsCount)return o;for(n in e=this._events)t.call(e,n)&&o.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?o.concat(Object.getOwnPropertySymbols(e)):o},s.prototype.listeners=function(e){var t=r?r+e:e,n=this._events[t];if(!n)return[];if(n.fn)return[n.fn];for(var o=0,i=n.length,a=Array(i);o<i;o++)a[o]=n[o].fn;return a},s.prototype.listenerCount=function(e){var t=r?r+e:e,n=this._events[t];return n?n.fn?1:n.length:0},s.prototype.emit=function(e,t,n,o,i,a){var s=r?r+e:e;if(!this._events[s])return!1;var l,c,u=this._events[s],f=arguments.length;if(u.fn){switch(u.once&&this.removeListener(e,u.fn,void 0,!0),f){case 1:return u.fn.call(u.context),!0;case 2:return u.fn.call(u.context,t),!0;case 3:return u.fn.call(u.context,t,n),!0;case 4:return u.fn.call(u.context,t,n,o),!0;case 5:return u.fn.call(u.context,t,n,o,i),!0;case 6:return u.fn.call(u.context,t,n,o,i,a),!0}for(c=1,l=Array(f-1);c<f;c++)l[c-1]=arguments[c];u.fn.apply(u.context,l)}else{var p,d=u.length;for(c=0;c<d;c++)switch(u[c].once&&this.removeListener(e,u[c].fn,void 0,!0),f){case 1:u[c].fn.call(u[c].context);break;case 2:u[c].fn.call(u[c].context,t);break;case 3:u[c].fn.call(u[c].context,t,n);break;case 4:u[c].fn.call(u[c].context,t,n,o);break;default:if(!l)for(p=1,l=Array(f-1);p<f;p++)l[p-1]=arguments[p];u[c].fn.apply(u[c].context,l)}}return!0},s.prototype.on=function(e,t,r){return i(this,e,t,r,!1)},s.prototype.once=function(e,t,r){return i(this,e,t,r,!0)},s.prototype.removeListener=function(e,t,n,o){var i=r?r+e:e;if(!this._events[i])return this;if(!t)return a(this,i),this;var s=this._events[i];if(s.fn)s.fn!==t||o&&!s.once||n&&s.context!==n||a(this,i);else{for(var l=0,c=[],u=s.length;l<u;l++)(s[l].fn!==t||o&&!s[l].once||n&&s[l].context!==n)&&c.push(s[l]);c.length?this._events[i]=1===c.length?c[0]:c:a(this,i)}return this},s.prototype.removeAllListeners=function(e){var t;return e?(t=r?r+e:e,this._events[t]&&a(this,t)):(this._events=new n,this._eventsCount=0),this},s.prototype.off=s.prototype.removeListener,s.prototype.addListener=s.prototype.on,s.prefixed=r,s.EventEmitter=s,e.exports=s},11424:(e,t,r)=>{var n=r(47603);e.exports=r(66400)(n)},11539:(e,t,r)=>{var n=r(37643),o=r(55048),i=r(49227),a=0/0,s=/^[-+]0x[0-9a-f]+$/i,l=/^0b[01]+$/i,c=/^0o[0-7]+$/i,u=parseInt;e.exports=function(e){if("number"==typeof e)return e;if(i(e))return a;if(o(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=o(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=n(e);var r=l.test(e);return r||c.test(e)?u(e.slice(2),r?2:8):s.test(e)?a:+e}},12290:e=>{var t=Function.prototype.toString;e.exports=function(e){if(null!=e){try{return t.call(e)}catch(e){}try{return e+""}catch(e){}}return""}},12344:(e,t,r)=>{e.exports=r(65984)()},12412:e=>{"use strict";e.exports=require("assert")},13272:(e,t,r)=>{Promise.resolve().then(r.bind(r,73404))},14675:e=>{e.exports=function(e){return function(){return e}}},14952:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},15451:(e,t,r)=>{var n=r(29395),o=r(27467);e.exports=function(e){return o(e)&&"[object Arguments]"==n(e)}},15871:(e,t,r)=>{var n=r(36341),o=r(27467);e.exports=function e(t,r,i,a,s){return t===r||(null!=t&&null!=r&&(o(t)||o(r))?n(t,r,i,a,e,s):t!=t&&r!=r)}},15883:(e,t,r)=>{var n=r(2984),o=r(46063),i=r(48169);e.exports=function(e){return e&&e.length?n(e,i,o):void 0}},15909:(e,t,r)=>{var n=r(87506),o=r(66930),i=r(658);e.exports=function(){this.size=0,this.__data__={hash:new n,map:new(i||o),string:new n}}},16854:e=>{e.exports=function(e){return this.__data__.has(e)}},17518:(e,t,r)=>{var n=r(21367),o=r(1707),i=r(22),a=r(54765),s=r(43378),l=r(89624),c=r(65727),u=r(48169),f=r(40542);e.exports=function(e,t,r){t=t.length?n(t,function(e){return f(e)?function(t){return o(t,1===e.length?e[0]:e)}:e}):[u];var p=-1;return t=n(t,l(i)),s(a(e,function(e,r,o){return{criteria:n(t,function(t){return t(e)}),index:++p,value:e}}),function(e,t){return c(e,t,r)})}},17830:(e,t,r)=>{e.exports=r(41547)(r(85718),"WeakMap")},18234:(e,t,r)=>{var n=r(91290),o=r(22),i=r(84482),a=Math.max;e.exports=function(e,t,r){var s=null==e?0:e.length;if(!s)return -1;var l=null==r?0:i(r);return l<0&&(l=a(s+l,0)),n(e,o(t,3),l)}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19976:(e,t,r)=>{var n=r(8336);e.exports=function(e,t){var r=n(this,e),o=r.size;return r.set(e,t),this.size+=+(r.size!=o),this}},20174:(e,t,r)=>{"use strict";r.d(t,{F:()=>s});var n=r(60687),o=r(16189),i=r(85814),a=r.n(i);r(43210);let s=({items:e})=>{let t=(0,o.usePathname)(),r=e=>t.startsWith(e);return(0,n.jsx)("div",{className:"border-y border-neutral-400 rounded-md bg-primary-500 my-4 shadow-md",children:(0,n.jsx)("div",{className:"flex items-center",children:e.map(e=>e.disabled?(0,n.jsxs)("div",{className:"flex items-center gap-2 font-medium transition-all duration-300 p-4 border-b-2 text-gray-400 cursor-not-allowed",children:[e.icon,e.label]},e.route):(0,n.jsxs)(a(),{href:e.route,className:`flex items-center gap-2 font-medium transition-all duration-300 p-4 border-b-2 text-neutral-100 ${r(e.route)?"border-neutral-100":"border-transparent hover:border-neutral-400"}`,children:[e.icon,e.label]},e.route))})})}},20540:(e,t,r)=>{var n=r(55048),o=r(70151),i=r(11539),a=Math.max,s=Math.min;e.exports=function(e,t,r){var l,c,u,f,p,d,h=0,y=!1,v=!1,m=!0;if("function"!=typeof e)throw TypeError("Expected a function");function b(t){var r=l,n=c;return l=c=void 0,h=t,f=e.apply(n,r)}function g(e){var r=e-d,n=e-h;return void 0===d||r>=t||r<0||v&&n>=u}function x(){var e,r,n,i=o();if(g(i))return w(i);p=setTimeout(x,(e=i-d,r=i-h,n=t-e,v?s(n,u-r):n))}function w(e){return(p=void 0,m&&l)?b(e):(l=c=void 0,f)}function O(){var e,r=o(),n=g(r);if(l=arguments,c=this,d=r,n){if(void 0===p)return h=e=d,p=setTimeout(x,t),y?b(e):f;if(v)return clearTimeout(p),p=setTimeout(x,t),b(d)}return void 0===p&&(p=setTimeout(x,t)),f}return t=i(t)||0,n(r)&&(y=!!r.leading,u=(v="maxWait"in r)?a(i(r.maxWait)||0,t):u,m="trailing"in r?!!r.trailing:m),O.cancel=function(){void 0!==p&&clearTimeout(p),h=0,l=d=c=p=void 0},O.flush=function(){return void 0===p?f:w(o())},O}},20623:(e,t,r)=>{var n=r(15871),o=r(40491),i=r(2896),a=r(67619),s=r(34883),l=r(41132),c=r(46436);e.exports=function(e,t){return a(e)&&s(t)?l(c(e),t):function(r){var a=o(r,e);return void 0===a&&a===t?i(r,e):n(t,a,3)}}},21367:e=>{e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length,o=Array(n);++r<n;)o[r]=t(e[r],r,e);return o}},21456:(e,t,r)=>{var n=r(41693),o=r(40542);e.exports=function(e,t,r){var i=t(e);return o(e)?i:n(i,r(e))}},21592:(e,t,r)=>{var n=r(42205),o=r(61837);e.exports=function(e,t){return n(o(e,t),1)}},21630:(e,t,r)=>{var n=r(10653),o=Object.prototype.hasOwnProperty;e.exports=function(e,t,r,i,a,s){var l=1&r,c=n(e),u=c.length;if(u!=n(t).length&&!l)return!1;for(var f=u;f--;){var p=c[f];if(!(l?p in t:o.call(t,p)))return!1}var d=s.get(e),h=s.get(t);if(d&&h)return d==t&&h==e;var y=!0;s.set(e,t),s.set(t,e);for(var v=l;++f<u;){var m=e[p=c[f]],b=t[p];if(i)var g=l?i(b,m,p,t,e,s):i(m,b,p,e,t,s);if(!(void 0===g?m===b||a(m,b,r,i,s):g)){y=!1;break}v||(v="constructor"==p)}if(y&&!v){var x=e.constructor,w=t.constructor;x!=w&&"constructor"in e&&"constructor"in t&&!("function"==typeof x&&x instanceof x&&"function"==typeof w&&w instanceof w)&&(y=!1)}return s.delete(e),s.delete(t),y}},21820:e=>{"use strict";e.exports=require("os")},22964:(e,t,r)=>{e.exports=r(23729)(r(18234))},23729:(e,t,r)=>{var n=r(22),o=r(32269),i=r(7651);e.exports=function(e){return function(t,r,a){var s=Object(t);if(!o(t)){var l=n(r,3);t=i(t),r=function(e){return l(s[e],e,s)}}var c=e(t,r,a);return c>-1?s[l?t[c]:c]:void 0}}},25118:e=>{e.exports=function(e){return this.__data__.has(e)}},27006:(e,t,r)=>{var n=r(46328),o=r(99525),i=r(58276);e.exports=function(e,t,r,a,s,l){var c=1&r,u=e.length,f=t.length;if(u!=f&&!(c&&f>u))return!1;var p=l.get(e),d=l.get(t);if(p&&d)return p==t&&d==e;var h=-1,y=!0,v=2&r?new n:void 0;for(l.set(e,t),l.set(t,e);++h<u;){var m=e[h],b=t[h];if(a)var g=c?a(b,m,h,t,e,l):a(m,b,h,e,t,l);if(void 0!==g){if(g)continue;y=!1;break}if(v){if(!o(t,function(e,t){if(!i(v,t)&&(m===e||s(m,e,r,a,l)))return v.push(t)})){y=!1;break}}else if(!(m===b||s(m,b,r,a,l))){y=!1;break}}return l.delete(e),l.delete(t),y}},27467:e=>{e.exports=function(e){return null!=e&&"object"==typeof e}},27669:e=>{e.exports=function(){this.__data__=[],this.size=0}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28837:(e,t,r)=>{var n=r(57797),o=Array.prototype.splice;e.exports=function(e){var t=this.__data__,r=n(t,e);return!(r<0)&&(r==t.length-1?t.pop():o.call(t,r,1),--this.size,!0)}},28977:(e,t,r)=>{var n=r(11539),o=1/0;e.exports=function(e){return e?(e=n(e))===o||e===-o?(e<0?-1:1)*17976931348623157e292:e==e?e:0:0===e?e:0}},29021:e=>{"use strict";e.exports=require("fs")},29205:(e,t,r)=>{var n=r(8336);e.exports=function(e){var t=n(this,e).delete(e);return this.size-=!!t,t}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29395:(e,t,r)=>{var n=r(79474),o=r(70222),i=r(84713),a=n?n.toStringTag:void 0;e.exports=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":a&&a in Object(e)?o(e):i(e)}},29508:(e,t,r)=>{var n=r(8336);e.exports=function(e){return n(this,e).get(e)}},29632:(e,t,r)=>{"use strict";e.exports=r(97668)},30316:(e,t,r)=>{var n=r(67554);e.exports=function(e,t){var r=!0;return n(e,function(e,n,o){return r=!!t(e,n,o)}),r}},30401:(e,t,r)=>{e.exports=r(41547)(r(85718),"Promise")},30854:(e,t,r)=>{var n=r(66930),o=r(658),i=r(95746);e.exports=function(e,t){var r=this.__data__;if(r instanceof n){var a=r.__data__;if(!o||a.length<199)return a.push([e,t]),this.size=++r.size,this;r=this.__data__=new i(a)}return r.set(e,t),this.size=r.size,this}},31158:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},32269:(e,t,r)=>{var n=r(5231),o=r(69619);e.exports=function(e){return null!=e&&o(e.length)&&!n(e)}},33873:e=>{"use strict";e.exports=require("path")},34117:e=>{var t=RegExp("[\\u200d\ud800-\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");e.exports=function(e){return t.test(e)}},34452:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},34746:e=>{e.exports=function(e){return this.__data__.get(e)}},34772:(e,t,r)=>{e.exports=r(41547)(r(85718),"Set")},34821:e=>{e.exports=function(e,t){for(var r=-1,n=Array(e);++r<e;)n[r]=t(r);return n}},34883:(e,t,r)=>{var n=r(55048);e.exports=function(e){return e==e&&!n(e)}},34990:(e,t,r)=>{e.exports=r(87321)()},35142:(e,t,r)=>{var n=r(40542),o=r(67619),i=r(51449),a=r(42403);e.exports=function(e,t){return n(e)?e:o(e,t)?[e]:i(a(e))}},35163:(e,t,r)=>{var n=r(15451),o=r(27467),i=Object.prototype,a=i.hasOwnProperty,s=i.propertyIsEnumerable;e.exports=n(function(){return arguments}())?n:function(e){return o(e)&&a.call(e,"callee")&&!s.call(e,"callee")}},35697:(e,t,r)=>{var n=r(79474),o=r(4999),i=r(67009),a=r(27006),s=r(59774),l=r(2408),c=n?n.prototype:void 0,u=c?c.valueOf:void 0;e.exports=function(e,t,r,n,c,f,p){switch(r){case"[object DataView]":if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)break;e=e.buffer,t=t.buffer;case"[object ArrayBuffer]":if(e.byteLength!=t.byteLength||!f(new o(e),new o(t)))break;return!0;case"[object Boolean]":case"[object Date]":case"[object Number]":return i(+e,+t);case"[object Error]":return e.name==t.name&&e.message==t.message;case"[object RegExp]":case"[object String]":return e==t+"";case"[object Map]":var d=s;case"[object Set]":var h=1&n;if(d||(d=l),e.size!=t.size&&!h)break;var y=p.get(e);if(y)return y==t;n|=2,p.set(e,t);var v=a(d(e),d(t),n,c,f,p);return p.delete(e),v;case"[object Symbol]":if(u)return u.call(e)==u.call(t)}return!1}},35800:(e,t,r)=>{var n=r(57797);e.exports=function(e){return n(this.__data__,e)>-1}},36315:(e,t,r)=>{var n=r(22),o=r(92662);e.exports=function(e,t){return e&&e.length?o(e,n(t,2)):[]}},36341:(e,t,r)=>{var n=r(67200),o=r(27006),i=r(35697),a=r(21630),s=r(1566),l=r(40542),c=r(80329),u=r(10090),f="[object Arguments]",p="[object Array]",d="[object Object]",h=Object.prototype.hasOwnProperty;e.exports=function(e,t,r,y,v,m){var b=l(e),g=l(t),x=b?p:s(e),w=g?p:s(t);x=x==f?d:x,w=w==f?d:w;var O=x==d,j=w==d,S=x==w;if(S&&c(e)){if(!c(t))return!1;b=!0,O=!1}if(S&&!O)return m||(m=new n),b||u(e)?o(e,t,r,y,v,m):i(e,t,x,r,y,v,m);if(!(1&r)){var P=O&&h.call(e,"__wrapped__"),E=j&&h.call(t,"__wrapped__");if(P||E){var A=P?e.value():e,k=E?t.value():t;return m||(m=new n),v(A,k,r,y,m)}}return!!S&&(m||(m=new n),a(e,t,r,y,v,m))}},36959:e=>{e.exports=function(){}},37446:(e,t,r)=>{Promise.resolve().then(r.bind(r,51129))},37456:e=>{e.exports=function(e){return null==e}},37575:(e,t,r)=>{var n=r(66930);e.exports=function(){this.__data__=new n,this.size=0}},37643:(e,t,r)=>{var n=r(6053),o=/^\s+/;e.exports=function(e){return e?e.slice(0,n(e)+1).replace(o,""):e}},38399:()=>{},38404:(e,t,r)=>{var n=r(29395),o=r(65932),i=r(27467),a=Object.prototype,s=Function.prototype.toString,l=a.hasOwnProperty,c=s.call(Object);e.exports=function(e){if(!i(e)||"[object Object]"!=n(e))return!1;var t=o(e);if(null===t)return!0;var r=l.call(t,"constructor")&&t.constructor;return"function"==typeof r&&r instanceof r&&s.call(r)==c}},38428:e=>{var t=/^(?:0|[1-9]\d*)$/;e.exports=function(e,r){var n=typeof e;return!!(r=null==r?0x1fffffffffffff:r)&&("number"==n||"symbol"!=n&&t.test(e))&&e>-1&&e%1==0&&e<r}},39672:(e,t,r)=>{var n=r(58141);e.exports=function(e,t){var r=this.__data__;return this.size+=+!this.has(e),r[e]=n&&void 0===t?"__lodash_hash_undefined__":t,this}},39774:e=>{e.exports=function(e){return e!=e}},40491:(e,t,r)=>{var n=r(1707);e.exports=function(e,t,r){var o=null==e?void 0:n(e,t);return void 0===o?r:o}},40542:e=>{e.exports=Array.isArray},41011:(e,t,r)=>{var n=r(41353);e.exports=function(e,t,r){var o=e.length;return r=void 0===r?o:r,!t&&r>=o?e:n(e,t,r)}},41132:e=>{e.exports=function(e,t){return function(r){return null!=r&&r[e]===t&&(void 0!==t||e in Object(r))}}},41157:(e,t,r)=>{var n=r(91928);e.exports=function(e,t,r){"__proto__"==t&&n?n(e,t,{configurable:!0,enumerable:!0,value:r,writable:!0}):e[t]=r}},41353:e=>{e.exports=function(e,t,r){var n=-1,o=e.length;t<0&&(t=-t>o?0:o+t),(r=r>o?o:r)<0&&(r+=o),o=t>r?0:r-t>>>0,t>>>=0;for(var i=Array(o);++n<o;)i[n]=e[n+t];return i}},41547:(e,t,r)=>{var n=r(61548),o=r(90851);e.exports=function(e,t){var r=o(e,t);return n(r)?r:void 0}},41693:e=>{e.exports=function(e,t){for(var r=-1,n=t.length,o=e.length;++r<n;)e[o+r]=t[r];return e}},41862:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},42082:e=>{e.exports=function(e){return function(t){return null==t?void 0:t[e]}}},42205:(e,t,r)=>{var n=r(41693),o=r(85450);e.exports=function e(t,r,i,a,s){var l=-1,c=t.length;for(i||(i=o),s||(s=[]);++l<c;){var u=t[l];r>0&&i(u)?r>1?e(u,r-1,i,a,s):n(s,u):a||(s[s.length]=u)}return s}},42403:(e,t,r)=>{var n=r(80195);e.exports=function(e){return null==e?"":n(e)}},43378:e=>{e.exports=function(e,t){var r=e.length;for(e.sort(t);r--;)e[r]=e[r].value;return e}},45058:(e,t,r)=>{var n=r(42082),o=r(8852),i=r(67619),a=r(46436);e.exports=function(e){return i(e)?n(a(e)):o(e)}},45603:(e,t,r)=>{var n=r(20540),o=r(55048);e.exports=function(e,t,r){var i=!0,a=!0;if("function"!=typeof e)throw TypeError("Expected a function");return o(r)&&(i="leading"in r?!!r.leading:i,a="trailing"in r?!!r.trailing:a),n(e,t,{leading:i,maxWait:t,trailing:a})}},45803:e=>{e.exports=function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e}},46063:e=>{e.exports=function(e,t){return e<t}},46229:(e,t,r)=>{var n=r(48169),o=r(66354),i=r(11424);e.exports=function(e,t){return i(o(e,t,n),e+"")}},46328:(e,t,r)=>{var n=r(95746),o=r(89185),i=r(16854);function a(e){var t=-1,r=null==e?0:e.length;for(this.__data__=new n;++t<r;)this.add(e[t])}a.prototype.add=a.prototype.push=o,a.prototype.has=i,e.exports=a},46436:(e,t,r)=>{var n=r(49227),o=1/0;e.exports=function(e){if("string"==typeof e||n(e))return e;var t=e+"";return"0"==t&&1/e==-o?"-0":t}},47033:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},47212:(e,t,r)=>{var n=r(87270),o=r(30316),i=r(22),a=r(40542),s=r(7383);e.exports=function(e,t,r){var l=a(e)?n:o;return r&&s(e,t,r)&&(t=void 0),l(e,i(t,3))}},47282:(e,t,r)=>{e=r.nmd(e);var n=r(10663),o=t&&!t.nodeType&&t,i=o&&e&&!e.nodeType&&e,a=i&&i.exports===o&&n.process,s=function(){try{var e=i&&i.require&&i.require("util").types;if(e)return e;return a&&a.binding&&a.binding("util")}catch(e){}}();e.exports=s},47603:(e,t,r)=>{var n=r(14675),o=r(91928),i=r(48169);e.exports=o?function(e,t){return o(e,"toString",{configurable:!0,enumerable:!1,value:n(t),writable:!0})}:i},48169:e=>{e.exports=function(e){return e}},48385:e=>{var t="\ud800-\udfff",r="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",n="\ud83c[\udffb-\udfff]",o="[^"+t+"]",i="(?:\ud83c[\udde6-\uddff]){2}",a="[\ud800-\udbff][\udc00-\udfff]",s="(?:"+r+"|"+n+")?",l="[\\ufe0e\\ufe0f]?",c="(?:\\u200d(?:"+[o,i,a].join("|")+")"+l+s+")*",u=RegExp(n+"(?="+n+")|"+("(?:"+[o+r+"?",r,i,a,"["+t+"]"].join("|"))+")"+(l+s+c),"g");e.exports=function(e){return e.match(u)||[]}},49227:(e,t,r)=>{var n=r(29395),o=r(27467);e.exports=function(e){return"symbol"==typeof e||o(e)&&"[object Symbol]"==n(e)}},51129:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Kobo-project\\\\data-analysis-tool-frontend\\\\app\\\\[locale]\\\\(main)\\\\project\\\\[hashedId]\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\project\\[hashedId]\\layout.tsx","default")},51200:(e,t,r)=>{Promise.resolve().then(r.bind(r,84638))},51449:(e,t,r)=>{var n=r(85745),o=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,i=/\\(\\)?/g;e.exports=n(function(e){var t=[];return 46===e.charCodeAt(0)&&t.push(""),e.replace(o,function(e,r,n,o){t.push(n?o.replace(i,"$1"):r||e)}),t})},52599:e=>{e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length,o=0,i=[];++r<n;){var a=e[r];t(a,r,e)&&(i[o++]=a)}return i}},52823:(e,t,r)=>{var n=r(85406),o=function(){var e=/[^.]+$/.exec(n&&n.keys&&n.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}();e.exports=function(e){return!!o&&o in e}},52931:(e,t,r)=>{var n=r(77834),o=r(89605),i=Object.prototype.hasOwnProperty;e.exports=function(e){if(!n(e))return o(e);var t=[];for(var r in Object(e))i.call(e,r)&&"constructor"!=r&&t.push(r);return t}},54765:(e,t,r)=>{var n=r(67554),o=r(32269);e.exports=function(e,t){var r=-1,i=o(e)?Array(e.length):[];return n(e,function(e,n,o){i[++r]=t(e,n,o)}),i}},55048:e=>{e.exports=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56506:(e,t,r)=>{var n=r(32269);e.exports=function(e,t){return function(r,o){if(null==r)return r;if(!n(r))return e(r,o);for(var i=r.length,a=t?i:-1,s=Object(r);(t?a--:++a<i)&&!1!==o(s[a],a,s););return r}}},57088:(e,t,r)=>{var n=r(2984),o=r(99180),i=r(22);e.exports=function(e,t){return e&&e.length?n(e,i(t,2),o):void 0}},57797:(e,t,r)=>{var n=r(67009);e.exports=function(e,t){for(var r=e.length;r--;)if(n(e[r][0],t))return r;return -1}},58141:(e,t,r)=>{e.exports=r(41547)(Object,"create")},58276:e=>{e.exports=function(e,t){return e.has(t)}},58744:(e,t,r)=>{var n=r(57797);e.exports=function(e,t){var r=this.__data__,o=n(r,e);return o<0?(++this.size,r.push([e,t])):r[o][1]=t,this}},59467:(e,t,r)=>{var n=r(35142),o=r(35163),i=r(40542),a=r(38428),s=r(69619),l=r(46436);e.exports=function(e,t,r){t=n(t,e);for(var c=-1,u=t.length,f=!1;++c<u;){var p=l(t[c]);if(!(f=null!=e&&r(e,p)))break;e=e[p]}return f||++c!=u?f:!!(u=null==e?0:e.length)&&s(u)&&a(p,u)&&(i(e)||o(e))}},59774:e=>{e.exports=function(e){var t=-1,r=Array(e.size);return e.forEach(function(e,n){r[++t]=[n,e]}),r}},61320:(e,t,r)=>{var n=r(8336);e.exports=function(e){return n(this,e).has(e)}},61548:(e,t,r)=>{var n=r(5231),o=r(52823),i=r(55048),a=r(12290),s=/^\[object .+?Constructor\]$/,l=Object.prototype,c=Function.prototype.toString,u=l.hasOwnProperty,f=RegExp("^"+c.call(u).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");e.exports=function(e){return!(!i(e)||o(e))&&(n(e)?f:s).test(a(e))}},61837:(e,t,r)=>{var n=r(21367),o=r(22),i=r(54765),a=r(40542);e.exports=function(e,t){return(a(e)?n:i)(e,o(t,3))}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63866:(e,t,r)=>{var n=r(29395),o=r(40542),i=r(27467);e.exports=function(e){return"string"==typeof e||!o(e)&&i(e)&&"[object String]"==n(e)}},63979:(e,t,r)=>{var n=r(52599),o=r(6330),i=Object.prototype.propertyIsEnumerable,a=Object.getOwnPropertySymbols;e.exports=a?function(e){return null==e?[]:n(a(e=Object(e)),function(t){return i.call(e,t)})}:o},64352:(e,t,r)=>{Promise.resolve().then(r.bind(r,87282))},65662:e=>{e.exports=function(e,t){return function(r){return e(t(r))}}},65727:(e,t,r)=>{var n=r(81957);e.exports=function(e,t,r){for(var o=-1,i=e.criteria,a=t.criteria,s=i.length,l=r.length;++o<s;){var c=n(i[o],a[o]);if(c){if(o>=l)return c;return c*("desc"==r[o]?-1:1)}}return e.index-t.index}},65932:(e,t,r)=>{e.exports=r(65662)(Object.getPrototypeOf,Object)},65984:e=>{e.exports=function(e){return function(t,r,n){for(var o=-1,i=Object(t),a=n(t),s=a.length;s--;){var l=a[e?s:++o];if(!1===r(i[l],l,i))break}return t}}},66354:(e,t,r)=>{var n=r(85244),o=Math.max;e.exports=function(e,t,r){return t=o(void 0===t?e.length-1:t,0),function(){for(var i=arguments,a=-1,s=o(i.length-t,0),l=Array(s);++a<s;)l[a]=i[t+a];a=-1;for(var c=Array(t+1);++a<t;)c[a]=i[a];return c[t]=r(l),n(e,this,c)}}},66400:e=>{var t=Date.now;e.exports=function(e){var r=0,n=0;return function(){var o=t(),i=16-(o-n);if(n=o,i>0){if(++r>=800)return arguments[0]}else r=0;return e.apply(void 0,arguments)}}},66713:(e,t,r)=>{var n=r(3105),o=r(34117),i=r(48385);e.exports=function(e){return o(e)?i(e):n(e)}},66837:(e,t,r)=>{var n=r(58141);e.exports=function(){this.__data__=n?n(null):{},this.size=0}},66930:(e,t,r)=>{var n=r(27669),o=r(28837),i=r(94388),a=r(35800),s=r(58744);function l(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}l.prototype.clear=n,l.prototype.delete=o,l.prototype.get=i,l.prototype.has=a,l.prototype.set=s,e.exports=l},67009:e=>{e.exports=function(e,t){return e===t||e!=e&&t!=t}},67200:(e,t,r)=>{var n=r(66930),o=r(37575),i=r(75411),a=r(34746),s=r(25118),l=r(30854);function c(e){var t=this.__data__=new n(e);this.size=t.size}c.prototype.clear=o,c.prototype.delete=i,c.prototype.get=a,c.prototype.has=s,c.prototype.set=l,e.exports=c},67367:(e,t,r)=>{var n=r(99525),o=r(22),i=r(75847),a=r(40542),s=r(7383);e.exports=function(e,t,r){var l=a(e)?n:i;return r&&s(e,t,r)&&(t=void 0),l(e,o(t,3))}},67554:(e,t,r)=>{var n=r(99114);e.exports=r(56506)(n)},67619:(e,t,r)=>{var n=r(40542),o=r(49227),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;e.exports=function(e,t){if(n(e))return!1;var r=typeof e;return!!("number"==r||"symbol"==r||"boolean"==r||null==e||o(e))||a.test(e)||!i.test(e)||null!=t&&e in Object(t)}},69433:(e,t,r)=>{e.exports=r(5566)("toUpperCase")},69619:e=>{e.exports=function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=0x1fffffffffffff}},69691:(e,t,r)=>{var n=r(41157),o=r(99114),i=r(22);e.exports=function(e,t){var r={};return t=i(t,3),o(e,function(e,o,i){n(r,o,t(e,o,i))}),r}},70151:(e,t,r)=>{var n=r(85718);e.exports=function(){return n.Date.now()}},70222:(e,t,r)=>{var n=r(79474),o=Object.prototype,i=o.hasOwnProperty,a=o.toString,s=n?n.toStringTag:void 0;e.exports=function(e){var t=i.call(e,s),r=e[s];try{e[s]=void 0;var n=!0}catch(e){}var o=a.call(e);return n&&(t?e[s]=r:delete e[s]),o}},71960:e=>{e.exports=function(e,t,r){for(var n=-1,o=null==e?0:e.length;++n<o;)if(r(t,e[n]))return!0;return!1}},71967:(e,t,r)=>{var n=r(15871);e.exports=function(e,t){return n(e,t)}},73404:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>gk});var n,o={};r.r(o),r.d(o,{Button:()=>ec,CaptionLabel:()=>eu,Chevron:()=>ef,Day:()=>ep,DayButton:()=>ed,Dropdown:()=>eh,DropdownNav:()=>ey,Footer:()=>ev,Month:()=>em,MonthCaption:()=>eb,MonthGrid:()=>eg,Months:()=>ex,MonthsDropdown:()=>ew,Nav:()=>eO,NextMonthButton:()=>ej,Option:()=>eS,PreviousMonthButton:()=>eP,Root:()=>eE,Select:()=>eA,Week:()=>ek,WeekNumber:()=>e_,WeekNumberHeader:()=>eC,Weekday:()=>eM,Weekdays:()=>eT,Weeks:()=>eN,YearsDropdown:()=>eD});var i={};r.r(i),r.d(i,{formatCaption:()=>eI,formatDay:()=>eL,formatMonthCaption:()=>eR,formatMonthDropdown:()=>eB,formatWeekNumber:()=>eF,formatWeekNumberHeader:()=>ez,formatWeekdayName:()=>e$,formatYearCaption:()=>eU,formatYearDropdown:()=>eW});var a={};r.r(a),r.d(a,{labelCaption:()=>eY,labelDay:()=>eV,labelDayButton:()=>eX,labelGrid:()=>eq,labelGridcell:()=>eH,labelMonthDropdown:()=>eK,labelNav:()=>eG,labelNext:()=>eZ,labelPrevious:()=>eJ,labelWeekNumber:()=>e0,labelWeekNumberHeader:()=>e1,labelWeekday:()=>eQ,labelYearDropdown:()=>e2});var s={};r.r(s),r.d(s,{scaleBand:()=>aI,scaleDiverging:()=>function e(){var t=lx(uR()(lr));return t.copy=function(){return uN(t,e())},aM.apply(t,arguments)},scaleDivergingLog:()=>function e(){var t=lM(uR()).domain([.1,1,10]);return t.copy=function(){return uN(t,e()).base(t.base())},aM.apply(t,arguments)},scaleDivergingPow:()=>uL,scaleDivergingSqrt:()=>uB,scaleDivergingSymlog:()=>function e(){var t=lC(uR());return t.copy=function(){return uN(t,e()).constant(t.constant())},aM.apply(t,arguments)},scaleIdentity:()=>function e(t){var r;function n(e){return null==e||isNaN(e*=1)?r:e}return n.invert=n,n.domain=n.range=function(e){return arguments.length?(t=Array.from(e,le),n):t.slice()},n.unknown=function(e){return arguments.length?(r=e,n):r},n.copy=function(){return e(t).unknown(r)},t=arguments.length?Array.from(t,le):[0,1],lx(n)},scaleImplicit:()=>aN,scaleLinear:()=>lw,scaleLog:()=>function e(){let t=lM(ls()).domain([1,10]);return t.copy=()=>la(t,e()).base(t.base()),ak.apply(t,arguments),t},scaleOrdinal:()=>aD,scalePoint:()=>aR,scalePow:()=>lL,scaleQuantile:()=>function e(){var t,r=[],n=[],o=[];function i(){var e=0,t=Math.max(1,n.length);for(o=Array(t-1);++e<t;)o[e-1]=function(e,t,r=sg){if(!(!(n=e.length)||isNaN(t*=1))){if(t<=0||n<2)return+r(e[0],0,e);if(t>=1)return+r(e[n-1],n-1,e);var n,o=(n-1)*t,i=Math.floor(o),a=+r(e[i],i,e);return a+(r(e[i+1],i+1,e)-a)*(o-i)}}(r,e/t);return a}function a(e){return null==e||isNaN(e*=1)?t:n[sw(o,e)]}return a.invertExtent=function(e){var t=n.indexOf(e);return t<0?[NaN,NaN]:[t>0?o[t-1]:r[0],t<o.length?o[t]:r[r.length-1]]},a.domain=function(e){if(!arguments.length)return r.slice();for(let t of(r=[],e))null==t||isNaN(t*=1)||r.push(t);return r.sort(sy),i()},a.range=function(e){return arguments.length?(n=Array.from(e),i()):n.slice()},a.unknown=function(e){return arguments.length?(t=e,a):t},a.quantiles=function(){return o.slice()},a.copy=function(){return e().domain(r).range(n).unknown(t)},ak.apply(a,arguments)},scaleQuantize:()=>function e(){var t,r=0,n=1,o=1,i=[.5],a=[0,1];function s(e){return null!=e&&e<=e?a[sw(i,e,0,o)]:t}function l(){var e=-1;for(i=Array(o);++e<o;)i[e]=((e+1)*n-(e-o)*r)/(o+1);return s}return s.domain=function(e){return arguments.length?([r,n]=e,r*=1,n*=1,l()):[r,n]},s.range=function(e){return arguments.length?(o=(a=Array.from(e)).length-1,l()):a.slice()},s.invertExtent=function(e){var t=a.indexOf(e);return t<0?[NaN,NaN]:t<1?[r,i[0]]:t>=o?[i[o-1],n]:[i[t-1],i[t]]},s.unknown=function(e){return arguments.length&&(t=e),s},s.thresholds=function(){return i.slice()},s.copy=function(){return e().domain([r,n]).range(a).unknown(t)},ak.apply(lx(s),arguments)},scaleRadial:()=>function e(){var t,r=ll(),n=[0,1],o=!1;function i(e){var n,i=Math.sign(n=r(e))*Math.sqrt(Math.abs(n));return isNaN(i)?t:o?Math.round(i):i}return i.invert=function(e){return r.invert(lF(e))},i.domain=function(e){return arguments.length?(r.domain(e),i):r.domain()},i.range=function(e){return arguments.length?(r.range((n=Array.from(e,le)).map(lF)),i):n.slice()},i.rangeRound=function(e){return i.range(e).round(!0)},i.round=function(e){return arguments.length?(o=!!e,i):o},i.clamp=function(e){return arguments.length?(r.clamp(e),i):r.clamp()},i.unknown=function(e){return arguments.length?(t=e,i):t},i.copy=function(){return e(r.domain(),n).round(o).clamp(r.clamp()).unknown(t)},ak.apply(i,arguments),lx(i)},scaleSequential:()=>function e(){var t=lx(uC()(lr));return t.copy=function(){return uN(t,e())},aM.apply(t,arguments)},scaleSequentialLog:()=>function e(){var t=lM(uC()).domain([1,10]);return t.copy=function(){return uN(t,e()).base(t.base())},aM.apply(t,arguments)},scaleSequentialPow:()=>uD,scaleSequentialQuantile:()=>function e(){var t=[],r=lr;function n(e){if(null!=e&&!isNaN(e*=1))return r((sw(t,e,1)-1)/(t.length-1))}return n.domain=function(e){if(!arguments.length)return t.slice();for(let r of(t=[],e))null==r||isNaN(r*=1)||t.push(r);return t.sort(sy),n},n.interpolator=function(e){return arguments.length?(r=e,n):r},n.range=function(){return t.map((e,n)=>r(n/(t.length-1)))},n.quantiles=function(e){return Array.from({length:e+1},(r,n)=>(function(e,t,r){if(!(!(n=(e=Float64Array.from(function*(e,t){if(void 0===t)for(let t of e)null!=t&&(t*=1)>=t&&(yield t);else{let r=-1;for(let n of e)null!=(n=t(n,++r,e))&&(n*=1)>=n&&(yield n)}}(e,void 0))).length)||isNaN(t*=1))){if(t<=0||n<2)return l$(e);if(t>=1)return lz(e);var n,o=(n-1)*t,i=Math.floor(o),a=lz((function e(t,r,n=0,o=1/0,i){if(r=Math.floor(r),n=Math.floor(Math.max(0,n)),o=Math.floor(Math.min(t.length-1,o)),!(n<=r&&r<=o))return t;for(i=void 0===i?lW:function(e=sy){if(e===sy)return lW;if("function"!=typeof e)throw TypeError("compare is not a function");return(t,r)=>{let n=e(t,r);return n||0===n?n:(0===e(r,r))-(0===e(t,t))}}(i);o>n;){if(o-n>600){let a=o-n+1,s=r-n+1,l=Math.log(a),c=.5*Math.exp(2*l/3),u=.5*Math.sqrt(l*c*(a-c)/a)*(s-a/2<0?-1:1),f=Math.max(n,Math.floor(r-s*c/a+u)),p=Math.min(o,Math.floor(r+(a-s)*c/a+u));e(t,r,f,p,i)}let a=t[r],s=n,l=o;for(lU(t,n,r),i(t[o],a)>0&&lU(t,n,o);s<l;){for(lU(t,s,l),++s,--l;0>i(t[s],a);)++s;for(;i(t[l],a)>0;)--l}0===i(t[n],a)?lU(t,n,l):lU(t,++l,o),l<=r&&(n=l+1),r<=l&&(o=l-1)}return t})(e,i).subarray(0,i+1));return a+(l$(e.subarray(i+1))-a)*(o-i)}})(t,n/e))},n.copy=function(){return e(r).domain(t)},aM.apply(n,arguments)},scaleSequentialSqrt:()=>uI,scaleSequentialSymlog:()=>function e(){var t=lC(uC());return t.copy=function(){return uN(t,e()).constant(t.constant())},aM.apply(t,arguments)},scaleSqrt:()=>lB,scaleSymlog:()=>function e(){var t=lC(ls());return t.copy=function(){return la(t,e()).constant(t.constant())},ak.apply(t,arguments)},scaleThreshold:()=>function e(){var t,r=[.5],n=[0,1],o=1;function i(e){return null!=e&&e<=e?n[sw(r,e,0,o)]:t}return i.domain=function(e){return arguments.length?(o=Math.min((r=Array.from(e)).length,n.length-1),i):r.slice()},i.range=function(e){return arguments.length?(n=Array.from(e),o=Math.min(r.length,n.length-1),i):n.slice()},i.invertExtent=function(e){var t=n.indexOf(e);return[r[t-1],r[t]]},i.unknown=function(e){return arguments.length?(t=e,i):t},i.copy=function(){return e().domain(r).range(n).unknown(t)},ak.apply(i,arguments)},scaleTime:()=>uT,scaleUtc:()=>u_,tickFormat:()=>lg});var l=r(60687),c=r(43210),u=r.n(c),f=r(62688);let p=(0,f.A)("chart-no-axes-column-increasing",[["line",{x1:"12",x2:"12",y1:"20",y2:"10",key:"1vz5eb"}],["line",{x1:"18",x2:"18",y1:"20",y2:"4",key:"cun8e5"}],["line",{x1:"6",x2:"6",y1:"20",y2:"16",key:"hq0ia6"}]]),d=(0,f.A)("chart-pie",[["path",{d:"M21 12c.552 0 1.005-.449.95-.998a10 10 0 0 0-8.953-8.951c-.55-.055-.998.398-.998.95v8a1 1 0 0 0 1 1z",key:"pzmjnu"}],["path",{d:"M21.21 15.89A10 10 0 1 1 8 2.83",key:"k2fpak"}]]);var h=r(41862);function y({className:e="",variant:t="default",...r}){return(0,l.jsx)("div",{className:`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-semibold ${{default:"bg-blue-500 text-neutral-100",secondary:"bg-gray-500 text-neutral-100",destructive:"bg-red-500 text-neutral-100",outline:"bg-transparent text-gray-700 border border-gray-300"}[t]} ${e}`,...r})}function v({className:e="",...t}){return(0,l.jsx)("div",{className:`rounded-lg border border-gray-200 bg-neutral-100 shadow-sm ${e}`,...t})}function m({className:e="",...t}){return(0,l.jsx)("div",{className:`flex flex-col space-y-1.5 p-6 ${e}`,...t})}function b({className:e="",...t}){return(0,l.jsx)("h3",{className:`text-2xl font-semibold leading-none tracking-tight ${e}`,...t})}function g({className:e="",...t}){return(0,l.jsx)("div",{className:`p-6 pt-0 ${e}`,...t})}let x=(0,c.createContext)(void 0);function w(){let e=(0,c.useContext)(x);if(!e)throw Error("Tabs components must be used within a Tabs component");return e}function O({defaultValue:e,children:t,className:r=""}){let[n,o]=(0,c.useState)(e);return(0,l.jsx)(x.Provider,{value:{activeTab:n,setActiveTab:o},children:(0,l.jsx)("div",{className:r,children:t})})}function j({children:e,className:t=""}){return(0,l.jsx)("div",{className:`inline-flex h-10 items-center justify-center rounded-md bg-gray-100 p-1 ${t}`,children:e})}function S({value:e,children:t,className:r=""}){let{activeTab:n,setActiveTab:o}=w(),i=n===e;return(0,l.jsx)("button",{className:`inline-flex items-center justify-center px-3 py-1.5 text-sm font-medium
        ${i?"bg-neutral-100 text-black shadow-sm":"text-gray-600"}
        ${r}`,onClick:()=>o(e),children:t})}function P({value:e,children:t,className:r=""}){let{activeTab:n}=w();return n!==e?null:(0,l.jsx)("div",{className:`mt-2 ${r}`,children:t})}var E=r(40228),A=r(85650),k=r(96241),M=r(24934),T=r(47033),_=r(14952);let C=(0,c.createContext)(void 0);function N(){let e=(0,c.useContext)(C);if(void 0===e)throw Error("useDayPicker() must be used within a custom component.");return e}!function(e){e.Root="root",e.Chevron="chevron",e.Day="day",e.DayButton="day_button",e.CaptionLabel="caption_label",e.Dropdowns="dropdowns",e.Dropdown="dropdown",e.DropdownRoot="dropdown_root",e.Footer="footer",e.MonthGrid="month_grid",e.MonthCaption="month_caption",e.MonthsDropdown="months_dropdown",e.Month="month",e.Months="months",e.Nav="nav",e.NextMonthButton="button_next",e.PreviousMonthButton="button_previous",e.Week="week",e.Weeks="weeks",e.Weekday="weekday",e.Weekdays="weekdays",e.WeekNumber="week_number",e.WeekNumberHeader="week_number_header",e.YearsDropdown="years_dropdown"}(uq||(uq={})),function(e){e.disabled="disabled",e.hidden="hidden",e.outside="outside",e.focused="focused",e.today="today"}(uY||(uY={})),function(e){e.range_end="range_end",e.range_middle="range_middle",e.range_start="range_start",e.selected="selected"}(uH||(uH={})),function(e){e.weeks_before_enter="weeks_before_enter",e.weeks_before_exit="weeks_before_exit",e.weeks_after_enter="weeks_after_enter",e.weeks_after_exit="weeks_after_exit",e.caption_after_enter="caption_after_enter",e.caption_after_exit="caption_after_exit",e.caption_before_enter="caption_before_enter",e.caption_before_exit="caption_before_exit"}(uX||(uX={}));var D=r(64722);Symbol.for("constructDateFrom");let I={},R={};function L(e,t){try{let r=(I[e]||=new Intl.DateTimeFormat("en-GB",{timeZone:e,hour:"numeric",timeZoneName:"longOffset"}).format)(t).split("GMT")[1]||"";if(r in R)return R[r];return F(r,r.split(":"))}catch{if(e in R)return R[e];let t=e?.match(B);if(t)return F(e,t.slice(1));return NaN}}let B=/([+-]\d\d):?(\d\d)?/;function F(e,t){let r=+t[0],n=+(t[1]||0);return R[e]=r>0?60*r+n:60*r-n}class z extends Date{constructor(...e){super(),e.length>1&&"string"==typeof e[e.length-1]&&(this.timeZone=e.pop()),this.internal=new Date,isNaN(L(this.timeZone,this))?this.setTime(NaN):e.length?"number"==typeof e[0]&&(1===e.length||2===e.length&&"number"!=typeof e[1])?this.setTime(e[0]):"string"==typeof e[0]?this.setTime(+new Date(e[0])):e[0]instanceof Date?this.setTime(+e[0]):(this.setTime(+new Date(...e)),U(this,NaN),W(this)):this.setTime(Date.now())}static tz(e,...t){return t.length?new z(...t,e):new z(Date.now(),e)}withTimeZone(e){return new z(+this,e)}getTimezoneOffset(){return-L(this.timeZone,this)}setTime(e){return Date.prototype.setTime.apply(this,arguments),W(this),+this}[Symbol.for("constructDateFrom")](e){return new z(+new Date(e),this.timeZone)}}let $=/^(get|set)(?!UTC)/;function W(e){e.internal.setTime(+e),e.internal.setUTCMinutes(e.internal.getUTCMinutes()-e.getTimezoneOffset())}function U(e){let t=L(e.timeZone,e),r=new Date(+e);r.setUTCHours(r.getUTCHours()-1);let n=-new Date(+e).getTimezoneOffset(),o=n- -new Date(+r).getTimezoneOffset(),i=Date.prototype.getHours.apply(e)!==e.internal.getUTCHours();o&&i&&e.internal.setUTCMinutes(e.internal.getUTCMinutes()+o);let a=n-t;a&&Date.prototype.setUTCMinutes.call(e,Date.prototype.getUTCMinutes.call(e)+a);let s=L(e.timeZone,e),l=-new Date(+e).getTimezoneOffset()-s-a;if(s!==t&&l){Date.prototype.setUTCMinutes.call(e,Date.prototype.getUTCMinutes.call(e)+l);let t=s-L(e.timeZone,e);t&&(e.internal.setUTCMinutes(e.internal.getUTCMinutes()+t),Date.prototype.setUTCMinutes.call(e,Date.prototype.getUTCMinutes.call(e)+t))}}Object.getOwnPropertyNames(Date.prototype).forEach(e=>{if(!$.test(e))return;let t=e.replace($,"$1UTC");z.prototype[t]&&(e.startsWith("get")?z.prototype[e]=function(){return this.internal[t]()}:(z.prototype[e]=function(){var e;return Date.prototype[t].apply(this.internal,arguments),e=this,Date.prototype.setFullYear.call(e,e.internal.getUTCFullYear(),e.internal.getUTCMonth(),e.internal.getUTCDate()),Date.prototype.setHours.call(e,e.internal.getUTCHours(),e.internal.getUTCMinutes(),e.internal.getUTCSeconds(),e.internal.getUTCMilliseconds()),U(e),+this},z.prototype[t]=function(){return Date.prototype[t].apply(this,arguments),W(this),+this}))});class q extends z{static tz(e,...t){return t.length?new q(...t,e):new q(Date.now(),e)}toISOString(){let[e,t,r]=this.tzComponents(),n=`${e}${t}:${r}`;return this.internal.toISOString().slice(0,-1)+n}toString(){return`${this.toDateString()} ${this.toTimeString()}`}toDateString(){let[e,t,r,n]=this.internal.toUTCString().split(" ");return`${e?.slice(0,-1)} ${r} ${t} ${n}`}toTimeString(){var e,t;let r=this.internal.toUTCString().split(" ")[4],[n,o,i]=this.tzComponents();return`${r} GMT${n}${o}${i} (${e=this.timeZone,t=this,new Intl.DateTimeFormat("en-GB",{timeZone:e,timeZoneName:"long"}).format(t).slice(12)})`}toLocaleString(e,t){return Date.prototype.toLocaleString.call(this,e,{...t,timeZone:t?.timeZone||this.timeZone})}toLocaleDateString(e,t){return Date.prototype.toLocaleDateString.call(this,e,{...t,timeZone:t?.timeZone||this.timeZone})}toLocaleTimeString(e,t){return Date.prototype.toLocaleTimeString.call(this,e,{...t,timeZone:t?.timeZone||this.timeZone})}tzComponents(){let e=this.getTimezoneOffset(),t=String(Math.floor(Math.abs(e)/60)).padStart(2,"0"),r=String(Math.abs(e)%60).padStart(2,"0");return[e>0?"-":"+",t,r]}withTimeZone(e){return new q(+this,e)}[Symbol.for("constructDateFrom")](e){return new q(+new Date(e),this.timeZone)}}var Y=r(87981),H=r(23711);function X(e,t,r){let n=(0,H.a)(e,r?.in);return isNaN(t)?(0,Y.w)(r?.in||e,NaN):(t&&n.setDate(n.getDate()+t),n)}function V(e,t,r){let n=(0,H.a)(e,r?.in);if(isNaN(t))return(0,Y.w)(r?.in||e,NaN);if(!t)return n;let o=n.getDate(),i=(0,Y.w)(r?.in||e,n.getTime());return(i.setMonth(n.getMonth()+t+1,0),o>=i.getDate())?i:(n.setFullYear(i.getFullYear(),i.getMonth(),o),n)}var G=r(48750),K=r(29789),Z=r(78872);function J(e,t){let r=(0,Z.q)(),n=t?.weekStartsOn??t?.locale?.options?.weekStartsOn??r.weekStartsOn??r.locale?.options?.weekStartsOn??0,o=(0,H.a)(e,t?.in),i=o.getDay();return o.setDate(o.getDate()+((i<n?-7:0)+6-(i-n))),o.setHours(23,59,59,999),o}var Q=r(38832),ee=r(46495),et=r(91522),er=r(30319),en=r(64916),eo=r(51877),ei=r(27272);function ea(e,t){let r=t.startOfMonth(e),n=r.getDay();return 1===n?r:0===n?t.addDays(r,-6):t.addDays(r,-1*(n-1))}class es{constructor(e,t){this.Date=Date,this.today=()=>this.overrides?.today?this.overrides.today():this.options.timeZone?q.tz(this.options.timeZone):new this.Date,this.newDate=(e,t,r)=>this.overrides?.newDate?this.overrides.newDate(e,t,r):this.options.timeZone?new q(e,t,r,this.options.timeZone):new Date(e,t,r),this.addDays=(e,t)=>this.overrides?.addDays?this.overrides.addDays(e,t):X(e,t),this.addMonths=(e,t)=>this.overrides?.addMonths?this.overrides.addMonths(e,t):V(e,t),this.addWeeks=(e,t)=>this.overrides?.addWeeks?this.overrides.addWeeks(e,t):X(e,7*t,void 0),this.addYears=(e,t)=>this.overrides?.addYears?this.overrides.addYears(e,t):V(e,12*t,void 0),this.differenceInCalendarDays=(e,t)=>this.overrides?.differenceInCalendarDays?this.overrides.differenceInCalendarDays(e,t):(0,G.m)(e,t),this.differenceInCalendarMonths=(e,t)=>this.overrides?.differenceInCalendarMonths?this.overrides.differenceInCalendarMonths(e,t):function(e,t,r){let[n,o]=(0,K.x)(void 0,e,t);return 12*(n.getFullYear()-o.getFullYear())+(n.getMonth()-o.getMonth())}(e,t),this.eachMonthOfInterval=e=>this.overrides?.eachMonthOfInterval?this.overrides.eachMonthOfInterval(e):function(e,t){let{start:r,end:n}=function(e,t){let[r,n]=(0,K.x)(e,t.start,t.end);return{start:r,end:n}}(void 0,e),o=+r>+n,i=o?+r:+n,a=o?n:r;a.setHours(0,0,0,0),a.setDate(1);let s=(void 0)??1;if(!s)return[];s<0&&(s=-s,o=!o);let l=[];for(;+a<=i;)l.push((0,Y.w)(r,a)),a.setMonth(a.getMonth()+s);return o?l.reverse():l}(e),this.endOfBroadcastWeek=e=>this.overrides?.endOfBroadcastWeek?this.overrides.endOfBroadcastWeek(e,this):function(e,t){let r=ea(e,t),n=function(e,t){let r=t.startOfMonth(e),n=r.getDay()>0?r.getDay():7,o=t.addDays(e,-n+1),i=t.addDays(o,34);return t.getMonth(e)===t.getMonth(i)?5:4}(e,t);return t.addDays(r,7*n-1)}(e,this),this.endOfISOWeek=e=>this.overrides?.endOfISOWeek?this.overrides.endOfISOWeek(e):J(e,{...void 0,weekStartsOn:1}),this.endOfMonth=e=>this.overrides?.endOfMonth?this.overrides.endOfMonth(e):function(e,t){let r=(0,H.a)(e,void 0),n=r.getMonth();return r.setFullYear(r.getFullYear(),n+1,0),r.setHours(23,59,59,999),r}(e),this.endOfWeek=e=>this.overrides?.endOfWeek?this.overrides.endOfWeek(e,this.options):J(e,this.options),this.endOfYear=e=>this.overrides?.endOfYear?this.overrides.endOfYear(e):function(e,t){let r=(0,H.a)(e,void 0),n=r.getFullYear();return r.setFullYear(n+1,0,0),r.setHours(23,59,59,999),r}(e),this.format=(e,t)=>{let r=this.overrides?.format?this.overrides.format(e,t,this.options):(0,A.GP)(e,t,this.options);return this.options.numerals&&"latn"!==this.options.numerals?this.replaceDigits(r):r},this.getISOWeek=e=>this.overrides?.getISOWeek?this.overrides.getISOWeek(e):(0,Q.s)(e),this.getMonth=e=>{var t;return this.overrides?.getMonth?this.overrides.getMonth(e,this.options):(t=this.options,(0,H.a)(e,t?.in).getMonth())},this.getYear=e=>{var t;return this.overrides?.getYear?this.overrides.getYear(e,this.options):(t=this.options,(0,H.a)(e,t?.in).getFullYear())},this.getWeek=e=>this.overrides?.getWeek?this.overrides.getWeek(e,this.options):(0,ee.N)(e,this.options),this.isAfter=(e,t)=>this.overrides?.isAfter?this.overrides.isAfter(e,t):+(0,H.a)(e)>+(0,H.a)(t),this.isBefore=(e,t)=>this.overrides?.isBefore?this.overrides.isBefore(e,t):+(0,H.a)(e)<+(0,H.a)(t),this.isDate=e=>this.overrides?.isDate?this.overrides.isDate(e):(0,et.$)(e),this.isSameDay=(e,t)=>this.overrides?.isSameDay?this.overrides.isSameDay(e,t):function(e,t,r){let[n,o]=(0,K.x)(void 0,e,t);return+(0,er.o)(n)==+(0,er.o)(o)}(e,t),this.isSameMonth=(e,t)=>this.overrides?.isSameMonth?this.overrides.isSameMonth(e,t):function(e,t,r){let[n,o]=(0,K.x)(void 0,e,t);return n.getFullYear()===o.getFullYear()&&n.getMonth()===o.getMonth()}(e,t),this.isSameYear=(e,t)=>this.overrides?.isSameYear?this.overrides.isSameYear(e,t):function(e,t,r){let[n,o]=(0,K.x)(void 0,e,t);return n.getFullYear()===o.getFullYear()}(e,t),this.max=e=>this.overrides?.max?this.overrides.max(e):function(e,t){let r,n;return e.forEach(e=>{n||"object"!=typeof e||(n=Y.w.bind(null,e));let t=(0,H.a)(e,n);(!r||r<t||isNaN(+t))&&(r=t)}),(0,Y.w)(n,r||NaN)}(e),this.min=e=>this.overrides?.min?this.overrides.min(e):function(e,t){let r,n;return e.forEach(e=>{n||"object"!=typeof e||(n=Y.w.bind(null,e));let t=(0,H.a)(e,n);(!r||r>t||isNaN(+t))&&(r=t)}),(0,Y.w)(n,r||NaN)}(e),this.setMonth=(e,t)=>this.overrides?.setMonth?this.overrides.setMonth(e,t):function(e,t,r){let n=(0,H.a)(e,void 0),o=n.getFullYear(),i=n.getDate(),a=(0,Y.w)(e,0);a.setFullYear(o,t,15),a.setHours(0,0,0,0);let s=function(e,t){let r=(0,H.a)(e,void 0),n=r.getFullYear(),o=r.getMonth(),i=(0,Y.w)(r,0);return i.setFullYear(n,o+1,0),i.setHours(0,0,0,0),i.getDate()}(a);return n.setMonth(t,Math.min(i,s)),n}(e,t),this.setYear=(e,t)=>this.overrides?.setYear?this.overrides.setYear(e,t):function(e,t,r){let n=(0,H.a)(e,void 0);return isNaN(+n)?(0,Y.w)(e,NaN):(n.setFullYear(t),n)}(e,t),this.startOfBroadcastWeek=e=>this.overrides?.startOfBroadcastWeek?this.overrides.startOfBroadcastWeek(e,this):ea(e,this),this.startOfDay=e=>this.overrides?.startOfDay?this.overrides.startOfDay(e):(0,er.o)(e),this.startOfISOWeek=e=>this.overrides?.startOfISOWeek?this.overrides.startOfISOWeek(e):(0,en.b)(e),this.startOfMonth=e=>this.overrides?.startOfMonth?this.overrides.startOfMonth(e):function(e,t){let r=(0,H.a)(e,void 0);return r.setDate(1),r.setHours(0,0,0,0),r}(e),this.startOfWeek=e=>this.overrides?.startOfWeek?this.overrides.startOfWeek(e,this.options):(0,eo.k)(e,this.options),this.startOfYear=e=>this.overrides?.startOfYear?this.overrides.startOfYear(e):(0,ei.D)(e),this.options={locale:D.c,...e},this.overrides=t}getDigitMap(){let{numerals:e="latn"}=this.options,t=new Intl.NumberFormat("en-US",{numberingSystem:e}),r={};for(let e=0;e<10;e++)r[e.toString()]=t.format(e);return r}replaceDigits(e){let t=this.getDigitMap();return e.replace(/\d/g,e=>t[e]||e)}formatNumber(e){return this.replaceDigits(e.toString())}}let el=new es;function ec(e){return c.createElement("button",{...e})}function eu(e){return c.createElement("span",{...e})}function ef(e){let{size:t=24,orientation:r="left",className:n}=e;return c.createElement("svg",{className:n,width:t,height:t,viewBox:"0 0 24 24"},"up"===r&&c.createElement("polygon",{points:"6.77 17 12.5 11.43 18.24 17 20 15.28 12.5 8 5 15.28"}),"down"===r&&c.createElement("polygon",{points:"6.77 8 12.5 13.57 18.24 8 20 9.72 12.5 17 5 9.72"}),"left"===r&&c.createElement("polygon",{points:"16 18.112 9.81111111 12 16 5.87733333 14.0888889 4 6 12 14.0888889 20"}),"right"===r&&c.createElement("polygon",{points:"8 18.112 14.18888889 12 8 5.87733333 9.91111111 4 18 12 9.91111111 20"}))}function ep(e){let{day:t,modifiers:r,...n}=e;return c.createElement("td",{...n})}function ed(e){let{day:t,modifiers:r,...n}=e,o=c.useRef(null);return c.useEffect(()=>{r.focused&&o.current?.focus()},[r.focused]),c.createElement("button",{ref:o,...n})}function eh(e){let{options:t,className:r,components:n,classNames:o,...i}=e,a=[o[uq.Dropdown],r].join(" "),s=t?.find(({value:e})=>e===i.value);return c.createElement("span",{"data-disabled":i.disabled,className:o[uq.DropdownRoot]},c.createElement(n.Select,{className:a,...i},t?.map(({value:e,label:t,disabled:r})=>c.createElement(n.Option,{key:e,value:e,disabled:r},t))),c.createElement("span",{className:o[uq.CaptionLabel],"aria-hidden":!0},s?.label,c.createElement(n.Chevron,{orientation:"down",size:18,className:o[uq.Chevron]})))}function ey(e){return c.createElement("div",{...e})}function ev(e){return c.createElement("div",{...e})}function em(e){let{calendarMonth:t,displayIndex:r,...n}=e;return c.createElement("div",{...n},e.children)}function eb(e){let{calendarMonth:t,displayIndex:r,...n}=e;return c.createElement("div",{...n})}function eg(e){return c.createElement("table",{...e})}function ex(e){return c.createElement("div",{...e})}function ew(e){let{components:t}=N();return c.createElement(t.Dropdown,{...e})}function eO(e){let{onPreviousClick:t,onNextClick:r,previousMonth:n,nextMonth:o,...i}=e,{components:a,classNames:s,labels:{labelPrevious:l,labelNext:u}}=N(),f=(0,c.useCallback)(e=>{o&&r?.(e)},[o,r]),p=(0,c.useCallback)(e=>{n&&t?.(e)},[n,t]);return c.createElement("nav",{...i},c.createElement(a.PreviousMonthButton,{type:"button",className:s[uq.PreviousMonthButton],tabIndex:n?void 0:-1,"aria-disabled":!n||void 0,"aria-label":l(n),onClick:p},c.createElement(a.Chevron,{disabled:!n||void 0,className:s[uq.Chevron],orientation:"left"})),c.createElement(a.NextMonthButton,{type:"button",className:s[uq.NextMonthButton],tabIndex:o?void 0:-1,"aria-disabled":!o||void 0,"aria-label":u(o),onClick:f},c.createElement(a.Chevron,{disabled:!o||void 0,orientation:"right",className:s[uq.Chevron]})))}function ej(e){let{components:t}=N();return c.createElement(t.Button,{...e})}function eS(e){return c.createElement("option",{...e})}function eP(e){let{components:t}=N();return c.createElement(t.Button,{...e})}function eE(e){let{rootRef:t,...r}=e;return c.createElement("div",{...r,ref:t})}function eA(e){return c.createElement("select",{...e})}function ek(e){let{week:t,...r}=e;return c.createElement("tr",{...r})}function eM(e){return c.createElement("th",{...e})}function eT(e){return c.createElement("thead",{"aria-hidden":!0},c.createElement("tr",{...e}))}function e_(e){let{week:t,...r}=e;return c.createElement("th",{...r})}function eC(e){return c.createElement("th",{...e})}function eN(e){return c.createElement("tbody",{...e})}function eD(e){let{components:t}=N();return c.createElement(t.Dropdown,{...e})}function eI(e,t,r){return(r??new es(t)).format(e,"LLLL y")}let eR=eI;function eL(e,t,r){return(r??new es(t)).format(e,"d")}function eB(e,t=el){return t.format(e,"LLLL")}function eF(e){return e<10?`0${e.toLocaleString()}`:`${e.toLocaleString()}`}function ez(){return""}function e$(e,t,r){return(r??new es(t)).format(e,"cccccc")}function eW(e,t=el){return t.format(e,"yyyy")}let eU=eW;function eq(e,t,r){return(r??new es(t)).format(e,"LLLL y")}let eY=eq;function eH(e,t,r,n){let o=(n??new es(r)).format(e,"PPPP");return t?.today&&(o=`Today, ${o}`),o}function eX(e,t,r,n){let o=(n??new es(r)).format(e,"PPPP");return t.today&&(o=`Today, ${o}`),t.selected&&(o=`${o}, selected`),o}let eV=eX;function eG(){return""}function eK(e){return"Choose the Month"}function eZ(e){return"Go to the Next Month"}function eJ(e){return"Go to the Previous Month"}function eQ(e,t,r){return(r??new es(t)).format(e,"cccc")}function e0(e,t){return`Week ${e}`}function e1(e){return"Week Number"}function e2(e){return"Choose the Year"}let e5=e=>e instanceof HTMLElement?e:null,e3=e=>[...e.querySelectorAll("[data-animated-month]")??[]],e4=e=>e5(e.querySelector("[data-animated-month]")),e8=e=>e5(e.querySelector("[data-animated-caption]")),e6=e=>e5(e.querySelector("[data-animated-weeks]")),e7=e=>e5(e.querySelector("[data-animated-nav]")),e9=e=>e5(e.querySelector("[data-animated-weekdays]"));function te(e,t){let{month:r,defaultMonth:n,today:o=t.today(),numberOfMonths:i=1,endMonth:a,startMonth:s,timeZone:l}=e,c=r||n||o,{differenceInCalendarMonths:u,addMonths:f,startOfMonth:p}=t;return a&&0>u(a,c)&&(c=f(a,-1*(i-1))),s&&0>u(c,s)&&(c=s),p(c=l?new q(c,l):c)}class tt{constructor(e,t,r=el){this.date=e,this.displayMonth=t,this.outside=!!(t&&!r.isSameMonth(e,t)),this.dateLib=r}isEqualTo(e){return this.dateLib.isSameDay(e.date,this.date)&&this.dateLib.isSameMonth(e.displayMonth,this.displayMonth)}}class tr{constructor(e,t){this.days=t,this.weekNumber=e}}class tn{constructor(e,t){this.date=e,this.weeks=t}}function to(e,t){let[r,n]=(0,c.useState)(e);return[void 0===t?r:t,n]}function ti(e){return!e[uY.disabled]&&!e[uY.hidden]&&!e[uY.outside]}function ta(e,t,r=!1,n=el){let{from:o,to:i}=e,{differenceInCalendarDays:a,isSameDay:s}=n;return o&&i?(0>a(i,o)&&([o,i]=[i,o]),a(t,o)>=+!!r&&a(i,t)>=+!!r):!r&&i?s(i,t):!r&&!!o&&s(o,t)}function ts(e){return!!(e&&"object"==typeof e&&"before"in e&&"after"in e)}function tl(e){return!!(e&&"object"==typeof e&&"from"in e)}function tc(e){return!!(e&&"object"==typeof e&&"after"in e)}function tu(e){return!!(e&&"object"==typeof e&&"before"in e)}function tf(e){return!!(e&&"object"==typeof e&&"dayOfWeek"in e)}function tp(e,t){return Array.isArray(e)&&e.every(t.isDate)}function td(e,t,r=el){let n=Array.isArray(t)?t:[t],{isSameDay:o,differenceInCalendarDays:i,isAfter:a}=r;return n.some(t=>{if("boolean"==typeof t)return t;if(r.isDate(t))return o(e,t);if(tp(t,r))return t.includes(e);if(tl(t))return ta(t,e,!1,r);if(tf(t))return Array.isArray(t.dayOfWeek)?t.dayOfWeek.includes(e.getDay()):t.dayOfWeek===e.getDay();if(ts(t)){let r=i(t.before,e),n=i(t.after,e),o=r>0,s=n<0;return a(t.before,t.after)?s&&o:o||s}return tc(t)?i(e,t.after)>0:tu(t)?i(t.before,e)>0:"function"==typeof t&&t(e)})}function th(e,t,r=el){return ta(e,t.from,!1,r)||ta(e,t.to,!1,r)||ta(t,e.from,!1,r)||ta(t,e.to,!1,r)}function ty(e){let{components:t,formatters:r,labels:n,dateLib:s,locale:l,classNames:u}=(0,c.useMemo)(()=>{var t,r;let n={...D.c,...e.locale};return{dateLib:new es({locale:n,weekStartsOn:e.broadcastCalendar?1:e.weekStartsOn,firstWeekContainsDate:e.firstWeekContainsDate,useAdditionalWeekYearTokens:e.useAdditionalWeekYearTokens,useAdditionalDayOfYearTokens:e.useAdditionalDayOfYearTokens,timeZone:e.timeZone,numerals:e.numerals},e.dateLib),components:(t=e.components,{...o,...t}),formatters:(r=e.formatters,r?.formatMonthCaption&&!r.formatCaption&&(r.formatCaption=r.formatMonthCaption),r?.formatYearCaption&&!r.formatYearDropdown&&(r.formatYearDropdown=r.formatYearCaption),{...i,...r}),labels:{...a,...e.labels},locale:n,classNames:{...function(){let e={};for(let t in uq)e[uq[t]]=`rdp-${uq[t]}`;for(let t in uY)e[uY[t]]=`rdp-${uY[t]}`;for(let t in uH)e[uH[t]]=`rdp-${uH[t]}`;for(let t in uX)e[uX[t]]=`rdp-${uX[t]}`;return e}(),...e.classNames}}},[e.locale,e.broadcastCalendar,e.weekStartsOn,e.firstWeekContainsDate,e.useAdditionalWeekYearTokens,e.useAdditionalDayOfYearTokens,e.timeZone,e.numerals,e.dateLib,e.components,e.formatters,e.labels,e.classNames]),{captionLayout:f,mode:p,onDayBlur:d,onDayClick:h,onDayFocus:y,onDayKeyDown:v,onDayMouseEnter:m,onDayMouseLeave:b,onNextClick:g,onPrevClick:x,showWeekNumber:w,styles:O}=e,{formatCaption:j,formatDay:S,formatMonthDropdown:P,formatWeekNumber:E,formatWeekNumberHeader:A,formatWeekdayName:k,formatYearDropdown:M}=r,T=function(e,t){let[r,n]=function(e,t){let{startMonth:r,endMonth:n}=e,{startOfYear:o,startOfDay:i,startOfMonth:a,endOfMonth:s,addYears:l,endOfYear:c,newDate:u,today:f}=t,{fromYear:p,toYear:d,fromMonth:h,toMonth:y}=e;!r&&h&&(r=h),!r&&p&&(r=t.newDate(p,0,1)),!n&&y&&(n=y),!n&&d&&(n=u(d,11,31));let v="dropdown"===e.captionLayout||"dropdown-years"===e.captionLayout;return r?r=a(r):p?r=u(p,0,1):!r&&v&&(r=o(l(e.today??f(),-100))),n?n=s(n):d?n=u(d,11,31):!n&&v&&(n=c(e.today??f())),[r?i(r):r,n?i(n):n]}(e,t),{startOfMonth:o,endOfMonth:i}=t,a=te(e,t),[s,l]=to(a,e.month?a:void 0);(0,c.useEffect)(()=>{l(te(e,t))},[e.timeZone]);let u=function(e,t,r,n){let{numberOfMonths:o=1}=r,i=[];for(let r=0;r<o;r++){let o=n.addMonths(e,r);if(t&&o>t)break;i.push(o)}return i}(s,n,e,t),f=function(e,t,r,n){let o=e[0],i=e[e.length-1],{ISOWeek:a,fixedWeeks:s,broadcastCalendar:l}=r??{},{addDays:c,differenceInCalendarDays:u,differenceInCalendarMonths:f,endOfBroadcastWeek:p,endOfISOWeek:d,endOfMonth:h,endOfWeek:y,isAfter:v,startOfBroadcastWeek:m,startOfISOWeek:b,startOfWeek:g}=n,x=l?m(o,n):a?b(o):g(o),w=u(l?p(i,n):a?d(h(i)):y(h(i)),x),O=f(i,o)+1,j=[];for(let e=0;e<=w;e++){let r=c(x,e);if(t&&v(r,t))break;j.push(r)}let S=(l?35:42)*O;if(s&&j.length<S){let e=S-j.length;for(let t=0;t<e;t++){let e=c(j[j.length-1],1);j.push(e)}}return j}(u,e.endMonth?i(e.endMonth):void 0,e,t),p=function(e,t,r,n){let{addDays:o,endOfBroadcastWeek:i,endOfISOWeek:a,endOfMonth:s,endOfWeek:l,getISOWeek:c,getWeek:u,startOfBroadcastWeek:f,startOfISOWeek:p,startOfWeek:d}=n,h=e.reduce((e,h)=>{let y=r.broadcastCalendar?f(h,n):r.ISOWeek?p(h):d(h),v=r.broadcastCalendar?i(h,n):r.ISOWeek?a(s(h)):l(s(h)),m=t.filter(e=>e>=y&&e<=v),b=r.broadcastCalendar?35:42;if(r.fixedWeeks&&m.length<b){let e=t.filter(e=>{let t=b-m.length;return e>v&&e<=o(v,t)});m.push(...e)}let g=m.reduce((e,t)=>{let o=r.ISOWeek?c(t):u(t),i=e.find(e=>e.weekNumber===o),a=new tt(t,h,n);return i?i.days.push(a):e.push(new tr(o,[a])),e},[]),x=new tn(h,g);return e.push(x),e},[]);return r.reverseMonths?h.reverse():h}(u,f,e,t),d=p.reduce((e,t)=>[...e,...t.weeks],[]),h=p.reduce((e,t)=>[...e,...t.weeks.reduce((e,t)=>[...e,...t.days],[])],[]),y=function(e,t,r,n){if(r.disableNavigation)return;let{pagedNavigation:o,numberOfMonths:i}=r,{startOfMonth:a,addMonths:s,differenceInCalendarMonths:l}=n,c=a(e);if(!t||!(0>=l(c,t)))return s(c,-(o?i??1:1))}(s,r,e,t),v=function(e,t,r,n){if(r.disableNavigation)return;let{pagedNavigation:o,numberOfMonths:i=1}=r,{startOfMonth:a,addMonths:s,differenceInCalendarMonths:l}=n,c=a(e);if(!t||!(l(t,e)<i))return s(c,o?i:1)}(s,n,e,t),{disableNavigation:m,onMonthChange:b}=e,g=e=>d.some(t=>t.days.some(t=>t.isEqualTo(e))),x=e=>{if(m)return;let t=o(e);r&&t<o(r)&&(t=o(r)),n&&t>o(n)&&(t=o(n)),l(t),b?.(t)};return{months:p,weeks:d,days:h,navStart:r,navEnd:n,previousMonth:y,nextMonth:v,goToMonth:x,goToDay:e=>{g(e)||x(e.date)}}}(e,s),{days:_,months:N,navStart:I,navEnd:R,previousMonth:L,nextMonth:B,goToMonth:F}=T,z=function(e,t,r){let{disabled:n,hidden:o,modifiers:i,showOutsideDays:a,broadcastCalendar:s,today:l}=t,{isSameDay:c,isSameMonth:u,startOfMonth:f,isBefore:p,endOfMonth:d,isAfter:h}=r,y=t.startMonth&&f(t.startMonth),v=t.endMonth&&d(t.endMonth),m={[uY.focused]:[],[uY.outside]:[],[uY.disabled]:[],[uY.hidden]:[],[uY.today]:[]},b={};for(let t of e){let{date:e,displayMonth:f}=t,d=!!(f&&!u(e,f)),g=!!(y&&p(e,y)),x=!!(v&&h(e,v)),w=!!(n&&td(e,n,r)),O=!!(o&&td(e,o,r))||g||x||!s&&!a&&d||s&&!1===a&&d,j=c(e,l??r.today());d&&m.outside.push(t),w&&m.disabled.push(t),O&&m.hidden.push(t),j&&m.today.push(t),i&&Object.keys(i).forEach(n=>{let o=i?.[n];o&&td(e,o,r)&&(b[n]?b[n].push(t):b[n]=[t])})}return e=>{let t={[uY.focused]:!1,[uY.disabled]:!1,[uY.hidden]:!1,[uY.outside]:!1,[uY.today]:!1},r={};for(let r in m){let n=m[r];t[r]=n.some(t=>t===e)}for(let t in b)r[t]=b[t].some(t=>t===e);return{...t,...r}}}(_,e,s),{isSelected:$,select:W,selected:U}=function(e,t){let r=function(e,t){let{selected:r,required:n,onSelect:o}=e,[i,a]=to(r,o?r:void 0),s=o?r:i,{isSameDay:l}=t;return{selected:s,select:(e,t,r)=>{let i=e;return!n&&s&&s&&l(e,s)&&(i=void 0),o||a(i),o?.(i,e,t,r),i},isSelected:e=>!!s&&l(s,e)}}(e,t),n=function(e,t){let{selected:r,required:n,onSelect:o}=e,[i,a]=to(r,o?r:void 0),s=o?r:i,{isSameDay:l}=t,c=e=>s?.some(t=>l(t,e))??!1,{min:u,max:f}=e;return{selected:s,select:(e,t,r)=>{let i=[...s??[]];if(c(e)){if(s?.length===u||n&&s?.length===1)return;i=s?.filter(t=>!l(t,e))}else i=s?.length===f?[e]:[...i,e];return o||a(i),o?.(i,e,t,r),i},isSelected:c}}(e,t),o=function(e,t){let{disabled:r,excludeDisabled:n,selected:o,required:i,onSelect:a}=e,[s,l]=to(o,a?o:void 0),c=a?o:s;return{selected:c,select:(o,s,u)=>{let{min:f,max:p}=e,d=o?function(e,t,r=0,n=0,o=!1,i=el){let a,{from:s,to:l}=t||{},{isSameDay:c,isAfter:u,isBefore:f}=i;if(s||l){if(s&&!l)a=c(s,e)?o?{from:s,to:void 0}:void 0:f(e,s)?{from:e,to:s}:{from:s,to:e};else if(s&&l)if(c(s,e)&&c(l,e))a=o?{from:s,to:l}:void 0;else if(c(s,e))a={from:s,to:r>0?void 0:e};else if(c(l,e))a={from:e,to:r>0?void 0:e};else if(f(e,s))a={from:e,to:l};else if(u(e,s))a={from:s,to:e};else if(u(e,l))a={from:s,to:e};else throw Error("Invalid range")}else a={from:e,to:r>0?void 0:e};if(a?.from&&a?.to){let t=i.differenceInCalendarDays(a.to,a.from);n>0&&t>n?a={from:e,to:void 0}:r>1&&t<r&&(a={from:e,to:void 0})}return a}(o,c,f,p,i,t):void 0;return n&&r&&d?.from&&d.to&&function(e,t,r=el){let n=Array.isArray(t)?t:[t];if(n.filter(e=>"function"!=typeof e).some(t=>"boolean"==typeof t?t:r.isDate(t)?ta(e,t,!1,r):tp(t,r)?t.some(t=>ta(e,t,!1,r)):tl(t)?!!t.from&&!!t.to&&th(e,{from:t.from,to:t.to},r):tf(t)?function(e,t,r=el){let n=Array.isArray(t)?t:[t],o=e.from,i=Math.min(r.differenceInCalendarDays(e.to,e.from),6);for(let e=0;e<=i;e++){if(n.includes(o.getDay()))return!0;o=r.addDays(o,1)}return!1}(e,t.dayOfWeek,r):ts(t)?r.isAfter(t.before,t.after)?th(e,{from:r.addDays(t.after,1),to:r.addDays(t.before,-1)},r):td(e.from,t,r)||td(e.to,t,r):!!(tc(t)||tu(t))&&(td(e.from,t,r)||td(e.to,t,r))))return!0;let o=n.filter(e=>"function"==typeof e);if(o.length){let t=e.from,n=r.differenceInCalendarDays(e.to,e.from);for(let e=0;e<=n;e++){if(o.some(e=>e(t)))return!0;t=r.addDays(t,1)}}return!1}({from:d.from,to:d.to},r,t)&&(d.from=o,d.to=void 0),a||l(d),a?.(d,o,s,u),d},isSelected:e=>c&&ta(c,e,!1,t)}}(e,t);switch(e.mode){case"single":return r;case"multiple":return n;case"range":return o;default:return}}(e,s)??{},{blur:q,focused:Y,isFocusTarget:H,moveFocus:X,setFocused:V}=function(e,t,r,n,o){let{autoFocus:i}=e,[a,s]=(0,c.useState)(),l=function(e,t,r,n){let o,i=-1;for(let a of e){let e=t(a);ti(e)&&(e[uY.focused]&&i<uV.FocusedModifier?(o=a,i=uV.FocusedModifier):n?.isEqualTo(a)&&i<uV.LastFocused?(o=a,i=uV.LastFocused):r(a.date)&&i<uV.Selected?(o=a,i=uV.Selected):e[uY.today]&&i<uV.Today&&(o=a,i=uV.Today))}return o||(o=e.find(e=>ti(t(e)))),o}(t.days,r,n||(()=>!1),a),[u,f]=(0,c.useState)(i?l:void 0);return{isFocusTarget:e=>!!l?.isEqualTo(e),setFocused:f,focused:u,blur:()=>{s(u),f(void 0)},moveFocus:(r,n)=>{if(!u)return;let i=function e(t,r,n,o,i,a,s,l=0){if(l>365)return;let c=function(e,t,r,n,o,i,a){let{ISOWeek:s,broadcastCalendar:l}=i,{addDays:c,addMonths:u,addWeeks:f,addYears:p,endOfBroadcastWeek:d,endOfISOWeek:h,endOfWeek:y,max:v,min:m,startOfBroadcastWeek:b,startOfISOWeek:g,startOfWeek:x}=a,w=({day:c,week:f,month:u,year:p,startOfWeek:e=>l?b(e,a):s?g(e):x(e),endOfWeek:e=>l?d(e,a):s?h(e):y(e)})[e](r,"after"===t?1:-1);return"before"===t&&n?w=v([n,w]):"after"===t&&o&&(w=m([o,w])),w}(t,r,n.date,o,i,a,s),u=!!(a.disabled&&td(c,a.disabled,s)),f=!!(a.hidden&&td(c,a.hidden,s)),p=new tt(c,c,s);return u||f?e(t,r,p,o,i,a,s,l+1):p}(r,n,u,t.navStart,t.navEnd,e,o);i&&(t.goToDay(i),f(i))}}}(e,T,z,$??(()=>!1),s),{labelDayButton:G,labelGridcell:K,labelGrid:Z,labelMonthDropdown:J,labelNav:Q,labelWeekday:ee,labelWeekNumber:et,labelWeekNumberHeader:er,labelYearDropdown:en}=n,eo=(0,c.useMemo)(()=>(function(e,t,r){let n=e.today(),o=t?e.startOfISOWeek(n):e.startOfWeek(n),i=[];for(let t=0;t<7;t++){let r=e.addDays(o,t);i.push(r)}return i})(s,e.ISOWeek),[s,e.ISOWeek]),ei=void 0!==p||void 0!==h,ea=(0,c.useCallback)(()=>{L&&(F(L),x?.(L))},[L,F,x]),ec=(0,c.useCallback)(()=>{B&&(F(B),g?.(B))},[F,B,g]),eu=(0,c.useCallback)((e,t)=>r=>{r.preventDefault(),r.stopPropagation(),V(e),W?.(e.date,t,r),h?.(e.date,t,r)},[W,h,V]),ef=(0,c.useCallback)((e,t)=>r=>{V(e),y?.(e.date,t,r)},[y,V]),ep=(0,c.useCallback)((e,t)=>r=>{q(),d?.(e.date,t,r)},[q,d]),ed=(0,c.useCallback)((t,r)=>n=>{let o={ArrowLeft:["day","rtl"===e.dir?"after":"before"],ArrowRight:["day","rtl"===e.dir?"before":"after"],ArrowDown:["week","after"],ArrowUp:["week","before"],PageUp:[n.shiftKey?"year":"month","before"],PageDown:[n.shiftKey?"year":"month","after"],Home:["startOfWeek","before"],End:["endOfWeek","after"]};if(o[n.key]){n.preventDefault(),n.stopPropagation();let[e,t]=o[n.key];X(e,t)}v?.(t.date,r,n)},[X,v,e.dir]),eh=(0,c.useCallback)((e,t)=>r=>{m?.(e.date,t,r)},[m]),ey=(0,c.useCallback)((e,t)=>r=>{b?.(e.date,t,r)},[b]),ev=(0,c.useCallback)(e=>t=>{let r=Number(t.target.value);F(s.setMonth(s.startOfMonth(e),r))},[s,F]),em=(0,c.useCallback)(e=>t=>{let r=Number(t.target.value);F(s.setYear(s.startOfMonth(e),r))},[s,F]),{className:eb,style:eg}=(0,c.useMemo)(()=>({className:[u[uq.Root],e.className].filter(Boolean).join(" "),style:{...O?.[uq.Root],...e.style}}),[u,e.className,e.style,O]),ex=function(e){let t={"data-mode":e.mode??void 0,"data-required":"required"in e?e.required:void 0,"data-multiple-months":e.numberOfMonths&&e.numberOfMonths>1||void 0,"data-week-numbers":e.showWeekNumber||void 0,"data-broadcast-calendar":e.broadcastCalendar||void 0};return Object.entries(e).forEach(([e,r])=>{e.startsWith("data-")&&(t[e]=r)}),t}(e),ew=(0,c.useRef)(null);return!function(e,t,{classNames:r,months:n,focused:o,dateLib:i}){let a=(0,c.useRef)(null),s=(0,c.useRef)(n),l=(0,c.useRef)(!1);(0,c.useLayoutEffect)(()=>{let c=s.current;if(s.current=n,!t||!e.current||!(e.current instanceof HTMLElement)||0===n.length||0===c.length||n.length!==c.length)return;let u=i.isSameMonth(n[0].date,c[0].date),f=i.isAfter(n[0].date,c[0].date),p=f?r[uX.caption_after_enter]:r[uX.caption_before_enter],d=f?r[uX.weeks_after_enter]:r[uX.weeks_before_enter],h=a.current,y=e.current.cloneNode(!0);if(y instanceof HTMLElement?(e3(y).forEach(e=>{if(!(e instanceof HTMLElement))return;let t=e4(e);t&&e.contains(t)&&e.removeChild(t);let r=e8(e);r&&r.classList.remove(p);let n=e6(e);n&&n.classList.remove(d)}),a.current=y):a.current=null,l.current||u||o)return;let v=h instanceof HTMLElement?e3(h):[],m=e3(e.current);if(m&&m.every(e=>e instanceof HTMLElement)&&v&&v.every(e=>e instanceof HTMLElement)){l.current=!0;let t=[];e.current.style.isolation="isolate";let n=e7(e.current);n&&(n.style.zIndex="1"),m.forEach((o,i)=>{let a=v[i];if(!a)return;o.style.position="relative",o.style.overflow="hidden";let s=e8(o);s&&s.classList.add(p);let c=e6(o);c&&c.classList.add(d);let u=()=>{l.current=!1,e.current&&(e.current.style.isolation=""),n&&(n.style.zIndex=""),s&&s.classList.remove(p),c&&c.classList.remove(d),o.style.position="",o.style.overflow="",o.contains(a)&&o.removeChild(a)};t.push(u),a.style.pointerEvents="none",a.style.position="absolute",a.style.overflow="hidden",a.setAttribute("aria-hidden","true");let h=e9(a);h&&(h.style.opacity="0");let y=e8(a);y&&(y.classList.add(f?r[uX.caption_before_exit]:r[uX.caption_after_exit]),y.addEventListener("animationend",u));let m=e6(a);m&&m.classList.add(f?r[uX.weeks_before_exit]:r[uX.weeks_after_exit]),o.insertBefore(a,o.firstChild)})}})}(ew,!!e.animate,{classNames:u,months:N,focused:Y,dateLib:s}),c.createElement(C.Provider,{value:{dayPickerProps:e,selected:U,select:W,isSelected:$,months:N,nextMonth:B,previousMonth:L,goToMonth:F,getModifiers:z,components:t,classNames:u,styles:O,labels:n,formatters:r}},c.createElement(t.Root,{rootRef:e.animate?ew:void 0,className:eb,style:eg,dir:e.dir,id:e.id,lang:e.lang,nonce:e.nonce,title:e.title,role:e.role,"aria-label":e["aria-label"],...ex},c.createElement(t.Months,{className:u[uq.Months],style:O?.[uq.Months]},!e.hideNavigation&&c.createElement(t.Nav,{"data-animated-nav":e.animate?"true":void 0,className:u[uq.Nav],style:O?.[uq.Nav],"aria-label":Q(),onPreviousClick:ea,onNextClick:ec,previousMonth:L,nextMonth:B}),N.map((n,o)=>{let i=function(e,t,r,n,o){let{startOfMonth:i,startOfYear:a,endOfYear:s,eachMonthOfInterval:l,getMonth:c}=o;return l({start:a(e),end:s(e)}).map(e=>{let a=n.formatMonthDropdown(e,o);return{value:c(e),label:a,disabled:t&&e<i(t)||r&&e>i(r)||!1}})}(n.date,I,R,r,s),a=function(e,t,r,n){if(!e||!t)return;let{startOfYear:o,endOfYear:i,addYears:a,getYear:s,isBefore:l,isSameYear:c}=n,u=o(e),f=i(t),p=[],d=u;for(;l(d,f)||c(d,f);)p.push(d),d=a(d,1);return p.map(e=>{let t=r.formatYearDropdown(e,n);return{value:s(e),label:t,disabled:!1}})}(I,R,r,s);return c.createElement(t.Month,{"data-animated-month":e.animate?"true":void 0,className:u[uq.Month],style:O?.[uq.Month],key:o,displayIndex:o,calendarMonth:n},c.createElement(t.MonthCaption,{"data-animated-caption":e.animate?"true":void 0,className:u[uq.MonthCaption],style:O?.[uq.MonthCaption],calendarMonth:n,displayIndex:o},f?.startsWith("dropdown")?c.createElement(t.DropdownNav,{className:u[uq.Dropdowns],style:O?.[uq.Dropdowns]},"dropdown"===f||"dropdown-months"===f?c.createElement(t.MonthsDropdown,{className:u[uq.MonthsDropdown],"aria-label":J(),classNames:u,components:t,disabled:!!e.disableNavigation,onChange:ev(n.date),options:i,style:O?.[uq.Dropdown],value:s.getMonth(n.date)}):c.createElement("span",null,P(n.date,s)),"dropdown"===f||"dropdown-years"===f?c.createElement(t.YearsDropdown,{className:u[uq.YearsDropdown],"aria-label":en(s.options),classNames:u,components:t,disabled:!!e.disableNavigation,onChange:em(n.date),options:a,style:O?.[uq.Dropdown],value:s.getYear(n.date)}):c.createElement("span",null,M(n.date,s)),c.createElement("span",{role:"status","aria-live":"polite",style:{border:0,clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"absolute",width:"1px",whiteSpace:"nowrap",wordWrap:"normal"}},j(n.date,s.options,s))):c.createElement(t.CaptionLabel,{className:u[uq.CaptionLabel],role:"status","aria-live":"polite"},j(n.date,s.options,s))),c.createElement(t.MonthGrid,{role:"grid","aria-multiselectable":"multiple"===p||"range"===p,"aria-label":Z(n.date,s.options,s)||void 0,className:u[uq.MonthGrid],style:O?.[uq.MonthGrid]},!e.hideWeekdays&&c.createElement(t.Weekdays,{"data-animated-weekdays":e.animate?"true":void 0,className:u[uq.Weekdays],style:O?.[uq.Weekdays]},w&&c.createElement(t.WeekNumberHeader,{"aria-label":er(s.options),className:u[uq.WeekNumberHeader],style:O?.[uq.WeekNumberHeader],scope:"col"},A()),eo.map((e,r)=>c.createElement(t.Weekday,{"aria-label":ee(e,s.options,s),className:u[uq.Weekday],key:r,style:O?.[uq.Weekday],scope:"col"},k(e,s.options,s)))),c.createElement(t.Weeks,{"data-animated-weeks":e.animate?"true":void 0,className:u[uq.Weeks],style:O?.[uq.Weeks]},n.weeks.map((r,n)=>c.createElement(t.Week,{className:u[uq.Week],key:r.weekNumber,style:O?.[uq.Week],week:r},w&&c.createElement(t.WeekNumber,{week:r,style:O?.[uq.WeekNumber],"aria-label":et(r.weekNumber,{locale:l}),className:u[uq.WeekNumber],scope:"row",role:"rowheader"},E(r.weekNumber)),r.days.map(r=>{let{date:n}=r,o=z(r);if(o[uY.focused]=!o.hidden&&!!Y?.isEqualTo(r),o[uH.selected]=$?.(n)||o.selected,tl(U)){let{from:e,to:t}=U;o[uH.range_start]=!!(e&&t&&s.isSameDay(n,e)),o[uH.range_end]=!!(e&&t&&s.isSameDay(n,t)),o[uH.range_middle]=ta(U,n,!0,s)}let i=function(e,t={},r={}){let n={...t?.[uq.Day]};return Object.entries(e).filter(([,e])=>!0===e).forEach(([e])=>{n={...n,...r?.[e]}}),n}(o,O,e.modifiersStyles),a=function(e,t,r={}){return Object.entries(e).filter(([,e])=>!0===e).reduce((e,[n])=>(r[n]?e.push(r[n]):t[uY[n]]?e.push(t[uY[n]]):t[uH[n]]&&e.push(t[uH[n]]),e),[t[uq.Day]])}(o,u,e.modifiersClassNames),l=ei||o.hidden?void 0:K(n,o,s.options,s);return c.createElement(t.Day,{key:`${s.format(n,"yyyy-MM-dd")}_${s.format(r.displayMonth,"yyyy-MM")}`,day:r,modifiers:o,className:a.join(" "),style:i,role:"gridcell","aria-selected":o.selected||void 0,"aria-label":l,"data-day":s.format(n,"yyyy-MM-dd"),"data-month":r.outside?s.format(n,"yyyy-MM"):void 0,"data-selected":o.selected||void 0,"data-disabled":o.disabled||void 0,"data-hidden":o.hidden||void 0,"data-outside":r.outside||void 0,"data-focused":o.focused||void 0,"data-today":o.today||void 0},!o.hidden&&ei?c.createElement(t.DayButton,{className:u[uq.DayButton],style:O?.[uq.DayButton],type:"button",day:r,modifiers:o,disabled:o.disabled||void 0,tabIndex:H(r)?0:-1,"aria-label":G(n,o,s.options,s),onClick:eu(r,o),onBlur:ep(r,o),onFocus:ef(r,o),onKeyDown:ed(r,o),onMouseEnter:eh(r,o),onMouseLeave:ey(r,o)},S(n,s.options,s)):!o.hidden&&S(r.date,s.options,s))}))))))})),e.footer&&c.createElement(t.Footer,{className:u[uq.Footer],style:O?.[uq.Footer],role:"status","aria-live":"polite"},e.footer)))}function tv(){let{previousMonth:e,nextMonth:t,goToMonth:r}=N();return(0,l.jsxs)("div",{className:"space-x-1 flex items-center",children:[(0,l.jsx)("button",{type:"button",onClick:()=>e&&r(e),className:"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100",children:(0,l.jsx)(T.A,{className:"h-4 w-4"})}),(0,l.jsx)("button",{type:"button",onClick:()=>t&&r(t),className:"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100",children:(0,l.jsx)(_.A,{className:"h-4 w-4"})})]})}function tm({className:e,classNames:t,showOutsideDays:r=!0,...n}){return(0,l.jsx)(ty,{showOutsideDays:r,className:(0,k.cn)("p-4 bg-neutral-100 rounded-2xl shadow-xl max-w-md mx-auto border border-gray-100",e),components:{Nav:tv},...n})}!function(e){e[e.Today=0]="Today",e[e.Selected=1]="Selected",e[e.LastFocused=2]="LastFocused",e[e.FocusedModifier=3]="FocusedModifier"}(uV||(uV={})),r(38399),tm.displayName="Calendar";var tb=r(70569),tg=r(98599),tx=r(11273),tw=r(51215);function tO(e){let t=function(e){let t=c.forwardRef((e,t)=>{var r;let n,o,{children:i,...a}=e,s=c.isValidElement(i)?(r=i,(o=(n=Object.getOwnPropertyDescriptor(r.props,"ref")?.get)&&"isReactWarning"in n&&n.isReactWarning)?r.ref:(o=(n=Object.getOwnPropertyDescriptor(r,"ref")?.get)&&"isReactWarning"in n&&n.isReactWarning)?r.props.ref:r.props.ref||r.ref):void 0,l=(0,tg.s)(s,t);if(c.isValidElement(i)){let e=function(e,t){let r={...t};for(let n in t){let o=e[n],i=t[n];/^on[A-Z]/.test(n)?o&&i?r[n]=(...e)=>{let t=i(...e);return o(...e),t}:o&&(r[n]=o):"style"===n?r[n]={...o,...i}:"className"===n&&(r[n]=[o,i].filter(Boolean).join(" "))}return{...e,...r}}(a,i.props);return i.type!==c.Fragment&&(e.ref=l),c.cloneElement(i,e)}return c.Children.count(i)>1?c.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=c.forwardRef((e,r)=>{let{children:n,...o}=e,i=c.Children.toArray(n),a=i.find(tS);if(a){let e=a.props.children,n=i.map(t=>t!==a?t:c.Children.count(e)>1?c.Children.only(null):c.isValidElement(e)?e.props.children:null);return(0,l.jsx)(t,{...o,ref:r,children:c.isValidElement(e)?c.cloneElement(e,void 0,n):null})}return(0,l.jsx)(t,{...o,ref:r,children:n})});return r.displayName=`${e}.Slot`,r}var tj=Symbol("radix.slottable");function tS(e){return c.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===tj}var tP=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=tO(`Primitive.${t}`),n=c.forwardRef((e,n)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(o?r:t,{...i,ref:n})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{}),tE=r(13495),tA=r(16309),tk="dismissableLayer.update",tM=c.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),tT=c.forwardRef((e,t)=>{let{disableOutsidePointerEvents:r=!1,onEscapeKeyDown:n,onPointerDownOutside:o,onFocusOutside:i,onInteractOutside:a,onDismiss:s,...u}=e,f=c.useContext(tM),[p,d]=c.useState(null),h=p?.ownerDocument??globalThis?.document,[,y]=c.useState({}),v=(0,tg.s)(t,e=>d(e)),m=Array.from(f.layers),[b]=[...f.layersWithOutsidePointerEventsDisabled].slice(-1),g=m.indexOf(b),x=p?m.indexOf(p):-1,w=f.layersWithOutsidePointerEventsDisabled.size>0,O=x>=g,j=function(e,t=globalThis?.document){let r=(0,tE.c)(e),n=c.useRef(!1),o=c.useRef(()=>{});return c.useEffect(()=>{let e=e=>{if(e.target&&!n.current){let n=function(){tC("dismissableLayer.pointerDownOutside",r,i,{discrete:!0})},i={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",o.current),o.current=n,t.addEventListener("click",o.current,{once:!0})):n()}else t.removeEventListener("click",o.current);n.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",e),t.removeEventListener("click",o.current)}},[t,r]),{onPointerDownCapture:()=>n.current=!0}}(e=>{let t=e.target,r=[...f.branches].some(e=>e.contains(t));O&&!r&&(o?.(e),a?.(e),e.defaultPrevented||s?.())},h),S=function(e,t=globalThis?.document){let r=(0,tE.c)(e),n=c.useRef(!1);return c.useEffect(()=>{let e=e=>{e.target&&!n.current&&tC("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,r]),{onFocusCapture:()=>n.current=!0,onBlurCapture:()=>n.current=!1}}(e=>{let t=e.target;![...f.branches].some(e=>e.contains(t))&&(i?.(e),a?.(e),e.defaultPrevented||s?.())},h);return(0,tA.U)(e=>{x===f.layers.size-1&&(n?.(e),!e.defaultPrevented&&s&&(e.preventDefault(),s()))},h),c.useEffect(()=>{if(p)return r&&(0===f.layersWithOutsidePointerEventsDisabled.size&&(uG=h.body.style.pointerEvents,h.body.style.pointerEvents="none"),f.layersWithOutsidePointerEventsDisabled.add(p)),f.layers.add(p),t_(),()=>{r&&1===f.layersWithOutsidePointerEventsDisabled.size&&(h.body.style.pointerEvents=uG)}},[p,h,r,f]),c.useEffect(()=>()=>{p&&(f.layers.delete(p),f.layersWithOutsidePointerEventsDisabled.delete(p),t_())},[p,f]),c.useEffect(()=>{let e=()=>y({});return document.addEventListener(tk,e),()=>document.removeEventListener(tk,e)},[]),(0,l.jsx)(tP.div,{...u,ref:v,style:{pointerEvents:w?O?"auto":"none":void 0,...e.style},onFocusCapture:(0,tb.m)(e.onFocusCapture,S.onFocusCapture),onBlurCapture:(0,tb.m)(e.onBlurCapture,S.onBlurCapture),onPointerDownCapture:(0,tb.m)(e.onPointerDownCapture,j.onPointerDownCapture)})});function t_(){let e=new CustomEvent(tk);document.dispatchEvent(e)}function tC(e,t,r,{discrete:n}){let o=r.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});if(t&&o.addEventListener(e,t,{once:!0}),n)o&&tw.flushSync(()=>o.dispatchEvent(i));else o.dispatchEvent(i)}tT.displayName="DismissableLayer",c.forwardRef((e,t)=>{let r=c.useContext(tM),n=c.useRef(null),o=(0,tg.s)(t,n);return c.useEffect(()=>{let e=n.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,l.jsx)(tP.div,{...e,ref:o})}).displayName="DismissableLayerBranch";var tN=r(1359),tD="focusScope.autoFocusOnMount",tI="focusScope.autoFocusOnUnmount",tR={bubbles:!1,cancelable:!0},tL=c.forwardRef((e,t)=>{let{loop:r=!1,trapped:n=!1,onMountAutoFocus:o,onUnmountAutoFocus:i,...a}=e,[s,u]=c.useState(null),f=(0,tE.c)(o),p=(0,tE.c)(i),d=c.useRef(null),h=(0,tg.s)(t,e=>u(e)),y=c.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;c.useEffect(()=>{if(n){let e=function(e){if(y.paused||!s)return;let t=e.target;s.contains(t)?d.current=t:tz(d.current,{select:!0})},t=function(e){if(y.paused||!s)return;let t=e.relatedTarget;null!==t&&(s.contains(t)||tz(d.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let r=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&tz(s)});return s&&r.observe(s,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),r.disconnect()}}},[n,s,y.paused]),c.useEffect(()=>{if(s){t$.add(y);let e=document.activeElement;if(!s.contains(e)){let t=new CustomEvent(tD,tR);s.addEventListener(tD,f),s.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let r=document.activeElement;for(let n of e)if(tz(n,{select:t}),document.activeElement!==r)return}(tB(s).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&tz(s))}return()=>{s.removeEventListener(tD,f),setTimeout(()=>{let t=new CustomEvent(tI,tR);s.addEventListener(tI,p),s.dispatchEvent(t),t.defaultPrevented||tz(e??document.body,{select:!0}),s.removeEventListener(tI,p),t$.remove(y)},0)}}},[s,f,p,y]);let v=c.useCallback(e=>{if(!r&&!n||y.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[n,i]=function(e){let t=tB(e);return[tF(t,e),tF(t.reverse(),e)]}(t);n&&i?e.shiftKey||o!==i?e.shiftKey&&o===n&&(e.preventDefault(),r&&tz(i,{select:!0})):(e.preventDefault(),r&&tz(n,{select:!0})):o===t&&e.preventDefault()}},[r,n,y.paused]);return(0,l.jsx)(tP.div,{tabIndex:-1,...a,ref:h,onKeyDown:v})});function tB(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function tF(e,t){for(let r of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(r,{upTo:t}))return r}function tz(e,{select:t=!1}={}){if(e&&e.focus){var r;let n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&(r=e)instanceof HTMLInputElement&&"select"in r&&t&&e.select()}}tL.displayName="FocusScope";var t$=function(){let e=[];return{add(t){let r=e[0];t!==r&&r?.pause(),(e=tW(e,t)).unshift(t)},remove(t){e=tW(e,t),e[0]?.resume()}}}();function tW(e,t){let r=[...e],n=r.indexOf(t);return -1!==n&&r.splice(n,1),r}var tU=r(96963),tq=r(4503),tY=r(25605),tH=c.forwardRef((e,t)=>{let{children:r,width:n=10,height:o=5,...i}=e;return(0,l.jsx)(tP.svg,{...i,ref:t,width:n,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?r:(0,l.jsx)("polygon",{points:"0,0 30,0 15,10"})})});tH.displayName="Arrow";var tX=r(66156),tV=r(18853),tG="Popper",[tK,tZ]=(0,tx.A)(tG),[tJ,tQ]=tK(tG),t0=e=>{let{__scopePopper:t,children:r}=e,[n,o]=c.useState(null);return(0,l.jsx)(tJ,{scope:t,anchor:n,onAnchorChange:o,children:r})};t0.displayName=tG;var t1="PopperAnchor",t2=c.forwardRef((e,t)=>{let{__scopePopper:r,virtualRef:n,...o}=e,i=tQ(t1,r),a=c.useRef(null),s=(0,tg.s)(t,a);return c.useEffect(()=>{i.onAnchorChange(n?.current||a.current)}),n?null:(0,l.jsx)(tP.div,{...o,ref:s})});t2.displayName=t1;var t5="PopperContent",[t3,t4]=tK(t5),t8=c.forwardRef((e,t)=>{let{__scopePopper:r,side:n="bottom",sideOffset:o=0,align:i="center",alignOffset:a=0,arrowPadding:s=0,avoidCollisions:u=!0,collisionBoundary:f=[],collisionPadding:p=0,sticky:d="partial",hideWhenDetached:h=!1,updatePositionStrategy:y="optimized",onPlaced:v,...m}=e,b=tQ(t5,r),[g,x]=c.useState(null),w=(0,tg.s)(t,e=>x(e)),[O,j]=c.useState(null),S=(0,tV.X)(O),P=S?.width??0,E=S?.height??0,A="number"==typeof p?p:{top:0,right:0,bottom:0,left:0,...p},k=Array.isArray(f)?f:[f],M=k.length>0,T={padding:A,boundary:k.filter(re),altBoundary:M},{refs:_,floatingStyles:C,placement:N,isPositioned:D,middlewareData:I}=(0,tq.we)({strategy:"fixed",placement:n+("center"!==i?"-"+i:""),whileElementsMounted:(...e)=>(0,tY.ll)(...e,{animationFrame:"always"===y}),elements:{reference:b.anchor},middleware:[(0,tq.cY)({mainAxis:o+E,alignmentAxis:a}),u&&(0,tq.BN)({mainAxis:!0,crossAxis:!1,limiter:"partial"===d?(0,tq.ER)():void 0,...T}),u&&(0,tq.UU)({...T}),(0,tq.Ej)({...T,apply:({elements:e,rects:t,availableWidth:r,availableHeight:n})=>{let{width:o,height:i}=t.reference,a=e.floating.style;a.setProperty("--radix-popper-available-width",`${r}px`),a.setProperty("--radix-popper-available-height",`${n}px`),a.setProperty("--radix-popper-anchor-width",`${o}px`),a.setProperty("--radix-popper-anchor-height",`${i}px`)}}),O&&(0,tq.UE)({element:O,padding:s}),rt({arrowWidth:P,arrowHeight:E}),h&&(0,tq.jD)({strategy:"referenceHidden",...T})]}),[R,L]=rr(N),B=(0,tE.c)(v);(0,tX.N)(()=>{D&&B?.()},[D,B]);let F=I.arrow?.x,z=I.arrow?.y,$=I.arrow?.centerOffset!==0,[W,U]=c.useState();return(0,tX.N)(()=>{g&&U(window.getComputedStyle(g).zIndex)},[g]),(0,l.jsx)("div",{ref:_.setFloating,"data-radix-popper-content-wrapper":"",style:{...C,transform:D?C.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:W,"--radix-popper-transform-origin":[I.transformOrigin?.x,I.transformOrigin?.y].join(" "),...I.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,l.jsx)(t3,{scope:r,placedSide:R,onArrowChange:j,arrowX:F,arrowY:z,shouldHideArrow:$,children:(0,l.jsx)(tP.div,{"data-side":R,"data-align":L,...m,ref:w,style:{...m.style,animation:D?void 0:"none"}})})})});t8.displayName=t5;var t6="PopperArrow",t7={top:"bottom",right:"left",bottom:"top",left:"right"},t9=c.forwardRef(function(e,t){let{__scopePopper:r,...n}=e,o=t4(t6,r),i=t7[o.placedSide];return(0,l.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,l.jsx)(tH,{...n,ref:t,style:{...n.style,display:"block"}})})});function re(e){return null!==e}t9.displayName=t6;var rt=e=>({name:"transformOrigin",options:e,fn(t){let{placement:r,rects:n,middlewareData:o}=t,i=o.arrow?.centerOffset!==0,a=i?0:e.arrowWidth,s=i?0:e.arrowHeight,[l,c]=rr(r),u={start:"0%",center:"50%",end:"100%"}[c],f=(o.arrow?.x??0)+a/2,p=(o.arrow?.y??0)+s/2,d="",h="";return"bottom"===l?(d=i?u:`${f}px`,h=`${-s}px`):"top"===l?(d=i?u:`${f}px`,h=`${n.floating.height+s}px`):"right"===l?(d=`${-s}px`,h=i?u:`${p}px`):"left"===l&&(d=`${n.floating.width+s}px`,h=i?u:`${p}px`),{data:{x:d,y:h}}}});function rr(e){let[t,r="center"]=e.split("-");return[t,r]}var rn=c.forwardRef((e,t)=>{let{container:r,...n}=e,[o,i]=c.useState(!1);(0,tX.N)(()=>i(!0),[]);let a=r||o&&globalThis?.document?.body;return a?tw.createPortal((0,l.jsx)(tP.div,{...n,ref:t}),a):null});rn.displayName="Portal";var ro=r(46059),ri=r(65551),ra=r(63376),rs=r(11490),rl="Popover",[rc,ru]=(0,tx.A)(rl,[tZ]),rf=tZ(),[rp,rd]=rc(rl),rh=e=>{let{__scopePopover:t,children:r,open:n,defaultOpen:o,onOpenChange:i,modal:a=!1}=e,s=rf(t),u=c.useRef(null),[f,p]=c.useState(!1),[d,h]=(0,ri.i)({prop:n,defaultProp:o??!1,onChange:i,caller:rl});return(0,l.jsx)(t0,{...s,children:(0,l.jsx)(rp,{scope:t,contentId:(0,tU.B)(),triggerRef:u,open:d,onOpenChange:h,onOpenToggle:c.useCallback(()=>h(e=>!e),[h]),hasCustomAnchor:f,onCustomAnchorAdd:c.useCallback(()=>p(!0),[]),onCustomAnchorRemove:c.useCallback(()=>p(!1),[]),modal:a,children:r})})};rh.displayName=rl;var ry="PopoverAnchor";c.forwardRef((e,t)=>{let{__scopePopover:r,...n}=e,o=rd(ry,r),i=rf(r),{onCustomAnchorAdd:a,onCustomAnchorRemove:s}=o;return c.useEffect(()=>(a(),()=>s()),[a,s]),(0,l.jsx)(t2,{...i,...n,ref:t})}).displayName=ry;var rv="PopoverTrigger",rm=c.forwardRef((e,t)=>{let{__scopePopover:r,...n}=e,o=rd(rv,r),i=rf(r),a=(0,tg.s)(t,o.triggerRef),s=(0,l.jsx)(tP.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":rM(o.open),...n,ref:a,onClick:(0,tb.m)(e.onClick,o.onOpenToggle)});return o.hasCustomAnchor?s:(0,l.jsx)(t2,{asChild:!0,...i,children:s})});rm.displayName=rv;var rb="PopoverPortal",[rg,rx]=rc(rb,{forceMount:void 0}),rw=e=>{let{__scopePopover:t,forceMount:r,children:n,container:o}=e,i=rd(rb,t);return(0,l.jsx)(rg,{scope:t,forceMount:r,children:(0,l.jsx)(ro.C,{present:r||i.open,children:(0,l.jsx)(rn,{asChild:!0,container:o,children:n})})})};rw.displayName=rb;var rO="PopoverContent",rj=c.forwardRef((e,t)=>{let r=rx(rO,e.__scopePopover),{forceMount:n=r.forceMount,...o}=e,i=rd(rO,e.__scopePopover);return(0,l.jsx)(ro.C,{present:n||i.open,children:i.modal?(0,l.jsx)(rP,{...o,ref:t}):(0,l.jsx)(rE,{...o,ref:t})})});rj.displayName=rO;var rS=tO("PopoverContent.RemoveScroll"),rP=c.forwardRef((e,t)=>{let r=rd(rO,e.__scopePopover),n=c.useRef(null),o=(0,tg.s)(t,n),i=c.useRef(!1);return c.useEffect(()=>{let e=n.current;if(e)return(0,ra.Eq)(e)},[]),(0,l.jsx)(rs.A,{as:rS,allowPinchZoom:!0,children:(0,l.jsx)(rA,{...e,ref:o,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,tb.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),i.current||r.triggerRef.current?.focus()}),onPointerDownOutside:(0,tb.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;i.current=2===t.button||r},{checkForDefaultPrevented:!1}),onFocusOutside:(0,tb.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1})})})}),rE=c.forwardRef((e,t)=>{let r=rd(rO,e.__scopePopover),n=c.useRef(!1),o=c.useRef(!1);return(0,l.jsx)(rA,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(n.current||r.triggerRef.current?.focus(),t.preventDefault()),n.current=!1,o.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(n.current=!0,"pointerdown"===t.detail.originalEvent.type&&(o.current=!0));let i=t.target;r.triggerRef.current?.contains(i)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&o.current&&t.preventDefault()}})}),rA=c.forwardRef((e,t)=>{let{__scopePopover:r,trapFocus:n,onOpenAutoFocus:o,onCloseAutoFocus:i,disableOutsidePointerEvents:a,onEscapeKeyDown:s,onPointerDownOutside:c,onFocusOutside:u,onInteractOutside:f,...p}=e,d=rd(rO,r),h=rf(r);return(0,tN.Oh)(),(0,l.jsx)(tL,{asChild:!0,loop:!0,trapped:n,onMountAutoFocus:o,onUnmountAutoFocus:i,children:(0,l.jsx)(tT,{asChild:!0,disableOutsidePointerEvents:a,onInteractOutside:f,onEscapeKeyDown:s,onPointerDownOutside:c,onFocusOutside:u,onDismiss:()=>d.onOpenChange(!1),children:(0,l.jsx)(t8,{"data-state":rM(d.open),role:"dialog",id:d.contentId,...h,...p,ref:t,style:{...p.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),rk="PopoverClose";function rM(e){return e?"open":"closed"}c.forwardRef((e,t)=>{let{__scopePopover:r,...n}=e,o=rd(rk,r);return(0,l.jsx)(tP.button,{type:"button",...n,ref:t,onClick:(0,tb.m)(e.onClick,()=>o.onOpenChange(!1))})}).displayName=rk,c.forwardRef((e,t)=>{let{__scopePopover:r,...n}=e,o=rf(r);return(0,l.jsx)(t9,{...o,...n,ref:t})}).displayName="PopoverArrow";let rT=c.forwardRef(({className:e,align:t="center",sideOffset:r=4,...n},o)=>(0,l.jsx)(rw,{children:(0,l.jsx)(rj,{ref:o,align:t,sideOffset:r,className:(0,k.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...n})}));rT.displayName=rj.displayName;var r_=r(77618);function rC({value:e,onChange:t,className:r}){let n=(0,r_.c3)();return(0,l.jsx)("div",{className:(0,k.cn)("grid gap-2",r),children:(0,l.jsxs)(rh,{children:[(0,l.jsx)(rm,{asChild:!0,children:(0,l.jsxs)(M.$,{id:"date",variant:"outline",className:(0,k.cn)("w-[300px] justify-start text-left font-normal",!e&&"text-muted-foreground"),children:[(0,l.jsx)(E.A,{className:"mr-2 h-4 w-4"}),e?.from?e.to?(0,l.jsxs)(l.Fragment,{children:[(0,A.GP)(e.from,"LLL dd, y")," -"," ",(0,A.GP)(e.to,"LLL dd, y")]}):(0,A.GP)(e.from,"LLL dd, y"):(0,l.jsx)("span",{children:n("pickDateRange")})]})}),(0,l.jsx)(rT,{className:"w-auto p-0",align:"start",children:(0,l.jsx)(tm,{initialFocus:!0,mode:"range",defaultMonth:e?.from,selected:e,onSelect:t,numberOfMonths:2})})]})})}var rN=r(16189),rD=r(12810),rI=r(49384),rR=r(45603),rL=r.n(rR),rB=r(63866),rF=r.n(rB),rz=r(77822),r$=r.n(rz),rW=r(40491),rU=r.n(rW),rq=r(93490),rY=r.n(rq),rH=function(e){return 0===e?0:e>0?1:-1},rX=function(e){return rF()(e)&&e.indexOf("%")===e.length-1},rV=function(e){return rY()(e)&&!r$()(e)},rG=function(e){return rV(e)||rF()(e)},rK=0,rZ=function(e){var t=++rK;return"".concat(e||"").concat(t)},rJ=function(e,t){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,o=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!rV(e)&&!rF()(e))return n;if(rX(e)){var i=e.indexOf("%");r=t*parseFloat(e.slice(0,i))/100}else r=+e;return r$()(r)&&(r=n),o&&r>t&&(r=t),r},rQ=function(e){if(!e)return null;var t=Object.keys(e);return t&&t.length?e[t[0]]:null},r0=function(e){if(!Array.isArray(e))return!1;for(var t=e.length,r={},n=0;n<t;n++)if(r[e[n]])return!0;else r[e[n]]=!0;return!1},r1=function(e,t){return rV(e)&&rV(t)?function(r){return e+r*(t-e)}:function(){return t}};function r2(e,t,r){return e&&e.length?e.find(function(e){return e&&("function"==typeof t?t(e):rU()(e,t))===r}):null}var r5=function(e,t){return rV(e)&&rV(t)?e-t:rF()(e)&&rF()(t)?e.localeCompare(t):e instanceof Date&&t instanceof Date?e.getTime()-t.getTime():String(e).localeCompare(String(t))},r3=function(e,t){for(var r=arguments.length,n=Array(r>2?r-2:0),o=2;o<r;o++)n[o-2]=arguments[o]},r4=r(37456),r8=r.n(r4),r6=r(5231),r7=r.n(r6),r9=r(55048),ne=r.n(r9),nt=r(29632);function nr(e,t){for(var r in e)if(({}).hasOwnProperty.call(e,r)&&(!({}).hasOwnProperty.call(t,r)||e[r]!==t[r]))return!1;for(var n in t)if(({}).hasOwnProperty.call(t,n)&&!({}).hasOwnProperty.call(e,n))return!1;return!0}function nn(e){return(nn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var no=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],ni=["points","pathLength"],na={svg:["viewBox","children"],polygon:ni,polyline:ni},ns=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],nl=function(e,t){if(!e||"function"==typeof e||"boolean"==typeof e)return null;var r=e;if((0,c.isValidElement)(e)&&(r=e.props),!ne()(r))return null;var n={};return Object.keys(r).forEach(function(e){ns.includes(e)&&(n[e]=t||function(t){return r[e](r,t)})}),n},nc=function(e,t,r){if(!ne()(e)||"object"!==nn(e))return null;var n=null;return Object.keys(e).forEach(function(o){var i=e[o];ns.includes(o)&&"function"==typeof i&&(n||(n={}),n[o]=function(e){return i(t,r,e),null})}),n},nu=["children"],nf=["children"];function np(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}var nd={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart",contextmenu:"onContextMenu",dblclick:"onDoubleClick"},nh=function(e){return"string"==typeof e?e:e?e.displayName||e.name||"Component":""},ny=null,nv=null,nm=function e(t){if(t===ny&&Array.isArray(nv))return nv;var r=[];return c.Children.forEach(t,function(t){r8()(t)||((0,nt.isFragment)(t)?r=r.concat(e(t.props.children)):r.push(t))}),nv=r,ny=t,r};function nb(e,t){var r=[],n=[];return n=Array.isArray(t)?t.map(function(e){return nh(e)}):[nh(t)],nm(e).forEach(function(e){var t=rU()(e,"type.displayName")||rU()(e,"type.name");-1!==n.indexOf(t)&&r.push(e)}),r}function ng(e,t){var r=nb(e,t);return r&&r[0]}var nx=function(e){if(!e||!e.props)return!1;var t=e.props,r=t.width,n=t.height;return!!rV(r)&&!(r<=0)&&!!rV(n)&&!(n<=0)},nw=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],nO=function(e,t,r,n){var o,i=null!=(o=null==na?void 0:na[n])?o:[];return t.startsWith("data-")||!r7()(e)&&(n&&i.includes(t)||no.includes(t))||r&&ns.includes(t)},nj=function(e,t,r){if(!e||"function"==typeof e||"boolean"==typeof e)return null;var n=e;if((0,c.isValidElement)(e)&&(n=e.props),!ne()(n))return null;var o={};return Object.keys(n).forEach(function(e){var i;nO(null==(i=n)?void 0:i[e],e,t,r)&&(o[e]=n[e])}),o},nS=function e(t,r){if(t===r)return!0;var n=c.Children.count(t);if(n!==c.Children.count(r))return!1;if(0===n)return!0;if(1===n)return nP(Array.isArray(t)?t[0]:t,Array.isArray(r)?r[0]:r);for(var o=0;o<n;o++){var i=t[o],a=r[o];if(Array.isArray(i)||Array.isArray(a)){if(!e(i,a))return!1}else if(!nP(i,a))return!1}return!0},nP=function(e,t){if(r8()(e)&&r8()(t))return!0;if(!r8()(e)&&!r8()(t)){var r=e.props||{},n=r.children,o=np(r,nu),i=t.props||{},a=i.children,s=np(i,nf);if(n&&a)return nr(o,s)&&nS(n,a);if(!n&&!a)return nr(o,s)}return!1},nE=function(e,t){var r=[],n={};return nm(e).forEach(function(e,o){var i;if((i=e)&&i.type&&rF()(i.type)&&nw.indexOf(i.type)>=0)r.push(e);else if(e){var a=nh(e.type),s=t[a]||{},l=s.handler,c=s.once;if(l&&(!c||!n[a])){var u=l(e,a,o);r.push(u),n[a]=!0}}}),r},nA=function(e){var t=e&&e.type;return t&&nd[t]?nd[t]:null};function nk(e){return(nk="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function nM(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function nT(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?nM(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=nk(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=nk(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==nk(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nM(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function n_(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var nC=(0,c.forwardRef)(function(e,t){var r,n=e.aspect,o=e.initialDimension,i=void 0===o?{width:-1,height:-1}:o,a=e.width,s=void 0===a?"100%":a,l=e.height,f=void 0===l?"100%":l,p=e.minWidth,d=void 0===p?0:p,h=e.minHeight,y=e.maxHeight,v=e.children,m=e.debounce,b=void 0===m?0:m,g=e.id,x=e.className,w=e.onResize,O=e.style,j=(0,c.useRef)(null),S=(0,c.useRef)();S.current=w,(0,c.useImperativeHandle)(t,function(){return Object.defineProperty(j.current,"current",{get:function(){return console.warn("The usage of ref.current.current is deprecated and will no longer be supported."),j.current},configurable:!0})});var P=function(e){if(Array.isArray(e))return e}(r=(0,c.useState)({containerWidth:i.width,containerHeight:i.height}))||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,s=[],l=!0,c=!1;try{i=(r=r.call(e)).next,!1;for(;!(l=(n=i.call(r)).done)&&(s.push(n.value),s.length!==t);l=!0);}catch(e){c=!0,o=e}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return s}}(r,2)||function(e,t){if(e){if("string"==typeof e)return n_(e,2);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return n_(e,t)}}(r,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),E=P[0],A=P[1],k=(0,c.useCallback)(function(e,t){A(function(r){var n=Math.round(e),o=Math.round(t);return r.containerWidth===n&&r.containerHeight===o?r:{containerWidth:n,containerHeight:o}})},[]);(0,c.useEffect)(function(){var e=function(e){var t,r=e[0].contentRect,n=r.width,o=r.height;k(n,o),null==(t=S.current)||t.call(S,n,o)};b>0&&(e=rL()(e,b,{trailing:!0,leading:!1}));var t=new ResizeObserver(e),r=j.current.getBoundingClientRect();return k(r.width,r.height),t.observe(j.current),function(){t.disconnect()}},[k,b]);var M=(0,c.useMemo)(function(){var e=E.containerWidth,t=E.containerHeight;if(e<0||t<0)return null;r3(rX(s)||rX(f),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",s,f),r3(!n||n>0,"The aspect(%s) must be greater than zero.",n);var r=rX(s)?e:s,o=rX(f)?t:f;n&&n>0&&(r?o=r/n:o&&(r=o*n),y&&o>y&&(o=y)),r3(r>0||o>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",r,o,s,f,d,h,n);var i=!Array.isArray(v)&&nh(v.type).endsWith("Chart");return u().Children.map(v,function(e){return u().isValidElement(e)?(0,c.cloneElement)(e,nT({width:r,height:o},i?{style:nT({height:"100%",width:"100%",maxHeight:o,maxWidth:r},e.props.style)}:{})):e})},[n,v,f,y,h,d,E,s]);return u().createElement("div",{id:g?"".concat(g):void 0,className:(0,rI.A)("recharts-responsive-container",x),style:nT(nT({},void 0===O?{}:O),{},{width:s,height:f,minWidth:d,minHeight:h,maxHeight:y}),ref:j},M)}),nN=r(34990),nD=r.n(nN),nI=r(85938),nR=r.n(nI);function nL(e,t){if(!e)throw Error("Invariant failed")}var nB=["children","width","height","viewBox","className","style","title","desc"];function nF(){return(nF=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function nz(e){var t=e.children,r=e.width,n=e.height,o=e.viewBox,i=e.className,a=e.style,s=e.title,l=e.desc,c=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,nB),f=o||{width:r,height:n,x:0,y:0},p=(0,rI.A)("recharts-surface",i);return u().createElement("svg",nF({},nj(c,!0,"svg"),{className:p,width:r,height:n,style:a,viewBox:"".concat(f.x," ").concat(f.y," ").concat(f.width," ").concat(f.height)}),u().createElement("title",null,s),u().createElement("desc",null,l),t)}var n$=["children","className"];function nW(){return(nW=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var nU=u().forwardRef(function(e,t){var r=e.children,n=e.className,o=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,n$),i=(0,rI.A)("recharts-layer",n);return u().createElement("g",nW({className:i},nj(o,!0),{ref:t}),r)});function nq(e){return(nq="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function nY(){return(nY=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function nH(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function nX(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function nV(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?nX(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=nq(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=nq(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==nq(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nX(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function nG(e){return Array.isArray(e)&&rG(e[0])&&rG(e[1])?e.join(" ~ "):e}var nK=function(e){var t=e.separator,r=void 0===t?" : ":t,n=e.contentStyle,o=e.itemStyle,i=void 0===o?{}:o,a=e.labelStyle,s=e.payload,l=e.formatter,c=e.itemSorter,f=e.wrapperClassName,p=e.labelClassName,d=e.label,h=e.labelFormatter,y=e.accessibilityLayer,v=nV({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},void 0===n?{}:n),m=nV({margin:0},void 0===a?{}:a),b=!r8()(d),g=b?d:"",x=(0,rI.A)("recharts-default-tooltip",f),w=(0,rI.A)("recharts-tooltip-label",p);return b&&h&&null!=s&&(g=h(d,s)),u().createElement("div",nY({className:x,style:v},void 0!==y&&y?{role:"status","aria-live":"assertive"}:{}),u().createElement("p",{className:w,style:m},u().isValidElement(g)?g:"".concat(g)),function(){if(s&&s.length){var e=(c?nR()(s,c):s).map(function(e,t){if("none"===e.type)return null;var n=nV({display:"block",paddingTop:4,paddingBottom:4,color:e.color||"#000"},i),o=e.formatter||l||nG,a=e.value,c=e.name,f=a,p=c;if(o&&null!=f&&null!=p){var d=o(a,c,e,t,s);if(Array.isArray(d)){var h=function(e){if(Array.isArray(e))return e}(d)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,s=[],l=!0,c=!1;try{i=(r=r.call(e)).next,!1;for(;!(l=(n=i.call(r)).done)&&(s.push(n.value),s.length!==t);l=!0);}catch(e){c=!0,o=e}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return s}}(d,2)||function(e,t){if(e){if("string"==typeof e)return nH(e,2);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nH(e,t)}}(d,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();f=h[0],p=h[1]}else f=d}return u().createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(t),style:n},rG(p)?u().createElement("span",{className:"recharts-tooltip-item-name"},p):null,rG(p)?u().createElement("span",{className:"recharts-tooltip-item-separator"},r):null,u().createElement("span",{className:"recharts-tooltip-item-value"},f),u().createElement("span",{className:"recharts-tooltip-item-unit"},e.unit||""))});return u().createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},e)}return null}())};function nZ(e){return(nZ="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function nJ(e,t,r){var n;return(n=function(e,t){if("object"!=nZ(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=nZ(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"==nZ(n)?n:n+"")in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var nQ="recharts-tooltip-wrapper",n0={visibility:"hidden"};function n1(e){var t=e.allowEscapeViewBox,r=e.coordinate,n=e.key,o=e.offsetTopLeft,i=e.position,a=e.reverseDirection,s=e.tooltipDimension,l=e.viewBox,c=e.viewBoxDimension;if(i&&rV(i[n]))return i[n];var u=r[n]-s-o,f=r[n]+o;return t[n]?a[n]?u:f:a[n]?u<l[n]?Math.max(f,l[n]):Math.max(u,l[n]):f+s>l[n]+c?Math.max(u,l[n]):Math.max(f,l[n])}function n2(e){return(n2="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function n5(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function n3(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n5(Object(r),!0).forEach(function(t){n7(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n5(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function n4(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(n4=function(){return!!e})()}function n8(e){return(n8=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function n6(e,t){return(n6=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function n7(e,t,r){return(t=n9(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function n9(e){var t=function(e,t){if("object"!=n2(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=n2(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==n2(t)?t:t+""}var oe=function(e){var t;function r(){var e,t,n;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");for(var o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return t=r,n=[].concat(i),t=n8(t),n7(e=function(e,t){if(t&&("object"===n2(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");var r=e;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,n4()?Reflect.construct(t,n||[],n8(this).constructor):t.apply(this,n)),"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),n7(e,"handleKeyDown",function(t){if("Escape"===t.key){var r,n,o,i;e.setState({dismissed:!0,dismissedAtCoordinate:{x:null!=(r=null==(n=e.props.coordinate)?void 0:n.x)?r:0,y:null!=(o=null==(i=e.props.coordinate)?void 0:i.y)?o:0}})}}),e}if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(e&&e.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),e&&n6(r,e),t=[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var e=this.wrapperNode.getBoundingClientRect();(Math.abs(e.width-this.state.lastBoundingBox.width)>1||Math.abs(e.height-this.state.lastBoundingBox.height)>1)&&this.setState({lastBoundingBox:{width:e.width,height:e.height}})}else(-1!==this.state.lastBoundingBox.width||-1!==this.state.lastBoundingBox.height)&&this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var e,t;this.props.active&&this.updateBBox(),this.state.dismissed&&((null==(e=this.props.coordinate)?void 0:e.x)!==this.state.dismissedAtCoordinate.x||(null==(t=this.props.coordinate)?void 0:t.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}},{key:"render",value:function(){var e,t,r,n,o,i,a,s,l,c,f,p,d,h,y,v,m,b,g,x=this,w=this.props,O=w.active,j=w.allowEscapeViewBox,S=w.animationDuration,P=w.animationEasing,E=w.children,A=w.coordinate,k=w.hasPayload,M=w.isAnimationActive,T=w.offset,_=w.position,C=w.reverseDirection,N=w.useTranslate3d,D=w.viewBox,I=w.wrapperStyle,R=(p=(e={allowEscapeViewBox:j,coordinate:A,offsetTopLeft:T,position:_,reverseDirection:C,tooltipBox:this.state.lastBoundingBox,useTranslate3d:N,viewBox:D}).allowEscapeViewBox,d=e.coordinate,h=e.offsetTopLeft,y=e.position,v=e.reverseDirection,m=e.tooltipBox,b=e.useTranslate3d,g=e.viewBox,m.height>0&&m.width>0&&d?(r=(t={translateX:c=n1({allowEscapeViewBox:p,coordinate:d,key:"x",offsetTopLeft:h,position:y,reverseDirection:v,tooltipDimension:m.width,viewBox:g,viewBoxDimension:g.width}),translateY:f=n1({allowEscapeViewBox:p,coordinate:d,key:"y",offsetTopLeft:h,position:y,reverseDirection:v,tooltipDimension:m.height,viewBox:g,viewBoxDimension:g.height}),useTranslate3d:b}).translateX,n=t.translateY,l={transform:t.useTranslate3d?"translate3d(".concat(r,"px, ").concat(n,"px, 0)"):"translate(".concat(r,"px, ").concat(n,"px)")}):l=n0,{cssProperties:l,cssClasses:(i=(o={translateX:c,translateY:f,coordinate:d}).coordinate,a=o.translateX,s=o.translateY,(0,rI.A)(nQ,nJ(nJ(nJ(nJ({},"".concat(nQ,"-right"),rV(a)&&i&&rV(i.x)&&a>=i.x),"".concat(nQ,"-left"),rV(a)&&i&&rV(i.x)&&a<i.x),"".concat(nQ,"-bottom"),rV(s)&&i&&rV(i.y)&&s>=i.y),"".concat(nQ,"-top"),rV(s)&&i&&rV(i.y)&&s<i.y)))}),L=R.cssClasses,B=R.cssProperties,F=n3(n3({transition:M&&O?"transform ".concat(S,"ms ").concat(P):void 0},B),{},{pointerEvents:"none",visibility:!this.state.dismissed&&O&&k?"visible":"hidden",position:"absolute",top:0,left:0},I);return u().createElement("div",{tabIndex:-1,className:L,style:F,ref:function(e){x.wrapperNode=e}},E)}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n9(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(c.PureComponent),ot={isSsr:!0,get:function(e){return ot[e]},set:function(e,t){if("string"==typeof e)ot[e]=t;else{var r=Object.keys(e);r&&r.length&&r.forEach(function(t){ot[t]=e[t]})}}},or=r(36315),on=r.n(or);function oo(e,t,r){return!0===t?on()(e,r):r7()(t)?on()(e,t):e}function oi(e){return(oi="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function oa(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function os(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?oa(Object(r),!0).forEach(function(t){of(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):oa(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function ol(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(ol=function(){return!!e})()}function oc(e){return(oc=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function ou(e,t){return(ou=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function of(e,t,r){return(t=op(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function op(e){var t=function(e,t){if("object"!=oi(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=oi(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==oi(t)?t:t+""}function od(e){return e.dataKey}var oh=function(e){var t;function r(){var e,t;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return e=r,t=arguments,e=oc(e),function(e,t){if(t&&("object"===oi(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");var r=e;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,ol()?Reflect.construct(e,t||[],oc(this).constructor):e.apply(this,t))}if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(e&&e.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),e&&ou(r,e),t=[{key:"render",value:function(){var e,t=this,r=this.props,n=r.active,o=r.allowEscapeViewBox,i=r.animationDuration,a=r.animationEasing,s=r.content,l=r.coordinate,c=r.filterNull,f=r.isAnimationActive,p=r.offset,d=r.payload,h=r.payloadUniqBy,y=r.position,v=r.reverseDirection,m=r.useTranslate3d,b=r.viewBox,g=r.wrapperStyle,x=null!=d?d:[];c&&x.length&&(x=oo(d.filter(function(e){return null!=e.value&&(!0!==e.hide||t.props.includeHidden)}),h,od));var w=x.length>0;return u().createElement(oe,{allowEscapeViewBox:o,animationDuration:i,animationEasing:a,isAnimationActive:f,active:n,coordinate:l,hasPayload:w,offset:p,position:y,reverseDirection:v,useTranslate3d:m,viewBox:b,wrapperStyle:g},(e=os(os({},this.props),{},{payload:x}),u().isValidElement(s)?u().cloneElement(s,e):"function"==typeof s?u().createElement(s,e):u().createElement(nK,e)))}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,op(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(c.PureComponent);of(oh,"displayName","Tooltip"),of(oh,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!ot.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}});var oy=r(69433),ov=r.n(oy);let om=Math.cos,ob=Math.sin,og=Math.sqrt,ox=Math.PI,ow=2*ox,oO={draw(e,t){let r=og(t/ox);e.moveTo(r,0),e.arc(0,0,r,0,ow)}},oj=og(1/3),oS=2*oj,oP=ob(ox/10)/ob(7*ox/10),oE=ob(ow/10)*oP,oA=-om(ow/10)*oP,ok=og(3),oM=og(3)/2,oT=1/og(12),o_=(oT/2+1)*3;function oC(e){return function(){return e}}let oN=Math.PI,oD=2*oN,oI=oD-1e-6;function oR(e){this._+=e[0];for(let t=1,r=e.length;t<r;++t)this._+=arguments[t]+e[t]}class oL{constructor(e){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==e?oR:function(e){let t=Math.floor(e);if(!(t>=0))throw Error(`invalid digits: ${e}`);if(t>15)return oR;let r=10**t;return function(e){this._+=e[0];for(let t=1,n=e.length;t<n;++t)this._+=Math.round(arguments[t]*r)/r+e[t]}}(e)}moveTo(e,t){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(e,t){this._append`L${this._x1=+e},${this._y1=+t}`}quadraticCurveTo(e,t,r,n){this._append`Q${+e},${+t},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(e,t,r,n,o,i){this._append`C${+e},${+t},${+r},${+n},${this._x1=+o},${this._y1=+i}`}arcTo(e,t,r,n,o){if(e*=1,t*=1,r*=1,n*=1,(o*=1)<0)throw Error(`negative radius: ${o}`);let i=this._x1,a=this._y1,s=r-e,l=n-t,c=i-e,u=a-t,f=c*c+u*u;if(null===this._x1)this._append`M${this._x1=e},${this._y1=t}`;else if(f>1e-6)if(Math.abs(u*s-l*c)>1e-6&&o){let p=r-i,d=n-a,h=s*s+l*l,y=Math.sqrt(h),v=Math.sqrt(f),m=o*Math.tan((oN-Math.acos((h+f-(p*p+d*d))/(2*y*v)))/2),b=m/v,g=m/y;Math.abs(b-1)>1e-6&&this._append`L${e+b*c},${t+b*u}`,this._append`A${o},${o},0,0,${+(u*p>c*d)},${this._x1=e+g*s},${this._y1=t+g*l}`}else this._append`L${this._x1=e},${this._y1=t}`}arc(e,t,r,n,o,i){if(e*=1,t*=1,r*=1,i=!!i,r<0)throw Error(`negative radius: ${r}`);let a=r*Math.cos(n),s=r*Math.sin(n),l=e+a,c=t+s,u=1^i,f=i?n-o:o-n;null===this._x1?this._append`M${l},${c}`:(Math.abs(this._x1-l)>1e-6||Math.abs(this._y1-c)>1e-6)&&this._append`L${l},${c}`,r&&(f<0&&(f=f%oD+oD),f>oI?this._append`A${r},${r},0,1,${u},${e-a},${t-s}A${r},${r},0,1,${u},${this._x1=l},${this._y1=c}`:f>1e-6&&this._append`A${r},${r},0,${+(f>=oN)},${u},${this._x1=e+r*Math.cos(o)},${this._y1=t+r*Math.sin(o)}`)}rect(e,t,r,n){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}h${r*=1}v${+n}h${-r}Z`}toString(){return this._}}function oB(e){let t=3;return e.digits=function(r){if(!arguments.length)return t;if(null==r)t=null;else{let e=Math.floor(r);if(!(e>=0))throw RangeError(`invalid digits: ${r}`);t=e}return e},()=>new oL(t)}function oF(e){return(oF="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}oL.prototype,og(3),og(3);var oz=["type","size","sizeType"];function o$(){return(o$=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function oW(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function oU(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?oW(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=oF(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=oF(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==oF(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):oW(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var oq={symbolCircle:oO,symbolCross:{draw(e,t){let r=og(t/5)/2;e.moveTo(-3*r,-r),e.lineTo(-r,-r),e.lineTo(-r,-3*r),e.lineTo(r,-3*r),e.lineTo(r,-r),e.lineTo(3*r,-r),e.lineTo(3*r,r),e.lineTo(r,r),e.lineTo(r,3*r),e.lineTo(-r,3*r),e.lineTo(-r,r),e.lineTo(-3*r,r),e.closePath()}},symbolDiamond:{draw(e,t){let r=og(t/oS),n=r*oj;e.moveTo(0,-r),e.lineTo(n,0),e.lineTo(0,r),e.lineTo(-n,0),e.closePath()}},symbolSquare:{draw(e,t){let r=og(t),n=-r/2;e.rect(n,n,r,r)}},symbolStar:{draw(e,t){let r=og(.8908130915292852*t),n=oE*r,o=oA*r;e.moveTo(0,-r),e.lineTo(n,o);for(let t=1;t<5;++t){let i=ow*t/5,a=om(i),s=ob(i);e.lineTo(s*r,-a*r),e.lineTo(a*n-s*o,s*n+a*o)}e.closePath()}},symbolTriangle:{draw(e,t){let r=-og(t/(3*ok));e.moveTo(0,2*r),e.lineTo(-ok*r,-r),e.lineTo(ok*r,-r),e.closePath()}},symbolWye:{draw(e,t){let r=og(t/o_),n=r/2,o=r*oT,i=r*oT+r,a=-n;e.moveTo(n,o),e.lineTo(n,i),e.lineTo(a,i),e.lineTo(-.5*n-oM*o,oM*n+-.5*o),e.lineTo(-.5*n-oM*i,oM*n+-.5*i),e.lineTo(-.5*a-oM*i,oM*a+-.5*i),e.lineTo(-.5*n+oM*o,-.5*o-oM*n),e.lineTo(-.5*n+oM*i,-.5*i-oM*n),e.lineTo(-.5*a+oM*i,-.5*i-oM*a),e.closePath()}}},oY=Math.PI/180,oH=function(e,t,r){if("area"===t)return e;switch(r){case"cross":return 5*e*e/9;case"diamond":return .5*e*e/Math.sqrt(3);case"square":return e*e;case"star":var n=18*oY;return 1.25*e*e*(Math.tan(n)-Math.tan(2*n)*Math.pow(Math.tan(n),2));case"triangle":return Math.sqrt(3)*e*e/4;case"wye":return(21-10*Math.sqrt(3))*e*e/8;default:return Math.PI*e*e/4}},oX=function(e){var t,r=e.type,n=void 0===r?"circle":r,o=e.size,i=void 0===o?64:o,a=e.sizeType,s=void 0===a?"area":a,l=oU(oU({},function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,oz)),{},{type:n,size:i,sizeType:s}),c=l.className,f=l.cx,p=l.cy,d=nj(l,!0);return f===+f&&p===+p&&i===+i?u().createElement("path",o$({},d,{className:(0,rI.A)("recharts-symbols",c),transform:"translate(".concat(f,", ").concat(p,")"),d:(t=oq["symbol".concat(ov()(n))]||oO,(function(e,t){let r=null,n=oB(o);function o(){let o;if(r||(r=o=n()),e.apply(this,arguments).draw(r,+t.apply(this,arguments)),o)return r=null,o+""||null}return e="function"==typeof e?e:oC(e||oO),t="function"==typeof t?t:oC(void 0===t?64:+t),o.type=function(t){return arguments.length?(e="function"==typeof t?t:oC(t),o):e},o.size=function(e){return arguments.length?(t="function"==typeof e?e:oC(+e),o):t},o.context=function(e){return arguments.length?(r=null==e?null:e,o):r},o})().type(t).size(oH(i,s,n))())})):null};function oV(e){return(oV="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function oG(){return(oG=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function oK(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}oX.registerSymbol=function(e,t){oq["symbol".concat(ov()(e))]=t};function oZ(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(oZ=function(){return!!e})()}function oJ(e){return(oJ=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function oQ(e,t){return(oQ=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function o0(e,t,r){return(t=o1(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function o1(e){var t=function(e,t){if("object"!=oV(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=oV(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==oV(t)?t:t+""}var o2=function(e){var t;function r(){var e,t;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return e=r,t=arguments,e=oJ(e),function(e,t){if(t&&("object"===oV(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");var r=e;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,oZ()?Reflect.construct(e,t||[],oJ(this).constructor):e.apply(this,t))}if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(e&&e.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),e&&oQ(r,e),t=[{key:"renderIcon",value:function(e){var t=this.props.inactiveColor,r=32/6,n=32/3,o=e.inactive?t:e.color;if("plainline"===e.type)return u().createElement("line",{strokeWidth:4,fill:"none",stroke:o,strokeDasharray:e.payload.strokeDasharray,x1:0,y1:16,x2:32,y2:16,className:"recharts-legend-icon"});if("line"===e.type)return u().createElement("path",{strokeWidth:4,fill:"none",stroke:o,d:"M0,".concat(16,"h").concat(n,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(2*n,",").concat(16,"\n            H").concat(32,"M").concat(2*n,",").concat(16,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(n,",").concat(16),className:"recharts-legend-icon"});if("rect"===e.type)return u().createElement("path",{stroke:"none",fill:o,d:"M0,".concat(4,"h").concat(32,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(u().isValidElement(e.legendIcon)){var i=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?oK(Object(r),!0).forEach(function(t){o0(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):oK(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({},e);return delete i.legendIcon,u().cloneElement(e.legendIcon,i)}return u().createElement(oX,{fill:o,cx:16,cy:16,size:32,sizeType:"diameter",type:e.type})}},{key:"renderItems",value:function(){var e=this,t=this.props,r=t.payload,n=t.iconSize,o=t.layout,i=t.formatter,a=t.inactiveColor,s={x:0,y:0,width:32,height:32},l={display:"horizontal"===o?"inline-block":"block",marginRight:10},c={display:"inline-block",verticalAlign:"middle",marginRight:4};return r.map(function(t,r){var o=t.formatter||i,f=(0,rI.A)(o0(o0({"recharts-legend-item":!0},"legend-item-".concat(r),!0),"inactive",t.inactive));if("none"===t.type)return null;var p=r7()(t.value)?null:t.value;r3(!r7()(t.value),'The name property is also required when using a function for the dataKey of a chart\'s cartesian components. Ex: <Bar name="Name of my Data"/>');var d=t.inactive?a:t.color;return u().createElement("li",oG({className:f,style:l,key:"legend-item-".concat(r)},nc(e.props,t,r)),u().createElement(nz,{width:n,height:n,viewBox:s,style:c},e.renderIcon(t)),u().createElement("span",{className:"recharts-legend-item-text",style:{color:d}},o?o(p,t,r):p))})}},{key:"render",value:function(){var e=this.props,t=e.payload,r=e.layout,n=e.align;return t&&t.length?u().createElement("ul",{className:"recharts-default-legend",style:{padding:0,margin:0,textAlign:"horizontal"===r?n:"left"}},this.renderItems()):null}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,o1(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(c.PureComponent);function o5(e){return(o5="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}o0(o2,"displayName","Legend"),o0(o2,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var o3=["ref"];function o4(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function o8(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?o4(Object(r),!0).forEach(function(t){it(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o4(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function o6(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,ir(n.key),n)}}function o7(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(o7=function(){return!!e})()}function o9(e){return(o9=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function ie(e,t){return(ie=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function it(e,t,r){return(t=ir(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ir(e){var t=function(e,t){if("object"!=o5(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=o5(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==o5(t)?t:t+""}function io(e){return e.value}var ii=function(e){var t,r;function n(){var e,t,r;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");for(var o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return t=n,r=[].concat(i),t=o9(t),it(e=function(e,t){if(t&&("object"===o5(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");var r=e;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,o7()?Reflect.construct(t,r||[],o9(this).constructor):t.apply(this,r)),"lastBoundingBox",{width:-1,height:-1}),e}if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(e&&e.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),e&&ie(n,e),t=[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var e=this.wrapperNode.getBoundingClientRect();return e.height=this.wrapperNode.offsetHeight,e.width=this.wrapperNode.offsetWidth,e}return null}},{key:"updateBBox",value:function(){var e=this.props.onBBoxUpdate,t=this.getBBox();t?(Math.abs(t.width-this.lastBoundingBox.width)>1||Math.abs(t.height-this.lastBoundingBox.height)>1)&&(this.lastBoundingBox.width=t.width,this.lastBoundingBox.height=t.height,e&&e(t)):(-1!==this.lastBoundingBox.width||-1!==this.lastBoundingBox.height)&&(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,e&&e(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?o8({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(e){var t,r,n=this.props,o=n.layout,i=n.align,a=n.verticalAlign,s=n.margin,l=n.chartWidth,c=n.chartHeight;return e&&(void 0!==e.left&&null!==e.left||void 0!==e.right&&null!==e.right)||(t="center"===i&&"vertical"===o?{left:((l||0)-this.getBBoxSnapshot().width)/2}:"right"===i?{right:s&&s.right||0}:{left:s&&s.left||0}),e&&(void 0!==e.top&&null!==e.top||void 0!==e.bottom&&null!==e.bottom)||(r="middle"===a?{top:((c||0)-this.getBBoxSnapshot().height)/2}:"bottom"===a?{bottom:s&&s.bottom||0}:{top:s&&s.top||0}),o8(o8({},t),r)}},{key:"render",value:function(){var e=this,t=this.props,r=t.content,n=t.width,o=t.height,i=t.wrapperStyle,a=t.payloadUniqBy,s=t.payload,l=o8(o8({position:"absolute",width:n||"auto",height:o||"auto"},this.getDefaultPosition(i)),i);return u().createElement("div",{className:"recharts-legend-wrapper",style:l,ref:function(t){e.wrapperNode=t}},function(e,t){if(u().isValidElement(e))return u().cloneElement(e,t);if("function"==typeof e)return u().createElement(e,t);t.ref;var r=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(t,o3);return u().createElement(o2,r)}(r,o8(o8({},this.props),{},{payload:oo(s,a,io)})))}}],r=[{key:"getWithHeight",value:function(e,t){var r=o8(o8({},this.defaultProps),e.props).layout;return"vertical"===r&&rV(e.props.height)?{height:e.props.height}:"horizontal"===r?{width:e.props.width||t}:null}}],t&&o6(n.prototype,t),r&&o6(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(c.PureComponent);function ia(){return(ia=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}it(ii,"displayName","Legend"),it(ii,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"});var is=function(e){var t=e.cx,r=e.cy,n=e.r,o=e.className,i=(0,rI.A)("recharts-dot",o);return t===+t&&r===+r&&n===+n?u().createElement("circle",ia({},nj(e,!1),nl(e),{className:i,cx:t,cy:r,r:n})):null},il=r(87955),ic=r.n(il),iu=Object.getOwnPropertyNames,ip=Object.getOwnPropertySymbols,id=Object.prototype.hasOwnProperty;function ih(e,t){return function(r,n,o){return e(r,n,o)&&t(r,n,o)}}function iy(e){return function(t,r,n){if(!t||!r||"object"!=typeof t||"object"!=typeof r)return e(t,r,n);var o=n.cache,i=o.get(t),a=o.get(r);if(i&&a)return i===r&&a===t;o.set(t,r),o.set(r,t);var s=e(t,r,n);return o.delete(t),o.delete(r),s}}function iv(e){return iu(e).concat(ip(e))}var im=Object.hasOwn||function(e,t){return id.call(e,t)};function ib(e,t){return e===t||!e&&!t&&e!=e&&t!=t}var ig=Object.getOwnPropertyDescriptor,ix=Object.keys;function iw(e,t,r){var n=e.length;if(t.length!==n)return!1;for(;n-- >0;)if(!r.equals(e[n],t[n],n,n,e,t,r))return!1;return!0}function iO(e,t){return ib(e.getTime(),t.getTime())}function ij(e,t){return e.name===t.name&&e.message===t.message&&e.cause===t.cause&&e.stack===t.stack}function iS(e,t){return e===t}function iP(e,t,r){var n,o,i=e.size;if(i!==t.size)return!1;if(!i)return!0;for(var a=Array(i),s=e.entries(),l=0;(n=s.next())&&!n.done;){for(var c=t.entries(),u=!1,f=0;(o=c.next())&&!o.done;){if(a[f]){f++;continue}var p=n.value,d=o.value;if(r.equals(p[0],d[0],l,f,e,t,r)&&r.equals(p[1],d[1],p[0],d[0],e,t,r)){u=a[f]=!0;break}f++}if(!u)return!1;l++}return!0}function iE(e,t,r){var n=ix(e),o=n.length;if(ix(t).length!==o)return!1;for(;o-- >0;)if(!iN(e,t,r,n[o]))return!1;return!0}function iA(e,t,r){var n,o,i,a=iv(e),s=a.length;if(iv(t).length!==s)return!1;for(;s-- >0;)if(!iN(e,t,r,n=a[s])||(o=ig(e,n),i=ig(t,n),(o||i)&&(!o||!i||o.configurable!==i.configurable||o.enumerable!==i.enumerable||o.writable!==i.writable)))return!1;return!0}function ik(e,t){return ib(e.valueOf(),t.valueOf())}function iM(e,t){return e.source===t.source&&e.flags===t.flags}function iT(e,t,r){var n,o,i=e.size;if(i!==t.size)return!1;if(!i)return!0;for(var a=Array(i),s=e.values();(n=s.next())&&!n.done;){for(var l=t.values(),c=!1,u=0;(o=l.next())&&!o.done;){if(!a[u]&&r.equals(n.value,o.value,n.value,o.value,e,t,r)){c=a[u]=!0;break}u++}if(!c)return!1}return!0}function i_(e,t){var r=e.length;if(t.length!==r)return!1;for(;r-- >0;)if(e[r]!==t[r])return!1;return!0}function iC(e,t){return e.hostname===t.hostname&&e.pathname===t.pathname&&e.protocol===t.protocol&&e.port===t.port&&e.hash===t.hash&&e.username===t.username&&e.password===t.password}function iN(e,t,r,n){return("_owner"===n||"__o"===n||"__v"===n)&&(!!e.$$typeof||!!t.$$typeof)||im(t,n)&&r.equals(e[n],t[n],n,n,e,t,r)}var iD=Array.isArray,iI="function"==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView:null,iR=Object.assign,iL=Object.prototype.toString.call.bind(Object.prototype.toString),iB=iF();function iF(e){void 0===e&&(e={});var t,r,n,o,i,a,s,l,c,u,f,p,d,h=e.circular,y=e.createInternalComparator,v=e.createState,m=e.strict,b=(r=(t=function(e){var t=e.circular,r=e.createCustomConfig,n=e.strict,o={areArraysEqual:n?iA:iw,areDatesEqual:iO,areErrorsEqual:ij,areFunctionsEqual:iS,areMapsEqual:n?ih(iP,iA):iP,areNumbersEqual:ib,areObjectsEqual:n?iA:iE,arePrimitiveWrappersEqual:ik,areRegExpsEqual:iM,areSetsEqual:n?ih(iT,iA):iT,areTypedArraysEqual:n?iA:i_,areUrlsEqual:iC};if(r&&(o=iR({},o,r(o))),t){var i=iy(o.areArraysEqual),a=iy(o.areMapsEqual),s=iy(o.areObjectsEqual),l=iy(o.areSetsEqual);o=iR({},o,{areArraysEqual:i,areMapsEqual:a,areObjectsEqual:s,areSetsEqual:l})}return o}(e)).areArraysEqual,n=t.areDatesEqual,o=t.areErrorsEqual,i=t.areFunctionsEqual,a=t.areMapsEqual,s=t.areNumbersEqual,l=t.areObjectsEqual,c=t.arePrimitiveWrappersEqual,u=t.areRegExpsEqual,f=t.areSetsEqual,p=t.areTypedArraysEqual,d=t.areUrlsEqual,function(e,t,h){if(e===t)return!0;if(null==e||null==t)return!1;var y=typeof e;if(y!==typeof t)return!1;if("object"!==y)return"number"===y?s(e,t,h):"function"===y&&i(e,t,h);var v=e.constructor;if(v!==t.constructor)return!1;if(v===Object)return l(e,t,h);if(iD(e))return r(e,t,h);if(null!=iI&&iI(e))return p(e,t,h);if(v===Date)return n(e,t,h);if(v===RegExp)return u(e,t,h);if(v===Map)return a(e,t,h);if(v===Set)return f(e,t,h);var m=iL(e);return"[object Date]"===m?n(e,t,h):"[object RegExp]"===m?u(e,t,h):"[object Map]"===m?a(e,t,h):"[object Set]"===m?f(e,t,h):"[object Object]"===m?"function"!=typeof e.then&&"function"!=typeof t.then&&l(e,t,h):"[object URL]"===m?d(e,t,h):"[object Error]"===m?o(e,t,h):"[object Arguments]"===m?l(e,t,h):("[object Boolean]"===m||"[object Number]"===m||"[object String]"===m)&&c(e,t,h)}),g=y?y(b):function(e,t,r,n,o,i,a){return b(e,t,a)};return function(e){var t=e.circular,r=e.comparator,n=e.createState,o=e.equals,i=e.strict;if(n)return function(e,a){var s=n(),l=s.cache;return r(e,a,{cache:void 0===l?t?new WeakMap:void 0:l,equals:o,meta:s.meta,strict:i})};if(t)return function(e,t){return r(e,t,{cache:new WeakMap,equals:o,meta:void 0,strict:i})};var a={cache:void 0,equals:o,meta:void 0,strict:i};return function(e,t){return r(e,t,a)}}({circular:void 0!==h&&h,comparator:b,createState:v,equals:g,strict:void 0!==m&&m})}function iz(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=-1;requestAnimationFrame(function n(o){if(r<0&&(r=o),o-r>t)e(o),r=-1;else{var i;i=n,"undefined"!=typeof requestAnimationFrame&&requestAnimationFrame(i)}})}function i$(e){return(i$="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function iW(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function iU(e){return(iU="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function iq(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function iY(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?iq(Object(r),!0).forEach(function(t){iH(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):iq(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function iH(e,t,r){var n;return(n=function(e,t){if("object"!==iU(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==iU(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"===iU(n)?n:String(n))in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}iF({strict:!0}),iF({circular:!0}),iF({circular:!0,strict:!0}),iF({createInternalComparator:function(){return ib}}),iF({strict:!0,createInternalComparator:function(){return ib}}),iF({circular:!0,createInternalComparator:function(){return ib}}),iF({circular:!0,createInternalComparator:function(){return ib},strict:!0});var iX=function(e){return e},iV=function(e,t){return Object.keys(t).reduce(function(r,n){return iY(iY({},r),{},iH({},n,e(n,t[n])))},{})},iG=function(e,t,r){return e.map(function(e){return"".concat(e.replace(/([A-Z])/g,function(e){return"-".concat(e.toLowerCase())})," ").concat(t,"ms ").concat(r)}).join(",")},iK=function(e,t,r,n,o,i,a,s){};function iZ(e,t){if(e){if("string"==typeof e)return iJ(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return iJ(e,t)}}function iJ(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var iQ=function(e,t){return[0,3*e,3*t-6*e,3*e-3*t+1]},i0=function(e,t){return e.map(function(e,r){return e*Math.pow(t,r)}).reduce(function(e,t){return e+t})},i1=function(e,t){return function(r){return i0(iQ(e,t),r)}},i2=function(){for(var e,t,r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];var i=n[0],a=n[1],s=n[2],l=n[3];if(1===n.length)switch(n[0]){case"linear":i=0,a=0,s=1,l=1;break;case"ease":i=.25,a=.1,s=.25,l=1;break;case"ease-in":i=.42,a=0,s=1,l=1;break;case"ease-out":i=.42,a=0,s=.58,l=1;break;case"ease-in-out":i=0,a=0,s=.58,l=1;break;default:var c=n[0].split("(");if("cubic-bezier"===c[0]&&4===c[1].split(")")[0].split(",").length){var u,f=function(e){if(Array.isArray(e))return e}(u=c[1].split(")")[0].split(",").map(function(e){return parseFloat(e)}))||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,s=[],l=!0,c=!1;try{i=(r=r.call(e)).next,!1;for(;!(l=(n=i.call(r)).done)&&(s.push(n.value),s.length!==t);l=!0);}catch(e){c=!0,o=e}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return s}}(u,4)||iZ(u,4)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();i=f[0],a=f[1],s=f[2],l=f[3]}else iK(!1,"[configBezier]: arguments should be one of oneOf 'linear', 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', instead received %s",n)}iK([i,s,a,l].every(function(e){return"number"==typeof e&&e>=0&&e<=1}),"[configBezier]: arguments should be x1, y1, x2, y2 of [0, 1] instead received %s",n);var p=i1(i,s),d=i1(a,l),h=(e=i,t=s,function(r){var n;return i0([].concat(function(e){if(Array.isArray(e))return iJ(e)}(n=iQ(e,t).map(function(e,t){return e*t}).slice(1))||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(n)||iZ(n)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),[0]),r)}),y=function(e){for(var t=e>1?1:e,r=t,n=0;n<8;++n){var o,i=p(r)-t,a=h(r);if(1e-4>Math.abs(i-t)||a<1e-4)break;r=(o=r-i/a)>1?1:o<0?0:o}return d(r)};return y.isStepper=!1,y},i5=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.stiff,r=void 0===t?100:t,n=e.damping,o=void 0===n?8:n,i=e.dt,a=void 0===i?17:i,s=function(e,t,n){var i=n+(-(e-t)*r-n*o)*a/1e3,s=n*a/1e3+e;return 1e-4>Math.abs(s-t)&&1e-4>Math.abs(i)?[t,0]:[s,i]};return s.isStepper=!0,s.dt=a,s},i3=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=t[0];if("string"==typeof n)switch(n){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return i2(n);case"spring":return i5();default:if("cubic-bezier"===n.split("(")[0])return i2(n);iK(!1,"[configEasing]: first argument should be one of 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', 'linear' and 'spring', instead  received %s",t)}return"function"==typeof n?n:(iK(!1,"[configEasing]: first argument type should be function or string, instead received %s",t),null)};function i4(e){return(i4="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function i8(e){return function(e){if(Array.isArray(e))return at(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||ae(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function i6(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function i7(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?i6(Object(r),!0).forEach(function(t){i9(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i6(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function i9(e,t,r){var n;return(n=function(e,t){if("object"!==i4(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==i4(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"===i4(n)?n:String(n))in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ae(e,t){if(e){if("string"==typeof e)return at(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return at(e,t)}}function at(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var ar=function(e,t,r){return e+(t-e)*r},an=function(e){return e.from!==e.to},ao=function e(t,r,n){var o=iV(function(e,r){if(an(r)){var n,o=function(e){if(Array.isArray(e))return e}(n=t(r.from,r.to,r.velocity))||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,s=[],l=!0,c=!1;try{i=(r=r.call(e)).next,!1;for(;!(l=(n=i.call(r)).done)&&(s.push(n.value),s.length!==t);l=!0);}catch(e){c=!0,o=e}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return s}}(n,2)||ae(n,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],a=o[1];return i7(i7({},r),{},{from:i,velocity:a})}return r},r);return n<1?iV(function(e,t){return an(t)?i7(i7({},t),{},{velocity:ar(t.velocity,o[e].velocity,n),from:ar(t.from,o[e].from,n)}):t},r):e(t,o,n-1)};let ai=function(e,t,r,n,o){var i,a,s=[Object.keys(e),Object.keys(t)].reduce(function(e,t){return e.filter(function(e){return t.includes(e)})}),l=s.reduce(function(r,n){return i7(i7({},r),{},i9({},n,[e[n],t[n]]))},{}),c=s.reduce(function(r,n){return i7(i7({},r),{},i9({},n,{from:e[n],velocity:0,to:t[n]}))},{}),u=-1,f=function(){return null};return f=r.isStepper?function(n){i||(i=n);var a=(n-i)/r.dt;c=ao(r,c,a),o(i7(i7(i7({},e),t),iV(function(e,t){return t.from},c))),i=n,Object.values(c).filter(an).length&&(u=requestAnimationFrame(f))}:function(i){a||(a=i);var s=(i-a)/n,c=iV(function(e,t){return ar.apply(void 0,i8(t).concat([r(s)]))},l);if(o(i7(i7(i7({},e),t),c)),s<1)u=requestAnimationFrame(f);else{var p=iV(function(e,t){return ar.apply(void 0,i8(t).concat([r(1)]))},l);o(i7(i7(i7({},e),t),p))}},function(){return requestAnimationFrame(f),function(){cancelAnimationFrame(u)}}};function aa(e){return(aa="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var as=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function al(e){return function(e){if(Array.isArray(e))return ac(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return ac(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ac(e,t)}}(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ac(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function au(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function af(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?au(Object(r),!0).forEach(function(t){ap(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):au(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function ap(e,t,r){return(t=ad(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ad(e){var t=function(e,t){if("object"!==aa(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==aa(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===aa(t)?t:String(t)}function ah(e,t){return(ah=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function ay(e,t){if(t&&("object"===aa(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return av(e)}function av(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function am(e){return(am=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var ab=function(e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");o.prototype=Object.create(e&&e.prototype,{constructor:{value:o,writable:!0,configurable:!0}}),Object.defineProperty(o,"prototype",{writable:!1}),e&&ah(o,e);var t,r,n=(t=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}(),function(){var e,r=am(o);return e=t?Reflect.construct(r,arguments,am(this).constructor):r.apply(this,arguments),ay(this,e)});function o(e,t){if(!(this instanceof o))throw TypeError("Cannot call a class as a function");var r=n.call(this,e,t),i=r.props,a=i.isActive,s=i.attributeName,l=i.from,c=i.to,u=i.steps,f=i.children,p=i.duration;if(r.handleStyleChange=r.handleStyleChange.bind(av(r)),r.changeStyle=r.changeStyle.bind(av(r)),!a||p<=0)return r.state={style:{}},"function"==typeof f&&(r.state={style:c}),ay(r);if(u&&u.length)r.state={style:u[0].style};else if(l){if("function"==typeof f)return r.state={style:l},ay(r);r.state={style:s?ap({},s,l):l}}else r.state={style:{}};return r}return r=[{key:"componentDidMount",value:function(){var e=this.props,t=e.isActive,r=e.canBegin;this.mounted=!0,t&&r&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(e){var t=this.props,r=t.isActive,n=t.canBegin,o=t.attributeName,i=t.shouldReAnimate,a=t.to,s=t.from,l=this.state.style;if(n){if(!r){var c={style:o?ap({},o,a):a};this.state&&l&&(o&&l[o]!==a||!o&&l!==a)&&this.setState(c);return}if(!iB(e.to,a)||!e.canBegin||!e.isActive){var u=!e.canBegin||!e.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var f=u||i?s:e.to;if(this.state&&l){var p={style:o?ap({},o,f):f};(o&&l[o]!==f||!o&&l!==f)&&this.setState(p)}this.runAnimation(af(af({},this.props),{},{from:f,begin:0}))}}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var e=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),e&&e()}},{key:"handleStyleChange",value:function(e){this.changeStyle(e)}},{key:"changeStyle",value:function(e){this.mounted&&this.setState({style:e})}},{key:"runJSAnimation",value:function(e){var t=this,r=e.from,n=e.to,o=e.duration,i=e.easing,a=e.begin,s=e.onAnimationEnd,l=e.onAnimationStart,c=ai(r,n,i3(i),o,this.changeStyle);this.manager.start([l,a,function(){t.stopJSAnimation=c()},o,s])}},{key:"runStepAnimation",value:function(e){var t=this,r=e.steps,n=e.begin,o=e.onAnimationStart,i=r[0],a=i.style,s=i.duration;return this.manager.start([o].concat(al(r.reduce(function(e,n,o){if(0===o)return e;var i=n.duration,a=n.easing,s=void 0===a?"ease":a,l=n.style,c=n.properties,u=n.onAnimationEnd,f=o>0?r[o-1]:n,p=c||Object.keys(l);if("function"==typeof s||"spring"===s)return[].concat(al(e),[t.runJSAnimation.bind(t,{from:f.style,to:l,duration:i,easing:s}),i]);var d=iG(p,i,s),h=af(af(af({},f.style),l),{},{transition:d});return[].concat(al(e),[h,i,u]).filter(iX)},[a,Math.max(void 0===s?0:s,n)])),[e.onAnimationEnd]))}},{key:"runAnimation",value:function(e){this.manager||(this.manager=(r=function(){return null},n=!1,o=function e(t){if(!n){if(Array.isArray(t)){if(!t.length)return;var o=function(e){if(Array.isArray(e))return e}(t)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(t)||function(e,t){if(e){if("string"==typeof e)return iW(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return iW(e,t)}}(t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],a=o.slice(1);return"number"==typeof i?void iz(e.bind(null,a),i):(e(i),void iz(e.bind(null,a)))}"object"===i$(t)&&r(t),"function"==typeof t&&t()}},{stop:function(){n=!0},start:function(e){n=!1,o(e)},subscribe:function(e){return r=e,function(){r=function(){return null}}}}));var t,r,n,o,i=e.begin,a=e.duration,s=e.attributeName,l=e.to,c=e.easing,u=e.onAnimationStart,f=e.onAnimationEnd,p=e.steps,d=e.children,h=this.manager;if(this.unSubscribe=h.subscribe(this.handleStyleChange),"function"==typeof c||"function"==typeof d||"spring"===c)return void this.runJSAnimation(e);if(p.length>1)return void this.runStepAnimation(e);var y=s?ap({},s,l):l,v=iG(Object.keys(y),a,c);h.start([u,i,af(af({},y),{},{transition:v}),a,f])}},{key:"render",value:function(){var e=this.props,t=e.children,r=(e.begin,e.duration),n=(e.attributeName,e.easing,e.isActive),o=(e.steps,e.from,e.to,e.canBegin,e.onAnimationEnd,e.shouldReAnimate,e.onAnimationReStart,function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r,n,o={},i=Object.keys(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||(o[r]=e[r]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,as)),i=c.Children.count(t),a=this.state.style;if("function"==typeof t)return t(a);if(!n||0===i||r<=0)return t;var s=function(e){var t=e.props,r=t.style,n=t.className;return(0,c.cloneElement)(e,af(af({},o),{},{style:af(af({},void 0===r?{}:r),a),className:n}))};return 1===i?s(c.Children.only(t)):u().createElement("div",null,c.Children.map(t,function(e){return s(e)}))}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,ad(n.key),n)}}(o.prototype,r),Object.defineProperty(o,"prototype",{writable:!1}),o}(c.PureComponent);function ag(e){return(ag="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ax(){return(ax=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function aw(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function aO(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function aj(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?aO(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=ag(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=ag(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==ag(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):aO(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}ab.displayName="Animate",ab.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}},ab.propTypes={from:ic().oneOfType([ic().object,ic().string]),to:ic().oneOfType([ic().object,ic().string]),attributeName:ic().string,duration:ic().number,begin:ic().number,easing:ic().oneOfType([ic().string,ic().func]),steps:ic().arrayOf(ic().shape({duration:ic().number.isRequired,style:ic().object.isRequired,easing:ic().oneOfType([ic().oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),ic().func]),properties:ic().arrayOf("string"),onAnimationEnd:ic().func})),children:ic().oneOfType([ic().node,ic().func]),isActive:ic().bool,canBegin:ic().bool,onAnimationEnd:ic().func,shouldReAnimate:ic().bool,onAnimationStart:ic().func,onAnimationReStart:ic().func};var aS=function(e,t,r,n,o){var i,a=Math.min(Math.abs(r)/2,Math.abs(n)/2),s=n>=0?1:-1,l=r>=0?1:-1,c=+(n>=0&&r>=0||n<0&&r<0);if(a>0&&o instanceof Array){for(var u=[0,0,0,0],f=0;f<4;f++)u[f]=o[f]>a?a:o[f];i="M".concat(e,",").concat(t+s*u[0]),u[0]>0&&(i+="A ".concat(u[0],",").concat(u[0],",0,0,").concat(c,",").concat(e+l*u[0],",").concat(t)),i+="L ".concat(e+r-l*u[1],",").concat(t),u[1]>0&&(i+="A ".concat(u[1],",").concat(u[1],",0,0,").concat(c,",\n        ").concat(e+r,",").concat(t+s*u[1])),i+="L ".concat(e+r,",").concat(t+n-s*u[2]),u[2]>0&&(i+="A ".concat(u[2],",").concat(u[2],",0,0,").concat(c,",\n        ").concat(e+r-l*u[2],",").concat(t+n)),i+="L ".concat(e+l*u[3],",").concat(t+n),u[3]>0&&(i+="A ".concat(u[3],",").concat(u[3],",0,0,").concat(c,",\n        ").concat(e,",").concat(t+n-s*u[3])),i+="Z"}else if(a>0&&o===+o&&o>0){var p=Math.min(a,o);i="M ".concat(e,",").concat(t+s*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(c,",").concat(e+l*p,",").concat(t,"\n            L ").concat(e+r-l*p,",").concat(t,"\n            A ").concat(p,",").concat(p,",0,0,").concat(c,",").concat(e+r,",").concat(t+s*p,"\n            L ").concat(e+r,",").concat(t+n-s*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(c,",").concat(e+r-l*p,",").concat(t+n,"\n            L ").concat(e+l*p,",").concat(t+n,"\n            A ").concat(p,",").concat(p,",0,0,").concat(c,",").concat(e,",").concat(t+n-s*p," Z")}else i="M ".concat(e,",").concat(t," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return i},aP=function(e,t){if(!e||!t)return!1;var r=e.x,n=e.y,o=t.x,i=t.y,a=t.width,s=t.height;if(Math.abs(a)>0&&Math.abs(s)>0){var l=Math.min(o,o+a),c=Math.max(o,o+a),u=Math.min(i,i+s),f=Math.max(i,i+s);return r>=l&&r<=c&&n>=u&&n<=f}return!1},aE={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},aA=function(e){var t,r=aj(aj({},aE),e),n=(0,c.useRef)(),o=function(e){if(Array.isArray(e))return e}(t=(0,c.useState)(-1))||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,s=[],l=!0,c=!1;try{i=(r=r.call(e)).next,!1;for(;!(l=(n=i.call(r)).done)&&(s.push(n.value),s.length!==t);l=!0);}catch(e){c=!0,o=e}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return s}}(t,2)||function(e,t){if(e){if("string"==typeof e)return aw(e,2);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return aw(e,t)}}(t,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],a=o[1];(0,c.useEffect)(function(){if(n.current&&n.current.getTotalLength)try{var e=n.current.getTotalLength();e&&a(e)}catch(e){}},[]);var s=r.x,l=r.y,f=r.width,p=r.height,d=r.radius,h=r.className,y=r.animationEasing,v=r.animationDuration,m=r.animationBegin,b=r.isAnimationActive,g=r.isUpdateAnimationActive;if(s!==+s||l!==+l||f!==+f||p!==+p||0===f||0===p)return null;var x=(0,rI.A)("recharts-rectangle",h);return g?u().createElement(ab,{canBegin:i>0,from:{width:f,height:p,x:s,y:l},to:{width:f,height:p,x:s,y:l},duration:v,animationEasing:y,isActive:g},function(e){var t=e.width,o=e.height,a=e.x,s=e.y;return u().createElement(ab,{canBegin:i>0,from:"0px ".concat(-1===i?1:i,"px"),to:"".concat(i,"px 0px"),attributeName:"strokeDasharray",begin:m,duration:v,isActive:b,easing:y},u().createElement("path",ax({},nj(r,!0),{className:x,d:aS(a,s,t,o,d),ref:n})))}):u().createElement("path",ax({},nj(r,!0),{className:x,d:aS(s,l,f,p,d)}))};function ak(e,t){switch(arguments.length){case 0:break;case 1:this.range(e);break;default:this.range(t).domain(e)}return this}function aM(e,t){switch(arguments.length){case 0:break;case 1:"function"==typeof e?this.interpolator(e):this.range(e);break;default:this.domain(e),"function"==typeof t?this.interpolator(t):this.range(t)}return this}class aT extends Map{constructor(e,t=aC){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:t}}),null!=e)for(let[t,r]of e)this.set(t,r)}get(e){return super.get(a_(this,e))}has(e){return super.has(a_(this,e))}set(e,t){return super.set(function({_intern:e,_key:t},r){let n=t(r);return e.has(n)?e.get(n):(e.set(n,r),r)}(this,e),t)}delete(e){return super.delete(function({_intern:e,_key:t},r){let n=t(r);return e.has(n)&&(r=e.get(n),e.delete(n)),r}(this,e))}}function a_({_intern:e,_key:t},r){let n=t(r);return e.has(n)?e.get(n):r}function aC(e){return null!==e&&"object"==typeof e?e.valueOf():e}let aN=Symbol("implicit");function aD(){var e=new aT,t=[],r=[],n=aN;function o(o){let i=e.get(o);if(void 0===i){if(n!==aN)return n;e.set(o,i=t.push(o)-1)}return r[i%r.length]}return o.domain=function(r){if(!arguments.length)return t.slice();for(let n of(t=[],e=new aT,r))e.has(n)||e.set(n,t.push(n)-1);return o},o.range=function(e){return arguments.length?(r=Array.from(e),o):r.slice()},o.unknown=function(e){return arguments.length?(n=e,o):n},o.copy=function(){return aD(t,r).unknown(n)},ak.apply(o,arguments),o}function aI(){var e,t,r=aD().unknown(void 0),n=r.domain,o=r.range,i=0,a=1,s=!1,l=0,c=0,u=.5;function f(){var r=n().length,f=a<i,p=f?a:i,d=f?i:a;e=(d-p)/Math.max(1,r-l+2*c),s&&(e=Math.floor(e)),p+=(d-p-e*(r-l))*u,t=e*(1-l),s&&(p=Math.round(p),t=Math.round(t));var h=(function(e,t,r){e*=1,t*=1,r=(o=arguments.length)<2?(t=e,e=0,1):o<3?1:+r;for(var n=-1,o=0|Math.max(0,Math.ceil((t-e)/r)),i=Array(o);++n<o;)i[n]=e+n*r;return i})(r).map(function(t){return p+e*t});return o(f?h.reverse():h)}return delete r.unknown,r.domain=function(e){return arguments.length?(n(e),f()):n()},r.range=function(e){return arguments.length?([i,a]=e,i*=1,a*=1,f()):[i,a]},r.rangeRound=function(e){return[i,a]=e,i*=1,a*=1,s=!0,f()},r.bandwidth=function(){return t},r.step=function(){return e},r.round=function(e){return arguments.length?(s=!!e,f()):s},r.padding=function(e){return arguments.length?(l=Math.min(1,c=+e),f()):l},r.paddingInner=function(e){return arguments.length?(l=Math.min(1,e),f()):l},r.paddingOuter=function(e){return arguments.length?(c=+e,f()):c},r.align=function(e){return arguments.length?(u=Math.max(0,Math.min(1,e)),f()):u},r.copy=function(){return aI(n(),[i,a]).round(s).paddingInner(l).paddingOuter(c).align(u)},ak.apply(f(),arguments)}function aR(){return function e(t){var r=t.copy;return t.padding=t.paddingOuter,delete t.paddingInner,delete t.paddingOuter,t.copy=function(){return e(r())},t}(aI.apply(null,arguments).paddingInner(1))}function aL(e){return(aL="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function aB(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function aF(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?aB(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=aL(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=aL(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==aL(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):aB(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function az(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var a$={widthCache:{},cacheCount:0},aW={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},aU="recharts_measurement_span",aq=function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==e||ot.isSsr)return{width:0,height:0};var n=(Object.keys(t=aF({},r)).forEach(function(e){t[e]||delete t[e]}),t),o=JSON.stringify({text:e,copyStyle:n});if(a$.widthCache[o])return a$.widthCache[o];try{var i=document.getElementById(aU);i||((i=document.createElement("span")).setAttribute("id",aU),i.setAttribute("aria-hidden","true"),document.body.appendChild(i));var a=aF(aF({},aW),n);Object.assign(i.style,a),i.textContent="".concat(e);var s=i.getBoundingClientRect(),l={width:s.width,height:s.height};return a$.widthCache[o]=l,++a$.cacheCount>2e3&&(a$.cacheCount=0,a$.widthCache={}),l}catch(e){return{width:0,height:0}}};function aY(e){return(aY="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function aH(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,s=[],l=!0,c=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(s.push(n.value),s.length!==t);l=!0);}catch(e){c=!0,o=e}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return s}}(e,t)||function(e,t){if(e){if("string"==typeof e)return aX(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return aX(e,t)}}(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function aX(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function aV(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,function(e){var t=function(e,t){if("object"!=aY(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=aY(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==aY(t)?t:t+""}(n.key),n)}}var aG=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,aK=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,aZ=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,aJ=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,aQ={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},a0=Object.keys(aQ),a1=function(){var e,t;function r(e,t){if(!(this instanceof r))throw TypeError("Cannot call a class as a function");this.num=e,this.unit=t,this.num=e,this.unit=t,Number.isNaN(e)&&(this.unit=""),""===t||aZ.test(t)||(this.num=NaN,this.unit=""),a0.includes(t)&&(this.num=e*aQ[t],this.unit="px")}return e=[{key:"add",value:function(e){return this.unit!==e.unit?new r(NaN,""):new r(this.num+e.num,this.unit)}},{key:"subtract",value:function(e){return this.unit!==e.unit?new r(NaN,""):new r(this.num-e.num,this.unit)}},{key:"multiply",value:function(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new r(NaN,""):new r(this.num*e.num,this.unit||e.unit)}},{key:"divide",value:function(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new r(NaN,""):new r(this.num/e.num,this.unit||e.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}],t=[{key:"parse",value:function(e){var t,n=aH(null!=(t=aJ.exec(e))?t:[],3),o=n[1],i=n[2];return new r(parseFloat(o),null!=i?i:"")}}],e&&aV(r.prototype,e),t&&aV(r,t),Object.defineProperty(r,"prototype",{writable:!1}),r}();function a2(e){if(e.includes("NaN"))return"NaN";for(var t=e;t.includes("*")||t.includes("/");){var r,n=aH(null!=(r=aG.exec(t))?r:[],4),o=n[1],i=n[2],a=n[3],s=a1.parse(null!=o?o:""),l=a1.parse(null!=a?a:""),c="*"===i?s.multiply(l):s.divide(l);if(c.isNaN())return"NaN";t=t.replace(aG,c.toString())}for(;t.includes("+")||/.-\d+(?:\.\d+)?/.test(t);){var u,f=aH(null!=(u=aK.exec(t))?u:[],4),p=f[1],d=f[2],h=f[3],y=a1.parse(null!=p?p:""),v=a1.parse(null!=h?h:""),m="+"===d?y.add(v):y.subtract(v);if(m.isNaN())return"NaN";t=t.replace(aK,m.toString())}return t}var a5=/\(([^()]*)\)/;function a3(e){var t=function(e){try{var t;return t=e.replace(/\s+/g,""),t=function(e){for(var t=e;t.includes("(");){var r=aH(a5.exec(t),2)[1];t=t.replace(a5,a2(r))}return t}(t),t=a2(t)}catch(e){return"NaN"}}(e.slice(5,-1));return"NaN"===t?"":t}var a4=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],a8=["dx","dy","angle","className","breakAll"];function a6(){return(a6=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function a7(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}function a9(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,s=[],l=!0,c=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(s.push(n.value),s.length!==t);l=!0);}catch(e){c=!0,o=e}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return s}}(e,t)||function(e,t){if(e){if("string"==typeof e)return se(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return se(e,t)}}(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function se(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var st=/[ \f\n\r\t\v\u2028\u2029]+/,sr=function(e){var t=e.children,r=e.breakAll,n=e.style;try{var o=[];r8()(t)||(o=r?t.toString().split(""):t.toString().split(st));var i=o.map(function(e){return{word:e,width:aq(e,n).width}}),a=r?0:aq("\xa0",n).width;return{wordsWithComputedWidth:i,spaceWidth:a}}catch(e){return null}},sn=function(e,t,r,n,o){var i,a=e.maxLines,s=e.children,l=e.style,c=e.breakAll,u=rV(a),f=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return e.reduce(function(e,t){var i=t.word,a=t.width,s=e[e.length-1];return s&&(null==n||o||s.width+a+r<Number(n))?(s.words.push(i),s.width+=a+r):e.push({words:[i],width:a}),e},[])},p=f(t);if(!u)return p;for(var d=function(e){var t=f(sr({breakAll:c,style:l,children:s.slice(0,e)+"…"}).wordsWithComputedWidth);return[t.length>a||t.reduce(function(e,t){return e.width>t.width?e:t}).width>Number(n),t]},h=0,y=s.length-1,v=0;h<=y&&v<=s.length-1;){var m=Math.floor((h+y)/2),b=a9(d(m-1),2),g=b[0],x=b[1],w=a9(d(m),1)[0];if(g||w||(h=m+1),g&&w&&(y=m-1),!g&&w){i=x;break}v++}return i||p},so=function(e){return[{words:r8()(e)?[]:e.toString().split(st)}]},si=function(e){var t=e.width,r=e.scaleToFit,n=e.children,o=e.style,i=e.breakAll,a=e.maxLines;if((t||r)&&!ot.isSsr){var s=sr({breakAll:i,children:n,style:o});if(!s)return so(n);var l=s.wordsWithComputedWidth,c=s.spaceWidth;return sn({breakAll:i,children:n,maxLines:a,style:o},l,c,t,r)}return so(n)},sa="#808080",ss=function(e){var t,r=e.x,n=void 0===r?0:r,o=e.y,i=void 0===o?0:o,a=e.lineHeight,s=void 0===a?"1em":a,l=e.capHeight,f=void 0===l?"0.71em":l,p=e.scaleToFit,d=void 0!==p&&p,h=e.textAnchor,y=e.verticalAnchor,v=e.fill,m=void 0===v?sa:v,b=a7(e,a4),g=(0,c.useMemo)(function(){return si({breakAll:b.breakAll,children:b.children,maxLines:b.maxLines,scaleToFit:d,style:b.style,width:b.width})},[b.breakAll,b.children,b.maxLines,d,b.style,b.width]),x=b.dx,w=b.dy,O=b.angle,j=b.className,S=b.breakAll,P=a7(b,a8);if(!rG(n)||!rG(i))return null;var E=n+(rV(x)?x:0),A=i+(rV(w)?w:0);switch(void 0===y?"end":y){case"start":t=a3("calc(".concat(f,")"));break;case"middle":t=a3("calc(".concat((g.length-1)/2," * -").concat(s," + (").concat(f," / 2))"));break;default:t=a3("calc(".concat(g.length-1," * -").concat(s,")"))}var k=[];if(d){var M=g[0].width,T=b.width;k.push("scale(".concat((rV(T)?T/M:1)/M,")"))}return O&&k.push("rotate(".concat(O,", ").concat(E,", ").concat(A,")")),k.length&&(P.transform=k.join(" ")),u().createElement("text",a6({},nj(P,!0),{x:E,y:A,className:(0,rI.A)("recharts-text",j),textAnchor:void 0===h?"start":h,fill:m.includes("url")?sa:m}),g.map(function(e,r){var n=e.words.join(S?"":" ");return u().createElement("tspan",{x:E,dy:0===r?t:s,key:"".concat(n,"-").concat(r)},n)}))};let sl=Math.sqrt(50),sc=Math.sqrt(10),su=Math.sqrt(2);function sf(e,t,r){let n,o,i,a=(t-e)/Math.max(0,r),s=Math.floor(Math.log10(a)),l=a/Math.pow(10,s),c=l>=sl?10:l>=sc?5:l>=su?2:1;return(s<0?(n=Math.round(e*(i=Math.pow(10,-s)/c)),o=Math.round(t*i),n/i<e&&++n,o/i>t&&--o,i=-i):(n=Math.round(e/(i=Math.pow(10,s)*c)),o=Math.round(t/i),n*i<e&&++n,o*i>t&&--o),o<n&&.5<=r&&r<2)?sf(e,t,2*r):[n,o,i]}function sp(e,t,r){if(t*=1,e*=1,!((r*=1)>0))return[];if(e===t)return[e];let n=t<e,[o,i,a]=n?sf(t,e,r):sf(e,t,r);if(!(i>=o))return[];let s=i-o+1,l=Array(s);if(n)if(a<0)for(let e=0;e<s;++e)l[e]=-((i-e)/a);else for(let e=0;e<s;++e)l[e]=(i-e)*a;else if(a<0)for(let e=0;e<s;++e)l[e]=-((o+e)/a);else for(let e=0;e<s;++e)l[e]=(o+e)*a;return l}function sd(e,t,r){return sf(e*=1,t*=1,r*=1)[2]}function sh(e,t,r){t*=1,e*=1,r*=1;let n=t<e,o=n?sd(t,e,r):sd(e,t,r);return(n?-1:1)*(o<0?-(1/o):o)}function sy(e,t){return null==e||null==t?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function sv(e,t){return null==e||null==t?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function sm(e){let t,r,n;function o(e,n,i=0,a=e.length){if(i<a){if(0!==t(n,n))return a;do{let t=i+a>>>1;0>r(e[t],n)?i=t+1:a=t}while(i<a)}return i}return 2!==e.length?(t=sy,r=(t,r)=>sy(e(t),r),n=(t,r)=>e(t)-r):(t=e===sy||e===sv?e:sb,r=e,n=e),{left:o,center:function(e,t,r=0,i=e.length){let a=o(e,t,r,i-1);return a>r&&n(e[a-1],t)>-n(e[a],t)?a-1:a},right:function(e,n,o=0,i=e.length){if(o<i){if(0!==t(n,n))return i;do{let t=o+i>>>1;0>=r(e[t],n)?o=t+1:i=t}while(o<i)}return o}}}function sb(){return 0}function sg(e){return null===e?NaN:+e}let sx=sm(sy),sw=sx.right;function sO(e,t,r){e.prototype=t.prototype=r,r.constructor=e}function sj(e,t){var r=Object.create(e.prototype);for(var n in t)r[n]=t[n];return r}function sS(){}sx.left,sm(sg).center;var sP="\\s*([+-]?\\d+)\\s*",sE="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",sA="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",sk=/^#([0-9a-f]{3,8})$/,sM=RegExp(`^rgb\\(${sP},${sP},${sP}\\)$`),sT=RegExp(`^rgb\\(${sA},${sA},${sA}\\)$`),s_=RegExp(`^rgba\\(${sP},${sP},${sP},${sE}\\)$`),sC=RegExp(`^rgba\\(${sA},${sA},${sA},${sE}\\)$`),sN=RegExp(`^hsl\\(${sE},${sA},${sA}\\)$`),sD=RegExp(`^hsla\\(${sE},${sA},${sA},${sE}\\)$`),sI={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function sR(){return this.rgb().formatHex()}function sL(){return this.rgb().formatRgb()}function sB(e){var t,r;return e=(e+"").trim().toLowerCase(),(t=sk.exec(e))?(r=t[1].length,t=parseInt(t[1],16),6===r?sF(t):3===r?new sW(t>>8&15|t>>4&240,t>>4&15|240&t,(15&t)<<4|15&t,1):8===r?sz(t>>24&255,t>>16&255,t>>8&255,(255&t)/255):4===r?sz(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|240&t,((15&t)<<4|15&t)/255):null):(t=sM.exec(e))?new sW(t[1],t[2],t[3],1):(t=sT.exec(e))?new sW(255*t[1]/100,255*t[2]/100,255*t[3]/100,1):(t=s_.exec(e))?sz(t[1],t[2],t[3],t[4]):(t=sC.exec(e))?sz(255*t[1]/100,255*t[2]/100,255*t[3]/100,t[4]):(t=sN.exec(e))?sV(t[1],t[2]/100,t[3]/100,1):(t=sD.exec(e))?sV(t[1],t[2]/100,t[3]/100,t[4]):sI.hasOwnProperty(e)?sF(sI[e]):"transparent"===e?new sW(NaN,NaN,NaN,0):null}function sF(e){return new sW(e>>16&255,e>>8&255,255&e,1)}function sz(e,t,r,n){return n<=0&&(e=t=r=NaN),new sW(e,t,r,n)}function s$(e,t,r,n){var o;return 1==arguments.length?((o=e)instanceof sS||(o=sB(o)),o)?new sW((o=o.rgb()).r,o.g,o.b,o.opacity):new sW:new sW(e,t,r,null==n?1:n)}function sW(e,t,r,n){this.r=+e,this.g=+t,this.b=+r,this.opacity=+n}function sU(){return`#${sX(this.r)}${sX(this.g)}${sX(this.b)}`}function sq(){let e=sY(this.opacity);return`${1===e?"rgb(":"rgba("}${sH(this.r)}, ${sH(this.g)}, ${sH(this.b)}${1===e?")":`, ${e})`}`}function sY(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function sH(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function sX(e){return((e=sH(e))<16?"0":"")+e.toString(16)}function sV(e,t,r,n){return n<=0?e=t=r=NaN:r<=0||r>=1?e=t=NaN:t<=0&&(e=NaN),new sK(e,t,r,n)}function sG(e){if(e instanceof sK)return new sK(e.h,e.s,e.l,e.opacity);if(e instanceof sS||(e=sB(e)),!e)return new sK;if(e instanceof sK)return e;var t=(e=e.rgb()).r/255,r=e.g/255,n=e.b/255,o=Math.min(t,r,n),i=Math.max(t,r,n),a=NaN,s=i-o,l=(i+o)/2;return s?(a=t===i?(r-n)/s+(r<n)*6:r===i?(n-t)/s+2:(t-r)/s+4,s/=l<.5?i+o:2-i-o,a*=60):s=l>0&&l<1?0:a,new sK(a,s,l,e.opacity)}function sK(e,t,r,n){this.h=+e,this.s=+t,this.l=+r,this.opacity=+n}function sZ(e){return(e=(e||0)%360)<0?e+360:e}function sJ(e){return Math.max(0,Math.min(1,e||0))}function sQ(e,t,r){return(e<60?t+(r-t)*e/60:e<180?r:e<240?t+(r-t)*(240-e)/60:t)*255}function s0(e,t,r,n,o){var i=e*e,a=i*e;return((1-3*e+3*i-a)*t+(4-6*i+3*a)*r+(1+3*e+3*i-3*a)*n+a*o)/6}sO(sS,sB,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:sR,formatHex:sR,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return sG(this).formatHsl()},formatRgb:sL,toString:sL}),sO(sW,s$,sj(sS,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new sW(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new sW(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new sW(sH(this.r),sH(this.g),sH(this.b),sY(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:sU,formatHex:sU,formatHex8:function(){return`#${sX(this.r)}${sX(this.g)}${sX(this.b)}${sX((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:sq,toString:sq})),sO(sK,function(e,t,r,n){return 1==arguments.length?sG(e):new sK(e,t,r,null==n?1:n)},sj(sS,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new sK(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new sK(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*t,o=2*r-n;return new sW(sQ(e>=240?e-240:e+120,o,n),sQ(e,o,n),sQ(e<120?e+240:e-120,o,n),this.opacity)},clamp(){return new sK(sZ(this.h),sJ(this.s),sJ(this.l),sY(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let e=sY(this.opacity);return`${1===e?"hsl(":"hsla("}${sZ(this.h)}, ${100*sJ(this.s)}%, ${100*sJ(this.l)}%${1===e?")":`, ${e})`}`}}));let s1=e=>()=>e;function s2(e,t){var r,n,o=t-e;return o?(r=e,n=o,function(e){return r+e*n}):s1(isNaN(e)?t:e)}let s5=function e(t){var r,n=1==(r=+t)?s2:function(e,t){var n,o,i;return t-e?(n=e,o=t,n=Math.pow(n,i=r),o=Math.pow(o,i)-n,i=1/i,function(e){return Math.pow(n+e*o,i)}):s1(isNaN(e)?t:e)};function o(e,t){var r=n((e=s$(e)).r,(t=s$(t)).r),o=n(e.g,t.g),i=n(e.b,t.b),a=s2(e.opacity,t.opacity);return function(t){return e.r=r(t),e.g=o(t),e.b=i(t),e.opacity=a(t),e+""}}return o.gamma=e,o}(1);function s3(e){return function(t){var r,n,o=t.length,i=Array(o),a=Array(o),s=Array(o);for(r=0;r<o;++r)n=s$(t[r]),i[r]=n.r||0,a[r]=n.g||0,s[r]=n.b||0;return i=e(i),a=e(a),s=e(s),n.opacity=1,function(e){return n.r=i(e),n.g=a(e),n.b=s(e),n+""}}}s3(function(e){var t=e.length-1;return function(r){var n=r<=0?r=0:r>=1?(r=1,t-1):Math.floor(r*t),o=e[n],i=e[n+1],a=n>0?e[n-1]:2*o-i,s=n<t-1?e[n+2]:2*i-o;return s0((r-n/t)*t,a,o,i,s)}}),s3(function(e){var t=e.length;return function(r){var n=Math.floor(((r%=1)<0?++r:r)*t),o=e[(n+t-1)%t],i=e[n%t],a=e[(n+1)%t],s=e[(n+2)%t];return s0((r-n/t)*t,o,i,a,s)}});function s4(e,t){return e*=1,t*=1,function(r){return e*(1-r)+t*r}}var s8=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,s6=RegExp(s8.source,"g");function s7(e,t){var r,n,o=typeof t;return null==t||"boolean"===o?s1(t):("number"===o?s4:"string"===o?(n=sB(t))?(t=n,s5):function(e,t){var r,n,o,i,a,s=s8.lastIndex=s6.lastIndex=0,l=-1,c=[],u=[];for(e+="",t+="";(o=s8.exec(e))&&(i=s6.exec(t));)(a=i.index)>s&&(a=t.slice(s,a),c[l]?c[l]+=a:c[++l]=a),(o=o[0])===(i=i[0])?c[l]?c[l]+=i:c[++l]=i:(c[++l]=null,u.push({i:l,x:s4(o,i)})),s=s6.lastIndex;return s<t.length&&(a=t.slice(s),c[l]?c[l]+=a:c[++l]=a),c.length<2?u[0]?(r=u[0].x,function(e){return r(e)+""}):(n=t,function(){return n}):(t=u.length,function(e){for(var r,n=0;n<t;++n)c[(r=u[n]).i]=r.x(e);return c.join("")})}:t instanceof sB?s5:t instanceof Date?function(e,t){var r=new Date;return e*=1,t*=1,function(n){return r.setTime(e*(1-n)+t*n),r}}:!ArrayBuffer.isView(r=t)||r instanceof DataView?Array.isArray(t)?function(e,t){var r,n=t?t.length:0,o=e?Math.min(n,e.length):0,i=Array(o),a=Array(n);for(r=0;r<o;++r)i[r]=s7(e[r],t[r]);for(;r<n;++r)a[r]=t[r];return function(e){for(r=0;r<o;++r)a[r]=i[r](e);return a}}:"function"!=typeof t.valueOf&&"function"!=typeof t.toString||isNaN(t)?function(e,t){var r,n={},o={};for(r in(null===e||"object"!=typeof e)&&(e={}),(null===t||"object"!=typeof t)&&(t={}),t)r in e?n[r]=s7(e[r],t[r]):o[r]=t[r];return function(e){for(r in n)o[r]=n[r](e);return o}}:s4:function(e,t){t||(t=[]);var r,n=e?Math.min(t.length,e.length):0,o=t.slice();return function(i){for(r=0;r<n;++r)o[r]=e[r]*(1-i)+t[r]*i;return o}})(e,t)}function s9(e,t){return e*=1,t*=1,function(r){return Math.round(e*(1-r)+t*r)}}function le(e){return+e}var lt=[0,1];function lr(e){return e}function ln(e,t){var r;return(t-=e*=1)?function(r){return(r-e)/t}:(r=isNaN(t)?NaN:.5,function(){return r})}function lo(e,t,r){var n=e[0],o=e[1],i=t[0],a=t[1];return o<n?(n=ln(o,n),i=r(a,i)):(n=ln(n,o),i=r(i,a)),function(e){return i(n(e))}}function li(e,t,r){var n=Math.min(e.length,t.length)-1,o=Array(n),i=Array(n),a=-1;for(e[n]<e[0]&&(e=e.slice().reverse(),t=t.slice().reverse());++a<n;)o[a]=ln(e[a],e[a+1]),i[a]=r(t[a],t[a+1]);return function(t){var r=sw(e,t,1,n)-1;return i[r](o[r](t))}}function la(e,t){return t.domain(e.domain()).range(e.range()).interpolate(e.interpolate()).clamp(e.clamp()).unknown(e.unknown())}function ls(){var e,t,r,n,o,i,a=lt,s=lt,l=s7,c=lr;function u(){var e,t,r,l=Math.min(a.length,s.length);return c!==lr&&(e=a[0],t=a[l-1],e>t&&(r=e,e=t,t=r),c=function(r){return Math.max(e,Math.min(t,r))}),n=l>2?li:lo,o=i=null,f}function f(t){return null==t||isNaN(t*=1)?r:(o||(o=n(a.map(e),s,l)))(e(c(t)))}return f.invert=function(r){return c(t((i||(i=n(s,a.map(e),s4)))(r)))},f.domain=function(e){return arguments.length?(a=Array.from(e,le),u()):a.slice()},f.range=function(e){return arguments.length?(s=Array.from(e),u()):s.slice()},f.rangeRound=function(e){return s=Array.from(e),l=s9,u()},f.clamp=function(e){return arguments.length?(c=!!e||lr,u()):c!==lr},f.interpolate=function(e){return arguments.length?(l=e,u()):l},f.unknown=function(e){return arguments.length?(r=e,f):r},function(r,n){return e=r,t=n,u()}}function ll(){return ls()(lr,lr)}var lc=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function lu(e){var t;if(!(t=lc.exec(e)))throw Error("invalid format: "+e);return new lf({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}function lf(e){this.fill=void 0===e.fill?" ":e.fill+"",this.align=void 0===e.align?">":e.align+"",this.sign=void 0===e.sign?"-":e.sign+"",this.symbol=void 0===e.symbol?"":e.symbol+"",this.zero=!!e.zero,this.width=void 0===e.width?void 0:+e.width,this.comma=!!e.comma,this.precision=void 0===e.precision?void 0:+e.precision,this.trim=!!e.trim,this.type=void 0===e.type?"":e.type+""}function lp(e,t){if((r=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var r,n=e.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+e.slice(r+1)]}function ld(e){return(e=lp(Math.abs(e)))?e[1]:NaN}function lh(e,t){var r=lp(e,t);if(!r)return e+"";var n=r[0],o=r[1];return o<0?"0."+Array(-o).join("0")+n:n.length>o+1?n.slice(0,o+1)+"."+n.slice(o+1):n+Array(o-n.length+2).join("0")}lu.prototype=lf.prototype,lf.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};let ly={"%":(e,t)=>(100*e).toFixed(t),b:e=>Math.round(e).toString(2),c:e=>e+"",d:function(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)},e:(e,t)=>e.toExponential(t),f:(e,t)=>e.toFixed(t),g:(e,t)=>e.toPrecision(t),o:e=>Math.round(e).toString(8),p:(e,t)=>lh(100*e,t),r:lh,s:function(e,t){var r=lp(e,t);if(!r)return e+"";var n=r[0],o=r[1],i=o-(uK=3*Math.max(-8,Math.min(8,Math.floor(o/3))))+1,a=n.length;return i===a?n:i>a?n+Array(i-a+1).join("0"):i>0?n.slice(0,i)+"."+n.slice(i):"0."+Array(1-i).join("0")+lp(e,Math.max(0,t+i-1))[0]},X:e=>Math.round(e).toString(16).toUpperCase(),x:e=>Math.round(e).toString(16)};function lv(e){return e}var lm=Array.prototype.map,lb=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function lg(e,t,r,n){var o,i,a,s=sh(e,t,r);switch((n=lu(null==n?",f":n)).type){case"s":var l=Math.max(Math.abs(e),Math.abs(t));return null!=n.precision||isNaN(a=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(ld(l)/3)))-ld(Math.abs(s))))||(n.precision=a),uQ(n,l);case"":case"e":case"g":case"p":case"r":null!=n.precision||isNaN(a=Math.max(0,ld(Math.abs(Math.max(Math.abs(e),Math.abs(t)))-(o=Math.abs(o=s)))-ld(o))+1)||(n.precision=a-("e"===n.type));break;case"f":case"%":null!=n.precision||isNaN(a=Math.max(0,-ld(Math.abs(s))))||(n.precision=a-("%"===n.type)*2)}return uJ(n)}function lx(e){var t=e.domain;return e.ticks=function(e){var r=t();return sp(r[0],r[r.length-1],null==e?10:e)},e.tickFormat=function(e,r){var n=t();return lg(n[0],n[n.length-1],null==e?10:e,r)},e.nice=function(r){null==r&&(r=10);var n,o,i=t(),a=0,s=i.length-1,l=i[a],c=i[s],u=10;for(c<l&&(o=l,l=c,c=o,o=a,a=s,s=o);u-- >0;){if((o=sd(l,c,r))===n)return i[a]=l,i[s]=c,t(i);if(o>0)l=Math.floor(l/o)*o,c=Math.ceil(c/o)*o;else if(o<0)l=Math.ceil(l*o)/o,c=Math.floor(c*o)/o;else break;n=o}return e},e}function lw(){var e=ll();return e.copy=function(){return la(e,lw())},ak.apply(e,arguments),lx(e)}function lO(e,t){e=e.slice();var r,n=0,o=e.length-1,i=e[n],a=e[o];return a<i&&(r=n,n=o,o=r,r=i,i=a,a=r),e[n]=t.floor(i),e[o]=t.ceil(a),e}function lj(e){return Math.log(e)}function lS(e){return Math.exp(e)}function lP(e){return-Math.log(-e)}function lE(e){return-Math.exp(-e)}function lA(e){return isFinite(e)?+("1e"+e):e<0?0:e}function lk(e){return(t,r)=>-e(-t,r)}function lM(e){let t,r,n=e(lj,lS),o=n.domain,i=10;function a(){var a,s;return t=(a=i)===Math.E?Math.log:10===a&&Math.log10||2===a&&Math.log2||(a=Math.log(a),e=>Math.log(e)/a),r=10===(s=i)?lA:s===Math.E?Math.exp:e=>Math.pow(s,e),o()[0]<0?(t=lk(t),r=lk(r),e(lP,lE)):e(lj,lS),n}return n.base=function(e){return arguments.length?(i=+e,a()):i},n.domain=function(e){return arguments.length?(o(e),a()):o()},n.ticks=e=>{let n,a,s=o(),l=s[0],c=s[s.length-1],u=c<l;u&&([l,c]=[c,l]);let f=t(l),p=t(c),d=null==e?10:+e,h=[];if(!(i%1)&&p-f<d){if(f=Math.floor(f),p=Math.ceil(p),l>0){for(;f<=p;++f)for(n=1;n<i;++n)if(!((a=f<0?n/r(-f):n*r(f))<l)){if(a>c)break;h.push(a)}}else for(;f<=p;++f)for(n=i-1;n>=1;--n)if(!((a=f>0?n/r(-f):n*r(f))<l)){if(a>c)break;h.push(a)}2*h.length<d&&(h=sp(l,c,d))}else h=sp(f,p,Math.min(p-f,d)).map(r);return u?h.reverse():h},n.tickFormat=(e,o)=>{if(null==e&&(e=10),null==o&&(o=10===i?"s":","),"function"!=typeof o&&(i%1||null!=(o=lu(o)).precision||(o.trim=!0),o=uJ(o)),e===1/0)return o;let a=Math.max(1,i*e/n.ticks().length);return e=>{let n=e/r(Math.round(t(e)));return n*i<i-.5&&(n*=i),n<=a?o(e):""}},n.nice=()=>o(lO(o(),{floor:e=>r(Math.floor(t(e))),ceil:e=>r(Math.ceil(t(e)))})),n}function lT(e){return function(t){return Math.sign(t)*Math.log1p(Math.abs(t/e))}}function l_(e){return function(t){return Math.sign(t)*Math.expm1(Math.abs(t))*e}}function lC(e){var t=1,r=e(lT(1),l_(t));return r.constant=function(r){return arguments.length?e(lT(t=+r),l_(t)):t},lx(r)}function lN(e){return function(t){return t<0?-Math.pow(-t,e):Math.pow(t,e)}}function lD(e){return e<0?-Math.sqrt(-e):Math.sqrt(e)}function lI(e){return e<0?-e*e:e*e}function lR(e){var t=e(lr,lr),r=1;return t.exponent=function(t){return arguments.length?1==(r=+t)?e(lr,lr):.5===r?e(lD,lI):e(lN(r),lN(1/r)):r},lx(t)}function lL(){var e=lR(ls());return e.copy=function(){return la(e,lL()).exponent(e.exponent())},ak.apply(e,arguments),e}function lB(){return lL.apply(null,arguments).exponent(.5)}function lF(e){return Math.sign(e)*e*e}function lz(e,t){let r;if(void 0===t)for(let t of e)null!=t&&(r<t||void 0===r&&t>=t)&&(r=t);else{let n=-1;for(let o of e)null!=(o=t(o,++n,e))&&(r<o||void 0===r&&o>=o)&&(r=o)}return r}function l$(e,t){let r;if(void 0===t)for(let t of e)null!=t&&(r>t||void 0===r&&t>=t)&&(r=t);else{let n=-1;for(let o of e)null!=(o=t(o,++n,e))&&(r>o||void 0===r&&o>=o)&&(r=o)}return r}uJ=(uZ=function(e){var t,r,n,o=void 0===e.grouping||void 0===e.thousands?lv:(t=lm.call(e.grouping,Number),r=e.thousands+"",function(e,n){for(var o=e.length,i=[],a=0,s=t[0],l=0;o>0&&s>0&&(l+s+1>n&&(s=Math.max(1,n-l)),i.push(e.substring(o-=s,o+s)),!((l+=s+1)>n));)s=t[a=(a+1)%t.length];return i.reverse().join(r)}),i=void 0===e.currency?"":e.currency[0]+"",a=void 0===e.currency?"":e.currency[1]+"",s=void 0===e.decimal?".":e.decimal+"",l=void 0===e.numerals?lv:(n=lm.call(e.numerals,String),function(e){return e.replace(/[0-9]/g,function(e){return n[+e]})}),c=void 0===e.percent?"%":e.percent+"",u=void 0===e.minus?"−":e.minus+"",f=void 0===e.nan?"NaN":e.nan+"";function p(e){var t=(e=lu(e)).fill,r=e.align,n=e.sign,p=e.symbol,d=e.zero,h=e.width,y=e.comma,v=e.precision,m=e.trim,b=e.type;"n"===b?(y=!0,b="g"):ly[b]||(void 0===v&&(v=12),m=!0,b="g"),(d||"0"===t&&"="===r)&&(d=!0,t="0",r="=");var g="$"===p?i:"#"===p&&/[boxX]/.test(b)?"0"+b.toLowerCase():"",x="$"===p?a:/[%p]/.test(b)?c:"",w=ly[b],O=/[defgprs%]/.test(b);function j(e){var i,a,c,p=g,j=x;if("c"===b)j=w(e)+j,e="";else{var S=(e*=1)<0||1/e<0;if(e=isNaN(e)?f:w(Math.abs(e),v),m&&(e=function(e){e:for(var t,r=e.length,n=1,o=-1;n<r;++n)switch(e[n]){case".":o=t=n;break;case"0":0===o&&(o=n),t=n;break;default:if(!+e[n])break e;o>0&&(o=0)}return o>0?e.slice(0,o)+e.slice(t+1):e}(e)),S&&0==+e&&"+"!==n&&(S=!1),p=(S?"("===n?n:u:"-"===n||"("===n?"":n)+p,j=("s"===b?lb[8+uK/3]:"")+j+(S&&"("===n?")":""),O){for(i=-1,a=e.length;++i<a;)if(48>(c=e.charCodeAt(i))||c>57){j=(46===c?s+e.slice(i+1):e.slice(i))+j,e=e.slice(0,i);break}}}y&&!d&&(e=o(e,1/0));var P=p.length+e.length+j.length,E=P<h?Array(h-P+1).join(t):"";switch(y&&d&&(e=o(E+e,E.length?h-j.length:1/0),E=""),r){case"<":e=p+e+j+E;break;case"=":e=p+E+e+j;break;case"^":e=E.slice(0,P=E.length>>1)+p+e+j+E.slice(P);break;default:e=E+p+e+j}return l(e)}return v=void 0===v?6:/[gprs]/.test(b)?Math.max(1,Math.min(21,v)):Math.max(0,Math.min(20,v)),j.toString=function(){return e+""},j}return{format:p,formatPrefix:function(e,t){var r=p(((e=lu(e)).type="f",e)),n=3*Math.max(-8,Math.min(8,Math.floor(ld(t)/3))),o=Math.pow(10,-n),i=lb[8+n/3];return function(e){return r(o*e)+i}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,uQ=uZ.formatPrefix;function lW(e,t){return(null==e||!(e>=e))-(null==t||!(t>=t))||(e<t?-1:+(e>t))}function lU(e,t,r){let n=e[t];e[t]=e[r],e[r]=n}let lq=new Date,lY=new Date;function lH(e,t,r,n){function o(t){return e(t=0==arguments.length?new Date:new Date(+t)),t}return o.floor=t=>(e(t=new Date(+t)),t),o.ceil=r=>(e(r=new Date(r-1)),t(r,1),e(r),r),o.round=e=>{let t=o(e),r=o.ceil(e);return e-t<r-e?t:r},o.offset=(e,r)=>(t(e=new Date(+e),null==r?1:Math.floor(r)),e),o.range=(r,n,i)=>{let a,s=[];if(r=o.ceil(r),i=null==i?1:Math.floor(i),!(r<n)||!(i>0))return s;do s.push(a=new Date(+r)),t(r,i),e(r);while(a<r&&r<n);return s},o.filter=r=>lH(t=>{if(t>=t)for(;e(t),!r(t);)t.setTime(t-1)},(e,n)=>{if(e>=e)if(n<0)for(;++n<=0;)for(;t(e,-1),!r(e););else for(;--n>=0;)for(;t(e,1),!r(e););}),r&&(o.count=(t,n)=>(lq.setTime(+t),lY.setTime(+n),e(lq),e(lY),Math.floor(r(lq,lY))),o.every=e=>isFinite(e=Math.floor(e))&&e>0?e>1?o.filter(n?t=>n(t)%e==0:t=>o.count(0,t)%e==0):o:null),o}let lX=lH(()=>{},(e,t)=>{e.setTime(+e+t)},(e,t)=>t-e);lX.every=e=>isFinite(e=Math.floor(e))&&e>0?e>1?lH(t=>{t.setTime(Math.floor(t/e)*e)},(t,r)=>{t.setTime(+t+r*e)},(t,r)=>(r-t)/e):lX:null,lX.range;let lV=lH(e=>{e.setTime(e-e.getMilliseconds())},(e,t)=>{e.setTime(+e+1e3*t)},(e,t)=>(t-e)/1e3,e=>e.getUTCSeconds());lV.range;let lG=lH(e=>{e.setTime(e-e.getMilliseconds()-1e3*e.getSeconds())},(e,t)=>{e.setTime(+e+6e4*t)},(e,t)=>(t-e)/6e4,e=>e.getMinutes());lG.range;let lK=lH(e=>{e.setUTCSeconds(0,0)},(e,t)=>{e.setTime(+e+6e4*t)},(e,t)=>(t-e)/6e4,e=>e.getUTCMinutes());lK.range;let lZ=lH(e=>{e.setTime(e-e.getMilliseconds()-1e3*e.getSeconds()-6e4*e.getMinutes())},(e,t)=>{e.setTime(+e+36e5*t)},(e,t)=>(t-e)/36e5,e=>e.getHours());lZ.range;let lJ=lH(e=>{e.setUTCMinutes(0,0,0)},(e,t)=>{e.setTime(+e+36e5*t)},(e,t)=>(t-e)/36e5,e=>e.getUTCHours());lJ.range;let lQ=lH(e=>e.setHours(0,0,0,0),(e,t)=>e.setDate(e.getDate()+t),(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*6e4)/864e5,e=>e.getDate()-1);lQ.range;let l0=lH(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/864e5,e=>e.getUTCDate()-1);l0.range;let l1=lH(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/864e5,e=>Math.floor(e/864e5));function l2(e){return lH(t=>{t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)},(e,t)=>{e.setDate(e.getDate()+7*t)},(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*6e4)/6048e5)}l1.range;let l5=l2(0),l3=l2(1),l4=l2(2),l8=l2(3),l6=l2(4),l7=l2(5),l9=l2(6);function ce(e){return lH(t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+7*t)},(e,t)=>(t-e)/6048e5)}l5.range,l3.range,l4.range,l8.range,l6.range,l7.range,l9.range;let ct=ce(0),cr=ce(1),cn=ce(2),co=ce(3),ci=ce(4),ca=ce(5),cs=ce(6);ct.range,cr.range,cn.range,co.range,ci.range,ca.range,cs.range;let cl=lH(e=>{e.setDate(1),e.setHours(0,0,0,0)},(e,t)=>{e.setMonth(e.getMonth()+t)},(e,t)=>t.getMonth()-e.getMonth()+(t.getFullYear()-e.getFullYear())*12,e=>e.getMonth());cl.range;let cc=lH(e=>{e.setUTCDate(1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCMonth(e.getUTCMonth()+t)},(e,t)=>t.getUTCMonth()-e.getUTCMonth()+(t.getUTCFullYear()-e.getUTCFullYear())*12,e=>e.getUTCMonth());cc.range;let cu=lH(e=>{e.setMonth(0,1),e.setHours(0,0,0,0)},(e,t)=>{e.setFullYear(e.getFullYear()+t)},(e,t)=>t.getFullYear()-e.getFullYear(),e=>e.getFullYear());cu.every=e=>isFinite(e=Math.floor(e))&&e>0?lH(t=>{t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)},(t,r)=>{t.setFullYear(t.getFullYear()+r*e)}):null,cu.range;let cf=lH(e=>{e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCFullYear(e.getUTCFullYear()+t)},(e,t)=>t.getUTCFullYear()-e.getUTCFullYear(),e=>e.getUTCFullYear());function cp(e,t,r,n,o,i){let a=[[lV,1,1e3],[lV,5,5e3],[lV,15,15e3],[lV,30,3e4],[i,1,6e4],[i,5,3e5],[i,15,9e5],[i,30,18e5],[o,1,36e5],[o,3,108e5],[o,6,216e5],[o,12,432e5],[n,1,864e5],[n,2,1728e5],[r,1,6048e5],[t,1,2592e6],[t,3,7776e6],[e,1,31536e6]];function s(t,r,n){let o=Math.abs(r-t)/n,i=sm(([,,e])=>e).right(a,o);if(i===a.length)return e.every(sh(t/31536e6,r/31536e6,n));if(0===i)return lX.every(Math.max(sh(t,r,n),1));let[s,l]=a[o/a[i-1][2]<a[i][2]/o?i-1:i];return s.every(l)}return[function(e,t,r){let n=t<e;n&&([e,t]=[t,e]);let o=r&&"function"==typeof r.range?r:s(e,t,r),i=o?o.range(e,+t+1):[];return n?i.reverse():i},s]}cf.every=e=>isFinite(e=Math.floor(e))&&e>0?lH(t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCFullYear(t.getUTCFullYear()+r*e)}):null,cf.range;let[cd,ch]=cp(cf,cc,ct,l1,lJ,lK),[cy,cv]=cp(cu,cl,l5,lQ,lZ,lG);function cm(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function cb(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function cg(e,t,r){return{y:e,m:t,d:r,H:0,M:0,S:0,L:0}}var cx={"-":"",_:" ",0:"0"},cw=/^\s*\d+/,cO=/^%/,cj=/[\\^$*+?|[\]().{}]/g;function cS(e,t,r){var n=e<0?"-":"",o=(n?-e:e)+"",i=o.length;return n+(i<r?Array(r-i+1).join(t)+o:o)}function cP(e){return e.replace(cj,"\\$&")}function cE(e){return RegExp("^(?:"+e.map(cP).join("|")+")","i")}function cA(e){return new Map(e.map((e,t)=>[e.toLowerCase(),t]))}function ck(e,t,r){var n=cw.exec(t.slice(r,r+1));return n?(e.w=+n[0],r+n[0].length):-1}function cM(e,t,r){var n=cw.exec(t.slice(r,r+1));return n?(e.u=+n[0],r+n[0].length):-1}function cT(e,t,r){var n=cw.exec(t.slice(r,r+2));return n?(e.U=+n[0],r+n[0].length):-1}function c_(e,t,r){var n=cw.exec(t.slice(r,r+2));return n?(e.V=+n[0],r+n[0].length):-1}function cC(e,t,r){var n=cw.exec(t.slice(r,r+2));return n?(e.W=+n[0],r+n[0].length):-1}function cN(e,t,r){var n=cw.exec(t.slice(r,r+4));return n?(e.y=+n[0],r+n[0].length):-1}function cD(e,t,r){var n=cw.exec(t.slice(r,r+2));return n?(e.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function cI(e,t,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(r,r+6));return n?(e.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function cR(e,t,r){var n=cw.exec(t.slice(r,r+1));return n?(e.q=3*n[0]-3,r+n[0].length):-1}function cL(e,t,r){var n=cw.exec(t.slice(r,r+2));return n?(e.m=n[0]-1,r+n[0].length):-1}function cB(e,t,r){var n=cw.exec(t.slice(r,r+2));return n?(e.d=+n[0],r+n[0].length):-1}function cF(e,t,r){var n=cw.exec(t.slice(r,r+3));return n?(e.m=0,e.d=+n[0],r+n[0].length):-1}function cz(e,t,r){var n=cw.exec(t.slice(r,r+2));return n?(e.H=+n[0],r+n[0].length):-1}function c$(e,t,r){var n=cw.exec(t.slice(r,r+2));return n?(e.M=+n[0],r+n[0].length):-1}function cW(e,t,r){var n=cw.exec(t.slice(r,r+2));return n?(e.S=+n[0],r+n[0].length):-1}function cU(e,t,r){var n=cw.exec(t.slice(r,r+3));return n?(e.L=+n[0],r+n[0].length):-1}function cq(e,t,r){var n=cw.exec(t.slice(r,r+6));return n?(e.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function cY(e,t,r){var n=cO.exec(t.slice(r,r+1));return n?r+n[0].length:-1}function cH(e,t,r){var n=cw.exec(t.slice(r));return n?(e.Q=+n[0],r+n[0].length):-1}function cX(e,t,r){var n=cw.exec(t.slice(r));return n?(e.s=+n[0],r+n[0].length):-1}function cV(e,t){return cS(e.getDate(),t,2)}function cG(e,t){return cS(e.getHours(),t,2)}function cK(e,t){return cS(e.getHours()%12||12,t,2)}function cZ(e,t){return cS(1+lQ.count(cu(e),e),t,3)}function cJ(e,t){return cS(e.getMilliseconds(),t,3)}function cQ(e,t){return cJ(e,t)+"000"}function c0(e,t){return cS(e.getMonth()+1,t,2)}function c1(e,t){return cS(e.getMinutes(),t,2)}function c2(e,t){return cS(e.getSeconds(),t,2)}function c5(e){var t=e.getDay();return 0===t?7:t}function c3(e,t){return cS(l5.count(cu(e)-1,e),t,2)}function c4(e){var t=e.getDay();return t>=4||0===t?l6(e):l6.ceil(e)}function c8(e,t){return e=c4(e),cS(l6.count(cu(e),e)+(4===cu(e).getDay()),t,2)}function c6(e){return e.getDay()}function c7(e,t){return cS(l3.count(cu(e)-1,e),t,2)}function c9(e,t){return cS(e.getFullYear()%100,t,2)}function ue(e,t){return cS((e=c4(e)).getFullYear()%100,t,2)}function ut(e,t){return cS(e.getFullYear()%1e4,t,4)}function ur(e,t){var r=e.getDay();return cS((e=r>=4||0===r?l6(e):l6.ceil(e)).getFullYear()%1e4,t,4)}function un(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+cS(t/60|0,"0",2)+cS(t%60,"0",2)}function uo(e,t){return cS(e.getUTCDate(),t,2)}function ui(e,t){return cS(e.getUTCHours(),t,2)}function ua(e,t){return cS(e.getUTCHours()%12||12,t,2)}function us(e,t){return cS(1+l0.count(cf(e),e),t,3)}function ul(e,t){return cS(e.getUTCMilliseconds(),t,3)}function uc(e,t){return ul(e,t)+"000"}function uu(e,t){return cS(e.getUTCMonth()+1,t,2)}function uf(e,t){return cS(e.getUTCMinutes(),t,2)}function up(e,t){return cS(e.getUTCSeconds(),t,2)}function ud(e){var t=e.getUTCDay();return 0===t?7:t}function uh(e,t){return cS(ct.count(cf(e)-1,e),t,2)}function uy(e){var t=e.getUTCDay();return t>=4||0===t?ci(e):ci.ceil(e)}function uv(e,t){return e=uy(e),cS(ci.count(cf(e),e)+(4===cf(e).getUTCDay()),t,2)}function um(e){return e.getUTCDay()}function ub(e,t){return cS(cr.count(cf(e)-1,e),t,2)}function ug(e,t){return cS(e.getUTCFullYear()%100,t,2)}function ux(e,t){return cS((e=uy(e)).getUTCFullYear()%100,t,2)}function uw(e,t){return cS(e.getUTCFullYear()%1e4,t,4)}function uO(e,t){var r=e.getUTCDay();return cS((e=r>=4||0===r?ci(e):ci.ceil(e)).getUTCFullYear()%1e4,t,4)}function uj(){return"+0000"}function uS(){return"%"}function uP(e){return+e}function uE(e){return Math.floor(e/1e3)}function uA(e){return new Date(e)}function uk(e){return e instanceof Date?+e:+new Date(+e)}function uM(e,t,r,n,o,i,a,s,l,c){var u=ll(),f=u.invert,p=u.domain,d=c(".%L"),h=c(":%S"),y=c("%I:%M"),v=c("%I %p"),m=c("%a %d"),b=c("%b %d"),g=c("%B"),x=c("%Y");function w(e){return(l(e)<e?d:s(e)<e?h:a(e)<e?y:i(e)<e?v:n(e)<e?o(e)<e?m:b:r(e)<e?g:x)(e)}return u.invert=function(e){return new Date(f(e))},u.domain=function(e){return arguments.length?p(Array.from(e,uk)):p().map(uA)},u.ticks=function(t){var r=p();return e(r[0],r[r.length-1],null==t?10:t)},u.tickFormat=function(e,t){return null==t?w:c(t)},u.nice=function(e){var r=p();return e&&"function"==typeof e.range||(e=t(r[0],r[r.length-1],null==e?10:e)),e?p(lO(r,e)):u},u.copy=function(){return la(u,uM(e,t,r,n,o,i,a,s,l,c))},u}function uT(){return ak.apply(uM(cy,cv,cu,cl,l5,lQ,lZ,lG,lV,u1).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function u_(){return ak.apply(uM(cd,ch,cf,cc,ct,l0,lJ,lK,lV,u2).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function uC(){var e,t,r,n,o,i=0,a=1,s=lr,l=!1;function c(t){return null==t||isNaN(t*=1)?o:s(0===r?.5:(t=(n(t)-e)*r,l?Math.max(0,Math.min(1,t)):t))}function u(e){return function(t){var r,n;return arguments.length?([r,n]=t,s=e(r,n),c):[s(0),s(1)]}}return c.domain=function(o){return arguments.length?([i,a]=o,e=n(i*=1),t=n(a*=1),r=e===t?0:1/(t-e),c):[i,a]},c.clamp=function(e){return arguments.length?(l=!!e,c):l},c.interpolator=function(e){return arguments.length?(s=e,c):s},c.range=u(s7),c.rangeRound=u(s9),c.unknown=function(e){return arguments.length?(o=e,c):o},function(o){return n=o,e=o(i),t=o(a),r=e===t?0:1/(t-e),c}}function uN(e,t){return t.domain(e.domain()).interpolator(e.interpolator()).clamp(e.clamp()).unknown(e.unknown())}function uD(){var e=lR(uC());return e.copy=function(){return uN(e,uD()).exponent(e.exponent())},aM.apply(e,arguments)}function uI(){return uD.apply(null,arguments).exponent(.5)}function uR(){var e,t,r,n,o,i,a,s=0,l=.5,c=1,u=1,f=lr,p=!1;function d(e){return isNaN(e*=1)?a:(e=.5+((e=+i(e))-t)*(u*e<u*t?n:o),f(p?Math.max(0,Math.min(1,e)):e))}function h(e){return function(t){var r,n,o;return arguments.length?([r,n,o]=t,f=function(e,t){void 0===t&&(t=e,e=s7);for(var r=0,n=t.length-1,o=t[0],i=Array(n<0?0:n);r<n;)i[r]=e(o,o=t[++r]);return function(e){var t=Math.max(0,Math.min(n-1,Math.floor(e*=n)));return i[t](e-t)}}(e,[r,n,o]),d):[f(0),f(.5),f(1)]}}return d.domain=function(a){return arguments.length?([s,l,c]=a,e=i(s*=1),t=i(l*=1),r=i(c*=1),n=e===t?0:.5/(t-e),o=t===r?0:.5/(r-t),u=t<e?-1:1,d):[s,l,c]},d.clamp=function(e){return arguments.length?(p=!!e,d):p},d.interpolator=function(e){return arguments.length?(f=e,d):f},d.range=h(s7),d.rangeRound=h(s9),d.unknown=function(e){return arguments.length?(a=e,d):a},function(a){return i=a,e=a(s),t=a(l),r=a(c),n=e===t?0:.5/(t-e),o=t===r?0:.5/(r-t),u=t<e?-1:1,d}}function uL(){var e=lR(uR());return e.copy=function(){return uN(e,uL()).exponent(e.exponent())},aM.apply(e,arguments)}function uB(){return uL.apply(null,arguments).exponent(.5)}function uF(e,t){if((o=e.length)>1)for(var r,n,o,i=1,a=e[t[0]],s=a.length;i<o;++i)for(n=a,a=e[t[i]],r=0;r<s;++r)a[r][1]+=a[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}function uz(e){return"object"==typeof e&&"length"in e?e:Array.from(e)}function u$(e){for(var t=e.length,r=Array(t);--t>=0;)r[t]=t;return r}function uW(e,t){return e[t]}function uU(e){let t=[];return t.key=e,t}u1=(u0=function(e){var t=e.dateTime,r=e.date,n=e.time,o=e.periods,i=e.days,a=e.shortDays,s=e.months,l=e.shortMonths,c=cE(o),u=cA(o),f=cE(i),p=cA(i),d=cE(a),h=cA(a),y=cE(s),v=cA(s),m=cE(l),b=cA(l),g={a:function(e){return a[e.getDay()]},A:function(e){return i[e.getDay()]},b:function(e){return l[e.getMonth()]},B:function(e){return s[e.getMonth()]},c:null,d:cV,e:cV,f:cQ,g:ue,G:ur,H:cG,I:cK,j:cZ,L:cJ,m:c0,M:c1,p:function(e){return o[+(e.getHours()>=12)]},q:function(e){return 1+~~(e.getMonth()/3)},Q:uP,s:uE,S:c2,u:c5,U:c3,V:c8,w:c6,W:c7,x:null,X:null,y:c9,Y:ut,Z:un,"%":uS},x={a:function(e){return a[e.getUTCDay()]},A:function(e){return i[e.getUTCDay()]},b:function(e){return l[e.getUTCMonth()]},B:function(e){return s[e.getUTCMonth()]},c:null,d:uo,e:uo,f:uc,g:ux,G:uO,H:ui,I:ua,j:us,L:ul,m:uu,M:uf,p:function(e){return o[+(e.getUTCHours()>=12)]},q:function(e){return 1+~~(e.getUTCMonth()/3)},Q:uP,s:uE,S:up,u:ud,U:uh,V:uv,w:um,W:ub,x:null,X:null,y:ug,Y:uw,Z:uj,"%":uS},w={a:function(e,t,r){var n=d.exec(t.slice(r));return n?(e.w=h.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(e,t,r){var n=f.exec(t.slice(r));return n?(e.w=p.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(e,t,r){var n=m.exec(t.slice(r));return n?(e.m=b.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(e,t,r){var n=y.exec(t.slice(r));return n?(e.m=v.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(e,r,n){return S(e,t,r,n)},d:cB,e:cB,f:cq,g:cD,G:cN,H:cz,I:cz,j:cF,L:cU,m:cL,M:c$,p:function(e,t,r){var n=c.exec(t.slice(r));return n?(e.p=u.get(n[0].toLowerCase()),r+n[0].length):-1},q:cR,Q:cH,s:cX,S:cW,u:cM,U:cT,V:c_,w:ck,W:cC,x:function(e,t,n){return S(e,r,t,n)},X:function(e,t,r){return S(e,n,t,r)},y:cD,Y:cN,Z:cI,"%":cY};function O(e,t){return function(r){var n,o,i,a=[],s=-1,l=0,c=e.length;for(r instanceof Date||(r=new Date(+r));++s<c;)37===e.charCodeAt(s)&&(a.push(e.slice(l,s)),null!=(o=cx[n=e.charAt(++s)])?n=e.charAt(++s):o="e"===n?" ":"0",(i=t[n])&&(n=i(r,o)),a.push(n),l=s+1);return a.push(e.slice(l,s)),a.join("")}}function j(e,t){return function(r){var n,o,i=cg(1900,void 0,1);if(S(i,e,r+="",0)!=r.length)return null;if("Q"in i)return new Date(i.Q);if("s"in i)return new Date(1e3*i.s+("L"in i?i.L:0));if(!t||"Z"in i||(i.Z=0),"p"in i&&(i.H=i.H%12+12*i.p),void 0===i.m&&(i.m="q"in i?i.q:0),"V"in i){if(i.V<1||i.V>53)return null;"w"in i||(i.w=1),"Z"in i?(n=(o=(n=cb(cg(i.y,0,1))).getUTCDay())>4||0===o?cr.ceil(n):cr(n),n=l0.offset(n,(i.V-1)*7),i.y=n.getUTCFullYear(),i.m=n.getUTCMonth(),i.d=n.getUTCDate()+(i.w+6)%7):(n=(o=(n=cm(cg(i.y,0,1))).getDay())>4||0===o?l3.ceil(n):l3(n),n=lQ.offset(n,(i.V-1)*7),i.y=n.getFullYear(),i.m=n.getMonth(),i.d=n.getDate()+(i.w+6)%7)}else("W"in i||"U"in i)&&("w"in i||(i.w="u"in i?i.u%7:+("W"in i)),o="Z"in i?cb(cg(i.y,0,1)).getUTCDay():cm(cg(i.y,0,1)).getDay(),i.m=0,i.d="W"in i?(i.w+6)%7+7*i.W-(o+5)%7:i.w+7*i.U-(o+6)%7);return"Z"in i?(i.H+=i.Z/100|0,i.M+=i.Z%100,cb(i)):cm(i)}}function S(e,t,r,n){for(var o,i,a=0,s=t.length,l=r.length;a<s;){if(n>=l)return -1;if(37===(o=t.charCodeAt(a++))){if(!(i=w[(o=t.charAt(a++))in cx?t.charAt(a++):o])||(n=i(e,r,n))<0)return -1}else if(o!=r.charCodeAt(n++))return -1}return n}return g.x=O(r,g),g.X=O(n,g),g.c=O(t,g),x.x=O(r,x),x.X=O(n,x),x.c=O(t,x),{format:function(e){var t=O(e+="",g);return t.toString=function(){return e},t},parse:function(e){var t=j(e+="",!1);return t.toString=function(){return e},t},utcFormat:function(e){var t=O(e+="",x);return t.toString=function(){return e},t},utcParse:function(e){var t=j(e+="",!0);return t.toString=function(){return e},t}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,u0.parse,u2=u0.utcFormat,u0.utcParse,Array.prototype.slice;var uq,uY,uH,uX,uV,uG,uK,uZ,uJ,uQ,u0,u1,u2,u5,u3,u4=r(90453),u8=r.n(u4),u6=r(15883),u7=r.n(u6),u9=r(21592),fe=r.n(u9),ft=r(71967),fr=r.n(ft),fn=!0,fo="[DecimalError] ",fi=fo+"Invalid argument: ",fa=fo+"Exponent out of range: ",fs=Math.floor,fl=Math.pow,fc=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,fu=fs(1286742750677284.5),ff={};function fp(e,t){var r,n,o,i,a,s,l,c,u=e.constructor,f=u.precision;if(!e.s||!t.s)return t.s||(t=new u(e)),fn?fO(t,f):t;if(l=e.d,c=t.d,a=e.e,o=t.e,l=l.slice(),i=a-o){for(i<0?(n=l,i=-i,s=c.length):(n=c,o=a,s=l.length),i>(s=(a=Math.ceil(f/7))>s?a+1:s+1)&&(i=s,n.length=1),n.reverse();i--;)n.push(0);n.reverse()}for((s=l.length)-(i=c.length)<0&&(i=s,n=c,c=l,l=n),r=0;i;)r=(l[--i]=l[i]+c[i]+r)/1e7|0,l[i]%=1e7;for(r&&(l.unshift(r),++o),s=l.length;0==l[--s];)l.pop();return t.d=l,t.e=o,fn?fO(t,f):t}function fd(e,t,r){if(e!==~~e||e<t||e>r)throw Error(fi+e)}function fh(e){var t,r,n,o=e.length-1,i="",a=e[0];if(o>0){for(i+=a,t=1;t<o;t++)(r=7-(n=e[t]+"").length)&&(i+=fg(r)),i+=n;(r=7-(n=(a=e[t])+"").length)&&(i+=fg(r))}else if(0===a)return"0";for(;a%10==0;)a/=10;return i+a}ff.absoluteValue=ff.abs=function(){var e=new this.constructor(this);return e.s&&(e.s=1),e},ff.comparedTo=ff.cmp=function(e){var t,r,n,o;if(e=new this.constructor(e),this.s!==e.s)return this.s||-e.s;if(this.e!==e.e)return this.e>e.e^this.s<0?1:-1;for(t=0,r=(n=this.d.length)<(o=e.d.length)?n:o;t<r;++t)if(this.d[t]!==e.d[t])return this.d[t]>e.d[t]^this.s<0?1:-1;return n===o?0:n>o^this.s<0?1:-1},ff.decimalPlaces=ff.dp=function(){var e=this.d.length-1,t=(e-this.e)*7;if(e=this.d[e])for(;e%10==0;e/=10)t--;return t<0?0:t},ff.dividedBy=ff.div=function(e){return fy(this,new this.constructor(e))},ff.dividedToIntegerBy=ff.idiv=function(e){var t=this.constructor;return fO(fy(this,new t(e),0,1),t.precision)},ff.equals=ff.eq=function(e){return!this.cmp(e)},ff.exponent=function(){return fm(this)},ff.greaterThan=ff.gt=function(e){return this.cmp(e)>0},ff.greaterThanOrEqualTo=ff.gte=function(e){return this.cmp(e)>=0},ff.isInteger=ff.isint=function(){return this.e>this.d.length-2},ff.isNegative=ff.isneg=function(){return this.s<0},ff.isPositive=ff.ispos=function(){return this.s>0},ff.isZero=function(){return 0===this.s},ff.lessThan=ff.lt=function(e){return 0>this.cmp(e)},ff.lessThanOrEqualTo=ff.lte=function(e){return 1>this.cmp(e)},ff.logarithm=ff.log=function(e){var t,r=this.constructor,n=r.precision,o=n+5;if(void 0===e)e=new r(10);else if((e=new r(e)).s<1||e.eq(u3))throw Error(fo+"NaN");if(this.s<1)throw Error(fo+(this.s?"NaN":"-Infinity"));return this.eq(u3)?new r(0):(fn=!1,t=fy(fx(this,o),fx(e,o),o),fn=!0,fO(t,n))},ff.minus=ff.sub=function(e){return e=new this.constructor(e),this.s==e.s?fj(this,e):fp(this,(e.s=-e.s,e))},ff.modulo=ff.mod=function(e){var t,r=this.constructor,n=r.precision;if(!(e=new r(e)).s)throw Error(fo+"NaN");return this.s?(fn=!1,t=fy(this,e,0,1).times(e),fn=!0,this.minus(t)):fO(new r(this),n)},ff.naturalExponential=ff.exp=function(){return fv(this)},ff.naturalLogarithm=ff.ln=function(){return fx(this)},ff.negated=ff.neg=function(){var e=new this.constructor(this);return e.s=-e.s||0,e},ff.plus=ff.add=function(e){return e=new this.constructor(e),this.s==e.s?fp(this,e):fj(this,(e.s=-e.s,e))},ff.precision=ff.sd=function(e){var t,r,n;if(void 0!==e&&!!e!==e&&1!==e&&0!==e)throw Error(fi+e);if(t=fm(this)+1,r=7*(n=this.d.length-1)+1,n=this.d[n]){for(;n%10==0;n/=10)r--;for(n=this.d[0];n>=10;n/=10)r++}return e&&t>r?t:r},ff.squareRoot=ff.sqrt=function(){var e,t,r,n,o,i,a,s=this.constructor;if(this.s<1){if(!this.s)return new s(0);throw Error(fo+"NaN")}for(e=fm(this),fn=!1,0==(o=Math.sqrt(+this))||o==1/0?(((t=fh(this.d)).length+e)%2==0&&(t+="0"),o=Math.sqrt(t),e=fs((e+1)/2)-(e<0||e%2),n=new s(t=o==1/0?"5e"+e:(t=o.toExponential()).slice(0,t.indexOf("e")+1)+e)):n=new s(o.toString()),o=a=(r=s.precision)+3;;)if(n=(i=n).plus(fy(this,i,a+2)).times(.5),fh(i.d).slice(0,a)===(t=fh(n.d)).slice(0,a)){if(t=t.slice(a-3,a+1),o==a&&"4999"==t){if(fO(i,r+1,0),i.times(i).eq(this)){n=i;break}}else if("9999"!=t)break;a+=4}return fn=!0,fO(n,r)},ff.times=ff.mul=function(e){var t,r,n,o,i,a,s,l,c,u=this.constructor,f=this.d,p=(e=new u(e)).d;if(!this.s||!e.s)return new u(0);for(e.s*=this.s,r=this.e+e.e,(l=f.length)<(c=p.length)&&(i=f,f=p,p=i,a=l,l=c,c=a),i=[],n=a=l+c;n--;)i.push(0);for(n=c;--n>=0;){for(t=0,o=l+n;o>n;)s=i[o]+p[n]*f[o-n-1]+t,i[o--]=s%1e7|0,t=s/1e7|0;i[o]=(i[o]+t)%1e7|0}for(;!i[--a];)i.pop();return t?++r:i.shift(),e.d=i,e.e=r,fn?fO(e,u.precision):e},ff.toDecimalPlaces=ff.todp=function(e,t){var r=this,n=r.constructor;return(r=new n(r),void 0===e)?r:(fd(e,0,1e9),void 0===t?t=n.rounding:fd(t,0,8),fO(r,e+fm(r)+1,t))},ff.toExponential=function(e,t){var r,n=this,o=n.constructor;return void 0===e?r=fS(n,!0):(fd(e,0,1e9),void 0===t?t=o.rounding:fd(t,0,8),r=fS(n=fO(new o(n),e+1,t),!0,e+1)),r},ff.toFixed=function(e,t){var r,n,o=this.constructor;return void 0===e?fS(this):(fd(e,0,1e9),void 0===t?t=o.rounding:fd(t,0,8),r=fS((n=fO(new o(this),e+fm(this)+1,t)).abs(),!1,e+fm(n)+1),this.isneg()&&!this.isZero()?"-"+r:r)},ff.toInteger=ff.toint=function(){var e=this.constructor;return fO(new e(this),fm(this)+1,e.rounding)},ff.toNumber=function(){return+this},ff.toPower=ff.pow=function(e){var t,r,n,o,i,a,s=this,l=s.constructor,c=+(e=new l(e));if(!e.s)return new l(u3);if(!(s=new l(s)).s){if(e.s<1)throw Error(fo+"Infinity");return s}if(s.eq(u3))return s;if(n=l.precision,e.eq(u3))return fO(s,n);if(a=(t=e.e)>=(r=e.d.length-1),i=s.s,a){if((r=c<0?-c:c)<=0x1fffffffffffff){for(o=new l(u3),t=Math.ceil(n/7+4),fn=!1;r%2&&fP((o=o.times(s)).d,t),0!==(r=fs(r/2));)fP((s=s.times(s)).d,t);return fn=!0,e.s<0?new l(u3).div(o):fO(o,n)}}else if(i<0)throw Error(fo+"NaN");return i=i<0&&1&e.d[Math.max(t,r)]?-1:1,s.s=1,fn=!1,o=e.times(fx(s,n+12)),fn=!0,(o=fv(o)).s=i,o},ff.toPrecision=function(e,t){var r,n,o=this,i=o.constructor;return void 0===e?(r=fm(o),n=fS(o,r<=i.toExpNeg||r>=i.toExpPos)):(fd(e,1,1e9),void 0===t?t=i.rounding:fd(t,0,8),r=fm(o=fO(new i(o),e,t)),n=fS(o,e<=r||r<=i.toExpNeg,e)),n},ff.toSignificantDigits=ff.tosd=function(e,t){var r=this.constructor;return void 0===e?(e=r.precision,t=r.rounding):(fd(e,1,1e9),void 0===t?t=r.rounding:fd(t,0,8)),fO(new r(this),e,t)},ff.toString=ff.valueOf=ff.val=ff.toJSON=ff[Symbol.for("nodejs.util.inspect.custom")]=function(){var e=fm(this),t=this.constructor;return fS(this,e<=t.toExpNeg||e>=t.toExpPos)};var fy=function(){function e(e,t){var r,n=0,o=e.length;for(e=e.slice();o--;)r=e[o]*t+n,e[o]=r%1e7|0,n=r/1e7|0;return n&&e.unshift(n),e}function t(e,t,r,n){var o,i;if(r!=n)i=r>n?1:-1;else for(o=i=0;o<r;o++)if(e[o]!=t[o]){i=e[o]>t[o]?1:-1;break}return i}function r(e,t,r){for(var n=0;r--;)e[r]-=n,n=+(e[r]<t[r]),e[r]=1e7*n+e[r]-t[r];for(;!e[0]&&e.length>1;)e.shift()}return function(n,o,i,a){var s,l,c,u,f,p,d,h,y,v,m,b,g,x,w,O,j,S,P=n.constructor,E=n.s==o.s?1:-1,A=n.d,k=o.d;if(!n.s)return new P(n);if(!o.s)throw Error(fo+"Division by zero");for(c=0,l=n.e-o.e,j=k.length,w=A.length,h=(d=new P(E)).d=[];k[c]==(A[c]||0);)++c;if(k[c]>(A[c]||0)&&--l,(b=null==i?i=P.precision:a?i+(fm(n)-fm(o))+1:i)<0)return new P(0);if(b=b/7+2|0,c=0,1==j)for(u=0,k=k[0],b++;(c<w||u)&&b--;c++)g=1e7*u+(A[c]||0),h[c]=g/k|0,u=g%k|0;else{for((u=1e7/(k[0]+1)|0)>1&&(k=e(k,u),A=e(A,u),j=k.length,w=A.length),x=j,v=(y=A.slice(0,j)).length;v<j;)y[v++]=0;(S=k.slice()).unshift(0),O=k[0],k[1]>=1e7/2&&++O;do u=0,(s=t(k,y,j,v))<0?(m=y[0],j!=v&&(m=1e7*m+(y[1]||0)),(u=m/O|0)>1?(u>=1e7&&(u=1e7-1),p=(f=e(k,u)).length,v=y.length,1==(s=t(f,y,p,v))&&(u--,r(f,j<p?S:k,p))):(0==u&&(s=u=1),f=k.slice()),(p=f.length)<v&&f.unshift(0),r(y,f,v),-1==s&&(v=y.length,(s=t(k,y,j,v))<1&&(u++,r(y,j<v?S:k,v))),v=y.length):0===s&&(u++,y=[0]),h[c++]=u,s&&y[0]?y[v++]=A[x]||0:(y=[A[x]],v=1);while((x++<w||void 0!==y[0])&&b--)}return h[0]||h.shift(),d.e=l,fO(d,a?i+fm(d)+1:i)}}();function fv(e,t){var r,n,o,i,a,s=0,l=0,c=e.constructor,u=c.precision;if(fm(e)>16)throw Error(fa+fm(e));if(!e.s)return new c(u3);for(null==t?(fn=!1,a=u):a=t,i=new c(.03125);e.abs().gte(.1);)e=e.times(i),l+=5;for(a+=Math.log(fl(2,l))/Math.LN10*2+5|0,r=n=o=new c(u3),c.precision=a;;){if(n=fO(n.times(e),a),r=r.times(++s),fh((i=o.plus(fy(n,r,a))).d).slice(0,a)===fh(o.d).slice(0,a)){for(;l--;)o=fO(o.times(o),a);return c.precision=u,null==t?(fn=!0,fO(o,u)):o}o=i}}function fm(e){for(var t=7*e.e,r=e.d[0];r>=10;r/=10)t++;return t}function fb(e,t,r){if(t>e.LN10.sd())throw fn=!0,r&&(e.precision=r),Error(fo+"LN10 precision limit exceeded");return fO(new e(e.LN10),t)}function fg(e){for(var t="";e--;)t+="0";return t}function fx(e,t){var r,n,o,i,a,s,l,c,u,f=1,p=e,d=p.d,h=p.constructor,y=h.precision;if(p.s<1)throw Error(fo+(p.s?"NaN":"-Infinity"));if(p.eq(u3))return new h(0);if(null==t?(fn=!1,c=y):c=t,p.eq(10))return null==t&&(fn=!0),fb(h,c);if(h.precision=c+=10,n=(r=fh(d)).charAt(0),!(15e14>Math.abs(i=fm(p))))return l=fb(h,c+2,y).times(i+""),p=fx(new h(n+"."+r.slice(1)),c-10).plus(l),h.precision=y,null==t?(fn=!0,fO(p,y)):p;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=fh((p=p.times(e)).d)).charAt(0),f++;for(i=fm(p),n>1?(p=new h("0."+r),i++):p=new h(n+"."+r.slice(1)),s=a=p=fy(p.minus(u3),p.plus(u3),c),u=fO(p.times(p),c),o=3;;){if(a=fO(a.times(u),c),fh((l=s.plus(fy(a,new h(o),c))).d).slice(0,c)===fh(s.d).slice(0,c))return s=s.times(2),0!==i&&(s=s.plus(fb(h,c+2,y).times(i+""))),s=fy(s,new h(f),c),h.precision=y,null==t?(fn=!0,fO(s,y)):s;s=l,o+=2}}function fw(e,t){var r,n,o;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;48===t.charCodeAt(n);)++n;for(o=t.length;48===t.charCodeAt(o-1);)--o;if(t=t.slice(n,o)){if(o-=n,e.e=fs((r=r-n-1)/7),e.d=[],n=(r+1)%7,r<0&&(n+=7),n<o){for(n&&e.d.push(+t.slice(0,n)),o-=7;n<o;)e.d.push(+t.slice(n,n+=7));n=7-(t=t.slice(n)).length}else n-=o;for(;n--;)t+="0";if(e.d.push(+t),fn&&(e.e>fu||e.e<-fu))throw Error(fa+r)}else e.s=0,e.e=0,e.d=[0];return e}function fO(e,t,r){var n,o,i,a,s,l,c,u,f=e.d;for(a=1,i=f[0];i>=10;i/=10)a++;if((n=t-a)<0)n+=7,o=t,c=f[u=0];else{if((u=Math.ceil((n+1)/7))>=(i=f.length))return e;for(a=1,c=i=f[u];i>=10;i/=10)a++;n%=7,o=n-7+a}if(void 0!==r&&(s=c/(i=fl(10,a-o-1))%10|0,l=t<0||void 0!==f[u+1]||c%i,l=r<4?(s||l)&&(0==r||r==(e.s<0?3:2)):s>5||5==s&&(4==r||l||6==r&&(n>0?o>0?c/fl(10,a-o):0:f[u-1])%10&1||r==(e.s<0?8:7))),t<1||!f[0])return l?(i=fm(e),f.length=1,t=t-i-1,f[0]=fl(10,(7-t%7)%7),e.e=fs(-t/7)||0):(f.length=1,f[0]=e.e=e.s=0),e;if(0==n?(f.length=u,i=1,u--):(f.length=u+1,i=fl(10,7-n),f[u]=o>0?(c/fl(10,a-o)%fl(10,o)|0)*i:0),l)for(;;)if(0==u){1e7==(f[0]+=i)&&(f[0]=1,++e.e);break}else{if(f[u]+=i,1e7!=f[u])break;f[u--]=0,i=1}for(n=f.length;0===f[--n];)f.pop();if(fn&&(e.e>fu||e.e<-fu))throw Error(fa+fm(e));return e}function fj(e,t){var r,n,o,i,a,s,l,c,u,f,p=e.constructor,d=p.precision;if(!e.s||!t.s)return t.s?t.s=-t.s:t=new p(e),fn?fO(t,d):t;if(l=e.d,f=t.d,n=t.e,c=e.e,l=l.slice(),a=c-n){for((u=a<0)?(r=l,a=-a,s=f.length):(r=f,n=c,s=l.length),a>(o=Math.max(Math.ceil(d/7),s)+2)&&(a=o,r.length=1),r.reverse(),o=a;o--;)r.push(0);r.reverse()}else{for((u=(o=l.length)<(s=f.length))&&(s=o),o=0;o<s;o++)if(l[o]!=f[o]){u=l[o]<f[o];break}a=0}for(u&&(r=l,l=f,f=r,t.s=-t.s),s=l.length,o=f.length-s;o>0;--o)l[s++]=0;for(o=f.length;o>a;){if(l[--o]<f[o]){for(i=o;i&&0===l[--i];)l[i]=1e7-1;--l[i],l[o]+=1e7}l[o]-=f[o]}for(;0===l[--s];)l.pop();for(;0===l[0];l.shift())--n;return l[0]?(t.d=l,t.e=n,fn?fO(t,d):t):new p(0)}function fS(e,t,r){var n,o=fm(e),i=fh(e.d),a=i.length;return t?(r&&(n=r-a)>0?i=i.charAt(0)+"."+i.slice(1)+fg(n):a>1&&(i=i.charAt(0)+"."+i.slice(1)),i=i+(o<0?"e":"e+")+o):o<0?(i="0."+fg(-o-1)+i,r&&(n=r-a)>0&&(i+=fg(n))):o>=a?(i+=fg(o+1-a),r&&(n=r-o-1)>0&&(i=i+"."+fg(n))):((n=o+1)<a&&(i=i.slice(0,n)+"."+i.slice(n)),r&&(n=r-a)>0&&(o+1===a&&(i+="."),i+=fg(n))),e.s<0?"-"+i:i}function fP(e,t){if(e.length>t)return e.length=t,!0}function fE(e){if(!e||"object"!=typeof e)throw Error(fo+"Object expected");var t,r,n,o=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(t=0;t<o.length;t+=3)if(void 0!==(n=e[r=o[t]]))if(fs(n)===n&&n>=o[t+1]&&n<=o[t+2])this[r]=n;else throw Error(fi+r+": "+n);if(void 0!==(n=e[r="LN10"]))if(n==Math.LN10)this[r]=new this(n);else throw Error(fi+r+": "+n);return this}var u5=function e(t){var r,n,o;function i(e){if(!(this instanceof i))return new i(e);if(this.constructor=i,e instanceof i){this.s=e.s,this.e=e.e,this.d=(e=e.d)?e.slice():e;return}if("number"==typeof e){if(0*e!=0)throw Error(fi+e);if(e>0)this.s=1;else if(e<0)e=-e,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(e===~~e&&e<1e7){this.e=0,this.d=[e];return}return fw(this,e.toString())}if("string"!=typeof e)throw Error(fi+e);if(45===e.charCodeAt(0)?(e=e.slice(1),this.s=-1):this.s=1,fc.test(e))fw(this,e);else throw Error(fi+e)}if(i.prototype=ff,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.clone=e,i.config=i.set=fE,void 0===t&&(t={}),t)for(r=0,o=["precision","rounding","toExpNeg","toExpPos","LN10"];r<o.length;)t.hasOwnProperty(n=o[r++])||(t[n]=this[n]);return i.config(t),i}({precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"});u3=new u5(1);let fA=u5;function fk(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var fM=function(e){return e},fT={},f_=function(e){return e===fT},fC=function(e){return function t(){return 0==arguments.length||1==arguments.length&&f_(arguments.length<=0?void 0:arguments[0])?t:e.apply(void 0,arguments)}},fN=function(e){return function e(t,r){return 1===t?r:fC(function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];var a=o.filter(function(e){return e!==fT}).length;return a>=t?r.apply(void 0,o):e(t-a,fC(function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];var i=o.map(function(e){return f_(e)?t.shift():e});return r.apply(void 0,((function(e){if(Array.isArray(e))return fk(e)})(i)||function(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(i)||function(e,t){if(e){if("string"==typeof e)return fk(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return fk(e,t)}}(i)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()).concat(t))}))})}(e.length,e)},fD=function(e,t){for(var r=[],n=e;n<t;++n)r[n-e]=n;return r},fI=fN(function(e,t){return Array.isArray(t)?t.map(e):Object.keys(t).map(function(e){return t[e]}).map(e)}),fR=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];if(!t.length)return fM;var n=t.reverse(),o=n[0],i=n.slice(1);return function(){return i.reduce(function(e,t){return t(e)},o.apply(void 0,arguments))}},fL=function(e){return Array.isArray(e)?e.reverse():e.split("").reverse.join("")},fB=function(e){var t=null,r=null;return function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];return t&&o.every(function(e,r){return e===t[r]})?r:(t=o,r=e.apply(void 0,o))}};fN(function(e,t,r){var n=+e;return n+r*(t-n)}),fN(function(e,t,r){var n=t-e;return(r-e)/(n=n||1/0)}),fN(function(e,t,r){var n=t-e;return Math.max(0,Math.min(1,(r-e)/(n=n||1/0)))});let fF={rangeStep:function(e,t,r){for(var n=new fA(e),o=0,i=[];n.lt(t)&&o<1e5;)i.push(n.toNumber()),n=n.add(r),o++;return i},getDigitCount:function(e){var t;return 0===e?1:Math.floor(new fA(e).abs().log(10).toNumber())+1}};function fz(e){return function(e){if(Array.isArray(e))return fU(e)}(e)||function(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(e)||fW(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function f$(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e)){var r=[],n=!0,o=!1,i=void 0;try{for(var a,s=e[Symbol.iterator]();!(n=(a=s.next()).done)&&(r.push(a.value),!t||r.length!==t);n=!0);}catch(e){o=!0,i=e}finally{try{n||null==s.return||s.return()}finally{if(o)throw i}}return r}}(e,t)||fW(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function fW(e,t){if(e){if("string"==typeof e)return fU(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return fU(e,t)}}function fU(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function fq(e){var t=f$(e,2),r=t[0],n=t[1],o=r,i=n;return r>n&&(o=n,i=r),[o,i]}function fY(e,t,r){if(e.lte(0))return new fA(0);var n=fF.getDigitCount(e.toNumber()),o=new fA(10).pow(n),i=e.div(o),a=1!==n?.05:.1,s=new fA(Math.ceil(i.div(a).toNumber())).add(r).mul(a).mul(o);return t?s:new fA(Math.ceil(s))}function fH(e,t,r){var n=1,o=new fA(e);if(!o.isint()&&r){var i=Math.abs(e);i<1?(n=new fA(10).pow(fF.getDigitCount(e)-1),o=new fA(Math.floor(o.div(n).toNumber())).mul(n)):i>1&&(o=new fA(Math.floor(e)))}else 0===e?o=new fA(Math.floor((t-1)/2)):r||(o=new fA(Math.floor(e)));var a=Math.floor((t-1)/2);return fR(fI(function(e){return o.add(new fA(e-a).mul(n)).toNumber()}),fD)(0,t)}var fX=fB(function(e){var t=f$(e,2),r=t[0],n=t[1],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(o,2),s=f$(fq([r,n]),2),l=s[0],c=s[1];if(l===-1/0||c===1/0){var u=c===1/0?[l].concat(fz(fD(0,o-1).map(function(){return 1/0}))):[].concat(fz(fD(0,o-1).map(function(){return-1/0})),[c]);return r>n?fL(u):u}if(l===c)return fH(l,o,i);var f=function e(t,r,n,o){var i,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((r-t)/(n-1)))return{step:new fA(0),tickMin:new fA(0),tickMax:new fA(0)};var s=fY(new fA(r).sub(t).div(n-1),o,a),l=Math.ceil((i=t<=0&&r>=0?new fA(0):(i=new fA(t).add(r).div(2)).sub(new fA(i).mod(s))).sub(t).div(s).toNumber()),c=Math.ceil(new fA(r).sub(i).div(s).toNumber()),u=l+c+1;return u>n?e(t,r,n,o,a+1):(u<n&&(c=r>0?c+(n-u):c,l=r>0?l:l+(n-u)),{step:s,tickMin:i.sub(new fA(l).mul(s)),tickMax:i.add(new fA(c).mul(s))})}(l,c,a,i),p=f.step,d=f.tickMin,h=f.tickMax,y=fF.rangeStep(d,h.add(new fA(.1).mul(p)),p);return r>n?fL(y):y});fB(function(e){var t=f$(e,2),r=t[0],n=t[1],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(o,2),s=f$(fq([r,n]),2),l=s[0],c=s[1];if(l===-1/0||c===1/0)return[r,n];if(l===c)return fH(l,o,i);var u=fY(new fA(c).sub(l).div(a-1),i,0),f=fR(fI(function(e){return new fA(l).add(new fA(e).mul(u)).toNumber()}),fD)(0,a).filter(function(e){return e>=l&&e<=c});return r>n?fL(f):f});var fV=fB(function(e,t){var r=f$(e,2),n=r[0],o=r[1],i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=f$(fq([n,o]),2),s=a[0],l=a[1];if(s===-1/0||l===1/0)return[n,o];if(s===l)return[s];var c=Math.max(t,2),u=fY(new fA(l).sub(s).div(c-1),i,0),f=[].concat(fz(fF.rangeStep(new fA(s),new fA(l).sub(new fA(.99).mul(u)),u)),[l]);return n>o?fL(f):f}),fG=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function fK(e){return(fK="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function fZ(){return(fZ=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function fJ(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function fQ(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(fQ=function(){return!!e})()}function f0(e){return(f0=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function f1(e,t){return(f1=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function f2(e,t,r){return(t=f5(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function f5(e){var t=function(e,t){if("object"!=fK(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=fK(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==fK(t)?t:t+""}var f3=function(e){var t;function r(){var e,t;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return e=r,t=arguments,e=f0(e),function(e,t){if(t&&("object"===fK(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");var r=e;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,fQ()?Reflect.construct(e,t||[],f0(this).constructor):e.apply(this,t))}if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(e&&e.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),e&&f1(r,e),t=[{key:"render",value:function(){var e=this.props,t=e.offset,r=e.layout,n=e.width,o=e.dataKey,i=e.data,a=e.dataPointFormatter,s=e.xAxis,l=e.yAxis,c=nj(function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,fG),!1);"x"===this.props.direction&&"number"!==s.type&&nL(!1);var f=i.map(function(e){var i,f,p=a(e,o),d=p.x,h=p.y,y=p.value,v=p.errorVal;if(!v)return null;var m=[];if(Array.isArray(v)){var b=function(e){if(Array.isArray(e))return e}(v)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,s=[],l=!0,c=!1;try{i=(r=r.call(e)).next,!1;for(;!(l=(n=i.call(r)).done)&&(s.push(n.value),s.length!==t);l=!0);}catch(e){c=!0,o=e}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return s}}(v,2)||function(e,t){if(e){if("string"==typeof e)return fJ(e,2);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return fJ(e,t)}}(v,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();i=b[0],f=b[1]}else i=f=v;if("vertical"===r){var g=s.scale,x=h+t,w=x+n,O=x-n,j=g(y-i),S=g(y+f);m.push({x1:S,y1:w,x2:S,y2:O}),m.push({x1:j,y1:x,x2:S,y2:x}),m.push({x1:j,y1:w,x2:j,y2:O})}else if("horizontal"===r){var P=l.scale,E=d+t,A=E-n,k=E+n,M=P(y-i),T=P(y+f);m.push({x1:A,y1:T,x2:k,y2:T}),m.push({x1:E,y1:M,x2:E,y2:T}),m.push({x1:A,y1:M,x2:k,y2:M})}return u().createElement(nU,fZ({className:"recharts-errorBar",key:"bar-".concat(m.map(function(e){return"".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2)}))},c),m.map(function(e){return u().createElement("line",fZ({},e,{key:"line-".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2)}))}))});return u().createElement(nU,{className:"recharts-errorBars"},f)}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,f5(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(u().Component);function f4(e){return(f4="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function f8(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function f6(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?f8(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=f4(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=f4(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==f4(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):f8(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}f2(f3,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"}),f2(f3,"displayName","ErrorBar");var f7=function(e){var t,r=e.children,n=e.formattedGraphicalItems,o=e.legendWidth,i=e.legendContent,a=ng(r,ii);if(!a)return null;var s=ii.defaultProps,l=void 0!==s?f6(f6({},s),a.props):{};return t=a.props&&a.props.payload?a.props&&a.props.payload:"children"===i?(n||[]).reduce(function(e,t){var r=t.item,n=t.props,o=n.sectors||n.data||[];return e.concat(o.map(function(e){return{type:a.props.iconType||r.props.legendType,value:e.name,color:e.fill,payload:e}}))},[]):(n||[]).map(function(e){var t=e.item,r=t.type.defaultProps,n=void 0!==r?f6(f6({},r),t.props):{},o=n.dataKey,i=n.name,a=n.legendType;return{inactive:n.hide,dataKey:o,type:l.iconType||a||"square",color:pl(t),value:i||o,payload:n}}),f6(f6(f6({},l),ii.getWithHeight(a,o)),{},{payload:t,item:a})};function f9(e){return(f9="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function pe(e){return function(e){if(Array.isArray(e))return pt(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return pt(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return pt(e,t)}}(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function pt(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function pr(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function pn(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?pr(Object(r),!0).forEach(function(t){po(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):pr(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function po(e,t,r){var n;return(n=function(e,t){if("object"!=f9(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=f9(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"==f9(n)?n:n+"")in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function pi(e,t,r){return r8()(e)||r8()(t)?r:rG(t)?rU()(e,t,r):r7()(t)?t(e):r}function pa(e,t,r,n){var o=fe()(e,function(e){return pi(e,t)});if("number"===r){var i=o.filter(function(e){return rV(e)||parseFloat(e)});return i.length?[u7()(i),u8()(i)]:[1/0,-1/0]}return(n?o.filter(function(e){return!r8()(e)}):o).map(function(e){return rG(e)||e instanceof Date?e:""})}var ps=function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2?arguments[2]:void 0,o=arguments.length>3?arguments[3]:void 0,i=-1,a=null!=(t=null==r?void 0:r.length)?t:0;if(a<=1)return 0;if(o&&"angleAxis"===o.axisType&&1e-6>=Math.abs(Math.abs(o.range[1]-o.range[0])-360))for(var s=o.range,l=0;l<a;l++){var c=l>0?n[l-1].coordinate:n[a-1].coordinate,u=n[l].coordinate,f=l>=a-1?n[0].coordinate:n[l+1].coordinate,p=void 0;if(rH(u-c)!==rH(f-u)){var d=[];if(rH(f-u)===rH(s[1]-s[0])){p=f;var h=u+s[1]-s[0];d[0]=Math.min(h,(h+c)/2),d[1]=Math.max(h,(h+c)/2)}else{p=c;var y=f+s[1]-s[0];d[0]=Math.min(u,(y+u)/2),d[1]=Math.max(u,(y+u)/2)}var v=[Math.min(u,(p+u)/2),Math.max(u,(p+u)/2)];if(e>v[0]&&e<=v[1]||e>=d[0]&&e<=d[1]){i=n[l].index;break}}else{var m=Math.min(c,f),b=Math.max(c,f);if(e>(m+u)/2&&e<=(b+u)/2){i=n[l].index;break}}}else for(var g=0;g<a;g++)if(0===g&&e<=(r[g].coordinate+r[g+1].coordinate)/2||g>0&&g<a-1&&e>(r[g].coordinate+r[g-1].coordinate)/2&&e<=(r[g].coordinate+r[g+1].coordinate)/2||g===a-1&&e>(r[g].coordinate+r[g-1].coordinate)/2){i=r[g].index;break}return i},pl=function(e){var t,r,n=e.type.displayName,o=null!=(t=e.type)&&t.defaultProps?pn(pn({},e.type.defaultProps),e.props):e.props,i=o.stroke,a=o.fill;switch(n){case"Line":r=i;break;case"Area":case"Radar":r=i&&"none"!==i?i:a;break;default:r=a}return r},pc=function(e){var t=e.barSize,r=e.totalSize,n=e.stackGroups,o=void 0===n?{}:n;if(!o)return{};for(var i={},a=Object.keys(o),s=0,l=a.length;s<l;s++)for(var c=o[a[s]].stackGroups,u=Object.keys(c),f=0,p=u.length;f<p;f++){var d=c[u[f]],h=d.items,y=d.cateAxisId,v=h.filter(function(e){return nh(e.type).indexOf("Bar")>=0});if(v&&v.length){var m=v[0].type.defaultProps,b=void 0!==m?pn(pn({},m),v[0].props):v[0].props,g=b.barSize,x=b[y];i[x]||(i[x]=[]);var w=r8()(g)?t:g;i[x].push({item:v[0],stackList:v.slice(1),barSize:r8()(w)?void 0:rJ(w,r,0)})}}return i},pu=function(e){var t,r=e.barGap,n=e.barCategoryGap,o=e.bandSize,i=e.sizeList,a=void 0===i?[]:i,s=e.maxBarSize,l=a.length;if(l<1)return null;var c=rJ(r,o,0,!0),u=[];if(a[0].barSize===+a[0].barSize){var f=!1,p=o/l,d=a.reduce(function(e,t){return e+t.barSize||0},0);(d+=(l-1)*c)>=o&&(d-=(l-1)*c,c=0),d>=o&&p>0&&(f=!0,p*=.9,d=l*p);var h={offset:((o-d)/2|0)-c,size:0};t=a.reduce(function(e,t){var r={item:t.item,position:{offset:h.offset+h.size+c,size:f?p:t.barSize}},n=[].concat(pe(e),[r]);return h=n[n.length-1].position,t.stackList&&t.stackList.length&&t.stackList.forEach(function(e){n.push({item:e,position:h})}),n},u)}else{var y=rJ(n,o,0,!0);o-2*y-(l-1)*c<=0&&(c=0);var v=(o-2*y-(l-1)*c)/l;v>1&&(v>>=0);var m=s===+s?Math.min(v,s):v;t=a.reduce(function(e,t,r){var n=[].concat(pe(e),[{item:t.item,position:{offset:y+(v+c)*r+(v-m)/2,size:m}}]);return t.stackList&&t.stackList.length&&t.stackList.forEach(function(e){n.push({item:e,position:n[n.length-1].position})}),n},u)}return t},pf=function(e,t,r,n){var o=r.children,i=r.width,a=r.margin,s=f7({children:o,legendWidth:i-(a.left||0)-(a.right||0)});if(s){var l=n||{},c=l.width,u=l.height,f=s.align,p=s.verticalAlign,d=s.layout;if(("vertical"===d||"horizontal"===d&&"middle"===p)&&"center"!==f&&rV(e[f]))return pn(pn({},e),{},po({},f,e[f]+(c||0)));if(("horizontal"===d||"vertical"===d&&"center"===f)&&"middle"!==p&&rV(e[p]))return pn(pn({},e),{},po({},p,e[p]+(u||0)))}return e},pp=function(e,t,r,n,o){var i=nb(t.props.children,f3).filter(function(e){var t;return t=e.props.direction,!!r8()(o)||("horizontal"===n?"yAxis"===o:"vertical"===n||"x"===t?"xAxis"===o:"y"!==t||"yAxis"===o)});if(i&&i.length){var a=i.map(function(e){return e.props.dataKey});return e.reduce(function(e,t){var n=pi(t,r);if(r8()(n))return e;var o=Array.isArray(n)?[u7()(n),u8()(n)]:[n,n],i=a.reduce(function(e,r){var n=pi(t,r,0),i=o[0]-Math.abs(Array.isArray(n)?n[0]:n),a=o[1]+Math.abs(Array.isArray(n)?n[1]:n);return[Math.min(i,e[0]),Math.max(a,e[1])]},[1/0,-1/0]);return[Math.min(i[0],e[0]),Math.max(i[1],e[1])]},[1/0,-1/0])}return null},pd=function(e,t,r,n,o){var i=t.map(function(t){return pp(e,t,r,o,n)}).filter(function(e){return!r8()(e)});return i&&i.length?i.reduce(function(e,t){return[Math.min(e[0],t[0]),Math.max(e[1],t[1])]},[1/0,-1/0]):null},ph=function(e,t,r,n,o){var i=t.map(function(t){var i=t.props.dataKey;return"number"===r&&i&&pp(e,t,i,n)||pa(e,i,r,o)});if("number"===r)return i.reduce(function(e,t){return[Math.min(e[0],t[0]),Math.max(e[1],t[1])]},[1/0,-1/0]);var a={};return i.reduce(function(e,t){for(var r=0,n=t.length;r<n;r++)a[t[r]]||(a[t[r]]=!0,e.push(t[r]));return e},[])},py=function(e,t){return"horizontal"===e&&"xAxis"===t||"vertical"===e&&"yAxis"===t||"centric"===e&&"angleAxis"===t||"radial"===e&&"radiusAxis"===t},pv=function(e,t,r,n){if(n)return e.map(function(e){return e.coordinate});var o,i,a=e.map(function(e){return e.coordinate===t&&(o=!0),e.coordinate===r&&(i=!0),e.coordinate});return o||a.push(t),i||a.push(r),a},pm=function(e,t,r){if(!e)return null;var n=e.scale,o=e.duplicateDomain,i=e.type,a=e.range,s="scaleBand"===e.realScaleType?n.bandwidth()/2:2,l=(t||r)&&"category"===i&&n.bandwidth?n.bandwidth()/s:0;return(l="angleAxis"===e.axisType&&(null==a?void 0:a.length)>=2?2*rH(a[0]-a[1])*l:l,t&&(e.ticks||e.niceTicks))?(e.ticks||e.niceTicks).map(function(e){return{coordinate:n(o?o.indexOf(e):e)+l,value:e,offset:l}}).filter(function(e){return!r$()(e.coordinate)}):e.isCategorical&&e.categoricalDomain?e.categoricalDomain.map(function(e,t){return{coordinate:n(e)+l,value:e,index:t,offset:l}}):n.ticks&&!r?n.ticks(e.tickCount).map(function(e){return{coordinate:n(e)+l,value:e,offset:l}}):n.domain().map(function(e,t){return{coordinate:n(e)+l,value:o?o[e]:e,index:t,offset:l}})},pb=new WeakMap,pg=function(e,t){if("function"!=typeof t)return e;pb.has(e)||pb.set(e,new WeakMap);var r=pb.get(e);if(r.has(t))return r.get(t);var n=function(){e.apply(void 0,arguments),t.apply(void 0,arguments)};return r.set(t,n),n},px=function(e,t,r){var n=e.scale,o=e.type,i=e.layout,a=e.axisType;if("auto"===n)return"radial"===i&&"radiusAxis"===a?{scale:aI(),realScaleType:"band"}:"radial"===i&&"angleAxis"===a?{scale:lw(),realScaleType:"linear"}:"category"===o&&t&&(t.indexOf("LineChart")>=0||t.indexOf("AreaChart")>=0||t.indexOf("ComposedChart")>=0&&!r)?{scale:aR(),realScaleType:"point"}:"category"===o?{scale:aI(),realScaleType:"band"}:{scale:lw(),realScaleType:"linear"};if(rF()(n)){var l="scale".concat(ov()(n));return{scale:(s[l]||aR)(),realScaleType:s[l]?l:"point"}}return r7()(n)?{scale:n}:{scale:aR(),realScaleType:"point"}},pw=function(e){var t=e.domain();if(t&&!(t.length<=2)){var r=t.length,n=e.range(),o=Math.min(n[0],n[1])-1e-4,i=Math.max(n[0],n[1])+1e-4,a=e(t[0]),s=e(t[r-1]);(a<o||a>i||s<o||s>i)&&e.domain([t[0],t[r-1]])}},pO=function(e,t){if(!e)return null;for(var r=0,n=e.length;r<n;r++)if(e[r].item===t)return e[r].position;return null},pj=function(e,t){if(!t||2!==t.length||!rV(t[0])||!rV(t[1]))return e;var r=Math.min(t[0],t[1]),n=Math.max(t[0],t[1]),o=[e[0],e[1]];return(!rV(e[0])||e[0]<r)&&(o[0]=r),(!rV(e[1])||e[1]>n)&&(o[1]=n),o[0]>n&&(o[0]=n),o[1]<r&&(o[1]=r),o},pS={sign:function(e){var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var o=0,i=0,a=0;a<t;++a){var s=r$()(e[a][r][1])?e[a][r][0]:e[a][r][1];s>=0?(e[a][r][0]=o,e[a][r][1]=o+s,o=e[a][r][1]):(e[a][r][0]=i,e[a][r][1]=i+s,i=e[a][r][1])}},expand:function(e,t){if((n=e.length)>0){for(var r,n,o,i=0,a=e[0].length;i<a;++i){for(o=r=0;r<n;++r)o+=e[r][i][1]||0;if(o)for(r=0;r<n;++r)e[r][i][1]/=o}uF(e,t)}},none:uF,silhouette:function(e,t){if((r=e.length)>0){for(var r,n=0,o=e[t[0]],i=o.length;n<i;++n){for(var a=0,s=0;a<r;++a)s+=e[a][n][1]||0;o[n][1]+=o[n][0]=-s/2}uF(e,t)}},wiggle:function(e,t){if((o=e.length)>0&&(n=(r=e[t[0]]).length)>0){for(var r,n,o,i=0,a=1;a<n;++a){for(var s=0,l=0,c=0;s<o;++s){for(var u=e[t[s]],f=u[a][1]||0,p=(f-(u[a-1][1]||0))/2,d=0;d<s;++d){var h=e[t[d]];p+=(h[a][1]||0)-(h[a-1][1]||0)}l+=f,c+=p*f}r[a-1][1]+=r[a-1][0]=i,l&&(i-=c/l)}r[a-1][1]+=r[a-1][0]=i,uF(e,t)}},positive:function(e){var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var o=0,i=0;i<t;++i){var a=r$()(e[i][r][1])?e[i][r][0]:e[i][r][1];a>=0?(e[i][r][0]=o,e[i][r][1]=o+a,o=e[i][r][1]):(e[i][r][0]=0,e[i][r][1]=0)}}},pP=function(e,t,r){var n=t.map(function(e){return e.props.dataKey}),o=pS[r];return(function(){var e=oC([]),t=u$,r=uF,n=uW;function o(o){var i,a,s=Array.from(e.apply(this,arguments),uU),l=s.length,c=-1;for(let e of o)for(i=0,++c;i<l;++i)(s[i][c]=[0,+n(e,s[i].key,c,o)]).data=e;for(i=0,a=uz(t(s));i<l;++i)s[a[i]].index=i;return r(s,a),s}return o.keys=function(t){return arguments.length?(e="function"==typeof t?t:oC(Array.from(t)),o):e},o.value=function(e){return arguments.length?(n="function"==typeof e?e:oC(+e),o):n},o.order=function(e){return arguments.length?(t=null==e?u$:"function"==typeof e?e:oC(Array.from(e)),o):t},o.offset=function(e){return arguments.length?(r=null==e?uF:e,o):r},o})().keys(n).value(function(e,t){return+pi(e,t,0)}).order(u$).offset(o)(e)},pE=function(e,t,r,n,o,i){if(!e)return null;var a=(i?t.reverse():t).reduce(function(e,t){var o,i=null!=(o=t.type)&&o.defaultProps?pn(pn({},t.type.defaultProps),t.props):t.props,a=i.stackId;if(i.hide)return e;var s=i[r],l=e[s]||{hasStack:!1,stackGroups:{}};if(rG(a)){var c=l.stackGroups[a]||{numericAxisId:r,cateAxisId:n,items:[]};c.items.push(t),l.hasStack=!0,l.stackGroups[a]=c}else l.stackGroups[rZ("_stackId_")]={numericAxisId:r,cateAxisId:n,items:[t]};return pn(pn({},e),{},po({},s,l))},{});return Object.keys(a).reduce(function(t,i){var s=a[i];return s.hasStack&&(s.stackGroups=Object.keys(s.stackGroups).reduce(function(t,i){var a=s.stackGroups[i];return pn(pn({},t),{},po({},i,{numericAxisId:r,cateAxisId:n,items:a.items,stackedData:pP(e,a.items,o)}))},{})),pn(pn({},t),{},po({},i,s))},{})},pA=function(e,t){var r=t.realScaleType,n=t.type,o=t.tickCount,i=t.originalDomain,a=t.allowDecimals,s=r||t.scale;if("auto"!==s&&"linear"!==s)return null;if(o&&"number"===n&&i&&("auto"===i[0]||"auto"===i[1])){var l=e.domain();if(!l.length)return null;var c=fX(l,o,a);return e.domain([u7()(c),u8()(c)]),{niceTicks:c}}return o&&"number"===n?{niceTicks:fV(e.domain(),o,a)}:null},pk=function(e){var t=e.axis,r=e.ticks,n=e.offset,o=e.bandSize,i=e.entry,a=e.index;if("category"===t.type)return r[a]?r[a].coordinate+n:null;var s=pi(i,t.dataKey,t.domain[a]);return r8()(s)?null:t.scale(s)-o/2+n},pM=function(e){var t=e.numericAxis,r=t.scale.domain();if("number"===t.type){var n=Math.min(r[0],r[1]),o=Math.max(r[0],r[1]);return n<=0&&o>=0?0:o<0?o:n}return r[0]},pT=function(e,t){var r,n=(null!=(r=e.type)&&r.defaultProps?pn(pn({},e.type.defaultProps),e.props):e.props).stackId;if(rG(n)){var o=t[n];if(o){var i=o.items.indexOf(e);return i>=0?o.stackedData[i]:null}}return null},p_=function(e,t,r){return Object.keys(e).reduce(function(n,o){var i=e[o].stackedData.reduce(function(e,n){var o=n.slice(t,r+1).reduce(function(e,t){return[u7()(t.concat([e[0]]).filter(rV)),u8()(t.concat([e[1]]).filter(rV))]},[1/0,-1/0]);return[Math.min(e[0],o[0]),Math.max(e[1],o[1])]},[1/0,-1/0]);return[Math.min(i[0],n[0]),Math.max(i[1],n[1])]},[1/0,-1/0]).map(function(e){return e===1/0||e===-1/0?0:e})},pC=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,pN=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,pD=function(e,t,r){if(r7()(e))return e(t,r);if(!Array.isArray(e))return t;var n=[];if(rV(e[0]))n[0]=r?e[0]:Math.min(e[0],t[0]);else if(pC.test(e[0])){var o=+pC.exec(e[0])[1];n[0]=t[0]-o}else r7()(e[0])?n[0]=e[0](t[0]):n[0]=t[0];if(rV(e[1]))n[1]=r?e[1]:Math.max(e[1],t[1]);else if(pN.test(e[1])){var i=+pN.exec(e[1])[1];n[1]=t[1]+i}else r7()(e[1])?n[1]=e[1](t[1]):n[1]=t[1];return n},pI=function(e,t,r){if(e&&e.scale&&e.scale.bandwidth){var n=e.scale.bandwidth();if(!r||n>0)return n}if(e&&t&&t.length>=2){for(var o=nR()(t,function(e){return e.coordinate}),i=1/0,a=1,s=o.length;a<s;a++){var l=o[a],c=o[a-1];i=Math.min((l.coordinate||0)-(c.coordinate||0),i)}return i===1/0?0:i}return r?void 0:0},pR=function(e,t,r){return!e||!e.length||fr()(e,rU()(r,"type.defaultProps.domain"))?t:e},pL=function(e,t){var r=e.type.defaultProps?pn(pn({},e.type.defaultProps),e.props):e.props,n=r.dataKey,o=r.name,i=r.unit,a=r.formatter,s=r.tooltipType,l=r.chartType,c=r.hide;return pn(pn({},nj(e,!1)),{},{dataKey:n,unit:i,formatter:a,name:o||n,color:pl(e),value:pi(t,n),type:s,payload:t,chartType:l,hide:c})};function pB(e){return(pB="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function pF(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function pz(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?pF(Object(r),!0).forEach(function(t){p$(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):pF(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function p$(e,t,r){var n;return(n=function(e,t){if("object"!=pB(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=pB(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"==pB(n)?n:n+"")in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var pW=["Webkit","Moz","O","ms"],pU=function(e,t){if(!e)return null;var r=e.replace(/(\w)/,function(e){return e.toUpperCase()}),n=pW.reduce(function(e,n){return pz(pz({},e),{},p$({},n+r,t))},{});return n[e]=t,n};function pq(e){return(pq="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function pY(){return(pY=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function pH(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function pX(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?pH(Object(r),!0).forEach(function(t){pJ(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):pH(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function pV(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,pQ(n.key),n)}}function pG(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(pG=function(){return!!e})()}function pK(e){return(pK=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function pZ(e,t){return(pZ=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function pJ(e,t,r){return(t=pQ(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function pQ(e){var t=function(e,t){if("object"!=pq(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=pq(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==pq(t)?t:t+""}var p0=function(e){var t=e.data,r=e.startIndex,n=e.endIndex,o=e.x,i=e.width,a=e.travellerWidth;if(!t||!t.length)return{};var s=t.length,l=aR().domain(nD()(0,s)).range([o,o+i-a]),c=l.domain().map(function(e){return l(e)});return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:l(r),endX:l(n),scale:l,scaleValues:c}},p1=function(e){return e.changedTouches&&!!e.changedTouches.length},p2=function(e){var t,r;function n(e){var t,r,o;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");return r=n,o=[e],r=pK(r),pJ(t=function(e,t){if(t&&("object"===pq(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");var r=e;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,pG()?Reflect.construct(r,o||[],pK(this).constructor):r.apply(this,o)),"handleDrag",function(e){t.leaveTimer&&(clearTimeout(t.leaveTimer),t.leaveTimer=null),t.state.isTravellerMoving?t.handleTravellerMove(e):t.state.isSlideMoving&&t.handleSlideDrag(e)}),pJ(t,"handleTouchMove",function(e){null!=e.changedTouches&&e.changedTouches.length>0&&t.handleDrag(e.changedTouches[0])}),pJ(t,"handleDragEnd",function(){t.setState({isTravellerMoving:!1,isSlideMoving:!1},function(){var e=t.props,r=e.endIndex,n=e.onDragEnd,o=e.startIndex;null==n||n({endIndex:r,startIndex:o})}),t.detachDragEndListener()}),pJ(t,"handleLeaveWrapper",function(){(t.state.isTravellerMoving||t.state.isSlideMoving)&&(t.leaveTimer=window.setTimeout(t.handleDragEnd,t.props.leaveTimeOut))}),pJ(t,"handleEnterSlideOrTraveller",function(){t.setState({isTextActive:!0})}),pJ(t,"handleLeaveSlideOrTraveller",function(){t.setState({isTextActive:!1})}),pJ(t,"handleSlideDragStart",function(e){var r=p1(e)?e.changedTouches[0]:e;t.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:r.pageX}),t.attachDragEndListener()}),t.travellerDragStartHandlers={startX:t.handleTravellerDragStart.bind(t,"startX"),endX:t.handleTravellerDragStart.bind(t,"endX")},t.state={},t}if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(e&&e.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),e&&pZ(n,e),t=[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(e){var t=e.startX,r=e.endX,o=this.state.scaleValues,i=this.props,a=i.gap,s=i.data.length-1,l=Math.min(t,r),c=Math.max(t,r),u=n.getIndexInRange(o,l),f=n.getIndexInRange(o,c);return{startIndex:u-u%a,endIndex:f===s?s:f-f%a}}},{key:"getTextOfTick",value:function(e){var t=this.props,r=t.data,n=t.tickFormatter,o=t.dataKey,i=pi(r[e],o,e);return r7()(n)?n(i,e):i}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(e){var t=this.state,r=t.slideMoveStartX,n=t.startX,o=t.endX,i=this.props,a=i.x,s=i.width,l=i.travellerWidth,c=i.startIndex,u=i.endIndex,f=i.onChange,p=e.pageX-r;p>0?p=Math.min(p,a+s-l-o,a+s-l-n):p<0&&(p=Math.max(p,a-n,a-o));var d=this.getIndex({startX:n+p,endX:o+p});(d.startIndex!==c||d.endIndex!==u)&&f&&f(d),this.setState({startX:n+p,endX:o+p,slideMoveStartX:e.pageX})}},{key:"handleTravellerDragStart",value:function(e,t){var r=p1(t)?t.changedTouches[0]:t;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:e,brushMoveStartX:r.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(e){var t=this.state,r=t.brushMoveStartX,n=t.movingTravellerId,o=t.endX,i=t.startX,a=this.state[n],s=this.props,l=s.x,c=s.width,u=s.travellerWidth,f=s.onChange,p=s.gap,d=s.data,h={startX:this.state.startX,endX:this.state.endX},y=e.pageX-r;y>0?y=Math.min(y,l+c-u-a):y<0&&(y=Math.max(y,l-a)),h[n]=a+y;var v=this.getIndex(h),m=v.startIndex,b=v.endIndex,g=function(){var e=d.length-1;return"startX"===n&&(o>i?m%p==0:b%p==0)||!!(o<i)&&b===e||"endX"===n&&(o>i?b%p==0:m%p==0)||!!(o>i)&&b===e};this.setState(pJ(pJ({},n,a+y),"brushMoveStartX",e.pageX),function(){f&&g()&&f(v)})}},{key:"handleTravellerMoveKeyboard",value:function(e,t){var r=this,n=this.state,o=n.scaleValues,i=n.startX,a=n.endX,s=this.state[t],l=o.indexOf(s);if(-1!==l){var c=l+e;if(-1!==c&&!(c>=o.length)){var u=o[c];"startX"===t&&u>=a||"endX"===t&&u<=i||this.setState(pJ({},t,u),function(){r.props.onChange(r.getIndex({startX:r.state.startX,endX:r.state.endX}))})}}}},{key:"renderBackground",value:function(){var e=this.props,t=e.x,r=e.y,n=e.width,o=e.height,i=e.fill,a=e.stroke;return u().createElement("rect",{stroke:a,fill:i,x:t,y:r,width:n,height:o})}},{key:"renderPanorama",value:function(){var e=this.props,t=e.x,r=e.y,n=e.width,o=e.height,i=e.data,a=e.children,s=e.padding,l=c.Children.only(a);return l?u().cloneElement(l,{x:t,y:r,width:n,height:o,margin:s,compact:!0,data:i}):null}},{key:"renderTravellerLayer",value:function(e,t){var r,o,i=this,a=this.props,s=a.y,l=a.travellerWidth,c=a.height,f=a.traveller,p=a.ariaLabel,d=a.data,h=a.startIndex,y=a.endIndex,v=Math.max(e,this.props.x),m=pX(pX({},nj(this.props,!1)),{},{x:v,y:s,width:l,height:c}),b=p||"Min value: ".concat(null==(r=d[h])?void 0:r.name,", Max value: ").concat(null==(o=d[y])?void 0:o.name);return u().createElement(nU,{tabIndex:0,role:"slider","aria-label":b,"aria-valuenow":e,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[t],onTouchStart:this.travellerDragStartHandlers[t],onKeyDown:function(e){["ArrowLeft","ArrowRight"].includes(e.key)&&(e.preventDefault(),e.stopPropagation(),i.handleTravellerMoveKeyboard("ArrowRight"===e.key?1:-1,t))},onFocus:function(){i.setState({isTravellerFocused:!0})},onBlur:function(){i.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},n.renderTraveller(f,m))}},{key:"renderSlide",value:function(e,t){var r=this.props,n=r.y,o=r.height,i=r.stroke,a=r.travellerWidth,s=Math.min(e,t)+a,l=Math.max(Math.abs(t-e)-a,0);return u().createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:i,fillOpacity:.2,x:s,y:n,width:l,height:o})}},{key:"renderText",value:function(){var e=this.props,t=e.startIndex,r=e.endIndex,n=e.y,o=e.height,i=e.travellerWidth,a=e.stroke,s=this.state,l=s.startX,c=s.endX,f={pointerEvents:"none",fill:a};return u().createElement(nU,{className:"recharts-brush-texts"},u().createElement(ss,pY({textAnchor:"end",verticalAnchor:"middle",x:Math.min(l,c)-5,y:n+o/2},f),this.getTextOfTick(t)),u().createElement(ss,pY({textAnchor:"start",verticalAnchor:"middle",x:Math.max(l,c)+i+5,y:n+o/2},f),this.getTextOfTick(r)))}},{key:"render",value:function(){var e=this.props,t=e.data,r=e.className,n=e.children,o=e.x,i=e.y,a=e.width,s=e.height,l=e.alwaysShowText,c=this.state,f=c.startX,p=c.endX,d=c.isTextActive,h=c.isSlideMoving,y=c.isTravellerMoving,v=c.isTravellerFocused;if(!t||!t.length||!rV(o)||!rV(i)||!rV(a)||!rV(s)||a<=0||s<=0)return null;var m=(0,rI.A)("recharts-brush",r),b=1===u().Children.count(n),g=pU("userSelect","none");return u().createElement(nU,{className:m,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:g},this.renderBackground(),b&&this.renderPanorama(),this.renderSlide(f,p),this.renderTravellerLayer(f,"startX"),this.renderTravellerLayer(p,"endX"),(d||h||y||v||l)&&this.renderText())}}],r=[{key:"renderDefaultTraveller",value:function(e){var t=e.x,r=e.y,n=e.width,o=e.height,i=e.stroke,a=Math.floor(r+o/2)-1;return u().createElement(u().Fragment,null,u().createElement("rect",{x:t,y:r,width:n,height:o,fill:i,stroke:"none"}),u().createElement("line",{x1:t+1,y1:a,x2:t+n-1,y2:a,fill:"none",stroke:"#fff"}),u().createElement("line",{x1:t+1,y1:a+2,x2:t+n-1,y2:a+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(e,t){var r;return u().isValidElement(e)?u().cloneElement(e,t):r7()(e)?e(t):n.renderDefaultTraveller(t)}},{key:"getDerivedStateFromProps",value:function(e,t){var r=e.data,n=e.width,o=e.x,i=e.travellerWidth,a=e.updateId,s=e.startIndex,l=e.endIndex;if(r!==t.prevData||a!==t.prevUpdateId)return pX({prevData:r,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:n},r&&r.length?p0({data:r,width:n,x:o,travellerWidth:i,startIndex:s,endIndex:l}):{scale:null,scaleValues:null});if(t.scale&&(n!==t.prevWidth||o!==t.prevX||i!==t.prevTravellerWidth)){t.scale.range([o,o+n-i]);var c=t.scale.domain().map(function(e){return t.scale(e)});return{prevData:r,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:n,startX:t.scale(e.startIndex),endX:t.scale(e.endIndex),scaleValues:c}}return null}},{key:"getIndexInRange",value:function(e,t){for(var r=e.length,n=0,o=r-1;o-n>1;){var i=Math.floor((n+o)/2);e[i]>t?o=i:n=i}return t>=e[o]?o:n}}],t&&pV(n.prototype,t),r&&pV(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(c.PureComponent);function p5(e){return(p5="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function p3(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function p4(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?p3(Object(r),!0).forEach(function(t){p8(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):p3(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function p8(e,t,r){var n;return(n=function(e,t){if("object"!=p5(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=p5(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"==p5(n)?n:n+"")in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function p6(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}pJ(p2,"displayName","Brush"),pJ(p2,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var p7=Math.PI/180,p9=function(e,t,r,n){return{x:e+Math.cos(-p7*n)*r,y:t+Math.sin(-p7*n)*r}},de=function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{top:0,right:0,bottom:0,left:0};return Math.min(Math.abs(e-(r.left||0)-(r.right||0)),Math.abs(t-(r.top||0)-(r.bottom||0)))/2},dt=function(e,t){var r=e.x,n=e.y;return Math.sqrt(Math.pow(r-t.x,2)+Math.pow(n-t.y,2))},dr=function(e,t){var r=e.x,n=e.y,o=t.cx,i=t.cy,a=dt({x:r,y:n},{x:o,y:i});if(a<=0)return{radius:a};var s=Math.acos((r-o)/a);return n>i&&(s=2*Math.PI-s),{radius:a,angle:180*s/Math.PI,angleInRadian:s}},dn=function(e){var t=e.startAngle,r=e.endAngle,n=Math.min(Math.floor(t/360),Math.floor(r/360));return{startAngle:t-360*n,endAngle:r-360*n}},di=function(e,t){var r,n=dr({x:e.x,y:e.y},t),o=n.radius,i=n.angle,a=t.innerRadius,s=t.outerRadius;if(o<a||o>s)return!1;if(0===o)return!0;var l=dn(t),c=l.startAngle,u=l.endAngle,f=i;if(c<=u){for(;f>u;)f-=360;for(;f<c;)f+=360;r=f>=c&&f<=u}else{for(;f>c;)f-=360;for(;f<u;)f+=360;r=f>=u&&f<=c}return r?p4(p4({},t),{},{radius:o,angle:f+360*Math.min(Math.floor(t.startAngle/360),Math.floor(t.endAngle/360))}):null},da=function(e){return(0,c.isValidElement)(e)||r7()(e)||"boolean"==typeof e?"":e.className};function ds(e){return(ds="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var dl=["offset"];function dc(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function du(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function df(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?du(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=ds(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=ds(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==ds(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):du(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function dp(){return(dp=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var dd=function(e){var t=e.value,r=e.formatter,n=r8()(e.children)?t:e.children;return r7()(r)?r(n):n},dh=function(e,t,r){var n,o,i=e.position,a=e.viewBox,s=e.offset,l=e.className,c=a.cx,f=a.cy,p=a.innerRadius,d=a.outerRadius,h=a.startAngle,y=a.endAngle,v=a.clockWise,m=(p+d)/2,b=rH(y-h)*Math.min(Math.abs(y-h),360),g=b>=0?1:-1;"insideStart"===i?(n=h+g*s,o=v):"insideEnd"===i?(n=y-g*s,o=!v):"end"===i&&(n=y+g*s,o=v),o=b<=0?o:!o;var x=p9(c,f,m,n),w=p9(c,f,m,n+(o?1:-1)*359),O="M".concat(x.x,",").concat(x.y,"\n    A").concat(m,",").concat(m,",0,1,").concat(+!o,",\n    ").concat(w.x,",").concat(w.y),j=r8()(e.id)?rZ("recharts-radial-line-"):e.id;return u().createElement("text",dp({},r,{dominantBaseline:"central",className:(0,rI.A)("recharts-radial-bar-label",l)}),u().createElement("defs",null,u().createElement("path",{id:j,d:O})),u().createElement("textPath",{xlinkHref:"#".concat(j)},t))},dy=function(e){var t=e.viewBox,r=e.offset,n=e.position,o=t.cx,i=t.cy,a=t.innerRadius,s=t.outerRadius,l=(t.startAngle+t.endAngle)/2;if("outside"===n){var c=p9(o,i,s+r,l),u=c.x;return{x:u,y:c.y,textAnchor:u>=o?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"end"};var f=p9(o,i,(a+s)/2,l);return{x:f.x,y:f.y,textAnchor:"middle",verticalAnchor:"middle"}},dv=function(e){var t=e.viewBox,r=e.parentViewBox,n=e.offset,o=e.position,i=t.x,a=t.y,s=t.width,l=t.height,c=l>=0?1:-1,u=c*n,f=c>0?"end":"start",p=c>0?"start":"end",d=s>=0?1:-1,h=d*n,y=d>0?"end":"start",v=d>0?"start":"end";if("top"===o)return df(df({},{x:i+s/2,y:a-c*n,textAnchor:"middle",verticalAnchor:f}),r?{height:Math.max(a-r.y,0),width:s}:{});if("bottom"===o)return df(df({},{x:i+s/2,y:a+l+u,textAnchor:"middle",verticalAnchor:p}),r?{height:Math.max(r.y+r.height-(a+l),0),width:s}:{});if("left"===o){var m={x:i-h,y:a+l/2,textAnchor:y,verticalAnchor:"middle"};return df(df({},m),r?{width:Math.max(m.x-r.x,0),height:l}:{})}if("right"===o){var b={x:i+s+h,y:a+l/2,textAnchor:v,verticalAnchor:"middle"};return df(df({},b),r?{width:Math.max(r.x+r.width-b.x,0),height:l}:{})}var g=r?{width:s,height:l}:{};return"insideLeft"===o?df({x:i+h,y:a+l/2,textAnchor:v,verticalAnchor:"middle"},g):"insideRight"===o?df({x:i+s-h,y:a+l/2,textAnchor:y,verticalAnchor:"middle"},g):"insideTop"===o?df({x:i+s/2,y:a+u,textAnchor:"middle",verticalAnchor:p},g):"insideBottom"===o?df({x:i+s/2,y:a+l-u,textAnchor:"middle",verticalAnchor:f},g):"insideTopLeft"===o?df({x:i+h,y:a+u,textAnchor:v,verticalAnchor:p},g):"insideTopRight"===o?df({x:i+s-h,y:a+u,textAnchor:y,verticalAnchor:p},g):"insideBottomLeft"===o?df({x:i+h,y:a+l-u,textAnchor:v,verticalAnchor:f},g):"insideBottomRight"===o?df({x:i+s-h,y:a+l-u,textAnchor:y,verticalAnchor:f},g):ne()(o)&&(rV(o.x)||rX(o.x))&&(rV(o.y)||rX(o.y))?df({x:i+rJ(o.x,s),y:a+rJ(o.y,l),textAnchor:"end",verticalAnchor:"end"},g):df({x:i+s/2,y:a+l/2,textAnchor:"middle",verticalAnchor:"middle"},g)};function dm(e){var t,r=e.offset,n=df({offset:void 0===r?5:r},function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,dl)),o=n.viewBox,i=n.position,a=n.value,s=n.children,l=n.content,f=n.className,p=n.textBreakAll;if(!o||r8()(a)&&r8()(s)&&!(0,c.isValidElement)(l)&&!r7()(l))return null;if((0,c.isValidElement)(l))return(0,c.cloneElement)(l,n);if(r7()(l)){if(t=(0,c.createElement)(l,n),(0,c.isValidElement)(t))return t}else t=dd(n);var d="cx"in o&&rV(o.cx),h=nj(n,!0);if(d&&("insideStart"===i||"insideEnd"===i||"end"===i))return dh(n,t,h);var y=d?dy(n):dv(n);return u().createElement(ss,dp({className:(0,rI.A)("recharts-label",void 0===f?"":f)},h,y,{breakAll:p}),t)}dm.displayName="Label";var db=function(e){var t=e.cx,r=e.cy,n=e.angle,o=e.startAngle,i=e.endAngle,a=e.r,s=e.radius,l=e.innerRadius,c=e.outerRadius,u=e.x,f=e.y,p=e.top,d=e.left,h=e.width,y=e.height,v=e.clockWise,m=e.labelViewBox;if(m)return m;if(rV(h)&&rV(y)){if(rV(u)&&rV(f))return{x:u,y:f,width:h,height:y};if(rV(p)&&rV(d))return{x:p,y:d,width:h,height:y}}return rV(u)&&rV(f)?{x:u,y:f,width:0,height:0}:rV(t)&&rV(r)?{cx:t,cy:r,startAngle:o||n||0,endAngle:i||n||0,innerRadius:l||0,outerRadius:c||s||a||0,clockWise:v}:e.viewBox?e.viewBox:{}};dm.parseViewBox=db,dm.renderCallByParent=function(e,t){var r,n,o=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!e||!e.children&&o&&!e.label)return null;var i=e.children,a=db(e),s=nb(i,dm).map(function(e,r){return(0,c.cloneElement)(e,{viewBox:t||a,key:"label-".concat(r)})});if(!o)return s;return[(r=e.label,n=t||a,!r?null:!0===r?u().createElement(dm,{key:"label-implicit",viewBox:n}):rG(r)?u().createElement(dm,{key:"label-implicit",viewBox:n,value:r}):(0,c.isValidElement)(r)?r.type===dm?(0,c.cloneElement)(r,{key:"label-implicit",viewBox:n}):u().createElement(dm,{key:"label-implicit",content:r,viewBox:n}):r7()(r)?u().createElement(dm,{key:"label-implicit",content:r,viewBox:n}):ne()(r)?u().createElement(dm,dp({viewBox:n},r,{key:"label-implicit"})):null)].concat(function(e){if(Array.isArray(e))return dc(e)}(s)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(s)||function(e,t){if(e){if("string"==typeof e)return dc(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return dc(e,t)}}(s)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())};var dg=function(e,t){var r=e.alwaysShow,n=e.ifOverflow;return r&&(n="extendDomain"),n===t},dx=r(69691),dw=r.n(dx),dO=r(47212),dj=r.n(dO),dS=function(e){return null};dS.displayName="Cell";var dP=r(5359),dE=r.n(dP);function dA(e){return(dA="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var dk=["valueAccessor"],dM=["data","dataKey","clockWise","id","textBreakAll"];function dT(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function d_(){return(d_=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function dC(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function dN(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?dC(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=dA(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=dA(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==dA(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):dC(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function dD(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}var dI=function(e){return Array.isArray(e.value)?dE()(e.value):e.value};function dR(e){var t=e.valueAccessor,r=void 0===t?dI:t,n=dD(e,dk),o=n.data,i=n.dataKey,a=n.clockWise,s=n.id,l=n.textBreakAll,c=dD(n,dM);return o&&o.length?u().createElement(nU,{className:"recharts-label-list"},o.map(function(e,t){var n=r8()(i)?r(e,t):pi(e&&e.payload,i),o=r8()(s)?{}:{id:"".concat(s,"-").concat(t)};return u().createElement(dm,d_({},nj(e,!0),c,o,{parentViewBox:e.parentViewBox,value:n,textBreakAll:l,viewBox:dm.parseViewBox(r8()(a)?e:dN(dN({},e),{},{clockWise:a})),key:"label-".concat(t),index:t}))})):null}dR.displayName="LabelList",dR.renderCallByParent=function(e,t){var r,n=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!e||!e.children&&n&&!e.label)return null;var o=nb(e.children,dR).map(function(e,r){return(0,c.cloneElement)(e,{data:t,key:"labelList-".concat(r)})});return n?[(r=e.label,!r?null:!0===r?u().createElement(dR,{key:"labelList-implicit",data:t}):u().isValidElement(r)||r7()(r)?u().createElement(dR,{key:"labelList-implicit",data:t,content:r}):ne()(r)?u().createElement(dR,d_({data:t},r,{key:"labelList-implicit"})):null)].concat(function(e){if(Array.isArray(e))return dT(e)}(o)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(o)||function(e,t){if(e){if("string"==typeof e)return dT(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return dT(e,t)}}(o)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()):o};var dL=r(38404),dB=r.n(dL),dF=r(98451),dz=r.n(dF);function d$(e){return(d$="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function dW(){return(dW=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function dU(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function dq(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function dY(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?dq(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=d$(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=d$(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==d$(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):dq(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var dH=function(e,t,r,n,o){var i,a=r-n;return"M ".concat(e,",").concat(t)+"L ".concat(e+r,",").concat(t)+"L ".concat(e+r-a/2,",").concat(t+o)+"L ".concat(e+r-a/2-n,",").concat(t+o)+"L ".concat(e,",").concat(t," Z")},dX={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},dV=function(e){var t,r=dY(dY({},dX),e),n=(0,c.useRef)(),o=function(e){if(Array.isArray(e))return e}(t=(0,c.useState)(-1))||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,s=[],l=!0,c=!1;try{i=(r=r.call(e)).next,!1;for(;!(l=(n=i.call(r)).done)&&(s.push(n.value),s.length!==t);l=!0);}catch(e){c=!0,o=e}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return s}}(t,2)||function(e,t){if(e){if("string"==typeof e)return dU(e,2);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return dU(e,t)}}(t,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],a=o[1];(0,c.useEffect)(function(){if(n.current&&n.current.getTotalLength)try{var e=n.current.getTotalLength();e&&a(e)}catch(e){}},[]);var s=r.x,l=r.y,f=r.upperWidth,p=r.lowerWidth,d=r.height,h=r.className,y=r.animationEasing,v=r.animationDuration,m=r.animationBegin,b=r.isUpdateAnimationActive;if(s!==+s||l!==+l||f!==+f||p!==+p||d!==+d||0===f&&0===p||0===d)return null;var g=(0,rI.A)("recharts-trapezoid",h);return b?u().createElement(ab,{canBegin:i>0,from:{upperWidth:0,lowerWidth:0,height:d,x:s,y:l},to:{upperWidth:f,lowerWidth:p,height:d,x:s,y:l},duration:v,animationEasing:y,isActive:b},function(e){var t=e.upperWidth,o=e.lowerWidth,a=e.height,s=e.x,l=e.y;return u().createElement(ab,{canBegin:i>0,from:"0px ".concat(-1===i?1:i,"px"),to:"".concat(i,"px 0px"),attributeName:"strokeDasharray",begin:m,duration:v,easing:y},u().createElement("path",dW({},nj(r,!0),{className:g,d:dH(s,l,t,o,a),ref:n})))}):u().createElement("g",null,u().createElement("path",dW({},nj(r,!0),{className:g,d:dH(s,l,f,p,d)})))};function dG(e){return(dG="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function dK(){return(dK=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function dZ(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function dJ(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?dZ(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=dG(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=dG(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==dG(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):dZ(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var dQ=function(e){var t=e.cx,r=e.cy,n=e.radius,o=e.angle,i=e.sign,a=e.isExternal,s=e.cornerRadius,l=e.cornerIsExternal,c=s*(a?1:-1)+n,u=Math.asin(s/c)/p7,f=l?o:o+i*u;return{center:p9(t,r,c,f),circleTangency:p9(t,r,n,f),lineTangency:p9(t,r,c*Math.cos(u*p7),l?o-i*u:o),theta:u}},d0=function(e){var t=e.cx,r=e.cy,n=e.innerRadius,o=e.outerRadius,i=e.startAngle,a=e.endAngle,s=rH(a-i)*Math.min(Math.abs(a-i),359.999),l=i+s,c=p9(t,r,o,i),u=p9(t,r,o,l),f="M ".concat(c.x,",").concat(c.y,"\n    A ").concat(o,",").concat(o,",0,\n    ").concat(+(Math.abs(s)>180),",").concat(+(i>l),",\n    ").concat(u.x,",").concat(u.y,"\n  ");if(n>0){var p=p9(t,r,n,i),d=p9(t,r,n,l);f+="L ".concat(d.x,",").concat(d.y,"\n            A ").concat(n,",").concat(n,",0,\n            ").concat(+(Math.abs(s)>180),",").concat(+(i<=l),",\n            ").concat(p.x,",").concat(p.y," Z")}else f+="L ".concat(t,",").concat(r," Z");return f},d1=function(e){var t=e.cx,r=e.cy,n=e.innerRadius,o=e.outerRadius,i=e.cornerRadius,a=e.forceCornerRadius,s=e.cornerIsExternal,l=e.startAngle,c=e.endAngle,u=rH(c-l),f=dQ({cx:t,cy:r,radius:o,angle:l,sign:u,cornerRadius:i,cornerIsExternal:s}),p=f.circleTangency,d=f.lineTangency,h=f.theta,y=dQ({cx:t,cy:r,radius:o,angle:c,sign:-u,cornerRadius:i,cornerIsExternal:s}),v=y.circleTangency,m=y.lineTangency,b=y.theta,g=s?Math.abs(l-c):Math.abs(l-c)-h-b;if(g<0)return a?"M ".concat(d.x,",").concat(d.y,"\n        a").concat(i,",").concat(i,",0,0,1,").concat(2*i,",0\n        a").concat(i,",").concat(i,",0,0,1,").concat(-(2*i),",0\n      "):d0({cx:t,cy:r,innerRadius:n,outerRadius:o,startAngle:l,endAngle:c});var x="M ".concat(d.x,",").concat(d.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(u<0),",").concat(p.x,",").concat(p.y,"\n    A").concat(o,",").concat(o,",0,").concat(+(g>180),",").concat(+(u<0),",").concat(v.x,",").concat(v.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(u<0),",").concat(m.x,",").concat(m.y,"\n  ");if(n>0){var w=dQ({cx:t,cy:r,radius:n,angle:l,sign:u,isExternal:!0,cornerRadius:i,cornerIsExternal:s}),O=w.circleTangency,j=w.lineTangency,S=w.theta,P=dQ({cx:t,cy:r,radius:n,angle:c,sign:-u,isExternal:!0,cornerRadius:i,cornerIsExternal:s}),E=P.circleTangency,A=P.lineTangency,k=P.theta,M=s?Math.abs(l-c):Math.abs(l-c)-S-k;if(M<0&&0===i)return"".concat(x,"L").concat(t,",").concat(r,"Z");x+="L".concat(A.x,",").concat(A.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(u<0),",").concat(E.x,",").concat(E.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(M>180),",").concat(+(u>0),",").concat(O.x,",").concat(O.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(u<0),",").concat(j.x,",").concat(j.y,"Z")}else x+="L".concat(t,",").concat(r,"Z");return x},d2={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},d5=function(e){var t,r=dJ(dJ({},d2),e),n=r.cx,o=r.cy,i=r.innerRadius,a=r.outerRadius,s=r.cornerRadius,l=r.forceCornerRadius,c=r.cornerIsExternal,f=r.startAngle,p=r.endAngle,d=r.className;if(a<i||f===p)return null;var h=(0,rI.A)("recharts-sector",d),y=a-i,v=rJ(s,y,0,!0);return t=v>0&&360>Math.abs(f-p)?d1({cx:n,cy:o,innerRadius:i,outerRadius:a,cornerRadius:Math.min(v,y/2),forceCornerRadius:l,cornerIsExternal:c,startAngle:f,endAngle:p}):d0({cx:n,cy:o,innerRadius:i,outerRadius:a,startAngle:f,endAngle:p}),u().createElement("path",dK({},nj(r,!0),{className:h,d:t,role:"img"}))},d3=["option","shapeType","propTransformer","activeClassName","isActive"];function d4(e){return(d4="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function d8(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function d6(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?d8(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=d4(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=d4(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==d4(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):d8(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function d7(e){var t=e.shapeType,r=e.elementProps;switch(t){case"rectangle":return u().createElement(aA,r);case"trapezoid":return u().createElement(dV,r);case"sector":return u().createElement(d5,r);case"symbols":if("symbols"===t)return u().createElement(oX,r);break;default:return null}}function d9(e){var t,r=e.option,n=e.shapeType,o=e.propTransformer,i=e.activeClassName,a=e.isActive,s=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,d3);if((0,c.isValidElement)(r))t=(0,c.cloneElement)(r,d6(d6({},s),(0,c.isValidElement)(r)?r.props:r));else if(r7()(r))t=r(s);else if(dB()(r)&&!dz()(r)){var l=(void 0===o?function(e,t){return d6(d6({},t),e)}:o)(r,s);t=u().createElement(d7,{shapeType:n,elementProps:l})}else t=u().createElement(d7,{shapeType:n,elementProps:s});return a?u().createElement(nU,{className:void 0===i?"recharts-active-shape":i},t):t}function he(e,t){return null!=t&&"trapezoids"in e.props}function ht(e,t){return null!=t&&"sectors"in e.props}function hr(e,t){return null!=t&&"points"in e.props}function hn(e,t){var r,n,o=e.x===(null==t||null==(r=t.labelViewBox)?void 0:r.x)||e.x===t.x,i=e.y===(null==t||null==(n=t.labelViewBox)?void 0:n.y)||e.y===t.y;return o&&i}function ho(e,t){var r=e.endAngle===t.endAngle,n=e.startAngle===t.startAngle;return r&&n}function hi(e,t){var r=e.x===t.x,n=e.y===t.y,o=e.z===t.z;return r&&n&&o}var ha=["x","y"];function hs(e){return(hs="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function hl(){return(hl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function hc(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function hu(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?hc(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=hs(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=hs(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==hs(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):hc(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function hf(e,t){var r=e.x,n=e.y,o=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,ha),i=parseInt("".concat(r),10),a=parseInt("".concat(n),10),s=parseInt("".concat(t.height||o.height),10),l=parseInt("".concat(t.width||o.width),10);return hu(hu(hu(hu(hu({},t),o),i?{x:i}:{}),a?{y:a}:{}),{},{height:s,width:l,name:t.name,radius:t.radius})}function hp(e){return u().createElement(d9,hl({shapeType:"rectangle",propTransformer:hf,activeClassName:"recharts-active-bar"},e))}var hd=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return function(r,n){if("number"==typeof e)return e;var o="number"==typeof r;return o?e(r,n):(o||nL(!1),t)}},hh=["value","background"];function hy(e){return(hy="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function hv(){return(hv=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function hm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function hb(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?hm(Object(r),!0).forEach(function(t){hj(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):hm(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function hg(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,hS(n.key),n)}}function hx(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(hx=function(){return!!e})()}function hw(e){return(hw=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function hO(e,t){return(hO=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function hj(e,t,r){return(t=hS(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function hS(e){var t=function(e,t){if("object"!=hy(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=hy(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==hy(t)?t:t+""}var hP=function(e){var t,r;function n(){var e,t,r;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");for(var o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return t=n,r=[].concat(i),t=hw(t),hj(e=function(e,t){if(t&&("object"===hy(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");var r=e;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,hx()?Reflect.construct(t,r||[],hw(this).constructor):t.apply(this,r)),"state",{isAnimationFinished:!1}),hj(e,"id",rZ("recharts-bar-")),hj(e,"handleAnimationEnd",function(){var t=e.props.onAnimationEnd;e.setState({isAnimationFinished:!0}),t&&t()}),hj(e,"handleAnimationStart",function(){var t=e.props.onAnimationStart;e.setState({isAnimationFinished:!1}),t&&t()}),e}if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(e&&e.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),e&&hO(n,e),t=[{key:"renderRectanglesStatically",value:function(e){var t=this,r=this.props,n=r.shape,o=r.dataKey,i=r.activeIndex,a=r.activeBar,s=nj(this.props,!1);return e&&e.map(function(e,r){var l=r===i,c=hb(hb(hb({},s),e),{},{isActive:l,option:l?a:n,index:r,dataKey:o,onAnimationStart:t.handleAnimationStart,onAnimationEnd:t.handleAnimationEnd});return u().createElement(nU,hv({className:"recharts-bar-rectangle"},nc(t.props,e,r),{key:"rectangle-".concat(null==e?void 0:e.x,"-").concat(null==e?void 0:e.y,"-").concat(null==e?void 0:e.value,"-").concat(r)}),u().createElement(hp,c))})}},{key:"renderRectanglesWithAnimation",value:function(){var e=this,t=this.props,r=t.data,n=t.layout,o=t.isAnimationActive,i=t.animationBegin,a=t.animationDuration,s=t.animationEasing,l=t.animationId,c=this.state.prevData;return u().createElement(ab,{begin:i,duration:a,isActive:o,easing:s,from:{t:0},to:{t:1},key:"bar-".concat(l),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(t){var o=t.t,i=r.map(function(e,t){var r=c&&c[t];if(r){var i=r1(r.x,e.x),a=r1(r.y,e.y),s=r1(r.width,e.width),l=r1(r.height,e.height);return hb(hb({},e),{},{x:i(o),y:a(o),width:s(o),height:l(o)})}if("horizontal"===n){var u=r1(0,e.height)(o);return hb(hb({},e),{},{y:e.y+e.height-u,height:u})}var f=r1(0,e.width)(o);return hb(hb({},e),{},{width:f})});return u().createElement(nU,null,e.renderRectanglesStatically(i))})}},{key:"renderRectangles",value:function(){var e=this.props,t=e.data,r=e.isAnimationActive,n=this.state.prevData;return r&&t&&t.length&&(!n||!fr()(n,t))?this.renderRectanglesWithAnimation():this.renderRectanglesStatically(t)}},{key:"renderBackground",value:function(){var e=this,t=this.props,r=t.data,n=t.dataKey,o=t.activeIndex,i=nj(this.props.background,!1);return r.map(function(t,r){t.value;var a=t.background,s=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(t,hh);if(!a)return null;var l=hb(hb(hb(hb(hb({},s),{},{fill:"#eee"},a),i),nc(e.props,t,r)),{},{onAnimationStart:e.handleAnimationStart,onAnimationEnd:e.handleAnimationEnd,dataKey:n,index:r,className:"recharts-bar-background-rectangle"});return u().createElement(hp,hv({key:"background-bar-".concat(r),option:e.props.background,isActive:r===o},l))})}},{key:"renderErrorBar",value:function(e,t){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var r=this.props,n=r.data,o=r.xAxis,i=r.yAxis,a=r.layout,s=nb(r.children,f3);if(!s)return null;var l="vertical"===a?n[0].height/2:n[0].width/2,c=function(e,t){var r=Array.isArray(e.value)?e.value[1]:e.value;return{x:e.x,y:e.y,value:r,errorVal:pi(e,t)}};return u().createElement(nU,{clipPath:e?"url(#clipPath-".concat(t,")"):null},s.map(function(e){return u().cloneElement(e,{key:"error-bar-".concat(t,"-").concat(e.props.dataKey),data:n,xAxis:o,yAxis:i,layout:a,offset:l,dataPointFormatter:c})}))}},{key:"render",value:function(){var e=this.props,t=e.hide,r=e.data,n=e.className,o=e.xAxis,i=e.yAxis,a=e.left,s=e.top,l=e.width,c=e.height,f=e.isAnimationActive,p=e.background,d=e.id;if(t||!r||!r.length)return null;var h=this.state.isAnimationFinished,y=(0,rI.A)("recharts-bar",n),v=o&&o.allowDataOverflow,m=i&&i.allowDataOverflow,b=v||m,g=r8()(d)?this.id:d;return u().createElement(nU,{className:y},v||m?u().createElement("defs",null,u().createElement("clipPath",{id:"clipPath-".concat(g)},u().createElement("rect",{x:v?a:a-l/2,y:m?s:s-c/2,width:v?l:2*l,height:m?c:2*c}))):null,u().createElement(nU,{className:"recharts-bar-rectangles",clipPath:b?"url(#clipPath-".concat(g,")"):null},p?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(b,g),(!f||h)&&dR.renderCallByParent(this.props,r))}}],r=[{key:"getDerivedStateFromProps",value:function(e,t){return e.animationId!==t.prevAnimationId?{prevAnimationId:e.animationId,curData:e.data,prevData:t.curData}:e.data!==t.curData?{curData:e.data}:null}}],t&&hg(n.prototype,t),r&&hg(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(c.PureComponent);function hE(e){return(hE="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function hA(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,h_(n.key),n)}}function hk(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function hM(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?hk(Object(r),!0).forEach(function(t){hT(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):hk(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function hT(e,t,r){return(t=h_(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function h_(e){var t=function(e,t){if("object"!=hE(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=hE(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==hE(t)?t:t+""}hj(hP,"displayName","Bar"),hj(hP,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",activeBar:!1,isAnimationActive:!ot.isSsr,animationBegin:0,animationDuration:400,animationEasing:"ease"}),hj(hP,"getComposedData",function(e){var t=e.props,r=e.item,n=e.barPosition,o=e.bandSize,i=e.xAxis,a=e.yAxis,s=e.xAxisTicks,l=e.yAxisTicks,c=e.stackedData,u=e.dataStartIndex,f=e.displayedData,p=e.offset,d=pO(n,r);if(!d)return null;var h=t.layout,y=r.type.defaultProps,v=void 0!==y?hb(hb({},y),r.props):r.props,m=v.dataKey,b=v.children,g=v.minPointSize,x="horizontal"===h?a:i,w=c?x.scale.domain():null,O=pM({numericAxis:x}),j=nb(b,dS),S=f.map(function(e,t){c?f=pj(c[u+t],w):Array.isArray(f=pi(e,m))||(f=[O,f]);var n=hd(g,hP.defaultProps.minPointSize)(f[1],t);if("horizontal"===h){var f,p,y,v,b,x,S,P=[a.scale(f[0]),a.scale(f[1])],E=P[0],A=P[1];p=pk({axis:i,ticks:s,bandSize:o,offset:d.offset,entry:e,index:t}),y=null!=(S=null!=A?A:E)?S:void 0,v=d.size;var k=E-A;if(b=Number.isNaN(k)?0:k,x={x:p,y:a.y,width:v,height:a.height},Math.abs(n)>0&&Math.abs(b)<Math.abs(n)){var M=rH(b||n)*(Math.abs(n)-Math.abs(b));y-=M,b+=M}}else{var T=[i.scale(f[0]),i.scale(f[1])],_=T[0],C=T[1];if(p=_,y=pk({axis:a,ticks:l,bandSize:o,offset:d.offset,entry:e,index:t}),v=C-_,b=d.size,x={x:i.x,y:y,width:i.width,height:b},Math.abs(n)>0&&Math.abs(v)<Math.abs(n)){var N=rH(v||n)*(Math.abs(n)-Math.abs(v));v+=N}}return hb(hb(hb({},e),{},{x:p,y:y,width:v,height:b,value:c?f:f[1],payload:e,background:x},j&&j[t]&&j[t].props),{},{tooltipPayload:[pL(r,e)],tooltipPosition:{x:p+v/2,y:y+b/2}})});return hb({data:S,layout:h},p)});var hC=function(e,t){var r=e.x,n=e.y,o=t.x,i=t.y;return{x:Math.min(r,o),y:Math.min(n,i),width:Math.abs(o-r),height:Math.abs(i-n)}},hN=function(){var e,t;function r(e){if(!(this instanceof r))throw TypeError("Cannot call a class as a function");this.scale=e}return e=[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.bandAware,n=t.position;if(void 0!==e){if(n)switch(n){case"start":default:return this.scale(e);case"middle":var o=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+o;case"end":var i=this.bandwidth?this.bandwidth():0;return this.scale(e)+i}if(r){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+a}return this.scale(e)}}},{key:"isInRange",value:function(e){var t=this.range(),r=t[0],n=t[t.length-1];return r<=n?e>=r&&e<=n:e>=n&&e<=r}}],t=[{key:"create",value:function(e){return new r(e)}}],e&&hA(r.prototype,e),t&&hA(r,t),Object.defineProperty(r,"prototype",{writable:!1}),r}();hT(hN,"EPS",1e-4);var hD=function(e){var t=Object.keys(e).reduce(function(t,r){return hM(hM({},t),{},hT({},r,hN.create(e[r])))},{});return hM(hM({},t),{},{apply:function(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.bandAware,o=r.position;return dw()(e,function(e,r){return t[r].apply(e,{bandAware:n,position:o})})},isInRange:function(e){return dj()(e,function(e,r){return t[r].isInRange(e)})}})},hI=function(e){var t=e.width,r=e.height,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,o=(n%180+180)%180*Math.PI/180,i=Math.atan(r/t);return Math.abs(o>i&&o<Math.PI-i?r/Math.sin(o):t/Math.cos(o))};function hR(){return(hR=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function hL(e){return(hL="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function hB(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function hF(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?hB(Object(r),!0).forEach(function(t){hU(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):hB(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function hz(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(hz=function(){return!!e})()}function h$(e){return(h$=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function hW(e,t){return(hW=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function hU(e,t,r){return(t=hq(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function hq(e){var t=function(e,t){if("object"!=hL(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=hL(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==hL(t)?t:t+""}var hY=function(e){var t=e.x,r=e.y,n=e.xAxis,o=e.yAxis,i=hD({x:n.scale,y:o.scale}),a=i.apply({x:t,y:r},{bandAware:!0});return dg(e,"discard")&&!i.isInRange(a)?null:a},hH=function(e){var t;function r(){var e,t;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return e=r,t=arguments,e=h$(e),function(e,t){if(t&&("object"===hL(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");var r=e;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,hz()?Reflect.construct(e,t||[],h$(this).constructor):e.apply(this,t))}if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(e&&e.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),e&&hW(r,e),t=[{key:"render",value:function(){var e=this.props,t=e.x,n=e.y,o=e.r,i=e.alwaysShow,a=e.clipPathId,s=rG(t),l=rG(n);if(r3(void 0===i,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!s||!l)return null;var c=hY(this.props);if(!c)return null;var f=c.x,p=c.y,d=this.props,h=d.shape,y=d.className,v=hF(hF({clipPath:dg(this.props,"hidden")?"url(#".concat(a,")"):void 0},nj(this.props,!0)),{},{cx:f,cy:p});return u().createElement(nU,{className:(0,rI.A)("recharts-reference-dot",y)},r.renderDot(h,v),dm.renderCallByParent(this.props,{x:f-o,y:p-o,width:2*o,height:2*o}))}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,hq(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(u().Component);hU(hH,"displayName","ReferenceDot"),hU(hH,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1}),hU(hH,"renderDot",function(e,t){var r;return u().isValidElement(e)?u().cloneElement(e,t):r7()(e)?e(t):u().createElement(is,hR({},t,{cx:t.cx,cy:t.cy,className:"recharts-reference-dot-dot"}))});var hX=r(67367),hV=r.n(hX),hG=r(22964),hK=r.n(hG),hZ=r(86451),hJ=r.n(hZ)()(function(e){return{x:e.left,y:e.top,width:e.width,height:e.height}},function(e){return["l",e.left,"t",e.top,"w",e.width,"h",e.height].join("")}),hQ=(0,c.createContext)(void 0),h0=(0,c.createContext)(void 0),h1=(0,c.createContext)(void 0),h2=(0,c.createContext)({}),h5=(0,c.createContext)(void 0),h3=(0,c.createContext)(0),h4=(0,c.createContext)(0),h8=function(e){var t=e.state,r=t.xAxisMap,n=t.yAxisMap,o=t.offset,i=e.clipPathId,a=e.children,s=e.width,l=e.height,c=hJ(o);return u().createElement(hQ.Provider,{value:r},u().createElement(h0.Provider,{value:n},u().createElement(h2.Provider,{value:o},u().createElement(h1.Provider,{value:c},u().createElement(h5.Provider,{value:i},u().createElement(h3.Provider,{value:l},u().createElement(h4.Provider,{value:s},a)))))))},h6=function(e){var t=(0,c.useContext)(hQ);null==t&&nL(!1);var r=t[e];return null==r&&nL(!1),r},h7=function(){var e=(0,c.useContext)(h0);return hK()(e,function(e){return dj()(e.domain,Number.isFinite)})||rQ(e)},h9=function(e){var t=(0,c.useContext)(h0);null==t&&nL(!1);var r=t[e];return null==r&&nL(!1),r},ye=function(){return(0,c.useContext)(h4)},yt=function(){return(0,c.useContext)(h3)};function yr(e){return(yr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function yn(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(yn=function(){return!!e})()}function yo(e){return(yo=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function yi(e,t){return(yi=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function ya(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function ys(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ya(Object(r),!0).forEach(function(t){yl(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ya(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function yl(e,t,r){return(t=yc(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function yc(e){var t=function(e,t){if("object"!=yr(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=yr(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==yr(t)?t:t+""}function yu(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function yf(){return(yf=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var yp=function(e,t){var r;return u().isValidElement(e)?u().cloneElement(e,t):r7()(e)?e(t):u().createElement("line",yf({},t,{className:"recharts-reference-line-line"}))},yd=function(e,t,r,n,o,i,a,s,l){var c=o.x,u=o.y,f=o.width,p=o.height;if(r){var d=l.y,h=e.y.apply(d,{position:i});if(dg(l,"discard")&&!e.y.isInRange(h))return null;var y=[{x:c+f,y:h},{x:c,y:h}];return"left"===s?y.reverse():y}if(t){var v=l.x,m=e.x.apply(v,{position:i});if(dg(l,"discard")&&!e.x.isInRange(m))return null;var b=[{x:m,y:u+p},{x:m,y:u}];return"top"===a?b.reverse():b}if(n){var g=l.segment.map(function(t){return e.apply(t,{position:i})});return dg(l,"discard")&&hV()(g,function(t){return!e.isInRange(t)})?null:g}return null};function yh(e){var t,r=e.x,n=e.y,o=e.segment,i=e.xAxisId,a=e.yAxisId,s=e.shape,l=e.className,f=e.alwaysShow,p=(0,c.useContext)(h5),d=h6(i),h=h9(a),y=(0,c.useContext)(h1);if(!p||!y)return null;r3(void 0===f,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var v=yd(hD({x:d.scale,y:h.scale}),rG(r),rG(n),o&&2===o.length,y,e.position,d.orientation,h.orientation,e);if(!v)return null;var m=function(e){if(Array.isArray(e))return e}(v)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,s=[],l=!0,c=!1;try{i=(r=r.call(e)).next,!1;for(;!(l=(n=i.call(r)).done)&&(s.push(n.value),s.length!==t);l=!0);}catch(e){c=!0,o=e}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return s}}(v,2)||function(e,t){if(e){if("string"==typeof e)return yu(e,2);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return yu(e,t)}}(v,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),b=m[0],g=b.x,x=b.y,w=m[1],O=w.x,j=w.y,S=ys(ys({clipPath:dg(e,"hidden")?"url(#".concat(p,")"):void 0},nj(e,!0)),{},{x1:g,y1:x,x2:O,y2:j});return u().createElement(nU,{className:(0,rI.A)("recharts-reference-line",l)},yp(s,S),dm.renderCallByParent(e,hC({x:(t={x1:g,y1:x,x2:O,y2:j}).x1,y:t.y1},{x:t.x2,y:t.y2})))}var yy=function(e){var t;function r(){var e,t;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return e=r,t=arguments,e=yo(e),function(e,t){if(t&&("object"===yr(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");var r=e;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,yn()?Reflect.construct(e,t||[],yo(this).constructor):e.apply(this,t))}if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(e&&e.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),e&&yi(r,e),t=[{key:"render",value:function(){return u().createElement(yh,this.props)}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,yc(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(u().Component);function yv(){return(yv=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function ym(e){return(ym="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function yb(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function yg(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?yb(Object(r),!0).forEach(function(t){yj(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):yb(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}yl(yy,"displayName","ReferenceLine"),yl(yy,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});function yx(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(yx=function(){return!!e})()}function yw(e){return(yw=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function yO(e,t){return(yO=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function yj(e,t,r){return(t=yS(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function yS(e){var t=function(e,t){if("object"!=ym(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=ym(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==ym(t)?t:t+""}var yP=function(e,t,r,n,o){var i=o.x1,a=o.x2,s=o.y1,l=o.y2,c=o.xAxis,u=o.yAxis;if(!c||!u)return null;var f=hD({x:c.scale,y:u.scale}),p={x:e?f.x.apply(i,{position:"start"}):f.x.rangeMin,y:r?f.y.apply(s,{position:"start"}):f.y.rangeMin},d={x:t?f.x.apply(a,{position:"end"}):f.x.rangeMax,y:n?f.y.apply(l,{position:"end"}):f.y.rangeMax};return!dg(o,"discard")||f.isInRange(p)&&f.isInRange(d)?hC(p,d):null},yE=function(e){var t;function r(){var e,t;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return e=r,t=arguments,e=yw(e),function(e,t){if(t&&("object"===ym(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");var r=e;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,yx()?Reflect.construct(e,t||[],yw(this).constructor):e.apply(this,t))}if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(e&&e.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),e&&yO(r,e),t=[{key:"render",value:function(){var e=this.props,t=e.x1,n=e.x2,o=e.y1,i=e.y2,a=e.className,s=e.alwaysShow,l=e.clipPathId;r3(void 0===s,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var c=rG(t),f=rG(n),p=rG(o),d=rG(i),h=this.props.shape;if(!c&&!f&&!p&&!d&&!h)return null;var y=yP(c,f,p,d,this.props);if(!y&&!h)return null;var v=dg(this.props,"hidden")?"url(#".concat(l,")"):void 0;return u().createElement(nU,{className:(0,rI.A)("recharts-reference-area",a)},r.renderRect(h,yg(yg({clipPath:v},nj(this.props,!0)),y)),dm.renderCallByParent(this.props,y))}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,yS(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(u().Component);function yA(e){return function(e){if(Array.isArray(e))return yk(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return yk(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return yk(e,t)}}(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function yk(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}yj(yE,"displayName","ReferenceArea"),yj(yE,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1}),yj(yE,"renderRect",function(e,t){var r;return u().isValidElement(e)?u().cloneElement(e,t):r7()(e)?e(t):u().createElement(aA,yv({},t,{className:"recharts-reference-area-rect"}))});var yM=function(e,t,r,n,o){var i=nb(e,yy),a=nb(e,hH),s=[].concat(yA(i),yA(a)),l=nb(e,yE),c="".concat(n,"Id"),u=n[0],f=t;if(s.length&&(f=s.reduce(function(e,t){if(t.props[c]===r&&dg(t.props,"extendDomain")&&rV(t.props[u])){var n=t.props[u];return[Math.min(e[0],n),Math.max(e[1],n)]}return e},f)),l.length){var p="".concat(u,"1"),d="".concat(u,"2");f=l.reduce(function(e,t){if(t.props[c]===r&&dg(t.props,"extendDomain")&&rV(t.props[p])&&rV(t.props[d])){var n=t.props[p],o=t.props[d];return[Math.min(e[0],n,o),Math.max(e[1],n,o)]}return e},f)}return o&&o.length&&(f=o.reduce(function(e,t){return rV(t)?[Math.min(e[0],t),Math.max(e[1],t)]:e},f)),f},yT=r(11117),y_=new(r.n(yT)()),yC="recharts.syncMouseEvents";function yN(e){return(yN="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function yD(e,t,r){return(t=yI(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function yI(e){var t=function(e,t){if("object"!=yN(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=yN(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==yN(t)?t:t+""}var yR=function(){var e,t;return e=function e(){if(!(this instanceof e))throw TypeError("Cannot call a class as a function");yD(this,"activeIndex",0),yD(this,"coordinateList",[]),yD(this,"layout","horizontal")},t=[{key:"setDetails",value:function(e){var t,r=e.coordinateList,n=void 0===r?null:r,o=e.container,i=void 0===o?null:o,a=e.layout,s=void 0===a?null:a,l=e.offset,c=void 0===l?null:l,u=e.mouseHandlerCallback,f=void 0===u?null:u;this.coordinateList=null!=(t=null!=n?n:this.coordinateList)?t:[],this.container=null!=i?i:this.container,this.layout=null!=s?s:this.layout,this.offset=null!=c?c:this.offset,this.mouseHandlerCallback=null!=f?f:this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(e){if(0!==this.coordinateList.length)switch(e.key){case"ArrowRight":if("horizontal"!==this.layout)return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break;case"ArrowLeft":if("horizontal"!==this.layout)return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse()}}},{key:"setIndex",value:function(e){this.activeIndex=e}},{key:"spoofMouse",value:function(){if("horizontal"===this.layout&&0!==this.coordinateList.length){var e,t,r=this.container.getBoundingClientRect(),n=r.x,o=r.y,i=r.height,a=this.coordinateList[this.activeIndex].coordinate,s=(null==(e=window)?void 0:e.scrollX)||0,l=(null==(t=window)?void 0:t.scrollY)||0,c=o+this.offset.top+i/2+l;this.mouseHandlerCallback({pageX:n+a+s,pageY:c})}}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,yI(n.key),n)}}(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}();function yL(){}function yB(e,t,r){e._context.bezierCurveTo((2*e._x0+e._x1)/3,(2*e._y0+e._y1)/3,(e._x0+2*e._x1)/3,(e._y0+2*e._y1)/3,(e._x0+4*e._x1+t)/6,(e._y0+4*e._y1+r)/6)}function yF(e){this._context=e}function yz(e){this._context=e}function y$(e){this._context=e}yF.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:yB(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:yB(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},yz.prototype={areaStart:yL,areaEnd:yL,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._x2=e,this._y2=t;break;case 1:this._point=2,this._x3=e,this._y3=t;break;case 2:this._point=3,this._x4=e,this._y4=t,this._context.moveTo((this._x0+4*this._x1+e)/6,(this._y0+4*this._y1+t)/6);break;default:yB(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},y$.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+e)/6,n=(this._y0+4*this._y1+t)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:yB(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};class yW{constructor(e,t){this._context=e,this._x=t}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+e)/2,this._y0,this._x0,t,e,t):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+t)/2,e,this._y0,e,t)}this._x0=e,this._y0=t}}function yU(e){this._context=e}function yq(e){this._context=e}function yY(e){return new yq(e)}yU.prototype={areaStart:yL,areaEnd:yL,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(e,t){e*=1,t*=1,this._point?this._context.lineTo(e,t):(this._point=1,this._context.moveTo(e,t))}};function yH(e,t,r){var n=e._x1-e._x0,o=t-e._x1,i=(e._y1-e._y0)/(n||o<0&&-0),a=(r-e._y1)/(o||n<0&&-0);return((i<0?-1:1)+(a<0?-1:1))*Math.min(Math.abs(i),Math.abs(a),.5*Math.abs((i*o+a*n)/(n+o)))||0}function yX(e,t){var r=e._x1-e._x0;return r?(3*(e._y1-e._y0)/r-t)/2:t}function yV(e,t,r){var n=e._x0,o=e._y0,i=e._x1,a=e._y1,s=(i-n)/3;e._context.bezierCurveTo(n+s,o+s*t,i-s,a-s*r,i,a)}function yG(e){this._context=e}function yK(e){this._context=new yZ(e)}function yZ(e){this._context=e}function yJ(e){this._context=e}function yQ(e){var t,r,n=e.length-1,o=Array(n),i=Array(n),a=Array(n);for(o[0]=0,i[0]=2,a[0]=e[0]+2*e[1],t=1;t<n-1;++t)o[t]=1,i[t]=4,a[t]=4*e[t]+2*e[t+1];for(o[n-1]=2,i[n-1]=7,a[n-1]=8*e[n-1]+e[n],t=1;t<n;++t)r=o[t]/i[t-1],i[t]-=r,a[t]-=r*a[t-1];for(o[n-1]=a[n-1]/i[n-1],t=n-2;t>=0;--t)o[t]=(a[t]-o[t+1])/i[t];for(t=0,i[n-1]=(e[n]+o[n-1])/2;t<n-1;++t)i[t]=2*e[t+1]-o[t+1];return[o,i]}function y0(e,t){this._context=e,this._t=t}function y1(e){return e[0]}function y2(e){return e[1]}function y5(e,t){var r=oC(!0),n=null,o=yY,i=null,a=oB(s);function s(s){var l,c,u,f=(s=uz(s)).length,p=!1;for(null==n&&(i=o(u=a())),l=0;l<=f;++l)!(l<f&&r(c=s[l],l,s))===p&&((p=!p)?i.lineStart():i.lineEnd()),p&&i.point(+e(c,l,s),+t(c,l,s));if(u)return i=null,u+""||null}return e="function"==typeof e?e:void 0===e?y1:oC(e),t="function"==typeof t?t:void 0===t?y2:oC(t),s.x=function(t){return arguments.length?(e="function"==typeof t?t:oC(+t),s):e},s.y=function(e){return arguments.length?(t="function"==typeof e?e:oC(+e),s):t},s.defined=function(e){return arguments.length?(r="function"==typeof e?e:oC(!!e),s):r},s.curve=function(e){return arguments.length?(o=e,null!=n&&(i=o(n)),s):o},s.context=function(e){return arguments.length?(null==e?n=i=null:i=o(n=e),s):n},s}function y3(e,t,r){var n=null,o=oC(!0),i=null,a=yY,s=null,l=oB(c);function c(c){var u,f,p,d,h,y=(c=uz(c)).length,v=!1,m=Array(y),b=Array(y);for(null==i&&(s=a(h=l())),u=0;u<=y;++u){if(!(u<y&&o(d=c[u],u,c))===v)if(v=!v)f=u,s.areaStart(),s.lineStart();else{for(s.lineEnd(),s.lineStart(),p=u-1;p>=f;--p)s.point(m[p],b[p]);s.lineEnd(),s.areaEnd()}v&&(m[u]=+e(d,u,c),b[u]=+t(d,u,c),s.point(n?+n(d,u,c):m[u],r?+r(d,u,c):b[u]))}if(h)return s=null,h+""||null}function u(){return y5().defined(o).curve(a).context(i)}return e="function"==typeof e?e:void 0===e?y1:oC(+e),t="function"==typeof t?t:void 0===t?oC(0):oC(+t),r="function"==typeof r?r:void 0===r?y2:oC(+r),c.x=function(t){return arguments.length?(e="function"==typeof t?t:oC(+t),n=null,c):e},c.x0=function(t){return arguments.length?(e="function"==typeof t?t:oC(+t),c):e},c.x1=function(e){return arguments.length?(n=null==e?null:"function"==typeof e?e:oC(+e),c):n},c.y=function(e){return arguments.length?(t="function"==typeof e?e:oC(+e),r=null,c):t},c.y0=function(e){return arguments.length?(t="function"==typeof e?e:oC(+e),c):t},c.y1=function(e){return arguments.length?(r=null==e?null:"function"==typeof e?e:oC(+e),c):r},c.lineX0=c.lineY0=function(){return u().x(e).y(t)},c.lineY1=function(){return u().x(e).y(r)},c.lineX1=function(){return u().x(n).y(t)},c.defined=function(e){return arguments.length?(o="function"==typeof e?e:oC(!!e),c):o},c.curve=function(e){return arguments.length?(a=e,null!=i&&(s=a(i)),c):a},c.context=function(e){return arguments.length?(null==e?i=s=null:s=a(i=e),c):i},c}function y4(e){return(y4="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function y8(){return(y8=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function y6(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function y7(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?y6(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=y4(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=y4(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==y4(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):y6(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}yq.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._context.lineTo(e,t)}}},yG.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:yV(this,this._t0,yX(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){var r=NaN;if(t*=1,(e*=1)!==this._x1||t!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,yV(this,yX(this,r=yH(this,e,t)),r);break;default:yV(this,this._t0,r=yH(this,e,t))}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t,this._t0=r}}},(yK.prototype=Object.create(yG.prototype)).point=function(e,t){yG.prototype.point.call(this,t,e)},yZ.prototype={moveTo:function(e,t){this._context.moveTo(t,e)},closePath:function(){this._context.closePath()},lineTo:function(e,t){this._context.lineTo(t,e)},bezierCurveTo:function(e,t,r,n,o,i){this._context.bezierCurveTo(t,e,n,r,i,o)}},yJ.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var e=this._x,t=this._y,r=e.length;if(r)if(this._line?this._context.lineTo(e[0],t[0]):this._context.moveTo(e[0],t[0]),2===r)this._context.lineTo(e[1],t[1]);else for(var n=yQ(e),o=yQ(t),i=0,a=1;a<r;++i,++a)this._context.bezierCurveTo(n[0][i],o[0][i],n[1][i],o[1][i],e[a],t[a]);(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(e,t){this._x.push(+e),this._y.push(+t)}},y0.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,t),this._context.lineTo(e,t);else{var r=this._x*(1-this._t)+e*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,t)}}this._x=e,this._y=t}};var y9={curveBasisClosed:function(e){return new yz(e)},curveBasisOpen:function(e){return new y$(e)},curveBasis:function(e){return new yF(e)},curveBumpX:function(e){return new yW(e,!0)},curveBumpY:function(e){return new yW(e,!1)},curveLinearClosed:function(e){return new yU(e)},curveLinear:yY,curveMonotoneX:function(e){return new yG(e)},curveMonotoneY:function(e){return new yK(e)},curveNatural:function(e){return new yJ(e)},curveStep:function(e){return new y0(e,.5)},curveStepAfter:function(e){return new y0(e,1)},curveStepBefore:function(e){return new y0(e,0)}},ve=function(e){return e.x===+e.x&&e.y===+e.y},vt=function(e){return e.x},vr=function(e){return e.y},vn=function(e,t){if(r7()(e))return e;var r="curve".concat(ov()(e));return("curveMonotone"===r||"curveBump"===r)&&t?y9["".concat(r).concat("vertical"===t?"Y":"X")]:y9[r]||yY},vo=function(e){var t,r=e.type,n=e.points,o=void 0===n?[]:n,i=e.baseLine,a=e.layout,s=e.connectNulls,l=void 0!==s&&s,c=vn(void 0===r?"linear":r,a),u=l?o.filter(function(e){return ve(e)}):o;if(Array.isArray(i)){var f=l?i.filter(function(e){return ve(e)}):i,p=u.map(function(e,t){return y7(y7({},e),{},{base:f[t]})});return(t="vertical"===a?y3().y(vr).x1(vt).x0(function(e){return e.base.x}):y3().x(vt).y1(vr).y0(function(e){return e.base.y})).defined(ve).curve(c),t(p)}return(t="vertical"===a&&rV(i)?y3().y(vr).x1(vt).x0(i):rV(i)?y3().x(vt).y1(vr).y0(i):y5().x(vt).y(vr)).defined(ve).curve(c),t(u)},vi=function(e){var t=e.className,r=e.points,n=e.path,o=e.pathRef;if((!r||!r.length)&&!n)return null;var i=r&&r.length?vo(e):n;return u().createElement("path",y8({},nj(e,!1),nl(e),{className:(0,rI.A)("recharts-curve",t),d:i,ref:o}))};function va(e){return(va="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var vs=["x","y","top","left","width","height","className"];function vl(){return(vl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function vc(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}var vu=function(e){var t=e.x,r=void 0===t?0:t,n=e.y,o=void 0===n?0:n,i=e.top,a=void 0===i?0:i,s=e.left,l=void 0===s?0:s,c=e.width,f=void 0===c?0:c,p=e.height,d=void 0===p?0:p,h=e.className,y=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?vc(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=va(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=va(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==va(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):vc(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({x:r,y:o,top:a,left:l,width:f,height:d},function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,vs));return rV(r)&&rV(o)&&rV(f)&&rV(d)&&rV(a)&&rV(l)?u().createElement("path",vl({},nj(y,!0),{className:(0,rI.A)("recharts-cross",h),d:"M".concat(r,",").concat(a,"v").concat(d,"M").concat(l,",").concat(o,"h").concat(f)})):null};function vf(e){var t=e.cx,r=e.cy,n=e.radius,o=e.startAngle,i=e.endAngle;return{points:[p9(t,r,n,o),p9(t,r,n,i)],cx:t,cy:r,radius:n,startAngle:o,endAngle:i}}function vp(e){return(vp="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function vd(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function vh(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?vd(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=vp(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=vp(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==vp(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):vd(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function vy(e){var t,r,n,o,i=e.element,a=e.tooltipEventType,s=e.isActive,l=e.activeCoordinate,u=e.activePayload,f=e.offset,p=e.activeTooltipIndex,d=e.tooltipAxisBandSize,h=e.layout,y=e.chartName,v=null!=(r=i.props.cursor)?r:null==(n=i.type.defaultProps)?void 0:n.cursor;if(!i||!v||!s||!l||"ScatterChart"!==y&&"axis"!==a)return null;var m=vi;if("ScatterChart"===y)o=l,m=vu;else if("BarChart"===y)t=d/2,o={stroke:"none",fill:"#ccc",x:"horizontal"===h?l.x-t:f.left+.5,y:"horizontal"===h?f.top+.5:l.y-t,width:"horizontal"===h?d:f.width-1,height:"horizontal"===h?f.height-1:d},m=aA;else if("radial"===h){var b=vf(l),g=b.cx,x=b.cy,w=b.radius;o={cx:g,cy:x,startAngle:b.startAngle,endAngle:b.endAngle,innerRadius:w,outerRadius:w},m=d5}else o={points:function(e,t,r){var n,o,i,a;if("horizontal"===e)i=n=t.x,o=r.top,a=r.top+r.height;else if("vertical"===e)a=o=t.y,n=r.left,i=r.left+r.width;else if(null!=t.cx&&null!=t.cy)if("centric"!==e)return vf(t);else{var s=t.cx,l=t.cy,c=t.innerRadius,u=t.outerRadius,f=t.angle,p=p9(s,l,c,f),d=p9(s,l,u,f);n=p.x,o=p.y,i=d.x,a=d.y}return[{x:n,y:o},{x:i,y:a}]}(h,l,f)},m=vi;var O=vh(vh(vh(vh({stroke:"#ccc",pointerEvents:"none"},f),o),nj(v,!1)),{},{payload:u,payloadIndex:p,className:(0,rI.A)("recharts-tooltip-cursor",v.className)});return(0,c.isValidElement)(v)?(0,c.cloneElement)(v,O):(0,c.createElement)(m,O)}var vv=["item"],vm=["children","className","width","height","style","compact","title","desc"];function vb(e){return(vb="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function vg(){return(vg=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function vx(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,s=[],l=!0,c=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(s.push(n.value),s.length!==t);l=!0);}catch(e){c=!0,o=e}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return s}}(e,t)||vE(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function vw(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}function vO(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(vO=function(){return!!e})()}function vj(e){return(vj=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function vS(e,t){return(vS=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function vP(e){return function(e){if(Array.isArray(e))return vA(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||vE(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function vE(e,t){if(e){if("string"==typeof e)return vA(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return vA(e,t)}}function vA(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function vk(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function vM(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?vk(Object(r),!0).forEach(function(t){vT(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):vk(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function vT(e,t,r){return(t=v_(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function v_(e){var t=function(e,t){if("object"!=vb(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=vb(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==vb(t)?t:t+""}var vC={xAxis:["bottom","top"],yAxis:["left","right"]},vN={width:"100%",height:"100%"},vD={x:0,y:0};function vI(e){return e}var vR=function(e,t,r,n){var o=t.find(function(e){return e&&e.index===r});if(o){if("horizontal"===e)return{x:o.coordinate,y:n.y};if("vertical"===e)return{x:n.x,y:o.coordinate};if("centric"===e){var i=o.coordinate,a=n.radius;return vM(vM(vM({},n),p9(n.cx,n.cy,a,i)),{},{angle:i,radius:a})}var s=o.coordinate,l=n.angle;return vM(vM(vM({},n),p9(n.cx,n.cy,s,l)),{},{angle:l,radius:s})}return vD},vL=function(e,t){var r=t.graphicalItems,n=t.dataStartIndex,o=t.dataEndIndex,i=(null!=r?r:[]).reduce(function(e,t){var r=t.props.data;return r&&r.length?[].concat(vP(e),vP(r)):e},[]);return i.length>0?i:e&&e.length&&rV(n)&&rV(o)?e.slice(n,o+1):[]};function vB(e){return"number"===e?[0,"auto"]:void 0}var vF=function(e,t,r,n){var o=e.graphicalItems,i=e.tooltipAxis,a=vL(t,e);return r<0||!o||!o.length||r>=a.length?null:o.reduce(function(o,s){var l,c,u=null!=(l=s.props.data)?l:t;return(u&&e.dataStartIndex+e.dataEndIndex!==0&&e.dataEndIndex-e.dataStartIndex>=r&&(u=u.slice(e.dataStartIndex,e.dataEndIndex+1)),c=i.dataKey&&!i.allowDuplicatedCategory?r2(void 0===u?a:u,i.dataKey,n):u&&u[r]||a[r])?[].concat(vP(o),[pL(s,c)]):o},[])},vz=function(e,t,r,n){var o=n||{x:e.chartX,y:e.chartY},i="horizontal"===r?o.x:"vertical"===r?o.y:"centric"===r?o.angle:o.radius,a=e.orderedTooltipTicks,s=e.tooltipAxis,l=e.tooltipTicks,c=ps(i,a,l,s);if(c>=0&&l){var u=l[c]&&l[c].value,f=vF(e,t,c,u),p=vR(r,a,c,o);return{activeTooltipIndex:c,activeLabel:u,activePayload:f,activeCoordinate:p}}return null},v$=function(e,t){var r=t.axes,n=t.graphicalItems,o=t.axisType,i=t.axisIdKey,a=t.stackGroups,s=t.dataStartIndex,l=t.dataEndIndex,c=e.layout,u=e.children,f=e.stackOffset,p=py(c,o);return r.reduce(function(t,r){var d=void 0!==r.type.defaultProps?vM(vM({},r.type.defaultProps),r.props):r.props,h=d.type,y=d.dataKey,v=d.allowDataOverflow,m=d.allowDuplicatedCategory,b=d.scale,g=d.ticks,x=d.includeHidden,w=d[i];if(t[w])return t;var O=vL(e.data,{graphicalItems:n.filter(function(e){var t;return(i in e.props?e.props[i]:null==(t=e.type.defaultProps)?void 0:t[i])===w}),dataStartIndex:s,dataEndIndex:l}),j=O.length;(function(e,t,r){if("number"===r&&!0===t&&Array.isArray(e)){var n=null==e?void 0:e[0],o=null==e?void 0:e[1];if(n&&o&&rV(n)&&rV(o))return!0}return!1})(d.domain,v,h)&&(E=pD(d.domain,null,v),p&&("number"===h||"auto"!==b)&&(k=pa(O,y,"category")));var S=vB(h);if(!E||0===E.length){var P,E,A,k,M,T=null!=(M=d.domain)?M:S;if(y){if(E=pa(O,y,h),"category"===h&&p){var _=r0(E);m&&_?(A=E,E=nD()(0,j)):m||(E=pR(T,E,r).reduce(function(e,t){return e.indexOf(t)>=0?e:[].concat(vP(e),[t])},[]))}else if("category"===h)E=m?E.filter(function(e){return""!==e&&!r8()(e)}):pR(T,E,r).reduce(function(e,t){return e.indexOf(t)>=0||""===t||r8()(t)?e:[].concat(vP(e),[t])},[]);else if("number"===h){var C=pd(O,n.filter(function(e){var t,r,n=i in e.props?e.props[i]:null==(t=e.type.defaultProps)?void 0:t[i],o="hide"in e.props?e.props.hide:null==(r=e.type.defaultProps)?void 0:r.hide;return n===w&&(x||!o)}),y,o,c);C&&(E=C)}p&&("number"===h||"auto"!==b)&&(k=pa(O,y,"category"))}else E=p?nD()(0,j):a&&a[w]&&a[w].hasStack&&"number"===h?"expand"===f?[0,1]:p_(a[w].stackGroups,s,l):ph(O,n.filter(function(e){var t=i in e.props?e.props[i]:e.type.defaultProps[i],r="hide"in e.props?e.props.hide:e.type.defaultProps.hide;return t===w&&(x||!r)}),h,c,!0);"number"===h?(E=yM(u,E,w,o,g),T&&(E=pD(T,E,v))):"category"===h&&T&&E.every(function(e){return T.indexOf(e)>=0})&&(E=T)}return vM(vM({},t),{},vT({},w,vM(vM({},d),{},{axisType:o,domain:E,categoricalDomain:k,duplicateDomain:A,originalDomain:null!=(P=d.domain)?P:S,isCategorical:p,layout:c})))},{})},vW=function(e,t){var r=t.graphicalItems,n=t.Axis,o=t.axisType,i=t.axisIdKey,a=t.stackGroups,s=t.dataStartIndex,l=t.dataEndIndex,c=e.layout,u=e.children,f=vL(e.data,{graphicalItems:r,dataStartIndex:s,dataEndIndex:l}),p=f.length,d=py(c,o),h=-1;return r.reduce(function(e,t){var y,v=(void 0!==t.type.defaultProps?vM(vM({},t.type.defaultProps),t.props):t.props)[i],m=vB("number");return e[v]?e:(h++,y=d?nD()(0,p):a&&a[v]&&a[v].hasStack?yM(u,y=p_(a[v].stackGroups,s,l),v,o):yM(u,y=pD(m,ph(f,r.filter(function(e){var t,r,n=i in e.props?e.props[i]:null==(t=e.type.defaultProps)?void 0:t[i],o="hide"in e.props?e.props.hide:null==(r=e.type.defaultProps)?void 0:r.hide;return n===v&&!o}),"number",c),n.defaultProps.allowDataOverflow),v,o),vM(vM({},e),{},vT({},v,vM(vM({axisType:o},n.defaultProps),{},{hide:!0,orientation:rU()(vC,"".concat(o,".").concat(h%2),null),domain:y,originalDomain:m,isCategorical:d,layout:c}))))},{})},vU=function(e,t){var r=t.axisType,n=void 0===r?"xAxis":r,o=t.AxisComp,i=t.graphicalItems,a=t.stackGroups,s=t.dataStartIndex,l=t.dataEndIndex,c=e.children,u="".concat(n,"Id"),f=nb(c,o),p={};return f&&f.length?p=v$(e,{axes:f,graphicalItems:i,axisType:n,axisIdKey:u,stackGroups:a,dataStartIndex:s,dataEndIndex:l}):i&&i.length&&(p=vW(e,{Axis:o,graphicalItems:i,axisType:n,axisIdKey:u,stackGroups:a,dataStartIndex:s,dataEndIndex:l})),p},vq=function(e){var t=rQ(e),r=pm(t,!1,!0);return{tooltipTicks:r,orderedTooltipTicks:nR()(r,function(e){return e.coordinate}),tooltipAxis:t,tooltipAxisBandSize:pI(t,r)}},vY=function(e){var t=e.children,r=e.defaultShowTooltip,n=ng(t,p2),o=0,i=0;return e.data&&0!==e.data.length&&(i=e.data.length-1),n&&n.props&&(n.props.startIndex>=0&&(o=n.props.startIndex),n.props.endIndex>=0&&(i=n.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:o,dataEndIndex:i,activeTooltipIndex:-1,isTooltipActive:!!r}},vH=function(e){return"horizontal"===e?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:"vertical"===e?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:"centric"===e?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},vX=function(e,t){var r=e.props,n=e.graphicalItems,o=e.xAxisMap,i=void 0===o?{}:o,a=e.yAxisMap,s=void 0===a?{}:a,l=r.width,c=r.height,u=r.children,f=r.margin||{},p=ng(u,p2),d=ng(u,ii),h=Object.keys(s).reduce(function(e,t){var r=s[t],n=r.orientation;return r.mirror||r.hide?e:vM(vM({},e),{},vT({},n,e[n]+r.width))},{left:f.left||0,right:f.right||0}),y=Object.keys(i).reduce(function(e,t){var r=i[t],n=r.orientation;return r.mirror||r.hide?e:vM(vM({},e),{},vT({},n,rU()(e,"".concat(n))+r.height))},{top:f.top||0,bottom:f.bottom||0}),v=vM(vM({},y),h),m=v.bottom;p&&(v.bottom+=p.props.height||p2.defaultProps.height),d&&t&&(v=pf(v,n,r,t));var b=l-v.left-v.right,g=c-v.top-v.bottom;return vM(vM({brushBottom:m},v),{},{width:Math.max(b,0),height:Math.max(g,0)})},vV=function(e){var t=e.chartName,r=e.GraphicalChild,n=e.defaultTooltipEventType,o=void 0===n?"axis":n,i=e.validateTooltipEventTypes,a=void 0===i?["axis"]:i,s=e.axisComponents,l=e.legendContent,f=e.formatAxisMap,p=e.defaultProps,d=function(e,t){var r=t.graphicalItems,n=t.stackGroups,o=t.offset,i=t.updateId,a=t.dataStartIndex,l=t.dataEndIndex,c=e.barSize,u=e.layout,f=e.barGap,p=e.barCategoryGap,d=e.maxBarSize,h=vH(u),y=h.numericAxisName,v=h.cateAxisName,m=!!r&&!!r.length&&r.some(function(e){var t=nh(e&&e.type);return t&&t.indexOf("Bar")>=0}),b=[];return r.forEach(function(r,h){var g=vL(e.data,{graphicalItems:[r],dataStartIndex:a,dataEndIndex:l}),x=void 0!==r.type.defaultProps?vM(vM({},r.type.defaultProps),r.props):r.props,w=x.dataKey,O=x.maxBarSize,j=x["".concat(y,"Id")],S=x["".concat(v,"Id")],P=s.reduce(function(e,r){var n=t["".concat(r.axisType,"Map")],o=x["".concat(r.axisType,"Id")];n&&n[o]||"zAxis"===r.axisType||nL(!1);var i=n[o];return vM(vM({},e),{},vT(vT({},r.axisType,i),"".concat(r.axisType,"Ticks"),pm(i)))},{}),E=P[v],A=P["".concat(v,"Ticks")],k=n&&n[j]&&n[j].hasStack&&pT(r,n[j].stackGroups),M=nh(r.type).indexOf("Bar")>=0,T=pI(E,A),_=[],C=m&&pc({barSize:c,stackGroups:n,totalSize:"xAxis"===v?P[v].width:"yAxis"===v?P[v].height:void 0});if(M){var N,D,I=r8()(O)?d:O,R=null!=(N=null!=(D=pI(E,A,!0))?D:I)?N:0;_=pu({barGap:f,barCategoryGap:p,bandSize:R!==T?R:T,sizeList:C[S],maxBarSize:I}),R!==T&&(_=_.map(function(e){return vM(vM({},e),{},{position:vM(vM({},e.position),{},{offset:e.position.offset-R/2})})}))}var L=r&&r.type&&r.type.getComposedData;L&&b.push({props:vM(vM({},L(vM(vM({},P),{},{displayedData:g,props:e,dataKey:w,item:r,bandSize:T,barPosition:_,offset:o,stackedData:k,layout:u,dataStartIndex:a,dataEndIndex:l}))),{},vT(vT(vT({key:r.key||"item-".concat(h)},y,P[y]),v,P[v]),"animationId",i)),childIndex:nm(e.children).indexOf(r),item:r})}),b},h=function(e,n){var o=e.props,i=e.dataStartIndex,a=e.dataEndIndex,l=e.updateId;if(!nx({props:o}))return null;var c=o.children,u=o.layout,p=o.stackOffset,h=o.data,y=o.reverseStackOrder,v=vH(u),m=v.numericAxisName,b=v.cateAxisName,g=nb(c,r),x=pE(h,g,"".concat(m,"Id"),"".concat(b,"Id"),p,y),w=s.reduce(function(e,t){var r="".concat(t.axisType,"Map");return vM(vM({},e),{},vT({},r,vU(o,vM(vM({},t),{},{graphicalItems:g,stackGroups:t.axisType===m&&x,dataStartIndex:i,dataEndIndex:a}))))},{}),O=vX(vM(vM({},w),{},{props:o,graphicalItems:g}),null==n?void 0:n.legendBBox);Object.keys(w).forEach(function(e){w[e]=f(o,w[e],O,e.replace("Map",""),t)});var j=vq(w["".concat(b,"Map")]),S=d(o,vM(vM({},w),{},{dataStartIndex:i,dataEndIndex:a,updateId:l,graphicalItems:g,stackGroups:x,offset:O}));return vM(vM({formattedGraphicalItems:S,graphicalItems:g,offset:O,stackGroups:x},j),w)},y=function(e){var r;function n(e){var r,o,i,a,s;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");return a=n,s=[e],a=vj(a),vT(i=function(e,t){if(t&&("object"===vb(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");var r=e;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,vO()?Reflect.construct(a,s||[],vj(this).constructor):a.apply(this,s)),"eventEmitterSymbol",Symbol("rechartsEventEmitter")),vT(i,"accessibilityManager",new yR),vT(i,"handleLegendBBoxUpdate",function(e){if(e){var t=i.state,r=t.dataStartIndex,n=t.dataEndIndex,o=t.updateId;i.setState(vM({legendBBox:e},h({props:i.props,dataStartIndex:r,dataEndIndex:n,updateId:o},vM(vM({},i.state),{},{legendBBox:e}))))}}),vT(i,"handleReceiveSyncEvent",function(e,t,r){i.props.syncId===e&&(r!==i.eventEmitterSymbol||"function"==typeof i.props.syncMethod)&&i.applySyncEvent(t)}),vT(i,"handleBrushChange",function(e){var t=e.startIndex,r=e.endIndex;if(t!==i.state.dataStartIndex||r!==i.state.dataEndIndex){var n=i.state.updateId;i.setState(function(){return vM({dataStartIndex:t,dataEndIndex:r},h({props:i.props,dataStartIndex:t,dataEndIndex:r,updateId:n},i.state))}),i.triggerSyncEvent({dataStartIndex:t,dataEndIndex:r})}}),vT(i,"handleMouseEnter",function(e){var t=i.getMouseInfo(e);if(t){var r=vM(vM({},t),{},{isTooltipActive:!0});i.setState(r),i.triggerSyncEvent(r);var n=i.props.onMouseEnter;r7()(n)&&n(r,e)}}),vT(i,"triggeredAfterMouseMove",function(e){var t=i.getMouseInfo(e),r=t?vM(vM({},t),{},{isTooltipActive:!0}):{isTooltipActive:!1};i.setState(r),i.triggerSyncEvent(r);var n=i.props.onMouseMove;r7()(n)&&n(r,e)}),vT(i,"handleItemMouseEnter",function(e){i.setState(function(){return{isTooltipActive:!0,activeItem:e,activePayload:e.tooltipPayload,activeCoordinate:e.tooltipPosition||{x:e.cx,y:e.cy}}})}),vT(i,"handleItemMouseLeave",function(){i.setState(function(){return{isTooltipActive:!1}})}),vT(i,"handleMouseMove",function(e){e.persist(),i.throttleTriggeredAfterMouseMove(e)}),vT(i,"handleMouseLeave",function(e){i.throttleTriggeredAfterMouseMove.cancel();var t={isTooltipActive:!1};i.setState(t),i.triggerSyncEvent(t);var r=i.props.onMouseLeave;r7()(r)&&r(t,e)}),vT(i,"handleOuterEvent",function(e){var t,r,n=nA(e),o=rU()(i.props,"".concat(n));n&&r7()(o)&&o(null!=(t=/.*touch.*/i.test(n)?i.getMouseInfo(e.changedTouches[0]):i.getMouseInfo(e))?t:{},e)}),vT(i,"handleClick",function(e){var t=i.getMouseInfo(e);if(t){var r=vM(vM({},t),{},{isTooltipActive:!0});i.setState(r),i.triggerSyncEvent(r);var n=i.props.onClick;r7()(n)&&n(r,e)}}),vT(i,"handleMouseDown",function(e){var t=i.props.onMouseDown;r7()(t)&&t(i.getMouseInfo(e),e)}),vT(i,"handleMouseUp",function(e){var t=i.props.onMouseUp;r7()(t)&&t(i.getMouseInfo(e),e)}),vT(i,"handleTouchMove",function(e){null!=e.changedTouches&&e.changedTouches.length>0&&i.throttleTriggeredAfterMouseMove(e.changedTouches[0])}),vT(i,"handleTouchStart",function(e){null!=e.changedTouches&&e.changedTouches.length>0&&i.handleMouseDown(e.changedTouches[0])}),vT(i,"handleTouchEnd",function(e){null!=e.changedTouches&&e.changedTouches.length>0&&i.handleMouseUp(e.changedTouches[0])}),vT(i,"handleDoubleClick",function(e){var t=i.props.onDoubleClick;r7()(t)&&t(i.getMouseInfo(e),e)}),vT(i,"handleContextMenu",function(e){var t=i.props.onContextMenu;r7()(t)&&t(i.getMouseInfo(e),e)}),vT(i,"triggerSyncEvent",function(e){void 0!==i.props.syncId&&y_.emit(yC,i.props.syncId,e,i.eventEmitterSymbol)}),vT(i,"applySyncEvent",function(e){var t=i.props,r=t.layout,n=t.syncMethod,o=i.state.updateId,a=e.dataStartIndex,s=e.dataEndIndex;if(void 0!==e.dataStartIndex||void 0!==e.dataEndIndex)i.setState(vM({dataStartIndex:a,dataEndIndex:s},h({props:i.props,dataStartIndex:a,dataEndIndex:s,updateId:o},i.state)));else if(void 0!==e.activeTooltipIndex){var l=e.chartX,c=e.chartY,u=e.activeTooltipIndex,f=i.state,p=f.offset,d=f.tooltipTicks;if(!p)return;if("function"==typeof n)u=n(d,e);else if("value"===n){u=-1;for(var y=0;y<d.length;y++)if(d[y].value===e.activeLabel){u=y;break}}var v=vM(vM({},p),{},{x:p.left,y:p.top}),m=Math.min(l,v.x+v.width),b=Math.min(c,v.y+v.height),g=d[u]&&d[u].value,x=vF(i.state,i.props.data,u),w=d[u]?{x:"horizontal"===r?d[u].coordinate:m,y:"horizontal"===r?b:d[u].coordinate}:vD;i.setState(vM(vM({},e),{},{activeLabel:g,activeCoordinate:w,activePayload:x,activeTooltipIndex:u}))}else i.setState(e)}),vT(i,"renderCursor",function(e){var r,n=i.state,o=n.isTooltipActive,a=n.activeCoordinate,s=n.activePayload,l=n.offset,c=n.activeTooltipIndex,f=n.tooltipAxisBandSize,p=i.getTooltipEventType(),d=null!=(r=e.props.active)?r:o,h=i.props.layout,y=e.key||"_recharts-cursor";return u().createElement(vy,{key:y,activeCoordinate:a,activePayload:s,activeTooltipIndex:c,chartName:t,element:e,isActive:d,layout:h,offset:l,tooltipAxisBandSize:f,tooltipEventType:p})}),vT(i,"renderPolarAxis",function(e,t,r){var n=rU()(e,"type.axisType"),o=rU()(i.state,"".concat(n,"Map")),a=e.type.defaultProps,s=void 0!==a?vM(vM({},a),e.props):e.props,l=o&&o[s["".concat(n,"Id")]];return(0,c.cloneElement)(e,vM(vM({},l),{},{className:(0,rI.A)(n,l.className),key:e.key||"".concat(t,"-").concat(r),ticks:pm(l,!0)}))}),vT(i,"renderPolarGrid",function(e){var t=e.props,r=t.radialLines,n=t.polarAngles,o=t.polarRadius,a=i.state,s=a.radiusAxisMap,l=a.angleAxisMap,u=rQ(s),f=rQ(l),p=f.cx,d=f.cy,h=f.innerRadius,y=f.outerRadius;return(0,c.cloneElement)(e,{polarAngles:Array.isArray(n)?n:pm(f,!0).map(function(e){return e.coordinate}),polarRadius:Array.isArray(o)?o:pm(u,!0).map(function(e){return e.coordinate}),cx:p,cy:d,innerRadius:h,outerRadius:y,key:e.key||"polar-grid",radialLines:r})}),vT(i,"renderLegend",function(){var e=i.state.formattedGraphicalItems,t=i.props,r=t.children,n=t.width,o=t.height,a=i.props.margin||{},s=f7({children:r,formattedGraphicalItems:e,legendWidth:n-(a.left||0)-(a.right||0),legendContent:l});if(!s)return null;var u=s.item,f=vw(s,vv);return(0,c.cloneElement)(u,vM(vM({},f),{},{chartWidth:n,chartHeight:o,margin:a,onBBoxUpdate:i.handleLegendBBoxUpdate}))}),vT(i,"renderTooltip",function(){var e,t=i.props,r=t.children,n=t.accessibilityLayer,o=ng(r,oh);if(!o)return null;var a=i.state,s=a.isTooltipActive,l=a.activeCoordinate,u=a.activePayload,f=a.activeLabel,p=a.offset,d=null!=(e=o.props.active)?e:s;return(0,c.cloneElement)(o,{viewBox:vM(vM({},p),{},{x:p.left,y:p.top}),active:d,label:f,payload:d?u:[],coordinate:l,accessibilityLayer:n})}),vT(i,"renderBrush",function(e){var t=i.props,r=t.margin,n=t.data,o=i.state,a=o.offset,s=o.dataStartIndex,l=o.dataEndIndex,u=o.updateId;return(0,c.cloneElement)(e,{key:e.key||"_recharts-brush",onChange:pg(i.handleBrushChange,e.props.onChange),data:n,x:rV(e.props.x)?e.props.x:a.left,y:rV(e.props.y)?e.props.y:a.top+a.height+a.brushBottom-(r.bottom||0),width:rV(e.props.width)?e.props.width:a.width,startIndex:s,endIndex:l,updateId:"brush-".concat(u)})}),vT(i,"renderReferenceElement",function(e,t,r){if(!e)return null;var n=i.clipPathId,o=i.state,a=o.xAxisMap,s=o.yAxisMap,l=o.offset,u=e.type.defaultProps||{},f=e.props,p=f.xAxisId,d=void 0===p?u.xAxisId:p,h=f.yAxisId,y=void 0===h?u.yAxisId:h;return(0,c.cloneElement)(e,{key:e.key||"".concat(t,"-").concat(r),xAxis:a[d],yAxis:s[y],viewBox:{x:l.left,y:l.top,width:l.width,height:l.height},clipPathId:n})}),vT(i,"renderActivePoints",function(e){var t=e.item,r=e.activePoint,o=e.basePoint,i=e.childIndex,a=e.isRange,s=[],l=t.props.key,c=void 0!==t.item.type.defaultProps?vM(vM({},t.item.type.defaultProps),t.item.props):t.item.props,u=c.activeDot,f=vM(vM({index:i,dataKey:c.dataKey,cx:r.x,cy:r.y,r:4,fill:pl(t.item),strokeWidth:2,stroke:"#fff",payload:r.payload,value:r.value},nj(u,!1)),nl(u));return s.push(n.renderActiveDot(u,f,"".concat(l,"-activePoint-").concat(i))),o?s.push(n.renderActiveDot(u,vM(vM({},f),{},{cx:o.x,cy:o.y}),"".concat(l,"-basePoint-").concat(i))):a&&s.push(null),s}),vT(i,"renderGraphicChild",function(e,t,r){var n=i.filterFormatItem(e,t,r);if(!n)return null;var o=i.getTooltipEventType(),a=i.state,s=a.isTooltipActive,l=a.tooltipAxis,u=a.activeTooltipIndex,f=a.activeLabel,p=ng(i.props.children,oh),d=n.props,h=d.points,y=d.isRange,v=d.baseLine,m=void 0!==n.item.type.defaultProps?vM(vM({},n.item.type.defaultProps),n.item.props):n.item.props,b=m.activeDot,g=m.hide,x=m.activeBar,w=m.activeShape,O=!!(!g&&s&&p&&(b||x||w)),j={};"axis"!==o&&p&&"click"===p.props.trigger?j={onClick:pg(i.handleItemMouseEnter,e.props.onClick)}:"axis"!==o&&(j={onMouseLeave:pg(i.handleItemMouseLeave,e.props.onMouseLeave),onMouseEnter:pg(i.handleItemMouseEnter,e.props.onMouseEnter)});var S=(0,c.cloneElement)(e,vM(vM({},n.props),j));if(O)if(u>=0){if(l.dataKey&&!l.allowDuplicatedCategory){var P="function"==typeof l.dataKey?function(e){return"function"==typeof l.dataKey?l.dataKey(e.payload):null}:"payload.".concat(l.dataKey.toString());A=r2(h,P,f),k=y&&v&&r2(v,P,f)}else A=null==h?void 0:h[u],k=y&&v&&v[u];if(w||x){var E=void 0!==e.props.activeIndex?e.props.activeIndex:u;return[(0,c.cloneElement)(e,vM(vM(vM({},n.props),j),{},{activeIndex:E})),null,null]}if(!r8()(A))return[S].concat(vP(i.renderActivePoints({item:n,activePoint:A,basePoint:k,childIndex:u,isRange:y})))}else{var A,k,M,T=(null!=(M=i.getItemByXY(i.state.activeCoordinate))?M:{graphicalItem:S}).graphicalItem,_=T.item,C=void 0===_?e:_,N=T.childIndex,D=vM(vM(vM({},n.props),j),{},{activeIndex:N});return[(0,c.cloneElement)(C,D),null,null]}return y?[S,null,null]:[S,null]}),vT(i,"renderCustomized",function(e,t,r){return(0,c.cloneElement)(e,vM(vM({key:"recharts-customized-".concat(r)},i.props),i.state))}),vT(i,"renderMap",{CartesianGrid:{handler:vI,once:!0},ReferenceArea:{handler:i.renderReferenceElement},ReferenceLine:{handler:vI},ReferenceDot:{handler:i.renderReferenceElement},XAxis:{handler:vI},YAxis:{handler:vI},Brush:{handler:i.renderBrush,once:!0},Bar:{handler:i.renderGraphicChild},Line:{handler:i.renderGraphicChild},Area:{handler:i.renderGraphicChild},Radar:{handler:i.renderGraphicChild},RadialBar:{handler:i.renderGraphicChild},Scatter:{handler:i.renderGraphicChild},Pie:{handler:i.renderGraphicChild},Funnel:{handler:i.renderGraphicChild},Tooltip:{handler:i.renderCursor,once:!0},PolarGrid:{handler:i.renderPolarGrid,once:!0},PolarAngleAxis:{handler:i.renderPolarAxis},PolarRadiusAxis:{handler:i.renderPolarAxis},Customized:{handler:i.renderCustomized}}),i.clipPathId="".concat(null!=(r=e.id)?r:rZ("recharts"),"-clip"),i.throttleTriggeredAfterMouseMove=rL()(i.triggeredAfterMouseMove,null!=(o=e.throttleDelay)?o:1e3/60),i.state={},i}if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(e&&e.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),e&&vS(n,e),r=[{key:"componentDidMount",value:function(){var e,t;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:null!=(e=this.props.margin.left)?e:0,top:null!=(t=this.props.margin.top)?t:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var e=this.props,t=e.children,r=e.data,n=e.height,o=e.layout,i=ng(t,oh);if(i){var a=i.props.defaultIndex;if("number"==typeof a&&!(a<0)&&!(a>this.state.tooltipTicks.length-1)){var s=this.state.tooltipTicks[a]&&this.state.tooltipTicks[a].value,l=vF(this.state,r,a,s),c=this.state.tooltipTicks[a].coordinate,u=(this.state.offset.top+n)/2,f="horizontal"===o?{x:c,y:u}:{y:c,x:u},p=this.state.formattedGraphicalItems.find(function(e){return"Scatter"===e.item.type.name});p&&(f=vM(vM({},f),p.props.points[a].tooltipPosition),l=p.props.points[a].tooltipPayload);var d={activeTooltipIndex:a,isTooltipActive:!0,activeLabel:s,activePayload:l,activeCoordinate:f};this.setState(d),this.renderCursor(i),this.accessibilityManager.setIndex(a)}}}},{key:"getSnapshotBeforeUpdate",value:function(e,t){if(!this.props.accessibilityLayer)return null;if(this.state.tooltipTicks!==t.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==e.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==e.margin){var r,n;this.accessibilityManager.setDetails({offset:{left:null!=(r=this.props.margin.left)?r:0,top:null!=(n=this.props.margin.top)?n:0}})}return null}},{key:"componentDidUpdate",value:function(e){nS([ng(e.children,oh)],[ng(this.props.children,oh)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var e=ng(this.props.children,oh);if(e&&"boolean"==typeof e.props.shared){var t=e.props.shared?"axis":"item";return a.indexOf(t)>=0?t:o}return o}},{key:"getMouseInfo",value:function(e){if(!this.container)return null;var t=this.container,r=t.getBoundingClientRect(),n={top:r.top+window.scrollY-document.documentElement.clientTop,left:r.left+window.scrollX-document.documentElement.clientLeft},o={chartX:Math.round(e.pageX-n.left),chartY:Math.round(e.pageY-n.top)},i=r.width/t.offsetWidth||1,a=this.inRange(o.chartX,o.chartY,i);if(!a)return null;var s=this.state,l=s.xAxisMap,c=s.yAxisMap,u=this.getTooltipEventType(),f=vz(this.state,this.props.data,this.props.layout,a);if("axis"!==u&&l&&c){var p=rQ(l).scale,d=rQ(c).scale,h=p&&p.invert?p.invert(o.chartX):null,y=d&&d.invert?d.invert(o.chartY):null;return vM(vM({},o),{},{xValue:h,yValue:y},f)}return f?vM(vM({},o),f):null}},{key:"inRange",value:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=this.props.layout,o=e/r,i=t/r;if("horizontal"===n||"vertical"===n){var a=this.state.offset;return o>=a.left&&o<=a.left+a.width&&i>=a.top&&i<=a.top+a.height?{x:o,y:i}:null}var s=this.state,l=s.angleAxisMap,c=s.radiusAxisMap;return l&&c?di({x:o,y:i},rQ(l)):null}},{key:"parseEventsOfWrapper",value:function(){var e=this.props.children,t=this.getTooltipEventType(),r=ng(e,oh),n={};return r&&"axis"===t&&(n="click"===r.props.trigger?{onClick:this.handleClick}:{onMouseEnter:this.handleMouseEnter,onDoubleClick:this.handleDoubleClick,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onContextMenu:this.handleContextMenu}),vM(vM({},nl(this.props,this.handleOuterEvent)),n)}},{key:"addListener",value:function(){y_.on(yC,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){y_.removeListener(yC,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(e,t,r){for(var n=this.state.formattedGraphicalItems,o=0,i=n.length;o<i;o++){var a=n[o];if(a.item===e||a.props.key===e.key||t===nh(a.item.type)&&r===a.childIndex)return a}return null}},{key:"renderClipPath",value:function(){var e=this.clipPathId,t=this.state.offset,r=t.left,n=t.top,o=t.height,i=t.width;return u().createElement("defs",null,u().createElement("clipPath",{id:e},u().createElement("rect",{x:r,y:n,height:o,width:i})))}},{key:"getXScales",value:function(){var e=this.state.xAxisMap;return e?Object.entries(e).reduce(function(e,t){var r=vx(t,2),n=r[0],o=r[1];return vM(vM({},e),{},vT({},n,o.scale))},{}):null}},{key:"getYScales",value:function(){var e=this.state.yAxisMap;return e?Object.entries(e).reduce(function(e,t){var r=vx(t,2),n=r[0],o=r[1];return vM(vM({},e),{},vT({},n,o.scale))},{}):null}},{key:"getXScaleByAxisId",value:function(e){var t;return null==(t=this.state.xAxisMap)||null==(t=t[e])?void 0:t.scale}},{key:"getYScaleByAxisId",value:function(e){var t;return null==(t=this.state.yAxisMap)||null==(t=t[e])?void 0:t.scale}},{key:"getItemByXY",value:function(e){var t=this.state,r=t.formattedGraphicalItems,n=t.activeItem;if(r&&r.length)for(var o=0,i=r.length;o<i;o++){var a=r[o],s=a.props,l=a.item,c=void 0!==l.type.defaultProps?vM(vM({},l.type.defaultProps),l.props):l.props,u=nh(l.type);if("Bar"===u){var f=(s.data||[]).find(function(t){return aP(e,t)});if(f)return{graphicalItem:a,payload:f}}else if("RadialBar"===u){var p=(s.data||[]).find(function(t){return di(e,t)});if(p)return{graphicalItem:a,payload:p}}else if(he(a,n)||ht(a,n)||hr(a,n)){var d=function(e){var t,r,n,o=e.activeTooltipItem,i=e.graphicalItem,a=e.itemData,s=(he(i,o)?t="trapezoids":ht(i,o)?t="sectors":hr(i,o)&&(t="points"),t),l=he(i,o)?null==(r=o.tooltipPayload)||null==(r=r[0])||null==(r=r.payload)?void 0:r.payload:ht(i,o)?null==(n=o.tooltipPayload)||null==(n=n[0])||null==(n=n.payload)?void 0:n.payload:hr(i,o)?o.payload:{},c=a.filter(function(e,t){var r=fr()(l,e),n=i.props[s].filter(function(e){var t;return(he(i,o)?t=hn:ht(i,o)?t=ho:hr(i,o)&&(t=hi),t)(e,o)}),a=i.props[s].indexOf(n[n.length-1]);return r&&t===a});return a.indexOf(c[c.length-1])}({graphicalItem:a,activeTooltipItem:n,itemData:c.data}),h=void 0===c.activeIndex?d:c.activeIndex;return{graphicalItem:vM(vM({},a),{},{childIndex:h}),payload:hr(a,n)?c.data[d]:a.props.data[d]}}}return null}},{key:"render",value:function(){var e,t,r=this;if(!nx(this))return null;var n=this.props,o=n.children,i=n.className,a=n.width,s=n.height,l=n.style,c=n.compact,f=n.title,p=n.desc,d=nj(vw(n,vm),!1);if(c)return u().createElement(h8,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},u().createElement(nz,vg({},d,{width:a,height:s,title:f,desc:p}),this.renderClipPath(),nE(o,this.renderMap)));this.props.accessibilityLayer&&(d.tabIndex=null!=(e=this.props.tabIndex)?e:0,d.role=null!=(t=this.props.role)?t:"application",d.onKeyDown=function(e){r.accessibilityManager.keyboardEvent(e)},d.onFocus=function(){r.accessibilityManager.focus()});var h=this.parseEventsOfWrapper();return u().createElement(h8,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},u().createElement("div",vg({className:(0,rI.A)("recharts-wrapper",i),style:vM({position:"relative",cursor:"default",width:a,height:s},l)},h,{ref:function(e){r.container=e}}),u().createElement(nz,vg({},d,{width:a,height:s,title:f,desc:p,style:vN}),this.renderClipPath(),nE(o,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,v_(n.key),n)}}(n.prototype,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(c.Component);vT(y,"displayName",t),vT(y,"defaultProps",vM({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},p)),vT(y,"getDerivedStateFromProps",function(e,t){var r=e.dataKey,n=e.data,o=e.children,i=e.width,a=e.height,s=e.layout,l=e.stackOffset,c=e.margin,u=t.dataStartIndex,f=t.dataEndIndex;if(void 0===t.updateId){var p=vY(e);return vM(vM(vM({},p),{},{updateId:0},h(vM(vM({props:e},p),{},{updateId:0}),t)),{},{prevDataKey:r,prevData:n,prevWidth:i,prevHeight:a,prevLayout:s,prevStackOffset:l,prevMargin:c,prevChildren:o})}if(r!==t.prevDataKey||n!==t.prevData||i!==t.prevWidth||a!==t.prevHeight||s!==t.prevLayout||l!==t.prevStackOffset||!nr(c,t.prevMargin)){var d=vY(e),y={chartX:t.chartX,chartY:t.chartY,isTooltipActive:t.isTooltipActive},v=vM(vM({},vz(t,n,s)),{},{updateId:t.updateId+1}),m=vM(vM(vM({},d),y),v);return vM(vM(vM({},m),h(vM({props:e},m),t)),{},{prevDataKey:r,prevData:n,prevWidth:i,prevHeight:a,prevLayout:s,prevStackOffset:l,prevMargin:c,prevChildren:o})}if(!nS(o,t.prevChildren)){var b,g,x,w,O=ng(o,p2),j=O&&null!=(b=null==(g=O.props)?void 0:g.startIndex)?b:u,S=O&&null!=(x=null==(w=O.props)?void 0:w.endIndex)?x:f,P=r8()(n)||j!==u||S!==f?t.updateId+1:t.updateId;return vM(vM({updateId:P},h(vM(vM({props:e},t),{},{updateId:P,dataStartIndex:j,dataEndIndex:S}),t)),{},{prevChildren:o,dataStartIndex:j,dataEndIndex:S})}return null}),vT(y,"renderActiveDot",function(e,t,r){var n;return n=(0,c.isValidElement)(e)?(0,c.cloneElement)(e,t):r7()(e)?e(t):u().createElement(is,t),u().createElement(nU,{className:"recharts-active-dot",key:r},n)});var v=(0,c.forwardRef)(function(e,t){return u().createElement(y,vg({},e,{ref:t}))});return v.displayName=y.displayName,v},vG=["points","className","baseLinePoints","connectNulls"];function vK(){return(vK=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function vZ(e){return function(e){if(Array.isArray(e))return vJ(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return vJ(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return vJ(e,t)}}(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function vJ(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var vQ=function(e){return e&&e.x===+e.x&&e.y===+e.y},v0=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=[[]];return e.forEach(function(e){vQ(e)?t[t.length-1].push(e):t[t.length-1].length>0&&t.push([])}),vQ(e[0])&&t[t.length-1].push(e[0]),t[t.length-1].length<=0&&(t=t.slice(0,-1)),t},v1=function(e,t){var r=v0(e);t&&(r=[r.reduce(function(e,t){return[].concat(vZ(e),vZ(t))},[])]);var n=r.map(function(e){return e.reduce(function(e,t,r){return"".concat(e).concat(0===r?"M":"L").concat(t.x,",").concat(t.y)},"")}).join("");return 1===r.length?"".concat(n,"Z"):n},v2=function(e,t,r){var n=v1(e,r);return"".concat("Z"===n.slice(-1)?n.slice(0,-1):n,"L").concat(v1(t.reverse(),r).slice(1))},v5=function(e){var t=e.points,r=e.className,n=e.baseLinePoints,o=e.connectNulls,i=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,vG);if(!t||!t.length)return null;var a=(0,rI.A)("recharts-polygon",r);if(n&&n.length){var s=i.stroke&&"none"!==i.stroke,l=v2(t,n,o);return u().createElement("g",{className:a},u().createElement("path",vK({},nj(i,!0),{fill:"Z"===l.slice(-1)?i.fill:"none",stroke:"none",d:l})),s?u().createElement("path",vK({},nj(i,!0),{fill:"none",d:v1(t,o)})):null,s?u().createElement("path",vK({},nj(i,!0),{fill:"none",d:v1(n,o)})):null)}var c=v1(t,o);return u().createElement("path",vK({},nj(i,!0),{fill:"Z"===c.slice(-1)?i.fill:"none",className:a,d:c}))};function v3(e){return(v3="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function v4(){return(v4=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function v8(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function v6(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?v8(Object(r),!0).forEach(function(t){mr(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):v8(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function v7(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,mn(n.key),n)}}function v9(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(v9=function(){return!!e})()}function me(e){return(me=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function mt(e,t){return(mt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function mr(e,t,r){return(t=mn(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function mn(e){var t=function(e,t){if("object"!=v3(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=v3(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==v3(t)?t:t+""}var mo=Math.PI/180,mi=function(e){var t,r;function n(){var e,t;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");return e=n,t=arguments,e=me(e),function(e,t){if(t&&("object"===v3(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");var r=e;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,v9()?Reflect.construct(e,t||[],me(this).constructor):e.apply(this,t))}if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(e&&e.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),e&&mt(n,e),t=[{key:"getTickLineCoord",value:function(e){var t=this.props,r=t.cx,n=t.cy,o=t.radius,i=t.orientation,a=t.tickSize,s=p9(r,n,o,e.coordinate),l=p9(r,n,o+("inner"===i?-1:1)*(a||8),e.coordinate);return{x1:s.x,y1:s.y,x2:l.x,y2:l.y}}},{key:"getTickTextAnchor",value:function(e){var t=this.props.orientation,r=Math.cos(-e.coordinate*mo);return r>1e-5?"outer"===t?"start":"end":r<-1e-5?"outer"===t?"end":"start":"middle"}},{key:"renderAxisLine",value:function(){var e=this.props,t=e.cx,r=e.cy,n=e.radius,o=e.axisLine,i=e.axisLineType,a=v6(v6({},nj(this.props,!1)),{},{fill:"none"},nj(o,!1));if("circle"===i)return u().createElement(is,v4({className:"recharts-polar-angle-axis-line"},a,{cx:t,cy:r,r:n}));var s=this.props.ticks.map(function(e){return p9(t,r,n,e.coordinate)});return u().createElement(v5,v4({className:"recharts-polar-angle-axis-line"},a,{points:s}))}},{key:"renderTicks",value:function(){var e=this,t=this.props,r=t.ticks,o=t.tick,i=t.tickLine,a=t.tickFormatter,s=t.stroke,l=nj(this.props,!1),c=nj(o,!1),f=v6(v6({},l),{},{fill:"none"},nj(i,!1)),p=r.map(function(t,r){var p=e.getTickLineCoord(t),d=v6(v6(v6({textAnchor:e.getTickTextAnchor(t)},l),{},{stroke:"none",fill:s},c),{},{index:r,payload:t,x:p.x2,y:p.y2});return u().createElement(nU,v4({className:(0,rI.A)("recharts-polar-angle-axis-tick",da(o)),key:"tick-".concat(t.coordinate)},nc(e.props,t,r)),i&&u().createElement("line",v4({className:"recharts-polar-angle-axis-tick-line"},f,p)),o&&n.renderTickItem(o,d,a?a(t.value,r):t.value))});return u().createElement(nU,{className:"recharts-polar-angle-axis-ticks"},p)}},{key:"render",value:function(){var e=this.props,t=e.ticks,r=e.radius,n=e.axisLine;return!(r<=0)&&t&&t.length?u().createElement(nU,{className:(0,rI.A)("recharts-polar-angle-axis",this.props.className)},n&&this.renderAxisLine(),this.renderTicks()):null}}],r=[{key:"renderTickItem",value:function(e,t,r){var n;return u().isValidElement(e)?u().cloneElement(e,t):r7()(e)?e(t):u().createElement(ss,v4({},t,{className:"recharts-polar-angle-axis-tick-value"}),r)}}],t&&v7(n.prototype,t),r&&v7(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(c.PureComponent);mr(mi,"displayName","PolarAngleAxis"),mr(mi,"axisType","angleAxis"),mr(mi,"defaultProps",{type:"category",angleAxisId:0,scale:"auto",cx:0,cy:0,orientation:"outer",axisLine:!0,tickLine:!0,tickSize:8,tick:!0,hide:!1,allowDuplicatedCategory:!0});var ma=r(57088),ms=r.n(ma),ml=r(10034),mc=r.n(ml),mu=["cx","cy","angle","ticks","axisLine"],mf=["ticks","tick","angle","tickFormatter","stroke"];function mp(e){return(mp="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function md(){return(md=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function mh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function my(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?mh(Object(r),!0).forEach(function(t){mw(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):mh(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function mv(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}function mm(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,mO(n.key),n)}}function mb(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(mb=function(){return!!e})()}function mg(e){return(mg=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function mx(e,t){return(mx=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function mw(e,t,r){return(t=mO(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function mO(e){var t=function(e,t){if("object"!=mp(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=mp(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==mp(t)?t:t+""}var mj=function(e){var t,r;function n(){var e,t;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");return e=n,t=arguments,e=mg(e),function(e,t){if(t&&("object"===mp(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");var r=e;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,mb()?Reflect.construct(e,t||[],mg(this).constructor):e.apply(this,t))}if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(e&&e.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),e&&mx(n,e),t=[{key:"getTickValueCoord",value:function(e){var t=e.coordinate,r=this.props,n=r.angle;return p9(r.cx,r.cy,t,n)}},{key:"getTickTextAnchor",value:function(){var e;switch(this.props.orientation){case"left":e="end";break;case"right":e="start";break;default:e="middle"}return e}},{key:"getViewBox",value:function(){var e=this.props,t=e.cx,r=e.cy,n=e.angle,o=e.ticks,i=ms()(o,function(e){return e.coordinate||0});return{cx:t,cy:r,startAngle:n,endAngle:n,innerRadius:mc()(o,function(e){return e.coordinate||0}).coordinate||0,outerRadius:i.coordinate||0}}},{key:"renderAxisLine",value:function(){var e=this.props,t=e.cx,r=e.cy,n=e.angle,o=e.ticks,i=e.axisLine,a=mv(e,mu),s=o.reduce(function(e,t){return[Math.min(e[0],t.coordinate),Math.max(e[1],t.coordinate)]},[1/0,-1/0]),l=p9(t,r,s[0],n),c=p9(t,r,s[1],n),f=my(my(my({},nj(a,!1)),{},{fill:"none"},nj(i,!1)),{},{x1:l.x,y1:l.y,x2:c.x,y2:c.y});return u().createElement("line",md({className:"recharts-polar-radius-axis-line"},f))}},{key:"renderTicks",value:function(){var e=this,t=this.props,r=t.ticks,o=t.tick,i=t.angle,a=t.tickFormatter,s=t.stroke,l=mv(t,mf),c=this.getTickTextAnchor(),f=nj(l,!1),p=nj(o,!1),d=r.map(function(t,r){var l=e.getTickValueCoord(t),d=my(my(my(my({textAnchor:c,transform:"rotate(".concat(90-i,", ").concat(l.x,", ").concat(l.y,")")},f),{},{stroke:"none",fill:s},p),{},{index:r},l),{},{payload:t});return u().createElement(nU,md({className:(0,rI.A)("recharts-polar-radius-axis-tick",da(o)),key:"tick-".concat(t.coordinate)},nc(e.props,t,r)),n.renderTickItem(o,d,a?a(t.value,r):t.value))});return u().createElement(nU,{className:"recharts-polar-radius-axis-ticks"},d)}},{key:"render",value:function(){var e=this.props,t=e.ticks,r=e.axisLine,n=e.tick;return t&&t.length?u().createElement(nU,{className:(0,rI.A)("recharts-polar-radius-axis",this.props.className)},r&&this.renderAxisLine(),n&&this.renderTicks(),dm.renderCallByParent(this.props,this.getViewBox())):null}}],r=[{key:"renderTickItem",value:function(e,t,r){var n;return u().isValidElement(e)?u().cloneElement(e,t):r7()(e)?e(t):u().createElement(ss,md({},t,{className:"recharts-polar-radius-axis-tick-value"}),r)}}],t&&mm(n.prototype,t),r&&mm(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(c.PureComponent);function mS(e){return(mS="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function mP(){return(mP=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function mE(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function mA(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?mE(Object(r),!0).forEach(function(t){mC(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):mE(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function mk(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,mN(n.key),n)}}function mM(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(mM=function(){return!!e})()}function mT(e){return(mT=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function m_(e,t){return(m_=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function mC(e,t,r){return(t=mN(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function mN(e){var t=function(e,t){if("object"!=mS(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=mS(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==mS(t)?t:t+""}mw(mj,"displayName","PolarRadiusAxis"),mw(mj,"axisType","radiusAxis"),mw(mj,"defaultProps",{type:"number",radiusAxisId:0,cx:0,cy:0,angle:0,orientation:"right",stroke:"#ccc",axisLine:!0,tick:!0,tickCount:5,allowDataOverflow:!1,scale:"auto",allowDuplicatedCategory:!0});var mD=function(e){var t,r;function n(e){var t,r,o;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");return r=n,o=[e],r=mT(r),mC(t=function(e,t){if(t&&("object"===mS(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");var r=e;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,mM()?Reflect.construct(r,o||[],mT(this).constructor):r.apply(this,o)),"pieRef",null),mC(t,"sectorRefs",[]),mC(t,"id",rZ("recharts-pie-")),mC(t,"handleAnimationEnd",function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),r7()(e)&&e()}),mC(t,"handleAnimationStart",function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),r7()(e)&&e()}),t.state={isAnimationFinished:!e.isAnimationActive,prevIsAnimationActive:e.isAnimationActive,prevAnimationId:e.animationId,sectorToFocus:0},t}if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(e&&e.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),e&&m_(n,e),t=[{key:"isActiveIndex",value:function(e){var t=this.props.activeIndex;return Array.isArray(t)?-1!==t.indexOf(e):e===t}},{key:"hasActiveIndex",value:function(){var e=this.props.activeIndex;return Array.isArray(e)?0!==e.length:e||0===e}},{key:"renderLabels",value:function(e){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var t=this.props,r=t.label,o=t.labelLine,i=t.dataKey,a=t.valueKey,s=nj(this.props,!1),l=nj(r,!1),c=nj(o,!1),f=r&&r.offsetRadius||20,p=e.map(function(e,t){var p=(e.startAngle+e.endAngle)/2,d=p9(e.cx,e.cy,e.outerRadius+f,p),h=mA(mA(mA(mA({},s),e),{},{stroke:"none"},l),{},{index:t,textAnchor:n.getTextAnchor(d.x,e.cx)},d),y=mA(mA(mA(mA({},s),e),{},{fill:"none",stroke:e.fill},c),{},{index:t,points:[p9(e.cx,e.cy,e.outerRadius,p),d]}),v=i;return r8()(i)&&r8()(a)?v="value":r8()(i)&&(v=a),u().createElement(nU,{key:"label-".concat(e.startAngle,"-").concat(e.endAngle,"-").concat(e.midAngle,"-").concat(t)},o&&n.renderLabelLineItem(o,y,"line"),n.renderLabelItem(r,h,pi(e,v)))});return u().createElement(nU,{className:"recharts-pie-labels"},p)}},{key:"renderSectorsStatically",value:function(e){var t=this,r=this.props,n=r.activeShape,o=r.blendStroke,i=r.inactiveShape;return e.map(function(r,a){if((null==r?void 0:r.startAngle)===0&&(null==r?void 0:r.endAngle)===0&&1!==e.length)return null;var s=t.isActiveIndex(a),l=i&&t.hasActiveIndex()?i:null,c=mA(mA({},r),{},{stroke:o?r.fill:r.stroke,tabIndex:-1});return u().createElement(nU,mP({ref:function(e){e&&!t.sectorRefs.includes(e)&&t.sectorRefs.push(e)},tabIndex:-1,className:"recharts-pie-sector"},nc(t.props,r,a),{key:"sector-".concat(null==r?void 0:r.startAngle,"-").concat(null==r?void 0:r.endAngle,"-").concat(r.midAngle,"-").concat(a)}),u().createElement(d9,mP({option:s?n:l,isActive:s,shapeType:"sector"},c)))})}},{key:"renderSectorsWithAnimation",value:function(){var e=this,t=this.props,r=t.sectors,n=t.isAnimationActive,o=t.animationBegin,i=t.animationDuration,a=t.animationEasing,s=t.animationId,l=this.state,c=l.prevSectors,f=l.prevIsAnimationActive;return u().createElement(ab,{begin:o,duration:i,isActive:n,easing:a,from:{t:0},to:{t:1},key:"pie-".concat(s,"-").concat(f),onAnimationStart:this.handleAnimationStart,onAnimationEnd:this.handleAnimationEnd},function(t){var n=t.t,o=[],i=(r&&r[0]).startAngle;return r.forEach(function(e,t){var r=c&&c[t],a=t>0?rU()(e,"paddingAngle",0):0;if(r){var s=r1(r.endAngle-r.startAngle,e.endAngle-e.startAngle),l=mA(mA({},e),{},{startAngle:i+a,endAngle:i+s(n)+a});o.push(l),i=l.endAngle}else{var u=r1(0,e.endAngle-e.startAngle)(n),f=mA(mA({},e),{},{startAngle:i+a,endAngle:i+u+a});o.push(f),i=f.endAngle}}),u().createElement(nU,null,e.renderSectorsStatically(o))})}},{key:"attachKeyboardHandlers",value:function(e){var t=this;e.onkeydown=function(e){if(!e.altKey)switch(e.key){case"ArrowLeft":var r=++t.state.sectorToFocus%t.sectorRefs.length;t.sectorRefs[r].focus(),t.setState({sectorToFocus:r});break;case"ArrowRight":var n=--t.state.sectorToFocus<0?t.sectorRefs.length-1:t.state.sectorToFocus%t.sectorRefs.length;t.sectorRefs[n].focus(),t.setState({sectorToFocus:n});break;case"Escape":t.sectorRefs[t.state.sectorToFocus].blur(),t.setState({sectorToFocus:0})}}}},{key:"renderSectors",value:function(){var e=this.props,t=e.sectors,r=e.isAnimationActive,n=this.state.prevSectors;return r&&t&&t.length&&(!n||!fr()(n,t))?this.renderSectorsWithAnimation():this.renderSectorsStatically(t)}},{key:"componentDidMount",value:function(){this.pieRef&&this.attachKeyboardHandlers(this.pieRef)}},{key:"render",value:function(){var e=this,t=this.props,r=t.hide,n=t.sectors,o=t.className,i=t.label,a=t.cx,s=t.cy,l=t.innerRadius,c=t.outerRadius,f=t.isAnimationActive,p=this.state.isAnimationFinished;if(r||!n||!n.length||!rV(a)||!rV(s)||!rV(l)||!rV(c))return null;var d=(0,rI.A)("recharts-pie",o);return u().createElement(nU,{tabIndex:this.props.rootTabIndex,className:d,ref:function(t){e.pieRef=t}},this.renderSectors(),i&&this.renderLabels(n),dm.renderCallByParent(this.props,null,!1),(!f||p)&&dR.renderCallByParent(this.props,n,!1))}}],r=[{key:"getDerivedStateFromProps",value:function(e,t){return t.prevIsAnimationActive!==e.isAnimationActive?{prevIsAnimationActive:e.isAnimationActive,prevAnimationId:e.animationId,curSectors:e.sectors,prevSectors:[],isAnimationFinished:!0}:e.isAnimationActive&&e.animationId!==t.prevAnimationId?{prevAnimationId:e.animationId,curSectors:e.sectors,prevSectors:t.curSectors,isAnimationFinished:!0}:e.sectors!==t.curSectors?{curSectors:e.sectors,isAnimationFinished:!0}:null}},{key:"getTextAnchor",value:function(e,t){return e>t?"start":e<t?"end":"middle"}},{key:"renderLabelLineItem",value:function(e,t,r){if(u().isValidElement(e))return u().cloneElement(e,t);if(r7()(e))return e(t);var n=(0,rI.A)("recharts-pie-label-line","boolean"!=typeof e?e.className:"");return u().createElement(vi,mP({},t,{key:r,type:"linear",className:n}))}},{key:"renderLabelItem",value:function(e,t,r){if(u().isValidElement(e))return u().cloneElement(e,t);var n=r;if(r7()(e)&&(n=e(t),u().isValidElement(n)))return n;var o=(0,rI.A)("recharts-pie-label-text","boolean"==typeof e||r7()(e)?"":e.className);return u().createElement(ss,mP({},t,{alignmentBaseline:"middle",className:o}),n)}}],t&&mk(n.prototype,t),r&&mk(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(c.PureComponent);mC(mD,"displayName","Pie"),mC(mD,"defaultProps",{stroke:"#fff",fill:"#808080",legendType:"rect",cx:"50%",cy:"50%",startAngle:0,endAngle:360,innerRadius:0,outerRadius:"80%",paddingAngle:0,labelLine:!0,hide:!1,minAngle:0,isAnimationActive:!ot.isSsr,animationBegin:400,animationDuration:1500,animationEasing:"ease",nameKey:"name",blendStroke:!1,rootTabIndex:0}),mC(mD,"parseDeltaAngle",function(e,t){return rH(t-e)*Math.min(Math.abs(t-e),360)}),mC(mD,"getRealPieData",function(e){var t=e.data,r=e.children,n=nj(e,!1),o=nb(r,dS);return t&&t.length?t.map(function(e,t){return mA(mA(mA({payload:e},n),e),o&&o[t]&&o[t].props)}):o&&o.length?o.map(function(e){return mA(mA({},n),e.props)}):[]}),mC(mD,"parseCoordinateOfPie",function(e,t){var r=t.top,n=t.left,o=t.width,i=t.height,a=de(o,i);return{cx:n+rJ(e.cx,o,o/2),cy:r+rJ(e.cy,i,i/2),innerRadius:rJ(e.innerRadius,a,0),outerRadius:rJ(e.outerRadius,a,.8*a),maxRadius:e.maxRadius||Math.sqrt(o*o+i*i)/2}}),mC(mD,"getComposedData",function(e){var t,r,n=e.item,o=e.offset,i=void 0!==n.type.defaultProps?mA(mA({},n.type.defaultProps),n.props):n.props,a=mD.getRealPieData(i);if(!a||!a.length)return null;var s=i.cornerRadius,l=i.startAngle,c=i.endAngle,u=i.paddingAngle,f=i.dataKey,p=i.nameKey,d=i.valueKey,h=i.tooltipType,y=Math.abs(i.minAngle),v=mD.parseCoordinateOfPie(i,o),m=mD.parseDeltaAngle(l,c),b=Math.abs(m),g=f;r8()(f)&&r8()(d)?(r3(!1,'Use "dataKey" to specify the value of pie,\n      the props "valueKey" will be deprecated in 1.1.0'),g="value"):r8()(f)&&(r3(!1,'Use "dataKey" to specify the value of pie,\n      the props "valueKey" will be deprecated in 1.1.0'),g=d);var x=a.filter(function(e){return 0!==pi(e,g,0)}).length,w=b-x*y-(b>=360?x:x-1)*u,O=a.reduce(function(e,t){var r=pi(t,g,0);return e+(rV(r)?r:0)},0);return O>0&&(t=a.map(function(e,t){var n,o=pi(e,g,0),i=pi(e,p,t),a=(rV(o)?o:0)/O,c=(n=t?r.endAngle+rH(m)*u*(0!==o):l)+rH(m)*((0!==o?y:0)+a*w),f=(n+c)/2,d=(v.innerRadius+v.outerRadius)/2,b=[{name:i,value:o,payload:e,dataKey:g,type:h}],x=p9(v.cx,v.cy,d,f);return r=mA(mA(mA({percent:a,cornerRadius:s,name:i,tooltipPayload:b,midAngle:f,middleRadius:d,tooltipPosition:x},e),v),{},{value:pi(e,g),startAngle:n,endAngle:c,payload:e,paddingAngle:rH(m)*u})})),mA(mA({},v),{},{sectors:t,data:a})});var mI=vV({chartName:"PieChart",GraphicalChild:mD,validateTooltipEventTypes:["item"],defaultTooltipEventType:"item",legendContent:"children",axisComponents:[{axisType:"angleAxis",AxisComp:mi},{axisType:"radiusAxis",AxisComp:mj}],formatAxisMap:function(e,t,r,n,o){var i=e.width,a=e.height,s=e.startAngle,l=e.endAngle,c=rJ(e.cx,i,i/2),u=rJ(e.cy,a,a/2),f=de(i,a,r),p=rJ(e.innerRadius,f,0),d=rJ(e.outerRadius,f,.8*f);return Object.keys(t).reduce(function(e,r){var i,a=t[r],f=a.domain,h=a.reversed;if(r8()(a.range))"angleAxis"===n?i=[s,l]:"radiusAxis"===n&&(i=[p,d]),h&&(i=[i[1],i[0]]);else{var y,v=function(e){if(Array.isArray(e))return e}(y=i=a.range)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,s=[],l=!0,c=!1;try{i=(r=r.call(e)).next,!1;for(;!(l=(n=i.call(r)).done)&&(s.push(n.value),s.length!==t);l=!0);}catch(e){c=!0,o=e}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return s}}(y,2)||function(e,t){if(e){if("string"==typeof e)return p6(e,2);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return p6(e,t)}}(y,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();s=v[0],l=v[1]}var m=px(a,o),b=m.realScaleType,g=m.scale;g.domain(f).range(i),pw(g);var x=pA(g,p4(p4({},a),{},{realScaleType:b})),w=p4(p4(p4({},a),x),{},{range:i,radius:d,realScaleType:b,scale:g,cx:c,cy:u,innerRadius:p,outerRadius:d,startAngle:s,endAngle:l});return p4(p4({},e),{},p8({},r,w))},{})},defaultProps:{layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"}});function mR(e,t,r){if(t<1)return[];if(1===t&&void 0===r)return e;for(var n=[],o=0;o<e.length;o+=t)if(void 0!==r&&!0!==r(e[o]))return;else n.push(e[o]);return n}function mL(e,t,r,n,o){if(e*t<e*n||e*t>e*o)return!1;var i=r();return e*(t-e*i/2-n)>=0&&e*(t+e*i/2-o)<=0}function mB(e){return(mB="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function mF(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function mz(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?mF(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=mB(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=mB(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==mB(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):mF(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function m$(e,t,r){var n,o,i,a,s,l=e.tick,c=e.ticks,u=e.viewBox,f=e.minTickGap,p=e.orientation,d=e.interval,h=e.tickFormatter,y=e.unit,v=e.angle;if(!c||!c.length||!l)return[];if(rV(d)||ot.isSsr)return mR(c,("number"==typeof d&&rV(d)?d:0)+1);var m=[],b="top"===p||"bottom"===p?"width":"height",g=y&&"width"===b?aq(y,{fontSize:t,letterSpacing:r}):{width:0,height:0},x=function(e,n){var o,i=r7()(h)?h(e.value,n):e.value;return"width"===b?(o=aq(i,{fontSize:t,letterSpacing:r}),hI({width:o.width+g.width,height:o.height+g.height},v)):aq(i,{fontSize:t,letterSpacing:r})[b]},w=c.length>=2?rH(c[1].coordinate-c[0].coordinate):1,O=(n="width"===b,o=u.x,i=u.y,a=u.width,s=u.height,1===w?{start:n?o:i,end:n?o+a:i+s}:{start:n?o+a:i+s,end:n?o:i});return"equidistantPreserveStart"===d?function(e,t,r,n,o){for(var i,a=(n||[]).slice(),s=t.start,l=t.end,c=0,u=1,f=s;u<=a.length;)if(i=function(){var t,i=null==n?void 0:n[c];if(void 0===i)return{v:mR(n,u)};var a=c,p=function(){return void 0===t&&(t=r(i,a)),t},d=i.coordinate,h=0===c||mL(e,d,p,f,l);h||(c=0,f=s,u+=1),h&&(f=d+e*(p()/2+o),c+=u)}())return i.v;return[]}(w,O,x,c,f):("preserveStart"===d||"preserveStartEnd"===d?function(e,t,r,n,o,i){var a=(n||[]).slice(),s=a.length,l=t.start,c=t.end;if(i){var u=n[s-1],f=r(u,s-1),p=e*(u.coordinate+e*f/2-c);a[s-1]=u=mz(mz({},u),{},{tickCoord:p>0?u.coordinate-p*e:u.coordinate}),mL(e,u.tickCoord,function(){return f},l,c)&&(c=u.tickCoord-e*(f/2+o),a[s-1]=mz(mz({},u),{},{isShow:!0}))}for(var d=i?s-1:s,h=function(t){var n,i=a[t],s=function(){return void 0===n&&(n=r(i,t)),n};if(0===t){var u=e*(i.coordinate-e*s()/2-l);a[t]=i=mz(mz({},i),{},{tickCoord:u<0?i.coordinate-u*e:i.coordinate})}else a[t]=i=mz(mz({},i),{},{tickCoord:i.coordinate});mL(e,i.tickCoord,s,l,c)&&(l=i.tickCoord+e*(s()/2+o),a[t]=mz(mz({},i),{},{isShow:!0}))},y=0;y<d;y++)h(y);return a}(w,O,x,c,f,"preserveStartEnd"===d):function(e,t,r,n,o){for(var i=(n||[]).slice(),a=i.length,s=t.start,l=t.end,c=function(t){var n,c=i[t],u=function(){return void 0===n&&(n=r(c,t)),n};if(t===a-1){var f=e*(c.coordinate+e*u()/2-l);i[t]=c=mz(mz({},c),{},{tickCoord:f>0?c.coordinate-f*e:c.coordinate})}else i[t]=c=mz(mz({},c),{},{tickCoord:c.coordinate});mL(e,c.tickCoord,u,s,l)&&(l=c.tickCoord-e*(u()/2+o),i[t]=mz(mz({},c),{},{isShow:!0}))},u=a-1;u>=0;u--)c(u);return i}(w,O,x,c,f)).filter(function(e){return e.isShow})}var mW=["viewBox"],mU=["viewBox"],mq=["ticks"];function mY(e){return(mY="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function mH(){return(mH=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function mX(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function mV(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?mX(Object(r),!0).forEach(function(t){m0(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):mX(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function mG(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}function mK(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,m1(n.key),n)}}function mZ(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(mZ=function(){return!!e})()}function mJ(e){return(mJ=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function mQ(e,t){return(mQ=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function m0(e,t,r){return(t=m1(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function m1(e){var t=function(e,t){if("object"!=mY(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=mY(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==mY(t)?t:t+""}var m2=function(e){var t,r;function n(e){var t,r,o;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");return r=n,o=[e],r=mJ(r),(t=function(e,t){if(t&&("object"===mY(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");var r=e;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,mZ()?Reflect.construct(r,o||[],mJ(this).constructor):r.apply(this,o))).state={fontSize:"",letterSpacing:""},t}if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(e&&e.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),e&&mQ(n,e),t=[{key:"shouldComponentUpdate",value:function(e,t){var r=e.viewBox,n=mG(e,mW),o=this.props,i=o.viewBox,a=mG(o,mU);return!nr(r,i)||!nr(n,a)||!nr(t,this.state)}},{key:"componentDidMount",value:function(){var e=this.layerReference;if(e){var t=e.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];t&&this.setState({fontSize:window.getComputedStyle(t).fontSize,letterSpacing:window.getComputedStyle(t).letterSpacing})}}},{key:"getTickLineCoord",value:function(e){var t,r,n,o,i,a,s=this.props,l=s.x,c=s.y,u=s.width,f=s.height,p=s.orientation,d=s.tickSize,h=s.mirror,y=s.tickMargin,v=h?-1:1,m=e.tickSize||d,b=rV(e.tickCoord)?e.tickCoord:e.coordinate;switch(p){case"top":t=r=e.coordinate,a=(n=(o=c+!h*f)-v*m)-v*y,i=b;break;case"left":n=o=e.coordinate,i=(t=(r=l+!h*u)-v*m)-v*y,a=b;break;case"right":n=o=e.coordinate,i=(t=(r=l+h*u)+v*m)+v*y,a=b;break;default:t=r=e.coordinate,a=(n=(o=c+h*f)+v*m)+v*y,i=b}return{line:{x1:t,y1:n,x2:r,y2:o},tick:{x:i,y:a}}}},{key:"getTickTextAnchor",value:function(){var e,t=this.props,r=t.orientation,n=t.mirror;switch(r){case"left":e=n?"start":"end";break;case"right":e=n?"end":"start";break;default:e="middle"}return e}},{key:"getTickVerticalAnchor",value:function(){var e=this.props,t=e.orientation,r=e.mirror,n="end";switch(t){case"left":case"right":n="middle";break;case"top":n=r?"start":"end";break;default:n=r?"end":"start"}return n}},{key:"renderAxisLine",value:function(){var e=this.props,t=e.x,r=e.y,n=e.width,o=e.height,i=e.orientation,a=e.mirror,s=e.axisLine,l=mV(mV(mV({},nj(this.props,!1)),nj(s,!1)),{},{fill:"none"});if("top"===i||"bottom"===i){var c=+("top"===i&&!a||"bottom"===i&&a);l=mV(mV({},l),{},{x1:t,y1:r+c*o,x2:t+n,y2:r+c*o})}else{var f=+("left"===i&&!a||"right"===i&&a);l=mV(mV({},l),{},{x1:t+f*n,y1:r,x2:t+f*n,y2:r+o})}return u().createElement("line",mH({},l,{className:(0,rI.A)("recharts-cartesian-axis-line",rU()(s,"className"))}))}},{key:"renderTicks",value:function(e,t,r){var o=this,i=this.props,a=i.tickLine,s=i.stroke,l=i.tick,c=i.tickFormatter,f=i.unit,p=m$(mV(mV({},this.props),{},{ticks:e}),t,r),d=this.getTickTextAnchor(),h=this.getTickVerticalAnchor(),y=nj(this.props,!1),v=nj(l,!1),m=mV(mV({},y),{},{fill:"none"},nj(a,!1)),b=p.map(function(e,t){var r=o.getTickLineCoord(e),i=r.line,b=r.tick,g=mV(mV(mV(mV({textAnchor:d,verticalAnchor:h},y),{},{stroke:"none",fill:s},v),b),{},{index:t,payload:e,visibleTicksCount:p.length,tickFormatter:c});return u().createElement(nU,mH({className:"recharts-cartesian-axis-tick",key:"tick-".concat(e.value,"-").concat(e.coordinate,"-").concat(e.tickCoord)},nc(o.props,e,t)),a&&u().createElement("line",mH({},m,i,{className:(0,rI.A)("recharts-cartesian-axis-tick-line",rU()(a,"className"))})),l&&n.renderTickItem(l,g,"".concat(r7()(c)?c(e.value,t):e.value).concat(f||"")))});return u().createElement("g",{className:"recharts-cartesian-axis-ticks"},b)}},{key:"render",value:function(){var e=this,t=this.props,r=t.axisLine,n=t.width,o=t.height,i=t.ticksGenerator,a=t.className;if(t.hide)return null;var s=this.props,l=s.ticks,c=mG(s,mq),f=l;return(r7()(i)&&(f=i(l&&l.length>0?this.props:c)),n<=0||o<=0||!f||!f.length)?null:u().createElement(nU,{className:(0,rI.A)("recharts-cartesian-axis",a),ref:function(t){e.layerReference=t}},r&&this.renderAxisLine(),this.renderTicks(f,this.state.fontSize,this.state.letterSpacing),dm.renderCallByParent(this.props))}}],r=[{key:"renderTickItem",value:function(e,t,r){var n;return u().isValidElement(e)?u().cloneElement(e,t):r7()(e)?e(t):u().createElement(ss,mH({},t,{className:"recharts-cartesian-axis-tick-value"}),r)}}],t&&mK(n.prototype,t),r&&mK(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(c.Component);function m5(e){return(m5="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}m0(m2,"displayName","CartesianAxis"),m0(m2,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});function m3(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(m3=function(){return!!e})()}function m4(e){return(m4=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function m8(e,t){return(m8=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function m6(e,t,r){return(t=m7(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function m7(e){var t=function(e,t){if("object"!=m5(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=m5(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==m5(t)?t:t+""}function m9(){return(m9=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function be(e){var t=e.xAxisId,r=ye(),n=yt(),o=h6(t);return null==o?null:u().createElement(m2,m9({},o,{className:(0,rI.A)("recharts-".concat(o.axisType," ").concat(o.axisType),o.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(e){return pm(e,!0)}}))}var bt=function(e){var t;function r(){var e,t;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return e=r,t=arguments,e=m4(e),function(e,t){if(t&&("object"===m5(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");var r=e;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,m3()?Reflect.construct(e,t||[],m4(this).constructor):e.apply(this,t))}if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(e&&e.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),e&&m8(r,e),t=[{key:"render",value:function(){return u().createElement(be,this.props)}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,m7(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(u().Component);function br(e){return(br="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}m6(bt,"displayName","XAxis"),m6(bt,"defaultProps",{allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0});function bn(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(bn=function(){return!!e})()}function bo(e){return(bo=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function bi(e,t){return(bi=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function ba(e,t,r){return(t=bs(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function bs(e){var t=function(e,t){if("object"!=br(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=br(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==br(t)?t:t+""}function bl(){return(bl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var bc=function(e){var t=e.yAxisId,r=ye(),n=yt(),o=h9(t);return null==o?null:u().createElement(m2,bl({},o,{className:(0,rI.A)("recharts-".concat(o.axisType," ").concat(o.axisType),o.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(e){return pm(e,!0)}}))},bu=function(e){var t;function r(){var e,t;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return e=r,t=arguments,e=bo(e),function(e,t){if(t&&("object"===br(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");var r=e;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,bn()?Reflect.construct(e,t||[],bo(this).constructor):e.apply(this,t))}if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(e&&e.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),e&&bi(r,e),t=[{key:"render",value:function(){return u().createElement(bc,this.props)}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,bs(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(u().Component);ba(bu,"displayName","YAxis"),ba(bu,"defaultProps",{allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1});var bf=vV({chartName:"BarChart",GraphicalChild:hP,defaultTooltipEventType:"axis",validateTooltipEventTypes:["axis","item"],axisComponents:[{axisType:"xAxis",AxisComp:bt},{axisType:"yAxis",AxisComp:bu}],formatAxisMap:function(e,t,r,n,o){var i=e.width,a=e.height,s=e.layout,l=e.children,c=Object.keys(t),u={left:r.left,leftMirror:r.left,right:i-r.right,rightMirror:i-r.right,top:r.top,topMirror:r.top,bottom:a-r.bottom,bottomMirror:a-r.bottom},f=!!ng(l,hP);return c.reduce(function(i,a){var l,c,p,d,h,y=t[a],v=y.orientation,m=y.domain,b=y.padding,g=void 0===b?{}:b,x=y.mirror,w=y.reversed,O="".concat(v).concat(x?"Mirror":"");if("number"===y.type&&("gap"===y.padding||"no-gap"===y.padding)){var j=m[1]-m[0],S=1/0,P=y.categoricalDomain.sort(r5);if(P.forEach(function(e,t){t>0&&(S=Math.min((e||0)-(P[t-1]||0),S))}),Number.isFinite(S)){var E=S/j,A="vertical"===y.layout?r.height:r.width;if("gap"===y.padding&&(l=E*A/2),"no-gap"===y.padding){var k=rJ(e.barCategoryGap,E*A),M=E*A/2;l=M-k-(M-k)/A*k}}}c="xAxis"===n?[r.left+(g.left||0)+(l||0),r.left+r.width-(g.right||0)-(l||0)]:"yAxis"===n?"horizontal"===s?[r.top+r.height-(g.bottom||0),r.top+(g.top||0)]:[r.top+(g.top||0)+(l||0),r.top+r.height-(g.bottom||0)-(l||0)]:y.range,w&&(c=[c[1],c[0]]);var T=px(y,o,f),_=T.scale,C=T.realScaleType;_.domain(m).range(c),pw(_);var N=pA(_,hM(hM({},y),{},{realScaleType:C}));"xAxis"===n?(h="top"===v&&!x||"bottom"===v&&x,p=r.left,d=u[O]-h*y.height):"yAxis"===n&&(h="left"===v&&!x||"right"===v&&x,p=u[O]-h*y.width,d=r.top);var D=hM(hM(hM({},y),N),{},{realScaleType:C,x:p,y:d,scale:_,width:"xAxis"===n?r.width:y.width,height:"yAxis"===n?r.height:y.height});return D.bandSize=pI(D,N),y.hide||"xAxis"!==n?y.hide||(u[O]+=(h?-1:1)*D.width):u[O]+=(h?-1:1)*D.height,hM(hM({},i),{},hT({},a,D))},{})}}),bp=["x1","y1","x2","y2","key"],bd=["offset"];function bh(e){return(bh="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function by(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function bv(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?by(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=bh(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=bh(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==bh(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):by(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function bm(){return(bm=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function bb(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}var bg=function(e){var t=e.fill;if(!t||"none"===t)return null;var r=e.fillOpacity,n=e.x,o=e.y,i=e.width,a=e.height,s=e.ry;return u().createElement("rect",{x:n,y:o,ry:s,width:i,height:a,stroke:"none",fill:t,fillOpacity:r,className:"recharts-cartesian-grid-bg"})};function bx(e,t){var r;if(u().isValidElement(e))r=u().cloneElement(e,t);else if(r7()(e))r=e(t);else{var n=t.x1,o=t.y1,i=t.x2,a=t.y2,s=t.key,l=nj(bb(t,bp),!1),c=(l.offset,bb(l,bd));r=u().createElement("line",bm({},c,{x1:n,y1:o,x2:i,y2:a,fill:"none",key:s}))}return r}function bw(e){var t=e.x,r=e.width,n=e.horizontal,o=void 0===n||n,i=e.horizontalPoints;if(!o||!i||!i.length)return null;var a=i.map(function(n,i){return bx(o,bv(bv({},e),{},{x1:t,y1:n,x2:t+r,y2:n,key:"line-".concat(i),index:i}))});return u().createElement("g",{className:"recharts-cartesian-grid-horizontal"},a)}function bO(e){var t=e.y,r=e.height,n=e.vertical,o=void 0===n||n,i=e.verticalPoints;if(!o||!i||!i.length)return null;var a=i.map(function(n,i){return bx(o,bv(bv({},e),{},{x1:n,y1:t,x2:n,y2:t+r,key:"line-".concat(i),index:i}))});return u().createElement("g",{className:"recharts-cartesian-grid-vertical"},a)}function bj(e){var t=e.horizontalFill,r=e.fillOpacity,n=e.x,o=e.y,i=e.width,a=e.height,s=e.horizontalPoints,l=e.horizontal;if(!(void 0===l||l)||!t||!t.length)return null;var c=s.map(function(e){return Math.round(e+o-o)}).sort(function(e,t){return e-t});o!==c[0]&&c.unshift(0);var f=c.map(function(e,s){var l=c[s+1]?c[s+1]-e:o+a-e;if(l<=0)return null;var f=s%t.length;return u().createElement("rect",{key:"react-".concat(s),y:e,x:n,height:l,width:i,stroke:"none",fill:t[f],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return u().createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},f)}function bS(e){var t=e.vertical,r=e.verticalFill,n=e.fillOpacity,o=e.x,i=e.y,a=e.width,s=e.height,l=e.verticalPoints;if(!(void 0===t||t)||!r||!r.length)return null;var c=l.map(function(e){return Math.round(e+o-o)}).sort(function(e,t){return e-t});o!==c[0]&&c.unshift(0);var f=c.map(function(e,t){var l=c[t+1]?c[t+1]-e:o+a-e;if(l<=0)return null;var f=t%r.length;return u().createElement("rect",{key:"react-".concat(t),x:e,y:i,width:l,height:s,stroke:"none",fill:r[f],fillOpacity:n,className:"recharts-cartesian-grid-bg"})});return u().createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},f)}var bP=function(e,t){var r=e.xAxis,n=e.width,o=e.height,i=e.offset;return pv(m$(bv(bv(bv({},m2.defaultProps),r),{},{ticks:pm(r,!0),viewBox:{x:0,y:0,width:n,height:o}})),i.left,i.left+i.width,t)},bE=function(e,t){var r=e.yAxis,n=e.width,o=e.height,i=e.offset;return pv(m$(bv(bv(bv({},m2.defaultProps),r),{},{ticks:pm(r,!0),viewBox:{x:0,y:0,width:n,height:o}})),i.top,i.top+i.height,t)},bA={horizontal:!0,vertical:!0,stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[]};function bk(e){var t,r,n,o,i,a,s=ye(),l=yt(),f=(0,c.useContext)(h2),p=bv(bv({},e),{},{stroke:null!=(t=e.stroke)?t:bA.stroke,fill:null!=(r=e.fill)?r:bA.fill,horizontal:null!=(n=e.horizontal)?n:bA.horizontal,horizontalFill:null!=(o=e.horizontalFill)?o:bA.horizontalFill,vertical:null!=(i=e.vertical)?i:bA.vertical,verticalFill:null!=(a=e.verticalFill)?a:bA.verticalFill,x:rV(e.x)?e.x:f.left,y:rV(e.y)?e.y:f.top,width:rV(e.width)?e.width:f.width,height:rV(e.height)?e.height:f.height}),d=p.x,h=p.y,y=p.width,v=p.height,m=p.syncWithTicks,b=p.horizontalValues,g=p.verticalValues,x=rQ((0,c.useContext)(hQ)),w=h7();if(!rV(y)||y<=0||!rV(v)||v<=0||!rV(d)||d!==+d||!rV(h)||h!==+h)return null;var O=p.verticalCoordinatesGenerator||bP,j=p.horizontalCoordinatesGenerator||bE,S=p.horizontalPoints,P=p.verticalPoints;if((!S||!S.length)&&r7()(j)){var E=b&&b.length,A=j({yAxis:w?bv(bv({},w),{},{ticks:E?b:w.ticks}):void 0,width:s,height:l,offset:f},!!E||m);r3(Array.isArray(A),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(bh(A),"]")),Array.isArray(A)&&(S=A)}if((!P||!P.length)&&r7()(O)){var k=g&&g.length,M=O({xAxis:x?bv(bv({},x),{},{ticks:k?g:x.ticks}):void 0,width:s,height:l,offset:f},!!k||m);r3(Array.isArray(M),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(bh(M),"]")),Array.isArray(M)&&(P=M)}return u().createElement("g",{className:"recharts-cartesian-grid"},u().createElement(bg,{fill:p.fill,fillOpacity:p.fillOpacity,x:p.x,y:p.y,width:p.width,height:p.height,ry:p.ry}),u().createElement(bw,bm({},p,{offset:f,horizontalPoints:S,xAxis:x,yAxis:w})),u().createElement(bO,bm({},p,{offset:f,verticalPoints:P,xAxis:x,yAxis:w})),u().createElement(bj,bm({},p,{horizontalPoints:S})),u().createElement(bS,bm({},p,{verticalPoints:P})))}bk.displayName="CartesianGrid";var bM=Symbol("radix.slottable");function bT(e){return c.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===bM}var b_=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=function(e){let t=function(e){let t=c.forwardRef((e,t)=>{var r;let n,o,{children:i,...a}=e,s=c.isValidElement(i)?(r=i,(o=(n=Object.getOwnPropertyDescriptor(r.props,"ref")?.get)&&"isReactWarning"in n&&n.isReactWarning)?r.ref:(o=(n=Object.getOwnPropertyDescriptor(r,"ref")?.get)&&"isReactWarning"in n&&n.isReactWarning)?r.props.ref:r.props.ref||r.ref):void 0,l=(0,tg.s)(s,t);if(c.isValidElement(i)){let e=function(e,t){let r={...t};for(let n in t){let o=e[n],i=t[n];/^on[A-Z]/.test(n)?o&&i?r[n]=(...e)=>{let t=i(...e);return o(...e),t}:o&&(r[n]=o):"style"===n?r[n]={...o,...i}:"className"===n&&(r[n]=[o,i].filter(Boolean).join(" "))}return{...e,...r}}(a,i.props);return i.type!==c.Fragment&&(e.ref=l),c.cloneElement(i,e)}return c.Children.count(i)>1?c.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=c.forwardRef((e,r)=>{let{children:n,...o}=e,i=c.Children.toArray(n),a=i.find(bT);if(a){let e=a.props.children,n=i.map(t=>t!==a?t:c.Children.count(e)>1?c.Children.only(null):c.isValidElement(e)?e.props.children:null);return(0,l.jsx)(t,{...o,ref:r,children:c.isValidElement(e)?c.cloneElement(e,void 0,n):null})}return(0,l.jsx)(t,{...o,ref:r,children:n})});return r.displayName=`${e}.Slot`,r}(`Primitive.${t}`),n=c.forwardRef((e,n)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(o?r:t,{...i,ref:n})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{}),bC="dismissableLayer.update",bN=c.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),bD=c.forwardRef((e,t)=>{let{disableOutsidePointerEvents:r=!1,onEscapeKeyDown:o,onPointerDownOutside:i,onFocusOutside:a,onInteractOutside:s,onDismiss:u,...f}=e,p=c.useContext(bN),[d,h]=c.useState(null),y=d?.ownerDocument??globalThis?.document,[,v]=c.useState({}),m=(0,tg.s)(t,e=>h(e)),b=Array.from(p.layers),[g]=[...p.layersWithOutsidePointerEventsDisabled].slice(-1),x=b.indexOf(g),w=d?b.indexOf(d):-1,O=p.layersWithOutsidePointerEventsDisabled.size>0,j=w>=x,S=function(e,t=globalThis?.document){let r=(0,tE.c)(e),n=c.useRef(!1),o=c.useRef(()=>{});return c.useEffect(()=>{let e=e=>{if(e.target&&!n.current){let n=function(){bR("dismissableLayer.pointerDownOutside",r,i,{discrete:!0})},i={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",o.current),o.current=n,t.addEventListener("click",o.current,{once:!0})):n()}else t.removeEventListener("click",o.current);n.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",e),t.removeEventListener("click",o.current)}},[t,r]),{onPointerDownCapture:()=>n.current=!0}}(e=>{let t=e.target,r=[...p.branches].some(e=>e.contains(t));j&&!r&&(i?.(e),s?.(e),e.defaultPrevented||u?.())},y),P=function(e,t=globalThis?.document){let r=(0,tE.c)(e),n=c.useRef(!1);return c.useEffect(()=>{let e=e=>{e.target&&!n.current&&bR("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,r]),{onFocusCapture:()=>n.current=!0,onBlurCapture:()=>n.current=!1}}(e=>{let t=e.target;![...p.branches].some(e=>e.contains(t))&&(a?.(e),s?.(e),e.defaultPrevented||u?.())},y);return(0,tA.U)(e=>{w===p.layers.size-1&&(o?.(e),!e.defaultPrevented&&u&&(e.preventDefault(),u()))},y),c.useEffect(()=>{if(d)return r&&(0===p.layersWithOutsidePointerEventsDisabled.size&&(n=y.body.style.pointerEvents,y.body.style.pointerEvents="none"),p.layersWithOutsidePointerEventsDisabled.add(d)),p.layers.add(d),bI(),()=>{r&&1===p.layersWithOutsidePointerEventsDisabled.size&&(y.body.style.pointerEvents=n)}},[d,y,r,p]),c.useEffect(()=>()=>{d&&(p.layers.delete(d),p.layersWithOutsidePointerEventsDisabled.delete(d),bI())},[d,p]),c.useEffect(()=>{let e=()=>v({});return document.addEventListener(bC,e),()=>document.removeEventListener(bC,e)},[]),(0,l.jsx)(b_.div,{...f,ref:m,style:{pointerEvents:O?j?"auto":"none":void 0,...e.style},onFocusCapture:(0,tb.m)(e.onFocusCapture,P.onFocusCapture),onBlurCapture:(0,tb.m)(e.onBlurCapture,P.onBlurCapture),onPointerDownCapture:(0,tb.m)(e.onPointerDownCapture,S.onPointerDownCapture)})});function bI(){let e=new CustomEvent(bC);document.dispatchEvent(e)}function bR(e,t,r,{discrete:n}){let o=r.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});if(t&&o.addEventListener(e,t,{once:!0}),n)o&&tw.flushSync(()=>o.dispatchEvent(i));else o.dispatchEvent(i)}bD.displayName="DismissableLayer",c.forwardRef((e,t)=>{let r=c.useContext(bN),n=c.useRef(null),o=(0,tg.s)(t,n);return c.useEffect(()=>{let e=n.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,l.jsx)(b_.div,{...e,ref:o})}).displayName="DismissableLayerBranch";var bL=c.forwardRef((e,t)=>{let{children:r,width:n=10,height:o=5,...i}=e;return(0,l.jsx)(b_.svg,{...i,ref:t,width:n,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?r:(0,l.jsx)("polygon",{points:"0,0 30,0 15,10"})})});bL.displayName="Arrow";var bB="Popper",[bF,bz]=(0,tx.A)(bB),[b$,bW]=bF(bB),bU=e=>{let{__scopePopper:t,children:r}=e,[n,o]=c.useState(null);return(0,l.jsx)(b$,{scope:t,anchor:n,onAnchorChange:o,children:r})};bU.displayName=bB;var bq="PopperAnchor",bY=c.forwardRef((e,t)=>{let{__scopePopper:r,virtualRef:n,...o}=e,i=bW(bq,r),a=c.useRef(null),s=(0,tg.s)(t,a);return c.useEffect(()=>{i.onAnchorChange(n?.current||a.current)}),n?null:(0,l.jsx)(b_.div,{...o,ref:s})});bY.displayName=bq;var bH="PopperContent",[bX,bV]=bF(bH),bG=c.forwardRef((e,t)=>{let{__scopePopper:r,side:n="bottom",sideOffset:o=0,align:i="center",alignOffset:a=0,arrowPadding:s=0,avoidCollisions:u=!0,collisionBoundary:f=[],collisionPadding:p=0,sticky:d="partial",hideWhenDetached:h=!1,updatePositionStrategy:y="optimized",onPlaced:v,...m}=e,b=bW(bH,r),[g,x]=c.useState(null),w=(0,tg.s)(t,e=>x(e)),[O,j]=c.useState(null),S=(0,tV.X)(O),P=S?.width??0,E=S?.height??0,A="number"==typeof p?p:{top:0,right:0,bottom:0,left:0,...p},k=Array.isArray(f)?f:[f],M=k.length>0,T={padding:A,boundary:k.filter(bQ),altBoundary:M},{refs:_,floatingStyles:C,placement:N,isPositioned:D,middlewareData:I}=(0,tq.we)({strategy:"fixed",placement:n+("center"!==i?"-"+i:""),whileElementsMounted:(...e)=>(0,tY.ll)(...e,{animationFrame:"always"===y}),elements:{reference:b.anchor},middleware:[(0,tq.cY)({mainAxis:o+E,alignmentAxis:a}),u&&(0,tq.BN)({mainAxis:!0,crossAxis:!1,limiter:"partial"===d?(0,tq.ER)():void 0,...T}),u&&(0,tq.UU)({...T}),(0,tq.Ej)({...T,apply:({elements:e,rects:t,availableWidth:r,availableHeight:n})=>{let{width:o,height:i}=t.reference,a=e.floating.style;a.setProperty("--radix-popper-available-width",`${r}px`),a.setProperty("--radix-popper-available-height",`${n}px`),a.setProperty("--radix-popper-anchor-width",`${o}px`),a.setProperty("--radix-popper-anchor-height",`${i}px`)}}),O&&(0,tq.UE)({element:O,padding:s}),b0({arrowWidth:P,arrowHeight:E}),h&&(0,tq.jD)({strategy:"referenceHidden",...T})]}),[R,L]=b1(N),B=(0,tE.c)(v);(0,tX.N)(()=>{D&&B?.()},[D,B]);let F=I.arrow?.x,z=I.arrow?.y,$=I.arrow?.centerOffset!==0,[W,U]=c.useState();return(0,tX.N)(()=>{g&&U(window.getComputedStyle(g).zIndex)},[g]),(0,l.jsx)("div",{ref:_.setFloating,"data-radix-popper-content-wrapper":"",style:{...C,transform:D?C.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:W,"--radix-popper-transform-origin":[I.transformOrigin?.x,I.transformOrigin?.y].join(" "),...I.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,l.jsx)(bX,{scope:r,placedSide:R,onArrowChange:j,arrowX:F,arrowY:z,shouldHideArrow:$,children:(0,l.jsx)(b_.div,{"data-side":R,"data-align":L,...m,ref:w,style:{...m.style,animation:D?void 0:"none"}})})})});bG.displayName=bH;var bK="PopperArrow",bZ={top:"bottom",right:"left",bottom:"top",left:"right"},bJ=c.forwardRef(function(e,t){let{__scopePopper:r,...n}=e,o=bV(bK,r),i=bZ[o.placedSide];return(0,l.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,l.jsx)(bL,{...n,ref:t,style:{...n.style,display:"block"}})})});function bQ(e){return null!==e}bJ.displayName=bK;var b0=e=>({name:"transformOrigin",options:e,fn(t){let{placement:r,rects:n,middlewareData:o}=t,i=o.arrow?.centerOffset!==0,a=i?0:e.arrowWidth,s=i?0:e.arrowHeight,[l,c]=b1(r),u={start:"0%",center:"50%",end:"100%"}[c],f=(o.arrow?.x??0)+a/2,p=(o.arrow?.y??0)+s/2,d="",h="";return"bottom"===l?(d=i?u:`${f}px`,h=`${-s}px`):"top"===l?(d=i?u:`${f}px`,h=`${n.floating.height+s}px`):"right"===l?(d=`${-s}px`,h=i?u:`${p}px`):"left"===l&&(d=`${n.floating.width+s}px`,h=i?u:`${p}px`),{data:{x:d,y:h}}}});function b1(e){let[t,r="center"]=e.split("-");return[t,r]}var b2=c.forwardRef((e,t)=>{let{container:r,...n}=e,[o,i]=c.useState(!1);(0,tX.N)(()=>i(!0),[]);let a=r||o&&globalThis?.document?.body;return a?tw.createPortal((0,l.jsx)(b_.div,{...n,ref:t}),a):null});b2.displayName="Portal";var b5=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),b3=c.forwardRef((e,t)=>(0,l.jsx)(b_.span,{...e,ref:t,style:{...b5,...e.style}}));b3.displayName="VisuallyHidden";var[b4,b8]=(0,tx.A)("Tooltip",[bz]),b6=bz(),b7="TooltipProvider",b9="tooltip.open",[ge,gt]=b4(b7),gr=e=>{let{__scopeTooltip:t,delayDuration:r=700,skipDelayDuration:n=300,disableHoverableContent:o=!1,children:i}=e,a=c.useRef(!0),s=c.useRef(!1),u=c.useRef(0);return c.useEffect(()=>{let e=u.current;return()=>window.clearTimeout(e)},[]),(0,l.jsx)(ge,{scope:t,isOpenDelayedRef:a,delayDuration:r,onOpen:c.useCallback(()=>{window.clearTimeout(u.current),a.current=!1},[]),onClose:c.useCallback(()=>{window.clearTimeout(u.current),u.current=window.setTimeout(()=>a.current=!0,n)},[n]),isPointerInTransitRef:s,onPointerInTransitChange:c.useCallback(e=>{s.current=e},[]),disableHoverableContent:o,children:i})};gr.displayName=b7;var gn="Tooltip",[go,gi]=b4(gn),ga=e=>{let{__scopeTooltip:t,children:r,open:n,defaultOpen:o,onOpenChange:i,disableHoverableContent:a,delayDuration:s}=e,u=gt(gn,e.__scopeTooltip),f=b6(t),[p,d]=c.useState(null),h=(0,tU.B)(),y=c.useRef(0),v=a??u.disableHoverableContent,m=s??u.delayDuration,b=c.useRef(!1),[g,x]=(0,ri.i)({prop:n,defaultProp:o??!1,onChange:e=>{e?(u.onOpen(),document.dispatchEvent(new CustomEvent(b9))):u.onClose(),i?.(e)},caller:gn}),w=c.useMemo(()=>g?b.current?"delayed-open":"instant-open":"closed",[g]),O=c.useCallback(()=>{window.clearTimeout(y.current),y.current=0,b.current=!1,x(!0)},[x]),j=c.useCallback(()=>{window.clearTimeout(y.current),y.current=0,x(!1)},[x]),S=c.useCallback(()=>{window.clearTimeout(y.current),y.current=window.setTimeout(()=>{b.current=!0,x(!0),y.current=0},m)},[m,x]);return c.useEffect(()=>()=>{y.current&&(window.clearTimeout(y.current),y.current=0)},[]),(0,l.jsx)(bU,{...f,children:(0,l.jsx)(go,{scope:t,contentId:h,open:g,stateAttribute:w,trigger:p,onTriggerChange:d,onTriggerEnter:c.useCallback(()=>{u.isOpenDelayedRef.current?S():O()},[u.isOpenDelayedRef,S,O]),onTriggerLeave:c.useCallback(()=>{v?j():(window.clearTimeout(y.current),y.current=0)},[j,v]),onOpen:O,onClose:j,disableHoverableContent:v,children:r})})};ga.displayName=gn;var gs="TooltipTrigger",gl=c.forwardRef((e,t)=>{let{__scopeTooltip:r,...n}=e,o=gi(gs,r),i=gt(gs,r),a=b6(r),s=c.useRef(null),u=(0,tg.s)(t,s,o.onTriggerChange),f=c.useRef(!1),p=c.useRef(!1),d=c.useCallback(()=>f.current=!1,[]);return c.useEffect(()=>()=>document.removeEventListener("pointerup",d),[d]),(0,l.jsx)(bY,{asChild:!0,...a,children:(0,l.jsx)(b_.button,{"aria-describedby":o.open?o.contentId:void 0,"data-state":o.stateAttribute,...n,ref:u,onPointerMove:(0,tb.m)(e.onPointerMove,e=>{"touch"!==e.pointerType&&(p.current||i.isPointerInTransitRef.current||(o.onTriggerEnter(),p.current=!0))}),onPointerLeave:(0,tb.m)(e.onPointerLeave,()=>{o.onTriggerLeave(),p.current=!1}),onPointerDown:(0,tb.m)(e.onPointerDown,()=>{o.open&&o.onClose(),f.current=!0,document.addEventListener("pointerup",d,{once:!0})}),onFocus:(0,tb.m)(e.onFocus,()=>{f.current||o.onOpen()}),onBlur:(0,tb.m)(e.onBlur,o.onClose),onClick:(0,tb.m)(e.onClick,o.onClose)})})});gl.displayName=gs;var gc="TooltipPortal",[gu,gf]=b4(gc,{forceMount:void 0}),gp=e=>{let{__scopeTooltip:t,forceMount:r,children:n,container:o}=e,i=gi(gc,t);return(0,l.jsx)(gu,{scope:t,forceMount:r,children:(0,l.jsx)(ro.C,{present:r||i.open,children:(0,l.jsx)(b2,{asChild:!0,container:o,children:n})})})};gp.displayName=gc;var gd="TooltipContent",gh=c.forwardRef((e,t)=>{let r=gf(gd,e.__scopeTooltip),{forceMount:n=r.forceMount,side:o="top",...i}=e,a=gi(gd,e.__scopeTooltip);return(0,l.jsx)(ro.C,{present:n||a.open,children:a.disableHoverableContent?(0,l.jsx)(gg,{side:o,...i,ref:t}):(0,l.jsx)(gy,{side:o,...i,ref:t})})}),gy=c.forwardRef((e,t)=>{let r=gi(gd,e.__scopeTooltip),n=gt(gd,e.__scopeTooltip),o=c.useRef(null),i=(0,tg.s)(t,o),[a,s]=c.useState(null),{trigger:u,onClose:f}=r,p=o.current,{onPointerInTransitChange:d}=n,h=c.useCallback(()=>{s(null),d(!1)},[d]),y=c.useCallback((e,t)=>{let r=e.currentTarget,n={x:e.clientX,y:e.clientY},o=function(e,t){let r=Math.abs(t.top-e.y),n=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),i=Math.abs(t.left-e.x);switch(Math.min(r,n,o,i)){case i:return"left";case o:return"right";case r:return"top";case n:return"bottom";default:throw Error("unreachable")}}(n,r.getBoundingClientRect());s(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:1*!!(e.y>t.y)),function(e){if(e.length<=1)return e.slice();let t=[];for(let r=0;r<e.length;r++){let n=e[r];for(;t.length>=2;){let e=t[t.length-1],r=t[t.length-2];if((e.x-r.x)*(n.y-r.y)>=(e.y-r.y)*(n.x-r.x))t.pop();else break}t.push(n)}t.pop();let r=[];for(let t=e.length-1;t>=0;t--){let n=e[t];for(;r.length>=2;){let e=r[r.length-1],t=r[r.length-2];if((e.x-t.x)*(n.y-t.y)>=(e.y-t.y)*(n.x-t.x))r.pop();else break}r.push(n)}return(r.pop(),1===t.length&&1===r.length&&t[0].x===r[0].x&&t[0].y===r[0].y)?t:t.concat(r)}(t)}([...function(e,t,r=5){let n=[];switch(t){case"top":n.push({x:e.x-r,y:e.y+r},{x:e.x+r,y:e.y+r});break;case"bottom":n.push({x:e.x-r,y:e.y-r},{x:e.x+r,y:e.y-r});break;case"left":n.push({x:e.x+r,y:e.y-r},{x:e.x+r,y:e.y+r});break;case"right":n.push({x:e.x-r,y:e.y-r},{x:e.x-r,y:e.y+r})}return n}(n,o),...function(e){let{top:t,right:r,bottom:n,left:o}=e;return[{x:o,y:t},{x:r,y:t},{x:r,y:n},{x:o,y:n}]}(t.getBoundingClientRect())])),d(!0)},[d]);return c.useEffect(()=>()=>h(),[h]),c.useEffect(()=>{if(u&&p){let e=e=>y(e,p),t=e=>y(e,u);return u.addEventListener("pointerleave",e),p.addEventListener("pointerleave",t),()=>{u.removeEventListener("pointerleave",e),p.removeEventListener("pointerleave",t)}}},[u,p,y,h]),c.useEffect(()=>{if(a){let e=e=>{let t=e.target,r={x:e.clientX,y:e.clientY},n=u?.contains(t)||p?.contains(t),o=!function(e,t){let{x:r,y:n}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let a=t[e],s=t[i],l=a.x,c=a.y,u=s.x,f=s.y;c>n!=f>n&&r<(u-l)*(n-c)/(f-c)+l&&(o=!o)}return o}(r,a);n?h():o&&(h(),f())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[u,p,a,f,h]),(0,l.jsx)(gg,{...e,ref:i})}),[gv,gm]=b4(gn,{isInside:!1}),gb=function(e){let t=({children:e})=>(0,l.jsx)(l.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=bM,t}("TooltipContent"),gg=c.forwardRef((e,t)=>{let{__scopeTooltip:r,children:n,"aria-label":o,onEscapeKeyDown:i,onPointerDownOutside:a,...s}=e,u=gi(gd,r),f=b6(r),{onClose:p}=u;return c.useEffect(()=>(document.addEventListener(b9,p),()=>document.removeEventListener(b9,p)),[p]),c.useEffect(()=>{if(u.trigger){let e=e=>{let t=e.target;t?.contains(u.trigger)&&p()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[u.trigger,p]),(0,l.jsx)(bD,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:i,onPointerDownOutside:a,onFocusOutside:e=>e.preventDefault(),onDismiss:p,children:(0,l.jsxs)(bG,{"data-state":u.stateAttribute,...f,...s,ref:t,style:{...s.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,l.jsx)(gb,{children:n}),(0,l.jsx)(gv,{scope:r,isInside:!0,children:(0,l.jsx)(b3,{id:u.contentId,role:"tooltip",children:o||n})})]})})});gh.displayName=gd;var gx="TooltipArrow",gw=c.forwardRef((e,t)=>{let{__scopeTooltip:r,...n}=e,o=b6(r);return gm(gx,r).isInside?null:(0,l.jsx)(bJ,{...o,...n,ref:t})});gw.displayName=gx;let gO=({children:e,content:t,side:r="top",align:n="center",...o})=>(0,l.jsx)(gr,{children:(0,l.jsxs)(ga,{delayDuration:0,children:[(0,l.jsx)(gl,{asChild:!0,children:e}),(0,l.jsx)(gp,{children:(0,l.jsxs)(gh,{side:r,align:n,className:(0,k.cn)("z-50 rounded bg-black px-2 py-1 text-xs text-neutral-100 shadow-md animate-fade-in-up","data-[state=delayed-open]:data-[side=top]:animate-slide-in-from-bottom-2"),children:[t,(0,l.jsx)(gw,{className:"fill-black"})]})})]})}),gj=["#0088FE","#00C49F","#FFBB28","#FF8042","#8884D8","#82CA9D","#FFC658","#FF6B6B"],gS=[{value:"verticalBar",label:"Vertical Bar",icon:p},{value:"pie",label:"Pie",icon:d},{value:"donut",label:"Donut",icon:d}],gP=({data:e,donut:t=!1})=>{let r=(0,r_.c3)(),n=e.labels.map((t,r)=>({name:t,value:e.values[r]}));return(0,l.jsx)("div",{className:"h-[400px] w-full",children:(0,l.jsx)(nC,{children:(0,l.jsxs)(mI,{children:[(0,l.jsx)(mD,{data:n,dataKey:"value",nameKey:"name",cx:"50%",cy:"50%",outerRadius:150,innerRadius:80*!!t,fill:"#8884d8",label:({name:e,percent:t})=>`${e} (${(100*t).toFixed(0)}%)`,children:n.map((e,t)=>(0,l.jsx)(dS,{fill:gj[t%gj.length]},`cell-${t}`))}),(0,l.jsx)(oh,{formatter:e=>[`${e} responses`,`${r("count")}`]}),(0,l.jsx)(ii,{})]})})})},gE=({data:e,layout:t="verticalBar",questionType:r})=>{let n=e.labels.map((t,r)=>({name:t,value:e.values[r]})),o="table"===r?"horizontalBar":t,i="table"===r&&n.length>10?n.slice(0,10):n;return(0,l.jsx)("div",{className:"h-[400px] w-full",children:(0,l.jsx)(nC,{children:(0,l.jsxs)(bf,{data:i,layout:"horizontalBar"===o?"vertical":"horizontal",margin:{top:20,right:30,left:20,bottom:5},children:[(0,l.jsx)(bk,{strokeDasharray:"3 3"}),(0,l.jsx)(bt,{dataKey:"name",type:"horizontalBar"===o?"number":"category",tick:{fontSize:12}}),(0,l.jsx)(bu,{type:"horizontalBar"===o?"category":"number",tick:{fontSize:12},width:"table"===r?150:60}),(0,l.jsx)(oh,{formatter:e=>[`${e} responses`,"Count"]}),(0,l.jsx)(ii,{}),(0,l.jsx)(hP,{dataKey:"value",fill:"#8884d8",children:i.map((e,t)=>(0,l.jsx)(dS,{fill:gj[t%gj.length]},`cell-${t}`))})]})})})},gA=({report:e})=>{let{question:t,type:r,answered:n,total:o,table:i,chartData:a,stats:s}=e,u=o>0?Math.round(n/o*100):0,[f,p]=(0,c.useState)("table"===r?"verticalBar":"pie"),d=(0,r_.c3)(),h="selectone"===r||"selectmany"===r,x=()=>{if("table"===r&&!Array.isArray(i)&&i.structure){let{structure:e,data:r,cellValues:n,metadata:o}=i,a=e.columns.filter(e=>null===e.parentId),s=new Map;return n&&("object"!=typeof n||Array.isArray(n)?n instanceof Map&&(s=n):Object.entries(n).forEach(([e,t])=>{s.set(e,t)})),r&&r.length>0&&r.forEach(e=>{if(e.rowId&&e.columnId){let t=`${e.rowId}_${e.columnId}`;e.cellValue&&s.set(t,e.cellValue)}}),0===s.size&&e.rows.forEach(t=>{e.columns.forEach(e=>{if(null===e.parentId){let r=`${t.id}_${e.id}`;s.set(r,`Test value for ${t.name} - ${e.name}`)}})}),(0,l.jsxs)("div",{className:"overflow-auto",children:[(0,l.jsxs)("div",{className:"mb-2 p-2 bg-blue-50 rounded-md",children:[(0,l.jsxs)("p",{className:"text-sm text-blue-700",children:[(0,l.jsxs)("span",{className:"font-medium",children:[d("matrixQuestion"),":"]})," ",d("matrixInfo")]}),o&&(0,l.jsxs)("div",{className:"mt-1 text-xs text-blue-600 flex items-center justify-between",children:[(0,l.jsx)("span",{children:o.hasRealData?`Showing data from ${o.submissionsWithData} of ${o.totalSubmissions} submissions`:d("noSubmissionsForTable")}),(0,l.jsxs)("span",{children:[d("lastUpdated"),":",new Date(o.lastUpdated).toLocaleString()]})]})]}),(0,l.jsxs)("table",{className:"w-full my-4 text-sm border-collapse shadow-sm",children:[(0,l.jsx)("thead",{children:(0,l.jsxs)("tr",{className:"border-b bg-gray-100",children:[(0,l.jsx)("th",{className:"pb-3 pt-3 text-left border p-2 bg-gray-50 font-semibold text-gray-700",children:t}),a.map(e=>(0,l.jsx)("th",{className:"pb-3 pt-3 text-center border p-2 bg-gray-50 font-semibold text-gray-700",children:e.name},e.id))]})}),(0,l.jsx)("tbody",{children:e.rows.map((e,t)=>(0,l.jsxs)("tr",{className:`border-b last:border-0 ${t%2==0?"bg-white":"bg-gray-50"} hover:bg-blue-50 transition-colors duration-150`,children:[(0,l.jsx)("td",{className:"py-3 border p-3 font-medium text-gray-700",children:e.name}),a.map(t=>{if(t.children&&t.children.length>0)return(0,l.jsx)("td",{className:"py-3 border p-3 text-center",children:t.children.map(t=>{let r=`${e.id}_${t.id}`,o=n instanceof Map?n.get(r):"object"!=typeof n||Array.isArray(n)?s.get(r):n[r];return(0,l.jsxs)("div",{className:"mb-2 p-1 rounded hover:bg-blue-100",children:[(0,l.jsx)("span",{className:"font-medium text-xs text-gray-600 block mb-1",children:t.name}),(0,l.jsx)("span",{className:"text-gray-800",children:void 0!==o&&""!==o?o.includes(",")||o.includes("(")?(0,l.jsx)("span",{className:"text-xs",children:o.split(", ").map((e,t)=>(0,l.jsx)("span",{className:`inline-block mr-1 mb-1 px-1 py-0.5 rounded ${e.includes("(")?"bg-blue-100 text-blue-800":"bg-gray-100 text-gray-700"}`,children:e},t))}):o:"-"})]},t.id)})},t.id);{let r=`${e.id}_${t.id}`,o=n instanceof Map?n.get(r):"object"!=typeof n||Array.isArray(n)?s.get(r):n[r];return(0,l.jsx)("td",{className:`py-3 border p-3 text-center ${o&&"-"!==o?"text-gray-800 font-medium":"text-gray-400"}`,children:void 0!==o&&""!==o?o.includes(",")||o.includes("(")?(0,l.jsx)("span",{className:"text-xs",children:o.split(", ").map((e,t)=>(0,l.jsx)("span",{className:`inline-block mr-1 mb-1 px-1 py-0.5 rounded ${e.includes("(")?"bg-blue-100 text-blue-800":"bg-gray-100 text-gray-700"}`,children:e},t))}):o:"-"},t.id)}})]},e.id))})]})]})}return(0,l.jsx)("div",{className:"overflow-auto",children:(0,l.jsxs)("table",{className:"w-full my-4 text-sm",children:[(0,l.jsx)("thead",{children:(0,l.jsxs)("tr",{className:"border-b",children:[(0,l.jsx)("th",{className:"pb-2 text-left",children:d("value")}),(0,l.jsx)("th",{className:"pb-2 text-right",children:d("count")}),(0,l.jsx)("th",{className:"pb-2 text-right",children:d("percentage")})]})}),(0,l.jsx)("tbody",{children:Array.isArray(i)&&i.map((e,t)=>(0,l.jsxs)("tr",{className:"border-b last:border-0",children:[(0,l.jsx)("td",{className:"py-2",children:e.value||d("noAnswer")}),(0,l.jsx)("td",{className:"py-2 text-right",children:e.frequency}),(0,l.jsxs)("td",{className:"py-2 text-right",children:[e.percentage,"%"]})]},t))})]})})};return(0,l.jsxs)(v,{className:"mb-6",children:[(0,l.jsx)(m,{children:(0,l.jsxs)("div",{className:"flex flex-col space-y-2 md:space-y-0 md:flex-row md:justify-between md:items-center",children:[(0,l.jsx)(b,{className:"text-base",children:t}),(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsxs)(y,{variant:u>75?"default":u>50?"secondary":"outline",children:[n," of ",o," responses (",u,"%)"]}),(0,l.jsx)(y,{variant:"outline",children:r})]})]})}),(0,l.jsx)(g,{children:h?(0,l.jsxs)(O,{defaultValue:"table",children:[(0,l.jsxs)(j,{className:"grid w-full grid-cols-2",children:[(0,l.jsx)(S,{value:"table",children:d("table")}),(0,l.jsx)(S,{value:"chart",children:d("chart")})]}),(0,l.jsx)(P,{value:"table",children:x()}),(0,l.jsxs)(P,{value:"chart",children:[(0,l.jsx)("div",{className:"flex items-center space-x-2 mb-4",children:gS.map(e=>{let t=e.icon,r="verticalBar"===e.value?{transform:"rotate(90deg)"}:{};return(0,l.jsx)(gO,{content:e.label,side:"top",children:(0,l.jsx)("button",{onClick:()=>p(e.value),className:`p-2 rounded-full border transition-colors duration-150 ${f===e.value?"bg-primary text-neutral-100 border-primary":"bg-muted text-muted-foreground border-transparent"}`,style:r,children:(0,l.jsx)(t,{size:20})})},e.value)})}),(()=>{switch(f){case"verticalBar":return(0,l.jsx)(gE,{data:a,layout:"verticalBar",questionType:r});case"pie":default:return(0,l.jsx)(gP,{data:a});case"donut":return(0,l.jsx)(gP,{data:a,donut:!0})}})()]})]}):(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"text-sm font-medium mb-4",children:d("responseData")}),x()]})})]})};function gk(){let e=(0,r_.c3)(),[t,r]=(0,c.useState)(!0),[n,o]=(0,c.useState)(null),[i,a]=(0,c.useState)(null),[s,u]=(0,c.useState)(),f=(0,rN.usePathname)(),p=async(e,t)=>{try{r(!0),o(null);let n=f.split("/"),i=n[n.indexOf("project")+1];if(!i)throw Error("Project ID not found in URL");let s=new URLSearchParams;e&&s.append("startDate",e.toISOString()),t&&s.append("endDate",t.toISOString());let l=await rD.A.get(`/projects/${i}/report?${s.toString()}`);if(l.data?.data)a(l.data.data);else throw Error("Invalid response format from server")}catch(e){console.error("Error fetching report data:",e),"ERR_NETWORK"===e.code?o("Unable to connect to the server. Please make sure the backend server is running."):e.response?.status===401?o("You are not authorized to view this report. Please log in again."):e.response?.status===403?o("You don't have permission to view this report."):e.response?.status===404?o("Report not found. The project may have been deleted or you don't have access."):o(e.response?.data?.message||e.message||"Failed to load report data. Please try again later.")}finally{r(!1)}};return t?(0,l.jsxs)("div",{className:"flex flex-col items-center justify-center h-[calc(100vh-200px)]",children:[(0,l.jsx)(h.A,{className:"w-10 h-10 animate-spin text-primary"}),(0,l.jsx)("p",{className:"mt-4 text-lg",children:e("loadingReportData")})]}):n||!i?(0,l.jsx)("div",{className:"p-4 md:p-8",children:(0,l.jsxs)("div",{className:"mb-6",children:[(0,l.jsx)("h1",{className:"text-2xl font-bold",children:e("dataReport")}),(0,l.jsxs)("div",{className:"p-8 mt-4 text-center border rounded-md border-gray-200 bg-gray-50",children:[(0,l.jsx)("p",{className:"text-lg text-red-500",children:n||e("noDataAvailable")}),(0,l.jsx)("p",{className:"mt-2 text-gray-600",children:!n&&e("submitToGenerateReport")})]})]})}):(0,l.jsxs)("div",{className:"p-4 md:p-8",children:[(0,l.jsxs)("div",{className:"mb-6",children:[(0,l.jsxs)("div",{className:"flex flex-col space-y-4 md:space-y-0 md:flex-row md:justify-between md:items-center",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"text-2xl font-bold",children:e("dataReport")}),(0,l.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:i.metadata.projectName})]}),(0,l.jsx)(rC,{value:s,onChange:e=>{u(e),e?.from&&e?.to&&p(e.from,e.to)}})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 gap-4 mt-6 md:grid-cols-3",children:[(0,l.jsxs)(v,{children:[(0,l.jsx)(m,{children:(0,l.jsx)(b,{className:"text-sm font-medium",children:e("totalSubmissions")})}),(0,l.jsx)(g,{children:(0,l.jsx)("p",{className:"text-2xl font-bold",children:i.summary.totalSubmissions})})]}),(0,l.jsxs)(v,{children:[(0,l.jsx)(m,{children:(0,l.jsx)(b,{className:"text-sm font-medium",children:e("totalQuestions")})}),(0,l.jsx)(g,{children:(0,l.jsx)("p",{className:"text-2xl font-bold",children:i.summary.totalQuestions})})]}),(0,l.jsxs)(v,{children:[(0,l.jsx)(m,{children:(0,l.jsx)(b,{className:"text-sm font-medium",children:e("responseRate")})}),(0,l.jsx)(g,{children:(0,l.jsxs)("p",{className:"text-2xl font-bold",children:[i.summary.averageResponseRate,"%"]})})]})]})]}),(0,l.jsx)("div",{children:i.data.map((e,t)=>(0,l.jsx)(gA,{report:e},t))})]})}},74075:e=>{"use strict";e.exports=require("zlib")},74610:e=>{e.exports=function(e,t,r){for(var n=r-1,o=e.length;++n<o;)if(e[n]===t)return n;return -1}},75254:(e,t,r)=>{var n=r(78418),o=r(93311),i=r(41132);e.exports=function(e){var t=o(e);return 1==t.length&&t[0][2]?i(t[0][0],t[0][1]):function(r){return r===e||n(r,e,t)}}},75411:e=>{e.exports=function(e){var t=this.__data__,r=t.delete(e);return this.size=t.size,r}},75847:(e,t,r)=>{var n=r(67554);e.exports=function(e,t){var r;return n(e,function(e,n,o){return!(r=t(e,n,o))}),!!r}},77822:(e,t,r)=>{var n=r(93490);e.exports=function(e){return n(e)&&e!=+e}},77834:e=>{var t=Object.prototype;e.exports=function(e){var r=e&&e.constructor;return e===("function"==typeof r&&r.prototype||t)}},78407:(e,t,r)=>{"use strict";r.d(t,{F:()=>o});var n=r(43210);let o=({projectData:e,user:t})=>(0,n.useMemo)(()=>{let r=t?.id===e?.user?.id,n=e?.projectUser?.[0],o=n?.permission||{};return{viewForm:r||o.viewForm||!1,editForm:r||o.editForm||!1,viewSubmissions:r||o.viewSubmissions||!1,addSubmissions:r||o.addSubmissions||!1,deleteSubmissions:r||o.deleteSubmissions||!1,validateSubmissions:r||o.validateSubmissions||!1,editSubmissions:r||o.editSubmissions||!1,manageProject:r||o.manageProject||!1}},[t?.id,e])},78418:(e,t,r)=>{var n=r(67200),o=r(15871);e.exports=function(e,t,r,i){var a=r.length,s=a,l=!i;if(null==e)return!s;for(e=Object(e);a--;){var c=r[a];if(l&&c[2]?c[1]!==e[c[0]]:!(c[0]in e))return!1}for(;++a<s;){var u=(c=r[a])[0],f=e[u],p=c[1];if(l&&c[2]){if(void 0===f&&!(u in e))return!1}else{var d=new n;if(i)var h=i(f,p,u,e,t,d);if(!(void 0===h?o(p,f,3,i,d):h))return!1}}return!0}},78424:(e,t,r)=>{Promise.resolve().then(r.bind(r,3945))},79474:(e,t,r)=>{e.exports=r(85718).Symbol},79551:e=>{"use strict";e.exports=require("url")},80195:(e,t,r)=>{var n=r(79474),o=r(21367),i=r(40542),a=r(49227),s=1/0,l=n?n.prototype:void 0,c=l?l.toString:void 0;e.exports=function e(t){if("string"==typeof t)return t;if(i(t))return o(t,e)+"";if(a(t))return c?c.call(t):"";var r=t+"";return"0"==r&&1/t==-s?"-0":r}},80329:(e,t,r)=>{e=r.nmd(e);var n=r(85718),o=r(1944),i=t&&!t.nodeType&&t,a=i&&e&&!e.nodeType&&e,s=a&&a.exports===i?n.Buffer:void 0,l=s?s.isBuffer:void 0;e.exports=l||o},80458:(e,t,r)=>{var n=r(29395),o=r(69619),i=r(27467),a={};a["[object Float32Array]"]=a["[object Float64Array]"]=a["[object Int8Array]"]=a["[object Int16Array]"]=a["[object Int32Array]"]=a["[object Uint8Array]"]=a["[object Uint8ClampedArray]"]=a["[object Uint16Array]"]=a["[object Uint32Array]"]=!0,a["[object Arguments]"]=a["[object Array]"]=a["[object ArrayBuffer]"]=a["[object Boolean]"]=a["[object DataView]"]=a["[object Date]"]=a["[object Error]"]=a["[object Function]"]=a["[object Map]"]=a["[object Number]"]=a["[object Object]"]=a["[object RegExp]"]=a["[object Set]"]=a["[object String]"]=a["[object WeakMap]"]=!1,e.exports=function(e){return i(e)&&o(e.length)&&!!a[n(e)]}},80704:(e,t,r)=>{var n=r(96678);e.exports=function(e,t){return!!(null==e?0:e.length)&&n(e,t,0)>-1}},81488:e=>{e.exports=function(e,t){return null!=e&&t in Object(e)}},81630:e=>{"use strict";e.exports=require("http")},81957:(e,t,r)=>{var n=r(49227);e.exports=function(e,t){if(e!==t){var r=void 0!==e,o=null===e,i=e==e,a=n(e),s=void 0!==t,l=null===t,c=t==t,u=n(t);if(!l&&!u&&!a&&e>t||a&&s&&c&&!l&&!u||o&&s&&c||!r&&c||!i)return 1;if(!o&&!a&&!u&&e<t||u&&r&&i&&!o&&!a||l&&r&&i||!s&&i||!c)return -1}return 0}},82038:(e,t,r)=>{var n=r(34821),o=r(35163),i=r(40542),a=r(80329),s=r(38428),l=r(10090),c=Object.prototype.hasOwnProperty;e.exports=function(e,t){var r=i(e),u=!r&&o(e),f=!r&&!u&&a(e),p=!r&&!u&&!f&&l(e),d=r||u||f||p,h=d?n(e.length,String):[],y=h.length;for(var v in e)(t||c.call(e,v))&&!(d&&("length"==v||f&&("offset"==v||"parent"==v)||p&&("buffer"==v||"byteLength"==v||"byteOffset"==v)||s(v,y)))&&h.push(v);return h}},82324:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>f,pages:()=>u,routeModule:()=>p,tree:()=>c});var n=r(65239),o=r(48088),i=r(88170),a=r.n(i),s=r(30893),l={};for(let e in s)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>s[e]);r.d(t,l);let c={children:["",{children:["[locale]",{children:["(main)",{children:["project",{children:["[hashedId]",{children:["data",{children:["reports",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,3945)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\project\\[hashedId]\\data\\reports\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,87282)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\project\\[hashedId]\\data\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,51129)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\project\\[hashedId]\\layout.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,84606)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,72121)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\project\\[hashedId]\\data\\reports\\page.tsx"],f={require:r,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/[locale]/(main)/project/[hashedId]/data/reports/page",pathname:"/[locale]/project/[hashedId]/data/reports",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},83997:e=>{"use strict";e.exports=require("tty")},84031:(e,t,r)=>{"use strict";var n=r(34452);function o(){}function i(){}i.resetWarningCache=o,e.exports=function(){function e(e,t,r,o,i,a){if(a!==n){var s=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw s.name="Invariant Violation",s}}function t(){return e}e.isRequired=e;var r={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:i,resetWarningCache:o};return r.PropTypes=r,r}},84261:e=>{e.exports=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=!!t,t}},84482:(e,t,r)=>{var n=r(28977);e.exports=function(e){var t=n(e),r=t%1;return t==t?r?t-r:t:0}},84638:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var n=r(60687);r(43210);var o=r(16189),i=r(62688);let a=(0,i.A)("table-2",[["path",{d:"M9 3H5a2 2 0 0 0-2 2v4m6-6h10a2 2 0 0 1 2 2v4M9 3v18m0 0h10a2 2 0 0 0 2-2V9M9 21H5a2 2 0 0 1-2-2V9m0 0h18",key:"gugj83"}]]),s=(0,i.A)("chart-no-axes-column",[["line",{x1:"18",x2:"18",y1:"20",y2:"10",key:"1xfpm4"}],["line",{x1:"12",x2:"12",y1:"20",y2:"4",key:"be30l9"}],["line",{x1:"6",x2:"6",y1:"20",y2:"14",key:"1r4le6"}]]);var l=r(31158),c=r(77618);function u({children:e}){let{hashedId:t}=(0,o.useParams)(),r=(0,c.c3)(),i=[{label:r("data"),href:`/project/${t}/data`,icon:a},{label:r("reports"),href:`/project/${t}/data/reports`,icon:s},{label:r("downloads"),href:`/project/${t}/data/downloads`,icon:l.A}],u=(0,o.usePathname)(),f=(0,o.useRouter)();return(0,n.jsxs)("div",{className:"flex flex-col min-h-screen bg-neutral-100",children:[(0,n.jsxs)("div",{className:"flex justify-between mb-4",children:[(0,n.jsx)("h2",{className:"heading-text",children:r("surveyResults")}),(0,n.jsxs)("div",{className:"",children:[(0,n.jsx)("h2",{className:"flex flex-col text-sm font-medium text-neutral-700 mb-1",children:r("navigate")}),(0,n.jsxs)("select",{value:i.some(e=>e.href===u)?u:"",onChange:e=>{let t=e.target.value;t!==u&&f.push(t)},className:" p-2 border border-neutral-300 rounded-md shadow-sm cursor-pointer",children:[(0,n.jsx)("option",{value:"",children:r(u===`/project/${t}/data`?"datatableOverview":"select")}),i.map(({label:e,href:t})=>(0,n.jsx)("option",{value:t,children:e},e))]})]})]}),(0,n.jsx)("main",{className:"p-4 bg-neutral-100 rounded-md border border-neutral-300 shadow-sm",children:(0,n.jsx)("div",{children:e})})]})}},84713:e=>{var t=Object.prototype.toString;e.exports=function(e){return t.call(e)}},85244:e=>{e.exports=function(e,t,r){switch(r.length){case 0:return e.call(t);case 1:return e.call(t,r[0]);case 2:return e.call(t,r[0],r[1]);case 3:return e.call(t,r[0],r[1],r[2])}return e.apply(t,r)}},85406:(e,t,r)=>{e.exports=r(85718)["__core-js_shared__"]},85450:(e,t,r)=>{var n=r(79474),o=r(35163),i=r(40542),a=n?n.isConcatSpreadable:void 0;e.exports=function(e){return i(e)||o(e)||!!(a&&e&&e[a])}},85718:(e,t,r)=>{var n=r(10663),o="object"==typeof self&&self&&self.Object===Object&&self;e.exports=n||o||Function("return this")()},85745:(e,t,r)=>{var n=r(86451);e.exports=function(e){var t=n(e,function(e){return 500===r.size&&r.clear(),e}),r=t.cache;return t}},85938:(e,t,r)=>{var n=r(42205),o=r(17518),i=r(46229),a=r(7383);e.exports=i(function(e,t){if(null==e)return[];var r=t.length;return r>1&&a(e,t[0],t[1])?t=[]:r>2&&a(t[0],t[1],t[2])&&(t=[t[0]]),o(e,n(t,1),[])})},86451:(e,t,r)=>{var n=r(95746);function o(e,t){if("function"!=typeof e||null!=t&&"function"!=typeof t)throw TypeError("Expected a function");var r=function(){var n=arguments,o=t?t.apply(this,n):n[0],i=r.cache;if(i.has(o))return i.get(o);var a=e.apply(this,n);return r.cache=i.set(o,a)||i,a};return r.cache=new(o.Cache||n),r}o.Cache=n,e.exports=o},87270:e=>{e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length;++r<n;)if(!t(e[r],r,e))return!1;return!0}},87282:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Kobo-project\\\\data-analysis-tool-frontend\\\\app\\\\[locale]\\\\(main)\\\\project\\\\[hashedId]\\\\data\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\project\\[hashedId]\\data\\layout.tsx","default")},87321:(e,t,r)=>{var n=r(98798),o=r(7383),i=r(28977);e.exports=function(e){return function(t,r,a){return a&&"number"!=typeof a&&o(t,r,a)&&(r=a=void 0),t=i(t),void 0===r?(r=t,t=0):r=i(r),a=void 0===a?t<r?1:-1:i(a),n(t,r,a,e)}}},87506:(e,t,r)=>{var n=r(66837),o=r(84261),i=r(89492),a=r(90200),s=r(39672);function l(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}l.prototype.clear=n,l.prototype.delete=o,l.prototype.get=i,l.prototype.has=a,l.prototype.set=s,e.exports=l},87955:(e,t,r)=>{e.exports=r(84031)()},89167:(e,t,r)=>{e.exports=r(41547)(r(85718),"DataView")},89185:e=>{e.exports=function(e){return this.__data__.set(e,"__lodash_hash_undefined__"),this}},89492:(e,t,r)=>{var n=r(58141),o=Object.prototype.hasOwnProperty;e.exports=function(e){var t=this.__data__;if(n){var r=t[e];return"__lodash_hash_undefined__"===r?void 0:r}return o.call(t,e)?t[e]:void 0}},89605:(e,t,r)=>{e.exports=r(65662)(Object.keys,Object)},89624:e=>{e.exports=function(e){return function(t){return e(t)}}},90200:(e,t,r)=>{var n=r(58141),o=Object.prototype.hasOwnProperty;e.exports=function(e){var t=this.__data__;return n?void 0!==t[e]:o.call(t,e)}},90453:(e,t,r)=>{var n=r(2984),o=r(99180),i=r(48169);e.exports=function(e){return e&&e.length?n(e,i,o):void 0}},90541:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>O});var n=r(60687),o=r(86429),i=r(86757),a=r(17090),s=r(61611),l=r(84027),c=r(16189),u=r(20174),f=r(77618);let p=({permissions:e})=>{let{hashedId:t}=(0,c.useParams)(),r=(0,f.c3)(),o=e.manageProject,p=o||e.viewForm||e.editForm,d=o||e.viewSubmissions||e.editSubmissions||e.addSubmissions||e.deleteSubmissions,h=[{label:r("overview"),icon:(0,n.jsx)(i.A,{size:16}),route:`/project/${t}/overview`,disabled:!1},{label:r("formBuilder"),icon:(0,n.jsx)(a.A,{size:16}),route:`/project/${t}/form-builder`,disabled:!p},{label:r("data"),icon:(0,n.jsx)(s.A,{size:16}),route:`/project/${t}/data`,disabled:!d},{label:r("settings"),icon:(0,n.jsx)(l.A,{size:16}),route:`/project/${t}/settings`,disabled:!o}];return(0,n.jsx)(u.F,{items:h})};var d=r(21650),h=r(78407),y=r(71845),v=r(6986),m=r(29494),b=r(28559),g=r(85814),x=r.n(g),w=r(43210);let O=({children:e})=>{let{hashedId:t}=(0,c.useParams)(),{user:r}=(0,d.A)(),i=(0,f.c3)(),a=(0,w.useMemo)(()=>(0,v.D)(t),[t]),{data:s,isLoading:l,isError:u}=(0,m.I)({queryKey:["projects",r?.id,a],queryFn:()=>(0,y.kf)({projectId:a}),enabled:!!a&&!!r?.id}),g=(0,h.F)({projectData:s,user:r});return t&&null!==a?l?(0,n.jsx)(o.A,{}):u?(0,n.jsx)("p",{className:"text-red-500",children:i("fetchProjectFailed")}):(0,n.jsxs)("div",{className:"section flex flex-col gap-4",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)("h1",{className:"heading-text capitalize",children:s?.name}),(0,n.jsxs)(x(),{href:"/dashboard",className:"flex items-center gap-2",children:[(0,n.jsx)(b.A,{size:16}),i("backToDashboard")]})]}),(0,n.jsx)(p,{permissions:g}),(0,n.jsx)("div",{className:"px-8",children:e})]}):(0,n.jsxs)("div",{className:"error-message",children:[(0,n.jsx)("h1",{className:"text-red-500",children:i("invalidProjectIdError")}),(0,n.jsx)("p",{className:"text-neutral-700",children:i("invalidProjectIdMessage")})]})}},90851:e=>{e.exports=function(e,t){return null==e?void 0:e[t]}},91290:e=>{e.exports=function(e,t,r,n){for(var o=e.length,i=r+(n?1:-1);n?i--:++i<o;)if(t(e[i],i,e))return i;return -1}},91928:(e,t,r)=>{var n=r(41547);e.exports=function(){try{var e=n(Object,"defineProperty");return e({},"",{}),e}catch(e){}}()},92662:(e,t,r)=>{var n=r(46328),o=r(80704),i=r(71960),a=r(58276),s=r(95308),l=r(2408);e.exports=function(e,t,r){var c=-1,u=o,f=e.length,p=!0,d=[],h=d;if(r)p=!1,u=i;else if(f>=200){var y=t?null:s(e);if(y)return l(y);p=!1,u=a,h=new n}else h=t?[]:d;t:for(;++c<f;){var v=e[c],m=t?t(v):v;if(v=r||0!==v?v:0,p&&m==m){for(var b=h.length;b--;)if(h[b]===m)continue t;t&&h.push(m),d.push(v)}else u(h,m,r)||(h!==d&&h.push(m),d.push(v))}return d}},93311:(e,t,r)=>{var n=r(34883),o=r(7651);e.exports=function(e){for(var t=o(e),r=t.length;r--;){var i=t[r],a=e[i];t[r]=[i,a,n(a)]}return t}},93490:(e,t,r)=>{var n=r(29395),o=r(27467);e.exports=function(e){return"number"==typeof e||o(e)&&"[object Number]"==n(e)}},94388:(e,t,r)=>{var n=r(57797);e.exports=function(e){var t=this.__data__,r=n(t,e);return r<0?void 0:t[r][1]}},94735:e=>{"use strict";e.exports=require("events")},95308:(e,t,r)=>{var n=r(34772),o=r(36959),i=r(2408);e.exports=n&&1/i(new n([,-0]))[1]==1/0?function(e){return new n(e)}:o},95746:(e,t,r)=>{var n=r(15909),o=r(29205),i=r(29508),a=r(61320),s=r(19976);function l(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}l.prototype.clear=n,l.prototype.delete=o,l.prototype.get=i,l.prototype.has=a,l.prototype.set=s,e.exports=l},96678:(e,t,r)=>{var n=r(91290),o=r(39774),i=r(74610);e.exports=function(e,t,r){return t==t?i(e,t,r):n(e,o,r)}},97668:(e,t)=>{"use strict";var r,n=Symbol.for("react.element"),o=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),s=Symbol.for("react.profiler"),l=Symbol.for("react.provider"),c=Symbol.for("react.context"),u=Symbol.for("react.server_context"),f=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),d=Symbol.for("react.suspense_list"),h=Symbol.for("react.memo"),y=Symbol.for("react.lazy");Symbol.for("react.offscreen");Symbol.for("react.module.reference"),t.isFragment=function(e){return function(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case n:switch(e=e.type){case i:case s:case a:case p:case d:return e;default:switch(e=e&&e.$$typeof){case u:case c:case f:case y:case h:case l:return e;default:return t}}case o:return t}}}(e)===i}},98451:(e,t,r)=>{var n=r(29395),o=r(27467);e.exports=function(e){return!0===e||!1===e||o(e)&&"[object Boolean]"==n(e)}},98798:e=>{var t=Math.ceil,r=Math.max;e.exports=function(e,n,o,i){for(var a=-1,s=r(t((n-e)/(o||1)),0),l=Array(s);s--;)l[i?s:++a]=e,e+=o;return l}},99114:(e,t,r)=>{var n=r(12344),o=r(7651);e.exports=function(e,t){return e&&n(e,t,o)}},99180:e=>{e.exports=function(e,t){return e>t}},99525:e=>{e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length;++r<n;)if(t(e[r],r,e))return!0;return!1}}};var t=require("../../../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[4447,7404,1658,6560,8610,5374,2198,5814,3851,8581,4898,5841,5041],()=>r(82324));module.exports=n})();