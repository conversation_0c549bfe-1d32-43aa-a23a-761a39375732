import {
  InputType,
  Question,
  QuestionCondition,
  QuestionOption,
} from "@prisma/client";
import { prisma } from "../utils/prisma";

class QuestionRepository {
  async findById(id: number): Promise<
    | (Question & {
        questionOptions: QuestionOption[];
        questionConditions: QuestionCondition[];
      })
    | null
  > {
    return await prisma.question.findUnique({
      where: {
        id,
      },
      include: {
        questionOptions: true,
        questionConditions: true,
      },
    });
  }

  async isPorjectOwner(userId: number, projectId: number): Promise<boolean> {
    const project = await prisma.project.findUnique({
      where: {
        id: projectId,
      },
      select: { userId: true },
    });

    return !!project && project.userId === userId;
  }

  async findAll(projectId?: number): Promise<
    (Question & {
      questionOptions: QuestionOption[];
      questionConditions: QuestionCondition[];
    })[]
  > {
    return await prisma.question.findMany({
      where: projectId ? { projectId } : undefined,
      include: {
        questionOptions: true,
        questionConditions: true,
      },
      orderBy: { position: "asc" },
    });
  }

  async create(questionData: {
    projectId: number;
    label: string;
    inputType: InputType;
    hint?: string;
    placeholder?: string;
    isRequired?: boolean;
    position: number;
    options?: string[];
  }): Promise<Question> {
    const {
      projectId,
      label,
      inputType,
      hint,
      placeholder,
      isRequired,
      position,
      options,
    } = questionData;

    return await prisma.question.create({
      data: {
        projectId,
        label,
        inputType,
        hint: hint ?? "", // fallback to empty string
        placeholder: placeholder ?? "",
        isRequired,
        position,
        questionOptions: options
          ? {
              create: options.map((opt) => ({ label: opt, code: opt })),
            }
          : undefined,
      },
      include: {
        questionOptions: true,
      },
    });
  }

  // async updateById(
  //   id: number,
  //   updateData: {
  //     label?: string;
  //     inputType?: InputType;
  //     hint?: string;
  //     placeholder?: string;
  //     isRequired?: boolean;
  //     position?: number;
  //   }
  // ): Promise<Partial<Question> | null> {
  //   const data: {
  //     label?: string;
  //     inputType?: InputType;
  //     hint?: string;
  //     placeholder?: string;
  //     isRequired?: boolean;
  //     position?: number;
  //   } = {};

  //   if (updateData.label !== undefined) data.label = updateData.label;
  //   if (updateData.inputType !== undefined)
  //     data.inputType = updateData.inputType;
  //   if (updateData.hint !== undefined) data.hint = updateData.hint;
  //   if (updateData.placeholder !== undefined)
  //     data.placeholder = updateData.placeholder;
  //   if (updateData.isRequired !== undefined)
  //     data.isRequired = updateData.isRequired;
  //   if (updateData.position !== undefined) data.position = updateData.position;

  //   return await prisma.question.update({
  //     where: { id },
  //     data,
  //   });
  // }

  async updateById(
  id: number,
  updateData: {
    label?: string;
    inputType?: InputType;
    hint?: string;
    placeholder?: string;
    isRequired?: boolean;
    position?: number;
    options?: Array<{
      label: string;
      code: string;
      sublabel?: string;
      nextQuestionId?: number;
    }>;
  }
): Promise<Partial<Question> | null> {
  const data: {
    label?: string;
    inputType?: InputType;
    hint?: string;
    placeholder?: string;
    isRequired?: boolean;
    position?: number;
  } = {};

  if (updateData.label !== undefined) data.label = updateData.label;
  if (updateData.inputType !== undefined) data.inputType = updateData.inputType;
  if (updateData.hint !== undefined) data.hint = updateData.hint;
  if (updateData.placeholder !== undefined) data.placeholder = updateData.placeholder;
  if (updateData.isRequired !== undefined) data.isRequired = updateData.isRequired;
  if (updateData.position !== undefined) data.position = updateData.position;

  // Handle options update in a transaction
  return await prisma.$transaction(async (tx) => {
    // Update the question
    const updatedQuestion = await tx.question.update({
      where: { id },
      data,
    });

    // If options are provided, update them
    if (updateData.options !== undefined) {
      // Delete existing options
      await tx.questionOption.deleteMany({
        where: { questionId: id },
      });

      // Create new options if any provided
      if (updateData.options.length > 0) {
        await tx.questionOption.createMany({
          data: updateData.options.map((option, index) => ({
            questionId: id,
            label: option.label,
            code: option.code,
            sublabel: option.sublabel,
            nextQuestionId: option.nextQuestionId,
            // position: index + 1, // Add position if your schema requires it
          })),
        });
      }
    }

    // Return the updated question with options
    return await tx.question.findUnique({
      where: { id },
      include: {
        questionOptions: true,
      },
    });
  });
}

  async deleteQuestion(id: number): Promise<Question> {
    return await prisma.question.delete({
      where: { id },
    });
  }

  async duplicateQuestion(
    id: number,
    projectId: number
  ): Promise<Question | null> {
    // Find the question to duplicate
    const question = await prisma.question.findUnique({
      where: { id },
    });

    if (!question) {
      return null;
    }

    const duplicatedQuestion = await prisma.question.create({
      data: {
        ...question,
        id: undefined, // Reset the ID to create a new question
        projectId, // Set the new project ID
      },
      include: {
        questionOptions: true,
        questionConditions: true,
      },
    });

    return duplicatedQuestion;
  }

  async questionoptions() {
    return await prisma.questionOption.findMany({});
  }

  async createQuestion(data: {
    projectId: number;
    label: string;
    inputType: InputType;
    hint?: string;
    placeholder?: string;
    isRequired?: boolean;
    position: number;
  }) {
    return await prisma.question.create({
      data: {
        projectId: data.projectId,
        label: data.label,
        inputType: data.inputType,
        hint: data.hint ?? "",
        placeholder: data.placeholder ?? "",
        isRequired: data.isRequired ?? false,
        position: data.position,
      },
    });
  }

  async createQuestionOption(data: {
    questionId: number;
    label: string;
    code: string;
    nextQuestionId?: number | null;
  }) {
    return await prisma.questionOption.create({
      data: {
        questionId: data.questionId,
        label: data.label,
        code: data.code,
        nextQuestionId: data.nextQuestionId,
      },
    });
  }

  async updateMultiplePositions(
    questionPositions: { id: number; position: number }[]
  ): Promise<Question[]> {
    return await prisma.$transaction(async (tx) => {
      const updatedQuestions: Question[] = [];

      for (const { id, position } of questionPositions) {
        const updatedQuestion = await tx.question.update({
          where: { id },
          data: { position },
          include: {
            questionOptions: true,
            questionConditions: true,
          },
        });
        updatedQuestions.push(updatedQuestion);
      }

      return updatedQuestions;
    });
  }

  // NEW UNIFIED POSITION SYSTEM: Update positions using ProjectQuestionOrder schema
  async updateUnifiedPositions(
    projectId: number,
    positionUpdates: {
      id: number;
      position: number;
      type: 'question' | 'group';
      parentGroupId?: number | null;
      groupId?: number | null;
    }[]
  ): Promise<{ questions: Question[]; groups: any[] }> {
    return await prisma.$transaction(async (tx) => {
      const updatedQuestions: Question[] = [];
      const updatedGroups: any[] = [];

      for (const update of positionUpdates) {
        if (update.type === 'question') {
          // Update or create ProjectQuestionOrder for question
          const existingOrder = await tx.projectQuestionOrder.findFirst({
            where: {
              questionId: update.id,
              projectId,
              type: 'question',
            },
          });

          if (existingOrder) {
            // Update existing ProjectQuestionOrder
            await tx.projectQuestionOrder.update({
              where: { id: existingOrder.id },
              data: {
                position: update.position,
                parentGroupId: update.parentGroupId,
                groupId: update.groupId,
              },
            });
          } else {
            // Create new ProjectQuestionOrder
            await tx.projectQuestionOrder.create({
              data: {
                questionId: update.id,
                projectId,
                type: 'question',
                position: update.position,
                parentGroupId: update.parentGroupId,
                groupId: update.groupId,
              },
            });
          }

          // Also update the legacy question.position field for backward compatibility
          const updatedQuestion = await tx.question.update({
            where: { id: update.id },
            data: { position: update.position },
            include: {
              questionOptions: true,
              questionConditions: true,
            },
          });
          updatedQuestions.push(updatedQuestion);

        } else if (update.type === 'group') {
          // Update or create ProjectQuestionOrder for group
          const existingOrder = await tx.projectQuestionOrder.findFirst({
            where: {
              groupId: update.id,
              projectId,
              type: 'group',
            },
          });

          if (existingOrder) {
            // Update existing ProjectQuestionOrder
            await tx.projectQuestionOrder.update({
              where: { id: existingOrder.id },
              data: {
                position: update.position,
                parentGroupId: update.parentGroupId,
              },
            });
          } else {
            // Create new ProjectQuestionOrder
            await tx.projectQuestionOrder.create({
              data: {
                groupId: update.id,
                projectId,
                type: 'group',
                position: update.position,
                parentGroupId: update.parentGroupId,
              },
            });
          }

          // Also update the legacy questionGroup.order field for backward compatibility
          const updatedGroup = await tx.questionGroup.update({
            where: { id: update.id },
            data: { order: update.position },
          });
          updatedGroups.push(updatedGroup);
        }
      }

      return { questions: updatedQuestions, groups: updatedGroups };
    });
  }
}

export default new QuestionRepository();
