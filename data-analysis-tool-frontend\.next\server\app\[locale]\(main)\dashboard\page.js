(()=>{var e={};e.id=4361,e.ids=[4361],e.modules={1306:(e,t,r)=>{Promise.resolve().then(r.bind(r,81494))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},13861:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35586:(e,t,r)=>{Promise.resolve().then(r.bind(r,41948))},41948:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>R});var s=r(60687),o=r(43210),a=r.n(o),i=r(69587),l=r(44255),n=r(68988),c=r(55629),d=r(24934),u=r(73678),p=r(58857),h=r(93617),m=r(26273),x=r(93437),g=r(85814),f=r.n(g),y=r(6986),v=r(96241),j=r(13861),b=r(77618);let w=(e,t,r)=>{if(null==e)return"-";if("boolean"==typeof e)return e?r?r("yes"):"Yes":r?r("no"):"No";if(e instanceof Date)return(0,v.Y)(e);if("date"===t&&"string"==typeof e)try{return(0,v.Y)(new Date(e))}catch{return e}return String(e)},C=()=>{let e=(0,b.c3)();return[{id:"select",header:({table:t})=>(0,s.jsx)(x.S,{className:"w-6 h-6 data-[state=checked]:bg-neutral-100 data-[state=checked]:text-primary-500 border-neutral-100 cursor-pointer",checked:t.getIsAllPageRowsSelected()||t.getIsSomePageRowsSelected()&&"indeterminate",onCheckedChange:e=>t.toggleAllPageRowsSelected(!!e),"aria-label":e("select")}),cell:({row:t})=>{let r=t.original.id,o=(0,y.l)(r);return(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(x.S,{className:"w-6 h-6 bg-neutral-100 border-neutral-400 data-[state=checked]:bg-neutral-100 data-[state=checked]:text-primary-500 cursor-pointer",checked:t.getIsSelected(),onCheckedChange:e=>t.toggleSelected(!!e),"aria-label":e("select")}),(0,s.jsx)(f(),{title:e("overview"),href:`project/${o}/overview`,className:" hover:text-primary-500 transition-colors",children:(0,s.jsx)(j.A,{className:"w-5 h-5"})})]})},enableHiding:!1},{accessorKey:"name",header:({column:t})=>(0,s.jsxs)("button",{className:"flex items-center text-left",onClick:()=>t.toggleSorting("asc"===t.getIsSorted()),children:[e("projectName"),(0,s.jsx)(m.CBv,{className:"h-4 w-4"})]}),cell:({row:e})=>{let t=e.original.id,r=(0,y.l)(t);return(0,s.jsx)(f(),{href:`project/${r}/overview`,className:"cursor-pointer text-primary-500 hover:text-primary-600 hover:underline transition-all duration-300",children:e.getValue("name")})},enableSorting:!0,sortingFn:(e,t,r)=>{let s=e.getValue(r)?.toString().toLowerCase()||"",o=t.getValue(r)?.toString().toLowerCase()||"";return s.localeCompare(o)}},{accessorKey:"description",header:e("description")},{accessorKey:"status",header:e("status")},{accessorKey:"updatedAt",header:e("dateModified"),cell:({getValue:t})=>{let r=t();return(0,s.jsx)("div",{className:"font-medium text-neutral-700",children:w(r,"date",e)||e("notRecorded")||"Not recorded"})}},{accessorKey:"lastDeployedAt",header:e("dateDeployed")},{accessorKey:"sector",header:e("sector")},{accessorKey:"country",header:e("countries")},{accessorKey:"lastSubmittionAt",header:e("lastSubmissionAt"),sortingFn:"basic"}]};var N=r(71845),S=r(8693),k=r(54050),A=r(54864),P=r(19150),q=r(21650);let E="data-table-column-visibility",D=({data:e})=>{let t=(0,S.jE)(),r=(0,A.wA)(),m=(0,b.c3)(),x=C(),{user:g}=(0,q.A)(),[f,y]=(0,o.useState)(""),[v,j]=(0,o.useState)({}),[w,D]=(0,o.useState)(null),[K,M]=a().useState(!1),[_,O]=(0,o.useState)(!1),[R,I]=(0,o.useState)(null),[F,$]=(0,o.useState)(!1),[B,U]=(0,o.useState)(void 0),[G,V]=(0,o.useState)(!1),[T,z]=(0,o.useState)(!1),[L,W]=(0,o.useState)(!1),H=(0,k.n)({mutationFn:e=>(0,N.D_)(e),onSuccess:(e,s)=>{let o=s.length,a=1===o?m("projectArchived"):`${o} ${m("projectArchivedSuccess")}`;r((0,P.Ds)({message:a,type:"success"})),t.invalidateQueries({queryKey:["projects",g?.id]}),w?.resetRowSelection(),O(!1)},onError:e=>{console.error("Error archiving projects:",e),r((0,P.Ds)({message:m("projectArchiveFailed"),type:"error"})),O(!1)}}),J=(0,k.n)({mutationFn:e=>(0,N.or)(e),onSuccess:(e,s)=>{let o=s.length,a=1===o?m("projectDeleted"):`${o} ${m("projectDeleted")}`;r((0,P.Ds)({message:a,type:"success"})),t.invalidateQueries({queryKey:["projects",g?.id]}),w?.resetRowSelection(),O(!1)},onError:e=>{console.error("Error deleting projects:",e),r((0,P.Ds)({message:m("projectDeleteFailed"),type:"error"})),O(!1)}});return(0,o.useEffect)(()=>{try{let e=localStorage.getItem(E);if(e){let t=JSON.parse(e);t&&"object"==typeof t&&!Array.isArray(t)?j(t):console.warn("Invalid format in localstorage for column visibility")}}catch(e){console.error("Error loading column visibility:",e)}},[]),(0,o.useEffect)(()=>{if(Object.keys(v).length>0)try{localStorage.setItem(E,JSON.stringify(v))}catch(e){console.error("Error saving column visibility:",e)}},[v]),(0,s.jsxs)("div",{className:"bg-neutral-100 rounded-md",children:[(0,s.jsxs)("div",{className:"px-6 py-2",children:[(0,s.jsxs)("div",{className:"flex flex-col laptop:flex-row justify-between items-center laptop:gap-5 mobile:p-4 laptop:p-2",children:[(0,s.jsxs)("div",{className:"flex flex-col items-center gap-2 laptop:gap-14 laptop:flex-row  text-neutral-700 laptop:p-4",children:[(0,s.jsx)("h2",{className:" font-semibold",children:m("myProjects")}),(0,s.jsxs)("div",{className:"flex flex-col tablet:flex-row gap-4 items-center py-4",children:[(0,s.jsx)(n.p,{placeholder:m("searchAllColumns"),value:f,onChange:e=>y(e.target.value)}),w&&(0,s.jsxs)(c.rI,{open:K,onOpenChange:e=>M(e),children:[(0,s.jsx)(c.ty,{asChild:!0,children:(0,s.jsxs)(d.$,{variant:"outline",className:"flex items-center gap-2 cursor-pointer",children:[m("showHideColumns"),K?(0,s.jsx)(i.Ucs,{className:"w-3 h-3"}):(0,s.jsx)(i.Vr3,{className:"w-3 h-3"})]})}),(0,s.jsx)(c.SQ,{align:"start",className:" border bg-neutral-100 border-neutral-200 shadow-md",children:w.getAllColumns().filter(e=>e.getCanHide()).map(e=>(0,s.jsx)(c.hO,{className:"capitalize cursor-pointer hover:bg-neutral-200",checked:v[e.id]??!0,onCheckedChange:t=>j(r=>({...r,[e.id]:t})),children:e.id},e.id))})]})]})]}),(0,s.jsxs)("div",{className:"flex gap-4 text-neutral-700",children:[(0,s.jsx)("div",{title:m("archive"),className:`p-2 rounded-full transition-all duration-300 ease-in-out ${L?"hover:bg-primary-500 hover:text-neutral-100 cursor-pointer":"opacity-50"}`,onClick:L?()=>{I({title:m("confirmArchive"),description:m("confirmArchiveMessage"),confirmButtonText:m("archive"),confirmButtonClass:"btn-primary",onConfirm:()=>{let e=w?.getSelectedRowModel().rows.map(e=>e.original.id)||[];H.mutate(e)}}),O(!0)}:void 0,children:(0,s.jsx)(i.Wlj,{className:"h-4 w-4"})}),(0,s.jsx)("div",{title:m("share"),className:`p-2 rounded-full transition-all duration-300 ease-in-out ${T?"hover:bg-primary-500 hover:text-neutral-100 cursor-pointer":"opacity-50"}`,onClick:T?()=>{let e=w?.getSelectedRowModel().rows[0]?.original;e&&(U(e),$(!0))}:void 0,children:(0,s.jsx)(i.NPy,{className:"h-4 w-4"})}),(0,s.jsx)("div",{title:m("delete"),className:`p-2 rounded-full transition-all duration-300 ease-in-out ${G?"hover:bg-primary-500 hover:text-neutral-100 cursor-pointer":"opacity-50"}`,onClick:G?()=>{I({title:m("confirmDelete"),description:(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("p",{children:m("confirmDeleteMessage")}),(0,s.jsxs)("ul",{className:"list-disc list-inside flex flex-col gap-2 mt-2 text-neutral-700",children:[(0,s.jsx)("li",{children:m("deleteProjectWarning1")}),(0,s.jsx)("li",{children:m("deleteProjectWarning2")}),(0,s.jsx)("li",{children:m("deleteProjectWarning3")})]})]}),confirmButtonText:m("delete"),confirmButtonClass:"bg-red-500 hover:bg-red-600 cursor-pointer",onConfirm:()=>{let e=w?.getSelectedRowModel().rows.map(e=>e.original.id)||[];J.mutate(e)}}),O(!0)}:void 0,children:(0,s.jsx)(l.hJ0,{className:"h-4 w-4"})})]})]}),(0,s.jsx)("div",{className:" mx-auto",children:(0,s.jsx)(h.x,{columns:x,data:e,globalFilter:f,setGlobalFilter:y,onTableInit:e=>{D(e),Object.keys(v).length>0&&e.setColumnVisibility(v)},columnVisibility:v,setColumnVisibility:e=>{j(e)},onRowSelectionChange:t=>{let r=Object.keys(t),s=e.filter((e,t)=>r.includes(t.toString())),o=s.length>0&&s.every(e=>"deployed"===e.status.toLowerCase());V(r.length>0),z(1===r.length),W(o)}})})]}),R&&(0,s.jsx)(u.R,{showModal:_,onClose:()=>O(!1),onConfirm:R.onConfirm,title:R.title,description:R.description,confirmButtonText:R.confirmButtonText,confirmButtonClass:R.confirmButtonClass}),(0,s.jsx)(p.m,{showModal:F,selectedProject:B,onClose:()=>$(!1),onShare:()=>{$(!1)}})]})};var K=r(12810),M=r(29494),_=r(86429);let O=async()=>{let{data:e}=await K.A.get("/projects");return e.projects};function R(){let{user:e}=(0,q.A)(),t=(0,b.c3)(),{data:r,isLoading:o,isError:a}=(0,M.I)({queryKey:["projects",e?.id],queryFn:O,enabled:!!e?.id});return o||!r?(0,s.jsx)(_.A,{}):a?(0,s.jsx)("p",{className:"text-red-500",children:t("error_loading_data")}):(0,s.jsx)(D,{data:r})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56994:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var s=r(65239),o=r(48088),a=r(88170),i=r.n(a),l=r(30893),n={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);r.d(t,n);let c={children:["",{children:["[locale]",{children:["(main)",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,81494)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\dashboard\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,84606)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,72121)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\dashboard\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/[locale]/(main)/dashboard/page",pathname:"/[locale]/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81494:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Kobo-project\\\\data-analysis-tool-frontend\\\\app\\\\[locale]\\\\(main)\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\dashboard\\page.tsx","default")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,7404,1658,6560,8610,5374,2198,5814,3851,8581,5841,5041,7858],()=>r(56994));module.exports=s})();