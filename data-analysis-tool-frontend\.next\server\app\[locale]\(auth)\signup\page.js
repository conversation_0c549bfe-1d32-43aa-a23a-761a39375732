(()=>{var e={};e.id=1968,e.ids=[1968],e.modules={1510:(e,a,t)=>{"use strict";t.d(a,{F0:()=>u,pe:()=>s});let{Axios:r,AxiosError:s,CanceledError:i,isCancel:n,CancelToken:o,VERSION:l,all:c,Cancel:d,isAxiosError:u,spread:m,toFormData:p,AxiosHeaders:h,HttpStatusCode:x,formToJSON:g,getAdapter:b,mergeConfig:y}=t(51060).A},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11437:(e,a,t)=>{"use strict";t.d(a,{A:()=>r});let r=(0,t(62688).A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},12412:e=>{"use strict";e.exports=require("assert")},15566:e=>{"use strict";e.exports=JSON.parse('["Afghanistan","Albania","Algeria","Andorra","Angola","Argentina","Armenia","Australia","Austria","Azerbaijan","Bahamas","Bahrain","Bangladesh","Barbados","Belarus","Belgium","Belize","Benin","Bhutan","Bolivia","Bosnia and Herzegovina","Botswana","Brazil","Brunei","Bulgaria","Burkina Faso","Burundi","Cabo Verde","Cambodia","Cameroon","Canada","Central African Republic","Chad","Chile","China","Colombia","Comoros","Congo (Congo-Brazzaville)","Costa Rica","Croatia","Cuba","Cyprus","Czech Republic","Denmark","Djibouti","Dominica","Dominican Republic","Ecuador","Egypt","El Salvador","Equatorial Guinea","Eritrea","Estonia","Eswatini","Ethiopia","Fiji","Finland","France","Gabon","Gambia","Georgia","Germany","Ghana","Greece","Grenada","Guatemala","Guinea","Guinea-Bissau","Guyana","Haiti","Honduras","Hungary","Iceland","India","Indonesia","Iran","Iraq","Ireland","Israel","Italy","Jamaica","Japan","Jordan","Kazakhstan","Kenya","Kiribati","Kuwait","Kyrgyzstan","Laos","Latvia","Lebanon","Lesotho","Liberia","Libya","Liechtenstein","Lithuania","Luxembourg","Madagascar","Malawi","Malaysia","Maldives","Mali","Malta","Marshall Islands","Mauritania","Mauritius","Mexico","Micronesia","Moldova","Monaco","Mongolia","Montenegro","Morocco","Mozambique","Myanmar","Namibia","Nauru","Nepal","Netherlands","New Zealand","Nicaragua","Niger","Nigeria","North Korea","North Macedonia","Norway","Oman","Pakistan","Palau","Palestine","Panama","Papua New Guinea","Paraguay","Peru","Philippines","Poland","Portugal","Qatar","Romania","Russia","Rwanda","Saint Kitts and Nevis","Saint Lucia","Saint Vincent and the Grenadines","Samoa","San Marino","Sao Tome and Principe","Saudi Arabia","Senegal","Serbia","Seychelles","Sierra Leone","Singapore","Slovakia","Slovenia","Solomon Islands","Somalia","South Africa","South Korea","South Sudan","Spain","Sri Lanka","Sudan","Suriname","Sweden","Switzerland","Syria","Taiwan","Tajikistan","Tanzania","Thailand","Timor-Leste","Togo","Tonga","Trinidad and Tobago","Tunisia","Turkey","Turkmenistan","Tuvalu","Uganda","Ukraine","United Arab Emirates","United Kingdom","United States","Uruguay","Uzbekistan","Vanuatu","Vatican City","Venezuela","Vietnam","Yemen","Zambia","Zimbabwe"]')},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},25031:(e,a,t)=>{Promise.resolve().then(t.bind(t,76509))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32833:(e,a,t)=>{"use strict";t.d(a,{b:()=>r});let r={information_media:"Information / Media",econommic_social_development:"Economic & Social Development",security_police_peacekeeping:"Security / Police / Peacekeeping",disarmament_and_demobilization:"Disarmament & Demobilization",environment:"Environment",private_sector:"Private Sector",humanitarian_coordination_information_management:"Humanitarian - Coordination & Info Management",humanitarian_multiple_clusters:"Humanitarian - Multiple Clusters",humanitarian_camp_management_and_coordination:"Humanitarian - Camp Management & Coordination",humanitarian_early_recovery:"Humanitarian - Early Recovery",humanitarian_education:"Humanitarian - Education",humanitarian_emergency_shelter:"Humanitarian - Emergency Shelter",humanitarian_emergency_telecoms:"Humanitarian - Emergency Telecoms",humanitarian_food_security:"Humanitarian - Food Security",humanitarian_health:"Humanitarian - Health",humanitarian_logistics:"Humanitarian - Logistics",humanitarian_nutrition:"Humanitarian - Nutrition",humanitarian_protection:"Humanitarian - Protection",humanitarian_sanitation_water_and_hygiene:"Humanitarian - Sanitation / Water / Hygiene",other:"Other"}},33873:e=>{"use strict";e.exports=require("path")},38038:(e,a,t)=>{"use strict";t.d(a,{A:()=>r});let r=(0,t(62688).A)("shield-check",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},40480:(e,a,t)=>{"use strict";t.d(a,{H:()=>r});let r=(e,a)=>{let t=Object.entries(a).find(([a,t])=>t===e);return t?t[0]:null}},54306:(e,a,t)=>{"use strict";t.r(a),t.d(a,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var r=t(65239),s=t(48088),i=t(88170),n=t.n(i),o=t(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(a,l);let c={children:["",{children:["[locale]",{children:["(auth)",{children:["signup",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,93091)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(auth)\\signup\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,72121)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,58014)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(auth)\\signup\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/[locale]/(auth)/signup/page",pathname:"/[locale]/signup",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57800:(e,a,t)=>{"use strict";t.d(a,{A:()=>r});let r=(0,t(62688).A)("briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},62163:(e,a,t)=>{"use strict";t.d(a,{i:()=>r});let r={non_profit_organization:"Non-profit Organization",government_institution:"Government Institution",educational_organization:"Educational Organization",a_commercial_or_for_profit_company:"Commercial / For-profit Company",i_am_not_associated_with_any_organization:"Not Associated with any Organization"}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65199:(e,a,t)=>{Promise.resolve().then(t.bind(t,93091))},68292:(e,a,t)=>{"use strict";t.d(a,{l:()=>n});var r=t(60687),s=t(78272),i=t(43210);let n=({id:e,options:a,value:t,onChange:n})=>{let[o,l]=(0,i.useState)(!1),c=(0,i.useRef)(null),d=(0,i.useRef)([]),u=(0,i.useRef)(null);(0,i.useEffect)(()=>{let e=e=>{u.current&&!u.current.contains(e.target)&&l(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]);let m=e=>{if(!o)return;let t=e.key.toLowerCase();if(t.match(/[a-z]/)){let e=a.findIndex(e=>e.toLowerCase().startsWith(t));-1!==e&&d.current[e]&&d.current[e]?.scrollIntoView({behavior:"auto",block:"nearest"})}};return(0,i.useEffect)(()=>(document.addEventListener("keydown",m),()=>{document.removeEventListener("keydown",m)}),[o,a]),(0,r.jsxs)("div",{className:"relative",ref:u,children:[(0,r.jsxs)("button",{id:e,type:"button",className:"px-4 py-2 flex items-center justify-between rounded-md border border-neutral-400 focus:border-primary-500 duration-300 w-full text-left cursor-pointer",onClick:()=>{l(!o)},children:[(0,r.jsx)("span",{children:t||"Select an option"}),(0,r.jsx)(s.A,{})]}),o&&(0,r.jsx)("ul",{className:"absolute z-10 max-h-[180px] overflow-auto border border-neutral-400 rounded-md bg-neutral-100 w-full mt-1 flex flex-col",ref:c,children:a.map((e,a)=>(0,r.jsx)("li",{ref:e=>{d.current[a]=e},className:"cursor-pointer bg-neutral-100 hover:bg-neutral-200 px-4 py-2",onClick:()=>{n(e),l(!1)},children:e},a))})]})}},74075:e=>{"use strict";e.exports=require("zlib")},76509:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>A});var r=t(60687),s=t(63442),i=t(38038),n=t(12597),o=t(13861),l=t(11437),c=t(57800),d=t(85814),u=t.n(d),m=t(16189),p=t(43210),h=t(27605),x=t(45880),g=t(1510),b=t(54864),y=t(19150),f=t(68292),v=t(15566),j=t(32833),w=t(62163),N=t(40480),_=t(12810),z=t(77618),S=t(64668);let C=e=>x.z.object({name:x.z.string().min(1,e("fullNameRequired")),email:x.z.string().min(1,e("emailRequired")).email(e("invalidEmail")),password:x.z.string().min(1,e("passwordRequired")).min(8,e("passwordMin")).max(32,e("passwordMax")).regex(/[A-Z]/,e("passwordUppercase")).regex(/[a-z]/,e("passwordLowercase")).regex(/[0-9]/,e("passwordNumber")).regex(/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/,e("passwordSpecial")),confirmPassword:x.z.string().min(1,e("confirmPasswordRequired")),country:x.z.string().min(1,e("selectCountry")),sector:x.z.string().min(1,e("selectSector")),organizationType:x.z.string().min(1,e("selectOrgType"))}).refine(e=>e.password===e.confirmPassword,{message:e("passwordsDoNotMatch"),path:["confirmPassword"]}),A=()=>{let e=(0,z.c3)(),a=C(e),{register:t,formState:{errors:d,isSubmitting:x,isSubmitted:A},setValue:k,handleSubmit:P,setError:M,watch:E}=(0,h.mN)({resolver:(0,s.u)(a)}),q=E("password"),T=E("confirmPassword");(0,p.useEffect)(()=>{t("country",{required:e("selectCountry")}),t("sector",{required:e("selectSector")}),t("organizationType",{required:e("selectOrgType")})},[t]);let[H,G]=(0,p.useState)(""),[L,O]=(0,p.useState)(""),[B,I]=(0,p.useState)(""),[R,D]=(0,p.useState)(!1),[F,K]=(0,p.useState)(!1);(0,p.useEffect)(()=>{k("country",H,{shouldValidate:A}),k("sector",L,{shouldValidate:A}),k("organizationType",B,{shouldValidate:A})},[H,L,B,k]);let U=(0,m.useRouter)(),V=(0,b.wA)(),$=async a=>{try{await _.A.post("/users/signup",a),U.push("/"),V((0,y.Ds)({message:e("signupSuccess"),type:"success"}))}catch(a){a instanceof g.pe?M(a.response?.data.errorField,{message:a.response?.data.message}):V((0,y.Ds)({message:e("signupError"),type:"error"}))}};return(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"flex flex-col gap-8 section w-11/12 mobile:w-4/5 tablet:w-2xl my-8 tablet:my-16",children:[(0,r.jsxs)("div",{className:"flex flex-col items-center gap-2",children:[(0,r.jsx)(i.A,{size:36}),(0,r.jsx)("h1",{className:"text-2xl tablet:text-3xl font-semibold text-center",children:e("createAccount")}),(0,r.jsx)("p",{className:"text-neutral-700 text-center",children:e("getStarted2")})]}),(0,r.jsxs)("form",{className:"flex flex-col gap-4",onSubmit:P($),children:[(0,r.jsxs)("div",{className:"group label-input-group",children:[(0,r.jsx)("label",{htmlFor:"name",className:"label-text",children:e("fullName")}),(0,r.jsx)("input",{...t("name"),id:"name",type:"text",placeholder:e("enterFullName"),className:"input-field"}),d.name&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:`${d.name.message}`})]}),(0,r.jsxs)("div",{className:"group label-input-group",children:[(0,r.jsx)("label",{htmlFor:"email",className:"label-text",children:e("email")}),(0,r.jsx)("input",{...t("email"),id:"email",type:"email",placeholder:e("enterEmail"),className:"input-field"}),d.email&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:`${d.email.message}`})]}),(0,r.jsxs)("div",{className:"group label-input-group",children:[(0,r.jsx)("label",{htmlFor:"password",className:"label-text",children:e("password")}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{...t("password"),id:"password",type:R?"text":"password",placeholder:e("enterPassword"),className:"input-field w-full pr-10"}),q&&q.length>0&&(0,r.jsxs)("button",{type:"button",tabIndex:-1,className:"absolute right-2 top-1/2 -translate-y-1/2 text-gray-500",onClick:()=>D(!R),children:[R?(0,r.jsx)(n.A,{className:"h-4 w-4"}):(0,r.jsx)(o.A,{className:"h-4 w-4"}),(0,r.jsxs)("span",{className:"sr-only",children:[R?"Hide":"Show"," password"]})]})]}),d.password&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:`${d.password.message}`})]}),(0,r.jsxs)("div",{className:"group label-input-group",children:[(0,r.jsx)("label",{htmlFor:"confirm-password",className:"label-text",children:e("confirmPassword")}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{...t("confirmPassword"),id:"confirm-password",type:F?"text":"password",placeholder:e("confirm_password_required"),className:"input-field w-full pr-10"}),T&&T.length>0&&(0,r.jsxs)("button",{type:"button",tabIndex:-1,className:"absolute right-2 top-1/2 -translate-y-1/2 text-gray-500",onClick:()=>K(!F),children:[F?(0,r.jsx)(n.A,{className:"h-4 w-4"}):(0,r.jsx)(o.A,{className:"h-4 w-4"}),(0,r.jsxs)("span",{className:"sr-only",children:[F?"Hide":"Show"," password"]})]})]}),d.confirmPassword&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:`${d.confirmPassword.message}`})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 tablet:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsxs)("label",{htmlFor:"country",className:"label-text",children:[(0,r.jsx)(l.A,{size:16})," ",e("country")]}),(0,r.jsx)(f.l,{id:"country",options:v,value:H||e("selectOption"),onChange:G}),d.country&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:`${d.country.message}`})]}),(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsxs)("label",{htmlFor:"sector",className:"label-text",children:[(0,r.jsx)(c.A,{size:16})," ",e("sector")]}),(0,r.jsx)(f.l,{id:"sector",options:Object.values(j.b),value:L&&j.b[L]?j.b[L]:e("selectOption"),onChange:e=>{O((0,N.H)(e,j.b)??"")}}),d.sector&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:`${d.sector.message}`})]}),(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsxs)("label",{htmlFor:"organizationType",className:"label-text",children:[(0,r.jsx)(c.A,{size:16})," ",e("organizationType")]}),(0,r.jsx)(f.l,{id:"organizationType",options:Object.values(w.i),value:B&&w.i[B]?w.i[B]:e("selectOption"),onChange:e=>{I((0,N.H)(e,w.i)??"")}}),d.organizationType&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:`${d.organizationType.message}`})]})]}),(0,r.jsx)("button",{type:"submit",className:"btn-primary",disabled:x,children:x?(0,r.jsxs)("span",{className:"flex items-center gap-2",children:[e("signingUp"),(0,r.jsx)("div",{className:"size-4 rounded-full border-x-2 animate-spin"})]}):e("signUp")})]}),(0,r.jsxs)("div",{className:"text-neutral-700 flex items-center gap-2",children:[(0,r.jsx)("span",{children:e("alreadyHaveAccount")}),(0,r.jsx)(u(),{href:"/",className:"font-medium hover:text-neutral-900 duration-300",children:e("signIn")})]}),(0,r.jsx)("div",{children:(0,r.jsx)(S.A,{})})]})})}},78272:(e,a,t)=>{"use strict";t.d(a,{A:()=>r});let r=(0,t(62688).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},93091:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Kobo-project\\\\data-analysis-tool-frontend\\\\app\\\\[locale]\\\\(auth)\\\\signup\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(auth)\\signup\\page.tsx","default")},94735:e=>{"use strict";e.exports=require("events")}};var a=require("../../../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),r=a.X(0,[4447,7404,1658,6560,7618,63,7605,4921,6226],()=>t(54306));module.exports=r})();