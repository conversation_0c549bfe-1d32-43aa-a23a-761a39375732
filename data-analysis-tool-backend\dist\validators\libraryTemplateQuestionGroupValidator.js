"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateSubGroupOrderAndAddQuestionSchema = exports.updateLibraryTemplateQuestionGroupSchema = exports.LibraryTemplateQuestionGroupSchema = void 0;
// validators/questionGroupValidator.ts
const zod_1 = require("zod");
// Define the QuestionGroup validation schema
exports.LibraryTemplateQuestionGroupSchema = zod_1.z.object({
    title: zod_1.z.string().min(1, "Title is required"), // Ensure the title is a non-empty string
    order: zod_1.z.number().int().min(1, "Order must be a positive integer"), // Ensure order is a positive integer
    parentGroupId: zod_1.z.number().int().optional(), // Optional parent group ID, if any
    libraryTemplateId: zod_1.z
        .number()
        .int()
        .min(1, "Project ID is required and must be a positive integer"), // Project ID validation
    selectedQuestionIds: zod_1.z
        .array(zod_1.z
        .number()
        .int()
        .positive("Selected question IDs must be positive integers"))
        .nonempty("At least one question must be selected"),
    createdAt: zod_1.z.date().optional(), // Optional createdAt field
    updatedAt: zod_1.z.date().optional(), // Optional updatedAt field
    subGroups: zod_1.z
        .array(
    // Validate subGroups (if present)
    zod_1.z.object({
        id: zod_1.z.number().int(), // Assuming each subGroup has an ID
        title: zod_1.z.string().min(1, "SubGroup title is required"), // Title for subGroups
        order: zod_1.z
            .number()
            .int()
            .min(1, "Order for subGroup must be a positive integer"), // Order for subGroups
    }))
        .optional(), // SubGroups are optional
});
// For updating a QuestionGroup (without requiring title)
exports.updateLibraryTemplateQuestionGroupSchema = zod_1.z.object({
    id: zod_1.z.number().int().min(1, "ID is required"),
    title: zod_1.z.string().min(1, "Title is required"),
    order: zod_1.z.number().int().min(1, "Order must be a positive integer"),
    parentGroupId: zod_1.z.number().int().optional(),
    selectedQuestionIds: zod_1.z
        .array(zod_1.z
        .number()
        .int()
        .positive("Selected question IDs must be positive integers"))
        .optional(), // optional because maybe you just update title/order
});
exports.updateSubGroupOrderAndAddQuestionSchema = zod_1.z.object({
    subGroups: zod_1.z
        .array(zod_1.z.object({
        id: zod_1.z.number().int().min(1, "SubGroup ID is required"),
        order: zod_1.z.number().int().min(1, "Order must be a positive integer"),
    }))
        .nonempty("subGroups cannot be empty"),
    newQuestions: zod_1.z
        .array(zod_1.z.object({
        subGroupId: zod_1.z.number().int().min(1, "SubGroup ID is required"),
        title: zod_1.z.string().min(1, "Question title is required"),
        order: zod_1.z.number().int().min(1, "Order must be a positive integer"),
        libraryTemplateId: zod_1.z.number().int().min(1, "Project ID is required"),
    }))
        .optional(),
});
