"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const libraryQuestionBlockQuestionGroupController_1 = require("../controllers/libraryQuestionBlockQuestionGroupController");
const router = express_1.default.Router();
router.post("/", libraryQuestionBlockQuestionGroupController_1.createLibraryQuestionBlockQuestionGroup);
router.patch("/", libraryQuestionBlockQuestionGroupController_1.updateLibraryQuestionBlockQuestionGroup);
router.delete("/:id", libraryQuestionBlockQuestionGroupController_1.deleteLibraryQuestionBlockQuestionGroup);
router.delete("/group/question/:id", libraryQuestionBlockQuestionGroupController_1.deleteLibraryQuestionBlockQuestionAndGroup);
router.patch("/question/remove", libraryQuestionBlockQuestionGroupController_1.removeLibraryQuestionBlockQuestionIdFromGroup);
router.patch("/question/move", libraryQuestionBlockQuestionGroupController_1.updateLibraryQuestionBlockQuestionFromOneGroupToAnother);
router.patch("/group/add", libraryQuestionBlockQuestionGroupController_1.updateLibraryQuestionBlockOneGroupInsideAnotherGroup);
router.patch("/group/remove", libraryQuestionBlockQuestionGroupController_1.removeLibraryQuestionBlockGroupFromParentGroup);
exports.default = router;
