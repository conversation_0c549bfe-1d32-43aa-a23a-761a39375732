"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const projectController_1 = require("../controllers/projectController");
const auth_1 = require("../middleware/auth");
const router = express_1.default.Router();
router.post("/", auth_1.authenticate, projectController_1.createProject);
router.get("/", auth_1.authenticate, projectController_1.getAllProject);
router.patch("/update-many-status", auth_1.authenticate, projectController_1.updateManyProjectStatus);
router.delete("/delete-multiple", auth_1.authenticate, projectController_1.DeleteMultipleProject);
router.patch("/:id", auth_1.authenticate, projectController_1.updateProjects);
router.patch("/change-status/:id", auth_1.authenticate, projectController_1.changeProjectStatus);
router.get("/:id", auth_1.authenticate, projectController_1.getProjectById);
router.get("/form/:id", auth_1.authenticate, projectController_1.fetchQuestionForForm);
router.delete("/delete/:id", auth_1.authenticate, projectController_1.deleteProject);
router.get("/getalldata/:id", projectController_1.fetchQuestionForForms);
// Create a project from a library template
router.post("/from-template", auth_1.authenticate, projectController_1.createProjectFromLibraryTemplate);
exports.default = router;
