const CHUNK_PUBLIC_PATH = "server/app/[locale]/(main)/dashboard/[status]/not-available/page.js";
const runtime = require("../../../../../../chunks/ssr/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_c336f25b._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__4c58c055._.js");
runtime.loadChunk("server/chunks/ssr/app_1f3630ef._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__d019762b._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_96715ba7._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_client_components_forbidden-error_ea7ea172.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_client_components_unauthorized-error_c8949b27.js");
runtime.loadChunk("server/chunks/ssr/messages_af0f449a._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_65a5dceb._.js");
runtime.loadChunk("server/chunks/ssr/_e73d4873._.js");
runtime.loadChunk("server/chunks/ssr/app_[locale]_(main)_layout_tsx_03500cec._.js");
runtime.loadChunk("server/chunks/ssr/_8a8b8117._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/[locale]/(main)/dashboard/[status]/not-available/page/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-page.js?page=/[locale]/(main)/dashboard/[status]/not-available/page { METADATA_0 => \"[project]/app/favicon.ico.mjs { IMAGE => \\\"[project]/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js server component)\", MODULE_1 => \"[project]/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_2 => \"[project]/node_modules/next/dist/client/components/not-found-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_3 => \"[project]/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_4 => \"[project]/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_5 => \"[project]/app/[locale]/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_6 => \"[project]/app/[locale]/(main)/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_7 => \"[project]/app/[locale]/(main)/dashboard/[status]/not-available/page.tsx [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-page.js?page=/[locale]/(main)/dashboard/[status]/not-available/page { METADATA_0 => \"[project]/app/favicon.ico.mjs { IMAGE => \\\"[project]/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js server component)\", MODULE_1 => \"[project]/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_2 => \"[project]/node_modules/next/dist/client/components/not-found-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_3 => \"[project]/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_4 => \"[project]/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_5 => \"[project]/app/[locale]/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_6 => \"[project]/app/[locale]/(main)/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_7 => \"[project]/app/[locale]/(main)/dashboard/[status]/not-available/page.tsx [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
