"use client";

import React, { useState } from "react";
import { Question, QuestionGroup } from "@/types/formBuilder";
import { QuestionItem } from "./QuestionItem";
import { ChevronDown, ChevronRight, Edit, Trash, Plus, GripVertical, FolderPlus } from "lucide-react";
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
  useDraggable,
  useDroppable,
} from "@dnd-kit/core";
import {
  SortableContext,
  verticalListSortingStrategy,
  arrayMove,
  useSortable,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";

import { useTranslations } from "next-intl";

interface QuestionGroupItemProps {
  id: number;
  title: string;
  questions: Question[];
  subGroups?: QuestionGroup[];
  parentGroupId?: number;
  nestingLevel?: number;
  onEditGroup: (groupId: number) => void;
  onDeleteGroup: (groupId: number) => void;
  onAddQuestionToGroup: (groupId: number) => void;
  onEditQuestion: (question: Question) => void;
  onDeleteQuestion: (question: Question) => void;
  onDuplicateQuestion: (question: Question) => void;
  onReorderQuestions?: (questionPositions: { id: number; position: number }[]) => void;
  onMoveQuestionBetweenGroups?: (questionId: number, fromGroupId: number | null, toGroupId: number | null) => void;
  onMoveGroupInsideGroup?: (childGroupId: number, parentGroupId: number | null) => void;
  isEditing?: boolean;
  onStartEditing?: (groupId: number, currentName: string) => void;
  onSaveGroupName?: (groupId: number) => void;
  onCancelEditing?: () => void;
  editingName?: string;
  onEditingNameChange?: (name: string) => void;
  selectionMode?: boolean;
  isDraggable?: boolean;
  selectedQuestionIds?: number[];
  onToggleQuestionSelect?: (questionId: number) => void;
  onCreateSubgroup?: (parentGroupId: number, selectedQuestionIds: number[]) => void;
}

const QuestionGroupItem = ({
  id,
  title,
  questions,
  subGroups = [],
  parentGroupId,
  nestingLevel = 0,
  onEditGroup, // Kept for future use
  onDeleteGroup,
  onAddQuestionToGroup,
  onEditQuestion,
  onDeleteQuestion,
  onDuplicateQuestion,
  onReorderQuestions,
  onMoveQuestionBetweenGroups,
  onMoveGroupInsideGroup,
  isEditing = false,
  onStartEditing,
  onSaveGroupName,
  onCancelEditing,
  editingName = "",
  onEditingNameChange,
  selectionMode = false,
  isDraggable = true,
  selectedQuestionIds = [],
  onToggleQuestionSelect,
  onCreateSubgroup,
}: QuestionGroupItemProps) => {
  const [isExpanded, setIsExpanded] = useState(true);

  // Get selected questions within this group
  const selectedQuestionsInGroup = questions.filter(q =>
    selectedQuestionIds.includes(q.id)
  );

  // Handle creating subgroup from selected questions
  const handleCreateSubgroup = () => {
    if (selectedQuestionsInGroup.length > 0 && onCreateSubgroup) {
      onCreateSubgroup(id, selectedQuestionsInGroup.map(q => q.id));
    }
  };

  // Make the group draggable
  const {
    attributes,
    listeners,
    setNodeRef: setDragRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: `group-${id}`,
    data: {
      type: 'group',
      groupId: id,
      parentGroupId,
    }
  });

  // Make the group a drop zone
  const { setNodeRef: setDropRef, isOver } = useDroppable({
    id: `group-drop-${id}`,
    data: {
      type: 'group-drop',
      groupId: id,
    }
  });

  const dragStyle = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  const t = useTranslations();

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor)
  );

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (!over || active.id === over.id) {
      return;
    }

    const activeData = active.data.current;
    const overData = over.data.current;

    // Handle question reordering within the same group
    if (activeData?.type === 'question' && overData?.type === 'question' && onReorderQuestions) {
      // Sort questions by position to maintain order
      const sortedQuestions = [...questions].sort((a, b) => a.position - b.position);

      const oldIndex = sortedQuestions.findIndex((q) => q.id === active.id);
      const newIndex = sortedQuestions.findIndex((q) => q.id === over.id);

      if (oldIndex === -1 || newIndex === -1) {
        return;
      }

      // Reorder the questions array
      const reorderedQuestions = arrayMove(sortedQuestions, oldIndex, newIndex);

      // Calculate new positions for all questions in this group
      const questionPositions = reorderedQuestions.map((question, index) => ({
        id: Number(question.id), // Ensure it's a number
        position: index + 1, // Start positions from 1
      }));

      // Call the parent handler to update positions
      onReorderQuestions(questionPositions);
    }

    // Handle moving questions between groups
    if (activeData?.type === 'question' && overData?.type === 'group-drop' && onMoveQuestionBetweenGroups) {
      const questionId = Number(active.id);
      const fromGroupId = activeData.questionGroupId || null;
      const toGroupId = overData.groupId;

      if (fromGroupId !== toGroupId) {
        onMoveQuestionBetweenGroups(questionId, fromGroupId, toGroupId);
      }
    }

    // Handle moving groups inside other groups
    if (activeData?.type === 'group' && overData?.type === 'group-drop' && onMoveGroupInsideGroup) {
      const childGroupId = activeData.groupId;
      const parentGroupId = overData.groupId;

      // Prevent dropping a group into itself or its descendants
      if (childGroupId !== parentGroupId) {
        onMoveGroupInsideGroup(childGroupId, parentGroupId);
      }
    }
  };

  return (
    <div
      ref={(node) => {
        setDragRef(node);
        setDropRef(node);
      }}
      style={dragStyle}
      className={`border border-neutral-400 rounded-md bg-card shadow-sm mb-4 ${
        isOver ? 'ring-2 ring-primary-500 ring-opacity-50' : ''
      } ${nestingLevel > 0 ? `ml-8 border-l-4 border-l-primary-300` : ''}`}
    >
      {/* Group Header */}
      <div className="flex items-center p-4 bg-neutral-100 border-b border-neutral-300 rounded-t-md">
        {isDraggable && (
          <div
            {...attributes}
            {...listeners}
            className="cursor-move mr-2 hover:text-primary-500 transition-colors"
            title="Drag to reorder group"
          >
            <GripVertical className="h-5 w-5" />
          </div>
        )}

        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="mr-2 text-neutral-700 hover:text-primary-500 transition-colors"
          aria-label={isExpanded ? t("collapseGroup") : t("expandGroup")}
        >
          {isExpanded ? (
            <ChevronDown className="h-5 w-5" />
          ) : (
            <ChevronRight className="h-5 w-5" />
          )}
        </button>

        {isEditing ? (
          <div className="flex-1 mr-4">
            <input
              type="text"
              value={editingName}
              onChange={(e) =>
                onEditingNameChange && onEditingNameChange(e.target.value)
              }
              className="w-full p-2 border border-gray-300 rounded"
              autoFocus
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  onSaveGroupName && onSaveGroupName(id);
                } else if (e.key === "Escape") {
                  onCancelEditing && onCancelEditing();
                }
              }}
              placeholder={t("enterGroupName")}
            />
          </div>
        ) : (
          <h3
            className="flex-1 font-medium text-lg cursor-pointer hover:text-primary-500"
            onClick={() => onStartEditing && onStartEditing(id, title)}
            title={t("clickToEditGroupName")}
          >
            {title}
          </h3>
        )}

        <div className="flex items-center space-x-3">
          {isEditing ? (
            <div className="flex space-x-2">
              <button
                onClick={() => onCancelEditing && onCancelEditing()}
                title={t("cancelEditing")}
                className="cursor-pointer px-3 py-1 rounded btn-outline"
              >
                {t("cancel")}
              </button>
              <button
                onClick={() => onSaveGroupName && onSaveGroupName(id)}
                title={t("saveGroupName")}
                className="cursor-pointer px-3 py-1 rounded btn-primary"
              >
                {t("save")}
              </button>
            </div>
          ) : (
            <>
              {/* Create Subgroup button - show when questions are selected in this group */}
              {selectionMode && selectedQuestionsInGroup.length > 0 && (
                <button
                  onClick={handleCreateSubgroup}
                  title={`Create Subgroup (${selectedQuestionsInGroup.length} questions)`}
                  className="cursor-pointer px-3 py-1 rounded btn-primary text-sm flex items-center gap-1"
                >
                  <FolderPlus size={14} />
                  Create Subgroup ({selectedQuestionsInGroup.length})
                </button>
              )}

              <button
                onClick={() => onAddQuestionToGroup(id)}
                title={t("addQuestionToGroup")}
                className="cursor-pointer p-2 rounded-full hover:bg-primary-100 text-neutral-700 hover:text-primary-500 transition-colors"
              >
                <Plus size={16} />
              </button>
              <button
                onClick={() => onStartEditing && onStartEditing(id, title)}
                title={t("editGroupName")}
                className="cursor-pointer p-2 rounded-full hover:bg-primary-100 text-neutral-700 hover:text-primary-500 transition-colors"
              >
                <Edit size={16} />
              </button>
              <button
                onClick={() => onDeleteGroup(id)}
                title={t("deleteGroup")}
                className="cursor-pointer p-2 rounded-full hover:bg-destructive/10 text-neutral-700 hover:text-destructive transition-colors"
              >
                <Trash size={16} />
              </button>
            </>
          )}
        </div>
      </div>

      {/* Group Content */}
      {isExpanded && (
        <div className="p-4 space-y-4">
          {/* Nested Groups */}
          {subGroups && subGroups.length > 0 && (
            <div className="space-y-4">
              {subGroups
                .sort((a, b) => a.order - b.order)
                .map((subGroup) => (
                  <QuestionGroupItem
                    key={subGroup.id}
                    id={subGroup.id}
                    title={subGroup.title}
                    questions={subGroup.question || []}
                    subGroups={subGroup.subGroups}
                    parentGroupId={id}
                    nestingLevel={nestingLevel + 1}
                    onEditGroup={onEditGroup}
                    onDeleteGroup={onDeleteGroup}
                    onAddQuestionToGroup={onAddQuestionToGroup}
                    onEditQuestion={onEditQuestion}
                    onDeleteQuestion={onDeleteQuestion}
                    onDuplicateQuestion={onDuplicateQuestion}
                    onReorderQuestions={onReorderQuestions}
                    onMoveQuestionBetweenGroups={onMoveQuestionBetweenGroups}
                    onMoveGroupInsideGroup={onMoveGroupInsideGroup}
                    selectionMode={selectionMode}
                    isDraggable={isDraggable}
                    selectedQuestionIds={selectedQuestionIds}
                    onToggleQuestionSelect={onToggleQuestionSelect}
                    onCreateSubgroup={onCreateSubgroup}
                  />
                ))}
            </div>
          )}

          {/* Questions in this group */}
          {questions.length > 0 ? (
            <DndContext
              sensors={sensors}
              collisionDetection={closestCenter}
              onDragEnd={handleDragEnd}
            >
              <SortableContext
                items={questions.map((question) => question.id)}
                strategy={verticalListSortingStrategy}
              >
                {questions
                  .sort((a, b) => a.position - b.position)
                  .map((question) => (
                    <div key={question.id} className="mb-4">
                      <QuestionItem
                        question={question}
                        onEdit={() => onEditQuestion(question)}
                        onDelete={() => onDeleteQuestion(question)}
                        onDuplicate={() => onDuplicateQuestion(question)}
                        selectionMode={selectionMode}
                        isSelected={selectedQuestionIds.includes(question.id)}
                        onToggleSelect={() => onToggleQuestionSelect && onToggleQuestionSelect(question.id)}
                      />
                    </div>
                  ))}
              </SortableContext>
            </DndContext>
          ) : (
            (!subGroups || subGroups.length === 0) && (
              <div className="text-center py-4 text-neutral-500">
 {t("noQuestionsInGroup")}              </div>
            )
          )}
        </div>
      )}
    </div>
  );
};

export { QuestionGroupItem };