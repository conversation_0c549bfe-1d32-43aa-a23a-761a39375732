(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7893],{7872:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>E});var s=r(95155),a=r(12115),n=r(19373),l=r(5041),i=r(35695),o=r(88570),u=r(34947),d=r(10150),c=r(77361),p=r(57799),m=r(82714),h=r(99474),y=r(95139),g=r(55747),f=r(92138),x=r(34540),v=r(71402),b=r(16112),j=r(3587),w=r(13388),N=r(80423),A=r(52707),q=r(29350);function E(){let e=(0,x.wA)(),t=(0,i.useRouter)(),{hashedId:r}=(0,i.useParams)(),E=(0,o.D)(r),{status:k,isAuthenticated:S,isLoading:O}=(0,q.A)();(0,a.useEffect)(()=>{O||S||t.push("/form-submission/".concat(r,"/sign-in"))},[O,S,t,r]);let[I,T]=(0,a.useState)({}),[F,C]=(0,a.useState)({}),[R,L]=(0,a.useState)(!1),[D,_]=(0,a.useState)([]),[Q,P]=(0,a.useState)([]),[U,J]=(0,a.useState)({}),{data:G,isLoading:K,isError:M}=(0,n.I)({queryKey:["questions",E],queryFn:()=>(0,u.K4)({projectId:E}),enabled:!!E}),{data:Y=[]}=(0,n.I)({queryKey:["questionGroups",E],queryFn:()=>(0,d.pr)({projectId:E}),enabled:!!E}),{data:B}=(0,n.I)({queryKey:["project",E],queryFn:()=>(0,c.kf)({projectId:E}),enabled:!!E});(0,a.useEffect)(()=>{if(G){let e={};G.forEach(t=>{e[t.id]="selectmany"===t.inputType?[]:""}),T(e)}},[G]),(0,a.useEffect)(()=>{if(G){let e=(0,j.UL)(G,I);_(e),P((0,j.Tr)(G,I));let t=(0,j.OD)(I,e);Object.keys(t).length!==Object.keys(I).length&&T(t)}},[G,I]);let V=(0,a.useMemo)(()=>(0,A.yi)(Y,G||[]),[Y,G]);(0,a.useEffect)(()=>{V.length>0&&J((0,A.cZ)(V,!0))},[V.length]);let W=(0,a.useMemo)(()=>(0,A.ru)(G||[]),[G]),X=(0,a.useMemo)(()=>(0,A.XV)(V,W),[V,W]),Z=(0,a.useCallback)(e=>{J(t=>({...t,[e]:!t[e]}))},[]),z=(0,l.n)({mutationFn:async e=>{let t=(null==G?void 0:G.map(t=>{let r,s,a,n=e[t.id],l="selectmany"===t.inputType,i="selectone"===t.inputType;if(!l&&!i&&(null==n||""===n)||i&&(!n||""===n.trim()))return null;if(l&&Array.isArray(n)&&t.questionOptions){let e=n.map(e=>{let r=t.questionOptions.find(t=>t.label===e);return null==r?void 0:r.id}).filter(e=>void 0!==e);r=e.length>0?e:[]}else if(i&&n&&t.questionOptions){let e=t.questionOptions.find(e=>e.label===n);if(void 0===(r=null==e?void 0:e.id))return console.warn("Could not find option ID for selectone question ".concat(t.id,' with value "').concat(n,'"')),null}if(null==(s=l?Array.isArray(n)?n.join(", "):"":"number"===t.inputType||"decimal"===t.inputType?n?Number(n):void 0:"date"===t.inputType||"dateandtime"===t.inputType?n||void 0:"table"===t.inputType?Array.isArray(n)&&n.length>0?JSON.stringify(n):void 0:n?String(n):void 0))return null;a=l?Array.isArray(r)?r:[]:i&&"number"==typeof r?r:void 0;let o={projectId:Number(E),questionId:t.id,answerType:String(t.inputType),value:s,isOtherOption:!1};return void 0!==a&&(o.questionOptionId=a),o}).filter(e=>null!==e))||[];if(0===t.length)throw Error("No valid answers to submit. Please fill out at least one field.");return await (0,c.lj)(t)},onSuccess:()=>{e((0,v.Ds)({message:"Form submitted successfully",type:"success"})),T({}),window.dispatchEvent(new Event("form-submitted")),localStorage.setItem("form_submitted",Date.now().toString())},onError:t=>{e((0,v.Ds)({message:"Failed to submit form. Please try again.",type:"error"})),console.error("Submission Error:",t)},onSettled:()=>{L(!1)}});(0,a.useEffect)(()=>{if(G){let e=(0,j.UL)(G,I);_(e),P((0,j.Tr)(G,I));let t=(0,j.OD)(I,e);Object.keys(t).length!==Object.keys(I).length&&T(t)}},[G,I]);let H=(0,a.useCallback)((e,t)=>{T(r=>({...r,[e]:t})),C(t=>({...t,[e]:""}))},[]),$=()=>{let e={};return D.forEach(t=>{if(t.isRequired){let r=I[t.id];("string"==typeof r&&!r.trim()||Array.isArray(r)&&0===r.length||null==r)&&(e[t.id]="".concat(t.label," is required"))}}),C(e),0===Object.keys(e).length},ee=async e=>{e.preventDefault(),$()&&(L(!0),z.mutate(I))},et=e=>!!G&&G.some(t=>{var r;return null==(r=t.questionOptions)?void 0:r.some(t=>t.nextQuestionId===e)}),er=e=>{var t;return(null==(t=e.questionOptions)?void 0:t.some(e=>e.nextQuestionId))||!1},es=e=>{var t,r,a,n;let l=null!=(t=I[e.id])?t:"selectmany"===e.inputType?[]:"";switch(e.inputType){case"text":if(null==(r=e.hint)?void 0:r.includes("multiline"))return(0,s.jsx)(h.T,{value:l,onChange:t=>H(e.id,t.target.value),placeholder:e.placeholder||"Your answer",required:e.isRequired});return(0,s.jsx)("input",{className:"input-field w-full",value:l,onChange:t=>H(e.id,t.target.value),placeholder:e.placeholder||"Your answer",required:e.isRequired});case"number":return(0,s.jsx)("input",{className:"input-field w-full",type:"number",value:l,onChange:t=>H(e.id,t.target.value),placeholder:e.placeholder||"Your answer",required:e.isRequired});case"decimal":return(0,s.jsx)("input",{className:"input-field w-full",type:"number",step:"any",value:l,onChange:t=>H(e.id,t.target.value),placeholder:e.placeholder||"Your answer",required:e.isRequired});case"selectone":return(0,s.jsx)(g.z,{value:l,onValueChange:t=>H(e.id,t),required:e.isRequired,children:(0,s.jsx)("div",{className:"space-y-2",children:null==(a=e.questionOptions)?void 0:a.map((e,t)=>(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(g.C,{value:e.label,id:"option-".concat(e.id)}),(0,s.jsx)(m.J,{htmlFor:"option-".concat(e.id),className:"cursor-pointer",children:e.label}),e.sublabel&&(0,s.jsx)("p",{className:"text-sm text-neutral-700 ml-4",children:"(".concat(e.sublabel,")")})]},t))})});case"selectmany":return(0,s.jsx)("div",{className:"space-y-2",children:null==(n=e.questionOptions)?void 0:n.map(t=>(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(y.S,{id:"option-".concat(t.id),checked:(l||[]).includes(t.label),onCheckedChange:r=>{let s=l||[],a=r?[...s,t.label]:s.filter(e=>e!==t.label);H(e.id,a)}}),(0,s.jsx)(m.J,{htmlFor:"option-".concat(t.id),className:"cursor-pointer",children:t.label})]},t.id))});case"date":return(0,s.jsx)("div",{className:"relative",children:(0,s.jsx)("input",{className:"input-field w-full",type:"date",value:l,onChange:t=>H(e.id,t.target.value),placeholder:e.placeholder||"Select date",required:e.isRequired})});case"dateandtime":return(0,s.jsx)("div",{className:"relative",children:(0,s.jsx)("input",{className:"input-field w-full",type:"time",value:l,onChange:t=>H(e.id,t.target.value),placeholder:e.placeholder||"Select time",required:e.isRequired})});case"table":return(0,s.jsx)(b.N,{questionId:e.id,value:l,onChange:t=>H(e.id,t),required:e.isRequired,tableLabel:e.label});default:return null}},ea=e=>{let t=et(e.id),r=er(e);return(0,s.jsxs)("div",{className:"border rounded-md p-4 ".concat(t?"border-primary-200 dark:border-primary-700 bg-primary-100 dark:bg-primary-900/20":"border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800"),children:[(0,s.jsxs)("div",{className:"mb-2",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsxs)(m.J,{className:"text-base font-medium",children:[e.label,e.isRequired&&(0,s.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),t&&(0,s.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-200 text-primary-800 dark:bg-primary-900 dark:text-primary-200",children:[(0,s.jsx)(f.A,{className:"w-3 h-3 mr-1"}),"Follow-up"]}),r&&(0,s.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-accent-200 text-accent-800 dark:bg-accent-700/20 dark:text-accent-200",children:"Has conditions"})]}),e.hint&&(0,s.jsx)("p",{className:"text-sm mt-1 ".concat(t?"text-primary-700 dark:text-primary-300":"text-muted-foreground"),children:e.hint}),F[e.id]&&(0,s.jsx)("p",{className:"text-sm text-red-500 mt-1",children:F[e.id]})]}),(0,s.jsx)("div",{className:"mt-2",children:es(e)})]},e.id)};return O||K?(0,s.jsx)(p.A,{}):S?M||!G?(0,s.jsx)("p",{className:"text-sm text-red-500",children:"Error loading form. Please try again."}):(0,s.jsx)("div",{className:"min-h-screen w-full bg-neutral-100 dark:bg-gray-900 flex flex-col items-center p-4 md:p-6",children:(0,s.jsxs)("div",{className:"w-full max-w-screen-lg bg-neutral-100 dark:bg-gray-800 rounded-md shadow-sm border border-gray-200 dark:border-gray-700",children:[(0,s.jsxs)("h2",{className:"text-2xl font-semibold p-6 border-b border-gray-200 dark:border-gray-700",children:["Form Submission ",(null==B?void 0:B.name)?" for ".concat(B.name):""]}),(0,s.jsx)("form",{onSubmit:ee,className:"p-6",children:(0,s.jsxs)("div",{className:"space-y-6",children:[G&&0!==G.length?X.map(e=>{if("group"===e.type){let t=e.data,r=U[t.id];return(0,s.jsx)(N.A,{group:t,nestingLevel:0,visibleQuestions:D,nestedQuestions:Q,renderQuestionInput:es,errors:F,onToggleExpansion:Z,isExpanded:r,expandedGroups:U,className:""},"group-".concat(t.id))}{let t=e.data;if(!D.some(e=>e.id===t.id))return null;let r=Q.find(e=>e.question.id===t.id);return r?(0,s.jsx)(w.A,{questionGroup:r,renderQuestionInput:es,errors:F,className:""},t.id):ea(t)}}):(0,s.jsx)("div",{className:"text-center py-12",children:(0,s.jsx)("p",{className:"text-muted-foreground",children:"This form has no questions yet."})}),G.length>0&&(0,s.jsx)("div",{className:"mt-6 flex justify-end",children:(0,s.jsx)("button",{className:"btn-primary",type:"submit",disabled:R,children:R?"Submitting...":"Submit Form"})}),0===G.length&&(0,s.jsx)("div",{className:"text-center py-12",children:(0,s.jsx)("p",{className:"text-muted-foreground",children:"This form has no questions yet."})}),G&&G.length>0&&0===D.length&&(0,s.jsx)("div",{className:"text-center py-12",children:(0,s.jsx)("p",{className:"text-muted-foreground",children:"No questions are currently visible. Please check your form configuration."})})]})})]})}):null}},29350:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var s=r(97381),a=r(59362),n=r(25784),l=r(35695),i=r(12115),o=r(34540);let u=e=>{let t=(0,o.wA)(),r=(0,l.useRouter)(),u=(0,l.usePathname)(),{status:d,user:c,error:p}=(0,o.d4)(e=>e.auth),m=async()=>{try{t((0,s.Le)());let e=(await n.A.get("/users/me")).data;t((0,s.tQ)(e))}catch(n){if(t((0,s.x9)()),(0,a.F0)(n)){var e,l,i,o,d;if(console.error("Auth error:",null==(e=n.response)?void 0:e.status,null==(l=n.response)?void 0:l.data),(null==(i=n.response)?void 0:i.status)===401){if(u.startsWith("/form-submission"))return;r.push("/")}else t((0,s.jB)((null==(d=n.response)||null==(o=d.data)?void 0:o.message)||n.message))}else t((0,s.jB)(n instanceof Error?n.message:"An unknown error occurred."))}};return(0,i.useEffect)(()=>{(null==e?void 0:e.skipFetchUser)||m()},[null==e?void 0:e.skipFetchUser]),(0,i.useEffect)(()=>{let e=e=>{if("logout"===e.key&&"true"===e.newValue)if(t((0,s.x9)()),u.startsWith("/form-submission")){let e=u.split("/")[2];e?r.push("/form-submission/".concat(e,"/sign-in")):r.push("/")}else r.push("/")};return window.addEventListener("storage",e),()=>{window.removeEventListener("storage",e)}},[t,r,u]),{status:d,user:c,error:p,isAuthenticated:"authenticated"===d,isLoading:"loading"===d,refreshAuthState:()=>{m()},signin:async(e,t,r)=>{try{await n.A.post("/users/login",e),await m(),null==t||t()}catch(e){if(e instanceof a.pe){var s,l;let t=null==(l=e.response)||null==(s=l.data)?void 0:s.errorType;null==r||r(t)}else null==r||r()}},logout:async()=>{try{await n.A.post("/users/logout"),localStorage.setItem("logout","true"),setTimeout(()=>localStorage.removeItem("logout"),100)}finally{if(t((0,s.x9)()),u.startsWith("/form-submission")){let e=u.split("/")[2];e?r.push("/form-submission/".concat(e,"/sign-in")):r.push("/")}else r.push("/")}}}}},59362:(e,t,r)=>{"use strict";r.d(t,{F0:()=>c,pe:()=>a});let{Axios:s,AxiosError:a,CanceledError:n,isCancel:l,CancelToken:i,VERSION:o,all:u,Cancel:d,isAxiosError:c,spread:p,toFormData:m,AxiosHeaders:h,HttpStatusCode:y,formToJSON:g,getAdapter:f,mergeConfig:x}=r(23464).A},77361:(e,t,r)=>{"use strict";r.d(t,{D_:()=>c,Im:()=>u,Oo:()=>p,c3:()=>n,kf:()=>a,lj:()=>h,or:()=>o,pf:()=>d,vj:()=>l,wI:()=>m,xx:()=>i});var s=r(25784);let a=async e=>{let{projectId:t}=e,{data:r}=await s.A.get("/projects/".concat(t));return r.project},n=async e=>{let{data:t}=await s.A.post("/projects/from-template",e);return t},l=async()=>{try{let{data:e}=await s.A.get("/projects");return e.projects}catch(e){throw console.error("Error fetching projects:",e),e}},i=async e=>{let{data:t}=await s.A.delete("/projects/delete/".concat(e));return t},o=async e=>{try{let{data:t}=await s.A.delete("/projects/delete-multiple",{data:{projectIds:e}});return t}catch(e){throw console.error("Error deleting multiple projects:",e),e}},u=async e=>{try{let{data:t}=await s.A.patch("/projects/change-status/".concat(e),{status:"archived"});return t}catch(e){throw console.error("Error archiving project:",e),e}},d=async function(e){arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{let{data:t}=await s.A.patch("/projects/change-status/".concat(e),{status:"deployed"});return t}catch(e){throw console.error("Error deploying project:",e),e}},c=async e=>{try{let{data:t}=await s.A.patch("/projects/update-many-status",{projectIds:e,status:"archived"});return t}catch(e){throw console.error("Error archiving multiple projects:",e),e}},p=async e=>{try{let{data:t}=await s.A.post("/users/check-email",{email:e});return t}catch(e){var t,r,a,n,l,i;throw Error("object"==typeof(null==(r=e.response)||null==(t=r.data)?void 0:t.message)?JSON.stringify(null==(n=e.response)||null==(a=n.data)?void 0:a.message):(null==(i=e.response)||null==(l=i.data)?void 0:l.message)||e.message||"Failed to check user")}},m=async e=>{let{projectId:t,email:r,permissions:a}=e;try{let e=await p(r);if(!e||!e.success)throw Error((null==e?void 0:e.message)||"User not found");let{data:n}=await s.A.post("/project-users",{userId:e.user.id,projectId:t,permission:a});return n}catch(e){var n,l,i,o,u,d;throw console.error("Error adding user to project:",e),Error("object"==typeof(null==(l=e.response)||null==(n=l.data)?void 0:n.message)?JSON.stringify(null==(o=e.response)||null==(i=o.data)?void 0:i.message):(null==(d=e.response)||null==(u=d.data)?void 0:u.message)||e.message||"Failed to add user")}},h=async e=>{try{let{data:t}=await s.A.post("/answers/multiple",e);return t}catch(e){throw console.error("Error creating answer submission:",e),e}}},88570:(e,t,r)=>{"use strict";r.d(t,{D:()=>i,l:()=>l});var s=r(41050);let a=r(49509).env.SALT||"rushan-salt",n=new s.A(a,12),l=e=>n.encode(e),i=e=>{let t=n.decode(e)[0];return"bigint"==typeof t?t<Number.MAX_SAFE_INTEGER?Number(t):null:"number"==typeof t?t:null}},89559:(e,t,r)=>{Promise.resolve().then(r.bind(r,7872))},97381:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>o,Le:()=>l,jB:()=>i,tQ:()=>a,x9:()=>n});let s=(0,r(51990).Z0)({name:"auth",initialState:{status:"loading",user:null,error:null},reducers:{setAuthenticatedUser:(e,t)=>{e.status="authenticated",e.user=t.payload,e.error=null},setUnauthenticated:e=>{e.status="unauthenticated",e.user=null,e.error=null},setAuthLoading:e=>{e.status="loading"},setAuthError:(e,t)=>{e.status="unauthenticated",e.error=t.payload,e.user=null}}}),{setAuthenticatedUser:a,setUnauthenticated:n,setAuthLoading:l,setAuthError:i}=s.actions,o=s.reducer}},e=>{var t=t=>e(e.s=t);e.O(0,[635,1111,6967,9373,4277,556,3481,7823,5645,8441,1684,7358],()=>t(89559)),_N_E=e.O()}]);