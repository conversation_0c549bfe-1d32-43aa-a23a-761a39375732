(()=>{var e={};e.id=5720,e.ids=[5720],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6082:(e,t,r)=>{Promise.resolve().then(r.bind(r,91283))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20507:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Kobo-project\\\\data-analysis-tool-frontend\\\\app\\\\[locale]\\\\(main)\\\\library\\\\question-block\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\library\\question-block\\layout.tsx","default")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32877:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var s=r(60687),o=r(48661),i=r(43210),a=r(29494),n=r(75531),l=r(86429),d=r(88678),c=r(21650);let u={viewForm:!0,editForm:!0,viewSubmissions:!0,addSubmissions:!0,deleteSubmissions:!0,editSubmissions:!0,manageProject:!0,validateSubmissions:!0};function p(){let[e,t]=(0,i.useState)(!1),{user:r}=(0,c.A)(),p=r?.id,{data:m,isLoading:b,isError:x}=(0,a.I)({queryKey:["questionBlockQuestions",p],queryFn:()=>(0,n.dI)(),enabled:!!p,retry:1});if(!p)return(0,s.jsx)("div",{className:"p-6 text-center",children:(0,s.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto",children:[(0,s.jsx)("h1",{className:"text-xl font-semibold text-red-600 mb-2",children:"Authentication Required"}),(0,s.jsx)("p",{className:"text-neutral-700 mb-4",children:"You need to be logged in to access the question block form builder."}),(0,s.jsx)("a",{href:"/",className:"inline-block px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark",children:"Go to Login"})]})});if(b||!m)return(0,s.jsx)(l.A,{});if(x)return(0,s.jsxs)("div",{className:"p-4",children:[(0,s.jsx)("h2",{className:"text-lg font-semibold text-red-500",children:"Error loading question block information"}),(0,s.jsx)("p",{className:"text-sm text-red-500 mt-2",children:"There was a problem fetching the questions. Please try refreshing the page."}),(0,s.jsx)("p",{className:"text-sm text-gray-500 mt-2",children:"If the problem persists, please check the browser console for more details."})]});let h=Array.isArray(m)?m:[];return(0,s.jsx)("div",{className:"p-6",children:e?(0,s.jsx)(o.V,{questions:h,questionGroups:[],contextType:"questionBlock",onClose:()=>t(!1)}):(0,s.jsx)(d.o,{setIsPreviewMode:t,contextType:"questionBlock",contextId:p,permissions:u})})}},33873:e=>{"use strict";e.exports=require("path")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},59647:(e,t,r)=>{Promise.resolve().then(r.bind(r,84579))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65914:(e,t,r)=>{Promise.resolve().then(r.bind(r,20507))},71680:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=r(65239),o=r(48088),i=r(88170),a=r.n(i),n=r(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let d={children:["",{children:["[locale]",{children:["(main)",{children:["library",{children:["question-block",{children:["form-builder",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,84579)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\library\\question-block\\form-builder\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,20507)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\library\\question-block\\layout.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,84606)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,72121)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\library\\question-block\\form-builder\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/[locale]/(main)/library/question-block/form-builder/page",pathname:"/[locale]/library/question-block/form-builder",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84579:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Kobo-project\\\\data-analysis-tool-frontend\\\\app\\\\[locale]\\\\(main)\\\\library\\\\question-block\\\\form-builder\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\library\\question-block\\form-builder\\page.tsx","default")},91283:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var s=r(60687),o=r(17090),i=r(20174);let a=()=>{let e=[{label:"Form Builder",icon:(0,s.jsx)(o.A,{size:16}),route:"/library/question-block/form-builder"}];return(0,s.jsx)(i.F,{items:e})};var n=r(85814),l=r.n(n);r(43210);var d=r(28559),c=r(77618);let u=({children:e})=>{let t=(0,c.c3)();return(0,s.jsxs)("div",{className:"section flex flex-col gap-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("h1",{className:"heading-text capitalize",children:t("questionBlock")}),(0,s.jsxs)(l(),{href:"/library",className:"flex items-center gap-2",children:[(0,s.jsx)(d.A,{size:16}),t("backToLibrary")]})]}),(0,s.jsx)(a,{}),(0,s.jsx)("div",{className:"px-8",children:e})]})}},94735:e=>{"use strict";e.exports=require("events")},96599:(e,t,r)=>{Promise.resolve().then(r.bind(r,32877))}};var t=require("../../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,7404,1658,6560,8610,5374,2198,5814,3851,8581,4921,6886,8159,5841,5041,4072,695],()=>r(71680));module.exports=s})();