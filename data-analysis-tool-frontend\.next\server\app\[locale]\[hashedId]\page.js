(()=>{var e={};e.id=8334,e.ids=[8334],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6986:(e,t,r)=>{"use strict";r.d(t,{D:()=>n,l:()=>o});var s=r(53907);let a=process.env.SALT||"rushan-salt",i=new s.A(a,12),o=e=>i.encode(e),n=e=>{let t=i.decode(e)[0];return"bigint"==typeof t?t<Number.MAX_SAFE_INTEGER?Number(t):null:"number"==typeof t?t:null}},10125:(e,t,r)=>{"use strict";r.d(t,{Notification:()=>p});var s=r(60687),a=r(43210),i=r(54864),o=r(88920),n=r(57101),l=r(19150),d=r(14719),c=r(43649),u=r(93613);let p=()=>{let e=(0,i.wA)(),{message:t,type:r,visible:p}=(0,i.d4)(e=>e.notification);(0,a.useEffect)(()=>{if(p){let t=setTimeout(()=>{e((0,l._b)())},5e3);return()=>clearTimeout(t)}},[p,e]);let m="success"===r?(0,s.jsx)(d.A,{}):"warning"===r?(0,s.jsx)(c.A,{}):(0,s.jsx)(u.A,{});return(0,s.jsx)(o.N,{children:p&&(0,s.jsxs)(n.P.div,{className:`z-50 fixed top-0 right-0 m-4 px-4 py-2 rounded font-semibold w-auto max-w-xs flex items-center gap-2 cursor-pointer ${"success"===r?"bg-green-500 hover:bg-green-600":"warning"===r?"bg-yellow-500 hover:bg-yellow-600":"bg-red-500 hover:bg-red-600"} transition-colors duration-300`,onClick:()=>e((0,l._b)()),initial:{opacity:0,y:-20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.3,ease:"easeIn"},children:[(0,s.jsx)("span",{className:"text-2xl",children:m}),(0,s.jsx)("span",{className:"break-words neutral-100space-normal",children:t})]})})}},10271:(e,t,r)=>{"use strict";r.d(t,{ReactQueryProvider:()=>l});var s=r(60687),a=r(43210),i=r(39091),o=r(8693),n=r(9124);let l=({children:e})=>{let[t]=(0,a.useState)(()=>new i.E({defaultOptions:{queries:{staleTime:3e5,refetchOnWindowFocus:!1}}}));return(0,s.jsxs)(o.Ht,{client:t,children:[e,(0,s.jsx)(n.E,{initialIsOpen:!1})]})}},10685:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Kobo-project\\\\data-analysis-tool-frontend\\\\app\\\\[locale]\\\\[hashedId]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\[hashedId]\\page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},12810:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let s=r(51060).A.create({baseURL:"http://localhost:4000/api",headers:{"Content-Type":"application/json"},withCredentials:!0});s.interceptors.request.use(e=>e,e=>Promise.reject(e)),s.interceptors.response.use(e=>e,e=>("ERR_NETWORK"===e.code&&console.error("Network error - Please check if the backend server is running"),Promise.reject(e)));let a=s},14952:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},15616:(e,t,r)=>{"use strict";r.d(t,{T:()=>o});var s=r(60687),a=r(43210),i=r(96241);let o=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("textarea",{className:(0,i.cn)("flex min-h-[80px] w-full rounded-md border border-gray-200 bg-neutral-100 px-3 py-2 text-sm shadow-sm placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-700 dark:bg-gray-900 dark:placeholder:text-gray-500",e),ref:r,...t}));o.displayName="Textarea"},16319:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19150:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>o,Ds:()=>a,_b:()=>i});let s=(0,r(9317).Z0)({name:"notification",initialState:{message:"",type:"",visible:!1},reducers:{showNotification:(e,t)=>{e.message=t.payload.message,e.type=t.payload.type,e.visible=!0},hideNotification:e=>{e.message="",e.type="",e.visible=!1}}}),{showNotification:a,hideNotification:i}=s.actions,o=s.reducer},19635:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>q});var s=r(60687),a=r(43210),i=r(29494),o=r(54050),n=r(16189),l=r(6986),d=r(75531),c=r(96),u=r(71845),p=r(86429),m=r(39390),h=r(15616),y=r(93437),b=r(40347),f=r(70334),x=r(78272),g=r(14952),v=r(54864),j=r(19150),w=r(13784);r(24527);var N=r(69396);function q(){let e=(0,v.wA)(),{hashedId:t}=(0,n.useParams)(),r=(0,l.D)(t),[q,k]=(0,a.useState)({}),[A,P]=(0,a.useState)({}),[I,O]=(0,a.useState)(!1),[C,E]=(0,a.useState)([]),[R,S]=(0,a.useState)([]),[T,D]=(0,a.useState)({}),{data:_,isLoading:M,isError:$}=(0,i.I)({queryKey:["questions",r],queryFn:()=>(0,d.K4)({projectId:r}),enabled:!!r}),{data:F=[]}=(0,i.I)({queryKey:["questionGroups",r],queryFn:()=>(0,c.pr)({projectId:r}),enabled:!!r}),{data:L}=(0,i.I)({queryKey:["project",r],queryFn:()=>(0,u.kf)({projectId:r}),enabled:!!r}),Q=(0,a.useMemo)(()=>F.reduce((e,t)=>(e[t.id]=_?.filter(e=>e.questionGroupId===t.id)||[],e),{}),[F,_]),K=(0,a.useMemo)(()=>_?.filter(e=>null===e.questionGroupId||void 0===e.questionGroupId)||[],[_]),U=(0,a.useMemo)(()=>{let e=[];return F.forEach(t=>{let r=_?.filter(e=>e.questionGroupId===t.id)||[],s=r.length>0?Math.min(...r.map(e=>e.position)):t.order;e.push({type:"group",data:t,order:s,originalPosition:s})}),K.forEach(t=>{e.push({type:"question",data:t,order:t.position,originalPosition:t.position})}),e.sort((e,t)=>e.order===t.order?(e.originalPosition||e.order)-(t.originalPosition||t.order):e.order-t.order)},[F,K,_]),G=(0,a.useCallback)(e=>{D(t=>({...t,[e]:!t[e]}))},[]),z=(0,o.n)({mutationFn:async e=>{let t=_?.map(t=>{let s,a,i,o=e[t.id],n="selectmany"===t.inputType,l="selectone"===t.inputType;if(!n&&!l&&(null==o||""===o)||l&&(!o||""===o.trim()))return null;if(n&&Array.isArray(o)&&t.questionOptions){let e=o.map(e=>{let r=t.questionOptions.find(t=>t.label===e);return r?.id}).filter(e=>void 0!==e);s=e.length>0?e:[]}else if(l&&o&&t.questionOptions){let e=t.questionOptions.find(e=>e.label===o);if(void 0===(s=e?.id))return console.warn(`Could not find option ID for selectone question ${t.id} with value "${o}"`),null}if(null==(a=n?Array.isArray(o)?o.join(", "):"":"number"===t.inputType||"decimal"===t.inputType?o?Number(o):void 0:"date"===t.inputType||"dateandtime"===t.inputType?o||void 0:"table"===t.inputType?Array.isArray(o)&&o.length>0?JSON.stringify(o):void 0:o?String(o):void 0))return null;i=n?Array.isArray(s)?s:[]:l&&"number"==typeof s?s:void 0;let d={projectId:Number(r),questionId:t.id,answerType:String(t.inputType),value:a,isOtherOption:!1};return void 0!==i&&(d.questionOptionId=i),d}).filter(e=>null!==e)||[];if(0===t.length)throw Error("No valid answers to submit. Please fill out at least one field.");return console.log("Submission Data:",{projectId:r,formattedAnswers:t,totalQuestions:_?.length,answeredQuestions:t.length}),console.log("All Questions Debug:",t.map(e=>({questionId:e.questionId,answerType:e.answerType,value:e.value,questionOptionId:e.questionOptionId,questionOptionIdType:Array.isArray(e.questionOptionId)?"array":typeof e.questionOptionId,questionOptionIdLength:Array.isArray(e.questionOptionId)?e.questionOptionId.length:"N/A",isOtherOption:e.isOtherOption,projectId:e.projectId}))),console.log("Backend Schema Validation Check:"),t.forEach((e,t)=>{console.log(`Answer ${t+1}:`,{hasProjectId:"number"==typeof e.projectId,hasQuestionId:"number"==typeof e.questionId,hasAnswerType:"string"==typeof e.answerType,hasValue:void 0!==e.value,hasIsOtherOption:"boolean"==typeof e.isOtherOption,questionOptionIdValidation:"selectmany"===e.answerType?Array.isArray(e.questionOptionId):"selectone"===e.answerType?void 0===e.questionOptionId||"number"==typeof e.questionOptionId:void 0===e.questionOptionId,allRequiredFieldsPresent:!!(e.projectId&&e.questionId&&e.answerType&&void 0!==e.isOtherOption)})}),await (0,u.lj)(t)},onSuccess:()=>{e((0,j.Ds)({message:"Form submitted successfully",type:"success"})),k({}),window.dispatchEvent(new Event("form-submitted")),localStorage.setItem("form_submitted",Date.now().toString())},onError:t=>{e((0,j.Ds)({message:"Failed to submit form. Please try again.",type:"error"})),console.error("Submission Error:",t)},onSettled:()=>{O(!1)}}),J=(0,a.useCallback)((e,t)=>{k(r=>({...r,[e]:t})),P(t=>({...t,[e]:""}))},[]),V=()=>{let e={};return C.forEach(t=>{if(t.isRequired){let r=q[t.id];("string"==typeof r&&!r.trim()||Array.isArray(r)&&0===r.length||null==r)&&(e[t.id]=`${t.label} is required`)}}),P(e),0===Object.keys(e).length},Z=async e=>{e.preventDefault(),V()&&(O(!0),z.mutate(q))},Y=e=>!!_&&_.some(t=>t.questionOptions?.some(t=>t.nextQuestionId===e)),B=e=>e.questionOptions?.some(e=>e.nextQuestionId)||!1,H=e=>{let t=q[e.id]??("selectmany"===e.inputType?[]:"");switch(e.inputType){case"text":if(e.hint?.includes("multiline"))return(0,s.jsx)(h.T,{value:t,onChange:t=>J(e.id,t.target.value),placeholder:e.placeholder||"Your answer",required:e.isRequired});return(0,s.jsx)("input",{className:"input-field w-full",value:t,onChange:t=>J(e.id,t.target.value),placeholder:e.placeholder||"Your answer",required:e.isRequired});case"number":return(0,s.jsx)("input",{className:"input-field w-full",type:"number",value:t,onChange:t=>J(e.id,t.target.value),placeholder:e.placeholder||"Your answer",required:e.isRequired});case"decimal":return(0,s.jsx)("input",{className:"input-field w-full",type:"number",step:"any",value:t,onChange:t=>J(e.id,t.target.value),placeholder:e.placeholder||"Your answer",required:e.isRequired});case"selectone":return(0,s.jsx)(b.z,{value:t,onValueChange:t=>J(e.id,t),required:e.isRequired,children:(0,s.jsx)("div",{className:"space-y-2",children:e.questionOptions?.map((e,t)=>(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(b.C,{value:e.label,id:`option-${e.id}`}),(0,s.jsx)(m.J,{htmlFor:`option-${e.id}`,className:"cursor-pointer",children:e.label}),e.sublabel&&(0,s.jsx)("p",{className:"text-sm text-neutral-700 ml-4",children:`(${e.sublabel})`})]},t))})});case"selectmany":return(0,s.jsx)("div",{className:"space-y-2",children:e.questionOptions?.map(r=>(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(y.S,{id:`option-${r.id}`,checked:(t||[]).includes(r.label),onCheckedChange:s=>{let a=t||[],i=s?[...a,r.label]:a.filter(e=>e!==r.label);J(e.id,i)}}),(0,s.jsx)(m.J,{htmlFor:`option-${r.id}`,className:"cursor-pointer",children:r.label})]},r.id))});case"date":return(0,s.jsx)("div",{className:"relative",children:(0,s.jsx)("input",{className:"input-field w-full",type:"date",value:t,onChange:t=>J(e.id,t.target.value),placeholder:e.placeholder||"Select date",required:e.isRequired})});case"dateandtime":return(0,s.jsx)("div",{className:"relative",children:(0,s.jsx)("input",{className:"input-field w-full",type:"time",value:t,onChange:t=>J(e.id,t.target.value),placeholder:e.placeholder||"Select time",required:e.isRequired})});case"table":return(0,s.jsx)(w.N,{questionId:e.id,value:t,onChange:t=>J(e.id,t),required:e.isRequired,tableLabel:e.label});default:return null}},X=e=>{let t=Y(e.id),r=B(e);return(0,s.jsxs)("div",{className:`border rounded-md p-4 ${t?"border-primary-200 dark:border-primary-700 bg-primary-100 dark:bg-primary-900/20":"border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800"}`,children:[(0,s.jsxs)("div",{className:"mb-2",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsxs)(m.J,{className:"text-base font-medium",children:[e.label,e.isRequired&&(0,s.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),t&&(0,s.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-200 text-primary-800 dark:bg-primary-900 dark:text-primary-200",children:[(0,s.jsx)(f.A,{className:"w-3 h-3 mr-1"}),"Follow-up"]}),r&&(0,s.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-accent-200 text-accent-800 dark:bg-accent-700/20 dark:text-accent-200",children:"Has conditions"})]}),e.hint&&(0,s.jsx)("p",{className:`text-sm mt-1 ${t?"text-primary-700 dark:text-primary-300":"text-muted-foreground"}`,children:e.hint}),A[e.id]&&(0,s.jsx)("p",{className:"text-sm text-red-500 mt-1",children:A[e.id]})]}),(0,s.jsx)("div",{className:"mt-2",children:H(e)})]},e.id)};return M?(0,s.jsx)(p.A,{}):$||!_?(0,s.jsx)("p",{className:"text-sm text-red-500",children:"Error loading form. Please try again."}):(0,s.jsx)("div",{className:"min-h-screen w-full bg-neutral-100 dark:bg-gray-900 flex flex-col items-center p-4 md:p-6",children:(0,s.jsxs)("div",{className:"w-full max-w-screen-lg bg-neutral-100 dark:bg-gray-800 rounded-md shadow-sm border border-gray-200 dark:border-gray-700",children:[(0,s.jsx)("h2",{className:"text-2xl font-semibold p-6 border-b border-gray-200 dark:border-gray-700",children:"Test Form"}),(0,s.jsx)("form",{onSubmit:Z,className:"p-6",children:(0,s.jsxs)("div",{className:"space-y-6",children:[_&&0!==_.length?U.map(e=>{if("group"===e.type){let t=e.data,r=Q[t.id]||[],a=r.filter(e=>C.some(t=>t.id===e.id)),i=T[t.id];return 0===a.length?null:(0,s.jsxs)("div",{className:"border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800",children:[(0,s.jsx)("div",{className:"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700",onClick:()=>G(t.id),children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[i?(0,s.jsx)(x.A,{className:"h-5 w-5 text-gray-500"}):(0,s.jsx)(g.A,{className:"h-5 w-5 text-gray-500"}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100",children:t.title}),(0,s.jsxs)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:["(",a.length," visible question",1!==a.length?"s":"",")"]})]})}),i&&(0,s.jsx)("div",{className:"p-4 space-y-4",children:R.filter(e=>r.some(t=>t.id===e.question.id)).map(e=>(0,s.jsx)(N.A,{questionGroup:e,renderQuestionInput:H,errors:A,className:""},e.question.id))})]},`group-${t.id}`)}{let t=e.data;if(!C.some(e=>e.id===t.id))return null;let r=R.find(e=>e.question.id===t.id);return r?(0,s.jsx)(N.A,{questionGroup:r,renderQuestionInput:H,errors:A,className:""},t.id):X(t)}}):(0,s.jsx)("div",{className:"text-center py-12",children:(0,s.jsx)("p",{className:"text-muted-foreground",children:"This form has no questions yet."})}),_.length>0&&(0,s.jsx)("div",{className:"mt-6 flex justify-end",children:(0,s.jsx)("button",{className:"btn-primary",type:"submit",disabled:I,children:I?"Submitting...":"Submit Form"})}),0===_.length&&(0,s.jsx)("div",{className:"text-center py-12",children:(0,s.jsx)("p",{className:"text-muted-foreground",children:"This form has no questions yet."})}),_&&_.length>0&&0===C.length&&(0,s.jsx)("div",{className:"text-center py-12",children:(0,s.jsx)("p",{className:"text-muted-foreground",children:"No questions are currently visible. Please check your form configuration."})})]})})]})})}},21820:e=>{"use strict";e.exports=require("os")},26946:(e,t,r)=>{Promise.resolve().then(r.bind(r,10125)),Promise.resolve().then(r.bind(r,10271)),Promise.resolve().then(r.bind(r,49271))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35790:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>o,l:()=>i,yg:()=>a});let s=(0,r(9317).Z0)({name:"createLibraryItem",initialState:{visible:!1},reducers:{showCreateLibraryModal:e=>{e.visible=!0},hideCreateLibraryModal:e=>{e.visible=!1}}}),{showCreateLibraryModal:a,hideCreateLibraryModal:i}=s.actions,o=s.reducer},36039:(e,t,r)=>{Promise.resolve().then(r.bind(r,45196))},39390:(e,t,r)=>{"use strict";r.d(t,{J:()=>n});var s=r(60687),a=r(43210),i=r(78148),o=r(96241);let n=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(i.b,{ref:r,className:(0,o.cn)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",e),...t}));n.displayName=i.b.displayName},40347:(e,t,r)=>{"use strict";r.d(t,{C:()=>d,z:()=>l});var s=r(60687),a=r(43210),i=r(14555),o=r(65822),n=r(96241);let l=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(i.bL,{className:(0,n.cn)("grid gap-2",e),...t,ref:r}));l.displayName=i.bL.displayName;let d=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(i.q7,{ref:r,className:(0,n.cn)("aspect-square h-4 w-4 rounded-full border text-gray-900 shadow focus:outline-none focus-visible:ring-1 focus-visible:ring-gray-950 disabled:cursor-not-allowed disabled:opacity-50 dark:text-gray-50 dark:focus-visible:ring-gray-300",e),...t,children:(0,s.jsx)(i.C1,{className:"flex items-center justify-center",children:(0,s.jsx)(o.A,{className:"h-2.5 w-2.5 fill-current text-current"})})}));d.displayName=i.q7.displayName},42895:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>l,Le:()=>o,jB:()=>n,tQ:()=>a,x9:()=>i});let s=(0,r(9317).Z0)({name:"auth",initialState:{status:"loading",user:null,error:null},reducers:{setAuthenticatedUser:(e,t)=>{e.status="authenticated",e.user=t.payload,e.error=null},setUnauthenticated:e=>{e.status="unauthenticated",e.user=null,e.error=null},setAuthLoading:e=>{e.status="loading"},setAuthError:(e,t)=>{e.status="unauthenticated",e.error=t.payload,e.user=null}}}),{setAuthenticatedUser:a,setUnauthenticated:i,setAuthLoading:o,setAuthError:n}=s.actions,l=s.reducer},44395:(e,t,r)=>{"use strict";r.d(t,{Notification:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call Notification() from the server but Notification is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\components\\general\\Notification.tsx","Notification")},46055:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},49271:(e,t,r)=>{"use strict";r.d(t,{ReduxProvider:()=>p});var s=r(60687),a=r(9317),i=r(19150),o=r(58432),n=r(42895),l=r(35790),d=r(89011);let c=(0,a.U1)({reducer:{notification:i.Ay,createProject:o.Ay,auth:n.Ay,createLibrary:l.Ay,createLibraryItem:d.Ay}});r(43210);var u=r(54864);let p=({children:e})=>(0,s.jsx)(u.Kq,{store:c,children:e})},50823:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},52911:(e,t,r)=>{Promise.resolve().then(r.bind(r,80994))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58014:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p,metadata:()=>u});var s=r(37413);r(82704);var a=r(7990),i=r.n(a),o=r(60866),n=r.n(o),l=r(77832),d=r(44395),c=r(60265);let u={title:"Data analysis tool",description:"A tool for data collection and analysis."};function p({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:`${i().className} ${n().className} antialiased`,children:(0,s.jsx)(l.ReduxProvider,{children:(0,s.jsxs)(c.ReactQueryProvider,{children:[(0,s.jsx)(d.Notification,{}),(0,s.jsx)("main",{className:"bg-neutral-200",children:e})]})})})})}},58432:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>o,Gl:()=>a,th:()=>i});let s=(0,r(9317).Z0)({name:"createProject",initialState:{visible:!1},reducers:{showCreateProjectModal:e=>{e.visible=!0},hideCreateProjectModal:e=>{e.visible=!1}}}),{showCreateProjectModal:a,hideCreateProjectModal:i}=s.actions,o=s.reducer},60265:(e,t,r)=>{"use strict";r.d(t,{ReactQueryProvider:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call ReactQueryProvider() from the server but ReactQueryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\providers\\ReactQueryProvider.tsx","ReactQueryProvider")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65912:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=r(65239),a=r(48088),i=r(88170),o=r.n(i),n=r(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let d={children:["",{children:["[locale]",{children:["[hashedId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,10685)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\[hashedId]\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,72121)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\[hashedId]\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/[hashedId]/page",pathname:"/[locale]/[hashedId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},68988:(e,t,r)=>{"use strict";r.d(t,{p:()=>i});var s=r(60687);r(43210);var a=r(96241);function i({className:e,type:t,...r}){return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-primary-500 focus-visible:ring-[1px]","focus-visible:outline-none","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}},70334:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},71845:(e,t,r)=>{"use strict";r.d(t,{D_:()=>u,Im:()=>d,Oo:()=>p,c3:()=>i,kf:()=>a,lj:()=>h,or:()=>l,pf:()=>c,vj:()=>o,wI:()=>m,xx:()=>n});var s=r(12810);let a=async({projectId:e})=>{let{data:t}=await s.A.get(`/projects/${e}`);return t.project},i=async e=>{let{data:t}=await s.A.post("/projects/from-template",e);return t},o=async()=>{try{let{data:e}=await s.A.get("/projects");return e.projects}catch(e){throw console.error("Error fetching projects:",e),e}},n=async e=>{let{data:t}=await s.A.delete(`/projects/delete/${e}`);return t},l=async e=>{try{let{data:t}=await s.A.delete("/projects/delete-multiple",{data:{projectIds:e}});return t}catch(e){throw console.error("Error deleting multiple projects:",e),e}},d=async e=>{try{let{data:t}=await s.A.patch(`/projects/change-status/${e}`,{status:"archived"});return t}catch(e){throw console.error("Error archiving project:",e),e}},c=async(e,t=!1)=>{try{let{data:t}=await s.A.patch(`/projects/change-status/${e}`,{status:"deployed"});return t}catch(e){throw console.error("Error deploying project:",e),e}},u=async e=>{try{let{data:t}=await s.A.patch("/projects/update-many-status",{projectIds:e,status:"archived"});return t}catch(e){throw console.error("Error archiving multiple projects:",e),e}},p=async e=>{try{let{data:t}=await s.A.post("/users/check-email",{email:e});return t}catch(e){throw Error("object"==typeof e.response?.data?.message?JSON.stringify(e.response?.data?.message):e.response?.data?.message||e.message||"Failed to check user")}},m=async({projectId:e,email:t,permissions:r})=>{try{let a=await p(t);if(!a||!a.success)throw Error(a?.message||"User not found");let{data:i}=await s.A.post("/project-users",{userId:a.user.id,projectId:e,permission:r});return i}catch(e){throw console.error("Error adding user to project:",e),Error("object"==typeof e.response?.data?.message?JSON.stringify(e.response?.data?.message):e.response?.data?.message||e.message||"Failed to add user")}},h=async e=>{try{let{data:t}=await s.A.post("/answers/multiple",e);return t}catch(e){throw console.error("Error creating answer submission:",e),e}}},72121:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,generateStaticParams:()=>l});var s=r(37413),a=r(60958),i=r(39916),o=r(81015);let n=["en","ne"];function l(){return n.map(e=>({locale:e}))}async function d({children:e,params:t}){let{locale:r}=await t;n.includes(r)||(0,i.notFound)();let l=await (0,o.V)(r);return(0,s.jsx)(a.A,{locale:r,messages:l,children:e})}},74075:e=>{"use strict";e.exports=require("zlib")},74612:(e,t,r)=>{Promise.resolve().then(r.bind(r,19635))},76565:(e,t,r)=>{var s={"./en.json":[87368,7368],"./ne.json":[3018,3018]};function a(e){if(!r.o(s,e))return Promise.resolve().then(()=>{var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t});var t=s[e],a=t[0];return r.e(t[1]).then(()=>r.t(a,19))}a.keys=()=>Object.keys(s),a.id=76565,e.exports=a},77832:(e,t,r)=>{"use strict";r.d(t,{ReduxProvider:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call ReduxProvider() from the server but ReduxProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\providers\\ReduxProvider.tsx","ReduxProvider")},79551:e=>{"use strict";e.exports=require("url")},81015:(e,t,r)=>{"use strict";r.d(t,{A:()=>o,V:()=>i});var s=r(35471);let a=["en","ne"];async function i(e){a.includes(e)||(console.warn(`Unsupported locale: ${e}, falling back to 'en'`),e="en");try{let t=(await r(76565)(`./${e}.json`)).default;if(!t||"object"!=typeof t)throw Error(`Invalid messages format for locale: ${e}`);return t}catch(t){if(console.error(`Failed to load messages for locale: ${e}`,t),"en"!==e)try{return console.log("Falling back to English messages"),(await r.e(7368).then(r.t.bind(r,87368,19))).default}catch(e){console.error("Failed to load fallback English messages",e)}return{}}}let o=(0,s.A)(async({locale:e})=>{let t=e?.toString()||"en";return{locale:t,messages:await i(t),timeZone:"Asia/Kathmandu",formats:{dateTime:{short:{day:"numeric",month:"short",year:"numeric"},medium:{day:"numeric",month:"long",year:"numeric"},long:{weekday:"long",day:"numeric",month:"long",year:"numeric"}},number:{currency:{style:"currency",currency:"NPR"}}}}})},81630:e=>{"use strict";e.exports=require("http")},82704:()=>{},83997:e=>{"use strict";e.exports=require("tty")},86429:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(60687);r(43210);let a=()=>(0,s.jsx)("div",{className:"w-full flex items-center justify-center",children:(0,s.jsx)("div",{className:"size-8 rounded-full border-x-2 border-primary-500 animate-spin my-16"})})},86778:(e,t,r)=>{Promise.resolve().then(r.bind(r,44395)),Promise.resolve().then(r.bind(r,60265)),Promise.resolve().then(r.bind(r,77832))},89011:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>o,dQ:()=>a,g7:()=>i});let s=(0,r(9317).Z0)({initialState:{visible:!1,option:""},name:"createLibraryItem",reducers:{showCreateLibraryItemModal:(e,t)=>{e.visible=!0,e.option=t.payload},hideCreateLibraryItemModal:e=>{e.visible=!1,e.option=""}}}),{showCreateLibraryItemModal:a,hideCreateLibraryItemModal:i}=s.actions,o=s.reducer},93437:(e,t,r)=>{"use strict";r.d(t,{S:()=>n});var s=r(60687);r(43210);var a=r(40211),i=r(13964),o=r(96241);function n({className:e,...t}){return(0,s.jsx)(a.bL,{"data-slot":"checkbox",className:(0,o.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:(0,s.jsx)(a.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,s.jsx)(i.A,{className:"size-3.5"})})})}},94735:e=>{"use strict";e.exports=require("events")},96241:(e,t,r)=>{"use strict";r.d(t,{Y:()=>o,cn:()=>i});var s=r(49384),a=r(82348);function i(...e){return(0,a.QP)((0,s.$)(e))}function o(e,t="short"){if(!e)return"";try{let r="string"==typeof e?new Date(e):e;if(isNaN(r.getTime()))return"";switch(t){case"short":return r.toLocaleDateString(void 0,{year:"numeric",month:"short",day:"numeric"});case"long":return r.toLocaleDateString(void 0,{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"});case"full":return r.toLocaleDateString(void 0,{year:"numeric",month:"long",day:"numeric",weekday:"long",hour:"2-digit",minute:"2-digit",second:"2-digit"});default:return r.toLocaleDateString()}}catch(t){return console.error("Error formatting date:",t),String(e)}}},96752:(e,t,r)=>{"use strict";r.d(t,{A0:()=>o,BF:()=>n,Hj:()=>l,XI:()=>i,nA:()=>c,nd:()=>d});var s=r(60687);r(43210);var a=r(96241);function i({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,s.jsx)("table",{"data-slot":"table",className:(0,a.cn)("w-full caption-bottom text-sm",e),...t})})}function o({className:e,...t}){return(0,s.jsx)("thead",{"data-slot":"table-header",className:(0,a.cn)("[&_tr]:border-b",e),...t})}function n({className:e,...t}){return(0,s.jsx)("tbody",{"data-slot":"table-body",className:(0,a.cn)("[&_tr:last-child]:border-0",e),...t})}function l({className:e,...t}){return(0,s.jsx)("tr",{"data-slot":"table-row",className:(0,a.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...t})}function d({className:e,...t}){return(0,s.jsx)("th",{"data-slot":"table-head",className:(0,a.cn)("text-foreground h-10 px-2 text-left align-middle font-medium neutral-100space-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}function c({className:e,...t}){return(0,s.jsx)("td",{"data-slot":"table-cell",className:(0,a.cn)("p-2 align-middle neutral-100space-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}},98580:(e,t,r)=>{Promise.resolve().then(r.bind(r,10685))}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,7404,1658,6560,8610,5374,3851,4072],()=>r(65912));module.exports=s})();