"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const prisma_1 = require("../utils/prisma");
class ProjectUserRepository {
    create(userId, projectId, permission) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.projectUser.create({
                data: {
                    userId,
                    projectId,
                    permission,
                },
            });
        });
    }
    deleteUserFromProject(userId, projectId) {
        return __awaiter(this, void 0, void 0, function* () {
            const projectUser = yield prisma_1.prisma.projectUser.findFirst({
                where: {
                    userId: userId,
                    projectId: projectId,
                },
            });
            if (projectUser) {
                return yield prisma_1.prisma.projectUser.delete({
                    where: {
                        id: projectUser.id, // Use 'id' for deletion
                    },
                });
            }
            throw new Error("project not found");
        });
    }
    updateUserPermission(userId, projectId, permission) {
        return __awaiter(this, void 0, void 0, function* () {
            const existing = yield prisma_1.prisma.projectUser.findFirst({
                where: { userId, projectId },
            });
            if (!existing)
                throw new Error("ProjectUser not found");
            return yield prisma_1.prisma.projectUser.update({
                where: { id: existing.id },
                data: { permission },
            });
        });
    }
    findUserProject(userId, projectId) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.projectUser.findFirst({
                where: {
                    userId,
                    projectId,
                },
            });
        });
    }
    allUsers(projectId) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.projectUser.findMany({
                where: {
                    projectId,
                },
                include: {
                    user: {
                        select: {
                            id: true,
                            name: true,
                            email: true
                        }
                    }
                }
            });
        });
    }
}
exports.default = new ProjectUserRepository();
