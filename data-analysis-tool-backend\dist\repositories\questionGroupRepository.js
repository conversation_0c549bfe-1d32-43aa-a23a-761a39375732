"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const prisma_1 = require("../utils/prisma");
class QuestionGroupRepository {
    create(data) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.$transaction((tx) => __awaiter(this, void 0, void 0, function* () {
                // Step 1: Create QuestionGroup
                const newGroup = yield tx.questionGroup.create({
                    data: {
                        title: data.title,
                        order: data.order,
                        projectId: data.projectId,
                        parentGroupId: data.parentGroupId,
                    },
                });
                // Step 2: Get max group-level position (root-level only)
                const lastGroupOrder = yield tx.projectQuestionOrder.findFirst({
                    where: {
                        parentGroupId: null,
                        type: "group",
                    },
                    orderBy: { position: "desc" },
                });
                const groupPosition = lastGroupOrder ? lastGroupOrder.position + 1 : 1;
                // Step 3: Create ProjectQuestionOrder for the group (root-level)
                yield tx.projectQuestionOrder.create({
                    data: {
                        groupId: newGroup.id,
                        parentGroupId: null,
                        type: "group",
                        position: groupPosition,
                        projectId: data.projectId,
                    },
                });
                // Step 4: For each selected question, set its position **within the group**
                if (data.selectedQuestionIds && data.selectedQuestionIds.length > 0) {
                    yield tx.question.updateMany({
                        where: {
                            id: { in: data.selectedQuestionIds },
                        },
                        data: {
                            questionGroupId: newGroup.id,
                        },
                    });
                    // Get current max position inside this group
                    const lastQuestionOrder = yield tx.projectQuestionOrder.findFirst({
                        where: {
                            parentGroupId: newGroup.id,
                            type: "question",
                        },
                        orderBy: { position: "desc" },
                    });
                    let questionPosition = lastQuestionOrder
                        ? lastQuestionOrder.position
                        : 0;
                    for (const questionId of data.selectedQuestionIds) {
                        questionPosition += 1;
                        // Check if this question already has a ProjectQuestionOrder
                        const existingOrder = yield tx.projectQuestionOrder.findFirst({
                            where: {
                                questionId,
                                type: "question",
                            },
                        });
                        if (existingOrder) {
                            // Update it to assign it to the new group
                            yield tx.projectQuestionOrder.update({
                                where: { id: existingOrder.id },
                                data: {
                                    groupId: newGroup.id,
                                    parentGroupId: newGroup.id,
                                    position: questionPosition,
                                    projectId: data.projectId,
                                },
                            });
                        }
                        else {
                            // Create new entry if none exists
                            yield tx.projectQuestionOrder.create({
                                data: {
                                    questionId,
                                    groupId: newGroup.id,
                                    parentGroupId: newGroup.id,
                                    type: "question",
                                    position: questionPosition,
                                    projectId: data.projectId,
                                },
                            });
                        }
                    }
                }
                return newGroup;
            }));
        });
    }
    delete(id) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.questionGroup.delete({
                where: { id },
            });
        });
    }
    deleteManyQuestionByGroup(id) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.question.deleteMany({
                where: {
                    questionGroupId: id,
                },
            });
        });
    }
    findById(id) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.questionGroup.findUnique({
                where: { id },
                include: {
                    question: {
                        orderBy: { position: "asc" },
                    },
                },
            });
        });
    }
    findAllByProject(projectId) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.questionGroup.findMany({
                where: { projectId },
                select: {
                    id: true,
                    title: true,
                    order: true,
                    parentGroupId: true,
                    projectId: true,
                    createdAt: true,
                    updatedAt: true,
                    question: true,
                },
                orderBy: {
                    order: "asc",
                },
            });
        });
    }
    update(id, updates) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.questionGroup.update({
                where: { id },
                data: updates,
            });
        });
    }
    updateGroupInsideParentGroup(childGroupId, ParentGroupId) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.questionGroup.update({
                where: {
                    id: childGroupId,
                },
                data: {
                    parentGroupId: ParentGroupId,
                },
            });
        });
    }
    RemoveGroupFromParentGroup(groupId) {
        return __awaiter(this, void 0, void 0, function* () {
            return prisma_1.prisma.questionGroup.update({
                where: {
                    id: groupId,
                },
                data: {
                    parentGroupId: null,
                },
            });
        });
    }
    updateMultiplePositions(groupPositions) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.$transaction((tx) => __awaiter(this, void 0, void 0, function* () {
                const updatedGroups = [];
                for (const { id, order, parentGroupId } of groupPositions) {
                    const updatedGroup = yield tx.questionGroup.update({
                        where: { id },
                        data: Object.assign({ order }, (parentGroupId !== undefined && { parentGroupId })),
                    });
                    updatedGroups.push(updatedGroup);
                }
                return updatedGroups;
            }));
        });
    }
}
exports.default = new QuestionGroupRepository();
