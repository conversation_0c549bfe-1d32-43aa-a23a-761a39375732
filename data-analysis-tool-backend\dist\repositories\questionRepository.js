"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const prisma_1 = require("../utils/prisma");
class QuestionRepository {
    findById(id) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.question.findUnique({
                where: {
                    id,
                },
                include: {
                    questionOptions: true,
                    questionConditions: true,
                },
            });
        });
    }
    isPorjectOwner(userId, projectId) {
        return __awaiter(this, void 0, void 0, function* () {
            const project = yield prisma_1.prisma.project.findUnique({
                where: {
                    id: projectId,
                },
                select: { userId: true },
            });
            return !!project && project.userId === userId;
        });
    }
    findAll(projectId) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.question.findMany({
                where: projectId ? { projectId } : undefined,
                include: {
                    questionOptions: true,
                    questionConditions: true,
                },
                orderBy: { position: "asc" },
            });
        });
    }
    create(questionData) {
        return __awaiter(this, void 0, void 0, function* () {
            const { projectId, label, inputType, hint, placeholder, isRequired, position, options, } = questionData;
            return yield prisma_1.prisma.question.create({
                data: {
                    projectId,
                    label,
                    inputType,
                    hint: hint !== null && hint !== void 0 ? hint : "", // fallback to empty string
                    placeholder: placeholder !== null && placeholder !== void 0 ? placeholder : "",
                    isRequired,
                    position,
                    questionOptions: options
                        ? {
                            create: options.map((opt) => ({ label: opt, code: opt })),
                        }
                        : undefined,
                },
                include: {
                    questionOptions: true,
                },
            });
        });
    }
    // async updateById(
    //   id: number,
    //   updateData: {
    //     label?: string;
    //     inputType?: InputType;
    //     hint?: string;
    //     placeholder?: string;
    //     isRequired?: boolean;
    //     position?: number;
    //   }
    // ): Promise<Partial<Question> | null> {
    //   const data: {
    //     label?: string;
    //     inputType?: InputType;
    //     hint?: string;
    //     placeholder?: string;
    //     isRequired?: boolean;
    //     position?: number;
    //   } = {};
    //   if (updateData.label !== undefined) data.label = updateData.label;
    //   if (updateData.inputType !== undefined)
    //     data.inputType = updateData.inputType;
    //   if (updateData.hint !== undefined) data.hint = updateData.hint;
    //   if (updateData.placeholder !== undefined)
    //     data.placeholder = updateData.placeholder;
    //   if (updateData.isRequired !== undefined)
    //     data.isRequired = updateData.isRequired;
    //   if (updateData.position !== undefined) data.position = updateData.position;
    //   return await prisma.question.update({
    //     where: { id },
    //     data,
    //   });
    // }
    updateById(id, updateData) {
        return __awaiter(this, void 0, void 0, function* () {
            const data = {};
            if (updateData.label !== undefined)
                data.label = updateData.label;
            if (updateData.inputType !== undefined)
                data.inputType = updateData.inputType;
            if (updateData.hint !== undefined)
                data.hint = updateData.hint;
            if (updateData.placeholder !== undefined)
                data.placeholder = updateData.placeholder;
            if (updateData.isRequired !== undefined)
                data.isRequired = updateData.isRequired;
            if (updateData.position !== undefined)
                data.position = updateData.position;
            // Handle options update in a transaction
            return yield prisma_1.prisma.$transaction((tx) => __awaiter(this, void 0, void 0, function* () {
                // Update the question
                const updatedQuestion = yield tx.question.update({
                    where: { id },
                    data,
                });
                // If options are provided, update them
                if (updateData.options !== undefined) {
                    // Delete existing options
                    yield tx.questionOption.deleteMany({
                        where: { questionId: id },
                    });
                    // Create new options if any provided
                    if (updateData.options.length > 0) {
                        yield tx.questionOption.createMany({
                            data: updateData.options.map((option, index) => ({
                                questionId: id,
                                label: option.label,
                                code: option.code,
                                sublabel: option.sublabel,
                                nextQuestionId: option.nextQuestionId,
                                // position: index + 1, // Add position if your schema requires it
                            })),
                        });
                    }
                }
                // Return the updated question with options
                return yield tx.question.findUnique({
                    where: { id },
                    include: {
                        questionOptions: true,
                    },
                });
            }));
        });
    }
    deleteQuestion(id) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.question.delete({
                where: { id },
            });
        });
    }
    duplicateQuestion(id, projectId) {
        return __awaiter(this, void 0, void 0, function* () {
            // Find the question to duplicate
            const question = yield prisma_1.prisma.question.findUnique({
                where: { id },
            });
            if (!question) {
                return null;
            }
            const duplicatedQuestion = yield prisma_1.prisma.question.create({
                data: Object.assign(Object.assign({}, question), { id: undefined, // Reset the ID to create a new question
                    projectId }),
                include: {
                    questionOptions: true,
                    questionConditions: true,
                },
            });
            return duplicatedQuestion;
        });
    }
    questionoptions() {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.questionOption.findMany({});
        });
    }
    createQuestion(data) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a, _b, _c;
            return yield prisma_1.prisma.question.create({
                data: {
                    projectId: data.projectId,
                    label: data.label,
                    inputType: data.inputType,
                    hint: (_a = data.hint) !== null && _a !== void 0 ? _a : "",
                    placeholder: (_b = data.placeholder) !== null && _b !== void 0 ? _b : "",
                    isRequired: (_c = data.isRequired) !== null && _c !== void 0 ? _c : false,
                    position: data.position,
                },
            });
        });
    }
    createQuestionOption(data) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.questionOption.create({
                data: {
                    questionId: data.questionId,
                    label: data.label,
                    code: data.code,
                    nextQuestionId: data.nextQuestionId,
                },
            });
        });
    }
    updateMultiplePositions(questionPositions) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.$transaction((tx) => __awaiter(this, void 0, void 0, function* () {
                const updatedQuestions = [];
                for (const { id, position } of questionPositions) {
                    const updatedQuestion = yield tx.question.update({
                        where: { id },
                        data: { position },
                        include: {
                            questionOptions: true,
                            questionConditions: true,
                        },
                    });
                    updatedQuestions.push(updatedQuestion);
                }
                return updatedQuestions;
            }));
        });
    }
    // NEW UNIFIED POSITION SYSTEM: Update positions using ProjectQuestionOrder schema
    updateUnifiedPositions(projectId, positionUpdates) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.$transaction((tx) => __awaiter(this, void 0, void 0, function* () {
                const updatedQuestions = [];
                const updatedGroups = [];
                for (const update of positionUpdates) {
                    if (update.type === 'question') {
                        // Update or create ProjectQuestionOrder for question
                        const existingOrder = yield tx.projectQuestionOrder.findFirst({
                            where: {
                                questionId: update.id,
                                projectId,
                                type: 'question',
                            },
                        });
                        if (existingOrder) {
                            // Update existing ProjectQuestionOrder
                            yield tx.projectQuestionOrder.update({
                                where: { id: existingOrder.id },
                                data: {
                                    position: update.position,
                                    parentGroupId: update.parentGroupId,
                                    groupId: update.groupId,
                                },
                            });
                        }
                        else {
                            // Create new ProjectQuestionOrder
                            yield tx.projectQuestionOrder.create({
                                data: {
                                    questionId: update.id,
                                    projectId,
                                    type: 'question',
                                    position: update.position,
                                    parentGroupId: update.parentGroupId,
                                    groupId: update.groupId,
                                },
                            });
                        }
                        // Also update the legacy question.position field for backward compatibility
                        const updatedQuestion = yield tx.question.update({
                            where: { id: update.id },
                            data: { position: update.position },
                            include: {
                                questionOptions: true,
                                questionConditions: true,
                            },
                        });
                        updatedQuestions.push(updatedQuestion);
                    }
                    else if (update.type === 'group') {
                        // Update or create ProjectQuestionOrder for group
                        const existingOrder = yield tx.projectQuestionOrder.findFirst({
                            where: {
                                groupId: update.id,
                                projectId,
                                type: 'group',
                            },
                        });
                        if (existingOrder) {
                            // Update existing ProjectQuestionOrder
                            yield tx.projectQuestionOrder.update({
                                where: { id: existingOrder.id },
                                data: {
                                    position: update.position,
                                    parentGroupId: update.parentGroupId,
                                },
                            });
                        }
                        else {
                            // Create new ProjectQuestionOrder
                            yield tx.projectQuestionOrder.create({
                                data: {
                                    groupId: update.id,
                                    projectId,
                                    type: 'group',
                                    position: update.position,
                                    parentGroupId: update.parentGroupId,
                                },
                            });
                        }
                        // Also update the legacy questionGroup.order field for backward compatibility
                        const updatedGroup = yield tx.questionGroup.update({
                            where: { id: update.id },
                            data: { order: update.position },
                        });
                        updatedGroups.push(updatedGroup);
                    }
                }
                return { questions: updatedQuestions, groups: updatedGroups };
            }));
        });
    }
}
exports.default = new QuestionRepository();
