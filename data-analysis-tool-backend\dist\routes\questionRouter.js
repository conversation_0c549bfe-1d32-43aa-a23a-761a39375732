"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const auth_1 = require("../middleware/auth");
const questionController_1 = require("../controllers/questionController");
const checkPermission_1 = require("../middleware/checkPermission");
const multer_1 = require("../utils/multer");
const router = express_1.default.Router();
router.patch("/positions", auth_1.authenticate, (0, checkPermission_1.checkPermission)([
    "manageProject",
    "editForm",
]), questionController_1.updateQuestionPositions);
// Create a question for a project
router.post("/:projectId", auth_1.authenticate, multer_1.uploads.single("file"), // Change from "excelFile" to "file" to match frontend
(0, checkPermission_1.checkPermission)([
    "manageProject",
    "editForm",
]), questionController_1.createQuestion);
// Update a specific question
router.patch("/:id", auth_1.authenticate, (0, checkPermission_1.checkPermission)([
    "manageProject",
    "editForm",
]), questionController_1.updateQuestion);
// Get all questions for a project (using path parameter)
router.get("/:projectId", auth_1.authenticate, questionController_1.getAllQuestion);
router.delete("/:id", auth_1.authenticate, questionController_1.deleteQuestion);
router.post("/duplicate/:id", auth_1.authenticate, (0, checkPermission_1.checkPermission)([
    "manageProject",
    "editForm",
]), questionController_1.duplicateQuestion);
exports.default = router;
