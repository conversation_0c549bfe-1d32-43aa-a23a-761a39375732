"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const prisma_1 = require("../utils/prisma");
class ProjectRepository {
    findByName(name, userId) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.project.findUnique({
                where: {
                    name_userId: {
                        name,
                        userId,
                    },
                },
            });
        });
    }
    findById(id) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.project.findUnique({
                where: { id },
                select: {
                    id: true,
                    name: true,
                    description: true,
                    sector: true,
                    user: true,
                    lastDeployedAt: true,
                    lastSubmissionAt: true,
                    createdAt: true,
                    updatedAt: true,
                },
            });
        });
    }
    findProjectByIdAndUser(id, userId) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.project.findFirst({
                where: {
                    id,
                    OR: [
                        { userId: userId },
                        {
                            projectUser: {
                                some: { userId },
                            },
                        },
                    ],
                },
                include: {
                    user: true,
                    questions: true,
                    projectUser: {
                        where: { userId },
                        select: {
                            permission: true,
                        },
                    },
                },
            });
        });
    }
    findAll(id) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.project.findMany({
                where: {
                    OR: [
                        { userId: id },
                        {
                            projectUser: {
                                some: { userId: id },
                            },
                        },
                    ],
                },
            });
        });
    }
    create(projectData) {
        return __awaiter(this, void 0, void 0, function* () {
            const { name, description, sector, userId, country } = projectData;
            return yield prisma_1.prisma.project.create({
                data: {
                    name,
                    description,
                    sector,
                    userId,
                    country,
                },
            });
        });
    }
    updateById(id, updateData) {
        return __awaiter(this, void 0, void 0, function* () {
            const data = {};
            if (updateData.name !== undefined)
                data.name = updateData.name;
            if (updateData.description !== undefined)
                data.description = updateData.description;
            if (updateData.sector !== undefined)
                data.sector = updateData.sector;
            if (updateData.country !== undefined)
                data.country = updateData.country;
            return yield prisma_1.prisma.project.update({
                where: { id },
                data,
            });
        });
    }
    deleteProject(id) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.project.delete({
                where: { id },
            });
        });
    }
    changeProjectStatus(id, status) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.project.update({
                where: { id },
                data: {
                    status: status,
                },
            });
        });
    }
    changeManyProjectStatus(ids, status) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.project.updateMany({
                where: { id: { in: ids } },
                data: { status },
            });
        });
    }
    deleteMultipleProject(ids) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.project.deleteMany({
                where: { id: { in: ids } },
            });
        });
    }
    fetchQuestionGroupWithQuestion(id) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.project.findUnique({
                where: { id },
                include: {
                    questionGroup: {
                        include: {
                            question: {
                                include: {
                                    questionOptions: true,
                                    questionConditions: true,
                                },
                                orderBy: { position: "asc" },
                            },
                            subGroups: {
                                include: {
                                    question: {
                                        include: {
                                            questionConditions: true,
                                            questionOptions: true,
                                        },
                                        orderBy: { position: "asc" },
                                    },
                                },
                            },
                        },
                        orderBy: { order: "asc" },
                    },
                    questions: {
                        where: { questionGroupId: null },
                        include: {
                            questionOptions: true,
                            questionGroup: true,
                        },
                        orderBy: { position: "asc" },
                    },
                },
            });
        });
    }
    fetchOrderedQuestions(projectId) {
        return __awaiter(this, void 0, void 0, function* () {
            // Fetch the ordered structure
            const orderItems = yield prisma_1.prisma.projectQuestionOrder.findMany({
                where: { projectId },
                orderBy: { position: "asc" },
            });
            const questionIds = orderItems
                .filter(item => item.questionId)
                .map(item => item.questionId);
            const groupIds = orderItems
                .filter(item => item.groupId)
                .map(item => item.groupId);
            // Fetch all questions and groups up front
            const questions = yield prisma_1.prisma.question.findMany({
                where: { id: { in: questionIds } },
                include: {
                    questionOptions: true,
                    questionConditions: true,
                },
            });
            const groups = yield prisma_1.prisma.questionGroup.findMany({
                where: { id: { in: groupIds } },
            });
            // Create maps for fast lookup
            const questionMap = new Map();
            questions.forEach(q => questionMap.set(q.id, q));
            const groupMap = new Map();
            groups.forEach(g => groupMap.set(g.id, Object.assign(Object.assign({}, g), { type: "group", questions: [] })));
            const orderedItems = [];
            // Build the structure based on position and parentGroupId
            for (const item of orderItems) {
                if (item.type === "question" && item.questionId) {
                    const question = questionMap.get(item.questionId);
                    if (!question)
                        continue;
                    if (item.parentGroupId) {
                        const parentGroup = groupMap.get(item.parentGroupId);
                        if (parentGroup) {
                            parentGroup.questions.push(Object.assign(Object.assign({}, question), { type: "question" }));
                        }
                    }
                    else {
                        orderedItems.push(Object.assign(Object.assign({}, question), { type: "question" }));
                    }
                }
                if (item.type === "group" && item.groupId && !item.parentGroupId) {
                    const group = groupMap.get(item.groupId);
                    if (group) {
                        orderedItems.push(group);
                    }
                }
            }
            return orderedItems;
        });
    }
}
exports.default = new ProjectRepository();
