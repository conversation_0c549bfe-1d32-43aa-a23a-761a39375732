(()=>{var e={};e.id=4835,e.ids=[4835],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6210:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>x});var s=r(60687),a=r(78122),o=r(62688);let n=(0,o.A)("layers",[["path",{d:"M12.83 2.18a2 2 0 0 0-1.66 0L2.6 6.08a1 1 0 0 0 0 1.83l8.58 3.91a2 2 0 0 0 1.66 0l8.58-3.9a1 1 0 0 0 0-1.83z",key:"zw3jo"}],["path",{d:"M2 12a1 1 0 0 0 .58.91l8.6 3.91a2 2 0 0 0 1.65 0l8.58-3.9A1 1 0 0 0 22 12",key:"1wduqc"}],["path",{d:"M2 17a1 1 0 0 0 .58.91l8.6 3.91a2 2 0 0 0 1.65 0l8.58-3.9A1 1 0 0 0 22 17",key:"kqbvx6"}]]);var l=r(80462),i=r(99270),d=r(97992);let c=(0,o.A)("zoom-in",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"11",x2:"11",y1:"8",y2:"14",key:"1vmskp"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]]),p=(0,o.A)("zoom-out",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]]);var u=r(43210);function x(){let[e,t]=(0,u.useState)("");return(0,s.jsxs)("div",{className:"flex flex-col space-y-6",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("h1",{className:"text-2xl font-semibold text-neutral-800",children:"Map"}),(0,s.jsx)("div",{className:"flex gap-2",children:(0,s.jsxs)("button",{className:"btn-primary",title:"Refresh map",children:[(0,s.jsx)(a.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"Refresh"})]})})]}),(0,s.jsxs)("div",{className:"flex justify-between gap-4 flex-wrap",children:[(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsxs)("select",{className:"border border-neutral-300 rounded px-3 py-2 bg-neutral-100 text-neutral-800 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 focus:outline-none transition-colors cursor-pointer",children:[(0,s.jsx)("option",{children:"Base Map"}),(0,s.jsx)("option",{children:"Satellite"}),(0,s.jsx)("option",{children:"Street Map"}),(0,s.jsx)("option",{children:"Terrain"})]}),(0,s.jsxs)("button",{className:"btn-primary",children:[(0,s.jsx)(n,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"Layers"})]}),(0,s.jsxs)("button",{className:"btn-primary",children:[(0,s.jsx)(l.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"Filter"})]})]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{type:"text",placeholder:"Search location...",className:"pl-9 pr-4 py-2 border border-neutral-300 rounded w-64 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 focus:outline-none transition-colors",value:e,onChange:e=>t(e.target.value)}),(0,s.jsx)(i.A,{className:"absolute left-3 top-2.5 w-4 h-4 text-neutral-400"})]})]}),(0,s.jsxs)("div",{className:"relative bg-neutral-100 border border-neutral-200 rounded-md h-[500px] shadow-sm overflow-hidden flex items-center justify-center",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-[url('https://via.placeholder.com/1200x800/f3f4f6/d1d5db?text=Map+Placeholder')] bg-cover bg-center opacity-40"}),(0,s.jsx)("div",{className:"absolute left-1/4 top-1/2 transform -translate-y-1/2",children:(0,s.jsx)(d.A,{className:"w-8 h-8 text-red-500"})}),(0,s.jsx)("div",{className:"absolute left-1/2 top-1/3 transform -translate-x-1/2",children:(0,s.jsx)(d.A,{className:"w-8 h-8 text-red-500"})}),(0,s.jsx)("div",{className:"absolute right-1/4 top-2/3 transform -translate-y-1/2",children:(0,s.jsx)(d.A,{className:"w-8 h-8 text-red-500"})}),(0,s.jsxs)("div",{className:"absolute right-4 top-4 flex flex-col gap-2",children:[(0,s.jsx)("button",{className:"p-2 bg-primary-500 rounded-full shadow hover:bg-primary-600 text-neutral-100 transition-colors cursor-pointer",children:(0,s.jsx)(c,{className:"w-4 h-4"})}),(0,s.jsx)("button",{className:"p-2 bg-primary-500 rounded-full shadow hover:bg-primary-600 text-neutral-100 transition-colors cursor-pointer",children:(0,s.jsx)(p,{className:"w-4 h-4"})})]}),(0,s.jsxs)("div",{className:"relative z-10 bg-neutral-100 px-6 py-4 rounded-lg shadow-lg",children:[(0,s.jsx)("p",{className:"font-medium text-neutral-800",children:"Map will be displayed here"}),(0,s.jsx)("p",{className:"text-sm text-neutral-500",children:"We need to integrate mapping library here."})]})]}),(0,s.jsxs)("div",{className:"bg-neutral-100 rounded-md border border-neutral-200 p-4 shadow-sm",children:[(0,s.jsx)("h3",{className:"font-medium text-neutral-800 mb-2",children:"Legend"}),(0,s.jsxs)("div",{className:"flex items-center gap-2 text-sm text-neutral-600 mb-1",children:[(0,s.jsx)(d.A,{className:"w-4 h-4 text-red-500"}),(0,s.jsx)("span",{children:"Survey Location"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2 text-sm text-neutral-600",children:[(0,s.jsx)("div",{className:"w-4 h-4 bg-primary-300 rounded"}),(0,s.jsx)("span",{children:"High Density Area"})]})]})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},54382:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d});var s=r(65239),a=r(48088),o=r(88170),n=r.n(o),l=r(30893),i={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);r.d(t,i);let d={children:["",{children:["[locale]",{children:["(main)",{children:["project",{children:["[hashedId]",{children:["data",{children:["map",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,76100)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\project\\[hashedId]\\data\\map\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,87282)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\project\\[hashedId]\\data\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,51129)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\project\\[hashedId]\\layout.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,84606)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,72121)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\project\\[hashedId]\\data\\map\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/(main)/project/[hashedId]/data/map/page",pathname:"/[locale]/project/[hashedId]/data/map",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},76100:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Kobo-project\\\\data-analysis-tool-frontend\\\\app\\\\[locale]\\\\(main)\\\\project\\\\[hashedId]\\\\data\\\\map\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\project\\[hashedId]\\data\\map\\page.tsx","default")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},85111:(e,t,r)=>{Promise.resolve().then(r.bind(r,76100))},94735:e=>{"use strict";e.exports=require("events")},96783:(e,t,r)=>{Promise.resolve().then(r.bind(r,6210))},97992:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])}};var t=require("../../../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,7404,1658,6560,7618,63,7605,3851,8581,6226,5233,8626],()=>r(54382));module.exports=s})();