(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8334],{50909:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>I});var s=r(95155),a=r(12115),n=r(19373),i=r(5041),l=r(35695),o=r(88570),d=r(34947),u=r(10150),c=r(77361),p=r(57799),m=r(82714),h=r(99474),y=r(95139),g=r(55747),f=r(92138),x=r(66474),b=r(13052),v=r(34540),j=r(71402),q=r(16112),w=r(3587),N=r(13388);function I(){let e=(0,v.wA)(),{hashedId:t}=(0,l.useParams)(),r=(0,o.D)(t),[I,O]=(0,a.useState)({}),[A,k]=(0,a.useState)({}),[E,T]=(0,a.useState)(!1),[S,C]=(0,a.useState)([]),[F,D]=(0,a.useState)([]),[P,R]=(0,a.useState)({}),{data:_,isLoading:G,isError:Q}=(0,n.I)({queryKey:["questions",r],queryFn:()=>(0,d.K4)({projectId:r}),enabled:!!r}),{data:J=[]}=(0,n.I)({queryKey:["questionGroups",r],queryFn:()=>(0,u.pr)({projectId:r}),enabled:!!r}),{data:L}=(0,n.I)({queryKey:["project",r],queryFn:()=>(0,c.kf)({projectId:r}),enabled:!!r});(0,a.useEffect)(()=>{if(_){let e={};_.forEach(t=>{e[t.id]="selectmany"===t.inputType?[]:""}),O(e)}},[_]),(0,a.useEffect)(()=>{if(_){let e=(0,w.UL)(_,I);C(e),D((0,w.Tr)(_,I));let t=(0,w.OD)(I,e);Object.keys(t).length!==Object.keys(I).length&&O(t)}},[_,I]),(0,a.useEffect)(()=>{if(J.length>0){let e={};J.forEach(t=>{e[t.id]=!0}),R(e)}},[J.length]);let M=(0,a.useMemo)(()=>J.reduce((e,t)=>(e[t.id]=(null==_?void 0:_.filter(e=>e.questionGroupId===t.id))||[],e),{}),[J,_]),K=(0,a.useMemo)(()=>(null==_?void 0:_.filter(e=>null===e.questionGroupId||void 0===e.questionGroupId))||[],[_]),V=(0,a.useMemo)(()=>{let e=[];return J.forEach(t=>{let r=(null==_?void 0:_.filter(e=>e.questionGroupId===t.id))||[],s=r.length>0?Math.min(...r.map(e=>e.position)):t.order;e.push({type:"group",data:t,order:s,originalPosition:s})}),K.forEach(t=>{e.push({type:"question",data:t,order:t.position,originalPosition:t.position})}),e.sort((e,t)=>e.order===t.order?(e.originalPosition||e.order)-(t.originalPosition||t.order):e.order-t.order)},[J,K,_]),Y=(0,a.useCallback)(e=>{R(t=>({...t,[e]:!t[e]}))},[]),U=(0,i.n)({mutationFn:async e=>{let t=(null==_?void 0:_.map(t=>{let s,a,n,i=e[t.id],l="selectmany"===t.inputType,o="selectone"===t.inputType;if(!l&&!o&&(null==i||""===i)||o&&(!i||""===i.trim()))return null;if(l&&Array.isArray(i)&&t.questionOptions){let e=i.map(e=>{let r=t.questionOptions.find(t=>t.label===e);return null==r?void 0:r.id}).filter(e=>void 0!==e);s=e.length>0?e:[]}else if(o&&i&&t.questionOptions){let e=t.questionOptions.find(e=>e.label===i);if(void 0===(s=null==e?void 0:e.id))return console.warn("Could not find option ID for selectone question ".concat(t.id,' with value "').concat(i,'"')),null}if(null==(a=l?Array.isArray(i)?i.join(", "):"":"number"===t.inputType||"decimal"===t.inputType?i?Number(i):void 0:"date"===t.inputType||"dateandtime"===t.inputType?i||void 0:"table"===t.inputType?Array.isArray(i)&&i.length>0?JSON.stringify(i):void 0:i?String(i):void 0))return null;n=l?Array.isArray(s)?s:[]:o&&"number"==typeof s?s:void 0;let d={projectId:Number(r),questionId:t.id,answerType:String(t.inputType),value:a,isOtherOption:!1};return void 0!==n&&(d.questionOptionId=n),d}).filter(e=>null!==e))||[];if(0===t.length)throw Error("No valid answers to submit. Please fill out at least one field.");return console.log("Submission Data:",{projectId:r,formattedAnswers:t,totalQuestions:null==_?void 0:_.length,answeredQuestions:t.length}),console.log("All Questions Debug:",t.map(e=>({questionId:e.questionId,answerType:e.answerType,value:e.value,questionOptionId:e.questionOptionId,questionOptionIdType:Array.isArray(e.questionOptionId)?"array":typeof e.questionOptionId,questionOptionIdLength:Array.isArray(e.questionOptionId)?e.questionOptionId.length:"N/A",isOtherOption:e.isOtherOption,projectId:e.projectId}))),console.log("Backend Schema Validation Check:"),t.forEach((e,t)=>{console.log("Answer ".concat(t+1,":"),{hasProjectId:"number"==typeof e.projectId,hasQuestionId:"number"==typeof e.questionId,hasAnswerType:"string"==typeof e.answerType,hasValue:void 0!==e.value,hasIsOtherOption:"boolean"==typeof e.isOtherOption,questionOptionIdValidation:"selectmany"===e.answerType?Array.isArray(e.questionOptionId):"selectone"===e.answerType?void 0===e.questionOptionId||"number"==typeof e.questionOptionId:void 0===e.questionOptionId,allRequiredFieldsPresent:!!(e.projectId&&e.questionId&&e.answerType&&void 0!==e.isOtherOption)})}),await (0,c.lj)(t)},onSuccess:()=>{e((0,j.Ds)({message:"Form submitted successfully",type:"success"})),O({}),window.dispatchEvent(new Event("form-submitted")),localStorage.setItem("form_submitted",Date.now().toString())},onError:t=>{e((0,j.Ds)({message:"Failed to submit form. Please try again.",type:"error"})),console.error("Submission Error:",t)},onSettled:()=>{T(!1)}});(0,a.useEffect)(()=>{if(_){let e=(0,w.UL)(_,I);C(e),D((0,w.Tr)(_,I));let t=(0,w.OD)(I,e);Object.keys(t).length!==Object.keys(I).length&&O(t)}},[_,I]);let z=(0,a.useCallback)((e,t)=>{O(r=>({...r,[e]:t})),k(t=>({...t,[e]:""}))},[]),B=()=>{let e={};return S.forEach(t=>{if(t.isRequired){let r=I[t.id];("string"==typeof r&&!r.trim()||Array.isArray(r)&&0===r.length||null==r)&&(e[t.id]="".concat(t.label," is required"))}}),k(e),0===Object.keys(e).length},H=async e=>{e.preventDefault(),B()&&(T(!0),U.mutate(I))},X=e=>!!_&&_.some(t=>{var r;return null==(r=t.questionOptions)?void 0:r.some(t=>t.nextQuestionId===e)}),W=e=>{var t;return(null==(t=e.questionOptions)?void 0:t.some(e=>e.nextQuestionId))||!1},Z=e=>{var t,r,a,n;let i=null!=(t=I[e.id])?t:"selectmany"===e.inputType?[]:"";switch(e.inputType){case"text":if(null==(r=e.hint)?void 0:r.includes("multiline"))return(0,s.jsx)(h.T,{value:i,onChange:t=>z(e.id,t.target.value),placeholder:e.placeholder||"Your answer",required:e.isRequired});return(0,s.jsx)("input",{className:"input-field w-full",value:i,onChange:t=>z(e.id,t.target.value),placeholder:e.placeholder||"Your answer",required:e.isRequired});case"number":return(0,s.jsx)("input",{className:"input-field w-full",type:"number",value:i,onChange:t=>z(e.id,t.target.value),placeholder:e.placeholder||"Your answer",required:e.isRequired});case"decimal":return(0,s.jsx)("input",{className:"input-field w-full",type:"number",step:"any",value:i,onChange:t=>z(e.id,t.target.value),placeholder:e.placeholder||"Your answer",required:e.isRequired});case"selectone":return(0,s.jsx)(g.z,{value:i,onValueChange:t=>z(e.id,t),required:e.isRequired,children:(0,s.jsx)("div",{className:"space-y-2",children:null==(a=e.questionOptions)?void 0:a.map((e,t)=>(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(g.C,{value:e.label,id:"option-".concat(e.id)}),(0,s.jsx)(m.J,{htmlFor:"option-".concat(e.id),className:"cursor-pointer",children:e.label}),e.sublabel&&(0,s.jsx)("p",{className:"text-sm text-neutral-700 ml-4",children:"(".concat(e.sublabel,")")})]},t))})});case"selectmany":return(0,s.jsx)("div",{className:"space-y-2",children:null==(n=e.questionOptions)?void 0:n.map(t=>(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(y.S,{id:"option-".concat(t.id),checked:(i||[]).includes(t.label),onCheckedChange:r=>{let s=i||[],a=r?[...s,t.label]:s.filter(e=>e!==t.label);z(e.id,a)}}),(0,s.jsx)(m.J,{htmlFor:"option-".concat(t.id),className:"cursor-pointer",children:t.label})]},t.id))});case"date":return(0,s.jsx)("div",{className:"relative",children:(0,s.jsx)("input",{className:"input-field w-full",type:"date",value:i,onChange:t=>z(e.id,t.target.value),placeholder:e.placeholder||"Select date",required:e.isRequired})});case"dateandtime":return(0,s.jsx)("div",{className:"relative",children:(0,s.jsx)("input",{className:"input-field w-full",type:"time",value:i,onChange:t=>z(e.id,t.target.value),placeholder:e.placeholder||"Select time",required:e.isRequired})});case"table":return(0,s.jsx)(q.N,{questionId:e.id,value:i,onChange:t=>z(e.id,t),required:e.isRequired,tableLabel:e.label});default:return null}},$=e=>{let t=X(e.id),r=W(e);return(0,s.jsxs)("div",{className:"border rounded-md p-4 ".concat(t?"border-primary-200 dark:border-primary-700 bg-primary-100 dark:bg-primary-900/20":"border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800"),children:[(0,s.jsxs)("div",{className:"mb-2",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsxs)(m.J,{className:"text-base font-medium",children:[e.label,e.isRequired&&(0,s.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),t&&(0,s.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-200 text-primary-800 dark:bg-primary-900 dark:text-primary-200",children:[(0,s.jsx)(f.A,{className:"w-3 h-3 mr-1"}),"Follow-up"]}),r&&(0,s.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-accent-200 text-accent-800 dark:bg-accent-700/20 dark:text-accent-200",children:"Has conditions"})]}),e.hint&&(0,s.jsx)("p",{className:"text-sm mt-1 ".concat(t?"text-primary-700 dark:text-primary-300":"text-muted-foreground"),children:e.hint}),A[e.id]&&(0,s.jsx)("p",{className:"text-sm text-red-500 mt-1",children:A[e.id]})]}),(0,s.jsx)("div",{className:"mt-2",children:Z(e)})]},e.id)};return G?(0,s.jsx)(p.A,{}):Q||!_?(0,s.jsx)("p",{className:"text-sm text-red-500",children:"Error loading form. Please try again."}):(0,s.jsx)("div",{className:"min-h-screen w-full bg-neutral-100 dark:bg-gray-900 flex flex-col items-center p-4 md:p-6",children:(0,s.jsxs)("div",{className:"w-full max-w-screen-lg bg-neutral-100 dark:bg-gray-800 rounded-md shadow-sm border border-gray-200 dark:border-gray-700",children:[(0,s.jsx)("h2",{className:"text-2xl font-semibold p-6 border-b border-gray-200 dark:border-gray-700",children:"Test Form"}),(0,s.jsx)("form",{onSubmit:H,className:"p-6",children:(0,s.jsxs)("div",{className:"space-y-6",children:[_&&0!==_.length?V.map(e=>{if("group"===e.type){let t=e.data,r=M[t.id]||[],a=r.filter(e=>S.some(t=>t.id===e.id)),n=P[t.id];return 0===a.length?null:(0,s.jsxs)("div",{className:"border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800",children:[(0,s.jsx)("div",{className:"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700",onClick:()=>Y(t.id),children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[n?(0,s.jsx)(x.A,{className:"h-5 w-5 text-gray-500"}):(0,s.jsx)(b.A,{className:"h-5 w-5 text-gray-500"}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100",children:t.title}),(0,s.jsxs)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:["(",a.length," visible question",1!==a.length?"s":"",")"]})]})}),n&&(0,s.jsx)("div",{className:"p-4 space-y-4",children:F.filter(e=>r.some(t=>t.id===e.question.id)).map(e=>(0,s.jsx)(N.A,{questionGroup:e,renderQuestionInput:Z,errors:A,className:""},e.question.id))})]},"group-".concat(t.id))}{let t=e.data;if(!S.some(e=>e.id===t.id))return null;let r=F.find(e=>e.question.id===t.id);return r?(0,s.jsx)(N.A,{questionGroup:r,renderQuestionInput:Z,errors:A,className:""},t.id):$(t)}}):(0,s.jsx)("div",{className:"text-center py-12",children:(0,s.jsx)("p",{className:"text-muted-foreground",children:"This form has no questions yet."})}),_.length>0&&(0,s.jsx)("div",{className:"mt-6 flex justify-end",children:(0,s.jsx)("button",{className:"btn-primary",type:"submit",disabled:E,children:E?"Submitting...":"Submit Form"})}),0===_.length&&(0,s.jsx)("div",{className:"text-center py-12",children:(0,s.jsx)("p",{className:"text-muted-foreground",children:"This form has no questions yet."})}),_&&_.length>0&&0===S.length&&(0,s.jsx)("div",{className:"text-center py-12",children:(0,s.jsx)("p",{className:"text-muted-foreground",children:"No questions are currently visible. Please check your form configuration."})})]})})]})})}},59836:(e,t,r)=>{Promise.resolve().then(r.bind(r,50909))},77361:(e,t,r)=>{"use strict";r.d(t,{D_:()=>c,Im:()=>d,Oo:()=>p,c3:()=>n,kf:()=>a,lj:()=>h,or:()=>o,pf:()=>u,vj:()=>i,wI:()=>m,xx:()=>l});var s=r(25784);let a=async e=>{let{projectId:t}=e,{data:r}=await s.A.get("/projects/".concat(t));return r.project},n=async e=>{let{data:t}=await s.A.post("/projects/from-template",e);return t},i=async()=>{try{let{data:e}=await s.A.get("/projects");return e.projects}catch(e){throw console.error("Error fetching projects:",e),e}},l=async e=>{let{data:t}=await s.A.delete("/projects/delete/".concat(e));return t},o=async e=>{try{let{data:t}=await s.A.delete("/projects/delete-multiple",{data:{projectIds:e}});return t}catch(e){throw console.error("Error deleting multiple projects:",e),e}},d=async e=>{try{let{data:t}=await s.A.patch("/projects/change-status/".concat(e),{status:"archived"});return t}catch(e){throw console.error("Error archiving project:",e),e}},u=async function(e){arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{let{data:t}=await s.A.patch("/projects/change-status/".concat(e),{status:"deployed"});return t}catch(e){throw console.error("Error deploying project:",e),e}},c=async e=>{try{let{data:t}=await s.A.patch("/projects/update-many-status",{projectIds:e,status:"archived"});return t}catch(e){throw console.error("Error archiving multiple projects:",e),e}},p=async e=>{try{let{data:t}=await s.A.post("/users/check-email",{email:e});return t}catch(e){var t,r,a,n,i,l;throw Error("object"==typeof(null==(r=e.response)||null==(t=r.data)?void 0:t.message)?JSON.stringify(null==(n=e.response)||null==(a=n.data)?void 0:a.message):(null==(l=e.response)||null==(i=l.data)?void 0:i.message)||e.message||"Failed to check user")}},m=async e=>{let{projectId:t,email:r,permissions:a}=e;try{let e=await p(r);if(!e||!e.success)throw Error((null==e?void 0:e.message)||"User not found");let{data:n}=await s.A.post("/project-users",{userId:e.user.id,projectId:t,permission:a});return n}catch(e){var n,i,l,o,d,u;throw console.error("Error adding user to project:",e),Error("object"==typeof(null==(i=e.response)||null==(n=i.data)?void 0:n.message)?JSON.stringify(null==(o=e.response)||null==(l=o.data)?void 0:l.message):(null==(u=e.response)||null==(d=u.data)?void 0:d.message)||e.message||"Failed to add user")}},h=async e=>{try{let{data:t}=await s.A.post("/answers/multiple",e);return t}catch(e){throw console.error("Error creating answer submission:",e),e}}},88570:(e,t,r)=>{"use strict";r.d(t,{D:()=>l,l:()=>i});var s=r(41050);let a=r(49509).env.SALT||"rushan-salt",n=new s.A(a,12),i=e=>n.encode(e),l=e=>{let t=n.decode(e)[0];return"bigint"==typeof t?t<Number.MAX_SAFE_INTEGER?Number(t):null:"number"==typeof t?t:null}}},e=>{var t=t=>e(e.s=t);e.O(0,[635,1111,6967,9373,4277,556,3481,7823,7248,8441,1684,7358],()=>t(59836)),_N_E=e.O()}]);