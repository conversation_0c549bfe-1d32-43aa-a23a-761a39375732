(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1968],{2511:(e,a,t)=>{"use strict";t.d(a,{b:()=>i});let i={information_media:"Information / Media",econommic_social_development:"Economic & Social Development",security_police_peacekeeping:"Security / Police / Peacekeeping",disarmament_and_demobilization:"Disarmament & Demobilization",environment:"Environment",private_sector:"Private Sector",humanitarian_coordination_information_management:"Humanitarian - Coordination & Info Management",humanitarian_multiple_clusters:"Humanitarian - Multiple Clusters",humanitarian_camp_management_and_coordination:"Humanitarian - Camp Management & Coordination",humanitarian_early_recovery:"Humanitarian - Early Recovery",humanitarian_education:"Humanitarian - Education",humanitarian_emergency_shelter:"Humanitarian - Emergency Shelter",humanitarian_emergency_telecoms:"Humanitarian - Emergency Telecoms",humanitarian_food_security:"Humanitarian - Food Security",humanitarian_health:"Humanitarian - Health",humanitarian_logistics:"Humanitarian - Logistics",humanitarian_nutrition:"Humanitarian - Nutrition",humanitarian_protection:"Humanitarian - Protection",humanitarian_sanitation_water_and_hygiene:"Humanitarian - Sanitation / Water / Hygiene",other:"Other"}},14961:(e,a,t)=>{Promise.resolve().then(t.bind(t,45193))},15616:(e,a,t)=>{"use strict";t.d(a,{A:()=>l});var i=t(95155),n=t(46453),s=t(35695),r=t(6874),o=t.n(r);function l(){let e=(0,n.Ym)(),a=(0,s.usePathname)(),t=e=>{let t=a.replace(/^\/(en|ne)/,"");return"/".concat(e).concat(t)};return(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(o(),{href:t("en"),className:"px-3 py-1 rounded ".concat("en"===e?"bg-primary-600 text-white":"bg-gray-200"),children:"English"}),(0,i.jsx)(o(),{href:t("ne"),className:"px-3 py-1 rounded ".concat("ne"===e?"bg-primary-600 text-white":"bg-gray-200"),children:"नेपाली"})]})}},17576:(e,a,t)=>{"use strict";t.d(a,{A:()=>i});let i=(0,t(19946).A)("briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},23491:(e,a,t)=>{"use strict";t.d(a,{i:()=>i});let i={non_profit_organization:"Non-profit Organization",government_institution:"Government Institution",educational_organization:"Educational Organization",a_commercial_or_for_profit_company:"Commercial / For-profit Company",i_am_not_associated_with_any_organization:"Not Associated with any Organization"}},25784:(e,a,t)=>{"use strict";t.d(a,{A:()=>n});let i=t(23464).A.create({baseURL:"http://localhost:4000/api",headers:{"Content-Type":"application/json"},withCredentials:!0});i.interceptors.request.use(e=>e,e=>Promise.reject(e)),i.interceptors.response.use(e=>e,e=>("ERR_NETWORK"===e.code&&console.error("Network error - Please check if the backend server is running"),Promise.reject(e)));let n=i},34869:(e,a,t)=>{"use strict";t.d(a,{A:()=>i});let i=(0,t(19946).A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},45193:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>C});var i=t(95155),n=t(90221),s=t(90232),r=t(78749),o=t(92657),l=t(34869),c=t(17576),d=t(6874),u=t.n(d),m=t(35695),p=t(12115),h=t(62177),g=t(55594),x=t(59362),y=t(34540),b=t(71402),f=t(50408),N=t(74567),w=t(2511),j=t(23491),v=t(64368),_=t(25784),S=t(17652),z=t(15616);let A=e=>g.z.object({name:g.z.string().min(1,e("fullNameRequired")),email:g.z.string().min(1,e("emailRequired")).email(e("invalidEmail")),password:g.z.string().min(1,e("passwordRequired")).min(8,e("passwordMin")).max(32,e("passwordMax")).regex(/[A-Z]/,e("passwordUppercase")).regex(/[a-z]/,e("passwordLowercase")).regex(/[0-9]/,e("passwordNumber")).regex(/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/,e("passwordSpecial")),confirmPassword:g.z.string().min(1,e("confirmPasswordRequired")),country:g.z.string().min(1,e("selectCountry")),sector:g.z.string().min(1,e("selectSector")),organizationType:g.z.string().min(1,e("selectOrgType"))}).refine(e=>e.password===e.confirmPassword,{message:e("passwordsDoNotMatch"),path:["confirmPassword"]}),C=()=>{let e=(0,S.c3)(),a=A(e),{register:t,formState:{errors:d,isSubmitting:g,isSubmitted:C},setValue:k,handleSubmit:E,setError:M,watch:P}=(0,h.mN)({resolver:(0,n.u)(a)}),T=P("password"),H=P("confirmPassword");(0,p.useEffect)(()=>{t("country",{required:e("selectCountry")}),t("sector",{required:e("selectSector")}),t("organizationType",{required:e("selectOrgType")})},[t]);let[L,R]=(0,p.useState)(""),[B,I]=(0,p.useState)(""),[O,F]=(0,p.useState)(""),[G,q]=(0,p.useState)(!1),[V,D]=(0,p.useState)(!1);(0,p.useEffect)(()=>{k("country",L,{shouldValidate:C}),k("sector",B,{shouldValidate:C}),k("organizationType",O,{shouldValidate:C})},[L,B,O,k]);let U=(0,m.useRouter)(),K=(0,y.wA)(),Z=async a=>{try{await _.A.post("/users/signup",a),U.push("/"),K((0,b.Ds)({message:e("signupSuccess"),type:"success"}))}catch(a){if(a instanceof x.pe){var t,i;M(null==(t=a.response)?void 0:t.data.errorField,{message:null==(i=a.response)?void 0:i.data.message})}else K((0,b.Ds)({message:e("signupError"),type:"error"}))}};return(0,i.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,i.jsxs)("div",{className:"flex flex-col gap-8 section w-11/12 mobile:w-4/5 tablet:w-2xl my-8 tablet:my-16",children:[(0,i.jsxs)("div",{className:"flex flex-col items-center gap-2",children:[(0,i.jsx)(s.A,{size:36}),(0,i.jsx)("h1",{className:"text-2xl tablet:text-3xl font-semibold text-center",children:e("createAccount")}),(0,i.jsx)("p",{className:"text-neutral-700 text-center",children:e("getStarted2")})]}),(0,i.jsxs)("form",{className:"flex flex-col gap-4",onSubmit:E(Z),children:[(0,i.jsxs)("div",{className:"group label-input-group",children:[(0,i.jsx)("label",{htmlFor:"name",className:"label-text",children:e("fullName")}),(0,i.jsx)("input",{...t("name"),id:"name",type:"text",placeholder:e("enterFullName"),className:"input-field"}),d.name&&(0,i.jsx)("p",{className:"text-red-500 text-sm",children:"".concat(d.name.message)})]}),(0,i.jsxs)("div",{className:"group label-input-group",children:[(0,i.jsx)("label",{htmlFor:"email",className:"label-text",children:e("email")}),(0,i.jsx)("input",{...t("email"),id:"email",type:"email",placeholder:e("enterEmail"),className:"input-field"}),d.email&&(0,i.jsx)("p",{className:"text-red-500 text-sm",children:"".concat(d.email.message)})]}),(0,i.jsxs)("div",{className:"group label-input-group",children:[(0,i.jsx)("label",{htmlFor:"password",className:"label-text",children:e("password")}),(0,i.jsxs)("div",{className:"relative",children:[(0,i.jsx)("input",{...t("password"),id:"password",type:G?"text":"password",placeholder:e("enterPassword"),className:"input-field w-full pr-10"}),T&&T.length>0&&(0,i.jsxs)("button",{type:"button",tabIndex:-1,className:"absolute right-2 top-1/2 -translate-y-1/2 text-gray-500",onClick:()=>q(!G),children:[G?(0,i.jsx)(r.A,{className:"h-4 w-4"}):(0,i.jsx)(o.A,{className:"h-4 w-4"}),(0,i.jsxs)("span",{className:"sr-only",children:[G?"Hide":"Show"," password"]})]})]}),d.password&&(0,i.jsx)("p",{className:"text-red-500 text-sm",children:"".concat(d.password.message)})]}),(0,i.jsxs)("div",{className:"group label-input-group",children:[(0,i.jsx)("label",{htmlFor:"confirm-password",className:"label-text",children:e("confirmPassword")}),(0,i.jsxs)("div",{className:"relative",children:[(0,i.jsx)("input",{...t("confirmPassword"),id:"confirm-password",type:V?"text":"password",placeholder:e("confirm_password_required"),className:"input-field w-full pr-10"}),H&&H.length>0&&(0,i.jsxs)("button",{type:"button",tabIndex:-1,className:"absolute right-2 top-1/2 -translate-y-1/2 text-gray-500",onClick:()=>D(!V),children:[V?(0,i.jsx)(r.A,{className:"h-4 w-4"}):(0,i.jsx)(o.A,{className:"h-4 w-4"}),(0,i.jsxs)("span",{className:"sr-only",children:[V?"Hide":"Show"," password"]})]})]}),d.confirmPassword&&(0,i.jsx)("p",{className:"text-red-500 text-sm",children:"".concat(d.confirmPassword.message)})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 tablet:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{className:"label-input-group group",children:[(0,i.jsxs)("label",{htmlFor:"country",className:"label-text",children:[(0,i.jsx)(l.A,{size:16})," ",e("country")]}),(0,i.jsx)(f.l,{id:"country",options:N,value:L||e("selectOption"),onChange:R}),d.country&&(0,i.jsx)("p",{className:"text-red-500 text-sm",children:"".concat(d.country.message)})]}),(0,i.jsxs)("div",{className:"label-input-group group",children:[(0,i.jsxs)("label",{htmlFor:"sector",className:"label-text",children:[(0,i.jsx)(c.A,{size:16})," ",e("sector")]}),(0,i.jsx)(f.l,{id:"sector",options:Object.values(w.b),value:B&&w.b[B]?w.b[B]:e("selectOption"),onChange:e=>{let a=(0,v.H)(e,w.b);I(null!=a?a:"")}}),d.sector&&(0,i.jsx)("p",{className:"text-red-500 text-sm",children:"".concat(d.sector.message)})]}),(0,i.jsxs)("div",{className:"label-input-group group",children:[(0,i.jsxs)("label",{htmlFor:"organizationType",className:"label-text",children:[(0,i.jsx)(c.A,{size:16})," ",e("organizationType")]}),(0,i.jsx)(f.l,{id:"organizationType",options:Object.values(j.i),value:O&&j.i[O]?j.i[O]:e("selectOption"),onChange:e=>{let a=(0,v.H)(e,j.i);F(null!=a?a:"")}}),d.organizationType&&(0,i.jsx)("p",{className:"text-red-500 text-sm",children:"".concat(d.organizationType.message)})]})]}),(0,i.jsx)("button",{type:"submit",className:"btn-primary",disabled:g,children:g?(0,i.jsxs)("span",{className:"flex items-center gap-2",children:[e("signingUp"),(0,i.jsx)("div",{className:"size-4 rounded-full border-x-2 animate-spin"})]}):e("signUp")})]}),(0,i.jsxs)("div",{className:"text-neutral-700 flex items-center gap-2",children:[(0,i.jsx)("span",{children:e("alreadyHaveAccount")}),(0,i.jsx)(u(),{href:"/",className:"font-medium hover:text-neutral-900 duration-300",children:e("signIn")})]}),(0,i.jsx)("div",{children:(0,i.jsx)(z.A,{})})]})})}},50408:(e,a,t)=>{"use strict";t.d(a,{l:()=>r});var i=t(95155),n=t(66474),s=t(12115);let r=e=>{let{id:a,options:t,value:r,onChange:o}=e,[l,c]=(0,s.useState)(!1),d=(0,s.useRef)(null),u=(0,s.useRef)([]),m=(0,s.useRef)(null);(0,s.useEffect)(()=>{let e=e=>{m.current&&!m.current.contains(e.target)&&c(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]);let p=e=>{if(!l)return;let a=e.key.toLowerCase();if(a.match(/[a-z]/)){let e=t.findIndex(e=>e.toLowerCase().startsWith(a));if(-1!==e&&u.current[e]){var i;null==(i=u.current[e])||i.scrollIntoView({behavior:"auto",block:"nearest"})}}};return(0,s.useEffect)(()=>(document.addEventListener("keydown",p),()=>{document.removeEventListener("keydown",p)}),[l,t]),(0,i.jsxs)("div",{className:"relative",ref:m,children:[(0,i.jsxs)("button",{id:a,type:"button",className:"px-4 py-2 flex items-center justify-between rounded-md border border-neutral-400 focus:border-primary-500 duration-300 w-full text-left cursor-pointer",onClick:()=>{c(!l)},children:[(0,i.jsx)("span",{children:r||"Select an option"}),(0,i.jsx)(n.A,{})]}),l&&(0,i.jsx)("ul",{className:"absolute z-10 max-h-[180px] overflow-auto border border-neutral-400 rounded-md bg-neutral-100 w-full mt-1 flex flex-col",ref:d,children:t.map((e,a)=>(0,i.jsx)("li",{ref:e=>{u.current[a]=e},className:"cursor-pointer bg-neutral-100 hover:bg-neutral-200 px-4 py-2",onClick:()=>{o(e),c(!1)},children:e},a))})]})}},59362:(e,a,t)=>{"use strict";t.d(a,{F0:()=>u,pe:()=>n});let{Axios:i,AxiosError:n,CanceledError:s,isCancel:r,CancelToken:o,VERSION:l,all:c,Cancel:d,isAxiosError:u,spread:m,toFormData:p,AxiosHeaders:h,HttpStatusCode:g,formToJSON:x,getAdapter:y,mergeConfig:b}=t(23464).A},64368:(e,a,t)=>{"use strict";t.d(a,{H:()=>i});let i=(e,a)=>{let t=Object.entries(a).find(a=>{let[t,i]=a;return i===e});return t?t[0]:null}},66474:(e,a,t)=>{"use strict";t.d(a,{A:()=>i});let i=(0,t(19946).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},71402:(e,a,t)=>{"use strict";t.d(a,{Ay:()=>r,Ds:()=>n,_b:()=>s});let i=(0,t(51990).Z0)({name:"notification",initialState:{message:"",type:"",visible:!1},reducers:{showNotification:(e,a)=>{e.message=a.payload.message,e.type=a.payload.type,e.visible=!0},hideNotification:e=>{e.message="",e.type="",e.visible=!1}}}),{showNotification:n,hideNotification:s}=i.actions,r=i.reducer},74567:e=>{"use strict";e.exports=JSON.parse('["Afghanistan","Albania","Algeria","Andorra","Angola","Argentina","Armenia","Australia","Austria","Azerbaijan","Bahamas","Bahrain","Bangladesh","Barbados","Belarus","Belgium","Belize","Benin","Bhutan","Bolivia","Bosnia and Herzegovina","Botswana","Brazil","Brunei","Bulgaria","Burkina Faso","Burundi","Cabo Verde","Cambodia","Cameroon","Canada","Central African Republic","Chad","Chile","China","Colombia","Comoros","Congo (Congo-Brazzaville)","Costa Rica","Croatia","Cuba","Cyprus","Czech Republic","Denmark","Djibouti","Dominica","Dominican Republic","Ecuador","Egypt","El Salvador","Equatorial Guinea","Eritrea","Estonia","Eswatini","Ethiopia","Fiji","Finland","France","Gabon","Gambia","Georgia","Germany","Ghana","Greece","Grenada","Guatemala","Guinea","Guinea-Bissau","Guyana","Haiti","Honduras","Hungary","Iceland","India","Indonesia","Iran","Iraq","Ireland","Israel","Italy","Jamaica","Japan","Jordan","Kazakhstan","Kenya","Kiribati","Kuwait","Kyrgyzstan","Laos","Latvia","Lebanon","Lesotho","Liberia","Libya","Liechtenstein","Lithuania","Luxembourg","Madagascar","Malawi","Malaysia","Maldives","Mali","Malta","Marshall Islands","Mauritania","Mauritius","Mexico","Micronesia","Moldova","Monaco","Mongolia","Montenegro","Morocco","Mozambique","Myanmar","Namibia","Nauru","Nepal","Netherlands","New Zealand","Nicaragua","Niger","Nigeria","North Korea","North Macedonia","Norway","Oman","Pakistan","Palau","Palestine","Panama","Papua New Guinea","Paraguay","Peru","Philippines","Poland","Portugal","Qatar","Romania","Russia","Rwanda","Saint Kitts and Nevis","Saint Lucia","Saint Vincent and the Grenadines","Samoa","San Marino","Sao Tome and Principe","Saudi Arabia","Senegal","Serbia","Seychelles","Sierra Leone","Singapore","Slovakia","Slovenia","Solomon Islands","Somalia","South Africa","South Korea","South Sudan","Spain","Sri Lanka","Sudan","Suriname","Sweden","Switzerland","Syria","Taiwan","Tajikistan","Tanzania","Thailand","Timor-Leste","Togo","Tonga","Trinidad and Tobago","Tunisia","Turkey","Turkmenistan","Tuvalu","Uganda","Ukraine","United Arab Emirates","United Kingdom","United States","Uruguay","Uzbekistan","Vanuatu","Vatican City","Venezuela","Vietnam","Yemen","Zambia","Zimbabwe"]')},90232:(e,a,t)=>{"use strict";t.d(a,{A:()=>i});let i=(0,t(19946).A)("shield-check",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])}},e=>{var a=a=>e(e.s=a);e.O(0,[6453,635,1111,1380,6874,2050,8441,1684,7358],()=>a(14961)),_N_E=e.O()}]);