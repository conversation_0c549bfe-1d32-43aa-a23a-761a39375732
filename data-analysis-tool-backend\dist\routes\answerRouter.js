"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const answerController_1 = require("../controllers/answerController");
const express_1 = __importDefault(require("express"));
const checkPermission_1 = require("../middleware/checkPermission");
const router = express_1.default.Router();
const auth_1 = require("../middleware/auth");
router.use(auth_1.authenticate);
router.post("/", (0, checkPermission_1.checkPermission)([
    "manageProject",
    "addSubmissions",
]), answerController_1.createAnswer);
router.get("/", (0, checkPermission_1.checkPermission)([
    "manageProject",
    "viewSubmissions",
]), answerController_1.getAnswersBySubmission);
router.delete("/", (0, checkPermission_1.checkPermission)([
    "manageProject",
    "deleteSubmissions",
]), answerController_1.deleteAnswer);
router.patch("/multiple", (0, checkPermission_1.checkPermission)([
    "manageProject",
    "editSubmissions",
]), answerController_1.updateMultipleAnswers);
router.post("/multiple", 
// checkPermission("addSubmissions") as unknown as express.RequestHandler,
answerController_1.submitMultipleAnswer);
router.get("/:id", (0, checkPermission_1.checkPermission)([
    "manageProject",
    "viewSubmissions",
]), answerController_1.getAnswerById);
router.patch("/:id", (0, checkPermission_1.checkPermission)([
    "manageProject",
    "editSubmissions",
]), answerController_1.updateAnswer);
exports.default = router;
