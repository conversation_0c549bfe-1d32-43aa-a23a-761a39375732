import { promises } from "dns";
import { prisma } from "../utils/prisma";
import { Prisma, QuestionGroup } from "@prisma/client";
import { promise } from "zod";
import { group } from "console";

class QuestionGroupRepository {
  async create(data: {
    title: string;
    order: number;
    projectId: number;
    parentGroupId?: number;
    selectedQuestionIds?: number[];
  }): Promise<QuestionGroup> {
    return await prisma.$transaction(async (tx) => {
      // Step 1: Create QuestionGroup
      const newGroup = await tx.questionGroup.create({
        data: {
          title: data.title,
          order: data.order,
          projectId: data.projectId,
          parentGroupId: data.parentGroupId,
        },
      });

      // Step 2: Get max group-level position (root-level only)
      const lastGroupOrder = await tx.projectQuestionOrder.findFirst({
        where: {
          parentGroupId: null,
          type: "group",
        },
        orderBy: { position: "desc" },
      });

      const groupPosition = lastGroupOrder ? lastGroupOrder.position + 1 : 1;

      // Step 3: Create ProjectQuestionOrder for the group (root-level)
      await tx.projectQuestionOrder.create({
        data: {
          groupId: newGroup.id,
          parentGroupId: null,
          type: "group",
          position: groupPosition,
          projectId: data.projectId,
        },
      });

      // Step 4: For each selected question, set its position **within the group**
      if (data.selectedQuestionIds && data.selectedQuestionIds.length > 0) {
        await tx.question.updateMany({
          where: {
            id: { in: data.selectedQuestionIds },
          },
          data: {
            questionGroupId: newGroup.id,
          },
        });

        // Get current max position inside this group
        const lastQuestionOrder = await tx.projectQuestionOrder.findFirst({
          where: {
            parentGroupId: newGroup.id,
            type: "question",
          },
          orderBy: { position: "desc" },
        });

        let questionPosition = lastQuestionOrder
          ? lastQuestionOrder.position
          : 0;

        for (const questionId of data.selectedQuestionIds) {
          questionPosition += 1;

          // Check if this question already has a ProjectQuestionOrder
          const existingOrder = await tx.projectQuestionOrder.findFirst({
            where: {
              questionId,
              type: "question",
            },
          });

          if (existingOrder) {
            // Update it to assign it to the new group
            await tx.projectQuestionOrder.update({
              where: { id: existingOrder.id },
              data: {
                groupId: newGroup.id,
                parentGroupId: newGroup.id,
                position: questionPosition,
                projectId: data.projectId,
              },
            });
          } else {
            // Create new entry if none exists
            await tx.projectQuestionOrder.create({
              data: {
                questionId,
                groupId: newGroup.id,
                parentGroupId: newGroup.id,
                type: "question",
                position: questionPosition,
                projectId: data.projectId,
              },
            });
          }
        }
      }

      return newGroup;
    });
  }

  async delete(id: number): Promise<QuestionGroup> {
    return await prisma.questionGroup.delete({
      where: { id },
    });
  }

  async deleteManyQuestionByGroup(id: number): Promise<Prisma.BatchPayload> {
    return await prisma.question.deleteMany({
      where: {
        questionGroupId: id,
      },
    });
  }

  async ungroupQuestionsByGroup(id: number): Promise<Prisma.BatchPayload> {
    return await prisma.question.updateMany({
      where: {
        questionGroupId: id,
      },
      data: {
        questionGroupId: null,
      },
    });
  }

  async findById(id: number): Promise<QuestionGroup | null> {
    return await prisma.questionGroup.findUnique({
      where: { id },
      include: {
        question: {
          orderBy: { position: "asc" },
        },
      },
    });
  }

  async findAllByProject(projectId: number): Promise<QuestionGroup[]> {
    return await prisma.questionGroup.findMany({
      where: { projectId },
      select: {
        id: true,
        title: true,
        order: true,
        parentGroupId: true,
        projectId: true,
        createdAt: true,
        updatedAt: true,
        question: true,
      },
      orderBy: {
        order: "asc",
      },
    });
  }

  async update(id: number, updates: any): Promise<QuestionGroup> {
    return await prisma.questionGroup.update({
      where: { id },
      data: updates,
    });
  }

  async updateGroupInsideParentGroup(
    childGroupId: number,
    ParentGroupId: number
  ): Promise<QuestionGroup | null> {
    return await prisma.questionGroup.update({
      where: {
        id: childGroupId,
      },
      data: {
        parentGroupId: ParentGroupId,
      },
    });
  }
  async RemoveGroupFromParentGroup(
    groupId: number
  ): Promise<QuestionGroup | null> {
    return prisma.questionGroup.update({
      where: {
        id: groupId,
      },
      data: {
        parentGroupId: null,
      },
    });
  }

  async updateMultiplePositions(
    groupPositions: { id: number; order: number; parentGroupId?: number }[]
  ): Promise<QuestionGroup[]> {
    return await prisma.$transaction(async (tx) => {
      const updatedGroups: QuestionGroup[] = [];

      for (const { id, order, parentGroupId } of groupPositions) {
        const updatedGroup = await tx.questionGroup.update({
          where: { id },
          data: {
            order,
            ...(parentGroupId !== undefined && { parentGroupId })
          },
        });
        updatedGroups.push(updatedGroup);
      }

      return updatedGroups;
    });
  }
}

export default new QuestionGroupRepository();
