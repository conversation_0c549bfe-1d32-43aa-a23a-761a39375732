"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const prisma_1 = require("../utils/prisma");
class AnswerRepository {
    createAnswer(data) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.answer.create({
                data: {
                    formSubmissionId: data.submissionId,
                    questionId: data.questionId,
                    questionOptionId: data.questionOptionId,
                    isOtherOption: data.isOtherOption,
                    value: data.value,
                    answerType: data.answerType,
                    imageUrl: data.imageUrl,
                },
            });
        });
    }
    getAnswersBySubmission(formSubmissionId) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.answer.findMany({
                where: { formSubmissionId },
                include: {
                    question: true,
                },
            });
        });
    }
    getAnswerById(id) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.answer.findUnique({
                where: { id },
                include: {
                    question: true,
                },
            });
        });
    }
    // answerRepository.ts
    deleteAnswersBySubmissionIdAndQuestionId(submissionId, questionId, answerType) {
        return __awaiter(this, void 0, void 0, function* () {
            if (answerType === "selectmany") {
                return yield prisma_1.prisma.answer.deleteMany({
                    where: {
                        formSubmissionId: submissionId,
                        questionId: questionId,
                    },
                });
            }
            else {
                const answer = yield prisma_1.prisma.answer.findFirst({
                    where: {
                        formSubmissionId: submissionId,
                        questionId: questionId,
                    },
                });
                if (!answer)
                    return null;
                return yield prisma_1.prisma.answer.delete({
                    where: {
                        id: answer.id,
                    },
                });
            }
        });
    }
    findBySubmissionIdQuestion(formSubmissionId, questionId) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.answer.findFirst({
                where: {
                    formSubmissionId: formSubmissionId,
                    questionId: questionId,
                },
            });
        });
    }
    updateSelectManyAnswers(submissionId, questionId, values, questionOptionIds) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a;
            // Delete previous answers
            yield prisma_1.prisma.answer.deleteMany({
                where: {
                    formSubmissionId: submissionId,
                    questionId,
                    answerType: "selectmany",
                },
            });
            // Insert new answers
            const createdAnswers = [];
            for (let i = 0; i < values.length; i++) {
                const val = values[i];
                const optionId = (_a = questionOptionIds[i]) !== null && _a !== void 0 ? _a : null;
                const answer = yield prisma_1.prisma.answer.create({
                    data: {
                        formSubmissionId: submissionId,
                        questionId,
                        questionOptionId: optionId,
                        value: String(val),
                        answerType: "selectmany",
                    },
                });
                createdAnswers.push(answer);
            }
            return createdAnswers;
        });
    }
    findDuplicateAnswer(submissionId, questionId, answerType) {
        return __awaiter(this, void 0, void 0, function* () {
            if (answerType !== "selectmany") {
                return yield prisma_1.prisma.answer.findFirst({
                    where: {
                        formSubmissionId: submissionId,
                        questionId: questionId,
                        answerType: { not: "selectmany" },
                    },
                });
            }
        });
    }
    updateSingleAnswer(data) {
        return __awaiter(this, void 0, void 0, function* () {
            const existing = yield prisma_1.prisma.answer.findFirst({
                where: {
                    formSubmissionId: data.submissionId,
                    questionId: data.questionId,
                    answerType: { not: "selectmany" },
                },
            });
            if (existing) {
                return yield prisma_1.prisma.answer.update({
                    where: { id: existing.id },
                    data: {
                        value: String(data.value),
                        questionOptionId: data.questionOptionId,
                        isOtherOption: data.isOtherOption,
                        imageUrl: data.imageUrl,
                        answerType: data.answerType,
                    },
                });
            }
            else {
                return yield prisma_1.prisma.answer.create({
                    data: {
                        formSubmissionId: data.submissionId,
                        questionId: data.questionId,
                        questionOptionId: data.questionOptionId,
                        isOtherOption: data.isOtherOption,
                        value: String(data.value),
                        answerType: data.answerType,
                        imageUrl: data.imageUrl,
                    },
                });
            }
        });
    }
    findAllAnswers(projectId) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.answer.findMany({
                where: {
                    formSubmission: {
                        projectId: projectId,
                    },
                },
                include: {
                    question: true,
                    questionOption: true,
                    formSubmission: {
                        include: {
                            user: true,
                        },
                    },
                },
            });
        });
    }
    AddMultipleAnswer(answers) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.$transaction((tx) => __awaiter(this, void 0, void 0, function* () {
                var _a;
                const createdAnswers = [];
                for (const answer of answers) {
                    const created = yield tx.answer.create({
                        data: {
                            formSubmissionId: answer.formSubmissionId,
                            questionId: answer.questionId,
                            value: answer.value,
                            answerType: answer.answerType,
                            imageUrl: answer.imageUrl,
                            questionOptionId: answer.questionOptionId,
                            isOtherOption: (_a = answer.isOtherOption) !== null && _a !== void 0 ? _a : false,
                        },
                    });
                    createdAnswers.push(created);
                }
                return createdAnswers;
            }));
        });
    }
    deleteDependentAnswers(tx, questionId, formSubmissionId) {
        return __awaiter(this, void 0, void 0, function* () {
            // Delete the direct answer to this question
            yield tx.answer.deleteMany({
                where: {
                    questionId,
                    formSubmissionId,
                },
            });
            // Find options for this question that have follow-up questions
            const options = yield tx.questionOption.findMany({
                where: {
                    questionId,
                    nextQuestionId: {
                        not: null,
                    },
                },
                select: {
                    nextQuestionId: true,
                },
            });
            // Recursively delete dependent answers
            for (const opt of options) {
                if (opt.nextQuestionId) {
                    yield this.deleteDependentAnswers(tx, opt.nextQuestionId, formSubmissionId);
                }
            }
        });
    }
    UpdateMultipleAnswers(answers) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield prisma_1.prisma.$transaction((tx) => __awaiter(this, void 0, void 0, function* () {
                var _a, _b, _c, _d, _e, _f;
                const updatedAnswers = [];
                for (const answer of answers) {
                    const existingAnswer = yield tx.answer.findUnique({
                        where: { id: answer.id },
                    });
                    if (!existingAnswer) {
                        throw new Error(`Answer with id ${answer.id} not found`);
                    }
                    // SELECTMANY
                    if (answer.answerType === "selectmany") {
                        if (!Array.isArray(answer.questionOptionId)) {
                            throw new Error("Expected array of questionOptionIds for selectmany");
                        }
                        const existingGroupAnswers = yield tx.answer.findMany({
                            where: {
                                questionId: existingAnswer.questionId,
                                answerType: "selectmany",
                                formSubmissionId: existingAnswer.formSubmissionId,
                            },
                        });
                        const existingOptionIds = existingGroupAnswers
                            .map((a) => a.questionOptionId)
                            .sort();
                        const newOptionIds = [...answer.questionOptionId].sort();
                        const isSame = existingOptionIds.length === newOptionIds.length &&
                            existingOptionIds.every((id, idx) => id === newOptionIds[idx]);
                        if (!isSame) {
                            // Identify removed options to delete follow-up answers
                            const removedOptionIds = existingOptionIds.filter((oldId) => !newOptionIds.includes(oldId));
                            for (const removedOptionId of removedOptionIds) {
                                const oldOption = yield tx.questionOption.findUnique({
                                    where: { id: removedOptionId },
                                });
                                if (oldOption === null || oldOption === void 0 ? void 0 : oldOption.nextQuestionId) {
                                    yield this.deleteDependentAnswers(tx, oldOption.nextQuestionId, existingAnswer.formSubmissionId);
                                }
                            }
                            yield tx.answer.deleteMany({
                                where: {
                                    questionId: existingAnswer.questionId,
                                    answerType: "selectmany",
                                    formSubmissionId: existingAnswer.formSubmissionId,
                                },
                            });
                            const values = Array.isArray(answer.value)
                                ? answer.value
                                : typeof answer.value === "string"
                                    ? answer.value.split(",").map((v) => v.trim())
                                    : [];
                            for (let i = 0; i < answer.questionOptionId.length; i++) {
                                const created = yield tx.answer.create({
                                    data: {
                                        questionId: existingAnswer.questionId,
                                        value: (_a = values[i]) !== null && _a !== void 0 ? _a : "",
                                        answerType: "selectmany",
                                        imageUrl: answer.imageUrl,
                                        questionOptionId: answer.questionOptionId[i],
                                        isOtherOption: (_b = answer.isOtherOption) !== null && _b !== void 0 ? _b : false,
                                        formSubmissionId: answer.formSubmissionId,
                                    },
                                });
                                updatedAnswers.push(created);
                            }
                        }
                        else {
                            updatedAnswers.push(...existingGroupAnswers);
                        }
                    }
                    // SELECTONE
                    else if (answer.answerType === "selectone") {
                        if (existingAnswer.questionOptionId !== answer.questionOptionId) {
                            const oldOption = yield tx.questionOption.findUnique({
                                where: { id: existingAnswer.questionOptionId },
                            });
                            if (oldOption === null || oldOption === void 0 ? void 0 : oldOption.nextQuestionId) {
                                yield this.deleteDependentAnswers(tx, oldOption.nextQuestionId, existingAnswer.formSubmissionId);
                            }
                            yield tx.answer.delete({
                                where: {
                                    id: existingAnswer.id,
                                    formSubmissionId: existingAnswer.formSubmissionId,
                                },
                            });
                            const created = yield tx.answer.create({
                                data: {
                                    questionId: existingAnswer.questionId,
                                    value: String((_c = answer.value) !== null && _c !== void 0 ? _c : ""),
                                    answerType: "selectone",
                                    imageUrl: answer.imageUrl,
                                    questionOptionId: answer.questionOptionId,
                                    isOtherOption: (_d = answer.isOtherOption) !== null && _d !== void 0 ? _d : false,
                                    formSubmissionId: answer.formSubmissionId,
                                },
                            });
                            updatedAnswers.push(created);
                        }
                        else {
                            const updated = yield tx.answer.update({
                                where: { id: existingAnswer.id },
                                data: {
                                    value: String((_e = answer.value) !== null && _e !== void 0 ? _e : ""),
                                    imageUrl: answer.imageUrl,
                                    isOtherOption: answer.isOtherOption,
                                },
                            });
                            updatedAnswers.push(updated);
                        }
                    }
                    // OTHER TYPES
                    else {
                        const updated = yield tx.answer.update({
                            where: { id: answer.id },
                            data: {
                                value: String((_f = answer.value) !== null && _f !== void 0 ? _f : ""),
                                answerType: answer.answerType,
                                imageUrl: answer.imageUrl,
                                questionOptionId: Array.isArray(answer.questionOptionId)
                                    ? undefined
                                    : answer.questionOptionId,
                                isOtherOption: answer.isOtherOption,
                            },
                        });
                        updatedAnswers.push(updated);
                    }
                }
                return updatedAnswers;
            }));
        });
    }
}
exports.default = new AnswerRepository();
