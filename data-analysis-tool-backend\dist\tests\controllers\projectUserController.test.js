"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const projectUserController_1 = require("../../controllers/projectUserController");
const projectUserRepository_1 = __importDefault(require("../../repositories/projectUserRepository"));
const projectRepository_1 = __importDefault(require("../../repositories/projectRepository"));
const userRepository_1 = __importDefault(require("../../repositories/userRepository"));
// Mock the repositories
jest.mock("../../repositories/projectUserRepository");
jest.mock("../../repositories/projectRepository");
jest.mock("../../repositories/userRepository");
describe("Project User Controller", () => {
    let mockRequest;
    let mockResponse;
    let responseObject = {};
    beforeEach(() => {
        // Reset mocks before each test
        jest.clearAllMocks();
        jest.resetAllMocks();
        // Setup mock response
        mockResponse = {
            status: jest.fn().mockReturnThis(),
            json: jest.fn().mockImplementation((result) => {
                responseObject = result;
                return mockResponse;
            }),
        };
        // Reset response object
        responseObject = {};
        // Setup default authenticated user
        mockRequest = {
            user: {
                id: 1,
            },
            params: {},
            body: {},
        };
    });
    describe("createProjectUser", () => {
        beforeEach(() => {
            mockRequest.body = {
                userId: 2,
                projectId: 1,
                permission: {
                    viewForm: true,
                    editForm: false,
                },
            };
        });
        it("should create a project user successfully", () => __awaiter(void 0, void 0, void 0, function* () {
            const mockUser = {
                id: 2,
                name: "Test User",
                email: "<EMAIL>",
            };
            const mockProject = {
                id: 1,
                name: "Test Project",
                description: "Test Description",
                sector: "other",
                user: {
                    id: 1,
                    name: "Owner",
                    email: "<EMAIL>",
                },
                lastDeployedAt: null,
                lastSubmissionAt: null,
                createdAt: new Date(),
                updatedAt: new Date(),
            };
            const mockProjectUser = {
                id: 1,
                userId: 2,
                projectId: 1,
                permission: {
                    viewForm: true,
                    editForm: false,
                },
                createdAt: new Date(),
                updatedAT: new Date(),
            };
            userRepository_1.default.findById.mockResolvedValue(mockUser);
            projectRepository_1.default.findById.mockResolvedValue(mockProject);
            projectUserRepository_1.default.findUserProject.mockResolvedValue(null);
            projectUserRepository_1.default.create.mockResolvedValue(mockProjectUser);
            yield (0, projectUserController_1.createProjectUser)(mockRequest, mockResponse);
            expect(userRepository_1.default.findById).toHaveBeenCalledWith(2);
            expect(projectRepository_1.default.findById).toHaveBeenCalledWith(1);
            expect(projectUserRepository_1.default.findUserProject).toHaveBeenCalledWith(2, 1);
            expect(projectUserRepository_1.default.create).toHaveBeenCalledWith(2, 1, {
                viewForm: true,
                editForm: false,
            });
            expect(mockResponse.status).toHaveBeenCalledWith(200);
            expect(responseObject).toHaveProperty("success", true);
            expect(responseObject).toHaveProperty("message", "user added successly to project");
            expect(responseObject.data).toHaveProperty("userProject", mockProjectUser);
        }));
        it("should return 404 when user is not found", () => __awaiter(void 0, void 0, void 0, function* () {
            userRepository_1.default.findById.mockResolvedValue(null);
            yield (0, projectUserController_1.createProjectUser)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(404);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "user not found");
            expect(projectUserRepository_1.default.create).not.toHaveBeenCalled();
        }));
        it("should return 400 when trying to add the owner", () => __awaiter(void 0, void 0, void 0, function* () {
            mockRequest.body.userId = 1; // Same as the owner ID
            const mockUser = {
                id: 1,
                name: "Owner",
                email: "<EMAIL>",
            };
            userRepository_1.default.findById.mockResolvedValue(mockUser);
            yield (0, projectUserController_1.createProjectUser)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(400);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "You are the owner of the project. No need to add yourself");
            expect(projectUserRepository_1.default.create).not.toHaveBeenCalled();
        }));
        it("should return 404 when project is not found", () => __awaiter(void 0, void 0, void 0, function* () {
            const mockUser = {
                id: 2,
                name: "Test User",
                email: "<EMAIL>",
            };
            userRepository_1.default.findById.mockResolvedValue(mockUser);
            projectRepository_1.default.findById.mockResolvedValue(null);
            yield (0, projectUserController_1.createProjectUser)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(404);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "project not found");
            expect(projectUserRepository_1.default.create).not.toHaveBeenCalled();
        }));
        it("should return 400 when user is already associated with project", () => __awaiter(void 0, void 0, void 0, function* () {
            const mockUser = {
                id: 2,
                name: "Test User",
                email: "<EMAIL>",
            };
            const mockProject = {
                id: 1,
                name: "Test Project",
                description: "Test Description",
                sector: "other",
                user: {
                    id: 1,
                    name: "Owner",
                    email: "<EMAIL>",
                },
                lastDeployedAt: null,
                lastSubmissionAt: null,
                createdAt: new Date(),
                updatedAt: new Date(),
            };
            const existingProjectUser = {
                id: 1,
                userId: 2,
                projectId: 1,
                permission: {
                    viewForm: true,
                },
            };
            userRepository_1.default.findById.mockResolvedValue(mockUser);
            projectRepository_1.default.findById.mockResolvedValue(mockProject);
            projectUserRepository_1.default.findUserProject.mockResolvedValue(existingProjectUser);
            yield (0, projectUserController_1.createProjectUser)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(400);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "user already associated with project");
            expect(projectUserRepository_1.default.create).not.toHaveBeenCalled();
        }));
        it("should return 400 for invalid input", () => __awaiter(void 0, void 0, void 0, function* () {
            mockRequest.body = {}; // Missing required fields
            yield (0, projectUserController_1.createProjectUser)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(400);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message");
            expect(projectUserRepository_1.default.create).not.toHaveBeenCalled();
        }));
        it("should handle server errors", () => __awaiter(void 0, void 0, void 0, function* () {
            userRepository_1.default.findById.mockRejectedValue(new Error("Database error"));
            yield (0, projectUserController_1.createProjectUser)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(500);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "error creating qustion");
        }));
    });
    describe("updateProjectUser", () => {
        beforeEach(() => {
            mockRequest.body = {
                userId: 2,
                projectId: 1,
                permission: {
                    viewForm: true,
                    editForm: true,
                },
            };
        });
        it("should update project user permissions successfully", () => __awaiter(void 0, void 0, void 0, function* () {
            const updatedProjectUser = {
                id: 1,
                userId: 2,
                projectId: 1,
                permission: {
                    viewForm: true,
                    editForm: true,
                },
                createdAt: new Date(),
                updatedAT: new Date(),
            };
            projectUserRepository_1.default.updateUserPermission.mockResolvedValue(updatedProjectUser);
            yield (0, projectUserController_1.updateProjectUser)(mockRequest, mockResponse);
            expect(projectUserRepository_1.default.updateUserPermission).toHaveBeenCalledWith(2, 1, { viewForm: true, editForm: true });
            expect(mockResponse.status).toHaveBeenCalledWith(200);
            // Controller returns success: false even on successful update
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "permission updated success");
            expect(responseObject.data).toHaveProperty("updatedProject", updatedProjectUser);
        }));
        it("should return 400 for invalid input", () => __awaiter(void 0, void 0, void 0, function* () {
            mockRequest.body = {}; // Missing required fields
            yield (0, projectUserController_1.updateProjectUser)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(400);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message");
            expect(projectUserRepository_1.default.updateUserPermission).not.toHaveBeenCalled();
        }));
        it("should handle server errors", () => __awaiter(void 0, void 0, void 0, function* () {
            projectUserRepository_1.default.updateUserPermission.mockRejectedValue(new Error("Database error"));
            yield (0, projectUserController_1.updateProjectUser)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(500);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "error creating qustion");
        }));
    });
    describe("deletProjectUser", () => {
        beforeEach(() => {
            mockRequest.body = {
                userId: 2,
                projectId: 1,
            };
        });
        it("should delete a project user successfully", () => __awaiter(void 0, void 0, void 0, function* () {
            const deletedProjectUser = {
                id: 1,
                userId: 2,
                projectId: 1,
                permission: {
                    viewForm: true,
                },
                createdAt: new Date(),
                updatedAT: new Date(),
            };
            projectUserRepository_1.default.deleteUserFromProject.mockResolvedValue(deletedProjectUser);
            yield (0, projectUserController_1.deletProjectUser)(mockRequest, mockResponse);
            expect(projectUserRepository_1.default.deleteUserFromProject).toHaveBeenCalledWith(2, 1);
            expect(mockResponse.status).toHaveBeenCalledWith(200);
            // Controller returns success: false even on successful deletion
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "user delete succes");
        }));
        it("should handle server errors", () => __awaiter(void 0, void 0, void 0, function* () {
            projectUserRepository_1.default.deleteUserFromProject.mockRejectedValue(new Error("Database error"));
            yield (0, projectUserController_1.deletProjectUser)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(500);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "error creating qustion");
        }));
    });
    describe("getAllProjectUser", () => {
        beforeEach(() => {
            // Set projectId in params instead of body since controller uses req.params.projectId
            mockRequest.params = {
                projectId: "1", // Params are strings in Express
            };
        });
        it("should get all project users successfully", () => __awaiter(void 0, void 0, void 0, function* () {
            const mockProjectUsers = [
                {
                    id: 1,
                    userId: 2,
                    projectId: 1,
                    permission: {
                        viewForm: true,
                    },
                    createdAt: new Date(),
                    updatedAT: new Date(),
                },
                {
                    id: 2,
                    userId: 3,
                    projectId: 1,
                    permission: {
                        viewForm: true,
                        editForm: true,
                    },
                    createdAt: new Date(),
                    updatedAT: new Date(),
                },
            ];
            projectUserRepository_1.default.allUsers.mockResolvedValue(mockProjectUsers);
            yield (0, projectUserController_1.getAllProjectUser)(mockRequest, mockResponse);
            // Number(req.params.projectId) is used in the controller
            expect(projectUserRepository_1.default.allUsers).toHaveBeenCalledWith(1);
            expect(mockResponse.status).toHaveBeenCalledWith(200);
            expect(responseObject).toHaveProperty("success", true);
            expect(responseObject).toHaveProperty("message", "project user fetched success");
            expect(responseObject.data).toHaveProperty("AllUser", mockProjectUsers);
        }));
        it("should handle server errors", () => __awaiter(void 0, void 0, void 0, function* () {
            projectUserRepository_1.default.allUsers.mockRejectedValue(new Error("Database error"));
            yield (0, projectUserController_1.getAllProjectUser)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(500);
            expect(responseObject).toHaveProperty("success", false);
            // Match the actual error message in the controller
            expect(responseObject).toHaveProperty("message", "error fetching users");
        }));
    });
    describe("copyProjectUsers", () => {
        beforeEach(() => {
            mockRequest.body = {
                sourceProjectId: 1,
                targetProjectId: 2,
            };
            mockRequest.user = {
                id: 1,
            };
        });
        it("should copy project users successfully", () => __awaiter(void 0, void 0, void 0, function* () {
            const sourceProject = {
                id: 1,
                name: "Source Project",
                description: "Source Description",
                sector: "other",
                user: {
                    id: 1,
                    name: "Owner",
                    email: "<EMAIL>",
                },
                lastDeployedAt: null,
                lastSubmissionAt: null,
                createdAt: new Date(),
                updatedAt: new Date(),
            };
            const targetProject = {
                id: 2,
                name: "Target Project",
                description: "Target Description",
                sector: "other",
                user: {
                    id: 1,
                    name: "Owner",
                    email: "<EMAIL>",
                },
                lastDeployedAt: null,
                lastSubmissionAt: null,
                createdAt: new Date(),
                updatedAt: new Date(),
            };
            const sourceUsers = [
                {
                    id: 1,
                    userId: 2,
                    projectId: 1,
                    permission: {
                        viewForm: true,
                        editForm: false,
                    },
                    createdAt: new Date(),
                    updatedAT: new Date(),
                },
                {
                    id: 2,
                    userId: 3,
                    projectId: 1,
                    permission: {
                        viewForm: true,
                        editForm: true,
                    },
                    createdAt: new Date(),
                    updatedAT: new Date(),
                },
                {
                    id: 3,
                    userId: 1, // Owner, should be skipped
                    projectId: 1,
                    permission: {
                        viewForm: true,
                    },
                    createdAt: new Date(),
                    updatedAT: new Date(),
                },
            ];
            const newUser1 = {
                id: 4,
                userId: 2,
                projectId: 2,
                permission: {
                    viewForm: true,
                    editForm: false,
                },
                createdAt: new Date(),
                updatedAT: new Date(),
            };
            const newUser2 = {
                id: 5,
                userId: 3,
                projectId: 2,
                permission: {
                    viewForm: true,
                    editForm: true,
                },
                createdAt: new Date(),
                updatedAT: new Date(),
            };
            projectRepository_1.default.findById
                .mockResolvedValueOnce(sourceProject)
                .mockResolvedValueOnce(targetProject);
            projectUserRepository_1.default.allUsers.mockResolvedValue(sourceUsers);
            projectUserRepository_1.default.findUserProject.mockResolvedValue(null);
            projectUserRepository_1.default.create
                .mockResolvedValueOnce(newUser1)
                .mockResolvedValueOnce(newUser2);
            yield (0, projectUserController_1.copyProjectUsers)(mockRequest, mockResponse);
            expect(projectRepository_1.default.findById).toHaveBeenCalledWith(1);
            expect(projectRepository_1.default.findById).toHaveBeenCalledWith(2);
            expect(projectUserRepository_1.default.allUsers).toHaveBeenCalledWith(1);
            expect(projectUserRepository_1.default.create).toHaveBeenCalledTimes(2);
            expect(mockResponse.status).toHaveBeenCalledWith(200);
            expect(responseObject).toHaveProperty("success", true);
            expect(responseObject).toHaveProperty("message", "2 users copied to target project successfully.");
            expect(responseObject).toHaveProperty("data", [newUser1, newUser2]);
        }));
        it("should return 404 when user is not found", () => __awaiter(void 0, void 0, void 0, function* () {
            mockRequest.user = undefined;
            yield (0, projectUserController_1.copyProjectUsers)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(404);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "User not found");
        }));
        it("should return 404 when source or target project is not found", () => __awaiter(void 0, void 0, void 0, function* () {
            projectRepository_1.default.findById
                .mockResolvedValueOnce(null) // Source project not found
                .mockResolvedValueOnce({}); // Target project found
            yield (0, projectUserController_1.copyProjectUsers)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(404);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "One or both projects not found");
        }));
        it("should return 403 when user is not the owner of target project", () => __awaiter(void 0, void 0, void 0, function* () {
            const sourceProject = {
                id: 1,
                name: "Source Project",
                description: "Source Description",
                sector: "other",
                user: {
                    id: 1,
                    name: "Owner",
                    email: "<EMAIL>",
                },
                lastDeployedAt: null,
                lastSubmissionAt: null,
                createdAt: new Date(),
                updatedAt: new Date(),
            };
            const targetProject = {
                id: 2,
                name: "Target Project",
                description: "Target Description",
                sector: "other",
                user: {
                    id: 2, // Different owner
                    name: "Another Owner",
                    email: "<EMAIL>",
                },
                lastDeployedAt: null,
                lastSubmissionAt: null,
                createdAt: new Date(),
                updatedAt: new Date(),
            };
            projectRepository_1.default.findById
                .mockResolvedValueOnce(sourceProject)
                .mockResolvedValueOnce(targetProject);
            yield (0, projectUserController_1.copyProjectUsers)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(403);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "You are not the owner of the target project");
        }));
        it("should handle server errors", () => __awaiter(void 0, void 0, void 0, function* () {
            projectRepository_1.default.findById.mockRejectedValue(new Error("Database error"));
            yield (0, projectUserController_1.copyProjectUsers)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(500);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "error copying project user");
        }));
    });
});
