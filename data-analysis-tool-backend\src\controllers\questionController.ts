import { Request, Response } from "express";
import { ApiResponse } from "../utils/ApiResponse";
import {
  questionPositionsSchema,
  questionSchema,
  questionWithFileSchema,
} from "../validators/questionValidators";
import { InputType, Status } from "@prisma/client";
import QuestionRepository from "../repositories/questionRepository";
import questionRepository from "../repositories/questionRepository";
import projectRepository from "../repositories/projectRepository";
import { prisma } from "../utils/prisma";
import { getProjectById } from "./projectController";
import { ZodUnknownDef } from "zod";
import ExcelJs from "exceljs";
import path from "path";
import fs from "fs";

export interface MulterRequest extends Request {
  file: Express.Multer.File;
}
interface userRequest extends Request {
  user?: {
    id: number;
  };
}

export const getAllQuestion = async (req: userRequest, res: Response) => {
  try {
    if (!req.user || !req.user.id) {
      return res.status(404).json({
        success: false,
        message: "user not found",
      });
    }
    const userId = Number(req.user.id);

    // Get projectId from either path parameter or query parameter
    const projectId = Number(req.params.projectId || req.query.projectId);

    if (!projectId) {
      return res.status(400).json({
        success: false,
        message: "Project ID is required",
      });
    }

    const questions = await questionRepository.findAll(projectId);

    return res
      .status(200)
      .json({ message: "Successfully fetched questions.", questions });
  } catch (error: unknown) {
    return res.status(500).json({
      success: false,
      message: "error fetching questions",
      error: error instanceof Error ? error.message : "unexpected error",
    });
  }
};

export const createQuestion = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const projectId = Number(req.params.projectId);

    // Pre-process form data for proper type conversion
    if (req.file) {
      // Convert string values to appropriate types
      if (req.body.isRequired !== undefined) {
        req.body.isRequired = req.body.isRequired === "true";
      }
      if (req.body.position !== undefined) {
        req.body.position = Number(req.body.position);
      }

      // For file uploads, we don't expect questionOptions in the request body
      // They will be parsed from the file
      req.body.questionOptions = undefined;
    }

    // Choose the appropriate validation schema based on whether a file is uploaded
    const validationSchema = req.file ? questionWithFileSchema : questionSchema;

    // Parse and validate the body
    const result = validationSchema.safeParse(req.body);
    if (!result.success) {
      res.status(400).json({
        success: false,
        errors: result.error.flatten().fieldErrors,
      });
      return;
    }

    const {
      questionOptions = [],
      conditions = [],
      ...questionData
    } = result.data as any;

    // Handle Excel file if present
    let excelOptions: {
      label: string;
      code: string;
      nextQuestionId?: number | null;
    }[] = [];

    if (req.file) {
      try {
        const workbook = new ExcelJs.Workbook();
        const filePath = path.join(
          __dirname,
          "../../tmp/my-uploads",
          req.file.filename
        );

        await workbook.xlsx.readFile(filePath);
        const worksheet = workbook.worksheets[0];

        worksheet.eachRow((row, rowNumber) => {
          if (rowNumber === 1) return; // Skip header row

          // Parse data from Excel
          if (Array.isArray(row.values)) {
            const [, label, code, nextQuestionIdRaw] = row.values;

            if (label && code) {
              excelOptions.push({
                label: String(label),
                code: String(code),
                nextQuestionId: nextQuestionIdRaw
                  ? Number(nextQuestionIdRaw)
                  : null,
              });
            }
          }
        });

        // Remove uploaded file
        fs.unlinkSync(filePath);

        // Validate that we have at least one option from the Excel file
        if (
          (questionData.inputType === InputType.selectone ||
            questionData.inputType === InputType.selectmany) &&
          excelOptions.length === 0
        ) {
          res.status(400).json({
            success: false,
            message:
              "The Excel file must contain at least one valid option for select input types",
          });
          return;
        }
      } catch (error) {
        console.error("Error processing Excel file:", error);
        res.status(400).json({
          success: false,
          message:
            "Failed to process the Excel file. Please ensure it's a valid Excel file with the correct format.",
        });
        return;
      }
    }

    // Combine options from request body and Excel file
    const allOptions = [...(questionOptions || []), ...excelOptions];

    // For select input types, ensure we have options (either from body or Excel)
    if (
      (questionData.inputType === InputType.selectone ||
        questionData.inputType === InputType.selectmany) &&
      allOptions.length === 0
    ) {
      res.status(400).json({
        success: false,
        message: "Options are required for select input types",
      });
      return;
    }

    // Start transaction
    const createdQuestion = await prisma.$transaction(async (tx) => {
      const question = await tx.question.create({
        data: {
          ...questionData,
          projectId,
        },
      });

      if (allOptions.length > 0) {
        await tx.questionOption.createMany({
          data: allOptions.map((option) => ({
            label: option.label,
            sublabel: option.sublabel,
            code: option.code,
            nextQuestionId: option.nextQuestionId || null,
            questionId: question.id,
          })),
        });
      }

      if (conditions && conditions.length > 0) {
        await tx.questionCondition.createMany({
          data: conditions.map((cond: { operator: any; value: any }) => ({
            operator: cond.operator,
            value: cond.value,
            questionId: question.id,
          })),
        });
      }

      const lastOrder = await tx.projectQuestionOrder.findFirst({
        where: {
          parentGroupId: questionData.parentGroupId ?? null,
        },
        orderBy: {
          position: "desc",
        },
      });

      const nextPosition = lastOrder ? lastOrder.position + 1 : 1;

      await tx.projectQuestionOrder.create({
        data: {
          questionId: question.id,
          groupId: questionData.groupId ?? null,
          type: "question",
          position: nextPosition,
          parentGroupId: questionData.parentGroupId ?? null,
          projectId:projectId
        },
      });

      return question;
    });

    res
      .status(200)
      .json(
        new ApiResponse(
          200,
          { question: createdQuestion },
          "Question created successfully"
        )
      );
  } catch (error: unknown) {
    console.error("Error creating question:", error);
    res.status(500).json({
      success: false,
      message: "Failed to create question",
      error: error instanceof Error ? error.message : "Unexpected error",
    });
  }
};

export const updateQuestion = async (
  req: userRequest,
  res: Response
): Promise<void> => {
  try {
    const id = Number(req.params.id);
    const userId = req.user?.id;

    if (!userId || isNaN(id)) {
      res.status(400).json({
        success: false,
        message: "Invalid request",
      });
      return;
    }
    const existingQuestion = await QuestionRepository.findById(id);

    if (!existingQuestion) {
      res.status(404).json({
        success: false,
        message: "Question not found",
      });
      return;
    }

    if (existingQuestion.projectId === undefined) {
      res.status(500).json({
        success: false,
        message: "Question has no associated project",
      });
      return;
    }

    const isOwner = await QuestionRepository.isPorjectOwner(
      userId,
      existingQuestion.projectId
    );
    if (!isOwner) {
      res.status(403).json({
        success: false,
        message: "You are not the project owner",
      });
      return;
    }

    // Parse the request body without requiring all fields
    const parseResult = questionSchema.safeParse({
      ...req.body,
      // Add dummy values for any required fields that aren't in the update
      // These won't actually be used for the update, just to pass validation
      label: req.body.label || existingQuestion.label,
      inputType: req.body.inputType || existingQuestion.inputType,
      position: req.body.position || existingQuestion.position,
    });

    if (!parseResult.success) {
      res.status(400).json({
        success: false,
        message: "Validation error",
        errors: parseResult.error.errors,
      });
      return;
    }

    // Only include fields that were actually in the request body
    // Only include fields that were actually in the request body
    const validatedData: Record<string, any> = {};
    const fields = [
      "label",
      "inputType",
      "isRequired",
      "hint",
      "placeholder",
      "position",
      "questionOptions",
      "conditions",
    ] as const;

    for (const field of fields) {
      if (req.body[field] !== undefined) {
        // Rename questionOptions to options
        if (field === "questionOptions") {
          validatedData.options = (parseResult.data as any)[field];
        } else {
          validatedData[field] = (parseResult.data as any)[field];
        }
      }
    }

    // Check if options are required for select input types
    if (
      validatedData.inputType === "selectone" ||
      validatedData.inputType === "selectmany"
    ) {
      if (
        !validatedData.options ||
        !Array.isArray(validatedData.options) ||
        validatedData.options.length === 0
      ) {
        res.status(400).json({
          success: false,
          message: "Options must be provided for select input types",
        });
        return;
      }
    }

    // Use the updated repository method to handle options and conditions
    const updatedQuestion = await QuestionRepository.updateById(
      id,
      validatedData
    );

    res
      .status(200)
      .json(
        new ApiResponse(
          200,
          { question: updatedQuestion },
          "question updated success"
        )
      );
  } catch (error: unknown) {
    res.status(500).json({
      success: false,
      message: "error updating question",
      error: error instanceof Error ? error.message : "unexpected error",
    });
    return;
  }
};

export const deleteQuestion = async (req: userRequest, res: Response) => {
  try {
    const userId = req.user?.id;
    const id = Number(req.params.id);

    if (!userId || isNaN(id)) {
      return res.status(400).json({
        message: "Invalid request: User ID or Question ID is missing",
        success: false,
      });
    }
    const currentQuestion = await questionRepository.findById(id);
    if (!currentQuestion) {
      return res.status(404).json({
        message: "Question not found",
        success: false,
      });
    }
    const isProjectOwner = await questionRepository.isPorjectOwner(
      userId,
      currentQuestion.projectId! // Add ! to assert that projectId is not undefined
    );

    if (!isProjectOwner) {
      return res.status(403).json({
        message: "Current user cannot delete question from this project",
        succcess: false,
      });
    }

    await questionRepository.deleteQuestion(id);

    return res.status(200).json({
      message: "Successfully deleted question",
      success: true,
    });
  } catch (error: unknown) {
    return res.status(500).json({
      message: error instanceof Error ? error.message : "unexpected error",
      success: false,
    });
  }
};

export const duplicateQuestion = async (req: userRequest, res: Response) => {
  try {
    const userId = req.user?.id;
    const id = Number(req.params.id);

    if (!userId || isNaN(id)) {
      return res.status(400).json({
        message: "Invalid request: User ID or Question ID is missing",
        success: false,
      });
    }
    const currentQuestion = await questionRepository.findById(id);
    if (!currentQuestion) {
      return res.status(404).json({
        message: "Question not found",
        success: false,
      });
    }
    const isProjectOwner = await questionRepository.isPorjectOwner(
      userId,
      currentQuestion.projectId
    );

    if (!isProjectOwner) {
      return res.status(403).json({
        message: "Current user cannot delete question from this project",
        succcess: false,
      });
    }

    const duplicatedQuestion = await questionRepository.duplicateQuestion(
      id,
      currentQuestion.projectId
    );

    return res.status(200).json({
      message: "Successfully duplicated the question.",
      success: true,
      duplicatedQuestion,
    });
  } catch (error: unknown) {
    return res.status(500).json({
      message: error instanceof Error ? error.message : "unexpected error",
      success: false,
    });
  }
};

export const updateQuestionPositions = async (
  req: userRequest,
  res: Response
): Promise<void> => {
  try {
    const userId = req.user?.id;
    const projectId = Number(req.query.projectId);

    if (!userId || isNaN(projectId)) {
      res.status(400).json({
        success: false,
        message: "Invalid request - missing userId or projectId",
      });
      return;
    }

    // Validate request body structure using the schema
    const parseResult = questionPositionsSchema.safeParse(req.body);
    if (!parseResult.success) {
      res.status(400).json({
        success: false,
        message: "Validation error",
        errors: parseResult.error.flatten().fieldErrors,
      });
      return;
    }

    const { questionPositions } = parseResult.data;

    // Check if user owns the project
    const isOwner = await questionRepository.isPorjectOwner(userId, projectId);
    if (!isOwner) {
      res.status(403).json({
        success: false,
        message: "You are not the project owner",
      });
      return;
    }

    // Update positions in bulk
    const updatedQuestions = await questionRepository.updateMultiplePositions(
      questionPositions
    );

    res.status(200).json({
      success: true,
      message: "Question positions updated successfully",
      data: { questions: updatedQuestions },
    });
  } catch (error: unknown) {
    console.error("Error in updateQuestionPositions:", error);
    res.status(500).json({
      success: false,
      message: "Error updating question positions",
      error: error instanceof Error ? error.message : "Unexpected error",
    });
  }
};

// NEW UNIFIED POSITION SYSTEM: Update positions using ProjectQuestionOrder schema
export const updateUnifiedPositions = async (
  req: userRequest,
  res: Response
): Promise<void> => {
  try {
    const userId = req.user?.id;
    const projectId = Number(req.query.projectId);

    if (!userId || isNaN(projectId)) {
      res.status(400).json({
        success: false,
        message: "Invalid request - missing userId or projectId",
      });
      return;
    }

    const { positionUpdates } = req.body;

    if (!positionUpdates || !Array.isArray(positionUpdates)) {
      res.status(400).json({
        success: false,
        message: "Invalid request - positionUpdates array is required",
      });
      return;
    }

    // Validate each position update
    for (const update of positionUpdates) {
      if (!update.id || !update.type || typeof update.position !== 'number') {
        res.status(400).json({
          success: false,
          message: "Invalid position update - id, type, and position are required",
        });
        return;
      }

      if (!['question', 'group'].includes(update.type)) {
        res.status(400).json({
          success: false,
          message: "Invalid type - must be 'question' or 'group'",
        });
        return;
      }
    }

    // Check if user owns the project
    const isOwner = await questionRepository.isPorjectOwner(userId, projectId);
    if (!isOwner) {
      res.status(403).json({
        success: false,
        message: "You are not the project owner",
      });
      return;
    }

    // Update positions using unified system
    const result = await questionRepository.updateUnifiedPositions(
      projectId,
      positionUpdates
    );

    res.status(200).json({
      success: true,
      message: "Positions updated successfully using unified system",
      data: result,
    });
  } catch (error: unknown) {
    console.error("Error in updateUnifiedPositions:", error);
    res.status(500).json({
      success: false,
      message: "Error updating unified positions",
      error: error instanceof Error ? error.message : "Unexpected error",
    });
  }
};
