(()=>{var e={};e.id=8586,e.ids=[8586],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10125:(e,t,s)=>{"use strict";s.d(t,{Notification:()=>p});var r=s(60687),a=s(43210),o=s(54864),n=s(88920),i=s(57101),l=s(19150),d=s(14719),c=s(43649),u=s(93613);let p=()=>{let e=(0,o.wA)(),{message:t,type:s,visible:p}=(0,o.d4)(e=>e.notification);(0,a.useEffect)(()=>{if(p){let t=setTimeout(()=>{e((0,l._b)())},5e3);return()=>clearTimeout(t)}},[p,e]);let m="success"===s?(0,r.jsx)(d.A,{}):"warning"===s?(0,r.jsx)(c.A,{}):(0,r.jsx)(u.A,{});return(0,r.jsx)(n.N,{children:p&&(0,r.jsxs)(i.P.div,{className:`z-50 fixed top-0 right-0 m-4 px-4 py-2 rounded font-semibold w-auto max-w-xs flex items-center gap-2 cursor-pointer ${"success"===s?"bg-green-500 hover:bg-green-600":"warning"===s?"bg-yellow-500 hover:bg-yellow-600":"bg-red-500 hover:bg-red-600"} transition-colors duration-300`,onClick:()=>e((0,l._b)()),initial:{opacity:0,y:-20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.3,ease:"easeIn"},children:[(0,r.jsx)("span",{className:"text-2xl",children:m}),(0,r.jsx)("span",{className:"break-words neutral-100space-normal",children:t})]})})}},10271:(e,t,s)=>{"use strict";s.d(t,{ReactQueryProvider:()=>l});var r=s(60687),a=s(43210),o=s(39091),n=s(8693),i=s(9124);let l=({children:e})=>{let[t]=(0,a.useState)(()=>new o.E({defaultOptions:{queries:{staleTime:3e5,refetchOnWindowFocus:!1}}}));return(0,r.jsxs)(n.Ht,{client:t,children:[e,(0,r.jsx)(i.E,{initialIsOpen:!1})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11674:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var r=s(65239),a=s(48088),o=s(88170),n=s.n(o),i=s(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);s.d(t,l);let d={children:["",{children:["[locale]",{children:["(auth)",{children:["reset-password",{children:["change-password",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,13689)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(auth)\\reset-password\\change-password\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,72121)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,58014)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(auth)\\reset-password\\change-password\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/(auth)/reset-password/change-password/page",pathname:"/[locale]/reset-password/change-password",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},12412:e=>{"use strict";e.exports=require("assert")},12597:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},12810:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let r=s(51060).A.create({baseURL:"http://localhost:4000/api",headers:{"Content-Type":"application/json"},withCredentials:!0});r.interceptors.request.use(e=>e,e=>Promise.reject(e)),r.interceptors.response.use(e=>e,e=>("ERR_NETWORK"===e.code&&console.error("Network error - Please check if the backend server is running"),Promise.reject(e)));let a=r},13689:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var r=s(37413),a=s(61120),o=s(62366);function n(){return(0,r.jsx)(a.Suspense,{fallback:(0,r.jsx)("div",{children:"Loading..."}),children:(0,r.jsx)(o.ChangePasswordPage,{})})}},13861:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},16319:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,86346,23)),Promise.resolve().then(s.t.bind(s,27924,23)),Promise.resolve().then(s.t.bind(s,35656,23)),Promise.resolve().then(s.t.bind(s,40099,23)),Promise.resolve().then(s.t.bind(s,38243,23)),Promise.resolve().then(s.t.bind(s,28827,23)),Promise.resolve().then(s.t.bind(s,62763,23)),Promise.resolve().then(s.t.bind(s,97173,23))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19150:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>n,Ds:()=>a,_b:()=>o});let r=(0,s(9317).Z0)({name:"notification",initialState:{message:"",type:"",visible:!1},reducers:{showNotification:(e,t)=>{e.message=t.payload.message,e.type=t.payload.type,e.visible=!0},hideNotification:e=>{e.message="",e.type="",e.visible=!1}}}),{showNotification:a,hideNotification:o}=r.actions,n=r.reducer},21820:e=>{"use strict";e.exports=require("os")},22747:(e,t,s)=>{Promise.resolve().then(s.bind(s,76708))},26946:(e,t,s)=>{Promise.resolve().then(s.bind(s,10125)),Promise.resolve().then(s.bind(s,10271)),Promise.resolve().then(s.bind(s,49271))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28559:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35790:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>n,l:()=>o,yg:()=>a});let r=(0,s(9317).Z0)({name:"createLibraryItem",initialState:{visible:!1},reducers:{showCreateLibraryModal:e=>{e.visible=!0},hideCreateLibraryModal:e=>{e.visible=!1}}}),{showCreateLibraryModal:a,hideCreateLibraryModal:o}=r.actions,n=r.reducer},36039:(e,t,s)=>{Promise.resolve().then(s.bind(s,45196))},38038:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("shield-check",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},42895:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>l,Le:()=>n,jB:()=>i,tQ:()=>a,x9:()=>o});let r=(0,s(9317).Z0)({name:"auth",initialState:{status:"loading",user:null,error:null},reducers:{setAuthenticatedUser:(e,t)=>{e.status="authenticated",e.user=t.payload,e.error=null},setUnauthenticated:e=>{e.status="unauthenticated",e.user=null,e.error=null},setAuthLoading:e=>{e.status="loading"},setAuthError:(e,t)=>{e.status="unauthenticated",e.error=t.payload,e.user=null}}}),{setAuthenticatedUser:a,setUnauthenticated:o,setAuthLoading:n,setAuthError:i}=r.actions,l=r.reducer},44395:(e,t,s)=>{"use strict";s.d(t,{Notification:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call Notification() from the server but Notification is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\components\\general\\Notification.tsx","Notification")},46055:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},49271:(e,t,s)=>{"use strict";s.d(t,{ReduxProvider:()=>p});var r=s(60687),a=s(9317),o=s(19150),n=s(58432),i=s(42895),l=s(35790),d=s(89011);let c=(0,a.U1)({reducer:{notification:o.Ay,createProject:n.Ay,auth:i.Ay,createLibrary:l.Ay,createLibraryItem:d.Ay}});s(43210);var u=s(54864);let p=({children:e})=>(0,r.jsx)(u.Kq,{store:c,children:e})},50823:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,16444,23)),Promise.resolve().then(s.t.bind(s,16042,23)),Promise.resolve().then(s.t.bind(s,88170,23)),Promise.resolve().then(s.t.bind(s,49477,23)),Promise.resolve().then(s.t.bind(s,29345,23)),Promise.resolve().then(s.t.bind(s,12089,23)),Promise.resolve().then(s.t.bind(s,46577,23)),Promise.resolve().then(s.t.bind(s,31307,23))},52911:(e,t,s)=>{Promise.resolve().then(s.bind(s,80994))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57091:(e,t,s)=>{Promise.resolve().then(s.bind(s,62366))},58014:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>p,metadata:()=>u});var r=s(37413);s(82704);var a=s(7990),o=s.n(a),n=s(60866),i=s.n(n),l=s(77832),d=s(44395),c=s(60265);let u={title:"Data analysis tool",description:"A tool for data collection and analysis."};function p({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:`${o().className} ${i().className} antialiased`,children:(0,r.jsx)(l.ReduxProvider,{children:(0,r.jsxs)(c.ReactQueryProvider,{children:[(0,r.jsx)(d.Notification,{}),(0,r.jsx)("main",{className:"bg-neutral-200",children:e})]})})})})}},58432:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>n,Gl:()=>a,th:()=>o});let r=(0,s(9317).Z0)({name:"createProject",initialState:{visible:!1},reducers:{showCreateProjectModal:e=>{e.visible=!0},hideCreateProjectModal:e=>{e.visible=!1}}}),{showCreateProjectModal:a,hideCreateProjectModal:o}=r.actions,n=r.reducer},60265:(e,t,s)=>{"use strict";s.d(t,{ReactQueryProvider:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call ReactQueryProvider() from the server but ReactQueryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\providers\\ReactQueryProvider.tsx","ReactQueryProvider")},62366:(e,t,s)=>{"use strict";s.d(t,{ChangePasswordPage:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call ChangePasswordPage() from the server but ChangePasswordPage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(auth)\\reset-password\\change-password\\ChangePasswordPage.tsx","ChangePasswordPage")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72121:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d,generateStaticParams:()=>l});var r=s(37413),a=s(60958),o=s(39916),n=s(81015);let i=["en","ne"];function l(){return i.map(e=>({locale:e}))}async function d({children:e,params:t}){let{locale:s}=await t;i.includes(s)||(0,o.notFound)();let l=await (0,n.V)(s);return(0,r.jsx)(a.A,{locale:s,messages:l,children:e})}},74075:e=>{"use strict";e.exports=require("zlib")},76565:(e,t,s)=>{var r={"./en.json":[87368,7368],"./ne.json":[3018,3018]};function a(e){if(!s.o(r,e))return Promise.resolve().then(()=>{var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t});var t=r[e],a=t[0];return s.e(t[1]).then(()=>s.t(a,19))}a.keys=()=>Object.keys(r),a.id=76565,e.exports=a},76708:(e,t,s)=>{"use strict";s.d(t,{ChangePasswordPage:()=>f});var r=s(60687),a=s(19150),o=s(12810),n=s(38038),i=s(12597),l=s(13861),d=s(28559),c=s(85814),u=s.n(c),p=s(16189),m=s(43210),h=s(27605),x=s(54864),b=s(77618);let f=()=>{let{register:e,formState:{errors:t,isSubmitting:s},handleSubmit:c,getValues:f,watch:y}=(0,h.mN)(),v=y("password"),w=y("confirmPassword"),g=(0,p.useSearchParams)().get("token"),P=(0,p.useRouter)(),j=(0,x.wA)(),N=(0,b.c3)(),[A,C]=(0,m.useState)(!1),[k,M]=(0,m.useState)(!1),E=async e=>{try{await o.A.post("/users/resetpassword",{token:g,newPassword:e.password}),j((0,a.Ds)({message:N("password_changed_successfully"),type:"success"})),P.push("/")}catch(e){console.error(e)}};return g?(0,r.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,r.jsxs)("div",{className:"section flex flex-col gap-8 w-11/12 mobile:w-4/5 tablet:w-lg",children:[(0,r.jsxs)("div",{className:"flex flex-col items-center gap-2",children:[(0,r.jsx)(n.A,{size:36}),(0,r.jsx)("h1",{className:"text-2xl tablet:text-3xl font-semibold text-center",children:N("createNewPassword")}),(0,r.jsx)("p",{className:"text-neutral-700 text-center",children:N("passwordRequirementsNote")})]}),(0,r.jsxs)("form",{className:"flex flex-col gap-4 ",onSubmit:c(E),noValidate:!0,children:[(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsx)("label",{htmlFor:"password",className:"label-text",children:N("newPassword")}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{...e("password",{required:N("pleaseEnterPassword"),validate:{minLength:e=>e.length>=8||N("passwordMustBeAtLeast8Characters"),hasUppercase:e=>/[A-Z]/.test(e)||N("passwordMustContainAtLeastOneUppercaseLetter"),hasNumber:e=>/\d/.test(e)||N("passwordMustContainAtLeastOneNumber"),hasSymbol:e=>/[\W_]/.test(e)||N("passwordMustContainAtLeastOneSymbol")}}),id:"password",type:A?"text":"password",className:"input-field w-full pr-10",placeholder:N("enterNewPassword")}),v&&v.length>0&&(0,r.jsxs)("button",{type:"button",tabIndex:-1,className:"absolute right-2 top-1/2 -translate-y-1/2 text-gray-500",onClick:()=>C(!A),children:[A?(0,r.jsx)(i.A,{className:"h-4 w-4"}):(0,r.jsx)(l.A,{className:"h-4 w-4"}),(0,r.jsxs)("span",{className:"sr-only",children:[A?"Hide":"Show"," password"]})]})]}),t.password&&(0,r.jsx)("p",{className:"text-sm text-red-500",children:`${t.password.message}`})]}),(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsx)("label",{htmlFor:"confirm-password",className:"label-text",children:N("confirmPassword")}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{...e("confirmPassword",{required:N("pleaseConfirmPassword"),validate:e=>e===f("password")||N("passwordsDoNotMatch")}),id:"confirm-password",type:k?"text":"password",className:"input-field w-full pr-10",placeholder:N("confirmYourPassword")}),w&&w.length>0&&(0,r.jsxs)("button",{type:"button",tabIndex:-1,className:"absolute right-2 top-1/2 -translate-y-1/2 text-gray-500",onClick:()=>M(!k),children:[k?(0,r.jsx)(i.A,{className:"h-4 w-4"}):(0,r.jsx)(l.A,{className:"h-4 w-4"}),(0,r.jsxs)("span",{className:"sr-only",children:[k?"Hide":"Show"," password"]})]})]}),t.confirmPassword&&(0,r.jsx)("p",{className:"text-sm text-red-500",children:`${t.confirmPassword.message}`})]}),(0,r.jsx)("button",{type:"submit",className:"btn-primary",children:s?(0,r.jsxs)("span",{className:"flex items-center gap-2",children:[N("updating"),(0,r.jsx)("div",{className:"animate-spin border-x-2 border-neutral-100 rounded-full size-4"})]}):(0,r.jsx)("span",{className:"flex items-center gap-2",children:N("resetPassword")})})]}),(0,r.jsxs)(u(),{href:"/",className:"text-neutral-700 self-center flex items-center gap-2",children:[(0,r.jsx)(d.A,{size:16})," ",N("backToSignin")]})]})}):(0,r.jsx)("p",{className:"text-red-500",children:N("noResetToken")})}},77832:(e,t,s)=>{"use strict";s.d(t,{ReduxProvider:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call ReduxProvider() from the server but ReduxProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\providers\\ReduxProvider.tsx","ReduxProvider")},79551:e=>{"use strict";e.exports=require("url")},81015:(e,t,s)=>{"use strict";s.d(t,{A:()=>n,V:()=>o});var r=s(35471);let a=["en","ne"];async function o(e){a.includes(e)||(console.warn(`Unsupported locale: ${e}, falling back to 'en'`),e="en");try{let t=(await s(76565)(`./${e}.json`)).default;if(!t||"object"!=typeof t)throw Error(`Invalid messages format for locale: ${e}`);return t}catch(t){if(console.error(`Failed to load messages for locale: ${e}`,t),"en"!==e)try{return console.log("Falling back to English messages"),(await s.e(7368).then(s.t.bind(s,87368,19))).default}catch(e){console.error("Failed to load fallback English messages",e)}return{}}}let n=(0,r.A)(async({locale:e})=>{let t=e?.toString()||"en";return{locale:t,messages:await o(t),timeZone:"Asia/Kathmandu",formats:{dateTime:{short:{day:"numeric",month:"short",year:"numeric"},medium:{day:"numeric",month:"long",year:"numeric"},long:{weekday:"long",day:"numeric",month:"long",year:"numeric"}},number:{currency:{style:"currency",currency:"NPR"}}}}})},81630:e=>{"use strict";e.exports=require("http")},82704:()=>{},83997:e=>{"use strict";e.exports=require("tty")},86778:(e,t,s)=>{Promise.resolve().then(s.bind(s,44395)),Promise.resolve().then(s.bind(s,60265)),Promise.resolve().then(s.bind(s,77832))},89011:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>n,dQ:()=>a,g7:()=>o});let r=(0,s(9317).Z0)({initialState:{visible:!1,option:""},name:"createLibraryItem",reducers:{showCreateLibraryItemModal:(e,t)=>{e.visible=!0,e.option=t.payload},hideCreateLibraryItemModal:e=>{e.visible=!1,e.option=""}}}),{showCreateLibraryItemModal:a,hideCreateLibraryItemModal:o}=r.actions,n=r.reducer},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,7404,1658,6560,8610,5374,2198,5814],()=>s(11674));module.exports=r})();