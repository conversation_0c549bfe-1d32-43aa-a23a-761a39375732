(()=>{var e={};e.id=7767,e.ids=[7767],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},14902:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>a.a,__next_app__:()=>p,pages:()=>l,routeModule:()=>c,tree:()=>u});var r=t(65239),i=t(48088),n=t(88170),a=t.n(n),o=t(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);t.d(s,d);let u={children:["",{children:["edit-submission",{children:["[hashedId]",{children:["[submissionId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,95120)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\edit-submission\\[hashedId]\\[submissionId]\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,58014)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\edit-submission\\[hashedId]\\[submissionId]\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},c=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/edit-submission/[hashedId]/[submissionId]/page",pathname:"/edit-submission/[hashedId]/[submissionId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},54481:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>E});var r=t(60687),i=t(16189),n=t(29494),a=t(12810),o=t(6986),d=t(75531),u=t(86429),l=t(43210),p=t(39390),c=t(15616),m=t(93437),h=t(40347),b=t(70334),x=t(54050),f=t(54864),y=t(19150),g=t(13784),q=t(80967),v=t(96),I=t(71845),w=t(24527),j=t(69396),O=t(44305),N=t(31207);function S({questions:e,submission:s,projectId:t,submissionId:i,onClose:o,onSave:d}){let u=(0,f.wA)(),[S,k]=(0,l.useState)({}),[E,C]=(0,l.useState)({}),[T,A]=(0,l.useState)({}),[P,$]=(0,l.useState)(!1),[R,_]=(0,l.useState)([]),[D,M]=(0,l.useState)([]),[K,F]=(0,l.useState)({}),[G,J]=(0,l.useState)(new Set);(0,l.useRef)(new Set),(0,l.useRef)(!1),(0,l.useRef)("");let{data:U=[]}=(0,n.I)({queryKey:["questionGroups",t],queryFn:()=>(0,v.pr)({projectId:t}),enabled:!!t}),{data:Q}=(0,n.I)({queryKey:["project",t],queryFn:()=>(0,I.kf)({projectId:t}),enabled:!!t}),Y=(0,l.useMemo)(()=>(0,N.yi)(U,e),[U,e]),z=(0,l.useMemo)(()=>(0,N.ru)(e),[e]),L=(0,l.useMemo)(()=>(0,N.XV)(Y,z),[Y,z]),H=(0,l.useCallback)(e=>{F(s=>({...s,[e]:!s[e]}))},[]),V=(0,l.useCallback)((e,s)=>{J(s=>new Set(s).add(e)),k(t=>({...t,[e]:s})),A(s=>({...s,[e]:""}))},[]),X=()=>{let e=(0,w.WK)(R,S);return A(e),0===Object.keys(e).length},B=(0,x.n)({mutationFn:async r=>{let n=e.map(e=>{let n=r[e.id],a="selectmany"===e.inputType,o=s.answers.find(s=>s.question.id===e.id),d=!o?.id;if(a&&Array.isArray(n)){if(n.length>0){let s=[];e.questionOptions&&(s=n.map(s=>{let t=e.questionOptions.find(e=>e.label===s);return t?.id}).filter(e=>void 0!==e));let r={projectId:t,questionId:e.id,answerType:e.inputType,value:n.join(", "),questionOptionId:s,isOtherOption:!1,formSubmissionId:i};return d?r:{...r,id:o.id}}return null}{let s,r;if(void 0===(s="number"===e.inputType||"decimal"===e.inputType?n?Number(n):void 0:"date"===e.inputType||"dateandtime"===e.inputType?n||void 0:"table"===e.inputType?Array.isArray(n)&&n.length>0?JSON.stringify(n):void 0:n?String(n):void 0))return null;if("selectone"===e.inputType&&n&&e.questionOptions){let s=e.questionOptions.find(e=>e.label===n);r=s?.id}let a={projectId:t,questionId:e.id,answerType:e.inputType,value:s,questionOptionId:r,isOtherOption:!1,formSubmissionId:i};return d?a:{...a,id:o.id}}}).filter(e=>null!==e);if(0===n.length)throw Error("No valid answers with IDs to submit");let o=n.map(e=>e.id?{id:e.id,questionId:e.questionId,projectId:t,value:e.value,answerType:e.answerType,questionOptionId:e.questionOptionId,isOtherOption:e.isOtherOption||!1,formSubmissionId:e.formSubmissionId}:e.questionId?{questionId:e.questionId,projectId:t,value:e.value,answerType:e.answerType,questionOptionId:e.questionOptionId,isOtherOption:e.isOtherOption||!1,formSubmissionId:e.formSubmissionId}:null).filter(e=>null!==e);try{return await (0,q.GN)(o,t)}catch(r){console.error("Error with /answers/multiple endpoint:",r),r.response&&(console.error("Error response data:",JSON.stringify(r.response.data,null,2)),console.error("Error response status:",r.response.status),console.error("Error response headers:",r.response.headers));let e=[],s=[];for(let r of n)try{if(r.id){let{data:s}=await a.A.patch(`/answers/${r.id}?projectId=${t}`,{id:r.id,questionId:r.questionId,projectId:t,value:r.value,answerType:r.answerType,questionOptionId:r.questionOptionId,isOtherOption:r.isOtherOption||!1,formSubmissionId:r.formSubmissionId});e.push(s)}else if(r.questionId){let{data:s}=await a.A.post(`/answers?projectId=${t}`,{submissionId:r.formSubmissionId,questionId:r.questionId,value:r.value,answerType:r.answerType,questionOptionId:r.questionOptionId,isOtherOption:r.isOtherOption||!1});e.push(s)}}catch(t){let e=r.id||r.questionId;console.error(`Error handling answer ${e}:`,t),t.response&&console.error("Individual error response data:",JSON.stringify(t.response.data,null,2)),s.push(e)}if(s.length>0)throw Error(`Failed to update answers with IDs: ${s.join(", ")}`);if(e.length>0)return u((0,y.Ds)({message:"Submission updated successfully using individual updates. Consider checking the bulk update endpoint.",type:"warning"})),e;throw r}},onSuccess:()=>{u((0,y.Ds)({message:"Submission updated successfully. You can continue editing if needed.",type:"success"})),J(new Set),d()},onError:e=>{let s=e.response?.data?.message||e.response?.data?.error||e.message||"Failed to update submission. Please check your input and try again.";u((0,y.Ds)({message:s,type:"error"})),console.error("Update Error:",{message:s,status:e.response?.status,data:JSON.stringify(e.response?.data,null,2)})},onSettled:()=>{$(!1)}}),W=async e=>{e.preventDefault(),X()&&($(!0),B.mutate(S))},Z=s=>e.some(e=>e.questionOptions?.some(e=>e.nextQuestionId===s)),ee=e=>e.questionOptions?.some(e=>e.nextQuestionId)||!1,es=e=>{let s=Z(e.id),t=ee(e);return(0,r.jsxs)("div",{className:`border rounded-md p-4 ${s?"border-primary-200 dark:border-primary-700 bg-primary-100 dark:bg-primary-900/20":"border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800"}`,children:[(0,r.jsxs)("div",{className:"mb-2",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsxs)(p.J,{className:"text-base font-medium",children:[e.label,e.isRequired&&(0,r.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),s&&(0,r.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200",children:[(0,r.jsx)(b.A,{className:"w-3 h-3 mr-1"}),"Follow-up"]}),t&&(0,r.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-accent-200 text-accent-700 dark:bg-accent-700/20 dark:text-accent-200",children:"Has conditions"})]}),e.hint&&(0,r.jsx)("p",{className:`text-sm mt-1 ${s?"text-primary-700 dark:text-primary-300":"text-muted-foreground"}`,children:e.hint}),T[e.id]&&(0,r.jsx)("p",{className:"text-sm text-red-500 mt-1",children:T[e.id]})]}),(0,r.jsx)("div",{className:"mt-2",children:et(e)})]},e.id)},et=e=>{let s=S[e.id]??("selectmany"===e.inputType?[]:"");switch(e.inputType){case"text":if(e.hint?.includes("multiline"))return(0,r.jsx)(c.T,{value:s,onChange:s=>V(e.id,s.target.value),placeholder:e.hint||"Your answer",required:e.isRequired});return(0,r.jsx)("input",{className:"input-field w-full",value:s,onChange:s=>V(e.id,s.target.value),placeholder:e.hint||"Your answer",required:e.isRequired});case"number":return(0,r.jsx)("input",{className:"input-field w-full",type:"number",value:s,onChange:s=>V(e.id,s.target.value),placeholder:e.hint||"Your answer",required:e.isRequired});case"decimal":return(0,r.jsx)("input",{className:"input-field w-full",type:"number",step:"any",value:s,onChange:s=>V(e.id,s.target.value),placeholder:e.hint||"Your answer",required:e.isRequired});case"selectone":return(0,r.jsx)(h.z,{value:s,onValueChange:s=>V(e.id,s),required:e.isRequired,children:(0,r.jsx)("div",{className:"space-y-2",children:e.questionOptions?.map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(h.C,{value:e.label,id:`option-${e.id}`}),(0,r.jsx)(p.J,{htmlFor:`option-${e.id}`,className:"cursor-pointer",children:e.label})]},s))})});case"selectmany":return(0,r.jsx)("div",{className:"space-y-2",children:e.questionOptions?.map(t=>(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(m.S,{id:`option-${t.id}`,checked:(s||[]).includes(t.label),onCheckedChange:r=>{let i=s||[],n=r?[...i,t.label]:i.filter(e=>e!==t.label);V(e.id,n)}}),(0,r.jsx)(p.J,{htmlFor:`option-${t.id}`,className:"cursor-pointer",children:t.label})]},t.id))});case"date":return(0,r.jsx)("div",{className:"relative",children:(0,r.jsx)("input",{className:"input-field w-full",type:"date",value:s,onChange:s=>V(e.id,s.target.value),placeholder:e.hint||"Select date",required:e.isRequired})});case"dateandtime":return(0,r.jsx)("div",{className:"relative",children:(0,r.jsx)("input",{className:"input-field w-full",type:"time",value:s,onChange:s=>V(e.id,s.target.value),placeholder:e.hint||"Select time",required:e.isRequired})});case"table":return(0,r.jsx)(g.N,{questionId:e.id,value:s,onChange:s=>V(e.id,s),required:e.isRequired,tableLabel:e.label});default:return null}};return(0,r.jsxs)("div",{className:"w-full max-w-screen-lg bg-neutral-100 dark:bg-gray-800 rounded-md shadow-sm border border-gray-200 dark:border-gray-700",children:[(0,r.jsxs)("h2",{className:"text-2xl font-semibold p-6 border-b border-gray-200 dark:border-gray-700",children:["Edit Submission",Q?.name?` for ${Q.name}`:""]}),(0,r.jsx)("form",{onSubmit:W,className:"p-6",children:(0,r.jsxs)("div",{className:"space-y-6",children:[0===e.length?(0,r.jsx)("div",{className:"text-center py-12",children:(0,r.jsx)("p",{className:"text-muted-foreground",children:"This form has no questions yet."})}):L.map(e=>{if("group"===e.type){let s=e.data,t=K[s.id];return(0,r.jsx)(O.A,{group:s,nestingLevel:0,visibleQuestions:R,nestedQuestions:D,renderQuestionInput:et,errors:T,onToggleExpansion:H,isExpanded:t,expandedGroups:K,className:""},`group-${s.id}`)}{let s=e.data;if(!R.some(e=>e.id===s.id))return null;let t=D.find(e=>e.question.id===s.id);return t?(0,r.jsx)(j.A,{questionGroup:t,renderQuestionInput:et,errors:T,className:""},s.id):es(s)}}),e.length>0&&(0,r.jsxs)("div",{className:"mt-6 flex justify-end gap-4",children:[(0,r.jsx)("button",{className:"btn-primary bg-neutral-500 hover:bg-neutral-600",type:"button",onClick:o,disabled:P,children:"Cancel"}),(0,r.jsx)("button",{className:"btn-primary",type:"submit",disabled:P,children:P?"Saving...":"Save Changes"})]})]})})]})}let k=async(e,s)=>{let{data:t}=await a.A.get(`/form-submissions/${e}`),r=t.data.formSubmissions.find(e=>e.id===s);if(!r)throw Error("Submission not found");return r};function E(){let{hashedId:e,submissionId:s}=(0,i.useParams)(),t=(0,o.D)(e),a=Number(s);if(null===t||isNaN(a))return(0,r.jsx)("div",{children:"Error: Invalid project or submission ID."});let{data:l=[],isLoading:p,isError:c}=(0,n.I)({queryKey:["questions",t],queryFn:()=>(0,d.K4)({projectId:t}),enabled:!!t}),{data:m,isLoading:h,isError:b,refetch:x}=(0,n.I)({queryKey:["submission",t,a],queryFn:()=>k(t,a),enabled:!!t&&!!a});return p||h?(0,r.jsx)(u.A,{}):c||b||!l||!m?(0,r.jsx)("p",{className:"text-sm text-red-500",children:"Error loading submission or form. Please try again."}):(0,r.jsx)("div",{className:"min-h-screen w-full bg-neutral-100 dark:bg-gray-900 flex flex-col items-center p-4 md:p-6",children:(0,r.jsx)(S,{questions:l,submission:m,projectId:t,submissionId:a,onSave:()=>{window.opener&&window.opener.postMessage({type:"REFETCH_SUBMISSIONS"},"*"),x()}})})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64702:(e,s,t)=>{Promise.resolve().then(t.bind(t,54481))},69430:(e,s,t)=>{Promise.resolve().then(t.bind(t,95120))},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},80967:(e,s,t)=>{"use strict";t.d(s,{GN:()=>o,J6:()=>n,O8:()=>i,s4:()=>a});var r=t(12810);let i=async(e,s)=>{try{let{data:t}=await r.A.delete(`/form-submissions/${e}?projectId=${s}`);return t}catch(e){throw console.error("Error deleting form submission:",e),e}},n=async(e,s)=>{try{let t=e.map(e=>r.A.delete(`/form-submissions/${e}?projectId=${s}`));return(await Promise.all(t)).map(e=>e.data)}catch(e){throw console.error("Error deleting multiple form submissions:",e),e}},a=async(e,s)=>{try{if(!e.submissionId||!e.questionId)throw Error("submissionId and questionId are required");let t={...e};null===t.questionOptionId?delete t.questionOptionId:Array.isArray(t.questionOptionId)&&(t.questionOptionId=t.questionOptionId.filter(e=>null!=e),0===t.questionOptionId.length&&delete t.questionOptionId);let{data:i}=await r.A.patch(`/answers/${e.questionId}?projectId=${s}`,t);return i}catch(e){throw console.error("Error updating answer:",e),e}},o=async(e,s)=>{try{let{data:t}=await r.A.patch(`/answers/multiple?projectId=${s}`,e);return t}catch(e){throw console.error("Error updating multiple answers with endpoint:",e),e}}},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")},95120:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Kobo-project\\\\data-analysis-tool-frontend\\\\app\\\\edit-submission\\\\[hashedId]\\\\[submissionId]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\edit-submission\\[hashedId]\\[submissionId]\\page.tsx","default")}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4447,7404,1658,6560,3851,3341,3571],()=>t(14902));module.exports=r})();