"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateProfileSchema = exports.userSchema = void 0;
const client_1 = require("@prisma/client");
const zod_1 = require("zod");
exports.userSchema = zod_1.z.object({
    name: zod_1.z.string().min(1, "Full name not provided"),
    email: zod_1.z.string().min(1, "Email not provided").email("Invalid email address"),
    password: zod_1.z
        .string()
        .min(1, "Password not provided")
        .min(8, "Password must be at least 8 characters")
        .max(32, "Password must be less than 32 characters")
        .regex(/[A-Z]/, "Must contain at least one uppercase letter")
        .regex(/[a-z]/, "Must contain at least one lowercase letter")
        .regex(/[0-9]/, "Must contain at least one number")
        .regex(/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/, "Must contain at least one special character"),
    country: zod_1.z.string().min(1, "Country not selected"),
    sector: zod_1.z.nativeEnum(client_1.Sector, {
        errorMap: () => ({ message: "Invalid sector selected" }),
    }),
    organizationType: zod_1.z.nativeEnum(client_1.OrganizationType, {
        errorMap: () => ({ message: "Invalid organization type selected" }),
    }),
});
exports.updateProfileSchema = zod_1.z.object({
    name: zod_1.z.string().min(1, "Name is required"),
    country: zod_1.z.string().min(1, "Country not selected"),
    city: zod_1.z.string().optional(),
    bio: zod_1.z.string().optional(),
    sector: zod_1.z.nativeEnum(client_1.Sector, {
        errorMap: () => ({ message: "Invalid sector selected" }),
    }),
    organizationType: zod_1.z.nativeEnum(client_1.OrganizationType, {
        errorMap: () => ({ message: "Invalid organization type selected" }),
    }),
});
