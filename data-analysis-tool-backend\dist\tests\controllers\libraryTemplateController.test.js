"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const libraryTemplateController_1 = require("../../controllers/libraryTemplateController");
const libraryTemplateRepository_1 = __importDefault(require("../../repositories/libraryTemplateRepository"));
// Mock the dependencies
jest.mock("../../repositories/libraryTemplateRepository");
describe("Library Template Controller", () => {
    let mockRequest;
    let mockResponse;
    let responseObject = {};
    beforeEach(() => {
        // Reset mocks before each test
        jest.clearAllMocks();
        jest.resetAllMocks();
        // Setup mock response
        mockResponse = {
            status: jest.fn().mockReturnThis(),
            json: jest.fn().mockImplementation((result) => {
                responseObject = result;
                return mockResponse;
            }),
        };
        // Reset response object
        responseObject = {};
        // Setup default authenticated user
        mockRequest = {
            user: {
                id: 1,
            },
            params: {},
            query: {},
            body: {},
        };
    });
    describe("createLibraryTemplate", () => {
        beforeEach(() => {
            mockRequest.body = {
                name: "Test Template",
                description: "This is a test template",
                sector: "information_media",
                country: "United States",
            };
        });
        it("should create a library template successfully", () => __awaiter(void 0, void 0, void 0, function* () {
            const mockTemplate = {
                id: 1,
                name: "Test Template",
                description: "This is a test template",
                sector: "information_media",
                country: "United States",
                userId: 1,
                createdAt: new Date(),
                updatedAt: new Date(),
            };
            libraryTemplateRepository_1.default.findByName.mockResolvedValue(null);
            libraryTemplateRepository_1.default.create.mockResolvedValue(mockTemplate);
            yield (0, libraryTemplateController_1.createLibraryTemplate)(mockRequest, mockResponse);
            expect(libraryTemplateRepository_1.default.findByName).toHaveBeenCalledWith("Test Template", 1);
            expect(libraryTemplateRepository_1.default.create).toHaveBeenCalledWith({
                name: "Test Template",
                description: "This is a test template",
                sector: "information_media",
                userId: 1,
                country: "United States",
            });
            expect(mockResponse.status).toHaveBeenCalledWith(201);
            expect(responseObject).toHaveProperty("message", "Library template created successfully");
            expect(responseObject).toHaveProperty("libraryTemplate", mockTemplate);
        }));
        it("should return 400 when library template already exists", () => __awaiter(void 0, void 0, void 0, function* () {
            const existingTemplate = {
                id: 1,
                name: "Test Template",
                userId: 1,
            };
            libraryTemplateRepository_1.default.findByName.mockResolvedValue(existingTemplate);
            yield (0, libraryTemplateController_1.createLibraryTemplate)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(400);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "library template already exists");
            expect(libraryTemplateRepository_1.default.create).not.toHaveBeenCalled();
        }));
        it("should return 400 for invalid input", () => __awaiter(void 0, void 0, void 0, function* () {
            // Missing required fields
            mockRequest.body = { name: "Test Template" };
            yield (0, libraryTemplateController_1.createLibraryTemplate)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(400);
            expect(responseObject).toHaveProperty("success", false);
            expect(libraryTemplateRepository_1.default.create).not.toHaveBeenCalled();
        }));
        it("should handle server errors", () => __awaiter(void 0, void 0, void 0, function* () {
            libraryTemplateRepository_1.default.findByName.mockRejectedValue(new Error("Database error"));
            yield (0, libraryTemplateController_1.createLibraryTemplate)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(500);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "error creating library template");
        }));
    });
    describe("getAllLibraryTemplates", () => {
        it("should get all library templates successfully", () => __awaiter(void 0, void 0, void 0, function* () {
            const mockTemplates = [
                {
                    id: 1,
                    name: "Template 1",
                    description: "Description 1",
                    userId: 1,
                },
                {
                    id: 2,
                    name: "Template 2",
                    description: "Description 2",
                    userId: 1,
                },
            ];
            libraryTemplateRepository_1.default.findAll.mockResolvedValue(mockTemplates);
            yield (0, libraryTemplateController_1.getAllLibraryTemplates)(mockRequest, mockResponse);
            expect(libraryTemplateRepository_1.default.findAll).toHaveBeenCalledWith(1);
            expect(mockResponse.status).toHaveBeenCalledWith(200);
            expect(responseObject).toHaveProperty("message", "Successfully fetched all library templates");
            expect(responseObject).toHaveProperty("templates", mockTemplates);
        }));
        it("should handle server errors", () => __awaiter(void 0, void 0, void 0, function* () {
            libraryTemplateRepository_1.default.findAll.mockRejectedValue(new Error("Database error"));
            yield (0, libraryTemplateController_1.getAllLibraryTemplates)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(500);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "error getting library templates");
        }));
    });
    describe("updateLibraryTemplate", () => {
        beforeEach(() => {
            mockRequest.params = { id: "1" };
            mockRequest.body = {
                name: "Updated Template",
                description: "Updated description",
                sector: "information_media",
                country: "Canada",
            };
        });
        it("should update a library template successfully", () => __awaiter(void 0, void 0, void 0, function* () {
            const existingTemplate = {
                id: 1,
                name: "Test Template",
                description: "This is a test template",
                userId: 1,
            };
            const updatedTemplate = {
                id: 1,
                name: "Updated Template",
                description: "Updated description",
                sector: "information_media",
                country: "Canada",
            };
            libraryTemplateRepository_1.default.findLibraryTemplateByIdAndUser.mockResolvedValue(existingTemplate);
            libraryTemplateRepository_1.default.updateById.mockResolvedValue(updatedTemplate);
            yield (0, libraryTemplateController_1.updateLibraryTemplate)(mockRequest, mockResponse);
            expect(libraryTemplateRepository_1.default.findLibraryTemplateByIdAndUser).toHaveBeenCalledWith(1, 1);
            expect(libraryTemplateRepository_1.default.updateById).toHaveBeenCalledWith(1, expect.objectContaining({
                name: "Updated Template",
                description: "Updated description",
            }));
            expect(mockResponse.status).toHaveBeenCalledWith(200);
            expect(responseObject.data).toHaveProperty("updatedLibraryTemplate", updatedTemplate);
        }));
        it("should return 404 when library template not found", () => __awaiter(void 0, void 0, void 0, function* () {
            libraryTemplateRepository_1.default.findLibraryTemplateByIdAndUser.mockResolvedValue(null);
            yield (0, libraryTemplateController_1.updateLibraryTemplate)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(404);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "no library template found");
            expect(libraryTemplateRepository_1.default.updateById).not.toHaveBeenCalled();
        }));
        it("should return 400 for invalid input", () => __awaiter(void 0, void 0, void 0, function* () {
            const existingTemplate = {
                id: 1,
                name: "Test Template",
                userId: 1,
            };
            libraryTemplateRepository_1.default.findLibraryTemplateByIdAndUser.mockResolvedValue(existingTemplate);
            // Invalid input - missing required fields
            mockRequest.body = {};
            yield (0, libraryTemplateController_1.updateLibraryTemplate)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(400);
            expect(responseObject).toHaveProperty("success", false);
            expect(libraryTemplateRepository_1.default.updateById).not.toHaveBeenCalled();
        }));
        it("should handle server errors", () => __awaiter(void 0, void 0, void 0, function* () {
            libraryTemplateRepository_1.default.findLibraryTemplateByIdAndUser.mockRejectedValue(new Error("Database error"));
            yield (0, libraryTemplateController_1.updateLibraryTemplate)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(500);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "error updating library template");
        }));
    });
    describe("deleteLibraryTemplate", () => {
        beforeEach(() => {
            mockRequest.params = { id: "1" };
        });
        it("should delete a library template successfully", () => __awaiter(void 0, void 0, void 0, function* () {
            const existingTemplate = {
                id: 1,
                name: "Test Template",
                userId: 1,
            };
            libraryTemplateRepository_1.default.findLibraryTemplateByIdAndUser.mockResolvedValue(existingTemplate);
            libraryTemplateRepository_1.default.deleteLibraryTemplate.mockResolvedValue({ id: 1 });
            yield (0, libraryTemplateController_1.deleteLibraryTemplate)(mockRequest, mockResponse);
            expect(libraryTemplateRepository_1.default.findLibraryTemplateByIdAndUser).toHaveBeenCalledWith(1, 1);
            expect(libraryTemplateRepository_1.default.deleteLibraryTemplate).toHaveBeenCalledWith(1);
            expect(mockResponse.status).toHaveBeenCalledWith(200);
            expect(responseObject).toHaveProperty("message", "library template deleted successfully");
        }));
        it("should return 404 when library template not found", () => __awaiter(void 0, void 0, void 0, function* () {
            libraryTemplateRepository_1.default.findLibraryTemplateByIdAndUser.mockResolvedValue(null);
            yield (0, libraryTemplateController_1.deleteLibraryTemplate)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(404);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "no library template found with given id");
            expect(libraryTemplateRepository_1.default.deleteLibraryTemplate).not.toHaveBeenCalled();
        }));
        it("should handle server errors", () => __awaiter(void 0, void 0, void 0, function* () {
            libraryTemplateRepository_1.default.findLibraryTemplateByIdAndUser.mockRejectedValue(new Error("Database error"));
            yield (0, libraryTemplateController_1.deleteLibraryTemplate)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(500);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "error deleting library template");
        }));
    });
    describe("getLibraryTemplateById", () => {
        beforeEach(() => {
            mockRequest.params = { id: "1" };
        });
        it("should get a library template by ID successfully", () => __awaiter(void 0, void 0, void 0, function* () {
            const mockTemplate = {
                id: 1,
                name: "Test Template",
                description: "This is a test template",
                userId: 1,
            };
            libraryTemplateRepository_1.default.findLibraryTemplateByIdAndUser.mockResolvedValue(mockTemplate);
            yield (0, libraryTemplateController_1.getLibraryTemplateById)(mockRequest, mockResponse);
            expect(libraryTemplateRepository_1.default.findLibraryTemplateByIdAndUser).toHaveBeenCalledWith(1, 1);
            expect(mockResponse.status).toHaveBeenCalledWith(200);
            expect(responseObject).toHaveProperty("message", "Successfully fetched library template");
            expect(responseObject).toHaveProperty("template", mockTemplate);
        }));
        it("should return 404 when library template not found", () => __awaiter(void 0, void 0, void 0, function* () {
            libraryTemplateRepository_1.default.findLibraryTemplateByIdAndUser.mockResolvedValue(null);
            yield (0, libraryTemplateController_1.getLibraryTemplateById)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(404);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "library template not found");
        }));
        it("should handle server errors", () => __awaiter(void 0, void 0, void 0, function* () {
            libraryTemplateRepository_1.default.findLibraryTemplateByIdAndUser.mockRejectedValue(new Error("Database error"));
            yield (0, libraryTemplateController_1.getLibraryTemplateById)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(500);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "error getting library template by id");
        }));
    });
});
