(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7047],{1243:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},13163:(e,s,t)=>{"use strict";t.d(s,{A:()=>c});var a=t(95155),r=t(60760),l=t(44518),i=t(95233),n=t(54416);t(12115);let c=e=>{let{children:s,className:t,isOpen:c,onClose:o,preventOutsideClick:d=!1}=e;return(0,a.jsx)(r.N,{children:c&&(0,a.jsx)(l.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-neutral-900/50 flex items-center justify-center z-40 p-4 overflow-y-auto",onClick:e=>{d||o()},children:(0,a.jsxs)(l.P.div,{initial:{scale:.6,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.6,opacity:0},transition:{duration:.3,ease:i.am},className:"relative section flex flex-col max-w-4xl max-h-[90vh] overflow-y-auto ".concat(t),onClick:e=>e.stopPropagation(),children:[(0,a.jsx)(n.A,{onClick:o,className:"absolute self-end cursor-pointer text-neutral-700 hover:text-neutral-900 active:scale-90 transition-all duration-300"}),s]})})})}},15616:(e,s,t)=>{"use strict";t.d(s,{A:()=>c});var a=t(95155),r=t(46453),l=t(35695),i=t(6874),n=t.n(i);function c(){let e=(0,r.Ym)(),s=(0,l.usePathname)(),t=e=>{let t=s.replace(/^\/(en|ne)/,"");return"/".concat(e).concat(t)};return(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(n(),{href:t("en"),className:"px-3 py-1 rounded ".concat("en"===e?"bg-primary-600 text-white":"bg-gray-200"),children:"English"}),(0,a.jsx)(n(),{href:t("ne"),className:"px-3 py-1 rounded ".concat("ne"===e?"bg-primary-600 text-white":"bg-gray-200"),children:"नेपाली"})]})}},25784:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let a=t(23464).A.create({baseURL:"http://localhost:4000/api",headers:{"Content-Type":"application/json"},withCredentials:!0});a.interceptors.request.use(e=>e,e=>Promise.reject(e)),a.interceptors.response.use(e=>e,e=>("ERR_NETWORK"===e.code&&console.error("Network error - Please check if the backend server is running"),Promise.reject(e)));let r=a},26956:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>w});var a=t(95155),r=t(42872),l=t(71402),i=t(90221),n=t(90232),c=t(78749),o=t(92657),d=t(6874),u=t.n(d),m=t(35695),p=t(12115),h=t(62177),x=t(34540),f=t(55594),g=t(29350),y=t(15616),j=t(17652);let v=f.z.object({email:f.z.string().min(1,"Email is required").email("Please enter a valid email address"),password:f.z.string().min(1,"Password is required")}),w=()=>{let{register:e,formState:{errors:s,isSubmitting:t},handleSubmit:d,getValues:f,watch:w}=(0,h.mN)({resolver:(0,i.u)(v)}),b=w("password"),N=(0,m.useRouter)(),A=(0,x.wA)(),k=(0,j.c3)(),[E,S]=(0,p.useState)(!1),[P,C]=(0,p.useState)(!1),{signin:z}=(0,g.A)({skipFetchUser:!0}),I=async e=>{z({email:e.email,password:e.password},()=>{A((0,l.Ds)({message:k("signInSuccessful"),type:"success"})),N.push("/dashboard")},e=>{"unverified"===e?S(!0):A((0,l.Ds)({message:k("invalidEmailOrPassword"),type:"error"}))})};return(0,a.jsxs)("div",{className:"min-h-screen flex items-center justify-center",children:[(0,a.jsx)(r.x,{email:f("email"),showModal:E,setShowModal:S}),(0,a.jsxs)("div",{className:"flex flex-col section w-11/12 mobile:w-4/5 tablet:w-lg",children:[(0,a.jsxs)("div",{className:"flex flex-col items-center gap-2 mb-8",children:[(0,a.jsx)(n.A,{size:36}),(0,a.jsx)("h1",{className:"text-2xl tablet:text-3xl font-semibold text-center",children:k("signInToYourAccount")}),(0,a.jsx)("p",{className:"text-neutral-700 text-center",children:k("getStarted2")})]}),(0,a.jsxs)("form",{className:"flex flex-col gap-4 mb-4",onSubmit:d(I),children:[(0,a.jsxs)("div",{className:"group label-input-group",children:[(0,a.jsx)("label",{htmlFor:"email",className:"label-text",children:k("email")}),(0,a.jsx)("input",{...e("email"),id:"email",type:"email",placeholder:k("enterEmail"),className:"input-field"}),s.email&&(0,a.jsx)("p",{className:"text-red-500 text-sm",children:"".concat(s.email.message)})]}),(0,a.jsxs)("div",{className:"group label-input-group",children:[(0,a.jsx)("label",{htmlFor:"password",className:"label-text",children:k("password")}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{...e("password"),id:"password",type:P?"text":"password",placeholder:k("enterPassword"),className:"input-field w-full pr-10"}),b&&b.length>0&&(0,a.jsxs)("button",{type:"button",tabIndex:-1,className:"absolute right-2 top-1/2 -translate-y-1/2 text-gray-500",onClick:()=>C(!P),children:[P?(0,a.jsx)(c.A,{className:"h-4 w-4"}):(0,a.jsx)(o.A,{className:"h-4 w-4"}),(0,a.jsxs)("span",{className:"sr-only",children:[P?"Hide":"Show"," password"]})]})]}),s.password&&(0,a.jsx)("p",{className:"text-red-500 text-sm",children:"".concat(s.password.message)})]}),(0,a.jsx)("button",{type:"submit",className:"btn-primary",disabled:t,children:t?(0,a.jsxs)("span",{className:"flex items-center gap-2",children:[k("signingIn"),(0,a.jsx)("div",{className:"size-4 rounded-full border-x-2 animate-spin"})]}):k("signIn")})]}),(0,a.jsx)(u(),{href:"/reset-password",className:"self-end underline text-neutral-700",children:k("forgotPassword")}),(0,a.jsxs)("div",{className:"text-neutral-700 flex items-center gap-2",children:[(0,a.jsx)("span",{children:k("noAccount")}),(0,a.jsx)(u(),{href:"/signup",className:"font-medium hover:text-neutral-900 duration-300",children:k("signUp")})]}),(0,a.jsx)("div",{className:"mt-4",children:(0,a.jsx)(y.A,{})})]})]})}},28883:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},29350:(e,s,t)=>{"use strict";t.d(s,{A:()=>o});var a=t(97381),r=t(59362),l=t(25784),i=t(35695),n=t(12115),c=t(34540);let o=e=>{let s=(0,c.wA)(),t=(0,i.useRouter)(),o=(0,i.usePathname)(),{status:d,user:u,error:m}=(0,c.d4)(e=>e.auth),p=async()=>{try{s((0,a.Le)());let e=(await l.A.get("/users/me")).data;s((0,a.tQ)(e))}catch(l){if(s((0,a.x9)()),(0,r.F0)(l)){var e,i,n,c,d;if(console.error("Auth error:",null==(e=l.response)?void 0:e.status,null==(i=l.response)?void 0:i.data),(null==(n=l.response)?void 0:n.status)===401){if(o.startsWith("/form-submission"))return;t.push("/")}else s((0,a.jB)((null==(d=l.response)||null==(c=d.data)?void 0:c.message)||l.message))}else s((0,a.jB)(l instanceof Error?l.message:"An unknown error occurred."))}};return(0,n.useEffect)(()=>{(null==e?void 0:e.skipFetchUser)||p()},[null==e?void 0:e.skipFetchUser]),(0,n.useEffect)(()=>{let e=e=>{if("logout"===e.key&&"true"===e.newValue)if(s((0,a.x9)()),o.startsWith("/form-submission")){let e=o.split("/")[2];e?t.push("/form-submission/".concat(e,"/sign-in")):t.push("/")}else t.push("/")};return window.addEventListener("storage",e),()=>{window.removeEventListener("storage",e)}},[s,t,o]),{status:d,user:u,error:m,isAuthenticated:"authenticated"===d,isLoading:"loading"===d,refreshAuthState:()=>{p()},signin:async(e,s,t)=>{try{await l.A.post("/users/login",e),await p(),null==s||s()}catch(e){if(e instanceof r.pe){var a,i;let s=null==(i=e.response)||null==(a=i.data)?void 0:a.errorType;null==t||t(s)}else null==t||t()}},logout:async()=>{try{await l.A.post("/users/logout"),localStorage.setItem("logout","true"),setTimeout(()=>localStorage.removeItem("logout"),100)}finally{if(s((0,a.x9)()),o.startsWith("/form-submission")){let e=o.split("/")[2];e?t.push("/form-submission/".concat(e,"/sign-in")):t.push("/")}else t.push("/")}}}}},42872:(e,s,t)=>{"use strict";t.d(s,{x:()=>m});var a=t(95155),r=t(12115),l=t(13163),i=t(28883),n=t(1243),c=t(25784),o=t(34540),d=t(71402),u=t(17652);let m=e=>{let{email:s,showModal:t,setShowModal:m}=e,p=(0,o.wA)(),h=(0,u.c3)(),x=async()=>{try{await c.A.post("/users/sendverificationemail",{email:s})}catch(e){p((0,d.Ds)({message:h("failedToSendVerification"),type:"error"}))}},[f,g]=(0,r.useState)(!0),[y,j]=(0,r.useState)(60);(0,r.useEffect)(()=>{let e;return f&&y>0?e=window.setInterval(()=>{j(e=>e-1)},1e3):0===y&&(g(!1),j(60)),()=>clearInterval(e)},[f,y]),(0,r.useEffect)(()=>(t&&s&&x(),()=>{j(60),g(!0)}),[t]);let v=async()=>{g(!0);try{await c.A.post("/users/sendverificationemail",{email:s})}catch(e){console.error("error sending email",e)}};return(0,a.jsxs)(l.A,{isOpen:t,onClose:()=>m(!1),className:"flex flex-col items-center gap-4",children:[(0,a.jsx)("div",{className:"rounded-full p-2 bg-primary-300",children:(0,a.jsx)(i.A,{className:"text-primary-500"})}),(0,a.jsx)("h1",{className:"heading-text",children:h("checkYourEmail")}),(0,a.jsxs)("p",{className:"flex flex-col items-center",children:[h("verificationSentTo"),(0,a.jsx)("span",{className:"font-medium",children:s})]}),(0,a.jsxs)("div",{className:"flex gap-2 items-center bg-yellow-100 text-yellow-900 px-4 py-2 rounded-md",children:[(0,a.jsx)(n.A,{size:16})," ",h("didNotReceiveEmail")]}),(0,a.jsx)("button",{className:"btn-primary",onClick:v,disabled:f,children:f?(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)("span",{children:[h("resendIn")," ",y,"s"]}),(0,a.jsx)("div",{className:"size-4 animate-spin border-x-2 rounded-full"})]}):(0,a.jsx)("span",{children:h("resend")})})]})}},54416:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},59362:(e,s,t)=>{"use strict";t.d(s,{F0:()=>u,pe:()=>r});let{Axios:a,AxiosError:r,CanceledError:l,isCancel:i,CancelToken:n,VERSION:c,all:o,Cancel:d,isAxiosError:u,spread:m,toFormData:p,AxiosHeaders:h,HttpStatusCode:x,formToJSON:f,getAdapter:g,mergeConfig:y}=t(23464).A},71402:(e,s,t)=>{"use strict";t.d(s,{Ay:()=>i,Ds:()=>r,_b:()=>l});let a=(0,t(51990).Z0)({name:"notification",initialState:{message:"",type:"",visible:!1},reducers:{showNotification:(e,s)=>{e.message=s.payload.message,e.type=s.payload.type,e.visible=!0},hideNotification:e=>{e.message="",e.type="",e.visible=!1}}}),{showNotification:r,hideNotification:l}=a.actions,i=a.reducer},82151:(e,s,t)=>{Promise.resolve().then(t.bind(t,26956))},90232:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("shield-check",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},97381:(e,s,t)=>{"use strict";t.d(s,{Ay:()=>c,Le:()=>i,jB:()=>n,tQ:()=>r,x9:()=>l});let a=(0,t(51990).Z0)({name:"auth",initialState:{status:"loading",user:null,error:null},reducers:{setAuthenticatedUser:(e,s)=>{e.status="authenticated",e.user=s.payload,e.error=null},setUnauthenticated:e=>{e.status="unauthenticated",e.user=null,e.error=null},setAuthLoading:e=>{e.status="loading"},setAuthError:(e,s)=>{e.status="unauthenticated",e.error=s.payload,e.user=null}}}),{setAuthenticatedUser:r,setUnauthenticated:l,setAuthLoading:i,setAuthError:n}=a.actions,c=a.reducer}},e=>{var s=s=>e(e.s=s);e.O(0,[6453,635,1111,4601,1380,6874,2050,8441,1684,7358],()=>s(82151)),_N_E=e.O()}]);