"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.removeLibraryTemplateGroupFromParentGroup = exports.updateOneLibraryTemplateGroupInsideAnotherGroup = exports.updateLibraryTemplateQuestionFromOneGroupToAnother = exports.removeLibraryTemplateQuestionIdFromGroup = exports.findAllLibraryTemplateGroupByLibrarytemplate = exports.deleteLibraryTemplateQuestionAndGroup = exports.deleteLibraryTemplateQuestionGroup = exports.updateLibraryTemplateQuestionGroup = exports.createLibraryTemplateQuestionGroup = void 0;
const prisma_1 = require("../utils/prisma");
const libraryTemplateQuestionGroupValidator_1 = require("../validators/libraryTemplateQuestionGroupValidator");
const libraryTemplateQuestionGroupRepository_1 = __importDefault(require("../repositories/libraryTemplateQuestionGroupRepository"));
const createLibraryTemplateQuestionGroup = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const result = libraryTemplateQuestionGroupValidator_1.LibraryTemplateQuestionGroupSchema.safeParse(req.body);
        if (!result.success) {
            return res.status(400).json({
                success: false,
                errors: result.error.flatten().fieldErrors,
            });
        }
        const libraryData = result.data;
        const libraryGroup = yield libraryTemplateQuestionGroupRepository_1.default.create(libraryData);
        return res.status(200).json({
            success: true,
            message: "library template group created",
            data: { libraryGroup },
        });
    }
    catch (error) {
        return res.status(500).json({
            success: false,
            message: "error creating library template group",
            error: error instanceof Error ? error.message : "unexpected error",
        });
    }
});
exports.createLibraryTemplateQuestionGroup = createLibraryTemplateQuestionGroup;
const updateLibraryTemplateQuestionGroup = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const result = libraryTemplateQuestionGroupValidator_1.updateLibraryTemplateQuestionGroupSchema.safeParse(req.body);
        if (!result.success) {
            return res.status(400).json({
                success: false,
                errors: result.error.flatten().fieldErrors,
            });
        }
        const { id, title, order, parentGroupId, selectedQuestionIds } = result.data;
        const updates = {
            title,
            order,
            parentGroupId,
        };
        if (selectedQuestionIds && selectedQuestionIds.length > 0) {
            updates.libraryQuestions = {
                set: selectedQuestionIds.map((questionId) => ({
                    id: questionId,
                })),
            };
        }
        const updateLibraryTemplateGroup = yield libraryTemplateQuestionGroupRepository_1.default.update(id, updates);
        return res.status(200).json({
            success: true,
            message: "library template group updated successfully",
            data: { updateLibraryTemplateGroup },
        });
    }
    catch (error) {
        return res.status(500).json({
            success: false,
            message: "Error updating library template group ",
            error: error instanceof Error ? error.message : "Unexpected error",
        });
    }
});
exports.updateLibraryTemplateQuestionGroup = updateLibraryTemplateQuestionGroup;
const deleteLibraryTemplateQuestionGroup = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = req.body;
        if (!id) {
            return res.status(404).json({
                success: false,
                message: "invalid id",
            });
        }
        yield libraryTemplateQuestionGroupRepository_1.default.delete(id);
        return res.status(200).json({
            success: true,
            message: "group deleted sucess",
        });
    }
    catch (error) {
        return res.status(500).json({
            success: false,
            message: "error delete library template group",
            error: error instanceof Error ? error.message : "unexpected error",
        });
    }
});
exports.deleteLibraryTemplateQuestionGroup = deleteLibraryTemplateQuestionGroup;
const deleteLibraryTemplateQuestionAndGroup = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = req.body;
        if (!id) {
            return res.status(404).json({
                success: false,
                message: "invalid id",
            });
        }
        yield libraryTemplateQuestionGroupRepository_1.default.deleteManyQuestionByGroup(id);
        yield libraryTemplateQuestionGroupRepository_1.default.delete(id);
        return res.status(200).json({
            success: true,
            message: "group and question related to that group are delete succesfuly",
        });
    }
    catch (error) {
        return res.status(500).json({
            success: false,
            message: "error delete library template group",
            error: error instanceof Error ? error.message : "unexpected error",
        });
    }
});
exports.deleteLibraryTemplateQuestionAndGroup = deleteLibraryTemplateQuestionAndGroup;
const findAllLibraryTemplateGroupByLibrarytemplate = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = req.body;
        if (!id) {
            return res.status(404).json({
                sucess: false,
                message: "please provide project id",
            });
        }
        const projectGroup = yield libraryTemplateQuestionGroupRepository_1.default.findAllByLibraryTemplate(id);
        return res.status(200).json({
            succes: true,
            message: "library template group fetched success",
            data: { projectGroup },
        });
    }
    catch (error) {
        return res.status(500).json({
            success: false,
            message: "error getting library template group",
            error: error instanceof Error ? error.message : "unexpected error",
        });
    }
});
exports.findAllLibraryTemplateGroupByLibrarytemplate = findAllLibraryTemplateGroupByLibrarytemplate;
const removeLibraryTemplateQuestionIdFromGroup = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { groupId, questionId } = req.body;
        const group = yield prisma_1.prisma.libraryTemplateQuestionGroup.findUnique({
            where: { id: Number(groupId) },
            include: { libraryQuestions: true },
        });
        if (!group) {
            return res.status(404).json({
                success: false,
                message: "library template group not found",
            });
        }
        const questionExists = group.libraryQuestions.some((q) => q.id === Number(questionId));
        if (!questionExists) {
            return res.status(404).json({
                success: false,
                message: "library question not found in this group",
            });
        }
        yield prisma_1.prisma.libraryQuestion.update({
            where: { id: Number(questionId) },
            data: { libraryTemplateQuestionGroupId: null }, // 👈 remove its link to the group
        });
        res.status(200).json({
            success: true,
            message: "Question removed from library template group successfully",
        });
    }
    catch (error) {
        return res.status(500).json({
            success: false,
            message: "error removing library template question from group",
            error: error instanceof Error ? error.message : "unexpected error",
        });
    }
});
exports.removeLibraryTemplateQuestionIdFromGroup = removeLibraryTemplateQuestionIdFromGroup;
const updateLibraryTemplateQuestionFromOneGroupToAnother = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { groupId, newGroupId, questionId } = req.body;
        if (!groupId || !newGroupId || !questionId) {
            return res.status(404).json({
                success: false,
                message: "id not found",
            });
        }
        const groupid = libraryTemplateQuestionGroupRepository_1.default.findById(groupId);
        if (!groupid) {
            return res.status(404).json({
                success: false,
                message: "group id not found",
            });
        }
        const newGroupid = libraryTemplateQuestionGroupRepository_1.default.findById(newGroupId);
        if (!newGroupid) {
            return res.status(404).json({
                success: false,
                message: "new group id not found",
            });
        }
        const question = yield prisma_1.prisma.libraryQuestion.findUnique({
            where: { id: Number(questionId) },
        });
        if (!question) {
            return res.status(404).json({
                success: false,
                message: "library question id not found",
            });
        }
        if (question.libraryTemplateQuestionGroupId !== Number(groupId)) {
            return res.status(400).json({
                success: false,
                message: "library question does not belong to the old group",
            });
        }
        yield prisma_1.prisma.libraryQuestion.update({
            where: { id: Number(questionId) },
            data: {
                libraryTemplateQuestionGroupId: Number(newGroupId),
            },
        });
        return res.status(200).json({
            success: true,
            message: "update success",
        });
    }
    catch (error) {
        return res.status(500).json({
            success: false,
            message: "error adding library question from one group to another",
            error: error instanceof Error ? error.message : "unexpected error",
        });
    }
});
exports.updateLibraryTemplateQuestionFromOneGroupToAnother = updateLibraryTemplateQuestionFromOneGroupToAnother;
const updateOneLibraryTemplateGroupInsideAnotherGroup = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { childGroupId, ParentGroupId } = req.body;
        const childGroupid = libraryTemplateQuestionGroupRepository_1.default.findById(childGroupId);
        if (!childGroupid) {
            return res.status(404).json({
                success: false,
                message: "group id not found",
            });
        }
        const ParentGroupid = libraryTemplateQuestionGroupRepository_1.default.findById(ParentGroupId);
        if (!ParentGroupid) {
            return res.status(404).json({
                success: false,
                message: "new group id not found",
            });
        }
        const update = yield libraryTemplateQuestionGroupRepository_1.default.updateGroupInsideParentGroup(childGroupId, ParentGroupId);
        return res.status(200).json({
            success: false,
            message: "library question Group updated success",
            data: { update },
        });
    }
    catch (error) {
        return res.status(500).json({
            success: false,
            message: "error moving library question group inside the parentGroup",
            error: error instanceof Error ? error.message : "unexpected error",
        });
    }
});
exports.updateOneLibraryTemplateGroupInsideAnotherGroup = updateOneLibraryTemplateGroupInsideAnotherGroup;
const removeLibraryTemplateGroupFromParentGroup = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { groupId } = req.body;
        const groupid = libraryTemplateQuestionGroupRepository_1.default.findById(groupId);
        if (!groupId) {
            return res.status(400).json({
                success: false,
                message: "Group id is required",
            });
        }
        const group = yield libraryTemplateQuestionGroupRepository_1.default.findById(groupId);
        if (!group) {
            return res.status(404).json({
                success: false,
                message: "Group id not found",
            });
        }
        // Optional: Check if group has a parentGroupId
        if (!group.parentGroupId) {
            return res.status(400).json({
                success: false,
                message: "library template group has no parent group to remove",
            });
        }
        yield libraryTemplateQuestionGroupRepository_1.default.RemoveGroupFromParentGroup(groupId);
        return res.status(200).json({
            success: false,
            message: "library question remove success",
        });
    }
    catch (error) {
        return res.status(500).json({
            success: false,
            message: "error adding library question from one group to another",
            error: error instanceof Error ? error.message : "unexpected error",
        });
    }
});
exports.removeLibraryTemplateGroupFromParentGroup = removeLibraryTemplateGroupFromParentGroup;
