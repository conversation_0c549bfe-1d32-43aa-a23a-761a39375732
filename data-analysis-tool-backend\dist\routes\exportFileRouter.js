"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const exportFileController_1 = require("../controllers/exportFileController");
const express_1 = __importDefault(require("express"));
const router = express_1.default.Router();
const auth_1 = require("../middleware/auth");
router.use(auth_1.authenticate);
router.get("/", exportFileController_1.findAllExportFile);
router.post("/:id", exportFileController_1.generateAndSaveExport);
router.delete("/:id", exportFileController_1.DeleteExportFile);
router.get("/download/:fileId", exportFileController_1.downloadExportedFile);
router.delete("/:id", exportFileController_1.DeleteExportFile);
exports.default = router;
