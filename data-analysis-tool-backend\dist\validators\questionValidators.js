"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.questionPositionsSchema = exports.questionWithFileSchema = exports.questionSchema = exports.questionConditionSchema = exports.questionOptionSchema = void 0;
const client_1 = require("@prisma/client");
const zod_1 = require("zod");
exports.questionOptionSchema = zod_1.z.object({
    id: zod_1.z.number().optional(),
    label: zod_1.z.string().min(1, "Option label is required"),
    sublabel: zod_1.z.string().nullable().optional(),
    code: zod_1.z.string().min(1, "Option code is required"),
    nextQuestionId: zod_1.z.number().optional().nullable(),
});
exports.questionConditionSchema = zod_1.z.object({
    id: zod_1.z.number().optional(), // Optional for create; required for update
    operator: zod_1.z.nativeEnum(client_1.Operator, {
        errorMap: () => ({ message: "Invalid operator value" }),
    }),
    value: zod_1.z.string().min(1, "Condition value is required"),
    questionId: zod_1.z.number().optional().nullable(), // May be null
    createdAt: zod_1.z.date().optional(), // Usually set by DB
    updatedAt: zod_1.z.date().optional(), // Usually set by DB
});
// Base schema without the superRefine validation
const baseQuestionSchema = zod_1.z.object({
    label: zod_1.z.string().min(1, "Question label is required"),
    inputType: zod_1.z.nativeEnum(client_1.InputType, {
        errorMap: () => ({ message: "Invalid input type selected" }),
    }),
    isRequired: zod_1.z.boolean().optional().default(false),
    hint: zod_1.z.string().optional(),
    placeholder: zod_1.z.string().optional(),
    position: zod_1.z.number().optional(),
    questionOptions: zod_1.z.array(exports.questionOptionSchema).optional(),
    conditions: zod_1.z.array(exports.questionConditionSchema).optional().nullable(),
});
// Schema for validating questions with manual option entry
exports.questionSchema = baseQuestionSchema.superRefine((data, ctx) => {
    if ((data.inputType === client_1.InputType.selectone ||
        data.inputType === client_1.InputType.selectmany) &&
        (!data.questionOptions || data.questionOptions.length === 0)) {
        ctx.addIssue({
            code: zod_1.z.ZodIssueCode.custom,
            path: ["questionOptions"],
            message: "Options are required for select input types.",
        });
    }
});
// Schema for validating questions with file upload
// This doesn't require questionOptions since they'll come from the file
exports.questionWithFileSchema = baseQuestionSchema
    .omit({
    questionOptions: true,
})
    .extend({
    // We don't need questionOptions for file uploads
    // but we'll keep the conditions
    conditions: zod_1.z.array(exports.questionConditionSchema).optional().nullable(),
});
exports.questionPositionsSchema = zod_1.z.object({
    questionPositions: zod_1.z.array(zod_1.z.object({
        id: zod_1.z.number(),
        position: zod_1.z.number(),
    })),
});
