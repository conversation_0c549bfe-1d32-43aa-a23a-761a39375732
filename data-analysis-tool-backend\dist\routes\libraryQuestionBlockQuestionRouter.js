"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const libraryQuestionBlockQuestionController_1 = require("../controllers/libraryQuestionBlockQuestionController");
const auth_1 = require("../middleware/auth");
const router = express_1.default.Router();
router.post("/", auth_1.authenticate, libraryQuestionBlockQuestionController_1.createLibraryQustionBlockQuestion);
router.get("/", auth_1.authenticate, libraryQuestionBlockQuestionController_1.getAllLibraryQustionBlockQuestion);
router.patch("/:id", auth_1.authenticate, libraryQuestionBlockQuestionController_1.updateLibraryQustionBlockQuestion);
router.delete("/:id", auth_1.authenticate, libraryQuestionBlockQuestionController_1.deleteLibraryQustionBlockQuestion);
exports.default = router;
