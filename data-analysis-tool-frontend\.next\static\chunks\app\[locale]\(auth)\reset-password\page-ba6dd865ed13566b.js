(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5127],{10746:(e,t,s)=>{Promise.resolve().then(s.bind(s,83918))},13163:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var a=s(95155),l=s(60760),r=s(44518),i=s(95233),c=s(54416);s(12115);let n=e=>{let{children:t,className:s,isOpen:n,onClose:o,preventOutsideClick:x=!1}=e;return(0,a.jsx)(l.N,{children:n&&(0,a.jsx)(r.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-neutral-900/50 flex items-center justify-center z-40 p-4 overflow-y-auto",onClick:e=>{x||o()},children:(0,a.jsxs)(r.P.div,{initial:{scale:.6,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.6,opacity:0},transition:{duration:.3,ease:i.am},className:"relative section flex flex-col max-w-4xl max-h-[90vh] overflow-y-auto ".concat(s),onClick:e=>e.stopPropagation(),children:[(0,a.jsx)(c.A,{onClick:o,className:"absolute self-end cursor-pointer text-neutral-700 hover:text-neutral-900 active:scale-90 transition-all duration-300"}),t]})})})}},15616:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var a=s(95155),l=s(46453),r=s(35695),i=s(6874),c=s.n(i);function n(){let e=(0,l.Ym)(),t=(0,r.usePathname)(),s=e=>{let s=t.replace(/^\/(en|ne)/,"");return"/".concat(e).concat(s)};return(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(c(),{href:s("en"),className:"px-3 py-1 rounded ".concat("en"===e?"bg-primary-600 text-white":"bg-gray-200"),children:"English"}),(0,a.jsx)(c(),{href:s("ne"),className:"px-3 py-1 rounded ".concat("ne"===e?"bg-primary-600 text-white":"bg-gray-200"),children:"नेपाली"})]})}},25784:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});let a=s(23464).A.create({baseURL:"http://localhost:4000/api",headers:{"Content-Type":"application/json"},withCredentials:!0});a.interceptors.request.use(e=>e,e=>Promise.reject(e)),a.interceptors.response.use(e=>e,e=>("ERR_NETWORK"===e.code&&console.error("Network error - Please check if the backend server is running"),Promise.reject(e)));let l=a},28883:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},35169:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},54416:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},83918:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>f});var a=s(95155),l=s(12115),r=s(13163),i=s(90232),c=s(28883),n=s(35169),o=s(6874),x=s.n(o),d=s(17652);let m=e=>{let{email:t,showModal:s,setShowModal:l}=e,o=(0,d.c3)(),m=[o("checkEmailSteps1"),o("checkEmailSteps2"),o("checkEmailSteps3"),o("checkEmailSteps4")];return(0,a.jsxs)(r.A,{isOpen:s,onClose:()=>l(!1),className:"flex flex-col gap-8",children:[(0,a.jsxs)("div",{className:"flex flex-col items-center gap-2",children:[(0,a.jsx)(i.A,{size:36}),(0,a.jsx)("h1",{className:"text-2xl tablet:text-3xl font-semibold text-center",children:o("checkYourEmail")}),(0,a.jsxs)("p",{className:"text-neutral-700 text-center",children:[o("resetLinkSentTo")," ",t]})]}),(0,a.jsxs)("div",{className:"rounded-md p-4 bg-neutral-200 text-neutral-700 flex flex-col gap-2",children:[(0,a.jsxs)("span",{className:"flex items-center gap-2 text-lg font-medium",children:[(0,a.jsx)(c.A,{size:18})," ",o("whatToDoNext")]}),(0,a.jsx)("ol",{children:m.map((e,t)=>(0,a.jsx)("li",{children:"".concat(t+1,". ").concat(e)},t))})]}),(0,a.jsxs)(x(),{href:"/",className:"text-neutral-700 self-center flex items-center gap-2",children:[(0,a.jsx)(n.A,{size:16})," ",o("backToSignin")]})]})};var h=s(62177),p=s(25784),u=s(15616);let f=()=>{let{register:e,formState:{errors:t,isSubmitting:s},handleSubmit:r,getValues:c}=(0,h.mN)(),[o,f]=(0,l.useState)(!1),j=(0,d.c3)(),g=async e=>{try{await p.A.post("/users/forgetpassword",{email:e.email}),f(!0)}catch(e){console.error(e instanceof Error?e.message:e)}};return(0,a.jsxs)("div",{className:"flex items-center justify-center min-h-screen",children:[(0,a.jsx)(m,{email:c("email"),showModal:o,setShowModal:f}),(0,a.jsxs)("div",{className:"section flex flex-col gap-8 w-11/12 mobile:w-4/5 tablet:w-lg",children:[(0,a.jsxs)("div",{className:"flex flex-col items-center gap-2",children:[(0,a.jsx)(i.A,{size:36}),(0,a.jsx)("h1",{className:"text-2xl tablet:text-3xl font-semibold text-center",children:j("resetYourPassword")}),(0,a.jsx)("p",{className:"text-neutral-700 text-center",children:j("resetPasswordInstructions")})]}),(0,a.jsxs)("form",{className:"flex flex-col gap-4 ",onSubmit:r(g),noValidate:!0,children:[(0,a.jsxs)("div",{className:"label-input-group group",children:[(0,a.jsx)("label",{htmlFor:"email",className:"label-text",children:j("email")}),(0,a.jsx)("input",{...e("email",{required:j("pleaseEnterYourEmail")}),id:"email",type:"email",className:"input-field",placeholder:"eg: <EMAIL>"}),t.email&&(0,a.jsx)("p",{className:"text-sm text-red-500",children:"".concat(t.email.message)})]}),(0,a.jsx)("button",{type:"submit",className:"btn-primary",children:s?(0,a.jsxs)("span",{className:"flex items-center gap-2",children:[j("sending"),(0,a.jsx)("div",{className:"animate-spin border-x-2 border-neutral-100 rounded-full size-4"})]}):(0,a.jsx)("span",{className:"flex items-center gap-2",children:j("sendResetLink")})})]}),(0,a.jsxs)(x(),{href:"/",className:"text-neutral-700 self-center flex items-center gap-2",children:[(0,a.jsx)(n.A,{size:16})," ",j("backToSignin")]}),(0,a.jsx)(u.A,{})]})]})}},90232:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("shield-check",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[6453,1111,4601,1380,6874,8441,1684,7358],()=>t(10746)),_N_E=e.O()}]);