"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2137],{29350:(e,t,s)=>{s.d(t,{A:()=>h});var r=s(97381),i=s(59362),n=s(25784),o=s(35695),a=s(12115),l=s(34540);let h=e=>{let t=(0,l.wA)(),s=(0,o.useRouter)(),h=(0,o.usePathname)(),{status:u,user:c,error:d}=(0,l.d4)(e=>e.auth),p=async()=>{try{t((0,r.Le)());let e=(await n.A.get("/users/me")).data;t((0,r.tQ)(e))}catch(n){if(t((0,r.x9)()),(0,i.F0)(n)){var e,o,a,l,u;if(console.error("Auth error:",null==(e=n.response)?void 0:e.status,null==(o=n.response)?void 0:o.data),(null==(a=n.response)?void 0:a.status)===401){if(h.startsWith("/form-submission"))return;s.push("/")}else t((0,r.jB)((null==(u=n.response)||null==(l=u.data)?void 0:l.message)||n.message))}else t((0,r.jB)(n instanceof Error?n.message:"An unknown error occurred."))}};return(0,a.useEffect)(()=>{(null==e?void 0:e.skipFetchUser)||p()},[null==e?void 0:e.skipFetchUser]),(0,a.useEffect)(()=>{let e=e=>{if("logout"===e.key&&"true"===e.newValue)if(t((0,r.x9)()),h.startsWith("/form-submission")){let e=h.split("/")[2];e?s.push("/form-submission/".concat(e,"/sign-in")):s.push("/")}else s.push("/")};return window.addEventListener("storage",e),()=>{window.removeEventListener("storage",e)}},[t,s,h]),{status:u,user:c,error:d,isAuthenticated:"authenticated"===u,isLoading:"loading"===u,refreshAuthState:()=>{p()},signin:async(e,t,s)=>{try{await n.A.post("/users/login",e),await p(),null==t||t()}catch(e){if(e instanceof i.pe){var r,o;let t=null==(o=e.response)||null==(r=o.data)?void 0:r.errorType;null==s||s(t)}else null==s||s()}},logout:async()=>{try{await n.A.post("/users/logout"),localStorage.setItem("logout","true"),setTimeout(()=>localStorage.removeItem("logout"),100)}finally{if(t((0,r.x9)()),h.startsWith("/form-submission")){let e=h.split("/")[2];e?s.push("/form-submission/".concat(e,"/sign-in")):s.push("/")}else s.push("/")}}}}},41050:(e,t,s)=>{s.d(t,{A:()=>y});let r=e=>[...new Set(e)],i=(e,t)=>e.filter(e=>!t.includes(e)),n=(e,t)=>e.filter(e=>t.includes(e)),o=e=>"bigint"==typeof e||!Number.isNaN(Number(e))&&Math.floor(Number(e))===e,a=e=>"bigint"==typeof e||e>=0&&Number.isSafeInteger(e);function l(e,t){let s;if(0===t.length)return e;let r=[...e];for(let e=r.length-1,i=0,n=0;e>0;e--,i++){i%=t.length,n+=s=t[i].codePointAt(0);let o=(s+i+n)%e,a=r[e],l=r[o];r[o]=a,r[e]=l}return r}let h=(e,t)=>{let s=[],r=e;if("bigint"==typeof r){let e=BigInt(t.length);do s.unshift(t[Number(r%e)]),r/=e;while(r>BigInt(0))}else do s.unshift(t[r%t.length]),r=Math.floor(r/t.length);while(r>0);return s},u=(e,t)=>e.reduce((s,r)=>{let i=t.indexOf(r);if(-1===i)throw Error(`The provided ID (${e.join("")}) is invalid, as it contains characters that do not exist in the alphabet (${t.join("")})`);if("bigint"==typeof s)return s*BigInt(t.length)+BigInt(i);let n=s*t.length+i;return Number.isSafeInteger(n)?n:(b("Unable to decode the provided string, due to lack of support for BigInt numbers in the current environment"),BigInt(s)*BigInt(t.length)+BigInt(i))},0),c=/^\+?\d+$/,d=e=>{if(!c.test(e))return Number.NaN;let t=Number.parseInt(e,10);return Number.isSafeInteger(t)?t:(b("Unable to encode the provided BigInt string without loss of information due to lack of support for BigInt type in the current environment"),BigInt(e))},p=(e,t,s)=>Array.from({length:Math.ceil(e.length/t)},(r,i)=>s(e.slice(i*t,(i+1)*t))),g=e=>new RegExp(e.map(e=>m(e)).sort((e,t)=>t.length-e.length).join("|")),f=e=>RegExp(`^[${e.map(e=>m(e)).sort((e,t)=>t.length-e.length).join("")}]+$`),m=e=>e.replace(/[\s#$()*+,.?[\\\]^{|}-]/g,"\\$&"),b=(e="BigInt is not available in this environment")=>{if("function"!=typeof BigInt)throw TypeError(e)};class y{constructor(e="",t=0,s="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890",o="cfhistuCFHISTU"){let a,h;if(this.minLength=t,"number"!=typeof t)throw TypeError(`Hashids: Provided 'minLength' has to be a number (is ${typeof t})`);if("string"!=typeof e)throw TypeError(`Hashids: Provided 'salt' has to be a string (is ${typeof e})`);if("string"!=typeof s)throw TypeError(`Hashids: Provided alphabet has to be a string (is ${typeof s})`);let u=Array.from(e),c=Array.from(s),d=Array.from(o);this.salt=u;let p=r(c);if(p.length<16)throw Error(`Hashids: alphabet must contain at least 16 unique characters, provided: ${p.join("")}`);this.alphabet=i(p,d);let m=n(d,p);this.seps=l(m,u),(0===this.seps.length||this.alphabet.length/this.seps.length>3.5)&&(a=Math.ceil(this.alphabet.length/3.5))>this.seps.length&&(h=a-this.seps.length,this.seps.push(...this.alphabet.slice(0,h)),this.alphabet=this.alphabet.slice(h)),this.alphabet=l(this.alphabet,u);let b=Math.ceil(this.alphabet.length/12);this.alphabet.length<3?(this.guards=this.seps.slice(0,b),this.seps=this.seps.slice(b)):(this.guards=this.alphabet.slice(0,b),this.alphabet=this.alphabet.slice(b)),this.guardsRegExp=g(this.guards),this.sepsRegExp=g(this.seps),this.allowedCharsRegExp=f([...this.alphabet,...this.guards,...this.seps])}encode(e,...t){let s=Array.isArray(e)?e:[...null!=e?[e]:[],...t];return 0===s.length?"":(s.every(o)||(s=s.map(e=>"bigint"==typeof e||"number"==typeof e?e:d(String(e)))),s.every(a))?this._encode(s).join(""):""}decode(e){return e&&"string"==typeof e&&0!==e.length?this._decode(e):[]}encodeHex(e){let t=e;switch(typeof t){case"bigint":t=t.toString(16);break;case"string":if(!/^[\dA-Fa-f]+$/.test(t))return"";break;default:throw Error(`Hashids: The provided value is neither a string, nor a BigInt (got: ${typeof t})`)}let s=p(t,12,e=>Number.parseInt(`1${e}`,16));return this.encode(s)}decodeHex(e){return this.decode(e).map(e=>e.toString(16).slice(1)).join("")}isValidId(e){return this.allowedCharsRegExp.test(e)}_encode(e){let{alphabet:t}=this,s=e.reduce((e,t,s)=>e+("bigint"==typeof t?Number(t%BigInt(s+100)):t%(s+100)),0),r=[t[s%t.length]],i=[...r],{seps:n}=this,{guards:o}=this;if(e.forEach((s,o)=>{let a=i.concat(this.salt,t),u=h(s,t=l(t,a));if(r.push(...u),o+1<e.length){let e=u[0].codePointAt(0)+o,t="bigint"==typeof s?Number(s%BigInt(e)):s%e;r.push(n[t%n.length])}}),r.length<this.minLength){let e=(s+r[0].codePointAt(0))%o.length;if(r.unshift(o[e]),r.length<this.minLength){let e=(s+r[2].codePointAt(0))%o.length;r.push(o[e])}}let a=Math.floor(t.length/2);for(;r.length<this.minLength;){t=l(t,t),r.unshift(...t.slice(a)),r.push(...t.slice(0,a));let e=r.length-this.minLength;if(e>0){let t=e/2;r=r.slice(t,t+this.minLength)}}return r}_decode(e){if(!this.isValidId(e))throw Error(`The provided ID (${e}) is invalid, as it contains characters that do not exist in the alphabet (${this.guards.join("")}${this.seps.join("")}${this.alphabet.join("")})`);let t=e.split(this.guardsRegExp),s=+(3===t.length||2===t.length),r=t[s];if(0===r.length)return[];let i=r[Symbol.iterator]().next().value,n=r.slice(i.length).split(this.sepsRegExp),o=this.alphabet,a=[];for(let e of n){let t=[i,...this.salt,...o],s=l(o,t.slice(0,o.length));a.push(u(Array.from(e),s)),o=s}return this._encode(a).join("")!==e?[]:a}}},59362:(e,t,s)=>{s.d(t,{F0:()=>c,pe:()=>i});let{Axios:r,AxiosError:i,CanceledError:n,isCancel:o,CancelToken:a,VERSION:l,all:h,Cancel:u,isAxiosError:c,spread:d,toFormData:p,AxiosHeaders:g,HttpStatusCode:f,formToJSON:m,getAdapter:b,mergeConfig:y}=s(23464).A},77361:(e,t,s)=>{s.d(t,{D_:()=>c,Im:()=>h,Oo:()=>d,c3:()=>n,kf:()=>i,lj:()=>g,or:()=>l,pf:()=>u,vj:()=>o,wI:()=>p,xx:()=>a});var r=s(25784);let i=async e=>{let{projectId:t}=e,{data:s}=await r.A.get("/projects/".concat(t));return s.project},n=async e=>{let{data:t}=await r.A.post("/projects/from-template",e);return t},o=async()=>{try{let{data:e}=await r.A.get("/projects");return e.projects}catch(e){throw console.error("Error fetching projects:",e),e}},a=async e=>{let{data:t}=await r.A.delete("/projects/delete/".concat(e));return t},l=async e=>{try{let{data:t}=await r.A.delete("/projects/delete-multiple",{data:{projectIds:e}});return t}catch(e){throw console.error("Error deleting multiple projects:",e),e}},h=async e=>{try{let{data:t}=await r.A.patch("/projects/change-status/".concat(e),{status:"archived"});return t}catch(e){throw console.error("Error archiving project:",e),e}},u=async function(e){arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{let{data:t}=await r.A.patch("/projects/change-status/".concat(e),{status:"deployed"});return t}catch(e){throw console.error("Error deploying project:",e),e}},c=async e=>{try{let{data:t}=await r.A.patch("/projects/update-many-status",{projectIds:e,status:"archived"});return t}catch(e){throw console.error("Error archiving multiple projects:",e),e}},d=async e=>{try{let{data:t}=await r.A.post("/users/check-email",{email:e});return t}catch(e){var t,s,i,n,o,a;throw Error("object"==typeof(null==(s=e.response)||null==(t=s.data)?void 0:t.message)?JSON.stringify(null==(n=e.response)||null==(i=n.data)?void 0:i.message):(null==(a=e.response)||null==(o=a.data)?void 0:o.message)||e.message||"Failed to check user")}},p=async e=>{let{projectId:t,email:s,permissions:i}=e;try{let e=await d(s);if(!e||!e.success)throw Error((null==e?void 0:e.message)||"User not found");let{data:n}=await r.A.post("/project-users",{userId:e.user.id,projectId:t,permission:i});return n}catch(e){var n,o,a,l,h,u;throw console.error("Error adding user to project:",e),Error("object"==typeof(null==(o=e.response)||null==(n=o.data)?void 0:n.message)?JSON.stringify(null==(l=e.response)||null==(a=l.data)?void 0:a.message):(null==(u=e.response)||null==(h=u.data)?void 0:h.message)||e.message||"Failed to add user")}},g=async e=>{try{let{data:t}=await r.A.post("/answers/multiple",e);return t}catch(e){throw console.error("Error creating answer submission:",e),e}}},86817:(e,t,s)=>{s.d(t,{F:()=>i});var r=s(12115);let i=e=>{let{projectData:t,user:s}=e;return(0,r.useMemo)(()=>{var e,r;let i=(null==s?void 0:s.id)===(null==t||null==(e=t.user)?void 0:e.id),n=null==t||null==(r=t.projectUser)?void 0:r[0],o=(null==n?void 0:n.permission)||{};return{viewForm:i||o.viewForm||!1,editForm:i||o.editForm||!1,viewSubmissions:i||o.viewSubmissions||!1,addSubmissions:i||o.addSubmissions||!1,deleteSubmissions:i||o.deleteSubmissions||!1,validateSubmissions:i||o.validateSubmissions||!1,editSubmissions:i||o.editSubmissions||!1,manageProject:i||o.manageProject||!1}},[null==s?void 0:s.id,t])}},88570:(e,t,s)=>{s.d(t,{D:()=>a,l:()=>o});var r=s(41050);let i=s(49509).env.SALT||"rushan-salt",n=new r.A(i,12),o=e=>n.encode(e),a=e=>{let t=n.decode(e)[0];return"bigint"==typeof t?t<Number.MAX_SAFE_INTEGER?Number(t):null:"number"==typeof t?t:null}},97381:(e,t,s)=>{s.d(t,{Ay:()=>l,Le:()=>o,jB:()=>a,tQ:()=>i,x9:()=>n});let r=(0,s(51990).Z0)({name:"auth",initialState:{status:"loading",user:null,error:null},reducers:{setAuthenticatedUser:(e,t)=>{e.status="authenticated",e.user=t.payload,e.error=null},setUnauthenticated:e=>{e.status="unauthenticated",e.user=null,e.error=null},setAuthLoading:e=>{e.status="loading"},setAuthError:(e,t)=>{e.status="unauthenticated",e.error=t.payload,e.user=null}}}),{setAuthenticatedUser:i,setUnauthenticated:n,setAuthLoading:o,setAuthError:a}=r.actions,l=r.reducer}}]);