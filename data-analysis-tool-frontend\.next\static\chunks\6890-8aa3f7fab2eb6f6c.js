(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6890],{13163:(e,t,s)=>{"use strict";s.d(t,{A:()=>o});var r=s(95155),l=s(60760),i=s(44518),n=s(95233),a=s(54416);s(12115);let o=e=>{let{children:t,className:s,isOpen:o,onClose:d,preventOutsideClick:c=!1}=e;return(0,r.jsx)(l.N,{children:o&&(0,r.jsx)(i.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-neutral-900/50 flex items-center justify-center z-40 p-4 overflow-y-auto",onClick:e=>{c||d()},children:(0,r.jsxs)(i.P.div,{initial:{scale:.6,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.6,opacity:0},transition:{duration:.3,ease:n.am},className:"relative section flex flex-col max-w-4xl max-h-[90vh] overflow-y-auto ".concat(s),onClick:e=>e.stopPropagation(),children:[(0,r.jsx)(a.A,{onClick:d,className:"absolute self-end cursor-pointer text-neutral-700 hover:text-neutral-900 active:scale-90 transition-all duration-300"}),t]})})})}},26862:(e,t,s)=>{"use strict";s.d(t,{Sc:()=>x.S,dO:()=>p}),s(97168),s(89852),s(82714),s(99474);var r=s(95155),l=s(12115),i=s(38715),n=s(66474),a=s(47863),o=s(5196),d=s(53999);i.bL,i.YJ,i.WT,l.forwardRef((e,t)=>{let{className:s,children:l,...a}=e;return(0,r.jsxs)(i.l9,{ref:t,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-gray-200 bg-neutral-100 px-3 py-2 text-sm shadow-sm ring-offset-neutral-100 placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1 dark:border-gray-700 dark:bg-gray-900 dark:ring-offset-gray-900 dark:placeholder:text-gray-500",s),...a,children:[l,(0,r.jsx)(i.In,{asChild:!0,children:(0,r.jsx)(n.A,{className:"h-4 w-4 opacity-50"})})]})}).displayName=i.l9.displayName;let c=l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,r.jsx)(i.PP,{ref:t,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",s),...l,children:(0,r.jsx)(a.A,{className:"h-4 w-4"})})});c.displayName=i.PP.displayName;let u=l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,r.jsx)(i.wn,{ref:t,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",s),...l,children:(0,r.jsx)(n.A,{className:"h-4 w-4"})})});u.displayName=i.wn.displayName,l.forwardRef((e,t)=>{let{className:s,children:l,position:n="popper",...a}=e;return(0,r.jsx)(i.ZL,{children:(0,r.jsxs)(i.UC,{ref:t,className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border border-gray-200 bg-neutral-100 text-slate-700 shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 dark:border-gray-700 dark:bg-gray-900 dark:text-slate-200","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",s),position:n,...a,children:[(0,r.jsx)(c,{}),(0,r.jsx)(i.LM,{className:(0,d.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:l}),(0,r.jsx)(u,{})]})})}).displayName=i.UC.displayName,l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,r.jsx)(i.JU,{ref:t,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",s),...l})}).displayName=i.JU.displayName,l.forwardRef((e,t)=>{let{className:s,children:l,...n}=e;return(0,r.jsxs)(i.q7,{ref:t,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-gray-100 focus:text-gray-900 data-[disabled]:pointer-events-none data-[disabled]:opacity-50 dark:focus:bg-gray-800 dark:focus:text-gray-50",s),...n,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(i.VF,{children:(0,r.jsx)(o.A,{className:"h-4 w-4"})})}),(0,r.jsx)(i.p4,{children:l})]})}).displayName=i.q7.displayName,l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,r.jsx)(i.wv,{ref:t,className:(0,d.cn)("-mx-1 my-1 h-px bg-gray-200 dark:bg-gray-700",s),...l})}).displayName=i.wv.displayName;var m=s(4884);let p=l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,r.jsx)(m.bL,{className:(0,d.cn)("peer inline-flex h-[24px] w-[44px] shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 focus-visible:ring-offset-neutral-100 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary-500 data-[state=unchecked]:bg-neutral-100 dark:focus-visible:ring-offset-gray-900",s),...l,ref:t,children:(0,r.jsx)(m.zi,{className:(0,d.cn)("pointer-events-none block h-5 w-5 rounded-full shadow-lg ring-0 transition-transform data-[state=checked]:bg-neutral-100 data-[state=unchecked]:bg-primary-500 data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})});p.displayName=m.bL.displayName;var x=s(95139);s(55747)},32383:()=>{},41232:(e,t,s)=>{"use strict";s.d(t,{o:()=>ex});var r=s(95155),l=s(12115),i=s(75143),n=s(50402),a=s(78266),o=s(48021),d=s(18084),c=s(74126),u=s(89917),m=s(17652);let p=e=>{let{question:t,onEdit:s,onDelete:l,onDuplicate:i,isSelected:p=!1,onToggleSelect:x,selectionMode:h=!1}=e,{attributes:b,listeners:g,setNodeRef:v,transform:f,transition:j,isDragging:y}=(0,n.gl)({id:t.id,data:{type:"question",questionId:t.id,questionGroupId:"questionGroupId"in t?t.questionGroupId:void 0}}),N={transform:a.Ks.Transform.toString(f),transition:j,opacity:y?.5:1},w=(0,m.c3)();return(0,r.jsx)("div",{ref:v,style:N,className:"border border-neutral-400 rounded-md bg-card shadow-sm",children:(0,r.jsxs)("div",{className:"flex items-center p-4",children:[h&&(0,r.jsx)("div",{className:"mr-2",children:(0,r.jsx)("input",{type:"checkbox",checked:p,onChange:()=>x&&x(),className:"h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"})}),(0,r.jsx)("div",{...b,...g,className:"cursor-move mr-3 hover:text-primary",children:(0,r.jsx)(o.A,{className:"h-5 w-5"})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h3",{className:"text-xl font-semibold",children:t.label||(0,r.jsx)("span",{className:"text-muted-foreground italic",children:w("emptyQuestion")})}),t.hint&&(0,r.jsx)("p",{className:"text-sm sub-text mt-1",children:t.hint})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)("button",{onClick:e=>{e.stopPropagation(),i()},title:w("duplicate"),className:"cursor-pointer p-2 rounded-full hover:bg-primary-100 text-neutral-700 hover:text-primary-500 transition-colors",children:(0,r.jsx)(d.A,{size:16})}),(0,r.jsx)("button",{onClick:e=>{e.stopPropagation(),l()},title:w("delete"),className:"cursor-pointer p-2 rounded-full hover:bg-destructive/10 text-neutral-700 hover:text-destructive transition-colors",children:(0,r.jsx)(c.A,{size:16})}),(0,r.jsx)("button",{onClick:e=>{e.stopPropagation(),s()},title:w("edit"),className:"cursor-pointer p-2 rounded-full hover:bg-primary-100 text-neutral-700 hover:text-primary-500 transition-colors",children:(0,r.jsx)(u.A,{size:16})})]})]})})};var x=s(66474),h=s(13052),b=s(9343),g=s(84616),v=s(13717);let f=e=>{let{id:t,title:s,questions:d,subGroups:u=[],parentGroupId:j,nestingLevel:y=0,onEditGroup:N,onDeleteGroup:w,onAddQuestionToGroup:q,onEditQuestion:C,onDeleteQuestion:k,onDuplicateQuestion:I,onReorderQuestions:E,onMoveQuestionBetweenGroups:Q,onMoveGroupInsideGroup:S,isEditing:A=!1,onStartEditing:T,onSaveGroupName:G,onCancelEditing:D,editingName:F="",onEditingNameChange:z,selectionMode:R=!1,isDraggable:O=!0,selectedQuestionIds:M=[],onToggleQuestionSelect:P,onCreateSubgroup:L}=e,[K,B]=(0,l.useState)(!0),V=d.filter(e=>M.includes(e.id)),{attributes:U,listeners:$,setNodeRef:J,transform:H,transition:_,isDragging:W}=(0,n.gl)({id:"group-".concat(t),data:{type:"group",groupId:t,parentGroupId:j}}),{setNodeRef:X,isOver:Z}=(0,i.zM)({id:"group-drop-".concat(t),data:{type:"group-drop",groupId:t}}),Y={transform:a.Ks.Transform.toString(H),transition:_,opacity:W?.5:1},ee=(0,m.c3)(),et=(0,i.FR)((0,i.MS)(i.AN,{activationConstraint:{distance:8}}),(0,i.MS)(i.uN));return(0,r.jsxs)("div",{ref:e=>{J(e),X(e)},style:Y,className:"border border-neutral-400 rounded-md bg-card shadow-sm mb-4 ".concat(Z?"ring-2 ring-primary-500 ring-opacity-50":""," ").concat(y>0?"ml-8 border-l-4 border-l-primary-300":""),children:[(0,r.jsxs)("div",{className:"flex items-center p-4 bg-neutral-100 border-b border-neutral-300 rounded-t-md",children:[O&&(0,r.jsx)("div",{...U,...$,className:"cursor-move mr-2 hover:text-primary-500 transition-colors",title:"Drag to reorder group",children:(0,r.jsx)(o.A,{className:"h-5 w-5"})}),(0,r.jsx)("button",{onClick:()=>B(!K),className:"mr-2 text-neutral-700 hover:text-primary-500 transition-colors","aria-label":ee(K?"collapseGroup":"expandGroup"),children:K?(0,r.jsx)(x.A,{className:"h-5 w-5"}):(0,r.jsx)(h.A,{className:"h-5 w-5"})}),A?(0,r.jsx)("div",{className:"flex-1 mr-4",children:(0,r.jsx)("input",{type:"text",value:F,onChange:e=>z&&z(e.target.value),className:"w-full p-2 border border-gray-300 rounded",autoFocus:!0,onKeyDown:e=>{"Enter"===e.key?G&&G(t):"Escape"===e.key&&D&&D()},placeholder:ee("enterGroupName")})}):(0,r.jsx)("h3",{className:"flex-1 font-medium text-lg cursor-pointer hover:text-primary-500",onClick:()=>T&&T(t,s),title:ee("clickToEditGroupName"),children:s}),(0,r.jsx)("div",{className:"flex items-center space-x-3",children:A?(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)("button",{onClick:()=>D&&D(),title:ee("cancelEditing"),className:"cursor-pointer px-3 py-1 rounded btn-outline",children:ee("cancel")}),(0,r.jsx)("button",{onClick:()=>G&&G(t),title:ee("saveGroupName"),className:"cursor-pointer px-3 py-1 rounded btn-primary",children:ee("save")})]}):(0,r.jsxs)(r.Fragment,{children:[R&&V.length>0&&(0,r.jsxs)("button",{onClick:()=>{V.length>0&&L&&L(t,V.map(e=>e.id))},title:"Create Subgroup (".concat(V.length," questions)"),className:"cursor-pointer px-3 py-1 rounded btn-primary text-sm flex items-center gap-1",children:[(0,r.jsx)(b.A,{size:14}),"Create Subgroup (",V.length,")"]}),(0,r.jsx)("button",{onClick:()=>q(t),title:ee("addQuestionToGroup"),className:"cursor-pointer p-2 rounded-full hover:bg-primary-100 text-neutral-700 hover:text-primary-500 transition-colors",children:(0,r.jsx)(g.A,{size:16})}),(0,r.jsx)("button",{onClick:()=>T&&T(t,s),title:ee("editGroupName"),className:"cursor-pointer p-2 rounded-full hover:bg-primary-100 text-neutral-700 hover:text-primary-500 transition-colors",children:(0,r.jsx)(v.A,{size:16})}),(0,r.jsx)("button",{onClick:()=>w(t),title:ee("deleteGroup"),className:"cursor-pointer p-2 rounded-full hover:bg-destructive/10 text-neutral-700 hover:text-destructive transition-colors",children:(0,r.jsx)(c.A,{size:16})})]})})]}),K&&(0,r.jsxs)("div",{className:"p-4 space-y-4",children:[u&&u.length>0&&(0,r.jsx)("div",{className:"space-y-4",children:u.sort((e,t)=>e.order-t.order).map(e=>(0,r.jsx)(f,{id:e.id,title:e.title,questions:e.question||[],subGroups:e.subGroups,parentGroupId:t,nestingLevel:y+1,onEditGroup:N,onDeleteGroup:w,onAddQuestionToGroup:q,onEditQuestion:C,onDeleteQuestion:k,onDuplicateQuestion:I,onReorderQuestions:E,onMoveQuestionBetweenGroups:Q,onMoveGroupInsideGroup:S,selectionMode:R,isDraggable:O,selectedQuestionIds:M,onToggleQuestionSelect:P,onCreateSubgroup:L},e.id))}),d.length>0?(0,r.jsx)(i.Mp,{sensors:et,collisionDetection:i.fp,onDragEnd:e=>{let{active:t,over:s}=e;if(!s||t.id===s.id)return;let r=t.data.current,l=s.data.current;if((null==r?void 0:r.type)==="question"&&(null==l?void 0:l.type)==="question"&&E){let e=[...d].sort((e,t)=>e.position-t.position),r=e.findIndex(e=>e.id===t.id),l=e.findIndex(e=>e.id===s.id);if(-1===r||-1===l)return;E((0,n.be)(e,r,l).map((e,t)=>({id:Number(e.id),position:t+1})))}if((null==r?void 0:r.type)==="question"&&(null==l?void 0:l.type)==="group-drop"&&Q){let e=Number(t.id),s=r.questionGroupId||null,i=l.groupId;s!==i&&Q(e,s,i)}if((null==r?void 0:r.type)==="group"&&(null==l?void 0:l.type)==="group-drop"&&S){let e=r.groupId,t=l.groupId;e!==t&&S(e,t)}},children:(0,r.jsx)(n.gB,{items:d.map(e=>e.id),strategy:n._G,children:d.sort((e,t)=>e.position-t.position).map(e=>(0,r.jsx)("div",{className:"mb-4",children:(0,r.jsx)(p,{question:e,onEdit:()=>C(e),onDelete:()=>k(e),onDuplicate:()=>I(e),selectionMode:R,isSelected:M.includes(e.id),onToggleSelect:()=>P&&P(e.id)})},e.id))})}):(!u||0===u.length)&&(0,r.jsxs)("div",{className:"text-center py-4 text-neutral-500",children:[ee("noQuestionsInGroup"),"              "]})]})]})};var j=s(92657),y=s(5040),N=s(49103),w=s(13163),q=s(62177),C=s(50408),k=s(64368);let I={text:"Text",number:"Number",decimal:"Decimal",selectone:"Select one",selectmany:"Select many",date:"Date",dateandtime:"Date and time",table:"Table"};Object.keys(I);var E=s(26862),Q=s(26715),S=s(5041),A=s(34947),T=s(19373);let G=e=>{let{contextType:t,contextId:s,value:l,onChange:i,currentQuestionId:n,placeholder:a="Select next question (optional)"}=e,{data:o=[],isLoading:d,error:c}=(0,T.I)({queryKey:"project"===t?["questions",s]:"template"===t?["templateQuestions",s]:["questionBlockQuestions",s],queryFn:()=>"project"===t?(0,A.K4)({projectId:s}):"template"===t?(0,A.ej)({templateId:s}):"questionBlock"===t?(0,A.dI)():[],enabled:!!s}),u=o.filter(e=>e.id!==n),m=u.find(e=>e.id===l);return(0,r.jsxs)("div",{className:"flex flex-col gap-1",children:[(0,r.jsxs)("select",{value:l||"",onChange:e=>{let t=e.target.value;i(t?parseInt(t):null)},className:"input-field text-sm",disabled:d,children:[(0,r.jsx)("option",{value:"",children:d?"Loading questions...":a}),u.map(e=>(0,r.jsx)("option",{value:e.id,children:e.label||"Question ".concat(e.id)},e.id))]}),m&&(0,r.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:["Type: ",m.inputType]})]})},D=e=>{var t;let{contextType:s,contextId:i,currentQuestionId:n,inputType:a}=e,{control:o,register:d,formState:{errors:u},setValue:m,watch:p}=(0,q.xW)(),{fields:x,append:h,remove:b}=(0,q.jz)({control:o,name:"questionOptions"});(0,l.useEffect)(()=>{0===x.length&&h({label:"",sublabel:"",code:"",nextQuestionId:null})},[x,h]);let v="selectone"===a||"selectmany"===a;return(0,r.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,r.jsx)("label",{className:"label-text",children:"Options"}),(0,r.jsxs)("div",{className:"flex flex-col gap-2",children:[x.map((e,t)=>(0,r.jsxs)("div",{className:"border  border-gray-400 rounded-lg p-3 space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("input",{...d("questionOptions.".concat(t,".label")),placeholder:"Option ".concat(t+1),className:"input-field flex-1 min-w-[150px]"}),(0,r.jsx)("input",{...d("questionOptions.".concat(t,".sublabel")),placeholder:"Sub Options",className:"input-field flex-1 min-w-[150px]"}),(0,r.jsx)("input",{...d("questionOptions.".concat(t,".code")),placeholder:"Code",className:"w-28 input-field"}),(0,r.jsx)("button",{type:"button",onClick:()=>b(t),className:"p-2 rounded-full hover:bg-red-500/10 text-neutral-700 hover:text-red-500 transition-colors cursor-pointer duration-300",children:(0,r.jsx)(c.A,{size:16})})]}),v&&s&&i&&(0,r.jsxs)("div",{className:"ml-2",children:[(0,r.jsx)("label",{className:"text-xs text-gray-600 mb-1 block",children:"Next Question (when this option is selected):"}),(0,r.jsx)(G,{contextType:s,contextId:i,currentQuestionId:n,value:p("questionOptions.".concat(t,".nextQuestionId")),onChange:e=>{m("questionOptions.".concat(t,".nextQuestionId"),e)},placeholder:"No follow-up question"})]})]},t)),u.questionOptions&&(0,r.jsx)("p",{className:"text-sm text-red-500",children:"".concat(null==(t=u.questionOptions.root)?void 0:t.message)}),(0,r.jsxs)("button",{type:"button",onClick:()=>h({label:"",sublabel:"",code:"",nextQuestionId:null}),className:"btn-outline mt-2 flex items-center justify-center gap-2",children:[(0,r.jsx)(g.A,{size:16}),"Add Option"]})]})]})};var F=s(90221);let z=e=>["selectone","selectmany"].includes(e);var R=s(55594);let O=Object.keys(I),M=R.z.object({label:R.z.string().min(1,"Question name is required"),inputType:R.z.enum(O),hint:R.z.string().optional(),placeholder:R.z.string().optional(),questionOptions:R.z.array(R.z.object({label:R.z.string(),sublabel:R.z.string().optional(),code:R.z.string(),nextQuestionId:R.z.number().optional().nullable()})).optional()}).superRefine((e,t)=>{z(e.inputType)});var P=s(57799);let L=()=>(0,r.jsx)("div",{className:"fixed top-0 left-0 h-screen w-screen bg-neutral-900/20 z-50 flex items-center justify-center",onClick:e=>{e.stopPropagation()},children:(0,r.jsx)(P.A,{})});var K=s(97168),B=s(89852),V=s(82714),U=s(78749),$=s(95749),J=s(88524),H=s(53999);function _(e){let{projectId:t,onTableCreated:s,isInModal:i=!1,isEditMode:n=!1,existingTableData:a}=e,{toast:o}=function(){let[e,t]=(0,l.useState)([]);return{toast:e=>{t(t=>[...t,e]),setTimeout(()=>{t(t=>t.filter(t=>t!==e))},3e3)},toasts:e}}(),[d,u]=(0,l.useState)((null==a?void 0:a.label)||""),[m,p]=(0,l.useState)(()=>{if(null==a?void 0:a.tableColumns){let e=[];return a.tableColumns.forEach(t=>{let s={id:"col-".concat(t.id),columnName:t.columnName,level:0,parentColumnId:void 0};e.push(s),t.childColumns&&t.childColumns.length>0&&(console.error("Processing ".concat(t.childColumns.length,' child columns for parent "').concat(t.columnName,'"')),t.childColumns.forEach(s=>{let r={id:"col-".concat(s.id),columnName:s.columnName,level:1,parentId:"col-".concat(t.id),parentColumnId:t.id};e.push(r)}))}),e.length>0?e:[{id:"col-1",columnName:"",level:0}]}return[{id:"col-1",columnName:"",level:0}]}),[x,h]=(0,l.useState)(()=>(null==a?void 0:a.tableRows)&&a.tableRows.length>0?[...a.tableRows].sort((e,t)=>e.id-t.id).map(e=>({id:e.id,rowsName:e.rowsName})):[{rowsName:"Row 1"}]),[b,v]=(0,l.useState)(!1),[f,y]=(0,l.useState)(!0),N=()=>"col-".concat(Date.now(),"-").concat(Math.floor(1e3*Math.random())),w=()=>{p([...m,{id:N(),columnName:"",level:0}])},q=e=>{let t=C(e);if(!t)return;if(t.level>0)return void o({title:"Cannot add child column",description:"Cannot create more than 2 levels of nested columns (parent → child → grandchild)",variant:"destructive"});if(m.filter(t=>t.parentId===e).length>=2)return void o({title:"Cannot add more child columns",description:'Parent column "'.concat(t.columnName||"Unnamed",'" cannot have more than 2 child columns'),variant:"destructive"});let s=e.match(/^col-(\d+)/),r=s?parseInt(s[1],10):void 0,l={id:N(),columnName:"",parentId:e,level:t.level+1,parentColumnId:r},i=[...m],n=k(e);if(-1===n||n===m.findIndex(t=>t.id===e)){let t=m.findIndex(t=>t.id===e);i.splice(t+1,0,l)}else i.splice(n+1,0,l);p(i)},C=e=>m.find(t=>t.id===e),k=e=>{let t=m.findIndex(t=>t.id===e);if(-1===t)return -1;let s=t,r=!1;for(let l=t+1;l<m.length;l++)if(m[l].parentId===e)s=l,r=!0;else if(I(m[l],e))s=l,r=!0;else break;return r?s:t},I=(e,t)=>{if(e.parentId===t)return!0;if(!e.parentId)return!1;let s=C(e.parentId);return!!s&&I(s,t)},E=()=>{let e=x.length+1;h([...x,{rowsName:"Row ".concat(e)}])},Q=e=>{if(m.length<=1)return;let t=new Set([e]);m.forEach(s=>{I(s,e)&&t.add(s.id)});let s=m.filter(e=>!t.has(e.id));0===s.length&&s.push({id:N(),columnName:"",level:0}),p(s)},S=e=>{let t=[...x];t.splice(e,1),h(t)},A=(e,t)=>{p(m.map(s=>s.id===e?{...s,columnName:t}:s))},T=(e,t)=>{let s=[...x];s[e]={...s[e],rowsName:t},h(s);let r=document.querySelectorAll(".row-input");r&&r[e]&&(t.trim()?r[e].classList.remove("border-red-500"):r[e].classList.add("border-red-500"))},G=()=>{var e;let t=n||(null==a?void 0:a.id);console.log("Is editing mode:",t);let s=new Map;t&&(null==a?void 0:a.tableColumns)&&a.tableColumns.forEach(e=>{let t="col-".concat(e.id);s.set(t,e.id)});let r=new Map;m.forEach(e=>{if(e.parentId){var t;r.has(e.parentId)||r.set(e.parentId,[]),null==(t=r.get(e.parentId))||t.push(e.id)}}),t&&(null==a?void 0:a.tableColumns)&&m.forEach(e=>{if(e.columnName.trim()){let t=e.id.match(/^col-(\d+)/);if(t&&t[1]){let r=parseInt(t[1],10);a.tableColumns.find(e=>e.id===r)&&s.set(e.id,r)}}});let l=Math.max(...Array.from(s.values(),e=>e||0),...(null==a||null==(e=a.tableColumns)?void 0:e.map(e=>e.id))||[0],0)+1;m.forEach(e=>{e.columnName.trim()&&!s.has(e.id)&&s.set(e.id,l++)});let i=[],d=new Map,c=Math.max(...m.map(e=>e.level||0),0);for(let e=0;e<=c;e++)m.filter(t=>t.level===e&&t.columnName.trim()).forEach(e=>{let r=s.get(e.id),l={columnName:e.columnName.trim()};if(t&&r&&(l.id=r),e.parentId)if(t){m.find(t=>t.id===e.parentId);let t=s.get(e.parentId);t?l.parentColumnId=t:(console.warn('Could not find parent DB ID for column "'.concat(e.columnName,'" (parentId: ').concat(e.parentId,")")),o({title:"Warning",description:'Column "'.concat(e.columnName,'" had a missing parent reference and was converted to a top-level column.'),variant:"destructive"}))}else{let t=d.get(e.parentId);void 0!==t?l.parentColumnId=t+1:(console.warn('Could not find parent position for column "'.concat(e.columnName,'" (parentId: ').concat(e.parentId,")")),o({title:"Warning",description:'Column "'.concat(e.columnName,'" had a missing parent reference and was converted to a top-level column.'),variant:"destructive"}))}let n=i.length;i.push(l),d.set(e.id,n)});let u=i.filter(e=>{if(void 0===e.parentColumnId)return!1;if(t){if(e.parentColumnId<=0)return!0}else{if(e.parentColumnId<=0||e.parentColumnId>i.length)return!0;let t=i[e.parentColumnId-1];if(t&&void 0!==t.parentColumnId)return!0}return!1});return u.length>0&&(console.error("Found invalid parent column references:",u),u.forEach(e=>{e.parentColumnId=void 0}),o({title:"Warning",description:"Fixed ".concat(u.length," invalid column relationships. Some child columns were converted to top-level columns."),variant:"destructive"})),i},D=async e=>{if(e&&e.preventDefault(),!d.trim()){console.log("Validation failed: Empty label"),o({title:"Error",description:"Please enter a table label",variant:"destructive"});return}let r=m.filter(e=>e.columnName.trim()),l=x.filter(e=>e.rowsName.trim()),i=document.querySelectorAll(".row-input");if(x.forEach((e,t)=>{i&&i[t]&&(e.rowsName.trim()?i[t].classList.remove("border-red-500"):i[t].classList.add("border-red-500"))}),0===r.length){console.log("Validation failed: No valid columns"),o({title:"Error",description:"Please add at least one column with a name",variant:"destructive"});return}if(0===l.length){console.log("Validation failed: No valid rows"),o({title:"Error",description:"Please add at least one row with a name",variant:"destructive"});return}console.log("Validation passed, proceeding with submission"),v(!0);try{let e;console.log("Preparing columns for submission");let r=G();console.log("Prepared API columns:",r);let i=[...l].sort((e,t)=>e.id&&t.id?e.id-t.id:e.id?-1:t.id?1:l.indexOf(e)-l.indexOf(t)).map(e=>{let t={rowsName:e.rowsName.trim()};if(n||(null==a?void 0:a.id)){var s;e.id&&(null==a||null==(s=a.tableRows)?void 0:s.some(t=>t.id===e.id))&&(t.id=e.id)}return t});(null==a?void 0:a.id)?(e=await (0,$.am)(a.id,d.trim(),r,i),o({title:"Success",description:"Table question updated successfully"})):(e=await (0,$.ZR)(d.trim(),t,r,i),o({title:"Success",description:"Table question created successfully"}),u(""),p([{id:N(),columnName:"",level:0}]),h([])),s&&(null==e?void 0:e.id)&&s(e.id)}catch(t){var c,b;console.error("Error with table operation:",t);let e=(null==a?void 0:a.id)?"Failed to update table question":"Failed to create table question";(null==(b=t.response)||null==(c=b.data)?void 0:c.message)?e=t.response.data.message:t.message&&(e=t.message),o({title:"Error",description:e,variant:"destructive"})}finally{v(!1)}},F=l.useRef(null);return(0,l.useEffect)(()=>{if(i){let e=e=>{e.cancelable&&e.stopPropagation(),D()};F.current&&F.current.addEventListener("submitTable",e),document.addEventListener("submitTable",e);let t=document.querySelectorAll(".table-question-builder");return t.forEach(t=>{t.addEventListener("submitTable",e)}),()=>{F.current&&F.current.removeEventListener("submitTable",e),document.removeEventListener("submitTable",e),t.forEach(t=>{t.removeEventListener("submitTable",e)})}}},[i,D,m]),(0,r.jsxs)("div",{ref:F,className:"space-y-6 p-4 border border-gray-200 rounded-md w-full table-question-builder",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("h3",{className:"text-lg font-medium",children:n||(null==a?void 0:a.id)?"Edit Table Question":"Create Table Question"}),(0,r.jsx)(K.$,{type:"button",variant:"outline",size:"sm",onClick:()=>y(!f),className:"flex items-center gap-1",children:f?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(U.A,{className:"h-4 w-4"}),"Hide Preview"]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(j.A,{className:"h-4 w-4"}),"Show Preview"]})})]}),(0,r.jsxs)("div",{className:"bg-blue-50 p-3 rounded-md text-sm text-blue-800 border border-blue-200",children:[(0,r.jsx)("p",{className:"font-medium mb-1",children:"Table Structure Guidelines:"}),(0,r.jsxs)("ul",{className:"list-disc pl-5 space-y-1",children:[(0,r.jsxs)("li",{children:["Create multiple ",(0,r.jsx)("span",{className:"font-medium",children:"parent columns"})," ",'using the "Add Top-Level Column" button']}),(0,r.jsxs)("li",{children:["Add up to 2 ",(0,r.jsx)("span",{className:"font-medium",children:"child columns"}),' under each parent using the "+" button']}),(0,r.jsx)("li",{children:"Child columns cannot have their own children (maximum 2 levels)"})]})]}),i?(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(V.J,{htmlFor:"table-label",children:"Table Label"}),(0,r.jsx)(B.p,{id:"table-label",value:d,onChange:e=>u(e.target.value),placeholder:"Enter table question label",required:!0})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 max-h-[60vh] overflow-y-auto",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(V.J,{children:"Columns"}),m.map(e=>(0,r.jsxs)("div",{className:(0,H.cn)("flex items-center gap-2 p-2 rounded-md",0===e.level?"bg-gray-50":"bg-white border-l-2 border-gray-300"),style:{marginLeft:"".concat(20*e.level,"px")},children:[(0,r.jsxs)("div",{className:"flex-1 flex items-center gap-2",children:[e.level>0&&(0,r.jsx)("div",{className:"w-4 h-4 flex items-center justify-center",children:(0,r.jsx)("div",{className:"w-2 h-2 bg-blue-400 rounded-full"})}),(0,r.jsx)(B.p,{value:e.columnName,onChange:t=>A(e.id,t.target.value),placeholder:"".concat(0===e.level?"Parent":"Child"," Column"),className:(0,H.cn)("flex-1",0===e.level?"border-blue-200 bg-white":"border-dashed")}),0===e.level&&(0,r.jsx)("div",{className:"text-xs text-blue-500 font-medium",children:"Parent"}),e.level>0&&(0,r.jsx)("div",{className:"text-xs text-gray-500 font-medium",children:"Child"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(K.$,{type:"button",variant:"ghost",size:"icon",onClick:()=>q(e.id),title:e.level>0?"Child columns cannot have their own children":m.filter(t=>t.parentId===e.id).length>=2?"Maximum 2 child columns allowed":"Add child column",disabled:e.level>0||m.filter(t=>t.parentId===e.id).length>=2,children:(0,r.jsx)(g.A,{className:(0,H.cn)("h-4 w-4",(e.level>0||m.filter(t=>t.parentId===e.id).length>=2)&&"text-gray-300")})}),(0,r.jsx)(K.$,{type:"button",variant:"ghost",size:"icon",onClick:()=>Q(e.id),disabled:m.length<=1,title:"Remove column",children:(0,r.jsx)(c.A,{className:"h-4 w-4"})})]})]},e.id)),(0,r.jsxs)(K.$,{type:"button",variant:"outline",size:"sm",onClick:w,className:"mt-2",children:[(0,r.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Add Top-Level Column"]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(V.J,{children:"Rows"}),x.map((e,t)=>(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(B.p,{value:e.rowsName,onChange:e=>T(t,e.target.value),placeholder:"Row ".concat(t+1),className:"row-input ".concat(e.rowsName.trim()?"":"border-red-500")}),(0,r.jsx)(K.$,{type:"button",variant:"ghost",size:"icon",onClick:()=>S(t),disabled:!1,children:(0,r.jsx)(c.A,{className:"h-4 w-4"})})]},t)),(0,r.jsxs)(K.$,{type:"button",variant:"outline",size:"sm",onClick:E,className:"mt-2",children:[(0,r.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Add Row"]})]})]}),f&&(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(V.J,{children:"Table Preview"}),(0,r.jsx)("div",{className:"border rounded-md p-4 overflow-x-auto max-h-[300px] overflow-y-auto",children:(0,r.jsxs)(J.XI,{children:[(0,r.jsx)(J.A0,{children:(()=>{let e=Math.max(...m.map(e=>e.level),0),t=[];t.push((0,r.jsx)(J.Hj,{children:m.filter(e=>0===e.level).map(e=>{let t=e=>{let s=m.filter(t=>t.parentId===e),r=[...s];return s.forEach(e=>{r=[...r,...t(e.id)]}),r},s=t(e.id),l=s.filter(e=>!m.some(t=>t.parentId===e.id)),i=s.length>0&&l.length||1;return(0,r.jsx)(J.nd,{colSpan:i,className:"text-center border-b",children:e.columnName||"Column"},e.id)})},"header-row-0"));for(let s=1;s<=e;s++)t.push((0,r.jsx)(J.Hj,{children:m.filter(e=>e.level===s-1).map(e=>{let t=m.filter(t=>t.parentId===e.id);return 0===t.length?(0,r.jsx)(J.nd,{className:"text-center border-b"},"empty-".concat(e.id)):t.map(e=>{let t=e=>{let s=m.filter(t=>t.parentId===e),r=[...s];return s.forEach(e=>{r=[...r,...t(e.id)]}),r},s=t(e.id),l=s.filter(e=>!m.some(t=>t.parentId===e.id)),i=s.length>0&&l.length||1;return(0,r.jsx)(J.nd,{colSpan:i,className:"text-center border-b",children:e.columnName||"Child Column"},e.id)})})},"header-row-".concat(s)));return t})()}),(0,r.jsx)(J.BF,{children:x.length>0?x.map((e,t)=>(0,r.jsx)(J.Hj,{children:m.filter(e=>{let t=m.some(t=>t.parentId===e.id);return 0===e.level&&!t||e.level>0}).map(e=>(0,r.jsx)(J.nA,{className:"bg-gray-50",children:(0,r.jsx)("div",{className:"h-8 flex items-center justify-center text-gray-400 text-xs",children:"Input field"})},e.id))},t)):(0,r.jsx)(J.Hj,{children:m.filter(e=>{let t=m.some(t=>t.parentId===e.id);return 0===e.level&&!t||e.level>0}).map(e=>(0,r.jsx)(J.nA,{className:"bg-gray-50",children:(0,r.jsx)("div",{className:"h-8 flex items-center justify-center text-gray-400 text-xs",children:"Input field"})},e.id))})})]})}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-2",children:"This preview shows how the table will appear to users filling out the form."})]})]})]}):(0,r.jsxs)("form",{onSubmit:D,className:"space-y-4",children:[(0,r.jsxs)("div",{className:"bg-blue-50 p-3 rounded-md text-sm text-blue-800 border border-blue-200 mb-4",children:[(0,r.jsx)("p",{className:"font-medium mb-1",children:"Table Structure Guidelines:"}),(0,r.jsxs)("ul",{className:"list-disc pl-5 space-y-1",children:[(0,r.jsxs)("li",{children:["Create multiple"," ",(0,r.jsx)("span",{className:"font-medium",children:"parent columns"}),' using the "Add Top-Level Column" button']}),(0,r.jsxs)("li",{children:["Add up to 2 ",(0,r.jsx)("span",{className:"font-medium",children:"child columns"})," ",'under each parent using the "+" button']}),(0,r.jsx)("li",{children:"Child columns cannot have their own children (maximum 2 levels)"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(V.J,{htmlFor:"table-label",children:"Table Label"}),(0,r.jsx)(B.p,{id:"table-label",value:d,onChange:e=>u(e.target.value),placeholder:"Enter table question label",required:!0})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(V.J,{children:"Columns"}),m.map(e=>(0,r.jsxs)("div",{className:(0,H.cn)("flex items-center gap-2 p-2 rounded-md",0===e.level?"bg-gray-50":"bg-white border-l-2 border-gray-300"),style:{marginLeft:"".concat(20*e.level,"px")},children:[(0,r.jsxs)("div",{className:"flex-1 flex items-center gap-2",children:[e.level>0&&(0,r.jsx)("div",{className:"w-4 h-4 flex items-center justify-center",children:(0,r.jsx)("div",{className:"w-2 h-2 bg-blue-400 rounded-full"})}),(0,r.jsx)(B.p,{value:e.columnName,onChange:t=>A(e.id,t.target.value),placeholder:"".concat(0===e.level?"Parent":"Child"," Column"),className:(0,H.cn)("flex-1",0===e.level?"border-blue-200 bg-white":"border-dashed")}),0===e.level&&(0,r.jsx)("div",{className:"text-xs text-blue-500 font-medium",children:"Parent"}),e.level>0&&(0,r.jsx)("div",{className:"text-xs text-gray-500 font-medium",children:"Child"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(K.$,{type:"button",variant:"ghost",size:"icon",onClick:()=>q(e.id),title:e.level>0?"Child columns cannot have their own children":m.filter(t=>t.parentId===e.id).length>=2?"Maximum 2 child columns allowed":"Add child column",disabled:e.level>0||m.filter(t=>t.parentId===e.id).length>=2,children:(0,r.jsx)(g.A,{className:(0,H.cn)("h-4 w-4",(e.level>0||m.filter(t=>t.parentId===e.id).length>=2)&&"text-gray-300")})}),(0,r.jsx)(K.$,{type:"button",variant:"ghost",size:"icon",onClick:()=>Q(e.id),disabled:m.length<=1,title:"Remove column",children:(0,r.jsx)(c.A,{className:"h-4 w-4"})})]})]},e.id)),(0,r.jsxs)(K.$,{type:"button",variant:"outline",size:"sm",onClick:w,className:"mt-2",children:[(0,r.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Add Top-Level Column"]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(V.J,{children:"Rows"}),x.map((e,t)=>(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(B.p,{value:e.rowsName,onChange:e=>T(t,e.target.value),placeholder:"Row ".concat(t+1),className:"row-input ".concat(e.rowsName.trim()?"":"border-red-500")}),(0,r.jsx)(K.$,{type:"button",variant:"ghost",size:"icon",onClick:()=>S(t),disabled:!1,children:(0,r.jsx)(c.A,{className:"h-4 w-4"})})]},t)),(0,r.jsxs)(K.$,{type:"button",variant:"outline",size:"sm",onClick:E,className:"mt-2",children:[(0,r.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Add Row"]})]})]}),f&&(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(V.J,{children:"Table Preview"}),(0,r.jsx)("div",{className:"border rounded-md p-4 overflow-x-auto",children:(0,r.jsxs)(J.XI,{children:[(0,r.jsx)(J.A0,{children:(()=>{let e=Math.max(...m.map(e=>e.level),0),t=[];t.push((0,r.jsx)(J.Hj,{children:m.filter(e=>0===e.level).map(e=>{let t=e=>{let s=m.filter(t=>t.parentId===e),r=[...s];return s.forEach(e=>{r=[...r,...t(e.id)]}),r},s=t(e.id),l=s.filter(e=>!m.some(t=>t.parentId===e.id)),i=s.length>0&&l.length||1;return(0,r.jsx)(J.nd,{colSpan:i,className:"text-center border-b",children:e.columnName||"Column"},e.id)})},"header-row-0"));for(let s=1;s<=e;s++)t.push((0,r.jsx)(J.Hj,{children:m.filter(e=>e.level===s-1).map(e=>{let t=m.filter(t=>t.parentId===e.id);return 0===t.length?(0,r.jsx)(J.nd,{className:"text-center border-b"},"empty-".concat(e.id)):t.map(e=>{let t=e=>{let s=m.filter(t=>t.parentId===e),r=[...s];return s.forEach(e=>{r=[...r,...t(e.id)]}),r},s=t(e.id),l=s.filter(e=>!m.some(t=>t.parentId===e.id)),i=s.length>0&&l.length||1;return(0,r.jsx)(J.nd,{colSpan:i,className:"text-center border-b",children:e.columnName||"Child Column"},e.id)})})},"header-row-".concat(s)));return t})()}),(0,r.jsx)(J.BF,{children:x.length>0?x.map((e,t)=>(0,r.jsx)(J.Hj,{children:m.filter(e=>{let t=m.some(t=>t.parentId===e.id);return 0===e.level&&!t||e.level>0}).map(e=>(0,r.jsx)(J.nA,{className:"bg-gray-50",children:(0,r.jsx)("div",{className:"h-8 flex items-center justify-center text-gray-400 text-xs",children:"Input field"})},e.id))},t)):(0,r.jsx)(J.Hj,{children:m.filter(e=>{let t=m.some(t=>t.parentId===e.id);return 0===e.level&&!t||e.level>0}).map(e=>(0,r.jsx)(J.nA,{className:"bg-gray-50",children:(0,r.jsx)("div",{className:"h-8 flex items-center justify-center text-gray-400 text-xs",children:"Input field"})},e.id))})})]})}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-2",children:"This preview shows how the table will appear to users filling out the form."})]})]}),(0,r.jsx)("div",{className:"flex items-center justify-end space-x-4 mt-6",children:(0,r.jsx)(K.$,{type:"submit",disabled:b,className:"bg-primary-500 text-white hover:bg-primary-600",children:b?"Saving...":n||(null==a?void 0:a.id)?"Update":"Save"})})]})]})}var W=s(27859),X=s(3925);let Z=e=>new Promise(t=>{if(!["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","application/vnd.ms-excel"].includes(e.type))return void t({isValid:!1,error:"Please select a valid Excel file (.xlsx or .xls)"});if(e.size>5242880)return void t({isValid:!1,error:"File size must be less than 5MB"});let s=new FileReader;s.onload=e=>{try{var s,r,l;let i=new Uint8Array(null==(s=e.target)?void 0:s.result),n=X.LF(i,{type:"array"}),a=n.Sheets[n.SheetNames[0]],o=X.Wp.sheet_to_json(a,{header:1});if(o.length<2)return void t({isValid:!1,error:"Excel file is empty or has no valid data"});let d=o[0].map(e=>null==e?void 0:e.toString().trim());if(!(null==(r=d[0])?void 0:r.includes("label"))||!(null==(l=d[1])?void 0:l.includes("code")))return void t({isValid:!1,error:"Invalid Excel format: Missing required headers (Label, Code)"});let c=o.slice(1);if(0===c.length)return void t({isValid:!1,error:"Excel file contains no valid options"});for(let e=0;e<c.length;e++){let s=c[e];if(!s[0]||!s[1])return void t({isValid:!1,error:"Invalid data in row ".concat(e+2,": Label and Code are required")})}t({isValid:!0})}catch(e){t({isValid:!1,error:"Failed to parse Excel file"})}},s.onerror=()=>{t({isValid:!1,error:"Error reading Excel file"})},s.readAsArrayBuffer(e)}),Y=e=>{let{file:t,onRemove:s,error:l}=e;return(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 rounded-lg ".concat(l?"bg-red-50 border border-red-200":"bg-green-50 border border-green-200"),children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[l?(0,r.jsx)(W.wew,{className:"text-red-500"}):(0,r.jsx)(W.qGT,{className:"text-green-500"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-sm font-medium",children:t.name}),(0,r.jsxs)("div",{className:"text-xs text-gray-500",children:[(t.size/1024).toFixed(1)," KB"]}),l&&(0,r.jsx)("div",{className:"text-xs text-red-600",children:l})]})]}),(0,r.jsx)("button",{type:"button",onClick:s,className:"text-red-500 hover:text-red-700 p-1",title:"Remove file",children:(0,r.jsx)(W.id1,{})})]})},ee=()=>{let e=new Blob(["Label,Code,Next Question ID\nOption 1,opt1,\nOption 2,opt2,\nOption 3,opt3,"],{type:"text/csv"}),t=window.URL.createObjectURL(e),s=document.createElement("a");s.href=t,s.download="question_options_template.csv",document.body.appendChild(s),s.click(),window.URL.revokeObjectURL(t),document.body.removeChild(s)},et=e=>{let{isOpen:t,onConfirm:s,onCancel:l}=e;if(!t)return null;let i=(0,m.c3)();return(0,r.jsx)("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-md w-full",children:[(0,r.jsx)("h2",{className:"text-lg text-neutral-700 font-semibold mb-4",children:i("unsavedChanges")}),(0,r.jsx)("p",{className:"mb-6 text-neutral-700",children:i("unsavedChangesWarning")}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,r.jsx)("button",{onClick:l,className:"btn-outline",children:i("cancel")}),(0,r.jsx)("button",{onClick:s,className:"btn-danger",children:i("discardChanges")})]})]})})},es=e=>{let{showModal:t,setShowModal:s,contextType:i,contextId:n,position:a}=e,o=(0,q.mN)({resolver:(0,F.u)(M),defaultValues:{label:"",inputType:"",hint:"",placeholder:"",questionOptions:[]}}),d=(0,m.c3)(),{register:c,formState:{errors:u,isSubmitted:p,isDirty:x},setValue:h,handleSubmit:b,reset:g,watch:v}=o,[f,j]=(0,l.useState)(!1),[y,N]=(0,l.useState)(null),[T,G]=(0,l.useState)("form"),[R,O]=(0,l.useState)(""),[P,K]=(0,l.useState)(!1),[B,V]=(0,l.useState)(""),[U,$]=(0,l.useState)(!1),J=(0,l.useRef)(null),H=(0,Q.jE)(),X="project"===i?["questions",n]:"template"===i?["templateQuestions",n]:["questionBlockQuestions",n],es=(0,S.n)({mutationFn:A.Af,onSuccess:()=>{H.invalidateQueries({queryKey:X}),"project"===i&&H.invalidateQueries({queryKey:["formBuilderData",n]}),eo()},onError:e=>{O(e.message||"Failed to add question"),$(!1)}});(0,l.useEffect)(()=>{c("inputType",{required:d("selectInputType")})},[c]),(0,l.useEffect)(()=>{h("inputType",B,{shouldValidate:p})},[B,h,p]),(0,l.useEffect)(()=>{t&&$(!1)},[t]);let er=async e=>{var t;let s=null==(t=e.target.files)?void 0:t[0];if(!s)return;let r=await Z(s);if(!r.isValid){O(r.error||"Invalid file"),N(null);return}O(""),N(s)},el=()=>{N(null),O(""),J.current&&(J.current.value="")},ei=e=>{G(e),"form"===e&&el()},en=()=>{let e=v("questionOptions");return x||!!v("label")||!!v("hint")||!!v("placeholder")||!!B||!!y||e&&Array.isArray(e)&&e.length>0},ea=()=>{en()?j(!0):eo()},eo=()=>{g(),V(""),N(null),G("form"),O(""),$(!1),j(!1),s(!1)},ed=async e=>{if(U)return;if("table"===B){let e=document.querySelector(".table-question-builder");e?e.dispatchEvent(new CustomEvent("submitTable")):console.error("TableQuestionBuilder not found");return}if(z(B)){if("excel"===T){if(!y)return void O("Please select an Excel file")}else if(0===(e.questionOptions||[]).length)return void o.setError("questionOptions",{type:"custom",message:d("atLeastOneOptionRequired")})}$(!0);let t=null!=y?y:void 0,s={label:e.label,isRequired:P,hint:e.hint,placeholder:e.placeholder,inputType:B,questionOptions:"form"===T?e.questionOptions:void 0,file:"excel"===T?t:void 0};es.mutate({contextType:i,contextId:n,dataToSend:s,position:a})},ec=z(B);return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(w.A,{isOpen:t,onClose:ea,className:"w-11/12 tablet:w-4/5 desktop:w-4/5 bg-neutral-100 rounded-lg p-6",children:[(0,r.jsx)("h1",{className:"heading-text capitalize mb-4",children:d("addQuestion")}),es.isPending&&(0,r.jsx)(L,{}),(0,r.jsx)(q.Op,{...o,children:(0,r.jsxs)("form",{className:"space-y-4 max-h-[500px] overflow-y-auto p-4",onSubmit:b(ed),children:[(0,r.jsxs)("div",{className:"label-input-group group ",children:[(0,r.jsx)("input",{...c("label",{required:d("questionNameRequired")}),className:"input-field",placeholder:d("enterQuestion")}),u.label&&(0,r.jsx)("p",{className:"text-sm text-red-500",children:"".concat(u.label.message)})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsx)("label",{htmlFor:"question-type",className:"label-text",children:d("inputType")}),(0,r.jsx)("div",{className:"mt-1",children:(0,r.jsx)(C.l,{id:"question-type",options:Object.values(I),value:B&&I[B]?I[B]:d("selectOption"),onChange:e=>{let t=(0,k.H)(e,I);V(null!=t?t:""),G("form"),el()}})}),u.inputType&&(0,r.jsx)("p",{className:"text-sm text-red-500",children:"".concat(u.inputType.message)})]}),(0,r.jsx)("div",{className:"flex items-end",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(E.dO,{id:"required",checked:P,onCheckedChange:()=>K(e=>!e),className:"data-[state=checked]:bg-primary-500 data-[state=unchecked]:bg-neutral-100 border border-primary-500"}),(0,r.jsx)("label",{htmlFor:"required",className:"label-text",children:d("required")})]})})]}),(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsx)("label",{htmlFor:"hint",className:"label-text",children:d("helpText")}),(0,r.jsx)("textarea",{...c("hint"),id:"hint",placeholder:d("helpTextHint"),className:"input-field resize-none"})]}),(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsx)("label",{htmlFor:"placeholder",className:"label-text",children:d("placeholderText")}),(0,r.jsx)("input",{...c("placeholder"),id:"placeholder",placeholder:d("placeholderHint"),className:"input-field"})]}),ec&&(0,r.jsx)("div",{className:"space-y-4",children:(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsx)("label",{className:"label-text",children:d("questionOptions")}),(0,r.jsxs)("div",{className:"flex space-x-4 mb-4",children:[(0,r.jsxs)("label",{className:"flex items-center space-x-2 cursor-pointer",children:[(0,r.jsx)("input",{type:"radio",value:"form",checked:"form"===T,onChange:e=>ei(e.target.value),className:"text-primary-500"}),(0,r.jsx)("span",{children:d("manualEntry")})]}),(0,r.jsxs)("label",{className:"flex items-center space-x-2 cursor-pointer",children:[(0,r.jsx)("input",{type:"radio",value:"excel",checked:"excel"===T,onChange:e=>ei(e.target.value),className:"text-primary-500"}),(0,r.jsx)("span",{children:d("excelUpload")})]})]}),"excel"===T&&(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-4 hover:border-gray-400 transition-colors",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(W.tAF,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,r.jsxs)("div",{className:"mt-2",children:[(0,r.jsxs)("label",{htmlFor:"excel-file",className:"cursor-pointer",children:[(0,r.jsx)("span",{className:"mt-2 block text-sm font-medium text-gray-900",children:d("uploadExcel")}),(0,r.jsxs)("span",{className:"mt-1 block text-xs text-gray-500",children:[d("supportedFormats"),": .xlsx, .xls (max 5MB)"]})]}),(0,r.jsx)("input",{ref:J,id:"excel-file",type:"file",accept:".xlsx,.xls",onChange:er,className:"sr-only"})]}),(0,r.jsxs)("div",{className:"mt-3 flex justify-center space-x-2",children:[(0,r.jsxs)("button",{type:"button",onClick:()=>{var e;return null==(e=J.current)?void 0:e.click()},className:"btn-outline inline-flex items-center",children:[(0,r.jsx)(W.bh6,{className:"mr-2"}),d("chooseExcel")]}),(0,r.jsxs)("button",{type:"button",onClick:ee,className:"btn-outline inline-flex items-center",title:d("downloadTemplate"),children:[(0,r.jsx)(W.Ah9,{className:"mr-2"}),d("downloadTemplate")]})]})]})}),y&&(0,r.jsx)(Y,{file:y,onRemove:el,error:R}),R&&!y&&(0,r.jsx)("div",{className:"text-sm text-red-600 bg-red-50 p-2 rounded",children:R}),(0,r.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-3",children:[(0,r.jsx)("p",{className:"text-sm text-blue-800 font-medium mb-2",children:d("excelFormatRequirements")}),(0,r.jsxs)("ul",{className:"text-xs text-blue-700 space-y-1",children:[(0,r.jsxs)("li",{children:["• ",d("columnA"),": ",(0,r.jsx)("strong",{children:d("label")})," (",d("required"),") -",d("optionDisplayText")]}),(0,r.jsxs)("li",{children:["• ",d("columnB"),": ",(0,r.jsx)("strong",{children:d("code")})," (",d("required"),") -",d("uniqueIdentifiers")]}),(0,r.jsxs)("li",{children:["• ",d("ColumnC"),": ",(0,r.jsx)("strong",{children:d("nextQuestionId")})," ","(",d("optional"),") - ",d("forConditionalLogic")]}),(0,r.jsxs)("li",{children:["• ",d("firstRowHeaders")]}),(0,r.jsxs)("li",{children:["• ",d("eachRowOption")]})]})]})]}),"form"===T&&(0,r.jsx)(D,{contextType:i,contextId:n,inputType:B})]})}),"table"===B&&(0,r.jsx)("div",{className:"mt-4",children:(0,r.jsx)(_,{projectId:n,isInModal:!0,onTableCreated:e=>{-1!==e&&(H.invalidateQueries({queryKey:X}),"project"===i&&H.invalidateQueries({queryKey:["formBuilderData",n]})),eo()}})}),(0,r.jsxs)("div",{className:"flex items-center justify-end space-x-4 pt-4",children:[(0,r.jsx)("button",{type:"button",onClick:ea,className:"btn-outline",disabled:U,children:d("cancel")}),(0,r.jsx)("button",{type:"submit",className:"btn-primary flex items-center justify-center gap-2",onClick:e=>{if("table"===B){e.preventDefault();let t=document.querySelector(".table-question-builder");t&&t.dispatchEvent(new CustomEvent("submitTable"))}},disabled:U||"excel"===T&&(!y||!!R),children:U?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("span",{className:"animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full"}),d("saving"),"..."]}):d("save")})]})]})})]}),(0,r.jsx)(et,{isOpen:f,onConfirm:eo,onCancel:()=>j(!1)})]})};var er=s(71402),el=s(34540);let ei=e=>{let{showModal:t,setShowModal:s,contextType:i,question:n,contextId:a}=e,o=(0,q.mN)({}),d=(0,m.c3)(),{register:c,formState:{errors:u,isSubmitted:p},setValue:x,handleSubmit:h,reset:b}=o,g=(e,t)=>{var s;return null!=(s=n[e])?s:t},[v,f]=(0,l.useState)(!1),[j,y]=(0,l.useState)("");(0,l.useEffect)(()=>{let e=M.safeParse(n);if(e.success)b(e.data),y(e.data.inputType||""),e.data.questionOptions&&e.data.questionOptions.length>0&&x("questionOptions",e.data.questionOptions);else{console.warn("Schema parsing failed, using raw question data:",e.error),x("label",g("label","")),x("hint",g("hint","")),x("placeholder",g("placeholder","")),x("inputType",g("inputType","")),y(g("inputType",""));let t=g("questionOptions",[]);Array.isArray(t)&&t.length>0&&x("questionOptions",t)}f(g("isRequired",!1))},[n,b,x]),(0,l.useEffect)(()=>{c("inputType",{required:"Please select an input type"})},[c]),(0,l.useEffect)(()=>{x("inputType",j,{shouldValidate:p})},[j,x,p]);let N=(0,Q.jE)(),C=(0,el.wA)(),k="project"===i?["questions"]:"template"===i?["templateQuestions"]:["questionBlockQuestions"],T=()=>{f(!1),y(""),s(!1)},G=(0,S.n)({mutationFn:A.sr,onSuccess:()=>{N.invalidateQueries({queryKey:k,exact:!1}),"project"===i&&N.invalidateQueries({queryKey:["formBuilderData",a]}),C((0,er.Ds)({message:d("questionsUpdated"),type:"success"})),T()},onError:()=>{C((0,er.Ds)({message:d("questionUpdateFailed"),type:"error"}))}}),F=async e=>{if("table"===j||"inputType"in n&&"table"===n.inputType){let e=document.querySelector(".table-question-builder");if(e)return void e.dispatchEvent(new CustomEvent("submitTable"))}let t={label:e.label,isRequired:v,hint:e.hint,placeholder:e.placeholder,inputType:j||("inputType"in n?n.inputType:""),questionOptions:e.questionOptions,..."position"in n&&{position:n.position}};G.mutate({id:n.id,contextType:i,dataToSend:t,contextId:a})};return(0,r.jsxs)(w.A,{isOpen:t,onClose:T,className:"w-11/12 tablet:w-4/5 desktop:w-3/5",children:[(0,r.jsx)("h1",{className:"heading-text capitalize mb-4",children:d("editQuestion")}),G.isPending&&(0,r.jsx)(L,{}),(0,r.jsx)(q.Op,{...o,children:(0,r.jsxs)("form",{className:"space-y-4 max-h-[500px] overflow-y-auto p-4",onSubmit:h(F),children:[(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsx)("input",{...c("label",{required:d("questionNameRequired")}),className:"input-field",placeholder:d("enterQuestion")}),u.label&&(0,r.jsx)("p",{className:"text-sm text-red-500",children:"".concat(u.label.message)})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsx)("label",{htmlFor:"question-type",className:"label-text",children:d("inputType")}),(0,r.jsx)("input",{id:"question-type",className:"input-field bg-gray-100 ",value:j&&I[j]?I[j]:"N/A",disabled:!0})]}),(0,r.jsx)("div",{className:"flex items-end",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(E.dO,{id:"required",checked:v,onCheckedChange:()=>f(e=>!e),className:"data-[state=checked]:bg-primary-500 data-[state=unchecked]:bg-neutral-100 border border-primary-500"}),(0,r.jsx)("label",{htmlFor:"required",className:"label-text",children:d("required")})]})}),u.inputType&&(0,r.jsx)("p",{className:"text-sm text-red-500",children:"".concat(u.inputType.message)})]}),(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsx)("label",{htmlFor:"hint",className:"label-text",children:d("helpText")}),(0,r.jsx)("textarea",{...c("hint"),id:"hint",placeholder:d("helpTextHint"),className:"input-field resize-none"})]}),(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsx)("label",{htmlFor:"placeholder",className:"label-text",children:d("placeholderText")}),(0,r.jsx)("input",{...c("placeholder"),id:"placeholder",placeholder:d("placeholderHint"),className:"input-field"})]}),z(j)&&(0,r.jsx)(D,{contextType:i,contextId:a,currentQuestionId:n.id,inputType:j},"".concat(n.id,"-").concat(j)),(0,r.jsxs)("div",{className:"flex items-center justify-end space-x-4",children:[(0,r.jsx)("button",{type:"button",onClick:T,className:"btn-outline",children:d("cancel")}),(0,r.jsx)("button",{onClick:h(F),className:"btn-primary",children:d("saveEdit")})]})]})})]})},en=e=>{let{showModal:t,setShowModal:s,contextType:i,question:n,contextId:a}=e,o=(0,Q.jE)(),d=(0,el.wA)(),c=(0,m.c3)(),{data:u,isLoading:p,error:x}=(0,T.I)({queryKey:["tableQuestion",n.id],queryFn:async()=>{try{return await (0,$.q7)(n.id)}catch(e){throw console.error("Error fetching table data:",e),e}},enabled:t&&n.id>0&&"table"===n.inputType}),h=l.useMemo(()=>u?{id:u.id,label:u.label,tableColumns:u.tableColumns.map(e=>{var t;return{id:e.id,columnName:e.columnName,parentColumnId:e.parentColumnId,childColumns:(null==(t=e.childColumns)?void 0:t.map(t=>({id:t.id,columnName:t.columnName,parentColumnId:t.parentColumnId||e.id})))||[]}}),tableRows:u.tableRows.map(e=>({id:e.id,rowsName:e.rowsName}))}:null,[u]);(0,l.useEffect)(()=>{},[t,n]);let b="project"===i?["questions"]:"template"===i?["templateQuestions"]:["questionBlockQuestions"];x&&(console.error("Error fetching table data:",x),d((0,er.Ds)({message:c("tableDataLoadFailed"),type:"error"})));let g=l.useRef(null);return(0,r.jsxs)(w.A,{isOpen:t,onClose:()=>{window.confirm(c("unsavedChangesCloseConfirm"))&&s(!1)},className:"w-11/12 tablet:w-4/5 desktop:w-3/5",preventOutsideClick:!0,children:[p&&(0,r.jsx)(L,{}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("h1",{className:"heading-text capitalize mb-4",children:c("editTableQuestion")}),h?(0,r.jsxs)("div",{ref:g,className:"table-question-builder-container",children:[(0,r.jsx)(_,{projectId:a,isInModal:!0,isEditMode:!0,existingTableData:h,onTableCreated:e=>{o.invalidateQueries({queryKey:b,exact:!1}),o.invalidateQueries({queryKey:["tableQuestion",n.id],exact:!0}),"project"===i&&o.invalidateQueries({queryKey:["formBuilderData",a]}),d((0,er.Ds)({message:c("tableQuestionUpdated"),type:"success"})),setTimeout(()=>{s(!1)},100)}}),(0,r.jsxs)("div",{className:"flex items-center justify-end space-x-4 mt-6",children:[(0,r.jsx)("button",{type:"button",onClick:()=>s(!1),className:"btn-outline",children:c("cancel")}),(0,r.jsx)("button",{type:"button",onClick:()=>{let e=null;if(g.current&&(e=g.current),!e){let t=document.querySelectorAll(".table-question-builder");t.length>0&&(e=t[0])}if(!e&&g.current){let t=g.current.querySelector(".table-question-builder");t&&(e=t)}if(e){let t=new CustomEvent("submitTable",{bubbles:!0,cancelable:!0,detail:{timestamp:Date.now()}});e.dispatchEvent(t)}else{console.error("Could not find any table builder element to dispatch event to");let e=document.querySelector("[class*='table']");if(e){let t=new CustomEvent("submitTable",{bubbles:!0,cancelable:!0,detail:{timestamp:Date.now(),isLastResort:!0}});e.dispatchEvent(t)}}},className:"btn-primary",children:c("saveChanges")})]})]}):p?null:(0,r.jsx)("div",{className:"p-4 text-center",children:(0,r.jsx)("p",{className:"text-red-500",children:c("tableDataLoadErrorRetry")})})]})]})};var ea=s(63642),eo=s(54416),ed=s(10150);let ec=e=>{let{showModal:t,setShowModal:s,contextType:i,contextId:n,existingGroup:a,questions:o,questionGroups:d=[]}=e,[c,u]=(0,l.useState)(""),[p,x]=(0,l.useState)([]),h=(0,el.wA)(),b=(0,Q.jE)(),g=(0,m.c3)();(0,l.useEffect)(()=>{t&&(a?(u(a.title),x(o.filter(e=>e.questionGroupId===a.id).map(e=>e.id))):(u(""),x([])))},[t,a,o]);let v=(0,S.n)({mutationFn:ed.IF,onSuccess:e=>{b.invalidateQueries({queryKey:["questionGroups",n]}),b.invalidateQueries({queryKey:["questions",n]}),"project"===i&&b.invalidateQueries({queryKey:["formBuilderData",n]}),h((0,er.Ds)({message:g("questionGroupCreated"),type:"success"})),s(!1)},onError:e=>{console.error("Error creating question group:",e),h((0,er.Ds)({message:g("questionGroupCreationFailed"),type:"error"}))}}),f=(0,S.n)({mutationFn:ed.lr,onSuccess:e=>{b.invalidateQueries({queryKey:["questionGroups",n]}),b.invalidateQueries({queryKey:["questions",n]}),"project"===i&&b.invalidateQueries({queryKey:["formBuilderData",n]}),h((0,er.Ds)({message:g("questionGroupUpdated"),type:"success"})),s(!1)},onError:e=>{console.error("Error updating question group:",e),h((0,er.Ds)({message:g("questionGroupUpdateFailed"),type:"error"}))}});return t?(0,r.jsx)("div",{className:"fixed inset-0 bg-neutral-900/50 flex items-center justify-center z-40",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-lg w-full max-w-2xl max-h-[90vh] overflow-auto p-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center p-4 ",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold",children:a?g("editQuestionGroup"):g("createQuestionGroup")}),(0,r.jsx)("button",{onClick:()=>s(!1),className:"text-gray-500 hover:text-gray-700 cursor-pointer",children:(0,r.jsx)(eo.A,{size:20})})]}),(0,r.jsxs)("form",{onSubmit:e=>{if(e.preventDefault(),!c.trim())return void h((0,er.Ds)({message:g("groupTitleRequired"),type:"error"}));a?f.mutate({id:a.id,title:c,order:a.order,selectedQuestionIds:p}):v.mutate({title:c,order:d.length+1,projectId:n,selectedQuestionIds:p})},className:"p-4",children:[(0,r.jsxs)("div",{className:"group label-input-group ",children:[(0,r.jsx)("label",{htmlFor:"title",children:g("groupTitle")}),(0,r.jsx)("input",{type:"text",id:"title",value:c,onChange:e=>u(e.target.value),className:" input-field w-full",placeholder:g("enterGroupTitle"),required:!0})]}),(0,r.jsxs)("div",{className:"mt-8 label-input-group",children:[(0,r.jsx)("label",{children:g("selectQuestionsForGroup")}),(0,r.jsx)("div",{className:"border border-neutral-300 rounded-md p-2 max-h-60 overflow-y-auto",children:o.length>0?o.map(e=>{let t=e.questionGroupId?d.find(t=>t.id===e.questionGroupId):null;return(0,r.jsxs)("div",{className:"flex gap-2 items-center mb-3 p-2 border-b border-neutral-300",children:[(0,r.jsx)("input",{type:"checkbox",id:"question-".concat(e.id),checked:p.includes(e.id),onChange:t=>{t.target.checked?x([...p,e.id]):x(p.filter(t=>t!==e.id))},className:"mr-2 cursor-pointer w-5 h-5"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"question-".concat(e.id),className:"text-sm",children:e.label}),t&&t.id!==((null==a?void 0:a.id)||-1)&&(0,r.jsxs)("div",{className:"text-xs text-neutral-700 mt-1",children:[g("currentlyInGroup"),": ",(0,r.jsx)("span",{className:"font-medium text-amber-600",children:t.title}),(0,r.jsxs)("span",{className:"ml-1 text-neutral-700",children:["(",g("willBeMovedToThisGroup"),")"]})]})]})]},e.id)}):(0,r.jsx)("p",{className:"text-gray-500 text-sm p-2",children:g("noAvailableQuestions")})})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-2 mt-4",children:[(0,r.jsx)("button",{type:"button",onClick:()=>s(!1),className:"btn-outline",children:g("cancel")}),(0,r.jsx)("button",{type:"submit",className:"px-4 py-2 btn-primary",disabled:v.isPending||f.isPending,children:v.isPending||f.isPending?g("saving"):a?g("updateGroup"):g("createGroup")})]})]})]})}):null},eu=e=>{let{showModal:t,setShowModal:s,onConfirmDelete:l,onConfirmDeleteWithQuestions:i,isDeleting:n}=e,a=(0,m.c3)();return t?(0,r.jsx)("div",{className:"fixed inset-0 bg-neutral-900/50 flex items-center justify-center z-40",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-lg w-full max-w-md",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center p-4 border-b",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold",children:a("deleteQuestionGroup")}),(0,r.jsx)("button",{onClick:()=>s(!1),className:"text-gray-500 hover:text-gray-700",disabled:n,children:(0,r.jsx)(eo.A,{size:20})})]}),(0,r.jsxs)("div",{className:"p-4",children:[(0,r.jsx)("p",{className:"mb-4",children:a("deleteQuestionGroupPrompt")}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("button",{onClick:l,className:"w-full px-4 py-2 bg-amber-500 text-white rounded-md hover:bg-amber-600",disabled:n,children:a(n?"deleting":"deleteGroupOnly")}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:a("deleteGroupKeepQuestions")}),(0,r.jsx)("button",{onClick:i,className:"w-full px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600",disabled:n,children:a(n?"deleting":"deleteGroupQuestions")}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:a("deleteGroupAndQuestions")}),(0,r.jsx)("button",{onClick:()=>s(!1),className:"w-full px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",disabled:n,children:a("cancel")})]})]})]})}):null};var em=s(47924);let ep=e=>{let{isOpen:t,onClose:s,onAddQuestions:i}=e,[n,a]=(0,l.useState)(""),[o,d]=(0,l.useState)([]),[c,u]=(0,l.useState)(!0),p=(0,m.c3)(),{data:x,isLoading:h,isError:b}=(0,T.I)({queryKey:["libraryQuestions"],queryFn:()=>(0,A.dI)(),enabled:t}),g=x?x.filter(e=>e.label.toLowerCase().includes(n.toLowerCase())):[],v=e=>{o.some(t=>t.id===e.id)?d(o.filter(t=>t.id!==e.id)):d([...o,e])};return((0,l.useEffect)(()=>{t||(d([]),a(""))},[t]),t)?(0,r.jsxs)("div",{className:"fixed inset-0 z-50 flex justify-end",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-neutral-900/50",onClick:s}),(0,r.jsxs)("div",{className:"relative w-full max-w-md bg-neutral-50 h-full overflow-auto shadow-xl",children:[(0,r.jsxs)("div",{className:"p-4 border-b border-neutral-200",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,r.jsx)("h2",{className:"text-xl font-bold",children:p("searchLibrary")}),(0,r.jsx)("button",{onClick:s,className:"self-end cursor-pointer text-neutral-700 hover:text-neutral-900 active:scale-90 transition-all duration-300",children:(0,r.jsx)(eo.A,{size:20})})]}),(0,r.jsxs)("div",{className:"relative mb-4",children:[(0,r.jsx)("input",{type:"text",placeholder:p("searchPlaceholder"),className:"input-field w-full p-2 pl-10",value:n,onChange:e=>a(e.target.value)}),(0,r.jsx)(em.A,{className:"absolute left-3 top-2.5 ",size:18})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,r.jsxs)("span",{children:[g.length," ",p("asset"),1!==g.length?p("s"):""," ",p("found")]}),(0,r.jsxs)("label",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",checked:c,onChange:()=>u(!c),className:"mr-2"}),p("expandDetails")]})]})]}),(0,r.jsx)("div",{className:"p-4",children:h?(0,r.jsx)("div",{className:"flex justify-center p-8",children:(0,r.jsx)(P.A,{})}):b?(0,r.jsx)("div",{className:"text-red-500 p-4 text-center",children:p("libraryLoadError")}):0===g.length?(0,r.jsx)("div",{className:"text-neutral-700 p-4 text-center",children:p("noQuestionsFound")}):(0,r.jsx)("div",{className:"space-y-2",children:g.map(e=>(0,r.jsx)("div",{className:"border border-neutral-500 rounded-md p-3",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",checked:o.some(t=>t.id===e.id),onChange:()=>v(e),className:"mr-3 h-5 w-5 cursor-pointer"}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:"font-medium",children:e.label}),c&&(0,r.jsxs)("div",{className:"text-sm text-neutral-700 mt-1",children:[p("type"),": ",String(e.inputType),e.hint&&(0,r.jsxs)("div",{children:[p("hint"),": ",e.hint]})]})]})]})},e.id))})}),(0,r.jsx)("div",{className:"border-t border-gray-200 p-4 sticky bottom-0 bg-neutral-50",children:(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("button",{onClick:s,className:"btn-outline",children:p("cancel")}),(0,r.jsxs)("button",{onClick:()=>{i(o),d([]),s()},disabled:0===o.length,className:"px-4 py-2 rounded-md ".concat(o.length>0?"btn-primary":"bg-gray-200 text-gray-500 pointer-events-none"),children:[p("addSelected")," (",o.length,")"]})]})})]})]}):null},ex=e=>{let{setIsPreviewMode:t,contextType:s,contextId:a,permissions:o}=e,d=(0,i.FR)((0,i.MS)(i.AN,{activationConstraint:{distance:8}}),(0,i.MS)(i.uN)),c=(0,m.c3)(),u=o.manageProject||o.editForm,[x,h]=(0,l.useState)(!1),[g,v]=(0,l.useState)(!1),[w,q]=(0,l.useState)(!1),[C,k]=(0,l.useState)(!1),[I,E]=(0,l.useState)(!1),[G,D]=(0,l.useState)(null),[F,z]=(0,l.useState)(!1),[R,O]=(0,l.useState)(!1),[M,P]=(0,l.useState)(!1),[K,B]=(0,l.useState)(!1),[V,U]=(0,l.useState)(null),[$,J]=(0,l.useState)([]),[H,_]=(0,l.useState)(null),[W,X]=(0,l.useState)(""),[Z,Y]=(0,l.useState)(!1),[ee,et]=(0,l.useState)(!1),[eo,em]=(0,l.useState)(null),[ex,eh]=(0,l.useState)([]),[eb,eg]=(0,l.useState)(""),[ev,ef]=(0,l.useState)(!1),ej=(0,el.wA)(),ey=(0,Q.jE)(),eN="project"===s?["questions",a]:"template"===s?["templateQuestions",a]:["questionBlockQuestions",a],ew=["questionGroups",a],eq=["formBuilderData",a],{data:eC,isLoading:ek,isError:eI}=(0,T.I)({queryKey:eq,queryFn:()=>(0,A.gf)({projectId:a}),enabled:"project"===s}),{data:eE=[],isLoading:eQ,isError:eS}=(0,T.I)({queryKey:eN,queryFn:()=>"template"===s?(0,A.ej)({templateId:a}):"questionBlock"===s?(0,A.dI)():[],enabled:"project"!==s}),eA=l.useMemo(()=>{if("project"!==s)return eE;{if(!(null==eC?void 0:eC.items))return[];let e=[];return eC.items.forEach(t=>{"question"===t.type?e.push(t):"group"===t.type&&t.questions&&t.questions.forEach(t=>{e.push(t)})}),e}},[eC,s,eE]),eT=l.useMemo(()=>"project"===s&&(null==eC?void 0:eC.items)?eC.items.filter(e=>"group"===e.type).map(e=>({...e,question:e.questions||[]})):[],[eC,s]),{data:eG=[]}=(0,T.I)({queryKey:ew,queryFn:()=>(0,ed.pr)({projectId:a}),enabled:"project"===s&&!eC});eT.reduce((e,t)=>(e[t.id]=eA.filter(e=>e.questionGroupId===t.id).sort((e,t)=>e.position-t.position),e),{});let eD=eA.filter(e=>!e.questionGroupId),eF=e=>{let t=new Map;e.forEach(e=>{let s=eA.filter(t=>t.questionGroupId===e.id).sort((e,t)=>e.position-t.position);t.set(e.id,{...e,subGroups:[],question:s})});let s=[];return e.forEach(e=>{let r=t.get(e.id);if(e.parentGroupId){let s=t.get(e.parentGroupId);s&&(s.subGroups=s.subGroups||[],s.subGroups.push(r))}else s.push(r)}),s},ez=l.useMemo(()=>eF(eT),[eT,eA]),eR=()=>{let e=[];return"project"===s&&ez.forEach(t=>{let s=eA.filter(e=>e.questionGroupId===t.id).sort((e,t)=>e.position-t.position),r=s.length>0?Math.min(...s.map(e=>e.position)):t.order;e.push({type:"group",data:t,order:r,originalPosition:r})}),("project"===s?eD:eA).forEach(t=>{e.push({type:"question",data:t,order:t.position,originalPosition:t.position})}),e.sort((e,t)=>e.order===t.order?(e.originalPosition||e.order)-(t.originalPosition||t.order):e.order-t.order)},eO=l.useMemo(()=>eR(),[ez,eD,s]);l.useEffect(()=>{let e=eA.some(e=>null!==e.questionGroupId&&void 0!==e.questionGroupId),t=0===eT.length;e&&t&&"project"===s&&ey.invalidateQueries({queryKey:ew})},[eA,eT,s,ey,ew]);let eM=(0,S.n)({mutationFn:A.ul,onSuccess:()=>{ey.invalidateQueries({queryKey:eN}),"project"===s&&ey.invalidateQueries({queryKey:eq}),ej((0,er.Ds)({message:c("questionDeleted"),type:"success"}))},onError:()=>{ej((0,er.Ds)({message:c("questionDeleteFailed"),type:"error"}))},onSettled:()=>{z(!1)}}),eP=(0,S.n)({mutationFn:A.ku,onSuccess:()=>{ey.invalidateQueries({queryKey:eN}),"project"===s&&ey.invalidateQueries({queryKey:eq}),ej((0,er.Ds)({message:c("questionDuplicated"),type:"success"}))},onError:()=>{ej((0,er.Ds)({message:c("questionDuplicateFailed"),type:"error"}))},onSettled:()=>{z(!1)}}),eL=(0,S.n)({mutationFn:ed.BU,onSuccess:(e,t)=>{let r=eT.find(e=>e.id===t.id);if(r&&eA.filter(e=>e.questionGroupId===r.id).length>0){let e=eA.map(e=>e.questionGroupId===r.id?{...e,questionGroupId:void 0}:e);ey.setQueryData(eN,e);let s=eT.filter(e=>e.id!==t.id);ey.setQueryData(ew,s)}ey.invalidateQueries({queryKey:ew}),ey.invalidateQueries({queryKey:eN}),"project"===s&&ey.invalidateQueries({queryKey:eq}),ej((0,er.Ds)({message:c("groupDeleted"),type:"success"})),B(!1),ef(!1)},onError:e=>{console.error(c("groupDeleteError"),e),ej((0,er.Ds)({message:c("groupDeleteFailed"),type:"error"})),ef(!1)}}),eK=(0,S.n)({mutationFn:ed.yb,onSuccess:(e,t)=>{let r=eT.find(e=>e.id===t.id);if(r){let e=eT.filter(e=>e.id!==t.id);ey.setQueryData(ew,e);let s=eA.filter(e=>e.questionGroupId!==r.id);ey.setQueryData(eN,s)}ey.invalidateQueries({queryKey:ew}),ey.invalidateQueries({queryKey:eN}),"project"===s&&ey.invalidateQueries({queryKey:eq}),ej((0,er.Ds)({message:c("groupAndQuestionsDeleted"),type:"success"})),B(!1),ef(!1)},onError:e=>{console.error(c("groupAndQuestionsDeleteError"),e),ej((0,er.Ds)({message:c("groupAndQuestionsDeleteFailed"),type:"error"})),ef(!1)}}),eB=(0,S.n)({mutationFn:A.ae,onMutate:async e=>{await ey.cancelQueries({queryKey:eN});let t=ey.getQueryData(eN);if(t&&e.questionPositions){let s=t.map(t=>{let s=e.questionPositions.find(e=>e.id===t.id);return s?{...t,position:s.position}:t});ey.setQueryData(eN,s)}return{previousQuestions:t}},onSuccess:()=>{ey.invalidateQueries({queryKey:eN}),"project"===s&&ey.invalidateQueries({queryKey:eq}),ej((0,er.Ds)({message:c("questionOrderUpdated"),type:"success"}))},onError:(e,t,s)=>{var r,l,i;(null==s?void 0:s.previousQuestions)&&ey.setQueryData(eN,s.previousQuestions),console.error("Failed to update question positions:",e),console.error("Error response:",null==(r=e.response)?void 0:r.data),ej((0,er.Ds)({message:"".concat(c("questionOrderUpdateFailed"),": ").concat((null==(i=e.response)||null==(l=i.data)?void 0:l.message)||e.message||c("tryAgain")),type:"error"}))},onSettled:()=>{ey.invalidateQueries({queryKey:eN})}}),eV=(0,S.n)({mutationFn:ed._U,onSuccess:()=>{ey.invalidateQueries({queryKey:eN}),ey.invalidateQueries({queryKey:ew}),"project"===s&&ey.invalidateQueries({queryKey:eq}),ej((0,er.Ds)({message:"Question moved successfully",type:"success"}))},onError:e=>{var t,s;console.error("Failed to move question between groups:",e),ej((0,er.Ds)({message:"Failed to move question: ".concat((null==(s=e.response)||null==(t=s.data)?void 0:t.message)||e.message||"Please try again"),type:"error"}))}}),eU=(0,S.n)({mutationFn:ed.kO,onSuccess:()=>{ey.invalidateQueries({queryKey:ew}),"project"===s&&ey.invalidateQueries({queryKey:eq}),ej((0,er.Ds)({message:"Group moved successfully",type:"success"}))},onError:e=>{var t,s;console.error("Failed to move group:",e),ej((0,er.Ds)({message:"Failed to move group: ".concat((null==(s=e.response)||null==(t=s.data)?void 0:t.message)||e.message||"Please try again"),type:"error"}))}});(0,S.n)({mutationFn:ed.Vq,onSuccess:()=>{ey.invalidateQueries({queryKey:ew}),ej((0,er.Ds)({message:"Group order updated successfully",type:"success"}))},onError:e=>{var t,s;console.error("Failed to update group positions:",e),ej((0,er.Ds)({message:"Failed to update group order: ".concat((null==(s=e.response)||null==(t=s.data)?void 0:t.message)||e.message||"Please try again"),type:"error"}))},onSettled:()=>{ey.invalidateQueries({queryKey:eN})}});let e$=(e,t,s)=>{t!==s&&eV.mutate({questionId:e,groupId:t||0,newGroupId:s||0})},eJ=e=>{let t=eA.map(t=>{let s=e.find(e=>e.id===t.id);return s?{...t,position:s.position}:t});ey.setQueryData(eN,t),eB.mutate({contextType:s,contextId:a,questionPositions:e})},eH=(e,t)=>{t&&eU.mutate({childGroupId:e,parentGroupId:t})},e_=(e,t)=>{em(e),eh(t),et(!0)},eW=()=>{if(!eb.trim()||!eo||0===ex.length)return;let e=eT.length>0?Math.max(...eT.map(e=>e.order)):0;e5.mutate({title:eb.trim(),order:e+1,projectId:a,selectedQuestionIds:ex,parentGroupId:eo}),et(!1),eg(""),em(null),eh([]),J(e=>e.filter(e=>!ex.includes(e)))},eX=()=>{et(!1),eg(""),em(null),eh([])},eZ=e=>{U(e),P(!0)},eY=e=>{U(e),B(!0)},e0=e=>{U(e),P(!0)},e1=e=>{J(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])},e5=(0,S.n)({mutationFn:ed.IF,onSuccess:(e,t)=>{var r,l;let i=null==(l=e.data)||null==(r=l.questionGroup)?void 0:r.id;if(i&&t.selectedQuestionIds){let e=eA.map(e=>{var s;return(null==(s=t.selectedQuestionIds)?void 0:s.includes(e.id))?{...e,questionGroupId:i}:e});ey.setQueryData(eN,e);let s={id:i,title:t.title,order:t.order,projectId:t.projectId,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),question:e.filter(e=>e.questionGroupId===i)};ey.setQueryData(ew,[...eT,s])}ey.invalidateQueries({queryKey:ew}),ey.invalidateQueries({queryKey:eN}),"project"===s&&ey.invalidateQueries({queryKey:eq}),ej((0,er.Ds)({message:c("groupCreated"),type:"success"})),J([]),Y(!1),ef(!1)},onError:e=>{console.error(c("groupCreateError"),e),ej((0,er.Ds)({message:c("groupCreateFailed"),type:"error"})),ef(!1)}}),e4=(e,t)=>{_(e),X(t)},e2=()=>{_(null),X("")},e3=(0,S.n)({mutationFn:ed.lr,onSuccess:()=>{ey.invalidateQueries({queryKey:ew}),"project"===s&&ey.invalidateQueries({queryKey:eq}),ej((0,er.Ds)({message:c("groupNameUpdated"),type:"success"})),_(null),X(""),ef(!1)},onError:()=>{ej((0,er.Ds)({message:c("groupNameUpdateFailed"),type:"error"})),ef(!1)}}),e6=e=>{if(!W.trim())return void ej((0,er.Ds)({message:c("groupNameEmpty"),type:"warning"}));ef(!0);let t=eT.find(t=>t.id===e);if(!t)return;let s=eT.map(t=>t.id===e?{...t,title:W}:t);ey.setQueryData(ew,s),e3.mutate({id:e,title:W,order:t.order})};(0,S.n)({mutationFn:A.Af,onSuccess:()=>{ey.invalidateQueries({queryKey:eN})},onError:e=>{console.error(c("addQuestionError"),e),ej((0,er.Ds)({message:c("addQuestionFailed"),type:"error"}))}});let e7=async e=>{if(0!==e.length){E(!0);try{let t=eA.length>0?Math.max(...eA.map(e=>e.position)):0;for(let r=0;r<e.length;r++){let l=e[r],i={label:l.label,isRequired:l.isRequired,hint:l.hint||"",placeholder:l.placeholder||"",inputType:String(l.inputType),questionOptions:l.questionOptions||[]};await (0,A.Af)({contextType:s,contextId:a,dataToSend:i,position:t+r+1})}ey.invalidateQueries({queryKey:eN}),"project"===s&&ey.invalidateQueries({queryKey:eq}),ej((0,er.Ds)({message:"".concat(e.length," ").concat(c("questionsAdded")),type:"success"}))}catch(e){console.error("Error adding questions:",e),ej((0,er.Ds)({message:c("addFromLibraryFailed"),type:"error"}))}finally{E(!1)}}},e9="project"===s?ek:eQ,e8="project"===s?eI:eS;return e9?(0,r.jsx)("div",{className:"min-h-[60vh] flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:c("loadingFormData")})]})}):e8?(0,r.jsx)("div",{className:"min-h-[60vh] flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-red-500 mb-4",children:c("formLoadError")}),(0,r.jsx)("button",{onClick:()=>{"project"===s?ey.invalidateQueries({queryKey:eq}):ey.invalidateQueries({queryKey:eN})},className:"btn-primary",children:c("retry")})]})}):(0,r.jsxs)("div",{className:"min-h-[60vh] relative",children:[(eM.isPending||eP.isPending||eL.isPending||eK.isPending||eB.isPending||I)&&(0,r.jsx)(L,{}),(0,r.jsx)(es,{showModal:x,setShowModal:h,contextType:s,contextId:a,position:eA.length>0?Math.max(...eA.map(e=>e.position))+1:1}),G&&"table"!==G.inputType&&(0,r.jsx)(ei,{showModal:g,setShowModal:v,contextType:s,question:G,contextId:a}),G&&"table"===G.inputType&&(0,r.jsx)(en,{showModal:w,setShowModal:q,contextType:s,question:G,contextId:a}),(0,r.jsx)(ec,{showModal:R,setShowModal:O,contextType:s,contextId:a,questions:eA,questionGroups:eT}),V&&(0,r.jsx)(ec,{showModal:M,setShowModal:P,contextType:s,contextId:a,existingGroup:eT.find(e=>e.id===V),questions:eA,questionGroups:eT}),(0,r.jsx)(ea.R,{showModal:F,onClose:()=>z(!1),onConfirm:()=>{G&&G.id&&eM.mutate({contextType:s,id:null==G?void 0:G.id,projectId:a})},title:c("deleteQuestion"),description:c("confirmDeleteQuestion"),confirmButtonText:c("delete"),cancelButtonText:c("cancel"),confirmButtonClass:"btn-danger"}),(0,r.jsx)(eu,{showModal:K,setShowModal:B,onConfirmDelete:()=>{V&&(ef(!0),eL.mutate({id:V}))},onConfirmDeleteWithQuestions:()=>{V&&(ef(!0),eK.mutate({id:V}))},isDeleting:eL.isPending||eK.isPending}),(0,r.jsx)(ep,{isOpen:C,onClose:()=>k(!1),onAddQuestions:e7}),(0,r.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsxs)("h1",{className:"heading-text mr-4",children:[" ",c("formBuilder")," "]}),$.length>0?(0,r.jsxs)("button",{className:"btn-primary flex items-center gap-2",onClick:()=>{if(0===$.length)return void ej((0,er.Ds)({message:c("selectAtLeastOneQuestion"),type:"warning"}));ef(!0);let e=eA.filter(e=>$.includes(e.id)),t=e.length>0?Math.min(...e.map(e=>e.position)):eT.length+1;e5.mutate({title:c("newGroup"),order:t,projectId:a,selectedQuestionIds:$})},disabled:ev,children:[(0,r.jsx)(b.A,{size:16}),c("createGroup")," (",$.length,")"]}):(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsxs)("button",{className:"btn-outline flex items-center gap-2",onClick:()=>{Z?(Y(!1),J([])):Y(!0)},children:[(0,r.jsx)(b.A,{size:16}),Z?c("cancelSelection"):c("selectQuestions")]}),(0,r.jsxs)("button",{className:"btn-outline flex items-center gap-2",onClick:()=>O(!0),children:[(0,r.jsx)(b.A,{size:16}),c("createEmptyGroup")]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("button",{className:"btn-outline p-2",onClick:()=>t(!0),title:c("previewForm"),children:(0,r.jsx)(j.A,{size:16})}),(0,r.jsx)("button",{className:"btn-outline p-2",onClick:()=>k(!0),title:c("questionLibrary"),children:(0,r.jsx)(y.A,{size:16})})]})]}),(0,r.jsx)("div",{className:"section shadow-none border border-neutral-400",children:(0,r.jsx)(i.Mp,{sensors:d,collisionDetection:i.fp,onDragEnd:e=>{let{active:t,over:r}=e;if(!r||t.id===r.id||"project"!==s)return;let l=t.data.current,i=r.data.current;if((null==l?void 0:l.type)==="question"&&(null==i?void 0:i.type)==="question"){let e=eA.find(e=>e.id===t.id),l=eA.find(e=>e.id===r.id);if(!e||!l||null!==e.questionGroupId&&void 0!==e.questionGroupId||null!==l.questionGroupId&&void 0!==l.questionGroupId||e.questionGroupId!==l.questionGroupId)return;let i=eA.filter(e=>null===e.questionGroupId||void 0===e.questionGroupId).sort((e,t)=>e.position-t.position),o=i.findIndex(e=>e.id===t.id),d=i.findIndex(e=>e.id===r.id);if(-1===o||-1===d)return;let c=(0,n.be)(i,o,d).map((e,t)=>({id:Number(e.id),position:t+1})),u=eA.map(e=>{let t=c.find(t=>t.id===e.id);return t?{...e,position:t.position}:e});ey.setQueryData(eN,u),eB.mutate({contextType:s,contextId:a,questionPositions:c})}if((null==l?void 0:l.type)==="question"&&(null==i?void 0:i.type)==="group-drop"){let e=Number(t.id);e$(e,l.questionGroupId||null,i.groupId)}if((null==l?void 0:l.type)==="group"&&(null==i?void 0:i.type)==="group-drop"){let e=l.groupId,t=i.groupId;e!==t&&eH(e,t)}(null==l?void 0:l.type)==="group"&&(null==i||i.type)},children:(0,r.jsx)(n.gB,{items:[...eA.map(e=>e.id),...eT.map(e=>"group-".concat(e.id))],strategy:n._G,children:(0,r.jsx)("div",{className:"space-y-4",children:0===eA.length?(0,r.jsxs)("div",{className:"text-center py-16 px-4",children:[(0,r.jsx)("h3",{className:"heading-text text-muted-foreground",children:c("noQuestionsYet")}),(0,r.jsx)("p",{className:"mt-1 text-sm sub-text",children:c("addFirstQuestion")}),(0,r.jsx)("div",{className:"p-4 flex justify-center",children:(0,r.jsxs)("button",{onClick:()=>h(!0),className:"btn-primary",disabled:!u,children:[(0,r.jsx)(N.A,{size:16}),c("addFirst")]})})]}):eO.map(e=>{if("group"===e.type){let t=e.data,l=t.question||[];return(0,r.jsx)("div",{className:"mb-4",children:(0,r.jsx)(f,{id:t.id,title:t.title,questions:l,subGroups:t.subGroups,parentGroupId:t.parentGroupId,nestingLevel:0,onEditGroup:eZ,onDeleteGroup:eY,onAddQuestionToGroup:e0,onEditQuestion:e=>{D(e),"table"===e.inputType?q(!0):v(!0)},onDeleteQuestion:e=>{D(e),z(!0)},onDuplicateQuestion:e=>{D(e),eP.mutate({id:e.id,contextType:s,contextId:a})},onReorderQuestions:eJ,onMoveQuestionBetweenGroups:e$,onMoveGroupInsideGroup:eH,isEditing:H===t.id,onStartEditing:e4,onSaveGroupName:e6,onCancelEditing:e2,editingName:W,onEditingNameChange:X,selectionMode:Z,isDraggable:!0,selectedQuestionIds:$,onToggleQuestionSelect:e1,onCreateSubgroup:e_})},"group-".concat(t.id))}{let t=e.data;return(0,r.jsx)("div",{className:"mb-4",children:(0,r.jsx)(p,{question:t,onEdit:()=>{D(t),"table"===t.inputType?q(!0):v(!0)},onDelete:()=>{D(t),z(!0)},onDuplicate:()=>{D(t),eP.mutate({id:t.id,contextType:s,contextId:a})},selectionMode:Z,isSelected:$.includes(t.id),onToggleSelect:()=>e1(t.id)})},"question-".concat(t.id))}})})})})}),ee&&(0,r.jsx)("div",{className:"fixed inset-0 bg-neutral-900/50 flex items-center justify-center z-40",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-md mx-4",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Create Subgroup"}),(0,r.jsxs)("p",{className:"text-sm text-neutral-600 mb-4",children:["Creating a subgroup with ",ex.length," selected question(s)."]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{htmlFor:"subgroup-name",className:"block text-sm font-medium mb-2",children:"Subgroup Name"}),(0,r.jsx)("input",{id:"subgroup-name",type:"text",value:eb,onChange:e=>eg(e.target.value),placeholder:"Enter subgroup name",className:"w-full px-3 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",autoFocus:!0,onKeyDown:e=>{"Enter"===e.key?eW():"Escape"===e.key&&eX()}})]}),(0,r.jsxs)("div",{className:"flex justify-end gap-3",children:[(0,r.jsx)("button",{onClick:eX,className:"px-4 py-2 text-neutral-600 hover:text-neutral-800 transition-colors",children:"Cancel"}),(0,r.jsx)("button",{onClick:eW,disabled:!eb.trim(),className:"px-4 py-2 bg-primary-500 text-white rounded-md hover:bg-primary-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:"Create Subgroup"})]})]})}),eA.length>0&&(0,r.jsx)("div",{className:"sticky bottom-0 p-4 flex justify-center",children:(0,r.jsxs)("button",{className:"btn-primary  max-w-md flex items-center justify-center gap-2 ".concat(!u&&"text-gray-400 cursor-not-allowed"),onClick:()=>h(!0),disabled:!u,children:[(0,r.jsx)(N.A,{size:16}),c("addQuestion")]})})]})}},50408:(e,t,s)=>{"use strict";s.d(t,{l:()=>n});var r=s(95155),l=s(66474),i=s(12115);let n=e=>{let{id:t,options:s,value:n,onChange:a}=e,[o,d]=(0,i.useState)(!1),c=(0,i.useRef)(null),u=(0,i.useRef)([]),m=(0,i.useRef)(null);(0,i.useEffect)(()=>{let e=e=>{m.current&&!m.current.contains(e.target)&&d(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]);let p=e=>{if(!o)return;let t=e.key.toLowerCase();if(t.match(/[a-z]/)){let e=s.findIndex(e=>e.toLowerCase().startsWith(t));if(-1!==e&&u.current[e]){var r;null==(r=u.current[e])||r.scrollIntoView({behavior:"auto",block:"nearest"})}}};return(0,i.useEffect)(()=>(document.addEventListener("keydown",p),()=>{document.removeEventListener("keydown",p)}),[o,s]),(0,r.jsxs)("div",{className:"relative",ref:m,children:[(0,r.jsxs)("button",{id:t,type:"button",className:"px-4 py-2 flex items-center justify-between rounded-md border border-neutral-400 focus:border-primary-500 duration-300 w-full text-left cursor-pointer",onClick:()=>{d(!o)},children:[(0,r.jsx)("span",{children:n||"Select an option"}),(0,r.jsx)(l.A,{})]}),o&&(0,r.jsx)("ul",{className:"absolute z-10 max-h-[180px] overflow-auto border border-neutral-400 rounded-md bg-neutral-100 w-full mt-1 flex flex-col",ref:c,children:s.map((e,t)=>(0,r.jsx)("li",{ref:e=>{u.current[t]=e},className:"cursor-pointer bg-neutral-100 hover:bg-neutral-200 px-4 py-2",onClick:()=>{a(e),d(!1)},children:e},t))})]})}},52707:(e,t,s)=>{"use strict";s.d(t,{XV:()=>l,cZ:()=>a,ru:()=>i,yi:()=>r});let r=(e,t)=>{let s=new Map;e.forEach(e=>{let r=t.filter(t=>t.questionGroupId===e.id).sort((e,t)=>e.position-t.position);s.set(e.id,{...e,subGroups:[],question:r})});let r=[];return e.forEach(e=>{let t=s.get(e.id);if(e.parentGroupId){let r=s.get(e.parentGroupId);r&&(r.subGroups=r.subGroups||[],r.subGroups.push(t))}else r.push(t)}),r},l=(e,t)=>{let s=[];return e.forEach(e=>{let t=e=>[...e.question||[],...(e.subGroups||[]).flatMap(t)],r=t(e),l=r.length>0?Math.min(...r.map(e=>e.position)):e.order;s.push({type:"group",data:e,order:l,originalPosition:l})}),t.forEach(e=>{s.push({type:"question",data:e,order:e.position,originalPosition:e.position})}),s.sort((e,t)=>e.order===t.order?(e.originalPosition||e.order)-(t.originalPosition||t.order):e.order-t.order)},i=e=>e.filter(e=>null===e.questionGroupId||void 0===e.questionGroupId),n=e=>{let t=[];return e.forEach(e=>{t.push(e.id),e.subGroups&&e.subGroups.length>0&&t.push(...n(e.subGroups))}),t},a=function(e){let t=!(arguments.length>1)||void 0===arguments[1]||arguments[1],s={};return n(e).forEach(e=>{s[e]=t}),s}},58766:(e,t,s)=>{"use strict";s.d(t,{V:()=>N,F:()=>I});var r=s(95155),l=s(12115),i=s(97168),n=s(82714),a=s(89852),o=s(99474),d=s(95139),c=s(55747),u=s(69074),m=s(14186),p=s(42355),x=s(54416),h=s(66474),b=s(13052),g=s(16112),v=s(3587),f=s(13388),j=s(52707),y=s(17652);function N(e){let{questions:t,questionGroups:s=[],contextType:N="project",onClose:w,hashedId:q}=e,[C,k]=(0,l.useState)({}),[I,E]=(0,l.useState)({}),[Q,S]=(0,l.useState)([]),[A,T]=(0,l.useState)([]),[G,D]=(0,l.useState)({}),F=(0,y.c3)();(0,l.useEffect)(()=>{let e={};t.forEach(t=>{e[t.id]="selectmany"===t.inputType?[]:""}),k(e)},[t]),(0,l.useEffect)(()=>{if(t){let e=(0,v.UL)(t,C);S(e),T((0,v.Tr)(t,C));let s=(0,v.OD)(C,e);Object.keys(s).length!==Object.keys(C).length&&k(s)}},[t,C]);let z=l.useMemo(()=>(0,j.yi)(s,t),[s,t]),R=l.useMemo(()=>(0,j.ru)(t),[t]);(0,l.useEffect)(()=>{D((0,j.cZ)(z,!0))},[z]);let O=l.useMemo(()=>"project"===N?(0,j.XV)(z,R):t.map(e=>({type:"question",data:e,order:e.position,originalPosition:e.position})),[z,R,t,N]),M=e=>{D(t=>({...t,[e]:!t[e]}))},P=(e,t)=>{k(s=>({...s,[e]:t})),E(t=>({...t,[e]:""}))},L=e=>(0,r.jsxs)("div",{className:"border border-neutral-500 dark:border-neutral-700 rounded-md p-4",children:[(0,r.jsxs)("div",{className:"mb-2",children:[(0,r.jsxs)(n.J,{className:"text-base font-medium",children:[e.label,e.isRequired&&(0,r.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),e.hint&&(0,r.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:e.hint}),I[e.id]&&(0,r.jsx)("p",{className:"text-sm text-red-500 mt-1",children:I[e.id]})]}),(0,r.jsx)("div",{className:"mt-2",children:K(e)})]},e.id),K=e=>{var t,s,l,i;let p=null!=(t=C[e.id])?t:"selectmany"===e.inputType?[]:"";switch(e.inputType){case"text":if(null==(s=e.hint)?void 0:s.includes("multiline"))return(0,r.jsx)(o.T,{value:p,onChange:t=>P(e.id,t.target.value),placeholder:e.hint||F("yourAnswer"),required:e.isRequired});return(0,r.jsx)(a.p,{value:p,onChange:t=>P(e.id,t.target.value),placeholder:e.hint||F("yourAnswer"),required:e.isRequired});case"number":return(0,r.jsx)(a.p,{type:"number",value:p,onChange:t=>P(e.id,t.target.value),placeholder:e.hint||F("yourAnswer"),required:e.isRequired});case"decimal":return(0,r.jsx)(a.p,{type:"number",step:"any",value:p,onChange:t=>P(e.id,t.target.value),placeholder:e.hint||F("yourAnswer"),required:e.isRequired});case"selectone":return(0,r.jsx)(c.z,{value:p,onValueChange:t=>P(e.id,t),required:e.isRequired,children:(0,r.jsx)("div",{className:"space-y-2",children:null==(l=e.questionOptions)?void 0:l.map((e,t)=>(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(c.C,{value:e.label,id:"option-".concat(e.id)}),(0,r.jsx)(n.J,{htmlFor:"option-".concat(e.id),className:"cursor-pointer",children:e.label}),e.sublabel&&(0,r.jsx)("p",{className:"text-sm text-neutral-700 ml-4",children:"(".concat(e.sublabel,")")})]},t))})});case"selectmany":return(0,r.jsx)("div",{className:"space-y-2",children:null==(i=e.questionOptions)?void 0:i.map(t=>(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(d.S,{className:"w-5 h-5 border border-neutral-500",id:"option-".concat(t.id),checked:(p||[]).includes(t.label),onCheckedChange:s=>{let r=p||[],l=s?[...r,t.label]:r.filter(e=>e!==t.label);P(e.id,l)}}),(0,r.jsxs)(n.J,{htmlFor:"option-".concat(t.id),className:"cursor-pointer",children:[t.label," ",t.sublabel]})]},t.id))});case"date":return(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(a.p,{type:"date",value:p,onChange:t=>P(e.id,t.target.value),placeholder:e.hint||F("selectDate"),required:e.isRequired}),(0,r.jsx)(u.A,{className:"absolute top-3 right-3 h-4 w-4 text-muted-foreground pointer-events-none"})]});case"dateandtime":return(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(a.p,{type:"time",value:p,onChange:t=>P(e.id,t.target.value),placeholder:e.hint||F("selectTime"),required:e.isRequired}),(0,r.jsx)(m.A,{className:"absolute top-3 right-3 h-4 w-4 text-muted-foreground pointer-events-none"})]});case"table":return(0,r.jsx)(g.N,{questionId:e.id,value:p,onChange:t=>P(e.id,t),required:e.isRequired,tableLabel:e.label});default:return null}};return(0,r.jsxs)("div",{className:"bg-neutral-100 dark:bg-neutral-800 rounded-md shadow-sm border border-neutral-500 dark:border-neutral-700",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-neutral-500 dark:border-neutral-700",children:[(0,r.jsx)(i.$,{variant:"ghost",size:"icon",onClick:w,children:(0,r.jsx)(p.A,{className:"h-5 w-5"})}),(0,r.jsx)("h2",{className:"text-lg font-semibold",children:F("formPreview")}),(0,r.jsx)(i.$,{className:"cursor-pointer hover:bg-neutral-200",variant:"ghost",size:"icon",onClick:w,children:(0,r.jsx)(x.A,{className:"h-5 w-5"})})]}),(0,r.jsx)("div",{className:"p-4 md:p-6",children:(0,r.jsxs)("div",{className:"space-y-6",children:[O.map(e=>{if("group"===e.type){let s=e.data,l=G[s.id],i=t.filter(e=>e.questionGroupId===s.id),n=i.filter(e=>Q.some(t=>t.id===e.id));return(0,r.jsxs)("div",{className:"border border-neutral-500 dark:border-neutral-600 rounded-lg bg-neutral-100 dark:bg-neutral-800 overflow-hidden",children:[(0,r.jsx)("div",{className:"flex items-center justify-between p-4 border-b border-neutral-500 dark:border-neutral-700 cursor-pointer hover:bg-neutral-200 dark:hover:bg-neutral-700",onClick:()=>M(s.id),children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[l?(0,r.jsx)(h.A,{className:"h-5 w-5 text-neutral-500"}):(0,r.jsx)(b.A,{className:"h-5 w-5 text-neutral-500"}),(0,r.jsx)("h3",{className:"text-lg font-semibold dark:text-neutral-100",children:s.title}),(0,r.jsxs)("span",{className:"text-sm text-neutral-700 dark:text-neutral-400",children:["(",n.length," ",F("visibleQuestion"),1!==n.length?F("s"):"",")"]})]})}),l&&(0,r.jsx)("div",{className:"p-4 space-y-4",children:A.filter(e=>i.some(t=>t.id===e.question.id)).map(e=>(0,r.jsx)(f.A,{questionGroup:e,renderQuestionInput:K,errors:I,className:""},e.question.id))})]},"group-".concat(s.id))}{let t=e.data;if(!Q.some(e=>e.id===t.id))return null;let s=A.find(e=>e.question.id===t.id);return s?(0,r.jsx)(f.A,{questionGroup:s,renderQuestionInput:K,errors:I,className:""},t.id):L(t)}}),0===t.length&&(0,r.jsx)("div",{className:"text-center py-12",children:(0,r.jsx)("p",{className:"text-muted-foreground",children:F("noFormQuestionsYet")})}),t.length>0&&0===Q.length&&(0,r.jsx)("div",{className:"text-center py-12",children:(0,r.jsx)("p",{className:"text-muted-foreground",children:F("noVisibleQuestions")})})]})})]})}var w=s(51154),q=s(80423),C=s(19373),k=s(34947);function I(e){let{projectId:t,onClose:s,hashedId:h}=e,[b,N]=(0,l.useState)({}),[I,E]=(0,l.useState)({}),[Q,S]=(0,l.useState)([]),[A,T]=(0,l.useState)([]),[G,D]=(0,l.useState)({}),F=(0,y.c3)(),{data:z,isLoading:R,isError:O,error:M}=(0,C.I)({queryKey:["formBuilderData",t],queryFn:()=>(0,k.gf)({projectId:t}),enabled:!!t}),P=(0,l.useMemo)(()=>{if(!(null==z?void 0:z.items))return[];let e=[];return z.items.forEach(t=>{"question"===t.type?e.push(t):"group"===t.type&&t.questions&&e.push(...t.questions)}),e.sort((e,t)=>e.position-t.position)},[z]),L=(0,l.useMemo)(()=>(null==z?void 0:z.items)?z.items.filter(e=>"group"===e.type).map(e=>({...e,question:e.questions||[]})):[],[z]);(0,l.useEffect)(()=>{if(P.length>0){let e={};P.forEach(t=>{e[t.id]="selectmany"===t.inputType?[]:""}),N(e)}},[P]),(0,l.useEffect)(()=>{if(P.length>0){let e=(0,v.UL)(P,b);S(e),T((0,v.Tr)(P,b));let t=(0,v.OD)(b,e);Object.keys(t).length!==Object.keys(b).length&&N(t)}},[P,b]);let K=(0,l.useMemo)(()=>(0,j.yi)(L,P),[L,P]),B=(0,l.useMemo)(()=>(0,j.ru)(P),[P]);(0,l.useEffect)(()=>{K.length>0&&D((0,j.cZ)(K,!0))},[K.length]);let V=(0,l.useMemo)(()=>(0,j.XV)(K,B),[K,B]),U=e=>{D(t=>({...t,[e]:!t[e]}))},$=(e,t)=>{N(s=>({...s,[e]:t})),E(t=>({...t,[e]:""}))},J=e=>(0,r.jsxs)("div",{className:"border border-neutral-500 dark:border-neutral-700 rounded-md p-4",children:[(0,r.jsxs)("div",{className:"mb-2",children:[(0,r.jsxs)(n.J,{className:"text-base font-medium",children:[e.label,e.isRequired&&(0,r.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),e.hint&&(0,r.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:e.hint}),I[e.id]&&(0,r.jsx)("p",{className:"text-sm text-red-500 mt-1",children:I[e.id]})]}),(0,r.jsx)("div",{className:"mt-2",children:H(e)})]},e.id),H=e=>{var t,s,l,i;let p=null!=(t=b[e.id])?t:"selectmany"===e.inputType?[]:"";switch(e.inputType){case"text":if(null==(s=e.hint)?void 0:s.includes("multiline"))return(0,r.jsx)(o.T,{value:p,onChange:t=>$(e.id,t.target.value),placeholder:e.hint||F("yourAnswer"),required:e.isRequired});return(0,r.jsx)(a.p,{value:p,onChange:t=>$(e.id,t.target.value),placeholder:e.hint||F("yourAnswer"),required:e.isRequired});case"number":return(0,r.jsx)(a.p,{type:"number",value:p,onChange:t=>$(e.id,t.target.value),placeholder:e.hint||F("yourAnswer"),required:e.isRequired});case"decimal":return(0,r.jsx)(a.p,{type:"number",step:"any",value:p,onChange:t=>$(e.id,t.target.value),placeholder:e.hint||F("yourAnswer"),required:e.isRequired});case"selectone":return(0,r.jsx)(c.z,{value:p,onValueChange:t=>$(e.id,t),required:e.isRequired,children:(0,r.jsx)("div",{className:"space-y-2",children:null==(l=e.questionOptions)?void 0:l.map((e,t)=>(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(c.C,{value:e.label,id:"option-".concat(e.id)}),(0,r.jsx)(n.J,{htmlFor:"option-".concat(e.id),className:"cursor-pointer",children:e.label}),e.sublabel&&(0,r.jsx)("p",{className:"text-sm text-neutral-700 ml-4",children:"(".concat(e.sublabel,")")})]},t))})});case"selectmany":return(0,r.jsx)("div",{className:"space-y-2",children:null==(i=e.questionOptions)?void 0:i.map(t=>(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(d.S,{className:"w-5 h-5 border border-neutral-500",id:"option-".concat(t.id),checked:(p||[]).includes(t.label),onCheckedChange:s=>{let r=p||[],l=s?[...r,t.label]:r.filter(e=>e!==t.label);$(e.id,l)}}),(0,r.jsxs)(n.J,{htmlFor:"option-".concat(t.id),className:"cursor-pointer",children:[t.label," ",t.sublabel]})]},t.id))});case"date":return(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(a.p,{type:"date",value:p,onChange:t=>$(e.id,t.target.value),placeholder:e.hint||F("selectDate"),required:e.isRequired}),(0,r.jsx)(u.A,{className:"absolute top-3 right-3 h-4 w-4 text-muted-foreground pointer-events-none"})]});case"dateandtime":return(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(a.p,{type:"time",value:p,onChange:t=>$(e.id,t.target.value),placeholder:e.hint||F("selectTime"),required:e.isRequired}),(0,r.jsx)(m.A,{className:"absolute top-3 right-3 h-4 w-4 text-muted-foreground pointer-events-none"})]});case"table":return(0,r.jsx)(g.N,{questionId:e.id,value:p,onChange:t=>$(e.id,t),required:e.isRequired,tableLabel:e.label});default:return null}};return R?(0,r.jsxs)("div",{className:"bg-neutral-100 dark:bg-neutral-800 rounded-md shadow-sm border border-neutral-500 dark:border-neutral-700",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-neutral-500 dark:border-neutral-700",children:[(0,r.jsx)(i.$,{variant:"ghost",size:"icon",onClick:s,children:(0,r.jsx)(p.A,{className:"h-5 w-5"})}),(0,r.jsx)("h2",{className:"text-lg font-semibold",children:F("formPreview")}),(0,r.jsx)(i.$,{className:"cursor-pointer hover:bg-neutral-200",variant:"ghost",size:"icon",onClick:s,children:(0,r.jsx)(x.A,{className:"h-5 w-5"})})]}),(0,r.jsx)("div",{className:"p-4 md:p-6",children:(0,r.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(w.A,{className:"h-6 w-6 animate-spin"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:F("loadingForm")})]})})})]}):O?(0,r.jsxs)("div",{className:"bg-neutral-100 dark:bg-neutral-800 rounded-md shadow-sm border border-neutral-500 dark:border-neutral-700",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-neutral-500 dark:border-neutral-700",children:[(0,r.jsx)(i.$,{variant:"ghost",size:"icon",onClick:s,children:(0,r.jsx)(p.A,{className:"h-5 w-5"})}),(0,r.jsx)("h2",{className:"text-lg font-semibold",children:F("formPreview")}),(0,r.jsx)(i.$,{className:"cursor-pointer hover:bg-neutral-200",variant:"ghost",size:"icon",onClick:s,children:(0,r.jsx)(x.A,{className:"h-5 w-5"})})]}),(0,r.jsx)("div",{className:"p-4 md:p-6",children:(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("p",{className:"text-red-500 mb-2",children:F("errorLoadingForm")}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:M instanceof Error?M.message:F("unknownError")})]})})]}):(0,r.jsxs)("div",{className:"bg-neutral-100 dark:bg-neutral-800 rounded-md shadow-sm border border-neutral-500 dark:border-neutral-700",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-neutral-500 dark:border-neutral-700",children:[(0,r.jsx)(i.$,{variant:"ghost",size:"icon",onClick:s,children:(0,r.jsx)(p.A,{className:"h-5 w-5"})}),(0,r.jsx)("h2",{className:"text-lg font-semibold",children:F("formPreview")}),(0,r.jsx)(i.$,{className:"cursor-pointer hover:bg-neutral-200",variant:"ghost",size:"icon",onClick:s,children:(0,r.jsx)(x.A,{className:"h-5 w-5"})})]}),(0,r.jsx)("div",{className:"p-4 md:p-6",children:(0,r.jsxs)("div",{className:"space-y-6",children:[V.map(e=>{if("group"===e.type){let t=e.data,s=G[t.id];return(0,r.jsx)(q.A,{group:t,nestingLevel:0,visibleQuestions:Q,nestedQuestions:A,renderQuestionInput:H,errors:I,onToggleExpansion:U,isExpanded:s,expandedGroups:G,className:""},"group-".concat(t.id))}{let t=e.data;if(!Q.some(e=>e.id===t.id))return null;let s=A.find(e=>e.question.id===t.id);return s?(0,r.jsx)(f.A,{questionGroup:s,renderQuestionInput:H,errors:I,className:""},t.id):J(t)}}),0===P.length&&(0,r.jsx)("div",{className:"text-center py-12",children:(0,r.jsx)("p",{className:"text-muted-foreground",children:F("noFormQuestionsYet")})}),P.length>0&&0===Q.length&&(0,r.jsx)("div",{className:"text-center py-12",children:(0,r.jsx)("p",{className:"text-muted-foreground",children:F("noVisibleQuestions")})})]})})]})}},63642:(e,t,s)=>{"use strict";s.d(t,{R:()=>i});var r=s(95155);s(12115);var l=s(13163);let i=e=>{let{showModal:t,onClose:s,onConfirm:i,title:n,description:a,confirmButtonText:o,cancelButtonText:d,confirmButtonClass:c,children:u}=e;return(0,r.jsxs)(l.A,{isOpen:t,onClose:s,className:"p-6 rounded-md max-w-xl",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-neutral-700",children:n}),(0,r.jsx)("div",{className:"text-neutral-700 mt-2",children:a}),u&&(0,r.jsx)("div",{className:"mt-6 space-y-4",children:u}),(0,r.jsxs)("div",{className:"flex justify-end gap-4 mt-6",children:[(0,r.jsx)("button",{className:"btn-outline",onClick:s,type:"button",children:d||"Cancel"}),(0,r.jsx)("button",{className:"font-medium rounded-md shadow-md text-neutral-100 flex items-center justify-center gap-2 px-4 py-2 active:scale-95 transition-all duration-300 ".concat(c),onClick:i,type:"button",children:o})]})]})}},64368:(e,t,s)=>{"use strict";s.d(t,{H:()=>r});let r=(e,t)=>{let s=Object.entries(t).find(t=>{let[s,r]=t;return r===e});return s?s[0]:null}},80423:(e,t,s)=>{"use strict";s.d(t,{A:()=>d});var r=s(95155),l=s(12115),i=s(66474),n=s(13052),a=s(13388);let o=e=>{let{group:t,nestingLevel:s=0,visibleQuestions:d,nestedQuestions:c,renderQuestionInput:u,errors:m,onToggleExpansion:p,isExpanded:x,expandedGroups:h,className:b=""}=e,[g,v]=(0,l.useState)(!0),f=void 0!==x?x:g,j=t.question||[],y=j.filter(e=>d.some(t=>t.id===e.id)),N=(t.subGroups||[]).filter(e=>(e.question||[]).some(e=>d.some(t=>t.id===e.id)));return 0===y.length&&0===N.length?null:(0,r.jsxs)("div",{className:"border border-neutral-400 rounded-md bg-card shadow-sm mb-4 ".concat(s>0?"ml-8 border-l-4 border-l-primary-300":""," ").concat(b),children:[(0,r.jsx)("div",{className:"flex items-center justify-between p-4 bg-neutral-100 border-b border-neutral-300 rounded-t-md cursor-pointer hover:bg-neutral-200 dark:bg-gray-700 dark:border-gray-600 dark:hover:bg-gray-600",onClick:()=>{p?p(t.id):v(!g)},children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[f?(0,r.jsx)(i.A,{className:"h-5 w-5 text-neutral-700 dark:text-neutral-300"}):(0,r.jsx)(n.A,{className:"h-5 w-5 text-neutral-700 dark:text-neutral-300"}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-neutral-900 dark:text-neutral-100",children:t.title}),(0,r.jsxs)("span",{className:"text-sm text-neutral-700 dark:text-neutral-400",children:["(",y.length+N.reduce((e,t)=>{var s;return e+((null==(s=t.question)?void 0:s.length)||0)},0)," visible question",y.length+N.reduce((e,t)=>{var s;return e+((null==(s=t.question)?void 0:s.length)||0)},0)!==1?"s":"",")"]})]})}),f&&(0,r.jsxs)("div",{className:"p-4 space-y-4",children:[N.sort((e,t)=>e.order-t.order).map(e=>{let t=h?h[e.id]:void 0;return(0,r.jsx)(o,{group:e,nestingLevel:s+1,visibleQuestions:d,nestedQuestions:c,renderQuestionInput:u,errors:m,onToggleExpansion:p,isExpanded:t,expandedGroups:h,className:b},e.id)}),c.filter(e=>j.some(t=>t.id===e.question.id)).map(e=>(0,r.jsx)(a.A,{questionGroup:e,renderQuestionInput:u,errors:m,className:""},e.question.id))]})]})},d=o},83686:()=>{},97168:(e,t,s)=>{"use strict";s.d(t,{$:()=>o});var r=s(95155);s(12115);var l=s(99708),i=s(74466),n=s(53999);let a=(0,i.F)("inline-flex items-center justify-center gap-2 neutral-100space-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-neutral-100 shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:s,size:i,asChild:o=!1,...d}=e,c=o?l.DX:"button";return(0,r.jsx)(c,{"data-slot":"button",className:(0,n.cn)(a({variant:s,size:i,className:t})),...d})}}}]);