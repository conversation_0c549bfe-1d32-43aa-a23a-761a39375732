"use strict";exports.id=3341,exports.ids=[3341],exports.modules={96:(e,r,t)=>{t.d(r,{BU:()=>i,IF:()=>o,Vq:()=>p,_U:()=>d,kO:()=>u,lr:()=>n,pr:()=>a,yb:()=>l});var s=t(12810);let a=async({projectId:e})=>{try{let{data:r}=await s.A.get(`/projects/form/${e}`);return r.data?.project?.questionGroup||[]}catch(r){console.error("Error fetching question groups from project endpoint:",r);try{let{data:r}=await s.A.post("/question-groups",{projectId:e});return r.data?.projectGroup||[]}catch(e){return console.error("Error in fallback fetch:",e),[]}}},o=async({title:e,order:r,projectId:t,selectedQuestionIds:a,parentGroupId:o})=>{try{let{data:n}=await s.A.post("/question-groups",{title:e,order:r,projectId:t,selectedQuestionIds:a||[],parentGroupId:o});return n}catch(e){throw console.error("Error creating question group:",e),e}},n=async({id:e,title:r,order:t,selectedQuestionIds:a})=>{try{let{data:o}=await s.A.patch("/question-groups",{id:e,title:r,order:t,selectedQuestionIds:a});return o}catch(e){throw console.error("Error updating question group:",e),e}},i=async({id:e})=>{try{let{data:r}=await s.A.delete(`/question-groups/${e}`);return r}catch(e){throw console.error("Error deleting question group:",e),e}},l=async({id:e})=>{try{let{data:r}=await s.A.delete(`/question-groups/group/question/${e}`);return r}catch(e){throw console.error("Error deleting question group and questions:",e),e}},d=async({groupId:e,newGroupId:r,questionId:t})=>{let{data:a}=await s.A.patch("/question-groups/question/move",{groupId:e,newGroupId:r,questionId:t});return a},u=async({childGroupId:e,parentGroupId:r})=>{let{data:t}=await s.A.patch("/question-groups/group/add",{childGroupId:e,ParentGroupId:r});return t},p=async({projectId:e,groupPositions:r})=>{let{data:t}=await s.A.patch("/question-groups/positions",{projectId:e,groupPositions:r});return t}},13784:(e,r,t)=>{t.d(r,{N:()=>l});var s=t(60687),a=t(43210),o=t.n(a),n=t(96752),i=t(68988);function l({questionId:e,value:r,onChange:t,required:l=!1,tableLabel:d}){let[u,p]=(0,a.useState)([]),[c,m]=(0,a.useState)([]),[h,f]=(0,a.useState)({}),[g,b]=(0,a.useState)(!0),[x,w]=(0,a.useState)(null),[y,q]=(0,a.useState)({}),j=o().useMemo(()=>{if(0===u.length)return{parentColumns:[],columnMap:new Map,hasChildColumns:!1};let e=u.filter(e=>void 0===e.parentColumnId||null===e.parentColumnId),r=new Map;e.forEach(e=>{let t=u.filter(r=>r.parentColumnId===e.id);r.set(e.id,t)});let t=e.some(e=>(r.get(e.id)||[]).length>0);return{parentColumns:e,columnMap:r,hasChildColumns:t}},[u]),N=(e,r,s)=>{let a=`${e}_${r}`;f(e=>({...e,[a]:s})),setTimeout(()=>{let e={...h,[a]:s},r=[];Object.entries(e).forEach(([e,t])=>{if(""!==t.trim()){let[s,a]=e.split("_").map(Number);r.push({columnId:s,rowsId:a,value:t})}}),t(r)},0)},v=0===u.length;return(0,s.jsx)("div",{className:"overflow-x-auto",children:g?(0,s.jsx)("div",{className:"py-4 text-center",children:"Loading table..."}):x?(0,s.jsx)("div",{className:"py-4 text-center text-red-500",children:x}):v?(0,s.jsx)("div",{className:"py-4 text-center text-amber-600",children:"This table has no columns defined. Please configure the table question first."}):(0,s.jsxs)(n.XI,{className:"border-collapse",children:[(0,s.jsxs)(n.A0,{children:[(0,s.jsx)(n.Hj,{children:j.parentColumns.map(e=>{let r=(j.columnMap.get(e.id)||[]).length||1;return(0,s.jsx)(n.nd,{colSpan:r,className:"text-center border bg-blue-50 font-medium",children:e.columnName},e.id)})}),j.hasChildColumns&&(0,s.jsx)(n.Hj,{children:j.parentColumns.map(e=>{let r=j.columnMap.get(e.id)||[];return 0===r.length?(0,s.jsx)(n.nd,{className:"border bg-blue-50/50 text-sm"},`empty-${e.id}`):r.map(e=>(0,s.jsx)(n.nd,{className:"border bg-blue-50/50 text-sm",children:e.columnName},e.id))})})]}),(0,s.jsx)(n.BF,{children:c.length>0?c.map((e,r)=>(0,s.jsx)(n.Hj,{className:r%2==0?"bg-white":"bg-gray-50",children:j.parentColumns.map(r=>{let t=j.columnMap.get(r.id)||[];return 0===t.length?(0,s.jsx)(n.nA,{className:"border p-1",children:(0,s.jsx)(i.p,{value:h[`${r.id}_${e.id}`]||"",onChange:t=>N(r.id,e.id,t.target.value),className:"w-full",required:l,placeholder:"Enter value"})},`cell-${r.id}-${e.id}`):t.map(r=>(0,s.jsx)(n.nA,{className:"border p-1",children:(0,s.jsx)(i.p,{value:h[`${r.id}_${e.id}`]||"",onChange:t=>N(r.id,e.id,t.target.value),className:"w-full",required:l,placeholder:"Enter value"})},`cell-${r.id}-${e.id}`))})},e.id)):(0,s.jsx)(n.Hj,{children:j.parentColumns.map(e=>{let r=j.columnMap.get(e.id)||[];return 0===r.length?(0,s.jsx)(n.nA,{className:"border p-1",children:(0,s.jsx)(i.p,{value:h[`${e.id}_no_row`]||"",onChange:r=>N(e.id,"no_row",r.target.value),className:"w-full",required:l,placeholder:"Enter value"})},`cell-${e.id}-no-row`):r.map(e=>(0,s.jsx)(n.nA,{className:"border p-1",children:(0,s.jsx)(i.p,{value:h[`${e.id}_no_row`]||"",onChange:r=>N(e.id,"no_row",r.target.value),className:"w-full",required:l,placeholder:"Enter value"})},`cell-${e.id}-no-row`))})})})]})})}t(15695)},15695:(e,r,t)=>{t.d(r,{ZR:()=>o,am:()=>n,q7:()=>a});var s=t(12810);let a=async e=>{try{if(!e||isNaN(e))throw console.error("Invalid questionId:",e),Error("Invalid question ID provided");try{let r=await s.A.get(`/table-questions/${e}`);if(r.data&&r.data.data&&r.data.data.question)return r.data.data.question;if(r.data&&r.data.data)return r.data.data;if(r.data&&r.data.success)return r.data}catch(e){console.error("Error from /table-questions/ endpoint:",e)}try{let r=await s.A.get(`/questions/${e}`);if(r.data&&r.data.data)return r.data.data}catch(e){console.error("Error from /questions/ endpoint:",e)}try{let r=await s.A.get(`/tables/${e}`);if(r.data&&r.data.data&&r.data.data.question)return r.data.data.question}catch(e){console.error("Error from /tables/ endpoint:",e)}throw console.error("All endpoints failed to return valid data"),Error("Failed to fetch table structure from any endpoint")}catch(e){throw console.error("Error fetching table structure:",e),e}},o=async(e,r,t,a)=>{try{if(!e||!e.trim())throw Error("Table label is required");if(!r||isNaN(r))throw Error("Valid project ID is required");if(!t||!Array.isArray(t)||0===t.length)throw Error("At least one column is required");if(a&&!Array.isArray(a))throw Error("Rows must be an array if provided");if(t.filter(e=>!e.columnName||!e.columnName.trim()).length>0)throw Error("All columns must have valid names");if(a&&a.filter(e=>!e.rowsName||!e.rowsName.trim()).length>0)throw Error("All rows must have valid names");let o=t.map(e=>({columnName:e.columnName,parentColumnId:e.parentColumnId})),{data:n}=await s.A.post("/table-questions",{label:e,projectId:r,columns:o,rows:a||[]});if(!n||!n.success)throw Error(n?.message||"Failed to create table");return n.data}catch(e){throw console.error("Error creating table:",e),e.response&&(console.error("Response status:",e.response.status),console.error("Response data:",e.response.data),e.response.data&&e.response.data.message&&(e.message=e.response.data.message)),e}},n=async(e,r,t,a)=>{try{if(!r||!r.trim())throw Error("Table label is required");if(!e||isNaN(e))throw Error("Valid table ID is required");if(!t||!Array.isArray(t)||0===t.length)throw Error("At least one column is required");if(a&&!Array.isArray(a))throw Error("Rows must be an array if provided");if(t.filter(e=>!e.columnName||!e.columnName.trim()).length>0)throw Error("All columns must have valid names");if(a&&a.filter(e=>!e.rowsName||!e.rowsName.trim()).length>0)throw Error("All rows must have valid names");let o=new Map,n=new Map;for(let e of(t.forEach((e,r)=>{e.id&&o.set(e.id,e),n.set(r+1,e)}),t))if(e.parentColumnId){if(e.parentColumnId<=0)throw Error(`Invalid parent column ID: ${e.parentColumnId}. Must be a positive number.`);let r=t.find(r=>r.id===e.parentColumnId);if(!r&&e.parentColumnId<=t.length&&(r=n.get(e.parentColumnId)),!r)throw Error(`Parent column with ID/position ${e.parentColumnId} not found in the columns array.`);if(r.parentColumnId)throw Error("Cannot create more than 2 levels of nested columns (parent → child → grandchild)")}let i=t.map(e=>{let r={columnName:e.columnName.trim()};return e.id&&(r.id=e.id),void 0!==e.parentColumnId&&(r.parentColumnId=e.parentColumnId),r});try{let{data:t}=await s.A.patch(`/table-questions/${e}`,{label:r.trim(),columns:i,rows:a?a.map(e=>({...e,rowsName:e.rowsName.trim()})):[]});if(!t||!t.success)throw Error(t?.message||"Failed to update table");return t.data}catch(e){if(console.error("API error updating table:",e),e.response&&(console.error("Response status:",e.response.status),console.error("Response data:",e.response.data),e.response.data&&e.response.data.message))throw Error(e.response.data.message);throw e}}catch(e){if(console.error("Error updating table:",e),e.message)throw Error(`Failed to update table: ${e.message}`);throw Error("Failed to update table due to an unknown error")}}},24527:(e,r,t)=>{t.d(r,{OD:()=>d,Tr:()=>l,UL:()=>o,WK:()=>u});let s=(e,r)=>{if(!e.questionOptions||0===e.questionOptions.length)return null;if("selectone"===e.inputType&&"string"==typeof r){let t=e.questionOptions.find(e=>e.label===r);return t?.nextQuestionId||null}if("selectmany"===e.inputType&&Array.isArray(r))for(let t of r){let r=e.questionOptions.find(e=>e.label===t);if(r?.nextQuestionId)return r.nextQuestionId}return null},a=e=>e.questionOptions&&0!==e.questionOptions.length?e.questionOptions.map(e=>e.nextQuestionId).filter(e=>null!=e):[],o=(e,r)=>{let t=new Set,o=new Set;return e.forEach(e=>{a(e).forEach(e=>o.add(e))}),e.forEach(e=>{o.has(e.id)||t.add(e.id)}),Object.entries(r).forEach(([r,a])=>{let o=parseInt(r),n=e.find(e=>e.id===o);if(n&&a){let e=s(n,a);e&&t.add(e)}}),e.filter(e=>t.has(e.id))},n=(e,r)=>{let t=r.find(r=>r.id===e);if(!t)return[];let s=a(t);return r.filter(e=>s.includes(e.id))},i=(e,r)=>r.some(r=>a(r).includes(e)),l=(e,r)=>{let t=new Set(o(e,r).map(e=>e.id));return e.filter(r=>!i(r.id,e)).sort((e,r)=>e.position-r.position).map(r=>{let s=n(r.id,e).sort((e,r)=>e.position-r.position);return{question:r,isVisible:t.has(r.id),isFollowUp:!1,followUps:s.map(e=>({question:e,isVisible:t.has(e.id)}))}}).filter(e=>e.isVisible||e.followUps.some(e=>e.isVisible))},d=(e,r)=>{let t=new Set(r.map(e=>e.id)),s={};return Object.entries(e).forEach(([e,r])=>{t.has(parseInt(e))&&(s[e]=r)}),s},u=(e,r)=>{let t={};return e.forEach(e=>{if(e.isRequired){let s=r[e.id];("string"==typeof s&&!s.trim()||Array.isArray(s)&&0===s.length||null==s)&&(t[e.id]=`${e.label} is required`)}}),t}},31207:(e,r,t)=>{t.d(r,{XV:()=>a,cZ:()=>i,ru:()=>o,yi:()=>s});let s=(e,r)=>{let t=new Map;e.forEach(e=>{let s=r.filter(r=>r.questionGroupId===e.id).sort((e,r)=>e.position-r.position);t.set(e.id,{...e,subGroups:[],question:s})});let s=[];return e.forEach(e=>{let r=t.get(e.id);if(e.parentGroupId){let s=t.get(e.parentGroupId);s&&(s.subGroups=s.subGroups||[],s.subGroups.push(r))}else s.push(r)}),s},a=(e,r)=>{let t=[];return e.forEach(e=>{let r=e=>[...e.question||[],...(e.subGroups||[]).flatMap(r)],s=r(e),a=s.length>0?Math.min(...s.map(e=>e.position)):e.order;t.push({type:"group",data:e,order:a,originalPosition:a})}),r.forEach(e=>{t.push({type:"question",data:e,order:e.position,originalPosition:e.position})}),t.sort((e,r)=>e.order===r.order?(e.originalPosition||e.order)-(r.originalPosition||r.order):e.order-r.order)},o=e=>e.filter(e=>null===e.questionGroupId||void 0===e.questionGroupId),n=e=>{let r=[];return e.forEach(e=>{r.push(e.id),e.subGroups&&e.subGroups.length>0&&r.push(...n(e.subGroups))}),r},i=(e,r=!0)=>{let t={};return n(e).forEach(e=>{t[e]=r}),t}},44305:(e,r,t)=>{t.d(r,{A:()=>d});var s=t(60687),a=t(43210),o=t(78272),n=t(14952),i=t(69396);let l=({group:e,nestingLevel:r=0,visibleQuestions:t,nestedQuestions:d,renderQuestionInput:u,errors:p,onToggleExpansion:c,isExpanded:m,expandedGroups:h,className:f=""})=>{let[g,b]=(0,a.useState)(!0),x=void 0!==m?m:g,w=e.question||[],y=w.filter(e=>t.some(r=>r.id===e.id)),q=(e.subGroups||[]).filter(e=>(e.question||[]).some(e=>t.some(r=>r.id===e.id)));return 0===y.length&&0===q.length?null:(0,s.jsxs)("div",{className:`border border-neutral-400 rounded-md bg-card shadow-sm mb-4 ${r>0?"ml-8 border-l-4 border-l-primary-300":""} ${f}`,children:[(0,s.jsx)("div",{className:"flex items-center justify-between p-4 bg-neutral-100 border-b border-neutral-300 rounded-t-md cursor-pointer hover:bg-neutral-200 dark:bg-gray-700 dark:border-gray-600 dark:hover:bg-gray-600",onClick:()=>{c?c(e.id):b(!g)},children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[x?(0,s.jsx)(o.A,{className:"h-5 w-5 text-neutral-700 dark:text-neutral-300"}):(0,s.jsx)(n.A,{className:"h-5 w-5 text-neutral-700 dark:text-neutral-300"}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-neutral-900 dark:text-neutral-100",children:e.title}),(0,s.jsxs)("span",{className:"text-sm text-neutral-700 dark:text-neutral-400",children:["(",y.length+q.reduce((e,r)=>e+(r.question?.length||0),0)," visible question",y.length+q.reduce((e,r)=>e+(r.question?.length||0),0)!==1?"s":"",")"]})]})}),x&&(0,s.jsxs)("div",{className:"p-4 space-y-4",children:[q.sort((e,r)=>e.order-r.order).map(e=>{let a=h?h[e.id]:void 0;return(0,s.jsx)(l,{group:e,nestingLevel:r+1,visibleQuestions:t,nestedQuestions:d,renderQuestionInput:u,errors:p,onToggleExpansion:c,isExpanded:a,expandedGroups:h,className:f},e.id)}),d.filter(e=>w.some(r=>r.id===e.question.id)).map(e=>(0,s.jsx)(i.A,{questionGroup:e,renderQuestionInput:u,errors:p,className:""},e.question.id))]})]})},d=l},69396:(e,r,t)=>{t.d(r,{A:()=>o});var s=t(60687);t(43210);var a=t(39390);let o=({questionGroup:e,renderQuestionInput:r,errors:t,className:o=""})=>{let{question:n,isVisible:i,followUps:l}=e;return i||l.some(e=>e.isVisible)?(0,s.jsxs)("div",{className:`${o}`,children:[i&&(0,s.jsxs)("div",{className:"border border-neutral-500 dark:border-neutral-700 rounded-md p-4 bg-neutral-100 dark:bg-neutral-800",children:[(0,s.jsxs)("div",{className:"mb-2",children:[(0,s.jsxs)(a.J,{className:"text-base font-medium",children:[n.label,n.isRequired&&(0,s.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),n.hint&&(0,s.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:n.hint}),t[n.id]&&(0,s.jsx)("p",{className:"text-sm text-red-500 mt-1",children:t[n.id]})]}),(0,s.jsx)("div",{className:"mt-2",children:r(n)}),l.some(e=>e.isVisible)&&(0,s.jsx)("div",{className:"mt-4 ml-4 space-y-3 border-l-2 border-primary-200 dark:border-primary-700 pl-4",children:l.map(({question:e,isVisible:o})=>o&&(0,s.jsxs)("div",{className:"border border-neutral-100 dark:border-neutral-600 rounded-md p-3 bg-primary-50 dark:bg-primary-900/20",children:[(0,s.jsxs)("div",{className:"mb-2",children:[(0,s.jsxs)(a.J,{className:"text-sm font-medium text-primary-900 dark:text-primary-100",children:[e.label,e.isRequired&&(0,s.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),e.hint&&(0,s.jsx)("p",{className:"text-xs text-primary-700 dark:text-primary-300 mt-1",children:e.hint}),t[e.id]&&(0,s.jsx)("p",{className:"text-xs text-red-500 mt-1",children:t[e.id]})]}),(0,s.jsx)("div",{className:"mt-2",children:r(e)})]},e.id))})]}),!i&&l.some(e=>e.isVisible)&&(0,s.jsx)("div",{className:"space-y-3",children:l.map(({question:e,isVisible:o})=>o&&(0,s.jsxs)("div",{className:"border border-neutral-200 dark:border-neutral-700 rounded-md p-4 bg-white dark:bg-neutral-800",children:[(0,s.jsxs)("div",{className:"mb-2",children:[(0,s.jsxs)(a.J,{className:"text-base font-medium",children:[e.label,e.isRequired&&(0,s.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),e.hint&&(0,s.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:e.hint}),t[e.id]&&(0,s.jsx)("p",{className:"text-sm text-red-500 mt-1",children:t[e.id]})]}),(0,s.jsx)("div",{className:"mt-2",children:r(e)})]},e.id))})]}):null}},75531:(e,r,t)=>{t.d(r,{Af:()=>i,K4:()=>o,ae:()=>c,dI:()=>p,eL:()=>m,ej:()=>n,gf:()=>h,ku:()=>d,sr:()=>u,ul:()=>l});var s=t(12810);let a=e=>{if("project"===e)return"/questions";if("template"===e)return"/template-questions";if("questionBlock"===e)return"/question-blocks";throw Error("Unsupported context type")},o=async({projectId:e})=>{let{data:r}=await s.A.get(`/questions/${e}`);return r.questions},n=async({templateId:e})=>{let{data:r}=await s.A.get(`/template-questions/${e}`);return r.questions},i=async({contextType:e,contextId:r,dataToSend:t,position:o})=>{let n="questionBlock"===e?`${a(e)}`:`${a(e)}/${r}`;if(!t.label||!t.inputType)throw Error("Label and inputType are required");let i=["selectone","selectmany"].includes(t.inputType),l=t.file instanceof File,d=Array.isArray(t.questionOptions)&&t.questionOptions.length>0;if(i&&!l&&!d)throw Error("Options are required for select input types");if(l){let e=new FormData;e.append("label",t.label),e.append("isRequired",t.isRequired?"true":"false"),e.append("inputType",t.inputType),t.hint&&e.append("hint",t.hint),t.placeholder&&e.append("placeholder",t.placeholder),e.append("position",String(o||1)),e.append("file",t.file);try{let{data:r}=await s.A.post(n,e,{headers:{"Content-Type":"multipart/form-data"}});return r}catch(e){throw console.error("Upload error details:",e.response?.data||e.message),Error(`Failed to upload question with file: ${e.response?.data?.message||e.message}`)}}try{let{data:e}=await s.A.post(n,{label:t.label,isRequired:t.isRequired,hint:t.hint,placeholder:t.placeholder,inputType:t.inputType,questionOptions:t.questionOptions,position:o||1});return e}catch(e){throw console.error("API error details:",e.response?.data||e.message),Error(`Failed to add question: ${e.response?.data?.message||e.message}`)}},l=async({contextType:e,id:r,projectId:t})=>{let{data:o}=await s.A.delete(`${a(e)}/${r}?projectId=${t}`);return o},d=async({id:e,contextType:r,contextId:t})=>{let{data:o}=await s.A.post(`${a(r)}/duplicate/${e}?projectId=${t}`,"questionBlock"===r?{}:"project"===r?{projectId:t}:{templateId:t});return o},u=async({id:e,contextType:r,dataToSend:t,contextId:o})=>{let{data:n}=await s.A.patch(`${a(r)}/${e}?projectId=${o}`,t);return n},p=async()=>{try{return(await s.A.get("/question-blocks")).data.questions||[]}catch(e){throw console.error("Error fetching question block questions:",e),e}},c=async({contextType:e,contextId:r,questionPositions:t})=>{if("project"!==e)throw Error("Question position updates are only supported for projects");let o=`${a(e)}/positions?projectId=${r}`;try{let{data:e}=await s.A.patch(o,{questionPositions:t});return e}catch(e){throw console.error("Update failed - Full error:",e),console.error("Update failed - Error details:",{status:e.response?.status,statusText:e.response?.statusText,data:e.response?.data,message:e.message,config:{url:e.config?.url,method:e.config?.method,data:e.config?.data}}),e}},m=async({contextType:e,contextId:r,positionUpdates:t})=>{if("project"!==e)throw Error("Unified position updates are only supported for projects");let o=`${a(e)}/unified-positions?projectId=${r}`;try{let{data:e}=await s.A.patch(o,{positionUpdates:t});return e}catch(e){throw console.error("Unified position update failed - Full error:",e),console.error("Unified position update failed - Error details:",{status:e.response?.status,statusText:e.response?.statusText,data:e.response?.data,message:e.message,config:{url:e.config?.url,method:e.config?.method,data:e.config?.data}}),e}},h=async({projectId:e})=>{let{data:r}=await s.A.get(`/projects/getalldata/${e}`);return r.data}}};