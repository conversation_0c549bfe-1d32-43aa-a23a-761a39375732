(()=>{var e={};e.id=8960,e.ids=[8960],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6654:(e,t,r)=>{Promise.resolve().then(r.bind(r,55573))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20174:(e,t,r)=>{"use strict";r.d(t,{F:()=>n});var s=r(60687),a=r(16189),o=r(85814),i=r.n(o);r(43210);let n=({items:e})=>{let t=(0,a.usePathname)(),r=e=>t.startsWith(e);return(0,s.jsx)("div",{className:"border-y border-neutral-400 rounded-md bg-primary-500 my-4 shadow-md",children:(0,s.jsx)("div",{className:"flex items-center",children:e.map(e=>e.disabled?(0,s.jsxs)("div",{className:"flex items-center gap-2 font-medium transition-all duration-300 p-4 border-b-2 text-gray-400 cursor-not-allowed",children:[e.icon,e.label]},e.route):(0,s.jsxs)(i(),{href:e.route,className:`flex items-center gap-2 font-medium transition-all duration-300 p-4 border-b-2 text-neutral-100 ${r(e.route)?"border-neutral-100":"border-transparent hover:border-neutral-400"}`,children:[e.icon,e.label]},e.route))})})}},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28555:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>y});var s=r(60687),a=r(86429),o=r(58869);let i=(0,r(62688).A)("shield-alert",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"M12 8v4",key:"1got3b"}],["path",{d:"M12 16h.01",key:"1drbdi"}]]);r(43210);var n=r(20174),l=r(77618);let c=()=>{let e=(0,l.c3)(),t=[{label:e("profile"),icon:(0,s.jsx)(o.A,{size:16}),route:"/account/profile"},{label:e("security"),icon:(0,s.jsx)(i,{size:16}),route:"/account/security"}];return(0,s.jsx)(n.F,{items:t})};var d=r(21650),p=r(34091),u=r(29494),m=r(28559),x=r(85814),h=r.n(x);let y=({children:e})=>{let{user:t}=(0,d.A)(),r=(0,l.c3)(),{data:o,isLoading:i,isError:n}=(0,u.I)({queryKey:["profile",t?.id],queryFn:p.l2,enabled:!!t?.id});return i?(0,s.jsx)(a.A,{}):n?(0,s.jsx)("p",{className:"text-red-500",children:r("profileError")}):(0,s.jsxs)("div",{className:"section flex flex-col gap-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("h1",{className:"heading-text capitalize",children:o?.name}),(0,s.jsxs)(h(),{href:"/dashboard",className:"flex items-center gap-2",children:[(0,s.jsx)(m.A,{size:16}),r("backToDashboard")]})]}),(0,s.jsx)(c,{}),(0,s.jsx)("div",{className:"px-8",children:e})]})}},28559:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30852:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>u,tree:()=>c});var s=r(65239),a=r(48088),o=r(88170),i=r.n(o),n=r(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let c={children:["",{children:["[locale]",{children:["(main)",{children:["account",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,76731)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\account\\profile\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,87186)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\account\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,84606)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,72121)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\account\\profile\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/(main)/account/profile/page",pathname:"/[locale]/account/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},33873:e=>{"use strict";e.exports=require("path")},34091:(e,t,r)=>{"use strict";r.d(t,{BZ:()=>o,eg:()=>i,ep:()=>n,kH:()=>l,l2:()=>a});var s=r(12810);let a=async()=>{let{data:e}=await s.A.get("/users/profile");return e.profile},o=async({email:e})=>{let{data:t}=await s.A.patch("/users/change-email",{email:e});return t},i=async({dataToSend:e})=>{let{data:t}=await s.A.patch("/users/update",e);return t},n=async()=>{let{data:e}=await s.A.get("/users/sessions");return e.sessions},l=async e=>{let{data:t}=await s.A.post("/users/sendverificationemail",{email:e});return t}},50964:(e,t,r)=>{Promise.resolve().then(r.bind(r,87186))},55511:e=>{"use strict";e.exports=require("crypto")},55573:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>_});var s=r(60687),a=r(68292),o=r(58869),i=r(11437),n=r(97992),l=r(57800),c=r(62688);let d=(0,c.A)("user-pen",[["path",{d:"M11.5 15H7a4 4 0 0 0-4 4v2",key:"15lzij"}],["path",{d:"M21.378 16.626a1 1 0 0 0-3.004-3.004l-4.01 4.012a2 2 0 0 0-.506.854l-.837 2.87a.5.5 0 0 0 .62.62l2.87-.837a2 2 0 0 0 .854-.506z",key:"1817ys"}],["circle",{cx:"10",cy:"7",r:"4",key:"e45bow"}]]),p=(0,c.A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]);var u=r(43210),m=r(27605),x=r(15566),h=r(32833),y=r(62163),f=r(40480),b=r(29494),g=r(8693),j=r(54050),v=r(34091),N=r(86429),z=r(54864),k=r(19150),A=r(21650),q=r(77618);let _=()=>{let{register:e,formState:{errors:t,isSubmitting:r,isSubmitted:c},reset:_,setValue:C,handleSubmit:P}=(0,m.mN)();(0,u.useEffect)(()=>{e("country",{required:"Please select a country"}),e("sector",{required:"Please select a sector"}),e("organizationType",{required:"Please select an organization type"})},[e]);let[E,w]=(0,u.useState)(null),[M,O]=(0,u.useState)(null),[T,D]=(0,u.useState)(null);(0,u.useEffect)(()=>{C("country",E,{shouldValidate:c}),C("sector",M,{shouldValidate:c}),C("organizationType",T,{shouldValidate:c})},[E,M,T,C]);let{user:K}=(0,A.A)(),F=(0,q.c3)(),{data:U,isLoading:S,isError:H}=(0,b.I)({queryKey:["profile",K?.id],queryFn:v.l2,enabled:!!K?.id});(0,u.useEffect)(()=>{U&&(_({name:U.name||"",country:U.country||"",city:U.city||"",sector:U.sector||"",organizationType:U.organizationType||"",bio:U.bio||""}),w(U.country||null),O(U.sector||null),D(U.organizationType||null))},[U,_]);let G=(0,z.wA)(),I=(0,g.jE)(),V=(0,j.n)({mutationFn:v.eg,onSuccess:()=>{I.invalidateQueries({queryKey:["profile",K?.id]}),G((0,k.Ds)({message:F("profileNotificationSuccess"),type:"success"}))},onError:e=>{G((0,k.Ds)({message:F("profileNotificationUnsucess")+e.message,type:"error"}))}}),$=async e=>{V.mutate({dataToSend:{name:e.name,country:e.country,city:e.city,sector:e.sector,organizationType:e.organizationType,bio:e.bio}})};return S?(0,s.jsx)(N.A,{}):H?(0,s.jsx)("p",{className:"text-red-500",children:F("profileError")}):(0,s.jsxs)("form",{className:"flex flex-col gap-8",onSubmit:P($),children:[(0,s.jsxs)("div",{className:"flex flex-col gap-2 w-full",children:[(0,s.jsx)("h1",{className:"heading-text",children:F("profileInformation")}),(0,s.jsx)("p",{className:"sub-text",children:F("profileUpdateText")})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 tablet:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"label-input-group group",children:[(0,s.jsxs)("label",{htmlFor:"name",className:"label-text",children:[(0,s.jsx)(o.A,{size:16})," ",F("fullName")]}),(0,s.jsx)("input",{...e("name",{required:"Please enter your name"}),id:"name",type:"text",className:"input-field",placeholder:"eg: John Doe"}),t.name&&(0,s.jsx)("p",{className:"text-red-500 text-sm",children:`${t.name.message}`})]}),(0,s.jsxs)("div",{className:"label-input-group group",children:[(0,s.jsxs)("label",{htmlFor:"country",className:"label-text",children:[(0,s.jsx)(i.A,{size:16})," ",F("country")]}),(0,s.jsx)(a.l,{id:"country",options:x,value:E,onChange:w}),t.country&&(0,s.jsx)("p",{className:"text-red-500 text-sm",children:`${t.country.message}`})]}),(0,s.jsxs)("div",{className:"label-input-group group",children:[(0,s.jsxs)("label",{htmlFor:"city",className:"label-text",children:[(0,s.jsx)(n.A,{size:16})," ",F("city")]}),(0,s.jsx)("input",{...e("city"),id:"city",type:"text",className:"input-field",placeholder:"eg: "+F("kathmandu")}),t.city&&(0,s.jsx)("p",{className:"text-red-500 text-sm",children:`${t.city.message}`})]}),(0,s.jsxs)("div",{className:"label-input-group group",children:[(0,s.jsxs)("label",{htmlFor:"sector",className:"label-text",children:[(0,s.jsx)(l.A,{size:16})," ",F("sector")]}),(0,s.jsx)(a.l,{id:"sector",options:Object.values(h.b),value:M&&h.b[M]?h.b[M]:F("selectOption"),onChange:e=>{O((0,f.H)(e,h.b))}}),t.sector&&(0,s.jsx)("p",{className:"text-red-500 text-sm",children:`${t.sector.message}`})]}),(0,s.jsxs)("div",{className:"label-input-group group",children:[(0,s.jsxs)("label",{htmlFor:"organizationType",className:"label-text",children:[(0,s.jsx)(l.A,{size:16})," ",F("organizationType")]}),(0,s.jsx)(a.l,{id:"organizationType",options:Object.values(y.i),value:T&&y.i[T]?y.i[T]:"Select an option",onChange:e=>{D((0,f.H)(e,y.i))}}),t.organizationType&&(0,s.jsx)("p",{className:"text-red-500 text-sm",children:`${t.organizationType.message}`})]}),(0,s.jsxs)("div",{className:"label-input-group group col-span-2",children:[(0,s.jsxs)("label",{htmlFor:"bio",className:"label-text",children:[(0,s.jsx)(d,{size:16})," ",F("bio")]}),(0,s.jsx)("textarea",{...e("bio"),cols:4,className:"input-field resize-none",placeholder:F("bioText")})]})]}),(0,s.jsxs)("button",{type:"submit",className:"btn-primary self-end",children:[(0,s.jsx)(p,{size:18}),r?(0,s.jsxs)("span",{className:"flex items-center gap-2",children:["Saving"," ",(0,s.jsx)("div",{className:"size-4 animate-spin border-x border-neutral-100 rounded-full"})]}):(0,s.jsx)("span",{children:F("save")})]})]})}},55591:e=>{"use strict";e.exports=require("https")},58869:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},62163:(e,t,r)=>{"use strict";r.d(t,{i:()=>s});let s={non_profit_organization:"Non-profit Organization",government_institution:"Government Institution",educational_organization:"Educational Organization",a_commercial_or_for_profit_company:"Commercial / For-profit Company",i_am_not_associated_with_any_organization:"Not Associated with any Organization"}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},76731:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Kobo-project\\\\data-analysis-tool-frontend\\\\app\\\\[locale]\\\\(main)\\\\account\\\\profile\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\account\\profile\\page.tsx","default")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},87186:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Kobo-project\\\\data-analysis-tool-frontend\\\\app\\\\[locale]\\\\(main)\\\\account\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\account\\layout.tsx","default")},87348:(e,t,r)=>{Promise.resolve().then(r.bind(r,28555))},94735:e=>{"use strict";e.exports=require("events")},95030:(e,t,r)=>{Promise.resolve().then(r.bind(r,76731))},97992:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,7404,1658,6560,7618,63,7605,3851,8581,6226,5233],()=>r(30852));module.exports=s})();