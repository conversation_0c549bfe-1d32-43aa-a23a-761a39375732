"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteMultipleLibraryTemplateSchema = exports.LibraryTemplateSchema = void 0;
const zod_1 = require("zod");
const client_1 = require("@prisma/client");
exports.LibraryTemplateSchema = zod_1.z.object({
    name: zod_1.z.string().min(1, "Name is required"),
    description: zod_1.z.string().min(1, "Description is required"),
    sector: zod_1.z.nativeEnum(client_1.Sector),
    country: zod_1.z.string().optional(),
});
exports.deleteMultipleLibraryTemplateSchema = zod_1.z.object({
    templateIds: zod_1.z.array(zod_1.z.number().int().nonnegative()).nonempty({
        message: "At least one template ID is required.",
    }),
});
