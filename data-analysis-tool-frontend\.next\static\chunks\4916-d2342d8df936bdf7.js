(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4916],{675:(e,t,r)=>{"use strict";r.d(t,{R:()=>n});var n=function(e,t){for(var r=arguments.length,n=Array(r>2?r-2:0),o=2;o<r;o++)n[o-2]=arguments[o]}},2348:(e,t,r)=>{"use strict";r.d(t,{W:()=>c});var n=r(12115),o=r(52596),i=r(70788),a=["children","className"];function u(){return(u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var c=n.forwardRef(function(e,t){var r=e.children,c=e.className,l=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,a),s=(0,o.A)("recharts-layer",c);return n.createElement("g",u({className:s},(0,i.J9)(l,!0),{ref:t}),r)})},2494:(e,t,r)=>{"use strict";r.d(t,{s:()=>u});var n=r(3711),o=r.n(n),i=r(40139),a=r.n(i);function u(e,t,r){return!0===t?o()(e,r):a()(t)?o()(e,t):e}},3401:(e,t,r)=>{"use strict";r.d(t,{E:()=>c});var n=r(92418),o=r(83394),i=r(96025),a=r(16238),u=r(83455),c=(0,n.gu)({chartName:"BarChart",GraphicalChild:o.y,defaultTooltipEventType:"axis",validateTooltipEventTypes:["axis","item"],axisComponents:[{axisType:"xAxis",AxisComp:i.W},{axisType:"yAxis",AxisComp:a.h}],formatAxisMap:u.pr})},3562:e=>{e.exports=function(e,t,r,n){for(var o=e.length,i=r+(n?1:-1);n?i--:++i<o;)if(t(e[i],i,e))return i;return -1}},3698:(e,t,r)=>{var n=r(77969),o=r(69363);e.exports=function(e,t){return n(o(e,t),1)}},3711:(e,t,r)=>{var n=r(18028),o=r(65836);e.exports=function(e,t){return e&&e.length?o(e,n(t,2)):[]}},4217:(e,t,r)=>{var n=r(36713),o=/^\s+/;e.exports=function(e){return e?e.slice(0,n(e)+1).replace(o,""):e}},4854:(e,t,r)=>{var n=r(67472),o=r(51911);e.exports=function(e,t,r,i){var a=r.length,u=a,c=!i;if(null==e)return!u;for(e=Object(e);a--;){var l=r[a];if(c&&l[2]?l[1]!==e[l[0]]:!(l[0]in e))return!1}for(;++a<u;){var s=(l=r[a])[0],f=e[s],p=l[1];if(c&&l[2]){if(void 0===f&&!(s in e))return!1}else{var d=new n;if(i)var h=i(f,p,s,e,t,d);if(!(void 0===h?o(p,f,3,i,d):h))return!1}}return!0}},5516:(e,t,r)=>{var n=r(5658);e.exports=function(e,t){var r=n(this,e),o=r.size;return r.set(e,t),this.size+=+(r.size!=o),this}},5658:(e,t,r)=>{var n=r(30699);e.exports=function(e,t){var r=e.__data__;return n(t)?r["string"==typeof t?"string":"hash"]:r.map}},6305:(e,t,r)=>{var n=r(53516),o=r(22471);e.exports=function(e,t){var r=-1,i=o(e)?Array(e.length):[];return n(e,function(e,n,o){i[++r]=t(e,n,o)}),i}},6997:e=>{e.exports=function(e){return this.__data__.set(e,"__lodash_hash_undefined__"),this}},7512:e=>{var t=Function.prototype.toString;e.exports=function(e){if(null!=e){try{return t.call(e)}catch(e){}try{return e+""}catch(e){}}return""}},7548:(e,t,r)=>{var n=r(16746);e.exports=function(e,t){return!!(null==e?0:e.length)&&n(e,t,0)>-1}},7771:(e,t,r)=>{var n=r(31598),o=r(18686),i=r(88748);e.exports=function(e,t){var r=this.__data__;if(r instanceof n){var a=r.__data__;if(!o||a.length<199)return a.push([e,t]),this.size=++r.size,this;r=this.__data__=new i(a)}return r.set(e,t),this.size=r.size,this}},7985:(e,t,r)=>{e.exports="object"==typeof r.g&&r.g&&r.g.Object===Object&&r.g},8782:(e,t,r)=>{"use strict";r.d(t,{r:()=>et});var n=r(92418),o=r(12115),i=r(40139),a=r.n(i),u=r(52596),c=r(2348),l=r(51172),s=r(70788),f=["points","className","baseLinePoints","connectNulls"];function p(){return(p=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function d(e){return function(e){if(Array.isArray(e))return h(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return h(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return h(e,t)}}(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function h(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var y=function(e){return e&&e.x===+e.x&&e.y===+e.y},v=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=[[]];return e.forEach(function(e){y(e)?t[t.length-1].push(e):t[t.length-1].length>0&&t.push([])}),y(e[0])&&t[t.length-1].push(e[0]),t[t.length-1].length<=0&&(t=t.slice(0,-1)),t},m=function(e,t){var r=v(e);t&&(r=[r.reduce(function(e,t){return[].concat(d(e),d(t))},[])]);var n=r.map(function(e){return e.reduce(function(e,t,r){return"".concat(e).concat(0===r?"M":"L").concat(t.x,",").concat(t.y)},"")}).join("");return 1===r.length?"".concat(n,"Z"):n},b=function(e,t,r){var n=m(e,r);return"".concat("Z"===n.slice(-1)?n.slice(0,-1):n,"L").concat(m(t.reverse(),r).slice(1))},g=function(e){var t=e.points,r=e.className,n=e.baseLinePoints,i=e.connectNulls,a=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,f);if(!t||!t.length)return null;var c=(0,u.A)("recharts-polygon",r);if(n&&n.length){var l=a.stroke&&"none"!==a.stroke,d=b(t,n,i);return o.createElement("g",{className:c},o.createElement("path",p({},(0,s.J9)(a,!0),{fill:"Z"===d.slice(-1)?a.fill:"none",stroke:"none",d:d})),l?o.createElement("path",p({},(0,s.J9)(a,!0),{fill:"none",d:m(t,i)})):null,l?o.createElement("path",p({},(0,s.J9)(a,!0),{fill:"none",d:m(n,i)})):null)}var h=m(t,i);return o.createElement("path",p({},(0,s.J9)(a,!0),{fill:"Z"===h.slice(-1)?a.fill:"none",className:c,d:h}))},x=r(79095),w=r(43597),O=r(25641);function j(e){return(j="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function S(){return(S=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function E(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function A(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?E(Object(r),!0).forEach(function(t){_(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):E(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function P(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,C(n.key),n)}}function k(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(k=function(){return!!e})()}function M(e){return(M=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function T(e,t){return(T=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function _(e,t,r){return(t=C(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function C(e){var t=function(e,t){if("object"!=j(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=j(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==j(t)?t:t+""}var D=Math.PI/180,N=function(e){var t,r;function n(){var e,t;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");return e=n,t=arguments,e=M(e),function(e,t){if(t&&("object"===j(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");var r=e;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,k()?Reflect.construct(e,t||[],M(this).constructor):e.apply(this,t))}if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(e&&e.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),e&&T(n,e),t=[{key:"getTickLineCoord",value:function(e){var t=this.props,r=t.cx,n=t.cy,o=t.radius,i=t.orientation,a=t.tickSize,u=(0,O.IZ)(r,n,o,e.coordinate),c=(0,O.IZ)(r,n,o+("inner"===i?-1:1)*(a||8),e.coordinate);return{x1:u.x,y1:u.y,x2:c.x,y2:c.y}}},{key:"getTickTextAnchor",value:function(e){var t=this.props.orientation,r=Math.cos(-e.coordinate*D);return r>1e-5?"outer"===t?"start":"end":r<-1e-5?"outer"===t?"end":"start":"middle"}},{key:"renderAxisLine",value:function(){var e=this.props,t=e.cx,r=e.cy,n=e.radius,i=e.axisLine,a=e.axisLineType,u=A(A({},(0,s.J9)(this.props,!1)),{},{fill:"none"},(0,s.J9)(i,!1));if("circle"===a)return o.createElement(l.c,S({className:"recharts-polar-angle-axis-line"},u,{cx:t,cy:r,r:n}));var c=this.props.ticks.map(function(e){return(0,O.IZ)(t,r,n,e.coordinate)});return o.createElement(g,S({className:"recharts-polar-angle-axis-line"},u,{points:c}))}},{key:"renderTicks",value:function(){var e=this,t=this.props,r=t.ticks,i=t.tick,a=t.tickLine,l=t.tickFormatter,f=t.stroke,p=(0,s.J9)(this.props,!1),d=(0,s.J9)(i,!1),h=A(A({},p),{},{fill:"none"},(0,s.J9)(a,!1)),y=r.map(function(t,r){var s=e.getTickLineCoord(t),y=A(A(A({textAnchor:e.getTickTextAnchor(t)},p),{},{stroke:"none",fill:f},d),{},{index:r,payload:t,x:s.x2,y:s.y2});return o.createElement(c.W,S({className:(0,u.A)("recharts-polar-angle-axis-tick",(0,O.Zk)(i)),key:"tick-".concat(t.coordinate)},(0,w.XC)(e.props,t,r)),a&&o.createElement("line",S({className:"recharts-polar-angle-axis-tick-line"},h,s)),i&&n.renderTickItem(i,y,l?l(t.value,r):t.value))});return o.createElement(c.W,{className:"recharts-polar-angle-axis-ticks"},y)}},{key:"render",value:function(){var e=this.props,t=e.ticks,r=e.radius,n=e.axisLine;return!(r<=0)&&t&&t.length?o.createElement(c.W,{className:(0,u.A)("recharts-polar-angle-axis",this.props.className)},n&&this.renderAxisLine(),this.renderTicks()):null}}],r=[{key:"renderTickItem",value:function(e,t,r){var n;return o.isValidElement(e)?o.cloneElement(e,t):a()(e)?e(t):o.createElement(x.E,S({},t,{className:"recharts-polar-angle-axis-tick-value"}),r)}}],t&&P(n.prototype,t),r&&P(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(o.PureComponent);_(N,"displayName","PolarAngleAxis"),_(N,"axisType","angleAxis"),_(N,"defaultProps",{type:"category",angleAxisId:0,scale:"auto",cx:0,cy:0,orientation:"outer",axisLine:!0,tickLine:!0,tickSize:8,tick:!0,hide:!1,allowDuplicatedCategory:!0});var I=r(83134),R=r.n(I),B=r(14268),L=r.n(B),W=r(60379),F=["cx","cy","angle","ticks","axisLine"],z=["ticks","tick","angle","tickFormatter","stroke"];function U(e){return(U="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function $(){return($=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function Y(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function H(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Y(Object(r),!0).forEach(function(t){K(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Y(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function q(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}function X(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,J(n.key),n)}}function Z(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(Z=function(){return!!e})()}function V(e){return(V=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function G(e,t){return(G=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function K(e,t,r){return(t=J(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function J(e){var t=function(e,t){if("object"!=U(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=U(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==U(t)?t:t+""}var Q=function(e){var t,r;function n(){var e,t;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");return e=n,t=arguments,e=V(e),function(e,t){if(t&&("object"===U(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");var r=e;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,Z()?Reflect.construct(e,t||[],V(this).constructor):e.apply(this,t))}if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(e&&e.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),e&&G(n,e),t=[{key:"getTickValueCoord",value:function(e){var t=e.coordinate,r=this.props,n=r.angle,o=r.cx,i=r.cy;return(0,O.IZ)(o,i,t,n)}},{key:"getTickTextAnchor",value:function(){var e;switch(this.props.orientation){case"left":e="end";break;case"right":e="start";break;default:e="middle"}return e}},{key:"getViewBox",value:function(){var e=this.props,t=e.cx,r=e.cy,n=e.angle,o=e.ticks,i=R()(o,function(e){return e.coordinate||0});return{cx:t,cy:r,startAngle:n,endAngle:n,innerRadius:L()(o,function(e){return e.coordinate||0}).coordinate||0,outerRadius:i.coordinate||0}}},{key:"renderAxisLine",value:function(){var e=this.props,t=e.cx,r=e.cy,n=e.angle,i=e.ticks,a=e.axisLine,u=q(e,F),c=i.reduce(function(e,t){return[Math.min(e[0],t.coordinate),Math.max(e[1],t.coordinate)]},[1/0,-1/0]),l=(0,O.IZ)(t,r,c[0],n),f=(0,O.IZ)(t,r,c[1],n),p=H(H(H({},(0,s.J9)(u,!1)),{},{fill:"none"},(0,s.J9)(a,!1)),{},{x1:l.x,y1:l.y,x2:f.x,y2:f.y});return o.createElement("line",$({className:"recharts-polar-radius-axis-line"},p))}},{key:"renderTicks",value:function(){var e=this,t=this.props,r=t.ticks,i=t.tick,a=t.angle,l=t.tickFormatter,f=t.stroke,p=q(t,z),d=this.getTickTextAnchor(),h=(0,s.J9)(p,!1),y=(0,s.J9)(i,!1),v=r.map(function(t,r){var s=e.getTickValueCoord(t),p=H(H(H(H({textAnchor:d,transform:"rotate(".concat(90-a,", ").concat(s.x,", ").concat(s.y,")")},h),{},{stroke:"none",fill:f},y),{},{index:r},s),{},{payload:t});return o.createElement(c.W,$({className:(0,u.A)("recharts-polar-radius-axis-tick",(0,O.Zk)(i)),key:"tick-".concat(t.coordinate)},(0,w.XC)(e.props,t,r)),n.renderTickItem(i,p,l?l(t.value,r):t.value))});return o.createElement(c.W,{className:"recharts-polar-radius-axis-ticks"},v)}},{key:"render",value:function(){var e=this.props,t=e.ticks,r=e.axisLine,n=e.tick;return t&&t.length?o.createElement(c.W,{className:(0,u.A)("recharts-polar-radius-axis",this.props.className)},r&&this.renderAxisLine(),n&&this.renderTicks(),W.J.renderCallByParent(this.props,this.getViewBox())):null}}],r=[{key:"renderTickItem",value:function(e,t,r){var n;return o.isValidElement(e)?o.cloneElement(e,t):a()(e)?e(t):o.createElement(x.E,$({},t,{className:"recharts-polar-radius-axis-tick-value"}),r)}}],t&&X(n.prototype,t),r&&X(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(o.PureComponent);K(Q,"displayName","PolarRadiusAxis"),K(Q,"axisType","radiusAxis"),K(Q,"defaultProps",{type:"number",radiusAxisId:0,cx:0,cy:0,angle:0,orientation:"right",stroke:"#ccc",axisLine:!0,tick:!0,tickCount:5,allowDataOverflow:!1,scale:"auto",allowDuplicatedCategory:!0});var ee=r(34e3),et=(0,n.gu)({chartName:"PieChart",GraphicalChild:ee.F,validateTooltipEventTypes:["item"],defaultTooltipEventType:"item",legendContent:"children",axisComponents:[{axisType:"angleAxis",AxisComp:N},{axisType:"radiusAxis",AxisComp:Q}],formatAxisMap:O.pr,defaultProps:{layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"}})},8870:function(e,t,r){var n;!function(o){"use strict";var i,a={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},u=!0,c="[DecimalError] ",l=c+"Invalid argument: ",s=c+"Exponent out of range: ",f=Math.floor,p=Math.pow,d=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,h=f(1286742750677284.5),y={};function v(e,t){var r,n,o,i,a,c,l,s,f=e.constructor,p=f.precision;if(!e.s||!t.s)return t.s||(t=new f(e)),u?A(t,p):t;if(l=e.d,s=t.d,a=e.e,o=t.e,l=l.slice(),i=a-o){for(i<0?(n=l,i=-i,c=s.length):(n=s,o=a,c=l.length),i>(c=(a=Math.ceil(p/7))>c?a+1:c+1)&&(i=c,n.length=1),n.reverse();i--;)n.push(0);n.reverse()}for((c=l.length)-(i=s.length)<0&&(i=c,n=s,s=l,l=n),r=0;i;)r=(l[--i]=l[i]+s[i]+r)/1e7|0,l[i]%=1e7;for(r&&(l.unshift(r),++o),c=l.length;0==l[--c];)l.pop();return t.d=l,t.e=o,u?A(t,p):t}function m(e,t,r){if(e!==~~e||e<t||e>r)throw Error(l+e)}function b(e){var t,r,n,o=e.length-1,i="",a=e[0];if(o>0){for(i+=a,t=1;t<o;t++)(r=7-(n=e[t]+"").length)&&(i+=j(r)),i+=n;(r=7-(n=(a=e[t])+"").length)&&(i+=j(r))}else if(0===a)return"0";for(;a%10==0;)a/=10;return i+a}y.absoluteValue=y.abs=function(){var e=new this.constructor(this);return e.s&&(e.s=1),e},y.comparedTo=y.cmp=function(e){var t,r,n,o;if(e=new this.constructor(e),this.s!==e.s)return this.s||-e.s;if(this.e!==e.e)return this.e>e.e^this.s<0?1:-1;for(t=0,r=(n=this.d.length)<(o=e.d.length)?n:o;t<r;++t)if(this.d[t]!==e.d[t])return this.d[t]>e.d[t]^this.s<0?1:-1;return n===o?0:n>o^this.s<0?1:-1},y.decimalPlaces=y.dp=function(){var e=this.d.length-1,t=(e-this.e)*7;if(e=this.d[e])for(;e%10==0;e/=10)t--;return t<0?0:t},y.dividedBy=y.div=function(e){return g(this,new this.constructor(e))},y.dividedToIntegerBy=y.idiv=function(e){var t=this.constructor;return A(g(this,new t(e),0,1),t.precision)},y.equals=y.eq=function(e){return!this.cmp(e)},y.exponent=function(){return w(this)},y.greaterThan=y.gt=function(e){return this.cmp(e)>0},y.greaterThanOrEqualTo=y.gte=function(e){return this.cmp(e)>=0},y.isInteger=y.isint=function(){return this.e>this.d.length-2},y.isNegative=y.isneg=function(){return this.s<0},y.isPositive=y.ispos=function(){return this.s>0},y.isZero=function(){return 0===this.s},y.lessThan=y.lt=function(e){return 0>this.cmp(e)},y.lessThanOrEqualTo=y.lte=function(e){return 1>this.cmp(e)},y.logarithm=y.log=function(e){var t,r=this.constructor,n=r.precision,o=n+5;if(void 0===e)e=new r(10);else if((e=new r(e)).s<1||e.eq(i))throw Error(c+"NaN");if(this.s<1)throw Error(c+(this.s?"NaN":"-Infinity"));return this.eq(i)?new r(0):(u=!1,t=g(S(this,o),S(e,o),o),u=!0,A(t,n))},y.minus=y.sub=function(e){return e=new this.constructor(e),this.s==e.s?P(this,e):v(this,(e.s=-e.s,e))},y.modulo=y.mod=function(e){var t,r=this.constructor,n=r.precision;if(!(e=new r(e)).s)throw Error(c+"NaN");return this.s?(u=!1,t=g(this,e,0,1).times(e),u=!0,this.minus(t)):A(new r(this),n)},y.naturalExponential=y.exp=function(){return x(this)},y.naturalLogarithm=y.ln=function(){return S(this)},y.negated=y.neg=function(){var e=new this.constructor(this);return e.s=-e.s||0,e},y.plus=y.add=function(e){return e=new this.constructor(e),this.s==e.s?v(this,e):P(this,(e.s=-e.s,e))},y.precision=y.sd=function(e){var t,r,n;if(void 0!==e&&!!e!==e&&1!==e&&0!==e)throw Error(l+e);if(t=w(this)+1,r=7*(n=this.d.length-1)+1,n=this.d[n]){for(;n%10==0;n/=10)r--;for(n=this.d[0];n>=10;n/=10)r++}return e&&t>r?t:r},y.squareRoot=y.sqrt=function(){var e,t,r,n,o,i,a,l=this.constructor;if(this.s<1){if(!this.s)return new l(0);throw Error(c+"NaN")}for(e=w(this),u=!1,0==(o=Math.sqrt(+this))||o==1/0?(((t=b(this.d)).length+e)%2==0&&(t+="0"),o=Math.sqrt(t),e=f((e+1)/2)-(e<0||e%2),n=new l(t=o==1/0?"5e"+e:(t=o.toExponential()).slice(0,t.indexOf("e")+1)+e)):n=new l(o.toString()),o=a=(r=l.precision)+3;;)if(n=(i=n).plus(g(this,i,a+2)).times(.5),b(i.d).slice(0,a)===(t=b(n.d)).slice(0,a)){if(t=t.slice(a-3,a+1),o==a&&"4999"==t){if(A(i,r+1,0),i.times(i).eq(this)){n=i;break}}else if("9999"!=t)break;a+=4}return u=!0,A(n,r)},y.times=y.mul=function(e){var t,r,n,o,i,a,c,l,s,f=this.constructor,p=this.d,d=(e=new f(e)).d;if(!this.s||!e.s)return new f(0);for(e.s*=this.s,r=this.e+e.e,(l=p.length)<(s=d.length)&&(i=p,p=d,d=i,a=l,l=s,s=a),i=[],n=a=l+s;n--;)i.push(0);for(n=s;--n>=0;){for(t=0,o=l+n;o>n;)c=i[o]+d[n]*p[o-n-1]+t,i[o--]=c%1e7|0,t=c/1e7|0;i[o]=(i[o]+t)%1e7|0}for(;!i[--a];)i.pop();return t?++r:i.shift(),e.d=i,e.e=r,u?A(e,f.precision):e},y.toDecimalPlaces=y.todp=function(e,t){var r=this,n=r.constructor;return(r=new n(r),void 0===e)?r:(m(e,0,1e9),void 0===t?t=n.rounding:m(t,0,8),A(r,e+w(r)+1,t))},y.toExponential=function(e,t){var r,n=this,o=n.constructor;return void 0===e?r=k(n,!0):(m(e,0,1e9),void 0===t?t=o.rounding:m(t,0,8),r=k(n=A(new o(n),e+1,t),!0,e+1)),r},y.toFixed=function(e,t){var r,n,o=this.constructor;return void 0===e?k(this):(m(e,0,1e9),void 0===t?t=o.rounding:m(t,0,8),r=k((n=A(new o(this),e+w(this)+1,t)).abs(),!1,e+w(n)+1),this.isneg()&&!this.isZero()?"-"+r:r)},y.toInteger=y.toint=function(){var e=this.constructor;return A(new e(this),w(this)+1,e.rounding)},y.toNumber=function(){return+this},y.toPower=y.pow=function(e){var t,r,n,o,a,l,s=this,p=s.constructor,d=+(e=new p(e));if(!e.s)return new p(i);if(!(s=new p(s)).s){if(e.s<1)throw Error(c+"Infinity");return s}if(s.eq(i))return s;if(n=p.precision,e.eq(i))return A(s,n);if(l=(t=e.e)>=(r=e.d.length-1),a=s.s,l){if((r=d<0?-d:d)<=0x1fffffffffffff){for(o=new p(i),t=Math.ceil(n/7+4),u=!1;r%2&&M((o=o.times(s)).d,t),0!==(r=f(r/2));)M((s=s.times(s)).d,t);return u=!0,e.s<0?new p(i).div(o):A(o,n)}}else if(a<0)throw Error(c+"NaN");return a=a<0&&1&e.d[Math.max(t,r)]?-1:1,s.s=1,u=!1,o=e.times(S(s,n+12)),u=!0,(o=x(o)).s=a,o},y.toPrecision=function(e,t){var r,n,o=this,i=o.constructor;return void 0===e?(r=w(o),n=k(o,r<=i.toExpNeg||r>=i.toExpPos)):(m(e,1,1e9),void 0===t?t=i.rounding:m(t,0,8),r=w(o=A(new i(o),e,t)),n=k(o,e<=r||r<=i.toExpNeg,e)),n},y.toSignificantDigits=y.tosd=function(e,t){var r=this.constructor;return void 0===e?(e=r.precision,t=r.rounding):(m(e,1,1e9),void 0===t?t=r.rounding:m(t,0,8)),A(new r(this),e,t)},y.toString=y.valueOf=y.val=y.toJSON=function(){var e=w(this),t=this.constructor;return k(this,e<=t.toExpNeg||e>=t.toExpPos)};var g=function(){function e(e,t){var r,n=0,o=e.length;for(e=e.slice();o--;)r=e[o]*t+n,e[o]=r%1e7|0,n=r/1e7|0;return n&&e.unshift(n),e}function t(e,t,r,n){var o,i;if(r!=n)i=r>n?1:-1;else for(o=i=0;o<r;o++)if(e[o]!=t[o]){i=e[o]>t[o]?1:-1;break}return i}function r(e,t,r){for(var n=0;r--;)e[r]-=n,n=+(e[r]<t[r]),e[r]=1e7*n+e[r]-t[r];for(;!e[0]&&e.length>1;)e.shift()}return function(n,o,i,a){var u,l,s,f,p,d,h,y,v,m,b,g,x,O,j,S,E,P,k=n.constructor,M=n.s==o.s?1:-1,T=n.d,_=o.d;if(!n.s)return new k(n);if(!o.s)throw Error(c+"Division by zero");for(s=0,l=n.e-o.e,E=_.length,j=T.length,y=(h=new k(M)).d=[];_[s]==(T[s]||0);)++s;if(_[s]>(T[s]||0)&&--l,(g=null==i?i=k.precision:a?i+(w(n)-w(o))+1:i)<0)return new k(0);if(g=g/7+2|0,s=0,1==E)for(f=0,_=_[0],g++;(s<j||f)&&g--;s++)x=1e7*f+(T[s]||0),y[s]=x/_|0,f=x%_|0;else{for((f=1e7/(_[0]+1)|0)>1&&(_=e(_,f),T=e(T,f),E=_.length,j=T.length),O=E,m=(v=T.slice(0,E)).length;m<E;)v[m++]=0;(P=_.slice()).unshift(0),S=_[0],_[1]>=1e7/2&&++S;do f=0,(u=t(_,v,E,m))<0?(b=v[0],E!=m&&(b=1e7*b+(v[1]||0)),(f=b/S|0)>1?(f>=1e7&&(f=1e7-1),d=(p=e(_,f)).length,m=v.length,1==(u=t(p,v,d,m))&&(f--,r(p,E<d?P:_,d))):(0==f&&(u=f=1),p=_.slice()),(d=p.length)<m&&p.unshift(0),r(v,p,m),-1==u&&(m=v.length,(u=t(_,v,E,m))<1&&(f++,r(v,E<m?P:_,m))),m=v.length):0===u&&(f++,v=[0]),y[s++]=f,u&&v[0]?v[m++]=T[O]||0:(v=[T[O]],m=1);while((O++<j||void 0!==v[0])&&g--)}return y[0]||y.shift(),h.e=l,A(h,a?i+w(h)+1:i)}}();function x(e,t){var r,n,o,a,c,l=0,f=0,d=e.constructor,h=d.precision;if(w(e)>16)throw Error(s+w(e));if(!e.s)return new d(i);for(null==t?(u=!1,c=h):c=t,a=new d(.03125);e.abs().gte(.1);)e=e.times(a),f+=5;for(c+=Math.log(p(2,f))/Math.LN10*2+5|0,r=n=o=new d(i),d.precision=c;;){if(n=A(n.times(e),c),r=r.times(++l),b((a=o.plus(g(n,r,c))).d).slice(0,c)===b(o.d).slice(0,c)){for(;f--;)o=A(o.times(o),c);return d.precision=h,null==t?(u=!0,A(o,h)):o}o=a}}function w(e){for(var t=7*e.e,r=e.d[0];r>=10;r/=10)t++;return t}function O(e,t,r){if(t>e.LN10.sd())throw u=!0,r&&(e.precision=r),Error(c+"LN10 precision limit exceeded");return A(new e(e.LN10),t)}function j(e){for(var t="";e--;)t+="0";return t}function S(e,t){var r,n,o,a,l,s,f,p,d,h=1,y=e,v=y.d,m=y.constructor,x=m.precision;if(y.s<1)throw Error(c+(y.s?"NaN":"-Infinity"));if(y.eq(i))return new m(0);if(null==t?(u=!1,p=x):p=t,y.eq(10))return null==t&&(u=!0),O(m,p);if(m.precision=p+=10,n=(r=b(v)).charAt(0),!(15e14>Math.abs(a=w(y))))return f=O(m,p+2,x).times(a+""),y=S(new m(n+"."+r.slice(1)),p-10).plus(f),m.precision=x,null==t?(u=!0,A(y,x)):y;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=b((y=y.times(e)).d)).charAt(0),h++;for(a=w(y),n>1?(y=new m("0."+r),a++):y=new m(n+"."+r.slice(1)),s=l=y=g(y.minus(i),y.plus(i),p),d=A(y.times(y),p),o=3;;){if(l=A(l.times(d),p),b((f=s.plus(g(l,new m(o),p))).d).slice(0,p)===b(s.d).slice(0,p))return s=s.times(2),0!==a&&(s=s.plus(O(m,p+2,x).times(a+""))),s=g(s,new m(h),p),m.precision=x,null==t?(u=!0,A(s,x)):s;s=f,o+=2}}function E(e,t){var r,n,o;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;48===t.charCodeAt(n);)++n;for(o=t.length;48===t.charCodeAt(o-1);)--o;if(t=t.slice(n,o)){if(o-=n,e.e=f((r=r-n-1)/7),e.d=[],n=(r+1)%7,r<0&&(n+=7),n<o){for(n&&e.d.push(+t.slice(0,n)),o-=7;n<o;)e.d.push(+t.slice(n,n+=7));n=7-(t=t.slice(n)).length}else n-=o;for(;n--;)t+="0";if(e.d.push(+t),u&&(e.e>h||e.e<-h))throw Error(s+r)}else e.s=0,e.e=0,e.d=[0];return e}function A(e,t,r){var n,o,i,a,c,l,d,y,v=e.d;for(a=1,i=v[0];i>=10;i/=10)a++;if((n=t-a)<0)n+=7,o=t,d=v[y=0];else{if((y=Math.ceil((n+1)/7))>=(i=v.length))return e;for(a=1,d=i=v[y];i>=10;i/=10)a++;n%=7,o=n-7+a}if(void 0!==r&&(c=d/(i=p(10,a-o-1))%10|0,l=t<0||void 0!==v[y+1]||d%i,l=r<4?(c||l)&&(0==r||r==(e.s<0?3:2)):c>5||5==c&&(4==r||l||6==r&&(n>0?o>0?d/p(10,a-o):0:v[y-1])%10&1||r==(e.s<0?8:7))),t<1||!v[0])return l?(i=w(e),v.length=1,t=t-i-1,v[0]=p(10,(7-t%7)%7),e.e=f(-t/7)||0):(v.length=1,v[0]=e.e=e.s=0),e;if(0==n?(v.length=y,i=1,y--):(v.length=y+1,i=p(10,7-n),v[y]=o>0?(d/p(10,a-o)%p(10,o)|0)*i:0),l)for(;;)if(0==y){1e7==(v[0]+=i)&&(v[0]=1,++e.e);break}else{if(v[y]+=i,1e7!=v[y])break;v[y--]=0,i=1}for(n=v.length;0===v[--n];)v.pop();if(u&&(e.e>h||e.e<-h))throw Error(s+w(e));return e}function P(e,t){var r,n,o,i,a,c,l,s,f,p,d=e.constructor,h=d.precision;if(!e.s||!t.s)return t.s?t.s=-t.s:t=new d(e),u?A(t,h):t;if(l=e.d,p=t.d,n=t.e,s=e.e,l=l.slice(),a=s-n){for((f=a<0)?(r=l,a=-a,c=p.length):(r=p,n=s,c=l.length),a>(o=Math.max(Math.ceil(h/7),c)+2)&&(a=o,r.length=1),r.reverse(),o=a;o--;)r.push(0);r.reverse()}else{for((f=(o=l.length)<(c=p.length))&&(c=o),o=0;o<c;o++)if(l[o]!=p[o]){f=l[o]<p[o];break}a=0}for(f&&(r=l,l=p,p=r,t.s=-t.s),c=l.length,o=p.length-c;o>0;--o)l[c++]=0;for(o=p.length;o>a;){if(l[--o]<p[o]){for(i=o;i&&0===l[--i];)l[i]=1e7-1;--l[i],l[o]+=1e7}l[o]-=p[o]}for(;0===l[--c];)l.pop();for(;0===l[0];l.shift())--n;return l[0]?(t.d=l,t.e=n,u?A(t,h):t):new d(0)}function k(e,t,r){var n,o=w(e),i=b(e.d),a=i.length;return t?(r&&(n=r-a)>0?i=i.charAt(0)+"."+i.slice(1)+j(n):a>1&&(i=i.charAt(0)+"."+i.slice(1)),i=i+(o<0?"e":"e+")+o):o<0?(i="0."+j(-o-1)+i,r&&(n=r-a)>0&&(i+=j(n))):o>=a?(i+=j(o+1-a),r&&(n=r-o-1)>0&&(i=i+"."+j(n))):((n=o+1)<a&&(i=i.slice(0,n)+"."+i.slice(n)),r&&(n=r-a)>0&&(o+1===a&&(i+="."),i+=j(n))),e.s<0?"-"+i:i}function M(e,t){if(e.length>t)return e.length=t,!0}function T(e){if(!e||"object"!=typeof e)throw Error(c+"Object expected");var t,r,n,o=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(t=0;t<o.length;t+=3)if(void 0!==(n=e[r=o[t]]))if(f(n)===n&&n>=o[t+1]&&n<=o[t+2])this[r]=n;else throw Error(l+r+": "+n);if(void 0!==(n=e[r="LN10"]))if(n==Math.LN10)this[r]=new this(n);else throw Error(l+r+": "+n);return this}(a=function e(t){var r,n,o;function i(e){if(!(this instanceof i))return new i(e);if(this.constructor=i,e instanceof i){this.s=e.s,this.e=e.e,this.d=(e=e.d)?e.slice():e;return}if("number"==typeof e){if(0*e!=0)throw Error(l+e);if(e>0)this.s=1;else if(e<0)e=-e,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(e===~~e&&e<1e7){this.e=0,this.d=[e];return}return E(this,e.toString())}if("string"!=typeof e)throw Error(l+e);if(45===e.charCodeAt(0)?(e=e.slice(1),this.s=-1):this.s=1,d.test(e))E(this,e);else throw Error(l+e)}if(i.prototype=y,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.clone=e,i.config=i.set=T,void 0===t&&(t={}),t)for(r=0,o=["precision","rounding","toExpNeg","toExpPos","LN10"];r<o.length;)t.hasOwnProperty(n=o[r++])||(t[n]=this[n]);return i.config(t),i}(a)).default=a.Decimal=a,i=new a(1),void 0===(n=(function(){return a}).call(t,r,t,e))||(e.exports=n)}(0)},9557:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>eS});var n=r(12115),o=r(38637),i=r.n(o),a=Object.getOwnPropertyNames,u=Object.getOwnPropertySymbols,c=Object.prototype.hasOwnProperty;function l(e,t){return function(r,n,o){return e(r,n,o)&&t(r,n,o)}}function s(e){return function(t,r,n){if(!t||!r||"object"!=typeof t||"object"!=typeof r)return e(t,r,n);var o=n.cache,i=o.get(t),a=o.get(r);if(i&&a)return i===r&&a===t;o.set(t,r),o.set(r,t);var u=e(t,r,n);return o.delete(t),o.delete(r),u}}function f(e){return a(e).concat(u(e))}var p=Object.hasOwn||function(e,t){return c.call(e,t)};function d(e,t){return e===t||!e&&!t&&e!=e&&t!=t}var h=Object.getOwnPropertyDescriptor,y=Object.keys;function v(e,t,r){var n=e.length;if(t.length!==n)return!1;for(;n-- >0;)if(!r.equals(e[n],t[n],n,n,e,t,r))return!1;return!0}function m(e,t){return d(e.getTime(),t.getTime())}function b(e,t){return e.name===t.name&&e.message===t.message&&e.cause===t.cause&&e.stack===t.stack}function g(e,t){return e===t}function x(e,t,r){var n,o,i=e.size;if(i!==t.size)return!1;if(!i)return!0;for(var a=Array(i),u=e.entries(),c=0;(n=u.next())&&!n.done;){for(var l=t.entries(),s=!1,f=0;(o=l.next())&&!o.done;){if(a[f]){f++;continue}var p=n.value,d=o.value;if(r.equals(p[0],d[0],c,f,e,t,r)&&r.equals(p[1],d[1],p[0],d[0],e,t,r)){s=a[f]=!0;break}f++}if(!s)return!1;c++}return!0}function w(e,t,r){var n=y(e),o=n.length;if(y(t).length!==o)return!1;for(;o-- >0;)if(!k(e,t,r,n[o]))return!1;return!0}function O(e,t,r){var n,o,i,a=f(e),u=a.length;if(f(t).length!==u)return!1;for(;u-- >0;)if(!k(e,t,r,n=a[u])||(o=h(e,n),i=h(t,n),(o||i)&&(!o||!i||o.configurable!==i.configurable||o.enumerable!==i.enumerable||o.writable!==i.writable)))return!1;return!0}function j(e,t){return d(e.valueOf(),t.valueOf())}function S(e,t){return e.source===t.source&&e.flags===t.flags}function E(e,t,r){var n,o,i=e.size;if(i!==t.size)return!1;if(!i)return!0;for(var a=Array(i),u=e.values();(n=u.next())&&!n.done;){for(var c=t.values(),l=!1,s=0;(o=c.next())&&!o.done;){if(!a[s]&&r.equals(n.value,o.value,n.value,o.value,e,t,r)){l=a[s]=!0;break}s++}if(!l)return!1}return!0}function A(e,t){var r=e.length;if(t.length!==r)return!1;for(;r-- >0;)if(e[r]!==t[r])return!1;return!0}function P(e,t){return e.hostname===t.hostname&&e.pathname===t.pathname&&e.protocol===t.protocol&&e.port===t.port&&e.hash===t.hash&&e.username===t.username&&e.password===t.password}function k(e,t,r,n){return("_owner"===n||"__o"===n||"__v"===n)&&(!!e.$$typeof||!!t.$$typeof)||p(t,n)&&r.equals(e[n],t[n],n,n,e,t,r)}var M=Array.isArray,T="function"==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView:null,_=Object.assign,C=Object.prototype.toString.call.bind(Object.prototype.toString),D=N();function N(e){void 0===e&&(e={});var t,r,n,o,i,a,u,c,f,p,h,y,k,D=e.circular,N=e.createInternalComparator,I=e.createState,R=e.strict,B=(r=(t=function(e){var t=e.circular,r=e.createCustomConfig,n=e.strict,o={areArraysEqual:n?O:v,areDatesEqual:m,areErrorsEqual:b,areFunctionsEqual:g,areMapsEqual:n?l(x,O):x,areNumbersEqual:d,areObjectsEqual:n?O:w,arePrimitiveWrappersEqual:j,areRegExpsEqual:S,areSetsEqual:n?l(E,O):E,areTypedArraysEqual:n?O:A,areUrlsEqual:P};if(r&&(o=_({},o,r(o))),t){var i=s(o.areArraysEqual),a=s(o.areMapsEqual),u=s(o.areObjectsEqual),c=s(o.areSetsEqual);o=_({},o,{areArraysEqual:i,areMapsEqual:a,areObjectsEqual:u,areSetsEqual:c})}return o}(e)).areArraysEqual,n=t.areDatesEqual,o=t.areErrorsEqual,i=t.areFunctionsEqual,a=t.areMapsEqual,u=t.areNumbersEqual,c=t.areObjectsEqual,f=t.arePrimitiveWrappersEqual,p=t.areRegExpsEqual,h=t.areSetsEqual,y=t.areTypedArraysEqual,k=t.areUrlsEqual,function(e,t,l){if(e===t)return!0;if(null==e||null==t)return!1;var s=typeof e;if(s!==typeof t)return!1;if("object"!==s)return"number"===s?u(e,t,l):"function"===s&&i(e,t,l);var d=e.constructor;if(d!==t.constructor)return!1;if(d===Object)return c(e,t,l);if(M(e))return r(e,t,l);if(null!=T&&T(e))return y(e,t,l);if(d===Date)return n(e,t,l);if(d===RegExp)return p(e,t,l);if(d===Map)return a(e,t,l);if(d===Set)return h(e,t,l);var v=C(e);return"[object Date]"===v?n(e,t,l):"[object RegExp]"===v?p(e,t,l):"[object Map]"===v?a(e,t,l):"[object Set]"===v?h(e,t,l):"[object Object]"===v?"function"!=typeof e.then&&"function"!=typeof t.then&&c(e,t,l):"[object URL]"===v?k(e,t,l):"[object Error]"===v?o(e,t,l):"[object Arguments]"===v?c(e,t,l):("[object Boolean]"===v||"[object Number]"===v||"[object String]"===v)&&f(e,t,l)}),L=N?N(B):function(e,t,r,n,o,i,a){return B(e,t,a)};return function(e){var t=e.circular,r=e.comparator,n=e.createState,o=e.equals,i=e.strict;if(n)return function(e,a){var u=n(),c=u.cache;return r(e,a,{cache:void 0===c?t?new WeakMap:void 0:c,equals:o,meta:u.meta,strict:i})};if(t)return function(e,t){return r(e,t,{cache:new WeakMap,equals:o,meta:void 0,strict:i})};var a={cache:void 0,equals:o,meta:void 0,strict:i};return function(e,t){return r(e,t,a)}}({circular:void 0!==D&&D,comparator:B,createState:I,equals:L,strict:void 0!==R&&R})}function I(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=-1;requestAnimationFrame(function n(o){if(r<0&&(r=o),o-r>t)e(o),r=-1;else{var i;i=n,"undefined"!=typeof requestAnimationFrame&&requestAnimationFrame(i)}})}function R(e){return(R="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function B(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function L(e){return(L="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function W(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function F(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?W(Object(r),!0).forEach(function(t){z(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):W(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function z(e,t,r){var n;return(n=function(e,t){if("object"!==L(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==L(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"===L(n)?n:String(n))in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}N({strict:!0}),N({circular:!0}),N({circular:!0,strict:!0}),N({createInternalComparator:function(){return d}}),N({strict:!0,createInternalComparator:function(){return d}}),N({circular:!0,createInternalComparator:function(){return d}}),N({circular:!0,createInternalComparator:function(){return d},strict:!0});var U=function(e){return e},$=function(e,t){return Object.keys(t).reduce(function(r,n){return F(F({},r),{},z({},n,e(n,t[n])))},{})},Y=function(e,t,r){return e.map(function(e){return"".concat(e.replace(/([A-Z])/g,function(e){return"-".concat(e.toLowerCase())})," ").concat(t,"ms ").concat(r)}).join(",")},H=function(e,t,r,n,o,i,a,u){};function q(e,t){if(e){if("string"==typeof e)return X(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return X(e,t)}}function X(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var Z=function(e,t){return[0,3*e,3*t-6*e,3*e-3*t+1]},V=function(e,t){return e.map(function(e,r){return e*Math.pow(t,r)}).reduce(function(e,t){return e+t})},G=function(e,t){return function(r){return V(Z(e,t),r)}},K=function(){for(var e,t,r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];var i=n[0],a=n[1],u=n[2],c=n[3];if(1===n.length)switch(n[0]){case"linear":i=0,a=0,u=1,c=1;break;case"ease":i=.25,a=.1,u=.25,c=1;break;case"ease-in":i=.42,a=0,u=1,c=1;break;case"ease-out":i=.42,a=0,u=.58,c=1;break;case"ease-in-out":i=0,a=0,u=.58,c=1;break;default:var l=n[0].split("(");if("cubic-bezier"===l[0]&&4===l[1].split(")")[0].split(",").length){var s,f=function(e){if(Array.isArray(e))return e}(s=l[1].split(")")[0].split(",").map(function(e){return parseFloat(e)}))||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{i=(r=r.call(e)).next,!1;for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(e){l=!0,o=e}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(s,4)||q(s,4)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();i=f[0],a=f[1],u=f[2],c=f[3]}else H(!1,"[configBezier]: arguments should be one of oneOf 'linear', 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', instead received %s",n)}H([i,u,a,c].every(function(e){return"number"==typeof e&&e>=0&&e<=1}),"[configBezier]: arguments should be x1, y1, x2, y2 of [0, 1] instead received %s",n);var p=G(i,u),d=G(a,c),h=(e=i,t=u,function(r){var n;return V([].concat(function(e){if(Array.isArray(e))return X(e)}(n=Z(e,t).map(function(e,t){return e*t}).slice(1))||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(n)||q(n)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),[0]),r)}),y=function(e){for(var t=e>1?1:e,r=t,n=0;n<8;++n){var o,i=p(r)-t,a=h(r);if(1e-4>Math.abs(i-t)||a<1e-4)break;r=(o=r-i/a)>1?1:o<0?0:o}return d(r)};return y.isStepper=!1,y},J=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.stiff,r=void 0===t?100:t,n=e.damping,o=void 0===n?8:n,i=e.dt,a=void 0===i?17:i,u=function(e,t,n){var i=n+(-(e-t)*r-n*o)*a/1e3,u=n*a/1e3+e;return 1e-4>Math.abs(u-t)&&1e-4>Math.abs(i)?[t,0]:[u,i]};return u.isStepper=!0,u.dt=a,u},Q=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=t[0];if("string"==typeof n)switch(n){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return K(n);case"spring":return J();default:if("cubic-bezier"===n.split("(")[0])return K(n);H(!1,"[configEasing]: first argument should be one of 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', 'linear' and 'spring', instead  received %s",t)}return"function"==typeof n?n:(H(!1,"[configEasing]: first argument type should be function or string, instead received %s",t),null)};function ee(e){return(ee="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function et(e){return function(e){if(Array.isArray(e))return ea(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||ei(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function er(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function en(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?er(Object(r),!0).forEach(function(t){eo(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):er(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function eo(e,t,r){var n;return(n=function(e,t){if("object"!==ee(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==ee(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"===ee(n)?n:String(n))in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ei(e,t){if(e){if("string"==typeof e)return ea(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ea(e,t)}}function ea(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var eu=function(e,t,r){return e+(t-e)*r},ec=function(e){return e.from!==e.to},el=function e(t,r,n){var o=$(function(e,r){if(ec(r)){var n,o=function(e){if(Array.isArray(e))return e}(n=t(r.from,r.to,r.velocity))||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{i=(r=r.call(e)).next,!1;for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(e){l=!0,o=e}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(n,2)||ei(n,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],a=o[1];return en(en({},r),{},{from:i,velocity:a})}return r},r);return n<1?$(function(e,t){return ec(t)?en(en({},t),{},{velocity:eu(t.velocity,o[e].velocity,n),from:eu(t.from,o[e].from,n)}):t},r):e(t,o,n-1)};let es=function(e,t,r,n,o){var i,a,u=[Object.keys(e),Object.keys(t)].reduce(function(e,t){return e.filter(function(e){return t.includes(e)})}),c=u.reduce(function(r,n){return en(en({},r),{},eo({},n,[e[n],t[n]]))},{}),l=u.reduce(function(r,n){return en(en({},r),{},eo({},n,{from:e[n],velocity:0,to:t[n]}))},{}),s=-1,f=function(){return null};return f=r.isStepper?function(n){i||(i=n);var a=(n-i)/r.dt;l=el(r,l,a),o(en(en(en({},e),t),$(function(e,t){return t.from},l))),i=n,Object.values(l).filter(ec).length&&(s=requestAnimationFrame(f))}:function(i){a||(a=i);var u=(i-a)/n,l=$(function(e,t){return eu.apply(void 0,et(t).concat([r(u)]))},c);if(o(en(en(en({},e),t),l)),u<1)s=requestAnimationFrame(f);else{var p=$(function(e,t){return eu.apply(void 0,et(t).concat([r(1)]))},c);o(en(en(en({},e),t),p))}},function(){return requestAnimationFrame(f),function(){cancelAnimationFrame(s)}}};function ef(e){return(ef="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var ep=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function ed(e){return function(e){if(Array.isArray(e))return eh(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return eh(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return eh(e,t)}}(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function eh(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function ey(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function ev(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ey(Object(r),!0).forEach(function(t){em(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ey(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function em(e,t,r){return(t=eb(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function eb(e){var t=function(e,t){if("object"!==ef(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==ef(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===ef(t)?t:String(t)}function eg(e,t){return(eg=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function ex(e,t){if(t&&("object"===ef(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return ew(e)}function ew(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function eO(e){return(eO=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var ej=function(e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");i.prototype=Object.create(e&&e.prototype,{constructor:{value:i,writable:!0,configurable:!0}}),Object.defineProperty(i,"prototype",{writable:!1}),e&&eg(i,e);var t,r,o=(t=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}(),function(){var e,r=eO(i);return e=t?Reflect.construct(r,arguments,eO(this).constructor):r.apply(this,arguments),ex(this,e)});function i(e,t){if(!(this instanceof i))throw TypeError("Cannot call a class as a function");var r=o.call(this,e,t),n=r.props,a=n.isActive,u=n.attributeName,c=n.from,l=n.to,s=n.steps,f=n.children,p=n.duration;if(r.handleStyleChange=r.handleStyleChange.bind(ew(r)),r.changeStyle=r.changeStyle.bind(ew(r)),!a||p<=0)return r.state={style:{}},"function"==typeof f&&(r.state={style:l}),ex(r);if(s&&s.length)r.state={style:s[0].style};else if(c){if("function"==typeof f)return r.state={style:c},ex(r);r.state={style:u?em({},u,c):c}}else r.state={style:{}};return r}return r=[{key:"componentDidMount",value:function(){var e=this.props,t=e.isActive,r=e.canBegin;this.mounted=!0,t&&r&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(e){var t=this.props,r=t.isActive,n=t.canBegin,o=t.attributeName,i=t.shouldReAnimate,a=t.to,u=t.from,c=this.state.style;if(n){if(!r){var l={style:o?em({},o,a):a};this.state&&c&&(o&&c[o]!==a||!o&&c!==a)&&this.setState(l);return}if(!D(e.to,a)||!e.canBegin||!e.isActive){var s=!e.canBegin||!e.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var f=s||i?u:e.to;if(this.state&&c){var p={style:o?em({},o,f):f};(o&&c[o]!==f||!o&&c!==f)&&this.setState(p)}this.runAnimation(ev(ev({},this.props),{},{from:f,begin:0}))}}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var e=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),e&&e()}},{key:"handleStyleChange",value:function(e){this.changeStyle(e)}},{key:"changeStyle",value:function(e){this.mounted&&this.setState({style:e})}},{key:"runJSAnimation",value:function(e){var t=this,r=e.from,n=e.to,o=e.duration,i=e.easing,a=e.begin,u=e.onAnimationEnd,c=e.onAnimationStart,l=es(r,n,Q(i),o,this.changeStyle);this.manager.start([c,a,function(){t.stopJSAnimation=l()},o,u])}},{key:"runStepAnimation",value:function(e){var t=this,r=e.steps,n=e.begin,o=e.onAnimationStart,i=r[0],a=i.style,u=i.duration;return this.manager.start([o].concat(ed(r.reduce(function(e,n,o){if(0===o)return e;var i=n.duration,a=n.easing,u=void 0===a?"ease":a,c=n.style,l=n.properties,s=n.onAnimationEnd,f=o>0?r[o-1]:n,p=l||Object.keys(c);if("function"==typeof u||"spring"===u)return[].concat(ed(e),[t.runJSAnimation.bind(t,{from:f.style,to:c,duration:i,easing:u}),i]);var d=Y(p,i,u),h=ev(ev(ev({},f.style),c),{},{transition:d});return[].concat(ed(e),[h,i,s]).filter(U)},[a,Math.max(void 0===u?0:u,n)])),[e.onAnimationEnd]))}},{key:"runAnimation",value:function(e){this.manager||(this.manager=(r=function(){return null},n=!1,o=function e(t){if(!n){if(Array.isArray(t)){if(!t.length)return;var o=function(e){if(Array.isArray(e))return e}(t)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(t)||function(e,t){if(e){if("string"==typeof e)return B(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return B(e,t)}}(t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],a=o.slice(1);return"number"==typeof i?void I(e.bind(null,a),i):(e(i),void I(e.bind(null,a)))}"object"===R(t)&&r(t),"function"==typeof t&&t()}},{stop:function(){n=!0},start:function(e){n=!1,o(e)},subscribe:function(e){return r=e,function(){r=function(){return null}}}}));var t,r,n,o,i=e.begin,a=e.duration,u=e.attributeName,c=e.to,l=e.easing,s=e.onAnimationStart,f=e.onAnimationEnd,p=e.steps,d=e.children,h=this.manager;if(this.unSubscribe=h.subscribe(this.handleStyleChange),"function"==typeof l||"function"==typeof d||"spring"===l)return void this.runJSAnimation(e);if(p.length>1)return void this.runStepAnimation(e);var y=u?em({},u,c):c,v=Y(Object.keys(y),a,l);h.start([s,i,ev(ev({},y),{},{transition:v}),a,f])}},{key:"render",value:function(){var e=this.props,t=e.children,r=(e.begin,e.duration),o=(e.attributeName,e.easing,e.isActive),i=(e.steps,e.from,e.to,e.canBegin,e.onAnimationEnd,e.shouldReAnimate,e.onAnimationReStart,function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r,n,o={},i=Object.keys(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||(o[r]=e[r]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,ep)),a=n.Children.count(t),u=this.state.style;if("function"==typeof t)return t(u);if(!o||0===a||r<=0)return t;var c=function(e){var t=e.props,r=t.style,o=t.className;return(0,n.cloneElement)(e,ev(ev({},i),{},{style:ev(ev({},void 0===r?{}:r),u),className:o}))};return 1===a?c(n.Children.only(t)):n.createElement("div",null,n.Children.map(t,function(e){return c(e)}))}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,eb(n.key),n)}}(i.prototype,r),Object.defineProperty(i,"prototype",{writable:!1}),i}(n.PureComponent);ej.displayName="Animate",ej.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}},ej.propTypes={from:i().oneOfType([i().object,i().string]),to:i().oneOfType([i().object,i().string]),attributeName:i().string,duration:i().number,begin:i().number,easing:i().oneOfType([i().string,i().func]),steps:i().arrayOf(i().shape({duration:i().number.isRequired,style:i().object.isRequired,easing:i().oneOfType([i().oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),i().func]),properties:i().arrayOf("string"),onAnimationEnd:i().func})),children:i().oneOfType([i().node,i().func]),isActive:i().bool,canBegin:i().bool,onAnimationEnd:i().func,shouldReAnimate:i().bool,onAnimationStart:i().func,onAnimationReStart:i().func};let eS=ej},9699:(e,t,r)=>{var n=r(11011);e.exports=function(e,t,r){for(var o=-1,i=e.criteria,a=t.criteria,u=i.length,c=r.length;++o<u;){var l=n(i[o],a[o]);if(l){if(o>=c)return l;return l*("desc"==r[o]?-1:1)}}return e.index-t.index}},9795:(e,t,r)=>{"use strict";r.d(t,{i:()=>D});var n=r(12115),o=r(23633),i=r.n(o);let a=Math.cos,u=Math.sin,c=Math.sqrt,l=Math.PI,s=2*l,f={draw(e,t){let r=c(t/l);e.moveTo(r,0),e.arc(0,0,r,0,s)}},p=c(1/3),d=2*p,h=u(l/10)/u(7*l/10),y=u(s/10)*h,v=-a(s/10)*h,m=c(3),b=c(3)/2,g=1/c(12),x=(g/2+1)*3;var w=r(85654),O=r(31847);c(3),c(3);var j=r(52596),S=r(70788);function E(e){return(E="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var A=["type","size","sizeType"];function P(){return(P=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function k(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function M(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?k(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=E(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=E(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==E(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):k(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var T={symbolCircle:f,symbolCross:{draw(e,t){let r=c(t/5)/2;e.moveTo(-3*r,-r),e.lineTo(-r,-r),e.lineTo(-r,-3*r),e.lineTo(r,-3*r),e.lineTo(r,-r),e.lineTo(3*r,-r),e.lineTo(3*r,r),e.lineTo(r,r),e.lineTo(r,3*r),e.lineTo(-r,3*r),e.lineTo(-r,r),e.lineTo(-3*r,r),e.closePath()}},symbolDiamond:{draw(e,t){let r=c(t/d),n=r*p;e.moveTo(0,-r),e.lineTo(n,0),e.lineTo(0,r),e.lineTo(-n,0),e.closePath()}},symbolSquare:{draw(e,t){let r=c(t),n=-r/2;e.rect(n,n,r,r)}},symbolStar:{draw(e,t){let r=c(.8908130915292852*t),n=y*r,o=v*r;e.moveTo(0,-r),e.lineTo(n,o);for(let t=1;t<5;++t){let i=s*t/5,c=a(i),l=u(i);e.lineTo(l*r,-c*r),e.lineTo(c*n-l*o,l*n+c*o)}e.closePath()}},symbolTriangle:{draw(e,t){let r=-c(t/(3*m));e.moveTo(0,2*r),e.lineTo(-m*r,-r),e.lineTo(m*r,-r),e.closePath()}},symbolWye:{draw(e,t){let r=c(t/x),n=r/2,o=r*g,i=r*g+r,a=-n;e.moveTo(n,o),e.lineTo(n,i),e.lineTo(a,i),e.lineTo(-.5*n-b*o,b*n+-.5*o),e.lineTo(-.5*n-b*i,b*n+-.5*i),e.lineTo(-.5*a-b*i,b*a+-.5*i),e.lineTo(-.5*n+b*o,-.5*o-b*n),e.lineTo(-.5*n+b*i,-.5*i-b*n),e.lineTo(-.5*a+b*i,-.5*i-b*a),e.closePath()}}},_=Math.PI/180,C=function(e,t,r){if("area"===t)return e;switch(r){case"cross":return 5*e*e/9;case"diamond":return .5*e*e/Math.sqrt(3);case"square":return e*e;case"star":var n=18*_;return 1.25*e*e*(Math.tan(n)-Math.tan(2*n)*Math.pow(Math.tan(n),2));case"triangle":return Math.sqrt(3)*e*e/4;case"wye":return(21-10*Math.sqrt(3))*e*e/8;default:return Math.PI*e*e/4}},D=function(e){var t,r=e.type,o=void 0===r?"circle":r,a=e.size,u=void 0===a?64:a,c=e.sizeType,l=void 0===c?"area":c,s=M(M({},function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,A)),{},{type:o,size:u,sizeType:l}),p=s.className,d=s.cx,h=s.cy,y=(0,S.J9)(s,!0);return d===+d&&h===+h&&u===+u?n.createElement("path",P({},y,{className:(0,j.A)("recharts-symbols",p),transform:"translate(".concat(d,", ").concat(h,")"),d:(t=T["symbol".concat(i()(o))]||f,(function(e,t){let r=null,n=(0,O.i)(o);function o(){let o;if(r||(r=o=n()),e.apply(this,arguments).draw(r,+t.apply(this,arguments)),o)return r=null,o+""||null}return e="function"==typeof e?e:(0,w.A)(e||f),t="function"==typeof t?t:(0,w.A)(void 0===t?64:+t),o.type=function(t){return arguments.length?(e="function"==typeof t?t:(0,w.A)(t),o):e},o.size=function(e){return arguments.length?(t="function"==typeof e?e:(0,w.A)(+e),o):t},o.context=function(e){return arguments.length?(r=null==e?null:e,o):r},o})().type(t).size(C(u,l,o))())})):null};D.registerSymbol=function(e,t){T["symbol".concat(i()(e))]=t}},9813:(e,t,r)=>{var n=r(22143),o=r(48611),i=Object.prototype,a=i.hasOwnProperty,u=i.propertyIsEnumerable;e.exports=n(function(){return arguments}())?n:function(e){return o(e)&&a.call(e,"callee")&&!u.call(e,"callee")}},9819:(e,t,r)=>{"use strict";function n(e){return"object"==typeof e&&"length"in e?e:Array.from(e)}r.d(t,{A:()=>n}),Array.prototype.slice},10537:(e,t,r)=>{var n=r(96540),o=r(31598),i=r(18686);e.exports=function(){this.size=0,this.__data__={hash:new n,map:new(i||o),string:new n}}},11011:(e,t,r)=>{var n=r(70771);e.exports=function(e,t){if(e!==t){var r=void 0!==e,o=null===e,i=e==e,a=n(e),u=void 0!==t,c=null===t,l=t==t,s=n(t);if(!c&&!s&&!a&&e>t||a&&u&&l&&!c&&!s||o&&u&&l||!r&&l||!i)return 1;if(!o&&!a&&!s&&e<t||s&&r&&i&&!o&&!a||c&&r&&i||!u&&i||!l)return -1}return 0}},11670:(e,t,r)=>{var n=r(79401),o=r(9813),i=r(39608),a=r(33497),u=r(99544),c=r(35190),l=Object.prototype.hasOwnProperty;e.exports=function(e,t){var r=i(e),s=!r&&o(e),f=!r&&!s&&a(e),p=!r&&!s&&!f&&c(e),d=r||s||f||p,h=d?n(e.length,String):[],y=h.length;for(var v in e)(t||l.call(e,v))&&!(d&&("length"==v||f&&("offset"==v||"parent"==v)||p&&("buffer"==v||"byteLength"==v||"byteOffset"==v)||u(v,y)))&&h.push(v);return h}},12486:e=>{e.exports=function(e){return e!=e}},12814:(e,t,r)=>{"use strict";r.d(t,{s0:()=>n3,gH:()=>n0,YB:()=>oo,HQ:()=>or,xi:()=>oi,Hj:()=>ob,BX:()=>n5,tA:()=>n2,DW:()=>op,y2:()=>of,PW:()=>n7,Ay:()=>nQ,vf:()=>n6,Mk:()=>oh,Ps:()=>n1,Mn:()=>ol,kA:()=>od,Rh:()=>oe,w7:()=>os,zb:()=>ox,kr:()=>nJ,_L:()=>n4,KC:()=>og,A1:()=>n9,W7:()=>on,AQ:()=>om,_f:()=>oa});var n,o,i,a,u,c,l,s={};r.r(s),r.d(s,{scaleBand:()=>f.A,scaleDiverging:()=>function e(){var t=eI(rJ()(ev));return t.copy=function(){return rV(t,e())},ej.K.apply(t,arguments)},scaleDivergingLog:()=>function e(){var t=eY(rJ()).domain([.1,1,10]);return t.copy=function(){return rV(t,e()).base(t.base())},ej.K.apply(t,arguments)},scaleDivergingPow:()=>rQ,scaleDivergingSqrt:()=>r0,scaleDivergingSymlog:()=>function e(){var t=eX(rJ());return t.copy=function(){return rV(t,e()).constant(t.constant())},ej.K.apply(t,arguments)},scaleIdentity:()=>function e(t){var r;function n(e){return null==e||isNaN(e*=1)?r:e}return n.invert=n,n.domain=n.range=function(e){return arguments.length?(t=Array.from(e,eh),n):t.slice()},n.unknown=function(e){return arguments.length?(r=e,n):r},n.copy=function(){return e(t).unknown(r)},t=arguments.length?Array.from(t,eh):[0,1],eI(n)},scaleImplicit:()=>eZ.h,scaleLinear:()=>eR,scaleLog:()=>function e(){let t=eY(ew()).domain([1,10]);return t.copy=()=>ex(t,e()).base(t.base()),ej.C.apply(t,arguments),t},scaleOrdinal:()=>eZ.A,scalePoint:()=>f.z,scalePow:()=>eQ,scaleQuantile:()=>function e(){var t,r=[],n=[],o=[];function i(){var e=0,t=Math.max(1,n.length);for(o=Array(t-1);++e<t;)o[e-1]=function(e,t,r=j){if(!(!(n=e.length)||isNaN(t*=1))){if(t<=0||n<2)return+r(e[0],0,e);if(t>=1)return+r(e[n-1],n-1,e);var n,o=(n-1)*t,i=Math.floor(o),a=+r(e[i],i,e);return a+(r(e[i+1],i+1,e)-a)*(o-i)}}(r,e/t);return a}function a(e){return null==e||isNaN(e*=1)?t:n[E(o,e)]}return a.invertExtent=function(e){var t=n.indexOf(e);return t<0?[NaN,NaN]:[t>0?o[t-1]:r[0],t<o.length?o[t]:r[r.length-1]]},a.domain=function(e){if(!arguments.length)return r.slice();for(let t of(r=[],e))null==t||isNaN(t*=1)||r.push(t);return r.sort(g),i()},a.range=function(e){return arguments.length?(n=Array.from(e),i()):n.slice()},a.unknown=function(e){return arguments.length?(t=e,a):t},a.quantiles=function(){return o.slice()},a.copy=function(){return e().domain(r).range(n).unknown(t)},ej.C.apply(a,arguments)},scaleQuantize:()=>function e(){var t,r=0,n=1,o=1,i=[.5],a=[0,1];function u(e){return null!=e&&e<=e?a[E(i,e,0,o)]:t}function c(){var e=-1;for(i=Array(o);++e<o;)i[e]=((e+1)*n-(e-o)*r)/(o+1);return u}return u.domain=function(e){return arguments.length?([r,n]=e,r*=1,n*=1,c()):[r,n]},u.range=function(e){return arguments.length?(o=(a=Array.from(e)).length-1,c()):a.slice()},u.invertExtent=function(e){var t=a.indexOf(e);return t<0?[NaN,NaN]:t<1?[r,i[0]]:t>=o?[i[o-1],n]:[i[t-1],i[t]]},u.unknown=function(e){return arguments.length&&(t=e),u},u.thresholds=function(){return i.slice()},u.copy=function(){return e().domain([r,n]).range(a).unknown(t)},ej.C.apply(eI(u),arguments)},scaleRadial:()=>function e(){var t,r=eO(),n=[0,1],o=!1;function i(e){var n,i=Math.sign(n=r(e))*Math.sqrt(Math.abs(n));return isNaN(i)?t:o?Math.round(i):i}return i.invert=function(e){return r.invert(e1(e))},i.domain=function(e){return arguments.length?(r.domain(e),i):r.domain()},i.range=function(e){return arguments.length?(r.range((n=Array.from(e,eh)).map(e1)),i):n.slice()},i.rangeRound=function(e){return i.range(e).round(!0)},i.round=function(e){return arguments.length?(o=!!e,i):o},i.clamp=function(e){return arguments.length?(r.clamp(e),i):r.clamp()},i.unknown=function(e){return arguments.length?(t=e,i):t},i.copy=function(){return e(r.domain(),n).round(o).clamp(r.clamp()).unknown(t)},ej.C.apply(i,arguments),eI(i)},scaleSequential:()=>function e(){var t=eI(rZ()(ev));return t.copy=function(){return rV(t,e())},ej.K.apply(t,arguments)},scaleSequentialLog:()=>function e(){var t=eY(rZ()).domain([1,10]);return t.copy=function(){return rV(t,e()).base(t.base())},ej.K.apply(t,arguments)},scaleSequentialPow:()=>rG,scaleSequentialQuantile:()=>function e(){var t=[],r=ev;function n(e){if(null!=e&&!isNaN(e*=1))return r((E(t,e,1)-1)/(t.length-1))}return n.domain=function(e){if(!arguments.length)return t.slice();for(let r of(t=[],e))null==r||isNaN(r*=1)||t.push(r);return t.sort(g),n},n.interpolator=function(e){return arguments.length?(r=e,n):r},n.range=function(){return t.map((e,n)=>r(n/(t.length-1)))},n.quantiles=function(e){return Array.from({length:e+1},(r,n)=>(function(e,t,r){if(!(!(n=(e=Float64Array.from(function*(e,t){if(void 0===t)for(let t of e)null!=t&&(t*=1)>=t&&(yield t);else{let r=-1;for(let n of e)null!=(n=t(n,++r,e))&&(n*=1)>=n&&(yield n)}}(e,void 0))).length)||isNaN(t*=1))){if(t<=0||n<2)return e5(e);if(t>=1)return e2(e);var n,o=(n-1)*t,i=Math.floor(o),a=e2((function e(t,r,n=0,o=1/0,i){if(r=Math.floor(r),n=Math.floor(Math.max(0,n)),o=Math.floor(Math.min(t.length-1,o)),!(n<=r&&r<=o))return t;for(i=void 0===i?e3:function(e=g){if(e===g)return e3;if("function"!=typeof e)throw TypeError("compare is not a function");return(t,r)=>{let n=e(t,r);return n||0===n?n:(0===e(r,r))-(0===e(t,t))}}(i);o>n;){if(o-n>600){let a=o-n+1,u=r-n+1,c=Math.log(a),l=.5*Math.exp(2*c/3),s=.5*Math.sqrt(c*l*(a-l)/a)*(u-a/2<0?-1:1),f=Math.max(n,Math.floor(r-u*l/a+s)),p=Math.min(o,Math.floor(r+(a-u)*l/a+s));e(t,r,f,p,i)}let a=t[r],u=n,c=o;for(e8(t,n,r),i(t[o],a)>0&&e8(t,n,o);u<c;){for(e8(t,u,c),++u,--c;0>i(t[u],a);)++u;for(;i(t[c],a)>0;)--c}0===i(t[n],a)?e8(t,n,c):e8(t,++c,o),c<=r&&(n=c+1),r<=c&&(o=c-1)}return t})(e,i).subarray(0,i+1));return a+(e5(e.subarray(i+1))-a)*(o-i)}})(t,n/e))},n.copy=function(){return e(r).domain(t)},ej.K.apply(n,arguments)},scaleSequentialSqrt:()=>rK,scaleSequentialSymlog:()=>function e(){var t=eX(rZ());return t.copy=function(){return rV(t,e()).constant(t.constant())},ej.K.apply(t,arguments)},scaleSqrt:()=>e0,scaleSymlog:()=>function e(){var t=eX(ew());return t.copy=function(){return ex(t,e()).constant(t.constant())},ej.C.apply(t,arguments)},scaleThreshold:()=>function e(){var t,r=[.5],n=[0,1],o=1;function i(e){return null!=e&&e<=e?n[E(r,e,0,o)]:t}return i.domain=function(e){return arguments.length?(o=Math.min((r=Array.from(e)).length,n.length-1),i):r.slice()},i.range=function(e){return arguments.length?(n=Array.from(e),o=Math.min(r.length,n.length-1),i):n.slice()},i.invertExtent=function(e){var t=n.indexOf(e);return[r[t-1],r[t]]},i.unknown=function(e){return arguments.length?(t=e,i):t},i.copy=function(){return e().domain(r).range(n).unknown(t)},ej.C.apply(i,arguments)},scaleTime:()=>rq,scaleUtc:()=>rX,tickFormat:()=>eN});var f=r(81519);let p=Math.sqrt(50),d=Math.sqrt(10),h=Math.sqrt(2);function y(e,t,r){let n,o,i,a=(t-e)/Math.max(0,r),u=Math.floor(Math.log10(a)),c=a/Math.pow(10,u),l=c>=p?10:c>=d?5:c>=h?2:1;return(u<0?(n=Math.round(e*(i=Math.pow(10,-u)/l)),o=Math.round(t*i),n/i<e&&++n,o/i>t&&--o,i=-i):(n=Math.round(e/(i=Math.pow(10,u)*l)),o=Math.round(t/i),n*i<e&&++n,o*i>t&&--o),o<n&&.5<=r&&r<2)?y(e,t,2*r):[n,o,i]}function v(e,t,r){if(t*=1,e*=1,!((r*=1)>0))return[];if(e===t)return[e];let n=t<e,[o,i,a]=n?y(t,e,r):y(e,t,r);if(!(i>=o))return[];let u=i-o+1,c=Array(u);if(n)if(a<0)for(let e=0;e<u;++e)c[e]=-((i-e)/a);else for(let e=0;e<u;++e)c[e]=(i-e)*a;else if(a<0)for(let e=0;e<u;++e)c[e]=-((o+e)/a);else for(let e=0;e<u;++e)c[e]=(o+e)*a;return c}function m(e,t,r){return y(e*=1,t*=1,r*=1)[2]}function b(e,t,r){t*=1,e*=1,r*=1;let n=t<e,o=n?m(t,e,r):m(e,t,r);return(n?-1:1)*(o<0?-(1/o):o)}function g(e,t){return null==e||null==t?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function x(e,t){return null==e||null==t?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function w(e){let t,r,n;function o(e,n,i=0,a=e.length){if(i<a){if(0!==t(n,n))return a;do{let t=i+a>>>1;0>r(e[t],n)?i=t+1:a=t}while(i<a)}return i}return 2!==e.length?(t=g,r=(t,r)=>g(e(t),r),n=(t,r)=>e(t)-r):(t=e===g||e===x?e:O,r=e,n=e),{left:o,center:function(e,t,r=0,i=e.length){let a=o(e,t,r,i-1);return a>r&&n(e[a-1],t)>-n(e[a],t)?a-1:a},right:function(e,n,o=0,i=e.length){if(o<i){if(0!==t(n,n))return i;do{let t=o+i>>>1;0>=r(e[t],n)?o=t+1:i=t}while(o<i)}return o}}}function O(){return 0}function j(e){return null===e?NaN:+e}let S=w(g),E=S.right;function A(e,t,r){e.prototype=t.prototype=r,r.constructor=e}function P(e,t){var r=Object.create(e.prototype);for(var n in t)r[n]=t[n];return r}function k(){}S.left,w(j).center;var M="\\s*([+-]?\\d+)\\s*",T="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",_="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",C=/^#([0-9a-f]{3,8})$/,D=RegExp(`^rgb\\(${M},${M},${M}\\)$`),N=RegExp(`^rgb\\(${_},${_},${_}\\)$`),I=RegExp(`^rgba\\(${M},${M},${M},${T}\\)$`),R=RegExp(`^rgba\\(${_},${_},${_},${T}\\)$`),B=RegExp(`^hsl\\(${T},${_},${_}\\)$`),L=RegExp(`^hsla\\(${T},${_},${_},${T}\\)$`),W={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function F(){return this.rgb().formatHex()}function z(){return this.rgb().formatRgb()}function U(e){var t,r;return e=(e+"").trim().toLowerCase(),(t=C.exec(e))?(r=t[1].length,t=parseInt(t[1],16),6===r?$(t):3===r?new q(t>>8&15|t>>4&240,t>>4&15|240&t,(15&t)<<4|15&t,1):8===r?Y(t>>24&255,t>>16&255,t>>8&255,(255&t)/255):4===r?Y(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|240&t,((15&t)<<4|15&t)/255):null):(t=D.exec(e))?new q(t[1],t[2],t[3],1):(t=N.exec(e))?new q(255*t[1]/100,255*t[2]/100,255*t[3]/100,1):(t=I.exec(e))?Y(t[1],t[2],t[3],t[4]):(t=R.exec(e))?Y(255*t[1]/100,255*t[2]/100,255*t[3]/100,t[4]):(t=B.exec(e))?J(t[1],t[2]/100,t[3]/100,1):(t=L.exec(e))?J(t[1],t[2]/100,t[3]/100,t[4]):W.hasOwnProperty(e)?$(W[e]):"transparent"===e?new q(NaN,NaN,NaN,0):null}function $(e){return new q(e>>16&255,e>>8&255,255&e,1)}function Y(e,t,r,n){return n<=0&&(e=t=r=NaN),new q(e,t,r,n)}function H(e,t,r,n){var o;return 1==arguments.length?((o=e)instanceof k||(o=U(o)),o)?new q((o=o.rgb()).r,o.g,o.b,o.opacity):new q:new q(e,t,r,null==n?1:n)}function q(e,t,r,n){this.r=+e,this.g=+t,this.b=+r,this.opacity=+n}function X(){return`#${K(this.r)}${K(this.g)}${K(this.b)}`}function Z(){let e=V(this.opacity);return`${1===e?"rgb(":"rgba("}${G(this.r)}, ${G(this.g)}, ${G(this.b)}${1===e?")":`, ${e})`}`}function V(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function G(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function K(e){return((e=G(e))<16?"0":"")+e.toString(16)}function J(e,t,r,n){return n<=0?e=t=r=NaN:r<=0||r>=1?e=t=NaN:t<=0&&(e=NaN),new ee(e,t,r,n)}function Q(e){if(e instanceof ee)return new ee(e.h,e.s,e.l,e.opacity);if(e instanceof k||(e=U(e)),!e)return new ee;if(e instanceof ee)return e;var t=(e=e.rgb()).r/255,r=e.g/255,n=e.b/255,o=Math.min(t,r,n),i=Math.max(t,r,n),a=NaN,u=i-o,c=(i+o)/2;return u?(a=t===i?(r-n)/u+(r<n)*6:r===i?(n-t)/u+2:(t-r)/u+4,u/=c<.5?i+o:2-i-o,a*=60):u=c>0&&c<1?0:a,new ee(a,u,c,e.opacity)}function ee(e,t,r,n){this.h=+e,this.s=+t,this.l=+r,this.opacity=+n}function et(e){return(e=(e||0)%360)<0?e+360:e}function er(e){return Math.max(0,Math.min(1,e||0))}function en(e,t,r){return(e<60?t+(r-t)*e/60:e<180?r:e<240?t+(r-t)*(240-e)/60:t)*255}function eo(e,t,r,n,o){var i=e*e,a=i*e;return((1-3*e+3*i-a)*t+(4-6*i+3*a)*r+(1+3*e+3*i-3*a)*n+a*o)/6}A(k,U,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:F,formatHex:F,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return Q(this).formatHsl()},formatRgb:z,toString:z}),A(q,H,P(k,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new q(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new q(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new q(G(this.r),G(this.g),G(this.b),V(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:X,formatHex:X,formatHex8:function(){return`#${K(this.r)}${K(this.g)}${K(this.b)}${K((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:Z,toString:Z})),A(ee,function(e,t,r,n){return 1==arguments.length?Q(e):new ee(e,t,r,null==n?1:n)},P(k,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new ee(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new ee(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*t,o=2*r-n;return new q(en(e>=240?e-240:e+120,o,n),en(e,o,n),en(e<120?e+240:e-120,o,n),this.opacity)},clamp(){return new ee(et(this.h),er(this.s),er(this.l),V(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let e=V(this.opacity);return`${1===e?"hsl(":"hsla("}${et(this.h)}, ${100*er(this.s)}%, ${100*er(this.l)}%${1===e?")":`, ${e})`}`}}));let ei=e=>()=>e;function ea(e,t){var r,n,o=t-e;return o?(r=e,n=o,function(e){return r+e*n}):ei(isNaN(e)?t:e)}let eu=function e(t){var r,n=1==(r=+t)?ea:function(e,t){var n,o,i;return t-e?(n=e,o=t,n=Math.pow(n,i=r),o=Math.pow(o,i)-n,i=1/i,function(e){return Math.pow(n+e*o,i)}):ei(isNaN(e)?t:e)};function o(e,t){var r=n((e=H(e)).r,(t=H(t)).r),o=n(e.g,t.g),i=n(e.b,t.b),a=ea(e.opacity,t.opacity);return function(t){return e.r=r(t),e.g=o(t),e.b=i(t),e.opacity=a(t),e+""}}return o.gamma=e,o}(1);function ec(e){return function(t){var r,n,o=t.length,i=Array(o),a=Array(o),u=Array(o);for(r=0;r<o;++r)n=H(t[r]),i[r]=n.r||0,a[r]=n.g||0,u[r]=n.b||0;return i=e(i),a=e(a),u=e(u),n.opacity=1,function(e){return n.r=i(e),n.g=a(e),n.b=u(e),n+""}}}ec(function(e){var t=e.length-1;return function(r){var n=r<=0?r=0:r>=1?(r=1,t-1):Math.floor(r*t),o=e[n],i=e[n+1],a=n>0?e[n-1]:2*o-i,u=n<t-1?e[n+2]:2*i-o;return eo((r-n/t)*t,a,o,i,u)}}),ec(function(e){var t=e.length;return function(r){var n=Math.floor(((r%=1)<0?++r:r)*t),o=e[(n+t-1)%t],i=e[n%t],a=e[(n+1)%t],u=e[(n+2)%t];return eo((r-n/t)*t,o,i,a,u)}});function el(e,t){return e*=1,t*=1,function(r){return e*(1-r)+t*r}}var es=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,ef=RegExp(es.source,"g");function ep(e,t){var r,n,o=typeof t;return null==t||"boolean"===o?ei(t):("number"===o?el:"string"===o?(n=U(t))?(t=n,eu):function(e,t){var r,n,o,i,a,u=es.lastIndex=ef.lastIndex=0,c=-1,l=[],s=[];for(e+="",t+="";(o=es.exec(e))&&(i=ef.exec(t));)(a=i.index)>u&&(a=t.slice(u,a),l[c]?l[c]+=a:l[++c]=a),(o=o[0])===(i=i[0])?l[c]?l[c]+=i:l[++c]=i:(l[++c]=null,s.push({i:c,x:el(o,i)})),u=ef.lastIndex;return u<t.length&&(a=t.slice(u),l[c]?l[c]+=a:l[++c]=a),l.length<2?s[0]?(r=s[0].x,function(e){return r(e)+""}):(n=t,function(){return n}):(t=s.length,function(e){for(var r,n=0;n<t;++n)l[(r=s[n]).i]=r.x(e);return l.join("")})}:t instanceof U?eu:t instanceof Date?function(e,t){var r=new Date;return e*=1,t*=1,function(n){return r.setTime(e*(1-n)+t*n),r}}:!ArrayBuffer.isView(r=t)||r instanceof DataView?Array.isArray(t)?function(e,t){var r,n=t?t.length:0,o=e?Math.min(n,e.length):0,i=Array(o),a=Array(n);for(r=0;r<o;++r)i[r]=ep(e[r],t[r]);for(;r<n;++r)a[r]=t[r];return function(e){for(r=0;r<o;++r)a[r]=i[r](e);return a}}:"function"!=typeof t.valueOf&&"function"!=typeof t.toString||isNaN(t)?function(e,t){var r,n={},o={};for(r in(null===e||"object"!=typeof e)&&(e={}),(null===t||"object"!=typeof t)&&(t={}),t)r in e?n[r]=ep(e[r],t[r]):o[r]=t[r];return function(e){for(r in n)o[r]=n[r](e);return o}}:el:function(e,t){t||(t=[]);var r,n=e?Math.min(t.length,e.length):0,o=t.slice();return function(i){for(r=0;r<n;++r)o[r]=e[r]*(1-i)+t[r]*i;return o}})(e,t)}function ed(e,t){return e*=1,t*=1,function(r){return Math.round(e*(1-r)+t*r)}}function eh(e){return+e}var ey=[0,1];function ev(e){return e}function em(e,t){var r;return(t-=e*=1)?function(r){return(r-e)/t}:(r=isNaN(t)?NaN:.5,function(){return r})}function eb(e,t,r){var n=e[0],o=e[1],i=t[0],a=t[1];return o<n?(n=em(o,n),i=r(a,i)):(n=em(n,o),i=r(i,a)),function(e){return i(n(e))}}function eg(e,t,r){var n=Math.min(e.length,t.length)-1,o=Array(n),i=Array(n),a=-1;for(e[n]<e[0]&&(e=e.slice().reverse(),t=t.slice().reverse());++a<n;)o[a]=em(e[a],e[a+1]),i[a]=r(t[a],t[a+1]);return function(t){var r=E(e,t,1,n)-1;return i[r](o[r](t))}}function ex(e,t){return t.domain(e.domain()).range(e.range()).interpolate(e.interpolate()).clamp(e.clamp()).unknown(e.unknown())}function ew(){var e,t,r,n,o,i,a=ey,u=ey,c=ep,l=ev;function s(){var e,t,r,c=Math.min(a.length,u.length);return l!==ev&&(e=a[0],t=a[c-1],e>t&&(r=e,e=t,t=r),l=function(r){return Math.max(e,Math.min(t,r))}),n=c>2?eg:eb,o=i=null,f}function f(t){return null==t||isNaN(t*=1)?r:(o||(o=n(a.map(e),u,c)))(e(l(t)))}return f.invert=function(r){return l(t((i||(i=n(u,a.map(e),el)))(r)))},f.domain=function(e){return arguments.length?(a=Array.from(e,eh),s()):a.slice()},f.range=function(e){return arguments.length?(u=Array.from(e),s()):u.slice()},f.rangeRound=function(e){return u=Array.from(e),c=ed,s()},f.clamp=function(e){return arguments.length?(l=!!e||ev,s()):l!==ev},f.interpolate=function(e){return arguments.length?(c=e,s()):c},f.unknown=function(e){return arguments.length?(r=e,f):r},function(r,n){return e=r,t=n,s()}}function eO(){return ew()(ev,ev)}var ej=r(28749),eS=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function eE(e){var t;if(!(t=eS.exec(e)))throw Error("invalid format: "+e);return new eA({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}function eA(e){this.fill=void 0===e.fill?" ":e.fill+"",this.align=void 0===e.align?">":e.align+"",this.sign=void 0===e.sign?"-":e.sign+"",this.symbol=void 0===e.symbol?"":e.symbol+"",this.zero=!!e.zero,this.width=void 0===e.width?void 0:+e.width,this.comma=!!e.comma,this.precision=void 0===e.precision?void 0:+e.precision,this.trim=!!e.trim,this.type=void 0===e.type?"":e.type+""}function eP(e,t){if((r=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var r,n=e.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+e.slice(r+1)]}function ek(e){return(e=eP(Math.abs(e)))?e[1]:NaN}function eM(e,t){var r=eP(e,t);if(!r)return e+"";var n=r[0],o=r[1];return o<0?"0."+Array(-o).join("0")+n:n.length>o+1?n.slice(0,o+1)+"."+n.slice(o+1):n+Array(o-n.length+2).join("0")}eE.prototype=eA.prototype,eA.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};let eT={"%":(e,t)=>(100*e).toFixed(t),b:e=>Math.round(e).toString(2),c:e=>e+"",d:function(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)},e:(e,t)=>e.toExponential(t),f:(e,t)=>e.toFixed(t),g:(e,t)=>e.toPrecision(t),o:e=>Math.round(e).toString(8),p:(e,t)=>eM(100*e,t),r:eM,s:function(e,t){var r=eP(e,t);if(!r)return e+"";var o=r[0],i=r[1],a=i-(n=3*Math.max(-8,Math.min(8,Math.floor(i/3))))+1,u=o.length;return a===u?o:a>u?o+Array(a-u+1).join("0"):a>0?o.slice(0,a)+"."+o.slice(a):"0."+Array(1-a).join("0")+eP(e,Math.max(0,t+a-1))[0]},X:e=>Math.round(e).toString(16).toUpperCase(),x:e=>Math.round(e).toString(16)};function e_(e){return e}var eC=Array.prototype.map,eD=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function eN(e,t,r,n){var o,u,c,l=b(e,t,r);switch((n=eE(null==n?",f":n)).type){case"s":var s=Math.max(Math.abs(e),Math.abs(t));return null!=n.precision||isNaN(c=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(ek(s)/3)))-ek(Math.abs(l))))||(n.precision=c),a(n,s);case"":case"e":case"g":case"p":case"r":null!=n.precision||isNaN(c=Math.max(0,ek(Math.abs(Math.max(Math.abs(e),Math.abs(t)))-(o=Math.abs(o=l)))-ek(o))+1)||(n.precision=c-("e"===n.type));break;case"f":case"%":null!=n.precision||isNaN(c=Math.max(0,-ek(Math.abs(l))))||(n.precision=c-("%"===n.type)*2)}return i(n)}function eI(e){var t=e.domain;return e.ticks=function(e){var r=t();return v(r[0],r[r.length-1],null==e?10:e)},e.tickFormat=function(e,r){var n=t();return eN(n[0],n[n.length-1],null==e?10:e,r)},e.nice=function(r){null==r&&(r=10);var n,o,i=t(),a=0,u=i.length-1,c=i[a],l=i[u],s=10;for(l<c&&(o=c,c=l,l=o,o=a,a=u,u=o);s-- >0;){if((o=m(c,l,r))===n)return i[a]=c,i[u]=l,t(i);if(o>0)c=Math.floor(c/o)*o,l=Math.ceil(l/o)*o;else if(o<0)c=Math.ceil(c*o)/o,l=Math.floor(l*o)/o;else break;n=o}return e},e}function eR(){var e=eO();return e.copy=function(){return ex(e,eR())},ej.C.apply(e,arguments),eI(e)}function eB(e,t){e=e.slice();var r,n=0,o=e.length-1,i=e[n],a=e[o];return a<i&&(r=n,n=o,o=r,r=i,i=a,a=r),e[n]=t.floor(i),e[o]=t.ceil(a),e}function eL(e){return Math.log(e)}function eW(e){return Math.exp(e)}function eF(e){return-Math.log(-e)}function ez(e){return-Math.exp(-e)}function eU(e){return isFinite(e)?+("1e"+e):e<0?0:e}function e$(e){return(t,r)=>-e(-t,r)}function eY(e){let t,r,n=e(eL,eW),o=n.domain,a=10;function u(){var i,u;return t=(i=a)===Math.E?Math.log:10===i&&Math.log10||2===i&&Math.log2||(i=Math.log(i),e=>Math.log(e)/i),r=10===(u=a)?eU:u===Math.E?Math.exp:e=>Math.pow(u,e),o()[0]<0?(t=e$(t),r=e$(r),e(eF,ez)):e(eL,eW),n}return n.base=function(e){return arguments.length?(a=+e,u()):a},n.domain=function(e){return arguments.length?(o(e),u()):o()},n.ticks=e=>{let n,i,u=o(),c=u[0],l=u[u.length-1],s=l<c;s&&([c,l]=[l,c]);let f=t(c),p=t(l),d=null==e?10:+e,h=[];if(!(a%1)&&p-f<d){if(f=Math.floor(f),p=Math.ceil(p),c>0){for(;f<=p;++f)for(n=1;n<a;++n)if(!((i=f<0?n/r(-f):n*r(f))<c)){if(i>l)break;h.push(i)}}else for(;f<=p;++f)for(n=a-1;n>=1;--n)if(!((i=f>0?n/r(-f):n*r(f))<c)){if(i>l)break;h.push(i)}2*h.length<d&&(h=v(c,l,d))}else h=v(f,p,Math.min(p-f,d)).map(r);return s?h.reverse():h},n.tickFormat=(e,o)=>{if(null==e&&(e=10),null==o&&(o=10===a?"s":","),"function"!=typeof o&&(a%1||null!=(o=eE(o)).precision||(o.trim=!0),o=i(o)),e===1/0)return o;let u=Math.max(1,a*e/n.ticks().length);return e=>{let n=e/r(Math.round(t(e)));return n*a<a-.5&&(n*=a),n<=u?o(e):""}},n.nice=()=>o(eB(o(),{floor:e=>r(Math.floor(t(e))),ceil:e=>r(Math.ceil(t(e)))})),n}function eH(e){return function(t){return Math.sign(t)*Math.log1p(Math.abs(t/e))}}function eq(e){return function(t){return Math.sign(t)*Math.expm1(Math.abs(t))*e}}function eX(e){var t=1,r=e(eH(1),eq(t));return r.constant=function(r){return arguments.length?e(eH(t=+r),eq(t)):t},eI(r)}i=(o=function(e){var t,r,o,i=void 0===e.grouping||void 0===e.thousands?e_:(t=eC.call(e.grouping,Number),r=e.thousands+"",function(e,n){for(var o=e.length,i=[],a=0,u=t[0],c=0;o>0&&u>0&&(c+u+1>n&&(u=Math.max(1,n-c)),i.push(e.substring(o-=u,o+u)),!((c+=u+1)>n));)u=t[a=(a+1)%t.length];return i.reverse().join(r)}),a=void 0===e.currency?"":e.currency[0]+"",u=void 0===e.currency?"":e.currency[1]+"",c=void 0===e.decimal?".":e.decimal+"",l=void 0===e.numerals?e_:(o=eC.call(e.numerals,String),function(e){return e.replace(/[0-9]/g,function(e){return o[+e]})}),s=void 0===e.percent?"%":e.percent+"",f=void 0===e.minus?"−":e.minus+"",p=void 0===e.nan?"NaN":e.nan+"";function d(e){var t=(e=eE(e)).fill,r=e.align,o=e.sign,d=e.symbol,h=e.zero,y=e.width,v=e.comma,m=e.precision,b=e.trim,g=e.type;"n"===g?(v=!0,g="g"):eT[g]||(void 0===m&&(m=12),b=!0,g="g"),(h||"0"===t&&"="===r)&&(h=!0,t="0",r="=");var x="$"===d?a:"#"===d&&/[boxX]/.test(g)?"0"+g.toLowerCase():"",w="$"===d?u:/[%p]/.test(g)?s:"",O=eT[g],j=/[defgprs%]/.test(g);function S(e){var a,u,s,d=x,S=w;if("c"===g)S=O(e)+S,e="";else{var E=(e*=1)<0||1/e<0;if(e=isNaN(e)?p:O(Math.abs(e),m),b&&(e=function(e){e:for(var t,r=e.length,n=1,o=-1;n<r;++n)switch(e[n]){case".":o=t=n;break;case"0":0===o&&(o=n),t=n;break;default:if(!+e[n])break e;o>0&&(o=0)}return o>0?e.slice(0,o)+e.slice(t+1):e}(e)),E&&0==+e&&"+"!==o&&(E=!1),d=(E?"("===o?o:f:"-"===o||"("===o?"":o)+d,S=("s"===g?eD[8+n/3]:"")+S+(E&&"("===o?")":""),j){for(a=-1,u=e.length;++a<u;)if(48>(s=e.charCodeAt(a))||s>57){S=(46===s?c+e.slice(a+1):e.slice(a))+S,e=e.slice(0,a);break}}}v&&!h&&(e=i(e,1/0));var A=d.length+e.length+S.length,P=A<y?Array(y-A+1).join(t):"";switch(v&&h&&(e=i(P+e,P.length?y-S.length:1/0),P=""),r){case"<":e=d+e+S+P;break;case"=":e=d+P+e+S;break;case"^":e=P.slice(0,A=P.length>>1)+d+e+S+P.slice(A);break;default:e=P+d+e+S}return l(e)}return m=void 0===m?6:/[gprs]/.test(g)?Math.max(1,Math.min(21,m)):Math.max(0,Math.min(20,m)),S.toString=function(){return e+""},S}return{format:d,formatPrefix:function(e,t){var r=d(((e=eE(e)).type="f",e)),n=3*Math.max(-8,Math.min(8,Math.floor(ek(t)/3))),o=Math.pow(10,-n),i=eD[8+n/3];return function(e){return r(o*e)+i}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,a=o.formatPrefix;var eZ=r(95442);function eV(e){return function(t){return t<0?-Math.pow(-t,e):Math.pow(t,e)}}function eG(e){return e<0?-Math.sqrt(-e):Math.sqrt(e)}function eK(e){return e<0?-e*e:e*e}function eJ(e){var t=e(ev,ev),r=1;return t.exponent=function(t){return arguments.length?1==(r=+t)?e(ev,ev):.5===r?e(eG,eK):e(eV(r),eV(1/r)):r},eI(t)}function eQ(){var e=eJ(ew());return e.copy=function(){return ex(e,eQ()).exponent(e.exponent())},ej.C.apply(e,arguments),e}function e0(){return eQ.apply(null,arguments).exponent(.5)}function e1(e){return Math.sign(e)*e*e}function e2(e,t){let r;if(void 0===t)for(let t of e)null!=t&&(r<t||void 0===r&&t>=t)&&(r=t);else{let n=-1;for(let o of e)null!=(o=t(o,++n,e))&&(r<o||void 0===r&&o>=o)&&(r=o)}return r}function e5(e,t){let r;if(void 0===t)for(let t of e)null!=t&&(r>t||void 0===r&&t>=t)&&(r=t);else{let n=-1;for(let o of e)null!=(o=t(o,++n,e))&&(r>o||void 0===r&&o>=o)&&(r=o)}return r}function e3(e,t){return(null==e||!(e>=e))-(null==t||!(t>=t))||(e<t?-1:+(e>t))}function e8(e,t,r){let n=e[t];e[t]=e[r],e[r]=n}let e9=new Date,e6=new Date;function e4(e,t,r,n){function o(t){return e(t=0==arguments.length?new Date:new Date(+t)),t}return o.floor=t=>(e(t=new Date(+t)),t),o.ceil=r=>(e(r=new Date(r-1)),t(r,1),e(r),r),o.round=e=>{let t=o(e),r=o.ceil(e);return e-t<r-e?t:r},o.offset=(e,r)=>(t(e=new Date(+e),null==r?1:Math.floor(r)),e),o.range=(r,n,i)=>{let a,u=[];if(r=o.ceil(r),i=null==i?1:Math.floor(i),!(r<n)||!(i>0))return u;do u.push(a=new Date(+r)),t(r,i),e(r);while(a<r&&r<n);return u},o.filter=r=>e4(t=>{if(t>=t)for(;e(t),!r(t);)t.setTime(t-1)},(e,n)=>{if(e>=e)if(n<0)for(;++n<=0;)for(;t(e,-1),!r(e););else for(;--n>=0;)for(;t(e,1),!r(e););}),r&&(o.count=(t,n)=>(e9.setTime(+t),e6.setTime(+n),e(e9),e(e6),Math.floor(r(e9,e6))),o.every=e=>isFinite(e=Math.floor(e))&&e>0?e>1?o.filter(n?t=>n(t)%e==0:t=>o.count(0,t)%e==0):o:null),o}let e7=e4(()=>{},(e,t)=>{e.setTime(+e+t)},(e,t)=>t-e);e7.every=e=>isFinite(e=Math.floor(e))&&e>0?e>1?e4(t=>{t.setTime(Math.floor(t/e)*e)},(t,r)=>{t.setTime(+t+r*e)},(t,r)=>(r-t)/e):e7:null,e7.range;let te=e4(e=>{e.setTime(e-e.getMilliseconds())},(e,t)=>{e.setTime(+e+1e3*t)},(e,t)=>(t-e)/1e3,e=>e.getUTCSeconds());te.range;let tt=e4(e=>{e.setTime(e-e.getMilliseconds()-1e3*e.getSeconds())},(e,t)=>{e.setTime(+e+6e4*t)},(e,t)=>(t-e)/6e4,e=>e.getMinutes());tt.range;let tr=e4(e=>{e.setUTCSeconds(0,0)},(e,t)=>{e.setTime(+e+6e4*t)},(e,t)=>(t-e)/6e4,e=>e.getUTCMinutes());tr.range;let tn=e4(e=>{e.setTime(e-e.getMilliseconds()-1e3*e.getSeconds()-6e4*e.getMinutes())},(e,t)=>{e.setTime(+e+36e5*t)},(e,t)=>(t-e)/36e5,e=>e.getHours());tn.range;let to=e4(e=>{e.setUTCMinutes(0,0,0)},(e,t)=>{e.setTime(+e+36e5*t)},(e,t)=>(t-e)/36e5,e=>e.getUTCHours());to.range;let ti=e4(e=>e.setHours(0,0,0,0),(e,t)=>e.setDate(e.getDate()+t),(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*6e4)/864e5,e=>e.getDate()-1);ti.range;let ta=e4(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/864e5,e=>e.getUTCDate()-1);ta.range;let tu=e4(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/864e5,e=>Math.floor(e/864e5));function tc(e){return e4(t=>{t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)},(e,t)=>{e.setDate(e.getDate()+7*t)},(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*6e4)/6048e5)}tu.range;let tl=tc(0),ts=tc(1),tf=tc(2),tp=tc(3),td=tc(4),th=tc(5),ty=tc(6);function tv(e){return e4(t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+7*t)},(e,t)=>(t-e)/6048e5)}tl.range,ts.range,tf.range,tp.range,td.range,th.range,ty.range;let tm=tv(0),tb=tv(1),tg=tv(2),tx=tv(3),tw=tv(4),tO=tv(5),tj=tv(6);tm.range,tb.range,tg.range,tx.range,tw.range,tO.range,tj.range;let tS=e4(e=>{e.setDate(1),e.setHours(0,0,0,0)},(e,t)=>{e.setMonth(e.getMonth()+t)},(e,t)=>t.getMonth()-e.getMonth()+(t.getFullYear()-e.getFullYear())*12,e=>e.getMonth());tS.range;let tE=e4(e=>{e.setUTCDate(1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCMonth(e.getUTCMonth()+t)},(e,t)=>t.getUTCMonth()-e.getUTCMonth()+(t.getUTCFullYear()-e.getUTCFullYear())*12,e=>e.getUTCMonth());tE.range;let tA=e4(e=>{e.setMonth(0,1),e.setHours(0,0,0,0)},(e,t)=>{e.setFullYear(e.getFullYear()+t)},(e,t)=>t.getFullYear()-e.getFullYear(),e=>e.getFullYear());tA.every=e=>isFinite(e=Math.floor(e))&&e>0?e4(t=>{t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)},(t,r)=>{t.setFullYear(t.getFullYear()+r*e)}):null,tA.range;let tP=e4(e=>{e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCFullYear(e.getUTCFullYear()+t)},(e,t)=>t.getUTCFullYear()-e.getUTCFullYear(),e=>e.getUTCFullYear());function tk(e,t,r,n,o,i){let a=[[te,1,1e3],[te,5,5e3],[te,15,15e3],[te,30,3e4],[i,1,6e4],[i,5,3e5],[i,15,9e5],[i,30,18e5],[o,1,36e5],[o,3,108e5],[o,6,216e5],[o,12,432e5],[n,1,864e5],[n,2,1728e5],[r,1,6048e5],[t,1,2592e6],[t,3,7776e6],[e,1,31536e6]];function u(t,r,n){let o=Math.abs(r-t)/n,i=w(([,,e])=>e).right(a,o);if(i===a.length)return e.every(b(t/31536e6,r/31536e6,n));if(0===i)return e7.every(Math.max(b(t,r,n),1));let[u,c]=a[o/a[i-1][2]<a[i][2]/o?i-1:i];return u.every(c)}return[function(e,t,r){let n=t<e;n&&([e,t]=[t,e]);let o=r&&"function"==typeof r.range?r:u(e,t,r),i=o?o.range(e,+t+1):[];return n?i.reverse():i},u]}tP.every=e=>isFinite(e=Math.floor(e))&&e>0?e4(t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCFullYear(t.getUTCFullYear()+r*e)}):null,tP.range;let[tM,tT]=tk(tP,tE,tm,tu,to,tr),[t_,tC]=tk(tA,tS,tl,ti,tn,tt);function tD(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function tN(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function tI(e,t,r){return{y:e,m:t,d:r,H:0,M:0,S:0,L:0}}var tR={"-":"",_:" ",0:"0"},tB=/^\s*\d+/,tL=/^%/,tW=/[\\^$*+?|[\]().{}]/g;function tF(e,t,r){var n=e<0?"-":"",o=(n?-e:e)+"",i=o.length;return n+(i<r?Array(r-i+1).join(t)+o:o)}function tz(e){return e.replace(tW,"\\$&")}function tU(e){return RegExp("^(?:"+e.map(tz).join("|")+")","i")}function t$(e){return new Map(e.map((e,t)=>[e.toLowerCase(),t]))}function tY(e,t,r){var n=tB.exec(t.slice(r,r+1));return n?(e.w=+n[0],r+n[0].length):-1}function tH(e,t,r){var n=tB.exec(t.slice(r,r+1));return n?(e.u=+n[0],r+n[0].length):-1}function tq(e,t,r){var n=tB.exec(t.slice(r,r+2));return n?(e.U=+n[0],r+n[0].length):-1}function tX(e,t,r){var n=tB.exec(t.slice(r,r+2));return n?(e.V=+n[0],r+n[0].length):-1}function tZ(e,t,r){var n=tB.exec(t.slice(r,r+2));return n?(e.W=+n[0],r+n[0].length):-1}function tV(e,t,r){var n=tB.exec(t.slice(r,r+4));return n?(e.y=+n[0],r+n[0].length):-1}function tG(e,t,r){var n=tB.exec(t.slice(r,r+2));return n?(e.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function tK(e,t,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(r,r+6));return n?(e.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function tJ(e,t,r){var n=tB.exec(t.slice(r,r+1));return n?(e.q=3*n[0]-3,r+n[0].length):-1}function tQ(e,t,r){var n=tB.exec(t.slice(r,r+2));return n?(e.m=n[0]-1,r+n[0].length):-1}function t0(e,t,r){var n=tB.exec(t.slice(r,r+2));return n?(e.d=+n[0],r+n[0].length):-1}function t1(e,t,r){var n=tB.exec(t.slice(r,r+3));return n?(e.m=0,e.d=+n[0],r+n[0].length):-1}function t2(e,t,r){var n=tB.exec(t.slice(r,r+2));return n?(e.H=+n[0],r+n[0].length):-1}function t5(e,t,r){var n=tB.exec(t.slice(r,r+2));return n?(e.M=+n[0],r+n[0].length):-1}function t3(e,t,r){var n=tB.exec(t.slice(r,r+2));return n?(e.S=+n[0],r+n[0].length):-1}function t8(e,t,r){var n=tB.exec(t.slice(r,r+3));return n?(e.L=+n[0],r+n[0].length):-1}function t9(e,t,r){var n=tB.exec(t.slice(r,r+6));return n?(e.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function t6(e,t,r){var n=tL.exec(t.slice(r,r+1));return n?r+n[0].length:-1}function t4(e,t,r){var n=tB.exec(t.slice(r));return n?(e.Q=+n[0],r+n[0].length):-1}function t7(e,t,r){var n=tB.exec(t.slice(r));return n?(e.s=+n[0],r+n[0].length):-1}function re(e,t){return tF(e.getDate(),t,2)}function rt(e,t){return tF(e.getHours(),t,2)}function rr(e,t){return tF(e.getHours()%12||12,t,2)}function rn(e,t){return tF(1+ti.count(tA(e),e),t,3)}function ro(e,t){return tF(e.getMilliseconds(),t,3)}function ri(e,t){return ro(e,t)+"000"}function ra(e,t){return tF(e.getMonth()+1,t,2)}function ru(e,t){return tF(e.getMinutes(),t,2)}function rc(e,t){return tF(e.getSeconds(),t,2)}function rl(e){var t=e.getDay();return 0===t?7:t}function rs(e,t){return tF(tl.count(tA(e)-1,e),t,2)}function rf(e){var t=e.getDay();return t>=4||0===t?td(e):td.ceil(e)}function rp(e,t){return e=rf(e),tF(td.count(tA(e),e)+(4===tA(e).getDay()),t,2)}function rd(e){return e.getDay()}function rh(e,t){return tF(ts.count(tA(e)-1,e),t,2)}function ry(e,t){return tF(e.getFullYear()%100,t,2)}function rv(e,t){return tF((e=rf(e)).getFullYear()%100,t,2)}function rm(e,t){return tF(e.getFullYear()%1e4,t,4)}function rb(e,t){var r=e.getDay();return tF((e=r>=4||0===r?td(e):td.ceil(e)).getFullYear()%1e4,t,4)}function rg(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+tF(t/60|0,"0",2)+tF(t%60,"0",2)}function rx(e,t){return tF(e.getUTCDate(),t,2)}function rw(e,t){return tF(e.getUTCHours(),t,2)}function rO(e,t){return tF(e.getUTCHours()%12||12,t,2)}function rj(e,t){return tF(1+ta.count(tP(e),e),t,3)}function rS(e,t){return tF(e.getUTCMilliseconds(),t,3)}function rE(e,t){return rS(e,t)+"000"}function rA(e,t){return tF(e.getUTCMonth()+1,t,2)}function rP(e,t){return tF(e.getUTCMinutes(),t,2)}function rk(e,t){return tF(e.getUTCSeconds(),t,2)}function rM(e){var t=e.getUTCDay();return 0===t?7:t}function rT(e,t){return tF(tm.count(tP(e)-1,e),t,2)}function r_(e){var t=e.getUTCDay();return t>=4||0===t?tw(e):tw.ceil(e)}function rC(e,t){return e=r_(e),tF(tw.count(tP(e),e)+(4===tP(e).getUTCDay()),t,2)}function rD(e){return e.getUTCDay()}function rN(e,t){return tF(tb.count(tP(e)-1,e),t,2)}function rI(e,t){return tF(e.getUTCFullYear()%100,t,2)}function rR(e,t){return tF((e=r_(e)).getUTCFullYear()%100,t,2)}function rB(e,t){return tF(e.getUTCFullYear()%1e4,t,4)}function rL(e,t){var r=e.getUTCDay();return tF((e=r>=4||0===r?tw(e):tw.ceil(e)).getUTCFullYear()%1e4,t,4)}function rW(){return"+0000"}function rF(){return"%"}function rz(e){return+e}function rU(e){return Math.floor(e/1e3)}function r$(e){return new Date(e)}function rY(e){return e instanceof Date?+e:+new Date(+e)}function rH(e,t,r,n,o,i,a,u,c,l){var s=eO(),f=s.invert,p=s.domain,d=l(".%L"),h=l(":%S"),y=l("%I:%M"),v=l("%I %p"),m=l("%a %d"),b=l("%b %d"),g=l("%B"),x=l("%Y");function w(e){return(c(e)<e?d:u(e)<e?h:a(e)<e?y:i(e)<e?v:n(e)<e?o(e)<e?m:b:r(e)<e?g:x)(e)}return s.invert=function(e){return new Date(f(e))},s.domain=function(e){return arguments.length?p(Array.from(e,rY)):p().map(r$)},s.ticks=function(t){var r=p();return e(r[0],r[r.length-1],null==t?10:t)},s.tickFormat=function(e,t){return null==t?w:l(t)},s.nice=function(e){var r=p();return e&&"function"==typeof e.range||(e=t(r[0],r[r.length-1],null==e?10:e)),e?p(eB(r,e)):s},s.copy=function(){return ex(s,rH(e,t,r,n,o,i,a,u,c,l))},s}function rq(){return ej.C.apply(rH(t_,tC,tA,tS,tl,ti,tn,tt,te,c).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function rX(){return ej.C.apply(rH(tM,tT,tP,tE,tm,ta,to,tr,te,l).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function rZ(){var e,t,r,n,o,i=0,a=1,u=ev,c=!1;function l(t){return null==t||isNaN(t*=1)?o:u(0===r?.5:(t=(n(t)-e)*r,c?Math.max(0,Math.min(1,t)):t))}function s(e){return function(t){var r,n;return arguments.length?([r,n]=t,u=e(r,n),l):[u(0),u(1)]}}return l.domain=function(o){return arguments.length?([i,a]=o,e=n(i*=1),t=n(a*=1),r=e===t?0:1/(t-e),l):[i,a]},l.clamp=function(e){return arguments.length?(c=!!e,l):c},l.interpolator=function(e){return arguments.length?(u=e,l):u},l.range=s(ep),l.rangeRound=s(ed),l.unknown=function(e){return arguments.length?(o=e,l):o},function(o){return n=o,e=o(i),t=o(a),r=e===t?0:1/(t-e),l}}function rV(e,t){return t.domain(e.domain()).interpolator(e.interpolator()).clamp(e.clamp()).unknown(e.unknown())}function rG(){var e=eJ(rZ());return e.copy=function(){return rV(e,rG()).exponent(e.exponent())},ej.K.apply(e,arguments)}function rK(){return rG.apply(null,arguments).exponent(.5)}function rJ(){var e,t,r,n,o,i,a,u=0,c=.5,l=1,s=1,f=ev,p=!1;function d(e){return isNaN(e*=1)?a:(e=.5+((e=+i(e))-t)*(s*e<s*t?n:o),f(p?Math.max(0,Math.min(1,e)):e))}function h(e){return function(t){var r,n,o;return arguments.length?([r,n,o]=t,f=function(e,t){void 0===t&&(t=e,e=ep);for(var r=0,n=t.length-1,o=t[0],i=Array(n<0?0:n);r<n;)i[r]=e(o,o=t[++r]);return function(e){var t=Math.max(0,Math.min(n-1,Math.floor(e*=n)));return i[t](e-t)}}(e,[r,n,o]),d):[f(0),f(.5),f(1)]}}return d.domain=function(a){return arguments.length?([u,c,l]=a,e=i(u*=1),t=i(c*=1),r=i(l*=1),n=e===t?0:.5/(t-e),o=t===r?0:.5/(r-t),s=t<e?-1:1,d):[u,c,l]},d.clamp=function(e){return arguments.length?(p=!!e,d):p},d.interpolator=function(e){return arguments.length?(f=e,d):f},d.range=h(ep),d.rangeRound=h(ed),d.unknown=function(e){return arguments.length?(a=e,d):a},function(a){return i=a,e=a(u),t=a(c),r=a(l),n=e===t?0:.5/(t-e),o=t===r?0:.5/(r-t),s=t<e?-1:1,d}}function rQ(){var e=eJ(rJ());return e.copy=function(){return rV(e,rQ()).exponent(e.exponent())},ej.K.apply(e,arguments)}function r0(){return rQ.apply(null,arguments).exponent(.5)}function r1(e,t){if((o=e.length)>1)for(var r,n,o,i=1,a=e[t[0]],u=a.length;i<o;++i)for(n=a,a=e[t[i]],r=0;r<u;++r)a[r][1]+=a[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}c=(u=function(e){var t=e.dateTime,r=e.date,n=e.time,o=e.periods,i=e.days,a=e.shortDays,u=e.months,c=e.shortMonths,l=tU(o),s=t$(o),f=tU(i),p=t$(i),d=tU(a),h=t$(a),y=tU(u),v=t$(u),m=tU(c),b=t$(c),g={a:function(e){return a[e.getDay()]},A:function(e){return i[e.getDay()]},b:function(e){return c[e.getMonth()]},B:function(e){return u[e.getMonth()]},c:null,d:re,e:re,f:ri,g:rv,G:rb,H:rt,I:rr,j:rn,L:ro,m:ra,M:ru,p:function(e){return o[+(e.getHours()>=12)]},q:function(e){return 1+~~(e.getMonth()/3)},Q:rz,s:rU,S:rc,u:rl,U:rs,V:rp,w:rd,W:rh,x:null,X:null,y:ry,Y:rm,Z:rg,"%":rF},x={a:function(e){return a[e.getUTCDay()]},A:function(e){return i[e.getUTCDay()]},b:function(e){return c[e.getUTCMonth()]},B:function(e){return u[e.getUTCMonth()]},c:null,d:rx,e:rx,f:rE,g:rR,G:rL,H:rw,I:rO,j:rj,L:rS,m:rA,M:rP,p:function(e){return o[+(e.getUTCHours()>=12)]},q:function(e){return 1+~~(e.getUTCMonth()/3)},Q:rz,s:rU,S:rk,u:rM,U:rT,V:rC,w:rD,W:rN,x:null,X:null,y:rI,Y:rB,Z:rW,"%":rF},w={a:function(e,t,r){var n=d.exec(t.slice(r));return n?(e.w=h.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(e,t,r){var n=f.exec(t.slice(r));return n?(e.w=p.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(e,t,r){var n=m.exec(t.slice(r));return n?(e.m=b.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(e,t,r){var n=y.exec(t.slice(r));return n?(e.m=v.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(e,r,n){return S(e,t,r,n)},d:t0,e:t0,f:t9,g:tG,G:tV,H:t2,I:t2,j:t1,L:t8,m:tQ,M:t5,p:function(e,t,r){var n=l.exec(t.slice(r));return n?(e.p=s.get(n[0].toLowerCase()),r+n[0].length):-1},q:tJ,Q:t4,s:t7,S:t3,u:tH,U:tq,V:tX,w:tY,W:tZ,x:function(e,t,n){return S(e,r,t,n)},X:function(e,t,r){return S(e,n,t,r)},y:tG,Y:tV,Z:tK,"%":t6};function O(e,t){return function(r){var n,o,i,a=[],u=-1,c=0,l=e.length;for(r instanceof Date||(r=new Date(+r));++u<l;)37===e.charCodeAt(u)&&(a.push(e.slice(c,u)),null!=(o=tR[n=e.charAt(++u)])?n=e.charAt(++u):o="e"===n?" ":"0",(i=t[n])&&(n=i(r,o)),a.push(n),c=u+1);return a.push(e.slice(c,u)),a.join("")}}function j(e,t){return function(r){var n,o,i=tI(1900,void 0,1);if(S(i,e,r+="",0)!=r.length)return null;if("Q"in i)return new Date(i.Q);if("s"in i)return new Date(1e3*i.s+("L"in i?i.L:0));if(!t||"Z"in i||(i.Z=0),"p"in i&&(i.H=i.H%12+12*i.p),void 0===i.m&&(i.m="q"in i?i.q:0),"V"in i){if(i.V<1||i.V>53)return null;"w"in i||(i.w=1),"Z"in i?(n=(o=(n=tN(tI(i.y,0,1))).getUTCDay())>4||0===o?tb.ceil(n):tb(n),n=ta.offset(n,(i.V-1)*7),i.y=n.getUTCFullYear(),i.m=n.getUTCMonth(),i.d=n.getUTCDate()+(i.w+6)%7):(n=(o=(n=tD(tI(i.y,0,1))).getDay())>4||0===o?ts.ceil(n):ts(n),n=ti.offset(n,(i.V-1)*7),i.y=n.getFullYear(),i.m=n.getMonth(),i.d=n.getDate()+(i.w+6)%7)}else("W"in i||"U"in i)&&("w"in i||(i.w="u"in i?i.u%7:+("W"in i)),o="Z"in i?tN(tI(i.y,0,1)).getUTCDay():tD(tI(i.y,0,1)).getDay(),i.m=0,i.d="W"in i?(i.w+6)%7+7*i.W-(o+5)%7:i.w+7*i.U-(o+6)%7);return"Z"in i?(i.H+=i.Z/100|0,i.M+=i.Z%100,tN(i)):tD(i)}}function S(e,t,r,n){for(var o,i,a=0,u=t.length,c=r.length;a<u;){if(n>=c)return -1;if(37===(o=t.charCodeAt(a++))){if(!(i=w[(o=t.charAt(a++))in tR?t.charAt(a++):o])||(n=i(e,r,n))<0)return -1}else if(o!=r.charCodeAt(n++))return -1}return n}return g.x=O(r,g),g.X=O(n,g),g.c=O(t,g),x.x=O(r,x),x.X=O(n,x),x.c=O(t,x),{format:function(e){var t=O(e+="",g);return t.toString=function(){return e},t},parse:function(e){var t=j(e+="",!1);return t.toString=function(){return e},t},utcFormat:function(e){var t=O(e+="",x);return t.toString=function(){return e},t},utcParse:function(e){var t=j(e+="",!0);return t.toString=function(){return e},t}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,u.parse,l=u.utcFormat,u.utcParse;var r2=r(9819),r5=r(85654);function r3(e){for(var t=e.length,r=Array(t);--t>=0;)r[t]=t;return r}function r8(e,t){return e[t]}function r9(e){let t=[];return t.key=e,t}var r6=r(22315),r4=r.n(r6),r7=r(89053),ne=r.n(r7),nt=r(59882),nr=r.n(nt),nn=r(40139),no=r.n(nn),ni=r(15438),na=r.n(ni),nu=r(48973),nc=r.n(nu),nl=r(3698),ns=r.n(nl),nf=r(13908),np=r.n(nf),nd=r(23633),nh=r.n(nd),ny=r(60245),nv=r.n(ny),nm=r(67206),nb=r.n(nm),ng=r(8870),nx=r.n(ng);function nw(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var nO=function(e){return e},nj={},nS=function(e){return e===nj},nE=function(e){return function t(){return 0==arguments.length||1==arguments.length&&nS(arguments.length<=0?void 0:arguments[0])?t:e.apply(void 0,arguments)}},nA=function(e){return function e(t,r){return 1===t?r:nE(function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];var a=o.filter(function(e){return e!==nj}).length;return a>=t?r.apply(void 0,o):e(t-a,nE(function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];var i=o.map(function(e){return nS(e)?t.shift():e});return r.apply(void 0,((function(e){if(Array.isArray(e))return nw(e)})(i)||function(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(i)||function(e,t){if(e){if("string"==typeof e)return nw(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nw(e,t)}}(i)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()).concat(t))}))})}(e.length,e)},nP=function(e,t){for(var r=[],n=e;n<t;++n)r[n-e]=n;return r},nk=nA(function(e,t){return Array.isArray(t)?t.map(e):Object.keys(t).map(function(e){return t[e]}).map(e)}),nM=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];if(!t.length)return nO;var n=t.reverse(),o=n[0],i=n.slice(1);return function(){return i.reduce(function(e,t){return t(e)},o.apply(void 0,arguments))}},nT=function(e){return Array.isArray(e)?e.reverse():e.split("").reverse.join("")},n_=function(e){var t=null,r=null;return function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];return t&&o.every(function(e,r){return e===t[r]})?r:(t=o,r=e.apply(void 0,o))}};nA(function(e,t,r){var n=+e;return n+r*(t-n)}),nA(function(e,t,r){var n=t-e;return(r-e)/(n=n||1/0)}),nA(function(e,t,r){var n=t-e;return Math.max(0,Math.min(1,(r-e)/(n=n||1/0)))});let nC={rangeStep:function(e,t,r){for(var n=new(nx())(e),o=0,i=[];n.lt(t)&&o<1e5;)i.push(n.toNumber()),n=n.add(r),o++;return i},getDigitCount:function(e){var t;return 0===e?1:Math.floor(new(nx())(e).abs().log(10).toNumber())+1}};function nD(e){return function(e){if(Array.isArray(e))return nR(e)}(e)||function(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(e)||nI(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nN(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e)){var r=[],n=!0,o=!1,i=void 0;try{for(var a,u=e[Symbol.iterator]();!(n=(a=u.next()).done)&&(r.push(a.value),!t||r.length!==t);n=!0);}catch(e){o=!0,i=e}finally{try{n||null==u.return||u.return()}finally{if(o)throw i}}return r}}(e,t)||nI(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nI(e,t){if(e){if("string"==typeof e)return nR(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nR(e,t)}}function nR(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function nB(e){var t=nN(e,2),r=t[0],n=t[1],o=r,i=n;return r>n&&(o=n,i=r),[o,i]}function nL(e,t,r){if(e.lte(0))return new(nx())(0);var n=nC.getDigitCount(e.toNumber()),o=new(nx())(10).pow(n),i=e.div(o),a=1!==n?.05:.1,u=new(nx())(Math.ceil(i.div(a).toNumber())).add(r).mul(a).mul(o);return t?u:new(nx())(Math.ceil(u))}function nW(e,t,r){var n=1,o=new(nx())(e);if(!o.isint()&&r){var i=Math.abs(e);i<1?(n=new(nx())(10).pow(nC.getDigitCount(e)-1),o=new(nx())(Math.floor(o.div(n).toNumber())).mul(n)):i>1&&(o=new(nx())(Math.floor(e)))}else 0===e?o=new(nx())(Math.floor((t-1)/2)):r||(o=new(nx())(Math.floor(e)));var a=Math.floor((t-1)/2);return nM(nk(function(e){return o.add(new(nx())(e-a).mul(n)).toNumber()}),nP)(0,t)}var nF=n_(function(e){var t=nN(e,2),r=t[0],n=t[1],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(o,2),u=nN(nB([r,n]),2),c=u[0],l=u[1];if(c===-1/0||l===1/0){var s=l===1/0?[c].concat(nD(nP(0,o-1).map(function(){return 1/0}))):[].concat(nD(nP(0,o-1).map(function(){return-1/0})),[l]);return r>n?nT(s):s}if(c===l)return nW(c,o,i);var f=function e(t,r,n,o){var i,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((r-t)/(n-1)))return{step:new(nx())(0),tickMin:new(nx())(0),tickMax:new(nx())(0)};var u=nL(new(nx())(r).sub(t).div(n-1),o,a),c=Math.ceil((i=t<=0&&r>=0?new(nx())(0):(i=new(nx())(t).add(r).div(2)).sub(new(nx())(i).mod(u))).sub(t).div(u).toNumber()),l=Math.ceil(new(nx())(r).sub(i).div(u).toNumber()),s=c+l+1;return s>n?e(t,r,n,o,a+1):(s<n&&(l=r>0?l+(n-s):l,c=r>0?c:c+(n-s)),{step:u,tickMin:i.sub(new(nx())(c).mul(u)),tickMax:i.add(new(nx())(l).mul(u))})}(c,l,a,i),p=f.step,d=f.tickMin,h=f.tickMax,y=nC.rangeStep(d,h.add(new(nx())(.1).mul(p)),p);return r>n?nT(y):y});n_(function(e){var t=nN(e,2),r=t[0],n=t[1],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(o,2),u=nN(nB([r,n]),2),c=u[0],l=u[1];if(c===-1/0||l===1/0)return[r,n];if(c===l)return nW(c,o,i);var s=nL(new(nx())(l).sub(c).div(a-1),i,0),f=nM(nk(function(e){return new(nx())(c).add(new(nx())(e).mul(s)).toNumber()}),nP)(0,a).filter(function(e){return e>=c&&e<=l});return r>n?nT(f):f});var nz=n_(function(e,t){var r=nN(e,2),n=r[0],o=r[1],i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=nN(nB([n,o]),2),u=a[0],c=a[1];if(u===-1/0||c===1/0)return[n,o];if(u===c)return[u];var l=Math.max(t,2),s=nL(new(nx())(c).sub(u).div(l-1),i,0),f=[].concat(nD(nC.rangeStep(new(nx())(u),new(nx())(c).sub(new(nx())(.99).mul(s)),s)),[c]);return n>o?nT(f):f}),nU=r(94011),n$=r(16377),nY=r(70788),nH=r(83197);function nq(e){return(nq="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function nX(e){return function(e){if(Array.isArray(e))return nZ(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return nZ(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nZ(e,t)}}(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nZ(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function nV(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function nG(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?nV(Object(r),!0).forEach(function(t){nK(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nV(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function nK(e,t,r){var n;return(n=function(e,t){if("object"!=nq(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=nq(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"==nq(n)?n:n+"")in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function nJ(e,t,r){return nr()(e)||nr()(t)?r:(0,n$.vh)(t)?nc()(e,t,r):no()(t)?t(e):r}function nQ(e,t,r,n){var o=ns()(e,function(e){return nJ(e,t)});if("number"===r){var i=o.filter(function(e){return(0,n$.Et)(e)||parseFloat(e)});return i.length?[ne()(i),r4()(i)]:[1/0,-1/0]}return(n?o.filter(function(e){return!nr()(e)}):o).map(function(e){return(0,n$.vh)(e)||e instanceof Date?e:""})}var n0=function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2?arguments[2]:void 0,o=arguments.length>3?arguments[3]:void 0,i=-1,a=null!=(t=null==r?void 0:r.length)?t:0;if(a<=1)return 0;if(o&&"angleAxis"===o.axisType&&1e-6>=Math.abs(Math.abs(o.range[1]-o.range[0])-360))for(var u=o.range,c=0;c<a;c++){var l=c>0?n[c-1].coordinate:n[a-1].coordinate,s=n[c].coordinate,f=c>=a-1?n[0].coordinate:n[c+1].coordinate,p=void 0;if((0,n$.sA)(s-l)!==(0,n$.sA)(f-s)){var d=[];if((0,n$.sA)(f-s)===(0,n$.sA)(u[1]-u[0])){p=f;var h=s+u[1]-u[0];d[0]=Math.min(h,(h+l)/2),d[1]=Math.max(h,(h+l)/2)}else{p=l;var y=f+u[1]-u[0];d[0]=Math.min(s,(y+s)/2),d[1]=Math.max(s,(y+s)/2)}var v=[Math.min(s,(p+s)/2),Math.max(s,(p+s)/2)];if(e>v[0]&&e<=v[1]||e>=d[0]&&e<=d[1]){i=n[c].index;break}}else{var m=Math.min(l,f),b=Math.max(l,f);if(e>(m+s)/2&&e<=(b+s)/2){i=n[c].index;break}}}else for(var g=0;g<a;g++)if(0===g&&e<=(r[g].coordinate+r[g+1].coordinate)/2||g>0&&g<a-1&&e>(r[g].coordinate+r[g-1].coordinate)/2&&e<=(r[g].coordinate+r[g+1].coordinate)/2||g===a-1&&e>(r[g].coordinate+r[g-1].coordinate)/2){i=r[g].index;break}return i},n1=function(e){var t,r,n=e.type.displayName,o=null!=(t=e.type)&&t.defaultProps?nG(nG({},e.type.defaultProps),e.props):e.props,i=o.stroke,a=o.fill;switch(n){case"Line":r=i;break;case"Area":case"Radar":r=i&&"none"!==i?i:a;break;default:r=a}return r},n2=function(e){var t=e.barSize,r=e.totalSize,n=e.stackGroups,o=void 0===n?{}:n;if(!o)return{};for(var i={},a=Object.keys(o),u=0,c=a.length;u<c;u++)for(var l=o[a[u]].stackGroups,s=Object.keys(l),f=0,p=s.length;f<p;f++){var d=l[s[f]],h=d.items,y=d.cateAxisId,v=h.filter(function(e){return(0,nY.Mn)(e.type).indexOf("Bar")>=0});if(v&&v.length){var m=v[0].type.defaultProps,b=void 0!==m?nG(nG({},m),v[0].props):v[0].props,g=b.barSize,x=b[y];i[x]||(i[x]=[]);var w=nr()(g)?t:g;i[x].push({item:v[0],stackList:v.slice(1),barSize:nr()(w)?void 0:(0,n$.F4)(w,r,0)})}}return i},n5=function(e){var t,r=e.barGap,n=e.barCategoryGap,o=e.bandSize,i=e.sizeList,a=void 0===i?[]:i,u=e.maxBarSize,c=a.length;if(c<1)return null;var l=(0,n$.F4)(r,o,0,!0),s=[];if(a[0].barSize===+a[0].barSize){var f=!1,p=o/c,d=a.reduce(function(e,t){return e+t.barSize||0},0);(d+=(c-1)*l)>=o&&(d-=(c-1)*l,l=0),d>=o&&p>0&&(f=!0,p*=.9,d=c*p);var h={offset:((o-d)/2|0)-l,size:0};t=a.reduce(function(e,t){var r={item:t.item,position:{offset:h.offset+h.size+l,size:f?p:t.barSize}},n=[].concat(nX(e),[r]);return h=n[n.length-1].position,t.stackList&&t.stackList.length&&t.stackList.forEach(function(e){n.push({item:e,position:h})}),n},s)}else{var y=(0,n$.F4)(n,o,0,!0);o-2*y-(c-1)*l<=0&&(l=0);var v=(o-2*y-(c-1)*l)/c;v>1&&(v>>=0);var m=u===+u?Math.min(v,u):v;t=a.reduce(function(e,t,r){var n=[].concat(nX(e),[{item:t.item,position:{offset:y+(v+l)*r+(v-m)/2,size:m}}]);return t.stackList&&t.stackList.length&&t.stackList.forEach(function(e){n.push({item:e,position:n[n.length-1].position})}),n},s)}return t},n3=function(e,t,r,n){var o=r.children,i=r.width,a=r.margin,u=i-(a.left||0)-(a.right||0),c=(0,nH.g)({children:o,legendWidth:u});if(c){var l=n||{},s=l.width,f=l.height,p=c.align,d=c.verticalAlign,h=c.layout;if(("vertical"===h||"horizontal"===h&&"middle"===d)&&"center"!==p&&(0,n$.Et)(e[p]))return nG(nG({},e),{},nK({},p,e[p]+(s||0)));if(("horizontal"===h||"vertical"===h&&"center"===p)&&"middle"!==d&&(0,n$.Et)(e[d]))return nG(nG({},e),{},nK({},d,e[d]+(f||0)))}return e},n8=function(e,t,r,n,o){var i=t.props.children,a=(0,nY.aS)(i,nU.u).filter(function(e){var t;return t=e.props.direction,!!nr()(o)||("horizontal"===n?"yAxis"===o:"vertical"===n||"x"===t?"xAxis"===o:"y"!==t||"yAxis"===o)});if(a&&a.length){var u=a.map(function(e){return e.props.dataKey});return e.reduce(function(e,t){var n=nJ(t,r);if(nr()(n))return e;var o=Array.isArray(n)?[ne()(n),r4()(n)]:[n,n],i=u.reduce(function(e,r){var n=nJ(t,r,0),i=o[0]-Math.abs(Array.isArray(n)?n[0]:n),a=o[1]+Math.abs(Array.isArray(n)?n[1]:n);return[Math.min(i,e[0]),Math.max(a,e[1])]},[1/0,-1/0]);return[Math.min(i[0],e[0]),Math.max(i[1],e[1])]},[1/0,-1/0])}return null},n9=function(e,t,r,n,o){var i=t.map(function(t){return n8(e,t,r,o,n)}).filter(function(e){return!nr()(e)});return i&&i.length?i.reduce(function(e,t){return[Math.min(e[0],t[0]),Math.max(e[1],t[1])]},[1/0,-1/0]):null},n6=function(e,t,r,n,o){var i=t.map(function(t){var i=t.props.dataKey;return"number"===r&&i&&n8(e,t,i,n)||nQ(e,i,r,o)});if("number"===r)return i.reduce(function(e,t){return[Math.min(e[0],t[0]),Math.max(e[1],t[1])]},[1/0,-1/0]);var a={};return i.reduce(function(e,t){for(var r=0,n=t.length;r<n;r++)a[t[r]]||(a[t[r]]=!0,e.push(t[r]));return e},[])},n4=function(e,t){return"horizontal"===e&&"xAxis"===t||"vertical"===e&&"yAxis"===t||"centric"===e&&"angleAxis"===t||"radial"===e&&"radiusAxis"===t},n7=function(e,t,r,n){if(n)return e.map(function(e){return e.coordinate});var o,i,a=e.map(function(e){return e.coordinate===t&&(o=!0),e.coordinate===r&&(i=!0),e.coordinate});return o||a.push(t),i||a.push(r),a},oe=function(e,t,r){if(!e)return null;var n=e.scale,o=e.duplicateDomain,i=e.type,a=e.range,u="scaleBand"===e.realScaleType?n.bandwidth()/2:2,c=(t||r)&&"category"===i&&n.bandwidth?n.bandwidth()/u:0;return(c="angleAxis"===e.axisType&&(null==a?void 0:a.length)>=2?2*(0,n$.sA)(a[0]-a[1])*c:c,t&&(e.ticks||e.niceTicks))?(e.ticks||e.niceTicks).map(function(e){return{coordinate:n(o?o.indexOf(e):e)+c,value:e,offset:c}}).filter(function(e){return!np()(e.coordinate)}):e.isCategorical&&e.categoricalDomain?e.categoricalDomain.map(function(e,t){return{coordinate:n(e)+c,value:e,index:t,offset:c}}):n.ticks&&!r?n.ticks(e.tickCount).map(function(e){return{coordinate:n(e)+c,value:e,offset:c}}):n.domain().map(function(e,t){return{coordinate:n(e)+c,value:o?o[e]:e,index:t,offset:c}})},ot=new WeakMap,or=function(e,t){if("function"!=typeof t)return e;ot.has(e)||ot.set(e,new WeakMap);var r=ot.get(e);if(r.has(t))return r.get(t);var n=function(){e.apply(void 0,arguments),t.apply(void 0,arguments)};return r.set(t,n),n},on=function(e,t,r){var n=e.scale,o=e.type,i=e.layout,a=e.axisType;if("auto"===n)return"radial"===i&&"radiusAxis"===a?{scale:f.A(),realScaleType:"band"}:"radial"===i&&"angleAxis"===a?{scale:eR(),realScaleType:"linear"}:"category"===o&&t&&(t.indexOf("LineChart")>=0||t.indexOf("AreaChart")>=0||t.indexOf("ComposedChart")>=0&&!r)?{scale:f.z(),realScaleType:"point"}:"category"===o?{scale:f.A(),realScaleType:"band"}:{scale:eR(),realScaleType:"linear"};if(na()(n)){var u="scale".concat(nh()(n));return{scale:(s[u]||f.z)(),realScaleType:s[u]?u:"point"}}return no()(n)?{scale:n}:{scale:f.z(),realScaleType:"point"}},oo=function(e){var t=e.domain();if(t&&!(t.length<=2)){var r=t.length,n=e.range(),o=Math.min(n[0],n[1])-1e-4,i=Math.max(n[0],n[1])+1e-4,a=e(t[0]),u=e(t[r-1]);(a<o||a>i||u<o||u>i)&&e.domain([t[0],t[r-1]])}},oi=function(e,t){if(!e)return null;for(var r=0,n=e.length;r<n;r++)if(e[r].item===t)return e[r].position;return null},oa=function(e,t){if(!t||2!==t.length||!(0,n$.Et)(t[0])||!(0,n$.Et)(t[1]))return e;var r=Math.min(t[0],t[1]),n=Math.max(t[0],t[1]),o=[e[0],e[1]];return(!(0,n$.Et)(e[0])||e[0]<r)&&(o[0]=r),(!(0,n$.Et)(e[1])||e[1]>n)&&(o[1]=n),o[0]>n&&(o[0]=n),o[1]<r&&(o[1]=r),o},ou={sign:function(e){var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var o=0,i=0,a=0;a<t;++a){var u=np()(e[a][r][1])?e[a][r][0]:e[a][r][1];u>=0?(e[a][r][0]=o,e[a][r][1]=o+u,o=e[a][r][1]):(e[a][r][0]=i,e[a][r][1]=i+u,i=e[a][r][1])}},expand:function(e,t){if((n=e.length)>0){for(var r,n,o,i=0,a=e[0].length;i<a;++i){for(o=r=0;r<n;++r)o+=e[r][i][1]||0;if(o)for(r=0;r<n;++r)e[r][i][1]/=o}r1(e,t)}},none:r1,silhouette:function(e,t){if((r=e.length)>0){for(var r,n=0,o=e[t[0]],i=o.length;n<i;++n){for(var a=0,u=0;a<r;++a)u+=e[a][n][1]||0;o[n][1]+=o[n][0]=-u/2}r1(e,t)}},wiggle:function(e,t){if((o=e.length)>0&&(n=(r=e[t[0]]).length)>0){for(var r,n,o,i=0,a=1;a<n;++a){for(var u=0,c=0,l=0;u<o;++u){for(var s=e[t[u]],f=s[a][1]||0,p=(f-(s[a-1][1]||0))/2,d=0;d<u;++d){var h=e[t[d]];p+=(h[a][1]||0)-(h[a-1][1]||0)}c+=f,l+=p*f}r[a-1][1]+=r[a-1][0]=i,c&&(i-=l/c)}r[a-1][1]+=r[a-1][0]=i,r1(e,t)}},positive:function(e){var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var o=0,i=0;i<t;++i){var a=np()(e[i][r][1])?e[i][r][0]:e[i][r][1];a>=0?(e[i][r][0]=o,e[i][r][1]=o+a,o=e[i][r][1]):(e[i][r][0]=0,e[i][r][1]=0)}}},oc=function(e,t,r){var n=t.map(function(e){return e.props.dataKey}),o=ou[r];return(function(){var e=(0,r5.A)([]),t=r3,r=r1,n=r8;function o(o){var i,a,u=Array.from(e.apply(this,arguments),r9),c=u.length,l=-1;for(let e of o)for(i=0,++l;i<c;++i)(u[i][l]=[0,+n(e,u[i].key,l,o)]).data=e;for(i=0,a=(0,r2.A)(t(u));i<c;++i)u[a[i]].index=i;return r(u,a),u}return o.keys=function(t){return arguments.length?(e="function"==typeof t?t:(0,r5.A)(Array.from(t)),o):e},o.value=function(e){return arguments.length?(n="function"==typeof e?e:(0,r5.A)(+e),o):n},o.order=function(e){return arguments.length?(t=null==e?r3:"function"==typeof e?e:(0,r5.A)(Array.from(e)),o):t},o.offset=function(e){return arguments.length?(r=null==e?r1:e,o):r},o})().keys(n).value(function(e,t){return+nJ(e,t,0)}).order(r3).offset(o)(e)},ol=function(e,t,r,n,o,i){if(!e)return null;var a=(i?t.reverse():t).reduce(function(e,t){var o,i=null!=(o=t.type)&&o.defaultProps?nG(nG({},t.type.defaultProps),t.props):t.props,a=i.stackId;if(i.hide)return e;var u=i[r],c=e[u]||{hasStack:!1,stackGroups:{}};if((0,n$.vh)(a)){var l=c.stackGroups[a]||{numericAxisId:r,cateAxisId:n,items:[]};l.items.push(t),c.hasStack=!0,c.stackGroups[a]=l}else c.stackGroups[(0,n$.NF)("_stackId_")]={numericAxisId:r,cateAxisId:n,items:[t]};return nG(nG({},e),{},nK({},u,c))},{});return Object.keys(a).reduce(function(t,i){var u=a[i];return u.hasStack&&(u.stackGroups=Object.keys(u.stackGroups).reduce(function(t,i){var a=u.stackGroups[i];return nG(nG({},t),{},nK({},i,{numericAxisId:r,cateAxisId:n,items:a.items,stackedData:oc(e,a.items,o)}))},{})),nG(nG({},t),{},nK({},i,u))},{})},os=function(e,t){var r=t.realScaleType,n=t.type,o=t.tickCount,i=t.originalDomain,a=t.allowDecimals,u=r||t.scale;if("auto"!==u&&"linear"!==u)return null;if(o&&"number"===n&&i&&("auto"===i[0]||"auto"===i[1])){var c=e.domain();if(!c.length)return null;var l=nF(c,o,a);return e.domain([ne()(l),r4()(l)]),{niceTicks:l}}return o&&"number"===n?{niceTicks:nz(e.domain(),o,a)}:null},of=function(e){var t=e.axis,r=e.ticks,n=e.offset,o=e.bandSize,i=e.entry,a=e.index;if("category"===t.type)return r[a]?r[a].coordinate+n:null;var u=nJ(i,t.dataKey,t.domain[a]);return nr()(u)?null:t.scale(u)-o/2+n},op=function(e){var t=e.numericAxis,r=t.scale.domain();if("number"===t.type){var n=Math.min(r[0],r[1]),o=Math.max(r[0],r[1]);return n<=0&&o>=0?0:o<0?o:n}return r[0]},od=function(e,t){var r,n=(null!=(r=e.type)&&r.defaultProps?nG(nG({},e.type.defaultProps),e.props):e.props).stackId;if((0,n$.vh)(n)){var o=t[n];if(o){var i=o.items.indexOf(e);return i>=0?o.stackedData[i]:null}}return null},oh=function(e,t,r){return Object.keys(e).reduce(function(n,o){var i=e[o].stackedData.reduce(function(e,n){var o=n.slice(t,r+1).reduce(function(e,t){return[ne()(t.concat([e[0]]).filter(n$.Et)),r4()(t.concat([e[1]]).filter(n$.Et))]},[1/0,-1/0]);return[Math.min(e[0],o[0]),Math.max(e[1],o[1])]},[1/0,-1/0]);return[Math.min(i[0],n[0]),Math.max(i[1],n[1])]},[1/0,-1/0]).map(function(e){return e===1/0||e===-1/0?0:e})},oy=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,ov=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,om=function(e,t,r){if(no()(e))return e(t,r);if(!Array.isArray(e))return t;var n=[];if((0,n$.Et)(e[0]))n[0]=r?e[0]:Math.min(e[0],t[0]);else if(oy.test(e[0])){var o=+oy.exec(e[0])[1];n[0]=t[0]-o}else no()(e[0])?n[0]=e[0](t[0]):n[0]=t[0];if((0,n$.Et)(e[1]))n[1]=r?e[1]:Math.max(e[1],t[1]);else if(ov.test(e[1])){var i=+ov.exec(e[1])[1];n[1]=t[1]+i}else no()(e[1])?n[1]=e[1](t[1]):n[1]=t[1];return n},ob=function(e,t,r){if(e&&e.scale&&e.scale.bandwidth){var n=e.scale.bandwidth();if(!r||n>0)return n}if(e&&t&&t.length>=2){for(var o=nb()(t,function(e){return e.coordinate}),i=1/0,a=1,u=o.length;a<u;a++){var c=o[a],l=o[a-1];i=Math.min((c.coordinate||0)-(l.coordinate||0),i)}return i===1/0?0:i}return r?void 0:0},og=function(e,t,r){return!e||!e.length||nv()(e,nc()(r,"type.defaultProps.domain"))?t:e},ox=function(e,t){var r=e.type.defaultProps?nG(nG({},e.type.defaultProps),e.props):e.props,n=r.dataKey,o=r.name,i=r.unit,a=r.formatter,u=r.tooltipType,c=r.chartType,l=r.hide;return nG(nG({},(0,nY.J9)(e,!1)),{},{dataKey:n,unit:i,formatter:a,name:o||n,color:n1(e),value:nJ(t,n),type:u,payload:t,chartType:c,hide:l})}},13052:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},13122:(e,t,r)=>{var n=r(40139),o=r(38985),i=r(67460),a=r(7512),u=/^\[object .+?Constructor\]$/,c=Object.prototype,l=Function.prototype.toString,s=c.hasOwnProperty,f=RegExp("^"+l.call(s).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");e.exports=function(e){return!(!i(e)||o(e))&&(n(e)?f:u).test(a(e))}},13364:(e,t,r)=>{var n=r(75899),o=Object.prototype.hasOwnProperty;e.exports=function(e){var t=this.__data__;return n?void 0!==t[e]:o.call(t,e)}},13465:e=>{e.exports=function(e){return e}},13908:(e,t,r)=>{var n=r(40566);e.exports=function(e){return n(e)&&e!=+e}},14268:(e,t,r)=>{var n=r(58918),o=r(18028),i=r(52521);e.exports=function(e,t){return e&&e.length?n(e,o(t,2),i):void 0}},15232:(e,t,r)=>{"use strict";function n(e,t){for(var r in e)if(({}).hasOwnProperty.call(e,r)&&(!({}).hasOwnProperty.call(t,r)||e[r]!==t[r]))return!1;for(var n in t)if(({}).hasOwnProperty.call(t,n)&&!({}).hasOwnProperty.call(e,n))return!1;return!0}r.d(t,{b:()=>n})},15438:(e,t,r)=>{var n=r(98233),o=r(39608),i=r(48611);e.exports=function(e){return"string"==typeof e||!o(e)&&i(e)&&"[object String]"==n(e)}},15473:e=>{e.exports=function(e,t){return null==e?void 0:e[t]}},15631:e=>{e.exports=function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=0x1fffffffffffff}},16238:(e,t,r)=>{"use strict";r.d(t,{h:()=>v});var n=r(12115),o=r(52596),i=r(50091),a=r(45167),u=r(12814);function c(e){return(c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function l(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(l=function(){return!!e})()}function s(e){return(s=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function f(e,t){return(f=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function p(e,t,r){return(t=d(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function d(e){var t=function(e,t){if("object"!=c(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=c(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==c(t)?t:t+""}function h(){return(h=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var y=function(e){var t=e.yAxisId,r=(0,i.yi)(),c=(0,i.rY)(),l=(0,i.Nk)(t);return null==l?null:n.createElement(a.u,h({},l,{className:(0,o.A)("recharts-".concat(l.axisType," ").concat(l.axisType),l.className),viewBox:{x:0,y:0,width:r,height:c},ticksGenerator:function(e){return(0,u.Rh)(e,!0)}}))},v=function(e){var t;function r(){var e,t;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return e=r,t=arguments,e=s(e),function(e,t){if(t&&("object"===c(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");var r=e;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,l()?Reflect.construct(e,t||[],s(this).constructor):e.apply(this,t))}if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(e&&e.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),e&&f(r,e),t=[{key:"render",value:function(){return n.createElement(y,this.props)}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,d(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(n.Component);p(v,"displayName","YAxis"),p(v,"defaultProps",{allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1})},16377:(e,t,r)=>{"use strict";r.d(t,{CG:()=>g,Dj:()=>x,Et:()=>d,F4:()=>m,NF:()=>v,_3:()=>p,ck:()=>O,eP:()=>w,lX:()=>b,sA:()=>f,vh:()=>h});var n=r(15438),o=r.n(n),i=r(13908),a=r.n(i),u=r(48973),c=r.n(u),l=r(40566),s=r.n(l),f=function(e){return 0===e?0:e>0?1:-1},p=function(e){return o()(e)&&e.indexOf("%")===e.length-1},d=function(e){return s()(e)&&!a()(e)},h=function(e){return d(e)||o()(e)},y=0,v=function(e){var t=++y;return"".concat(e||"").concat(t)},m=function(e,t){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!d(e)&&!o()(e))return n;if(p(e)){var u=e.indexOf("%");r=t*parseFloat(e.slice(0,u))/100}else r=+e;return a()(r)&&(r=n),i&&r>t&&(r=t),r},b=function(e){if(!e)return null;var t=Object.keys(e);return t&&t.length?e[t[0]]:null},g=function(e){if(!Array.isArray(e))return!1;for(var t=e.length,r={},n=0;n<t;n++)if(r[e[n]])return!0;else r[e[n]]=!0;return!1},x=function(e,t){return d(e)&&d(t)?function(r){return e+r*(t-e)}:function(){return t}};function w(e,t,r){return e&&e.length?e.find(function(e){return e&&("function"==typeof t?t(e):c()(e,t))===r}):null}var O=function(e,t){return d(e)&&d(t)?e-t:o()(e)&&o()(t)?e.localeCompare(t):e instanceof Date&&t instanceof Date?e.getTime()-t.getTime():String(e).localeCompare(String(t))}},16571:(e,t,r)=>{var n=r(50687),o=r(54906),i=r(13465);e.exports=o?function(e,t){return o(e,"toString",{configurable:!0,enumerable:!1,value:n(t),writable:!0})}:i},16613:(e,t,r)=>{var n=r(24376),o=r(57213),i=r(39608),a=r(70771),u=1/0,c=n?n.prototype:void 0,l=c?c.toString:void 0;e.exports=function e(t){if("string"==typeof t)return t;if(i(t))return o(t,e)+"";if(a(t))return l?l.call(t):"";var r=t+"";return"0"==r&&1/t==-u?"-0":r}},16746:(e,t,r)=>{var n=r(3562),o=r(12486),i=r(69806);e.exports=function(e,t,r){return t==t?i(e,t,r):n(e,o,r)}},17489:(e,t,r)=>{var n=r(91113);e.exports=function(e){var t=n(e,function(e){return 500===r.size&&r.clear(),e}),r=t.cache;return t}},17855:e=>{var t="\ud800-\udfff",r="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",n="\ud83c[\udffb-\udfff]",o="[^"+t+"]",i="(?:\ud83c[\udde6-\uddff]){2}",a="[\ud800-\udbff][\udc00-\udfff]",u="(?:"+r+"|"+n+")?",c="[\\ufe0e\\ufe0f]?",l="(?:\\u200d(?:"+[o,i,a].join("|")+")"+c+u+")*",s=RegExp(n+"(?="+n+")|"+("(?:"+[o+r+"?",r,i,a,"["+t+"]"].join("|"))+")"+(c+u+l),"g");e.exports=function(e){return e.match(s)||[]}},18028:(e,t,r)=>{var n=r(55910),o=r(96699),i=r(13465),a=r(39608),u=r(28126);e.exports=function(e){return"function"==typeof e?e:null==e?i:"object"==typeof e?a(e)?o(e[0],e[1]):n(e):u(e)}},18489:(e,t,r)=>{var n=r(96294),o=r(72043),i=Object.prototype.hasOwnProperty;e.exports=function(e){if(!n(e))return o(e);var t=[];for(var r in Object(e))i.call(e,r)&&"constructor"!=r&&t.push(r);return t}},18686:(e,t,r)=>{e.exports=r(83711)(r(82500),"Map")},18940:(e,t,r)=>{e.exports=r(64189)()},20134:(e,t,r)=>{var n=r(86452),o=r(50111);e.exports=function(e,t){return null!=e&&o(e,t,n)}},20480:(e,t,r)=>{var n=r(86216),o=r(35095);e.exports=function(e,t){return e&&n(e,t,o)}},20570:(e,t,r)=>{var n=r(24376),o=Object.prototype,i=o.hasOwnProperty,a=o.toString,u=n?n.toStringTag:void 0;e.exports=function(e){var t=i.call(e,u),r=e[u];try{e[u]=void 0;var n=!0}catch(e){}var o=a.call(e);return n&&(t?e[u]=r:delete e[u]),o}},20963:(e,t,r)=>{var n=r(65646),o=r(38649),i=r(35095);e.exports=function(e){return n(e,i,o)}},20988:(e,t,r)=>{var n=r(75899);e.exports=function(e,t){var r=this.__data__;return this.size+=+!this.has(e),r[e]=n&&void 0===t?"__lodash_hash_undefined__":t,this}},21087:(e,t,r)=>{var n=r(13465),o=r(64588),i=r(61632);e.exports=function(e,t){return i(o(e,t,n),e+"")}},21582:e=>{e.exports=function(e,t){return e>t}},21790:(e,t,r)=>{var n=r(54360);e.exports=function(e,t){var r=this.__data__,o=n(r,e);return o<0?(++this.size,r.push([e,t])):r[o][1]=t,this}},22143:(e,t,r)=>{var n=r(98233),o=r(48611);e.exports=function(e){return o(e)&&"[object Arguments]"==n(e)}},22315:(e,t,r)=>{var n=r(58918),o=r(21582),i=r(13465);e.exports=function(e){return e&&e.length?n(e,i,o):void 0}},22471:(e,t,r)=>{var n=r(40139),o=r(15631);e.exports=function(e){return null!=e&&o(e.length)&&!n(e)}},23360:(e,t,r)=>{var n=r(42233);e.exports=function(e){var t=n(e),r=t%1;return t==t?r?t-r:t:0}},23633:(e,t,r)=>{e.exports=r(62962)("toUpperCase")},24026:(e,t,r)=>{"use strict";r.d(t,{s:()=>D});var n=r(12115),o=r(40139),i=r.n(o),a=r(52596),u=r(675),c=r(72790),l=r(9795),s=r(43597);function f(e){return(f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function p(){return(p=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function d(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function h(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(h=function(){return!!e})()}function y(e){return(y=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function v(e,t){return(v=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function m(e,t,r){return(t=b(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function b(e){var t=function(e,t){if("object"!=f(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=f(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==f(t)?t:t+""}var g=function(e){var t;function r(){var e,t;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return e=r,t=arguments,e=y(e),function(e,t){if(t&&("object"===f(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");var r=e;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,h()?Reflect.construct(e,t||[],y(this).constructor):e.apply(this,t))}if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(e&&e.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),e&&v(r,e),t=[{key:"renderIcon",value:function(e){var t=this.props.inactiveColor,r=32/6,o=32/3,i=e.inactive?t:e.color;if("plainline"===e.type)return n.createElement("line",{strokeWidth:4,fill:"none",stroke:i,strokeDasharray:e.payload.strokeDasharray,x1:0,y1:16,x2:32,y2:16,className:"recharts-legend-icon"});if("line"===e.type)return n.createElement("path",{strokeWidth:4,fill:"none",stroke:i,d:"M0,".concat(16,"h").concat(o,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(2*o,",").concat(16,"\n            H").concat(32,"M").concat(2*o,",").concat(16,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(o,",").concat(16),className:"recharts-legend-icon"});if("rect"===e.type)return n.createElement("path",{stroke:"none",fill:i,d:"M0,".concat(4,"h").concat(32,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(n.isValidElement(e.legendIcon)){var a=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?d(Object(r),!0).forEach(function(t){m(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):d(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({},e);return delete a.legendIcon,n.cloneElement(e.legendIcon,a)}return n.createElement(l.i,{fill:i,cx:16,cy:16,size:32,sizeType:"diameter",type:e.type})}},{key:"renderItems",value:function(){var e=this,t=this.props,r=t.payload,o=t.iconSize,l=t.layout,f=t.formatter,d=t.inactiveColor,h={x:0,y:0,width:32,height:32},y={display:"horizontal"===l?"inline-block":"block",marginRight:10},v={display:"inline-block",verticalAlign:"middle",marginRight:4};return r.map(function(t,r){var l=t.formatter||f,b=(0,a.A)(m(m({"recharts-legend-item":!0},"legend-item-".concat(r),!0),"inactive",t.inactive));if("none"===t.type)return null;var g=i()(t.value)?null:t.value;(0,u.R)(!i()(t.value),'The name property is also required when using a function for the dataKey of a chart\'s cartesian components. Ex: <Bar name="Name of my Data"/>');var x=t.inactive?d:t.color;return n.createElement("li",p({className:b,style:y,key:"legend-item-".concat(r)},(0,s.XC)(e.props,t,r)),n.createElement(c.u,{width:o,height:o,viewBox:h,style:v},e.renderIcon(t)),n.createElement("span",{className:"recharts-legend-item-text",style:{color:x}},l?l(g,t,r):g))})}},{key:"render",value:function(){var e=this.props,t=e.payload,r=e.layout,o=e.align;return t&&t.length?n.createElement("ul",{className:"recharts-default-legend",style:{padding:0,margin:0,textAlign:"horizontal"===r?o:"left"}},this.renderItems()):null}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,b(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(n.PureComponent);m(g,"displayName","Legend"),m(g,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var x=r(16377),w=r(2494);function O(e){return(O="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var j=["ref"];function S(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function E(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?S(Object(r),!0).forEach(function(t){T(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):S(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function A(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,_(n.key),n)}}function P(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(P=function(){return!!e})()}function k(e){return(k=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function M(e,t){return(M=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function T(e,t,r){return(t=_(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function _(e){var t=function(e,t){if("object"!=O(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=O(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==O(t)?t:t+""}function C(e){return e.value}var D=function(e){var t,r;function o(){var e,t,r;if(!(this instanceof o))throw TypeError("Cannot call a class as a function");for(var n=arguments.length,i=Array(n),a=0;a<n;a++)i[a]=arguments[a];return t=o,r=[].concat(i),t=k(t),T(e=function(e,t){if(t&&("object"===O(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");var r=e;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,P()?Reflect.construct(t,r||[],k(this).constructor):t.apply(this,r)),"lastBoundingBox",{width:-1,height:-1}),e}if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");return o.prototype=Object.create(e&&e.prototype,{constructor:{value:o,writable:!0,configurable:!0}}),Object.defineProperty(o,"prototype",{writable:!1}),e&&M(o,e),t=[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var e=this.wrapperNode.getBoundingClientRect();return e.height=this.wrapperNode.offsetHeight,e.width=this.wrapperNode.offsetWidth,e}return null}},{key:"updateBBox",value:function(){var e=this.props.onBBoxUpdate,t=this.getBBox();t?(Math.abs(t.width-this.lastBoundingBox.width)>1||Math.abs(t.height-this.lastBoundingBox.height)>1)&&(this.lastBoundingBox.width=t.width,this.lastBoundingBox.height=t.height,e&&e(t)):(-1!==this.lastBoundingBox.width||-1!==this.lastBoundingBox.height)&&(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,e&&e(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?E({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(e){var t,r,n=this.props,o=n.layout,i=n.align,a=n.verticalAlign,u=n.margin,c=n.chartWidth,l=n.chartHeight;return e&&(void 0!==e.left&&null!==e.left||void 0!==e.right&&null!==e.right)||(t="center"===i&&"vertical"===o?{left:((c||0)-this.getBBoxSnapshot().width)/2}:"right"===i?{right:u&&u.right||0}:{left:u&&u.left||0}),e&&(void 0!==e.top&&null!==e.top||void 0!==e.bottom&&null!==e.bottom)||(r="middle"===a?{top:((l||0)-this.getBBoxSnapshot().height)/2}:"bottom"===a?{bottom:u&&u.bottom||0}:{top:u&&u.top||0}),E(E({},t),r)}},{key:"render",value:function(){var e=this,t=this.props,r=t.content,o=t.width,i=t.height,a=t.wrapperStyle,u=t.payloadUniqBy,c=t.payload,l=E(E({position:"absolute",width:o||"auto",height:i||"auto"},this.getDefaultPosition(a)),a);return n.createElement("div",{className:"recharts-legend-wrapper",style:l,ref:function(t){e.wrapperNode=t}},function(e,t){if(n.isValidElement(e))return n.cloneElement(e,t);if("function"==typeof e)return n.createElement(e,t);t.ref;var r=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(t,j);return n.createElement(g,r)}(r,E(E({},this.props),{},{payload:(0,w.s)(c,u,C)})))}}],r=[{key:"getWithHeight",value:function(e,t){var r=E(E({},this.defaultProps),e.props).layout;return"vertical"===r&&(0,x.Et)(e.props.height)?{height:e.props.height}:"horizontal"===r?{width:e.props.width||t}:null}}],t&&A(o.prototype,t),r&&A(o,r),Object.defineProperty(o,"prototype",{writable:!1}),o}(n.PureComponent);T(D,"displayName","Legend"),T(D,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"})},24376:(e,t,r)=>{e.exports=r(82500).Symbol},25641:(e,t,r)=>{"use strict";r.d(t,{IZ:()=>v,Kg:()=>y,Zk:()=>j,lY:()=>m,pr:()=>b,yy:()=>O});var n=r(59882),o=r.n(n),i=r(12115),a=r(40139),u=r.n(a),c=r(16377),l=r(12814);function s(e){return(s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function f(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function p(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?f(Object(r),!0).forEach(function(t){d(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function d(e,t,r){var n;return(n=function(e,t){if("object"!=s(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=s(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"==s(n)?n:n+"")in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function h(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var y=Math.PI/180,v=function(e,t,r,n){return{x:e+Math.cos(-y*n)*r,y:t+Math.sin(-y*n)*r}},m=function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{top:0,right:0,bottom:0,left:0};return Math.min(Math.abs(e-(r.left||0)-(r.right||0)),Math.abs(t-(r.top||0)-(r.bottom||0)))/2},b=function(e,t,r,n,i){var a=e.width,u=e.height,s=e.startAngle,f=e.endAngle,y=(0,c.F4)(e.cx,a,a/2),v=(0,c.F4)(e.cy,u,u/2),b=m(a,u,r),g=(0,c.F4)(e.innerRadius,b,0),x=(0,c.F4)(e.outerRadius,b,.8*b);return Object.keys(t).reduce(function(e,r){var a,u=t[r],c=u.domain,m=u.reversed;if(o()(u.range))"angleAxis"===n?a=[s,f]:"radiusAxis"===n&&(a=[g,x]),m&&(a=[a[1],a[0]]);else{var b,w=function(e){if(Array.isArray(e))return e}(b=a=u.range)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{i=(r=r.call(e)).next,!1;for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(e){l=!0,o=e}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(b,2)||function(e,t){if(e){if("string"==typeof e)return h(e,2);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return h(e,t)}}(b,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();s=w[0],f=w[1]}var O=(0,l.W7)(u,i),j=O.realScaleType,S=O.scale;S.domain(c).range(a),(0,l.YB)(S);var E=(0,l.w7)(S,p(p({},u),{},{realScaleType:j})),A=p(p(p({},u),E),{},{range:a,radius:x,realScaleType:j,scale:S,cx:y,cy:v,innerRadius:g,outerRadius:x,startAngle:s,endAngle:f});return p(p({},e),{},d({},r,A))},{})},g=function(e,t){var r=e.x,n=e.y;return Math.sqrt(Math.pow(r-t.x,2)+Math.pow(n-t.y,2))},x=function(e,t){var r=e.x,n=e.y,o=t.cx,i=t.cy,a=g({x:r,y:n},{x:o,y:i});if(a<=0)return{radius:a};var u=Math.acos((r-o)/a);return n>i&&(u=2*Math.PI-u),{radius:a,angle:180*u/Math.PI,angleInRadian:u}},w=function(e){var t=e.startAngle,r=e.endAngle,n=Math.min(Math.floor(t/360),Math.floor(r/360));return{startAngle:t-360*n,endAngle:r-360*n}},O=function(e,t){var r,n=x({x:e.x,y:e.y},t),o=n.radius,i=n.angle,a=t.innerRadius,u=t.outerRadius;if(o<a||o>u)return!1;if(0===o)return!0;var c=w(t),l=c.startAngle,s=c.endAngle,f=i;if(l<=s){for(;f>s;)f-=360;for(;f<l;)f+=360;r=f>=l&&f<=s}else{for(;f>l;)f-=360;for(;f<s;)f+=360;r=f>=s&&f<=l}return r?p(p({},t),{},{radius:o,angle:f+360*Math.min(Math.floor(t.startAngle/360),Math.floor(t.endAngle/360))}):null},j=function(e){return(0,i.isValidElement)(e)||u()(e)||"boolean"==typeof e?"":e.className}},26151:e=>{e.exports=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=!!t,t}},27569:(e,t,r)=>{var n=r(32197),o=r(35095);e.exports=function(e){for(var t=o(e),r=t.length;r--;){var i=t[r],a=e[i];t[r]=[i,a,n(a)]}return t}},28126:(e,t,r)=>{var n=r(96548),o=r(93294),i=r(79595),a=r(94356);e.exports=function(e){return i(e)?n(a(e)):o(e)}},28749:(e,t,r)=>{"use strict";function n(e,t){switch(arguments.length){case 0:break;case 1:this.range(e);break;default:this.range(t).domain(e)}return this}function o(e,t){switch(arguments.length){case 0:break;case 1:"function"==typeof e?this.interpolator(e):this.range(e);break;default:this.domain(e),"function"==typeof t?this.interpolator(t):this.range(t)}return this}r.d(t,{C:()=>n,K:()=>o})},28897:(e,t,r)=>{var n=r(54906);e.exports=function(e,t,r){"__proto__"==t&&n?n(e,t,{configurable:!0,enumerable:!0,value:r,writable:!0}):e[t]=r}},29794:(e,t,r)=>{var n=r(48628),o=r(74888),i=r(18028),a=r(39608),u=r(39641);e.exports=function(e,t,r){var c=a(e)?n:o;return r&&u(e,t,r)&&(t=void 0),c(e,i(t,3))}},30152:(e,t,r)=>{e.exports=r(82500)["__core-js_shared__"]},30294:(e,t)=>{"use strict";var r,n=Symbol.for("react.element"),o=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),u=Symbol.for("react.profiler"),c=Symbol.for("react.provider"),l=Symbol.for("react.context"),s=Symbol.for("react.server_context"),f=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),d=Symbol.for("react.suspense_list"),h=Symbol.for("react.memo"),y=Symbol.for("react.lazy");Symbol.for("react.offscreen");Symbol.for("react.module.reference"),t.isFragment=function(e){return function(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case n:switch(e=e.type){case i:case u:case a:case p:case d:return e;default:switch(e=e&&e.$$typeof){case s:case l:case f:case y:case h:case c:return e;default:return t}}case o:return t}}}(e)===i}},30699:e=>{e.exports=function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e}},30716:e=>{e.exports=function(e){return function(t,r,n){for(var o=-1,i=Object(t),a=n(t),u=a.length;u--;){var c=a[e?u:++o];if(!1===r(i[c],c,i))break}return t}}},31431:e=>{e.exports=function(){}},31545:(e,t,r)=>{var n=r(75899);e.exports=function(){this.__data__=n?n(null):{},this.size=0}},31598:(e,t,r)=>{var n=r(31887),o=r(90929),i=r(45170),a=r(61830),u=r(21790);function c(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}c.prototype.clear=n,c.prototype.delete=o,c.prototype.get=i,c.prototype.has=a,c.prototype.set=u,e.exports=c},31847:(e,t,r)=>{"use strict";r.d(t,{i:()=>c});let n=Math.PI,o=2*n,i=o-1e-6;function a(e){this._+=e[0];for(let t=1,r=e.length;t<r;++t)this._+=arguments[t]+e[t]}class u{constructor(e){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==e?a:function(e){let t=Math.floor(e);if(!(t>=0))throw Error(`invalid digits: ${e}`);if(t>15)return a;let r=10**t;return function(e){this._+=e[0];for(let t=1,n=e.length;t<n;++t)this._+=Math.round(arguments[t]*r)/r+e[t]}}(e)}moveTo(e,t){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(e,t){this._append`L${this._x1=+e},${this._y1=+t}`}quadraticCurveTo(e,t,r,n){this._append`Q${+e},${+t},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(e,t,r,n,o,i){this._append`C${+e},${+t},${+r},${+n},${this._x1=+o},${this._y1=+i}`}arcTo(e,t,r,o,i){if(e*=1,t*=1,r*=1,o*=1,(i*=1)<0)throw Error(`negative radius: ${i}`);let a=this._x1,u=this._y1,c=r-e,l=o-t,s=a-e,f=u-t,p=s*s+f*f;if(null===this._x1)this._append`M${this._x1=e},${this._y1=t}`;else if(p>1e-6)if(Math.abs(f*c-l*s)>1e-6&&i){let d=r-a,h=o-u,y=c*c+l*l,v=Math.sqrt(y),m=Math.sqrt(p),b=i*Math.tan((n-Math.acos((y+p-(d*d+h*h))/(2*v*m)))/2),g=b/m,x=b/v;Math.abs(g-1)>1e-6&&this._append`L${e+g*s},${t+g*f}`,this._append`A${i},${i},0,0,${+(f*d>s*h)},${this._x1=e+x*c},${this._y1=t+x*l}`}else this._append`L${this._x1=e},${this._y1=t}`}arc(e,t,r,a,u,c){if(e*=1,t*=1,r*=1,c=!!c,r<0)throw Error(`negative radius: ${r}`);let l=r*Math.cos(a),s=r*Math.sin(a),f=e+l,p=t+s,d=1^c,h=c?a-u:u-a;null===this._x1?this._append`M${f},${p}`:(Math.abs(this._x1-f)>1e-6||Math.abs(this._y1-p)>1e-6)&&this._append`L${f},${p}`,r&&(h<0&&(h=h%o+o),h>i?this._append`A${r},${r},0,1,${d},${e-l},${t-s}A${r},${r},0,1,${d},${this._x1=f},${this._y1=p}`:h>1e-6&&this._append`A${r},${r},0,${+(h>=n)},${d},${this._x1=e+r*Math.cos(u)},${this._y1=t+r*Math.sin(u)}`)}rect(e,t,r,n){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}h${r*=1}v${+n}h${-r}Z`}toString(){return this._}}function c(e){let t=3;return e.digits=function(r){if(!arguments.length)return t;if(null==r)t=null;else{let e=Math.floor(r);if(!(e>=0))throw RangeError(`invalid digits: ${r}`);t=e}return e},()=>new u(t)}u.prototype},31887:e=>{e.exports=function(){this.__data__=[],this.size=0}},32197:(e,t,r)=>{var n=r(67460);e.exports=function(e){return e==e&&!n(e)}},33332:e=>{e.exports=function(e){return function(t){return e(t)}}},33497:(e,t,r)=>{e=r.nmd(e);var n=r(82500),o=r(44158),i=t&&!t.nodeType&&t,a=i&&e&&!e.nodeType&&e,u=a&&a.exports===i?n.Buffer:void 0,c=u?u.isBuffer:void 0;e.exports=c||o},34e3:(e,t,r)=>{"use strict";r.d(t,{F:()=>L});var n=r(12115),o=r(9557),i=r(48973),a=r.n(i),u=r(60245),c=r.n(u),l=r(59882),s=r.n(l),f=r(40139),p=r.n(f),d=r(52596),h=r(2348),y=r(70688),v=r(79095),m=r(60379),b=r(36079),g=r(54811),x=r(70788),w=r(41643),O=r(25641),j=r(16377),S=r(12814),E=r(675),A=r(43597),P=r(67790);function k(e){return(k="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function M(){return(M=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function T(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function _(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?T(Object(r),!0).forEach(function(t){R(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):T(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function C(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,B(n.key),n)}}function D(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(D=function(){return!!e})()}function N(e){return(N=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function I(e,t){return(I=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function R(e,t,r){return(t=B(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function B(e){var t=function(e,t){if("object"!=k(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=k(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==k(t)?t:t+""}var L=function(e){var t,r;function i(e){var t,r,n;if(!(this instanceof i))throw TypeError("Cannot call a class as a function");return r=i,n=[e],r=N(r),R(t=function(e,t){if(t&&("object"===k(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");var r=e;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,D()?Reflect.construct(r,n||[],N(this).constructor):r.apply(this,n)),"pieRef",null),R(t,"sectorRefs",[]),R(t,"id",(0,j.NF)("recharts-pie-")),R(t,"handleAnimationEnd",function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),p()(e)&&e()}),R(t,"handleAnimationStart",function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),p()(e)&&e()}),t.state={isAnimationFinished:!e.isAnimationActive,prevIsAnimationActive:e.isAnimationActive,prevAnimationId:e.animationId,sectorToFocus:0},t}if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");return i.prototype=Object.create(e&&e.prototype,{constructor:{value:i,writable:!0,configurable:!0}}),Object.defineProperty(i,"prototype",{writable:!1}),e&&I(i,e),t=[{key:"isActiveIndex",value:function(e){var t=this.props.activeIndex;return Array.isArray(t)?-1!==t.indexOf(e):e===t}},{key:"hasActiveIndex",value:function(){var e=this.props.activeIndex;return Array.isArray(e)?0!==e.length:e||0===e}},{key:"renderLabels",value:function(e){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var t=this.props,r=t.label,o=t.labelLine,a=t.dataKey,u=t.valueKey,c=(0,x.J9)(this.props,!1),l=(0,x.J9)(r,!1),f=(0,x.J9)(o,!1),p=r&&r.offsetRadius||20,d=e.map(function(e,t){var d=(e.startAngle+e.endAngle)/2,y=(0,O.IZ)(e.cx,e.cy,e.outerRadius+p,d),v=_(_(_(_({},c),e),{},{stroke:"none"},l),{},{index:t,textAnchor:i.getTextAnchor(y.x,e.cx)},y),m=_(_(_(_({},c),e),{},{fill:"none",stroke:e.fill},f),{},{index:t,points:[(0,O.IZ)(e.cx,e.cy,e.outerRadius,d),y]}),b=a;return s()(a)&&s()(u)?b="value":s()(a)&&(b=u),n.createElement(h.W,{key:"label-".concat(e.startAngle,"-").concat(e.endAngle,"-").concat(e.midAngle,"-").concat(t)},o&&i.renderLabelLineItem(o,m,"line"),i.renderLabelItem(r,v,(0,S.kr)(e,b)))});return n.createElement(h.W,{className:"recharts-pie-labels"},d)}},{key:"renderSectorsStatically",value:function(e){var t=this,r=this.props,o=r.activeShape,i=r.blendStroke,a=r.inactiveShape;return e.map(function(r,u){if((null==r?void 0:r.startAngle)===0&&(null==r?void 0:r.endAngle)===0&&1!==e.length)return null;var c=t.isActiveIndex(u),l=a&&t.hasActiveIndex()?a:null,s=_(_({},r),{},{stroke:i?r.fill:r.stroke,tabIndex:-1});return n.createElement(h.W,M({ref:function(e){e&&!t.sectorRefs.includes(e)&&t.sectorRefs.push(e)},tabIndex:-1,className:"recharts-pie-sector"},(0,A.XC)(t.props,r,u),{key:"sector-".concat(null==r?void 0:r.startAngle,"-").concat(null==r?void 0:r.endAngle,"-").concat(r.midAngle,"-").concat(u)}),n.createElement(P.yp,M({option:c?o:l,isActive:c,shapeType:"sector"},s)))})}},{key:"renderSectorsWithAnimation",value:function(){var e=this,t=this.props,r=t.sectors,i=t.isAnimationActive,u=t.animationBegin,c=t.animationDuration,l=t.animationEasing,s=t.animationId,f=this.state,p=f.prevSectors,d=f.prevIsAnimationActive;return n.createElement(o.Ay,{begin:u,duration:c,isActive:i,easing:l,from:{t:0},to:{t:1},key:"pie-".concat(s,"-").concat(d),onAnimationStart:this.handleAnimationStart,onAnimationEnd:this.handleAnimationEnd},function(t){var o=t.t,i=[],u=(r&&r[0]).startAngle;return r.forEach(function(e,t){var r=p&&p[t],n=t>0?a()(e,"paddingAngle",0):0;if(r){var c=(0,j.Dj)(r.endAngle-r.startAngle,e.endAngle-e.startAngle),l=_(_({},e),{},{startAngle:u+n,endAngle:u+c(o)+n});i.push(l),u=l.endAngle}else{var s=e.endAngle,f=e.startAngle,d=(0,j.Dj)(0,s-f)(o),h=_(_({},e),{},{startAngle:u+n,endAngle:u+d+n});i.push(h),u=h.endAngle}}),n.createElement(h.W,null,e.renderSectorsStatically(i))})}},{key:"attachKeyboardHandlers",value:function(e){var t=this;e.onkeydown=function(e){if(!e.altKey)switch(e.key){case"ArrowLeft":var r=++t.state.sectorToFocus%t.sectorRefs.length;t.sectorRefs[r].focus(),t.setState({sectorToFocus:r});break;case"ArrowRight":var n=--t.state.sectorToFocus<0?t.sectorRefs.length-1:t.state.sectorToFocus%t.sectorRefs.length;t.sectorRefs[n].focus(),t.setState({sectorToFocus:n});break;case"Escape":t.sectorRefs[t.state.sectorToFocus].blur(),t.setState({sectorToFocus:0})}}}},{key:"renderSectors",value:function(){var e=this.props,t=e.sectors,r=e.isAnimationActive,n=this.state.prevSectors;return r&&t&&t.length&&(!n||!c()(n,t))?this.renderSectorsWithAnimation():this.renderSectorsStatically(t)}},{key:"componentDidMount",value:function(){this.pieRef&&this.attachKeyboardHandlers(this.pieRef)}},{key:"render",value:function(){var e=this,t=this.props,r=t.hide,o=t.sectors,i=t.className,a=t.label,u=t.cx,c=t.cy,l=t.innerRadius,s=t.outerRadius,f=t.isAnimationActive,p=this.state.isAnimationFinished;if(r||!o||!o.length||!(0,j.Et)(u)||!(0,j.Et)(c)||!(0,j.Et)(l)||!(0,j.Et)(s))return null;var y=(0,d.A)("recharts-pie",i);return n.createElement(h.W,{tabIndex:this.props.rootTabIndex,className:y,ref:function(t){e.pieRef=t}},this.renderSectors(),a&&this.renderLabels(o),m.J.renderCallByParent(this.props,null,!1),(!f||p)&&b.Z.renderCallByParent(this.props,o,!1))}}],r=[{key:"getDerivedStateFromProps",value:function(e,t){return t.prevIsAnimationActive!==e.isAnimationActive?{prevIsAnimationActive:e.isAnimationActive,prevAnimationId:e.animationId,curSectors:e.sectors,prevSectors:[],isAnimationFinished:!0}:e.isAnimationActive&&e.animationId!==t.prevAnimationId?{prevAnimationId:e.animationId,curSectors:e.sectors,prevSectors:t.curSectors,isAnimationFinished:!0}:e.sectors!==t.curSectors?{curSectors:e.sectors,isAnimationFinished:!0}:null}},{key:"getTextAnchor",value:function(e,t){return e>t?"start":e<t?"end":"middle"}},{key:"renderLabelLineItem",value:function(e,t,r){if(n.isValidElement(e))return n.cloneElement(e,t);if(p()(e))return e(t);var o=(0,d.A)("recharts-pie-label-line","boolean"!=typeof e?e.className:"");return n.createElement(y.I,M({},t,{key:r,type:"linear",className:o}))}},{key:"renderLabelItem",value:function(e,t,r){if(n.isValidElement(e))return n.cloneElement(e,t);var o=r;if(p()(e)&&(o=e(t),n.isValidElement(o)))return o;var i=(0,d.A)("recharts-pie-label-text","boolean"==typeof e||p()(e)?"":e.className);return n.createElement(v.E,M({},t,{alignmentBaseline:"middle",className:i}),o)}}],t&&C(i.prototype,t),r&&C(i,r),Object.defineProperty(i,"prototype",{writable:!1}),i}(n.PureComponent);R(L,"displayName","Pie"),R(L,"defaultProps",{stroke:"#fff",fill:"#808080",legendType:"rect",cx:"50%",cy:"50%",startAngle:0,endAngle:360,innerRadius:0,outerRadius:"80%",paddingAngle:0,labelLine:!0,hide:!1,minAngle:0,isAnimationActive:!w.m.isSsr,animationBegin:400,animationDuration:1500,animationEasing:"ease",nameKey:"name",blendStroke:!1,rootTabIndex:0}),R(L,"parseDeltaAngle",function(e,t){return(0,j.sA)(t-e)*Math.min(Math.abs(t-e),360)}),R(L,"getRealPieData",function(e){var t=e.data,r=e.children,n=(0,x.J9)(e,!1),o=(0,x.aS)(r,g.f);return t&&t.length?t.map(function(e,t){return _(_(_({payload:e},n),e),o&&o[t]&&o[t].props)}):o&&o.length?o.map(function(e){return _(_({},n),e.props)}):[]}),R(L,"parseCoordinateOfPie",function(e,t){var r=t.top,n=t.left,o=t.width,i=t.height,a=(0,O.lY)(o,i);return{cx:n+(0,j.F4)(e.cx,o,o/2),cy:r+(0,j.F4)(e.cy,i,i/2),innerRadius:(0,j.F4)(e.innerRadius,a,0),outerRadius:(0,j.F4)(e.outerRadius,a,.8*a),maxRadius:e.maxRadius||Math.sqrt(o*o+i*i)/2}}),R(L,"getComposedData",function(e){var t,r,n=e.item,o=e.offset,i=void 0!==n.type.defaultProps?_(_({},n.type.defaultProps),n.props):n.props,a=L.getRealPieData(i);if(!a||!a.length)return null;var u=i.cornerRadius,c=i.startAngle,l=i.endAngle,f=i.paddingAngle,p=i.dataKey,d=i.nameKey,h=i.valueKey,y=i.tooltipType,v=Math.abs(i.minAngle),m=L.parseCoordinateOfPie(i,o),b=L.parseDeltaAngle(c,l),g=Math.abs(b),x=p;s()(p)&&s()(h)?((0,E.R)(!1,'Use "dataKey" to specify the value of pie,\n      the props "valueKey" will be deprecated in 1.1.0'),x="value"):s()(p)&&((0,E.R)(!1,'Use "dataKey" to specify the value of pie,\n      the props "valueKey" will be deprecated in 1.1.0'),x=h);var w=a.filter(function(e){return 0!==(0,S.kr)(e,x,0)}).length,A=g-w*v-(g>=360?w:w-1)*f,P=a.reduce(function(e,t){var r=(0,S.kr)(t,x,0);return e+((0,j.Et)(r)?r:0)},0);return P>0&&(t=a.map(function(e,t){var n,o=(0,S.kr)(e,x,0),i=(0,S.kr)(e,d,t),a=((0,j.Et)(o)?o:0)/P,l=(n=t?r.endAngle+(0,j.sA)(b)*f*(0!==o):c)+(0,j.sA)(b)*((0!==o?v:0)+a*A),s=(n+l)/2,p=(m.innerRadius+m.outerRadius)/2,h=[{name:i,value:o,payload:e,dataKey:x,type:y}],g=(0,O.IZ)(m.cx,m.cy,p,s);return r=_(_(_({percent:a,cornerRadius:u,name:i,tooltipPayload:h,midAngle:s,middleRadius:p,tooltipPosition:g},e),m),{},{value:(0,S.kr)(e,x),startAngle:n,endAngle:l,payload:e,paddingAngle:(0,j.sA)(b)*f})})),_(_({},m),{},{sectors:t,data:a})})},34210:e=>{e.exports=function(e){return this.__data__.has(e)}},34711:(e,t,r)=>{var n=r(73800),o=r(94356);e.exports=function(e,t){t=n(t,e);for(var r=0,i=t.length;null!=e&&r<i;)e=e[o(t[r++])];return r&&r==i?e:void 0}},35095:(e,t,r)=>{var n=r(11670),o=r(18489),i=r(22471);e.exports=function(e){return i(e)?n(e):o(e)}},35190:(e,t,r)=>{var n=r(89316),o=r(33332),i=r(49840),a=i&&i.isTypedArray;e.exports=a?o(a):n},36079:(e,t,r)=>{"use strict";r.d(t,{Z:()=>E});var n=r(12115),o=r(59882),i=r.n(o),a=r(67460),u=r.n(a),c=r(40139),l=r.n(c),s=r(83979),f=r.n(s),p=r(60379),d=r(2348),h=r(70788),y=r(12814);function v(e){return(v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var m=["valueAccessor"],b=["data","dataKey","clockWise","id","textBreakAll"];function g(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function x(){return(x=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function w(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function O(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?w(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=v(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=v(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==v(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):w(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function j(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}var S=function(e){return Array.isArray(e.value)?f()(e.value):e.value};function E(e){var t=e.valueAccessor,r=void 0===t?S:t,o=j(e,m),a=o.data,u=o.dataKey,c=o.clockWise,l=o.id,s=o.textBreakAll,f=j(o,b);return a&&a.length?n.createElement(d.W,{className:"recharts-label-list"},a.map(function(e,t){var o=i()(u)?r(e,t):(0,y.kr)(e&&e.payload,u),a=i()(l)?{}:{id:"".concat(l,"-").concat(t)};return n.createElement(p.J,x({},(0,h.J9)(e,!0),f,a,{parentViewBox:e.parentViewBox,value:o,textBreakAll:s,viewBox:p.J.parseViewBox(i()(c)?e:O(O({},e),{},{clockWise:c})),key:"label-".concat(t),index:t}))})):null}E.displayName="LabelList",E.renderCallByParent=function(e,t){var r,o=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!e||!e.children&&o&&!e.label)return null;var i=e.children,a=(0,h.aS)(i,E).map(function(e,r){return(0,n.cloneElement)(e,{data:t,key:"labelList-".concat(r)})});return o?[(r=e.label,!r?null:!0===r?n.createElement(E,{key:"labelList-implicit",data:t}):n.isValidElement(r)||l()(r)?n.createElement(E,{key:"labelList-implicit",data:t,content:r}):u()(r)?n.createElement(E,x({data:t},r,{key:"labelList-implicit"})):null)].concat(function(e){if(Array.isArray(e))return g(e)}(a)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(a)||function(e,t){if(e){if("string"==typeof e)return g(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return g(e,t)}}(a)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()):a}},36314:(e,t,r)=>{var n=r(24376),o=r(9813),i=r(39608),a=n?n.isConcatSpreadable:void 0;e.exports=function(e){return i(e)||o(e)||!!(a&&e&&e[a])}},36447:(e,t,r)=>{"use strict";r.d(t,{f:()=>h});var n=r(40139),o=r.n(n),i=r(16377),a=r(46605),u=r(41643),c=r(83455);function l(e,t,r){if(t<1)return[];if(1===t&&void 0===r)return e;for(var n=[],o=0;o<e.length;o+=t)if(void 0!==r&&!0!==r(e[o]))return;else n.push(e[o]);return n}function s(e,t,r,n,o){if(e*t<e*n||e*t>e*o)return!1;var i=r();return e*(t-e*i/2-n)>=0&&e*(t+e*i/2-o)<=0}function f(e){return(f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function p(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function d(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?p(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=f(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=f(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==f(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):p(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function h(e,t,r){var n,f,p,h,y,v=e.tick,m=e.ticks,b=e.viewBox,g=e.minTickGap,x=e.orientation,w=e.interval,O=e.tickFormatter,j=e.unit,S=e.angle;if(!m||!m.length||!v)return[];if((0,i.Et)(w)||u.m.isSsr)return l(m,("number"==typeof w&&(0,i.Et)(w)?w:0)+1);var E=[],A="top"===x||"bottom"===x?"width":"height",P=j&&"width"===A?(0,a.Pu)(j,{fontSize:t,letterSpacing:r}):{width:0,height:0},k=function(e,n){var i,u,l=o()(O)?O(e.value,n):e.value;return"width"===A?(i=(0,a.Pu)(l,{fontSize:t,letterSpacing:r}),u={width:i.width+P.width,height:i.height+P.height},(0,c.bx)(u,S)):(0,a.Pu)(l,{fontSize:t,letterSpacing:r})[A]},M=m.length>=2?(0,i.sA)(m[1].coordinate-m[0].coordinate):1,T=(n="width"===A,f=b.x,p=b.y,h=b.width,y=b.height,1===M?{start:n?f:p,end:n?f+h:p+y}:{start:n?f+h:p+y,end:n?f:p});return"equidistantPreserveStart"===w?function(e,t,r,n,o){for(var i,a=(n||[]).slice(),u=t.start,c=t.end,f=0,p=1,d=u;p<=a.length;)if(i=function(){var t,i=null==n?void 0:n[f];if(void 0===i)return{v:l(n,p)};var a=f,h=function(){return void 0===t&&(t=r(i,a)),t},y=i.coordinate,v=0===f||s(e,y,h,d,c);v||(f=0,d=u,p+=1),v&&(d=y+e*(h()/2+o),f+=p)}())return i.v;return[]}(M,T,k,m,g):("preserveStart"===w||"preserveStartEnd"===w?function(e,t,r,n,o,i){var a=(n||[]).slice(),u=a.length,c=t.start,l=t.end;if(i){var f=n[u-1],p=r(f,u-1),h=e*(f.coordinate+e*p/2-l);a[u-1]=f=d(d({},f),{},{tickCoord:h>0?f.coordinate-h*e:f.coordinate}),s(e,f.tickCoord,function(){return p},c,l)&&(l=f.tickCoord-e*(p/2+o),a[u-1]=d(d({},f),{},{isShow:!0}))}for(var y=i?u-1:u,v=function(t){var n,i=a[t],u=function(){return void 0===n&&(n=r(i,t)),n};if(0===t){var f=e*(i.coordinate-e*u()/2-c);a[t]=i=d(d({},i),{},{tickCoord:f<0?i.coordinate-f*e:i.coordinate})}else a[t]=i=d(d({},i),{},{tickCoord:i.coordinate});s(e,i.tickCoord,u,c,l)&&(c=i.tickCoord+e*(u()/2+o),a[t]=d(d({},i),{},{isShow:!0}))},m=0;m<y;m++)v(m);return a}(M,T,k,m,g,"preserveStartEnd"===w):function(e,t,r,n,o){for(var i=(n||[]).slice(),a=i.length,u=t.start,c=t.end,l=function(t){var n,l=i[t],f=function(){return void 0===n&&(n=r(l,t)),n};if(t===a-1){var p=e*(l.coordinate+e*f()/2-c);i[t]=l=d(d({},l),{},{tickCoord:p>0?l.coordinate-p*e:l.coordinate})}else i[t]=l=d(d({},l),{},{tickCoord:l.coordinate});s(e,l.tickCoord,f,u,c)&&(c=l.tickCoord-e*(f()/2+o),i[t]=d(d({},l),{},{isShow:!0}))},f=a-1;f>=0;f--)l(f);return i}(M,T,k,m,g)).filter(function(e){return e.isShow})}},36713:e=>{var t=/\s/;e.exports=function(e){for(var r=e.length;r--&&t.test(e.charAt(r)););return r}},36730:e=>{var t=Date.now;e.exports=function(e){var r=0,n=0;return function(){var o=t(),i=16-(o-n);if(n=o,i>0){if(++r>=800)return arguments[0]}else r=0;return e.apply(void 0,arguments)}}},36815:(e,t,r)=>{var n=r(4217),o=r(67460),i=r(70771),a=0/0,u=/^[-+]0x[0-9a-f]+$/i,c=/^0b[01]+$/i,l=/^0o[0-7]+$/i,s=parseInt;e.exports=function(e){if("number"==typeof e)return e;if(i(e))return a;if(o(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=o(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=n(e);var r=c.test(e);return r||l.test(e)?s(e.slice(2),r?2:8):u.test(e)?a:+e}},37835:(e,t,r)=>{var n=r(17489),o=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,i=/\\(\\)?/g;e.exports=n(function(e){var t=[];return 46===e.charCodeAt(0)&&t.push(""),e.replace(o,function(e,r,n,o){t.push(n?o.replace(i,"$1"):r||e)}),t})},37929:e=>{e.exports=function(e,t,r){var n=-1,o=e.length;t<0&&(t=-t>o?0:o+t),(r=r>o?o:r)<0&&(r+=o),o=t>r?0:r-t>>>0,t>>>=0;for(var i=Array(o);++n<o;)i[n]=e[n+t];return i}},38008:(e,t,r)=>{e.exports=r(83711)(r(82500),"Set")},38406:(e,t,r)=>{var n=r(85090),o=r(92313),i=r(82954);e.exports=function(e,t,r,a,u,c){var l=1&r,s=e.length,f=t.length;if(s!=f&&!(l&&f>s))return!1;var p=c.get(e),d=c.get(t);if(p&&d)return p==t&&d==e;var h=-1,y=!0,v=2&r?new n:void 0;for(c.set(e,t),c.set(t,e);++h<s;){var m=e[h],b=t[h];if(a)var g=l?a(b,m,h,t,e,c):a(m,b,h,e,t,c);if(void 0!==g){if(g)continue;y=!1;break}if(v){if(!o(t,function(e,t){if(!i(v,t)&&(m===e||u(m,e,r,a,c)))return v.push(t)})){y=!1;break}}else if(!(m===b||u(m,b,r,a,c))){y=!1;break}}return c.delete(e),c.delete(t),y}},38637:(e,t,r)=>{e.exports=r(79399)()},38649:(e,t,r)=>{var n=r(38675),o=r(43720),i=Object.prototype.propertyIsEnumerable,a=Object.getOwnPropertySymbols;e.exports=a?function(e){return null==e?[]:n(a(e=Object(e)),function(t){return i.call(e,t)})}:o},38675:e=>{e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length,o=0,i=[];++r<n;){var a=e[r];t(a,r,e)&&(i[o++]=a)}return i}},38985:(e,t,r)=>{var n=r(30152),o=function(){var e=/[^.]+$/.exec(n&&n.keys&&n.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}();e.exports=function(e){return!!o&&o in e}},39303:()=>{},39608:e=>{e.exports=Array.isArray},39641:(e,t,r)=>{var n=r(58817),o=r(22471),i=r(99544),a=r(67460);e.exports=function(e,t,r){if(!a(r))return!1;var u=typeof t;return("number"==u?!!(o(r)&&i(t,r.length)):"string"==u&&t in r)&&n(r[t],e)}},39984:e=>{e.exports=function(e,t,r){for(var n=-1,o=null==e?0:e.length;++n<o;)if(r(t,e[n]))return!0;return!1}},40139:(e,t,r)=>{var n=r(98233),o=r(67460);e.exports=function(e){if(!o(e))return!1;var t=n(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t}},40382:(e,t,r)=>{e.exports=r(83711)(r(82500),"WeakMap")},40566:(e,t,r)=>{var n=r(98233),o=r(48611);e.exports=function(e){return"number"==typeof e||o(e)&&"[object Number]"==n(e)}},41643:(e,t,r)=>{"use strict";r.d(t,{m:()=>n});var n={isSsr:!("undefined"!=typeof window&&window.document&&window.document.createElement&&window.setTimeout),get:function(e){return n[e]},set:function(e,t){if("string"==typeof e)n[e]=t;else{var r=Object.keys(e);r&&r.length&&r.forEach(function(t){n[t]=e[t]})}}}},42233:(e,t,r)=>{var n=r(36815),o=1/0;e.exports=function(e){return e?(e=n(e))===o||e===-o?(e<0?-1:1)*17976931348623157e292:e==e?e:0:0===e?e:0}},42355:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},43597:(e,t,r)=>{"use strict";r.d(t,{QQ:()=>u,VU:()=>l,XC:()=>p,_U:()=>f,j2:()=>s});var n=r(12115),o=r(67460),i=r.n(o);function a(e){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var u=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],c=["points","pathLength"],l={svg:["viewBox","children"],polygon:c,polyline:c},s=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],f=function(e,t){if(!e||"function"==typeof e||"boolean"==typeof e)return null;var r=e;if((0,n.isValidElement)(e)&&(r=e.props),!i()(r))return null;var o={};return Object.keys(r).forEach(function(e){s.includes(e)&&(o[e]=t||function(t){return r[e](r,t)})}),o},p=function(e,t,r){if(!i()(e)||"object"!==a(e))return null;var n=null;return Object.keys(e).forEach(function(o){var i=e[o];s.includes(o)&&"function"==typeof i&&(n||(n={}),n[o]=function(e){return i(t,r,e),null})}),n}},43720:e=>{e.exports=function(){return[]}},44101:(e,t,r)=>{e.exports=r(83711)(r(82500),"DataView")},44158:e=>{e.exports=function(){return!1}},44482:(e,t,r)=>{var n=r(98233),o=r(73726),i=r(48611),a=Object.prototype,u=Function.prototype.toString,c=a.hasOwnProperty,l=u.call(Object);e.exports=function(e){if(!i(e)||"[object Object]"!=n(e))return!1;var t=o(e);if(null===t)return!0;var r=c.call(t,"constructor")&&t.constructor;return"function"==typeof r&&r instanceof r&&u.call(r)==l}},44538:(e,t,r)=>{"use strict";r.d(t,{J:()=>d,M:()=>y});var n=r(12115),o=r(52596),i=r(9557),a=r(70788);function u(e){return(u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function c(){return(c=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function l(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function s(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function f(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?s(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=u(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=u(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==u(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var p=function(e,t,r,n,o){var i,a=Math.min(Math.abs(r)/2,Math.abs(n)/2),u=n>=0?1:-1,c=r>=0?1:-1,l=+(n>=0&&r>=0||n<0&&r<0);if(a>0&&o instanceof Array){for(var s=[0,0,0,0],f=0;f<4;f++)s[f]=o[f]>a?a:o[f];i="M".concat(e,",").concat(t+u*s[0]),s[0]>0&&(i+="A ".concat(s[0],",").concat(s[0],",0,0,").concat(l,",").concat(e+c*s[0],",").concat(t)),i+="L ".concat(e+r-c*s[1],",").concat(t),s[1]>0&&(i+="A ".concat(s[1],",").concat(s[1],",0,0,").concat(l,",\n        ").concat(e+r,",").concat(t+u*s[1])),i+="L ".concat(e+r,",").concat(t+n-u*s[2]),s[2]>0&&(i+="A ".concat(s[2],",").concat(s[2],",0,0,").concat(l,",\n        ").concat(e+r-c*s[2],",").concat(t+n)),i+="L ".concat(e+c*s[3],",").concat(t+n),s[3]>0&&(i+="A ".concat(s[3],",").concat(s[3],",0,0,").concat(l,",\n        ").concat(e,",").concat(t+n-u*s[3])),i+="Z"}else if(a>0&&o===+o&&o>0){var p=Math.min(a,o);i="M ".concat(e,",").concat(t+u*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(e+c*p,",").concat(t,"\n            L ").concat(e+r-c*p,",").concat(t,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(e+r,",").concat(t+u*p,"\n            L ").concat(e+r,",").concat(t+n-u*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(e+r-c*p,",").concat(t+n,"\n            L ").concat(e+c*p,",").concat(t+n,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(e,",").concat(t+n-u*p," Z")}else i="M ".concat(e,",").concat(t," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return i},d=function(e,t){if(!e||!t)return!1;var r=e.x,n=e.y,o=t.x,i=t.y,a=t.width,u=t.height;if(Math.abs(a)>0&&Math.abs(u)>0){var c=Math.min(o,o+a),l=Math.max(o,o+a),s=Math.min(i,i+u),f=Math.max(i,i+u);return r>=c&&r<=l&&n>=s&&n<=f}return!1},h={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},y=function(e){var t,r=f(f({},h),e),u=(0,n.useRef)(),s=function(e){if(Array.isArray(e))return e}(t=(0,n.useState)(-1))||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{i=(r=r.call(e)).next,!1;for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(e){l=!0,o=e}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(t,2)||function(e,t){if(e){if("string"==typeof e)return l(e,2);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return l(e,t)}}(t,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),d=s[0],y=s[1];(0,n.useEffect)(function(){if(u.current&&u.current.getTotalLength)try{var e=u.current.getTotalLength();e&&y(e)}catch(e){}},[]);var v=r.x,m=r.y,b=r.width,g=r.height,x=r.radius,w=r.className,O=r.animationEasing,j=r.animationDuration,S=r.animationBegin,E=r.isAnimationActive,A=r.isUpdateAnimationActive;if(v!==+v||m!==+m||b!==+b||g!==+g||0===b||0===g)return null;var P=(0,o.A)("recharts-rectangle",w);return A?n.createElement(i.Ay,{canBegin:d>0,from:{width:b,height:g,x:v,y:m},to:{width:b,height:g,x:v,y:m},duration:j,animationEasing:O,isActive:A},function(e){var t=e.width,o=e.height,l=e.x,s=e.y;return n.createElement(i.Ay,{canBegin:d>0,from:"0px ".concat(-1===d?1:d,"px"),to:"".concat(d,"px 0px"),attributeName:"strokeDasharray",begin:S,duration:j,isActive:E,easing:O},n.createElement("path",c({},(0,a.J9)(r,!0),{className:P,d:p(l,s,t,o,x),ref:u})))}):n.createElement("path",c({},(0,a.J9)(r,!0),{className:P,d:p(v,m,b,g,x)}))}},45167:(e,t,r)=>{"use strict";r.d(t,{u:()=>_});var n=r(12115),o=r(40139),i=r.n(o),a=r(48973),u=r.n(a),c=r(52596),l=r(15232),s=r(2348),f=r(79095),p=r(60379),d=r(16377),h=r(43597),y=r(70788),v=r(36447),m=["viewBox"],b=["viewBox"],g=["ticks"];function x(e){return(x="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function w(){return(w=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function O(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function j(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?O(Object(r),!0).forEach(function(t){M(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):O(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function S(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}function E(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,T(n.key),n)}}function A(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(A=function(){return!!e})()}function P(e){return(P=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function k(e,t){return(k=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function M(e,t,r){return(t=T(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function T(e){var t=function(e,t){if("object"!=x(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=x(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==x(t)?t:t+""}var _=function(e){var t,r;function o(e){var t,r,n;if(!(this instanceof o))throw TypeError("Cannot call a class as a function");return r=o,n=[e],r=P(r),(t=function(e,t){if(t&&("object"===x(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");var r=e;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,A()?Reflect.construct(r,n||[],P(this).constructor):r.apply(this,n))).state={fontSize:"",letterSpacing:""},t}if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");return o.prototype=Object.create(e&&e.prototype,{constructor:{value:o,writable:!0,configurable:!0}}),Object.defineProperty(o,"prototype",{writable:!1}),e&&k(o,e),t=[{key:"shouldComponentUpdate",value:function(e,t){var r=e.viewBox,n=S(e,m),o=this.props,i=o.viewBox,a=S(o,b);return!(0,l.b)(r,i)||!(0,l.b)(n,a)||!(0,l.b)(t,this.state)}},{key:"componentDidMount",value:function(){var e=this.layerReference;if(e){var t=e.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];t&&this.setState({fontSize:window.getComputedStyle(t).fontSize,letterSpacing:window.getComputedStyle(t).letterSpacing})}}},{key:"getTickLineCoord",value:function(e){var t,r,n,o,i,a,u=this.props,c=u.x,l=u.y,s=u.width,f=u.height,p=u.orientation,h=u.tickSize,y=u.mirror,v=u.tickMargin,m=y?-1:1,b=e.tickSize||h,g=(0,d.Et)(e.tickCoord)?e.tickCoord:e.coordinate;switch(p){case"top":t=r=e.coordinate,a=(n=(o=l+!y*f)-m*b)-m*v,i=g;break;case"left":n=o=e.coordinate,i=(t=(r=c+!y*s)-m*b)-m*v,a=g;break;case"right":n=o=e.coordinate,i=(t=(r=c+y*s)+m*b)+m*v,a=g;break;default:t=r=e.coordinate,a=(n=(o=l+y*f)+m*b)+m*v,i=g}return{line:{x1:t,y1:n,x2:r,y2:o},tick:{x:i,y:a}}}},{key:"getTickTextAnchor",value:function(){var e,t=this.props,r=t.orientation,n=t.mirror;switch(r){case"left":e=n?"start":"end";break;case"right":e=n?"end":"start";break;default:e="middle"}return e}},{key:"getTickVerticalAnchor",value:function(){var e=this.props,t=e.orientation,r=e.mirror,n="end";switch(t){case"left":case"right":n="middle";break;case"top":n=r?"start":"end";break;default:n=r?"end":"start"}return n}},{key:"renderAxisLine",value:function(){var e=this.props,t=e.x,r=e.y,o=e.width,i=e.height,a=e.orientation,l=e.mirror,s=e.axisLine,f=j(j(j({},(0,y.J9)(this.props,!1)),(0,y.J9)(s,!1)),{},{fill:"none"});if("top"===a||"bottom"===a){var p=+("top"===a&&!l||"bottom"===a&&l);f=j(j({},f),{},{x1:t,y1:r+p*i,x2:t+o,y2:r+p*i})}else{var d=+("left"===a&&!l||"right"===a&&l);f=j(j({},f),{},{x1:t+d*o,y1:r,x2:t+d*o,y2:r+i})}return n.createElement("line",w({},f,{className:(0,c.A)("recharts-cartesian-axis-line",u()(s,"className"))}))}},{key:"renderTicks",value:function(e,t,r){var a=this,l=this.props,f=l.tickLine,p=l.stroke,d=l.tick,m=l.tickFormatter,b=l.unit,g=(0,v.f)(j(j({},this.props),{},{ticks:e}),t,r),x=this.getTickTextAnchor(),O=this.getTickVerticalAnchor(),S=(0,y.J9)(this.props,!1),E=(0,y.J9)(d,!1),A=j(j({},S),{},{fill:"none"},(0,y.J9)(f,!1)),P=g.map(function(e,t){var r=a.getTickLineCoord(e),l=r.line,y=r.tick,v=j(j(j(j({textAnchor:x,verticalAnchor:O},S),{},{stroke:"none",fill:p},E),y),{},{index:t,payload:e,visibleTicksCount:g.length,tickFormatter:m});return n.createElement(s.W,w({className:"recharts-cartesian-axis-tick",key:"tick-".concat(e.value,"-").concat(e.coordinate,"-").concat(e.tickCoord)},(0,h.XC)(a.props,e,t)),f&&n.createElement("line",w({},A,l,{className:(0,c.A)("recharts-cartesian-axis-tick-line",u()(f,"className"))})),d&&o.renderTickItem(d,v,"".concat(i()(m)?m(e.value,t):e.value).concat(b||"")))});return n.createElement("g",{className:"recharts-cartesian-axis-ticks"},P)}},{key:"render",value:function(){var e=this,t=this.props,r=t.axisLine,o=t.width,a=t.height,u=t.ticksGenerator,l=t.className;if(t.hide)return null;var f=this.props,d=f.ticks,h=S(f,g),y=d;return(i()(u)&&(y=u(d&&d.length>0?this.props:h)),o<=0||a<=0||!y||!y.length)?null:n.createElement(s.W,{className:(0,c.A)("recharts-cartesian-axis",l),ref:function(t){e.layerReference=t}},r&&this.renderAxisLine(),this.renderTicks(y,this.state.fontSize,this.state.letterSpacing),p.J.renderCallByParent(this.props))}}],r=[{key:"renderTickItem",value:function(e,t,r){var o;return n.isValidElement(e)?n.cloneElement(e,t):i()(e)?e(t):n.createElement(f.E,w({},t,{className:"recharts-cartesian-axis-tick-value"}),r)}}],t&&E(o.prototype,t),r&&E(o,r),Object.defineProperty(o,"prototype",{writable:!1}),o}(n.Component);M(_,"displayName","CartesianAxis"),M(_,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"})},45170:(e,t,r)=>{var n=r(54360);e.exports=function(e){var t=this.__data__,r=n(t,e);return r<0?void 0:t[r][1]}},45964:(e,t,r)=>{var n=r(67460),o=r(76685),i=r(36815),a=Math.max,u=Math.min;e.exports=function(e,t,r){var c,l,s,f,p,d,h=0,y=!1,v=!1,m=!0;if("function"!=typeof e)throw TypeError("Expected a function");function b(t){var r=c,n=l;return c=l=void 0,h=t,f=e.apply(n,r)}function g(e){var r=e-d,n=e-h;return void 0===d||r>=t||r<0||v&&n>=s}function x(){var e,r,n,i=o();if(g(i))return w(i);p=setTimeout(x,(e=i-d,r=i-h,n=t-e,v?u(n,s-r):n))}function w(e){return(p=void 0,m&&c)?b(e):(c=l=void 0,f)}function O(){var e,r=o(),n=g(r);if(c=arguments,l=this,d=r,n){if(void 0===p)return h=e=d,p=setTimeout(x,t),y?b(e):f;if(v)return clearTimeout(p),p=setTimeout(x,t),b(d)}return void 0===p&&(p=setTimeout(x,t)),f}return t=i(t)||0,n(r)&&(y=!!r.leading,s=(v="maxWait"in r)?a(i(r.maxWait)||0,t):s,m="trailing"in r?!!r.trailing:m),O.cancel=function(){void 0!==p&&clearTimeout(p),h=0,c=d=l=p=void 0},O.flush=function(){return void 0===p?f:w(o())},O}},46605:(e,t,r)=>{"use strict";r.d(t,{A3:()=>p,Pu:()=>f});var n=r(41643);function o(e){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function i(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function a(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?i(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=o(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=o(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==o(t)?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function u(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var c={widthCache:{},cacheCount:0},l={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},s="recharts_measurement_span",f=function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==e||n.m.isSsr)return{width:0,height:0};var o=(Object.keys(t=a({},r)).forEach(function(e){t[e]||delete t[e]}),t),i=JSON.stringify({text:e,copyStyle:o});if(c.widthCache[i])return c.widthCache[i];try{var u=document.getElementById(s);u||((u=document.createElement("span")).setAttribute("id",s),u.setAttribute("aria-hidden","true"),document.body.appendChild(u));var f=a(a({},l),o);Object.assign(u.style,f),u.textContent="".concat(e);var p=u.getBoundingClientRect(),d={width:p.width,height:p.height};return c.widthCache[i]=d,++c.cacheCount>2e3&&(c.cacheCount=0,c.widthCache={}),d}catch(e){return{width:0,height:0}}},p=function(e){return{top:e.top+window.scrollY-document.documentElement.clientTop,left:e.left+window.scrollX-document.documentElement.clientLeft}}},47995:e=>{e.exports=function(e){var t=this.__data__,r=t.delete(e);return this.size=t.size,r}},48611:e=>{e.exports=function(e){return null!=e&&"object"==typeof e}},48628:e=>{e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length;++r<n;)if(!t(e[r],r,e))return!1;return!0}},48659:(e,t,r)=>{var n=r(37929);e.exports=function(e,t,r){var o=e.length;return r=void 0===r?o:r,!t&&r>=o?e:n(e,t,r)}},48973:(e,t,r)=>{var n=r(34711);e.exports=function(e,t,r){var o=null==e?void 0:n(e,t);return void 0===o?r:o}},49840:(e,t,r)=>{e=r.nmd(e);var n=r(7985),o=t&&!t.nodeType&&t,i=o&&e&&!e.nodeType&&e,a=i&&i.exports===o&&n.process,u=function(){try{var e=i&&i.require&&i.require("util").types;if(e)return e;return a&&a.binding&&a.binding("util")}catch(e){}}();e.exports=u},49872:(e,t,r)=>{var n=r(22471);e.exports=function(e,t){return function(r,o){if(null==r)return r;if(!n(r))return e(r,o);for(var i=r.length,a=t?i:-1,u=Object(r);(t?a--:++a<i)&&!1!==o(u[a],a,u););return r}}},49956:(e,t,r)=>{"use strict";r.d(t,{UC:()=>e_,ZL:()=>eT,bL:()=>ek,l9:()=>eM});var n,o=r(12115),i=r(85185),a=r(6101),u=r(46081),c=r(47650),l=r(95155);function s(e){let t=function(e){let t=o.forwardRef((e,t)=>{var r,n,i;let u,c,{children:l,...s}=e,f=o.isValidElement(l)?(c=(u=null==(n=Object.getOwnPropertyDescriptor((r=l).props,"ref"))?void 0:n.get)&&"isReactWarning"in u&&u.isReactWarning)?r.ref:(c=(u=null==(i=Object.getOwnPropertyDescriptor(r,"ref"))?void 0:i.get)&&"isReactWarning"in u&&u.isReactWarning)?r.props.ref:r.props.ref||r.ref:void 0,p=(0,a.s)(f,t);if(o.isValidElement(l)){let e=function(e,t){let r={...t};for(let n in t){let o=e[n],i=t[n];/^on[A-Z]/.test(n)?o&&i?r[n]=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let n=i(...t);return o(...t),n}:o&&(r[n]=o):"style"===n?r[n]={...o,...i}:"className"===n&&(r[n]=[o,i].filter(Boolean).join(" "))}return{...e,...r}}(s,l.props);return l.type!==o.Fragment&&(e.ref=p),o.cloneElement(l,e)}return o.Children.count(l)>1?o.Children.only(null):null});return t.displayName="".concat(e,".SlotClone"),t}(e),r=o.forwardRef((e,r)=>{let{children:n,...i}=e,a=o.Children.toArray(n),u=a.find(p);if(u){let e=u.props.children,n=a.map(t=>t!==u?t:o.Children.count(e)>1?o.Children.only(null):o.isValidElement(e)?e.props.children:null);return(0,l.jsx)(t,{...i,ref:r,children:o.isValidElement(e)?o.cloneElement(e,void 0,n):null})}return(0,l.jsx)(t,{...i,ref:r,children:n})});return r.displayName="".concat(e,".Slot"),r}var f=Symbol("radix.slottable");function p(e){return o.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===f}var d=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=s(`Primitive.${t}`),n=o.forwardRef((e,n)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(o?r:t,{...i,ref:n})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{}),h=r(39033),y=r(51595),v="dismissableLayer.update",m=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),b=o.forwardRef((e,t)=>{var r,u;let{disableOutsidePointerEvents:c=!1,onEscapeKeyDown:s,onPointerDownOutside:f,onFocusOutside:p,onInteractOutside:b,onDismiss:w,...O}=e,j=o.useContext(m),[S,E]=o.useState(null),A=null!=(u=null==S?void 0:S.ownerDocument)?u:null==(r=globalThis)?void 0:r.document,[,P]=o.useState({}),k=(0,a.s)(t,e=>E(e)),M=Array.from(j.layers),[T]=[...j.layersWithOutsidePointerEventsDisabled].slice(-1),_=M.indexOf(T),C=S?M.indexOf(S):-1,D=j.layersWithOutsidePointerEventsDisabled.size>0,N=C>=_,I=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,n=(0,h.c)(e),i=o.useRef(!1),a=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!i.current){let t=function(){x("dismissableLayer.pointerDownOutside",n,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(r.removeEventListener("click",a.current),a.current=t,r.addEventListener("click",a.current,{once:!0})):t()}else r.removeEventListener("click",a.current);i.current=!1},t=window.setTimeout(()=>{r.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),r.removeEventListener("pointerdown",e),r.removeEventListener("click",a.current)}},[r,n]),{onPointerDownCapture:()=>i.current=!0}}(e=>{let t=e.target,r=[...j.branches].some(e=>e.contains(t));N&&!r&&(null==f||f(e),null==b||b(e),e.defaultPrevented||null==w||w())},A),R=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,n=(0,h.c)(e),i=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!i.current&&x("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return r.addEventListener("focusin",e),()=>r.removeEventListener("focusin",e)},[r,n]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}(e=>{let t=e.target;![...j.branches].some(e=>e.contains(t))&&(null==p||p(e),null==b||b(e),e.defaultPrevented||null==w||w())},A);return(0,y.U)(e=>{C===j.layers.size-1&&(null==s||s(e),!e.defaultPrevented&&w&&(e.preventDefault(),w()))},A),o.useEffect(()=>{if(S)return c&&(0===j.layersWithOutsidePointerEventsDisabled.size&&(n=A.body.style.pointerEvents,A.body.style.pointerEvents="none"),j.layersWithOutsidePointerEventsDisabled.add(S)),j.layers.add(S),g(),()=>{c&&1===j.layersWithOutsidePointerEventsDisabled.size&&(A.body.style.pointerEvents=n)}},[S,A,c,j]),o.useEffect(()=>()=>{S&&(j.layers.delete(S),j.layersWithOutsidePointerEventsDisabled.delete(S),g())},[S,j]),o.useEffect(()=>{let e=()=>P({});return document.addEventListener(v,e),()=>document.removeEventListener(v,e)},[]),(0,l.jsx)(d.div,{...O,ref:k,style:{pointerEvents:D?N?"auto":"none":void 0,...e.style},onFocusCapture:(0,i.m)(e.onFocusCapture,R.onFocusCapture),onBlurCapture:(0,i.m)(e.onBlurCapture,R.onBlurCapture),onPointerDownCapture:(0,i.m)(e.onPointerDownCapture,I.onPointerDownCapture)})});function g(){let e=new CustomEvent(v);document.dispatchEvent(e)}function x(e,t,r,n){let{discrete:o}=n,i=r.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});if(t&&i.addEventListener(e,t,{once:!0}),o)i&&c.flushSync(()=>i.dispatchEvent(a));else i.dispatchEvent(a)}b.displayName="DismissableLayer",o.forwardRef((e,t)=>{let r=o.useContext(m),n=o.useRef(null),i=(0,a.s)(t,n);return o.useEffect(()=>{let e=n.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,l.jsx)(d.div,{...e,ref:i})}).displayName="DismissableLayerBranch";var w=r(92293),O="focusScope.autoFocusOnMount",j="focusScope.autoFocusOnUnmount",S={bubbles:!1,cancelable:!0},E=o.forwardRef((e,t)=>{let{loop:r=!1,trapped:n=!1,onMountAutoFocus:i,onUnmountAutoFocus:u,...c}=e,[s,f]=o.useState(null),p=(0,h.c)(i),y=(0,h.c)(u),v=o.useRef(null),m=(0,a.s)(t,e=>f(e)),b=o.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;o.useEffect(()=>{if(n){let e=function(e){if(b.paused||!s)return;let t=e.target;s.contains(t)?v.current=t:k(v.current,{select:!0})},t=function(e){if(b.paused||!s)return;let t=e.relatedTarget;null!==t&&(s.contains(t)||k(v.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let r=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&k(s)});return s&&r.observe(s,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),r.disconnect()}}},[n,s,b.paused]),o.useEffect(()=>{if(s){M.add(b);let e=document.activeElement;if(!s.contains(e)){let t=new CustomEvent(O,S);s.addEventListener(O,p),s.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=document.activeElement;for(let n of e)if(k(n,{select:t}),document.activeElement!==r)return}(A(s).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&k(s))}return()=>{s.removeEventListener(O,p),setTimeout(()=>{let t=new CustomEvent(j,S);s.addEventListener(j,y),s.dispatchEvent(t),t.defaultPrevented||k(null!=e?e:document.body,{select:!0}),s.removeEventListener(j,y),M.remove(b)},0)}}},[s,p,y,b]);let g=o.useCallback(e=>{if(!r&&!n||b.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[n,i]=function(e){let t=A(e);return[P(t,e),P(t.reverse(),e)]}(t);n&&i?e.shiftKey||o!==i?e.shiftKey&&o===n&&(e.preventDefault(),r&&k(i,{select:!0})):(e.preventDefault(),r&&k(n,{select:!0})):o===t&&e.preventDefault()}},[r,n,b.paused]);return(0,l.jsx)(d.div,{tabIndex:-1,...c,ref:m,onKeyDown:g})});function A(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function P(e,t){for(let r of e)if(!function(e,t){let{upTo:r}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===r||e!==r);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(r,{upTo:t}))return r}function k(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var r;let n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&(r=e)instanceof HTMLInputElement&&"select"in r&&t&&e.select()}}E.displayName="FocusScope";var M=function(){let e=[];return{add(t){let r=e[0];t!==r&&(null==r||r.pause()),(e=T(e,t)).unshift(t)},remove(t){var r;null==(r=(e=T(e,t))[0])||r.resume()}}}();function T(e,t){let r=[...e],n=r.indexOf(t);return -1!==n&&r.splice(n,1),r}var _=r(61285),C=r(84945),D=r(22475),N=o.forwardRef((e,t)=>{let{children:r,width:n=10,height:o=5,...i}=e;return(0,l.jsx)(d.svg,{...i,ref:t,width:n,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?r:(0,l.jsx)("polygon",{points:"0,0 30,0 15,10"})})});N.displayName="Arrow";var I=r(52712),R=r(11275),B="Popper",[L,W]=(0,u.A)(B),[F,z]=L(B),U=e=>{let{__scopePopper:t,children:r}=e,[n,i]=o.useState(null);return(0,l.jsx)(F,{scope:t,anchor:n,onAnchorChange:i,children:r})};U.displayName=B;var $="PopperAnchor",Y=o.forwardRef((e,t)=>{let{__scopePopper:r,virtualRef:n,...i}=e,u=z($,r),c=o.useRef(null),s=(0,a.s)(t,c);return o.useEffect(()=>{u.onAnchorChange((null==n?void 0:n.current)||c.current)}),n?null:(0,l.jsx)(d.div,{...i,ref:s})});Y.displayName=$;var H="PopperContent",[q,X]=L(H),Z=o.forwardRef((e,t)=>{var r,n,i,u,c,s,f,p;let{__scopePopper:y,side:v="bottom",sideOffset:m=0,align:b="center",alignOffset:g=0,arrowPadding:x=0,avoidCollisions:w=!0,collisionBoundary:O=[],collisionPadding:j=0,sticky:S="partial",hideWhenDetached:E=!1,updatePositionStrategy:A="optimized",onPlaced:P,...k}=e,M=z(H,y),[T,_]=o.useState(null),N=(0,a.s)(t,e=>_(e)),[B,L]=o.useState(null),W=(0,R.X)(B),F=null!=(f=null==W?void 0:W.width)?f:0,U=null!=(p=null==W?void 0:W.height)?p:0,$="number"==typeof j?j:{top:0,right:0,bottom:0,left:0,...j},Y=Array.isArray(O)?O:[O],X=Y.length>0,Z={padding:$,boundary:Y.filter(J),altBoundary:X},{refs:V,floatingStyles:G,placement:K,isPositioned:et,middlewareData:er}=(0,C.we)({strategy:"fixed",placement:v+("center"!==b?"-"+b:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,D.ll)(...t,{animationFrame:"always"===A})},elements:{reference:M.anchor},middleware:[(0,C.cY)({mainAxis:m+U,alignmentAxis:g}),w&&(0,C.BN)({mainAxis:!0,crossAxis:!1,limiter:"partial"===S?(0,C.ER)():void 0,...Z}),w&&(0,C.UU)({...Z}),(0,C.Ej)({...Z,apply:e=>{let{elements:t,rects:r,availableWidth:n,availableHeight:o}=e,{width:i,height:a}=r.reference,u=t.floating.style;u.setProperty("--radix-popper-available-width","".concat(n,"px")),u.setProperty("--radix-popper-available-height","".concat(o,"px")),u.setProperty("--radix-popper-anchor-width","".concat(i,"px")),u.setProperty("--radix-popper-anchor-height","".concat(a,"px"))}}),B&&(0,C.UE)({element:B,padding:x}),Q({arrowWidth:F,arrowHeight:U}),E&&(0,C.jD)({strategy:"referenceHidden",...Z})]}),[en,eo]=ee(K),ei=(0,h.c)(P);(0,I.N)(()=>{et&&(null==ei||ei())},[et,ei]);let ea=null==(r=er.arrow)?void 0:r.x,eu=null==(n=er.arrow)?void 0:n.y,ec=(null==(i=er.arrow)?void 0:i.centerOffset)!==0,[el,es]=o.useState();return(0,I.N)(()=>{T&&es(window.getComputedStyle(T).zIndex)},[T]),(0,l.jsx)("div",{ref:V.setFloating,"data-radix-popper-content-wrapper":"",style:{...G,transform:et?G.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:el,"--radix-popper-transform-origin":[null==(u=er.transformOrigin)?void 0:u.x,null==(c=er.transformOrigin)?void 0:c.y].join(" "),...(null==(s=er.hide)?void 0:s.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,l.jsx)(q,{scope:y,placedSide:en,onArrowChange:L,arrowX:ea,arrowY:eu,shouldHideArrow:ec,children:(0,l.jsx)(d.div,{"data-side":en,"data-align":eo,...k,ref:N,style:{...k.style,animation:et?void 0:"none"}})})})});Z.displayName=H;var V="PopperArrow",G={top:"bottom",right:"left",bottom:"top",left:"right"},K=o.forwardRef(function(e,t){let{__scopePopper:r,...n}=e,o=X(V,r),i=G[o.placedSide];return(0,l.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,l.jsx)(N,{...n,ref:t,style:{...n.style,display:"block"}})})});function J(e){return null!==e}K.displayName=V;var Q=e=>({name:"transformOrigin",options:e,fn(t){var r,n,o,i,a;let{placement:u,rects:c,middlewareData:l}=t,s=(null==(r=l.arrow)?void 0:r.centerOffset)!==0,f=s?0:e.arrowWidth,p=s?0:e.arrowHeight,[d,h]=ee(u),y={start:"0%",center:"50%",end:"100%"}[h],v=(null!=(i=null==(n=l.arrow)?void 0:n.x)?i:0)+f/2,m=(null!=(a=null==(o=l.arrow)?void 0:o.y)?a:0)+p/2,b="",g="";return"bottom"===d?(b=s?y:"".concat(v,"px"),g="".concat(-p,"px")):"top"===d?(b=s?y:"".concat(v,"px"),g="".concat(c.floating.height+p,"px")):"right"===d?(b="".concat(-p,"px"),g=s?y:"".concat(m,"px")):"left"===d&&(b="".concat(c.floating.width+p,"px"),g=s?y:"".concat(m,"px")),{data:{x:b,y:g}}}});function ee(e){let[t,r="center"]=e.split("-");return[t,r]}var et=o.forwardRef((e,t)=>{var r,n;let{container:i,...a}=e,[u,s]=o.useState(!1);(0,I.N)(()=>s(!0),[]);let f=i||u&&(null==(n=globalThis)||null==(r=n.document)?void 0:r.body);return f?c.createPortal((0,l.jsx)(d.div,{...a,ref:t}),f):null});et.displayName="Portal";var er=r(28905),en=r(5845),eo=r(38168),ei=r(31114),ea="Popover",[eu,ec]=(0,u.A)(ea,[W]),el=W(),[es,ef]=eu(ea),ep=e=>{let{__scopePopover:t,children:r,open:n,defaultOpen:i,onOpenChange:a,modal:u=!1}=e,c=el(t),s=o.useRef(null),[f,p]=o.useState(!1),[d,h]=(0,en.i)({prop:n,defaultProp:null!=i&&i,onChange:a,caller:ea});return(0,l.jsx)(U,{...c,children:(0,l.jsx)(es,{scope:t,contentId:(0,_.B)(),triggerRef:s,open:d,onOpenChange:h,onOpenToggle:o.useCallback(()=>h(e=>!e),[h]),hasCustomAnchor:f,onCustomAnchorAdd:o.useCallback(()=>p(!0),[]),onCustomAnchorRemove:o.useCallback(()=>p(!1),[]),modal:u,children:r})})};ep.displayName=ea;var ed="PopoverAnchor";o.forwardRef((e,t)=>{let{__scopePopover:r,...n}=e,i=ef(ed,r),a=el(r),{onCustomAnchorAdd:u,onCustomAnchorRemove:c}=i;return o.useEffect(()=>(u(),()=>c()),[u,c]),(0,l.jsx)(Y,{...a,...n,ref:t})}).displayName=ed;var eh="PopoverTrigger",ey=o.forwardRef((e,t)=>{let{__scopePopover:r,...n}=e,o=ef(eh,r),u=el(r),c=(0,a.s)(t,o.triggerRef),s=(0,l.jsx)(d.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":eP(o.open),...n,ref:c,onClick:(0,i.m)(e.onClick,o.onOpenToggle)});return o.hasCustomAnchor?s:(0,l.jsx)(Y,{asChild:!0,...u,children:s})});ey.displayName=eh;var ev="PopoverPortal",[em,eb]=eu(ev,{forceMount:void 0}),eg=e=>{let{__scopePopover:t,forceMount:r,children:n,container:o}=e,i=ef(ev,t);return(0,l.jsx)(em,{scope:t,forceMount:r,children:(0,l.jsx)(er.C,{present:r||i.open,children:(0,l.jsx)(et,{asChild:!0,container:o,children:n})})})};eg.displayName=ev;var ex="PopoverContent",ew=o.forwardRef((e,t)=>{let r=eb(ex,e.__scopePopover),{forceMount:n=r.forceMount,...o}=e,i=ef(ex,e.__scopePopover);return(0,l.jsx)(er.C,{present:n||i.open,children:i.modal?(0,l.jsx)(ej,{...o,ref:t}):(0,l.jsx)(eS,{...o,ref:t})})});ew.displayName=ex;var eO=s("PopoverContent.RemoveScroll"),ej=o.forwardRef((e,t)=>{let r=ef(ex,e.__scopePopover),n=o.useRef(null),u=(0,a.s)(t,n),c=o.useRef(!1);return o.useEffect(()=>{let e=n.current;if(e)return(0,eo.Eq)(e)},[]),(0,l.jsx)(ei.A,{as:eO,allowPinchZoom:!0,children:(0,l.jsx)(eE,{...e,ref:u,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,i.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),c.current||null==(t=r.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,i.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;c.current=2===t.button||r},{checkForDefaultPrevented:!1}),onFocusOutside:(0,i.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1})})})}),eS=o.forwardRef((e,t)=>{let r=ef(ex,e.__scopePopover),n=o.useRef(!1),i=o.useRef(!1);return(0,l.jsx)(eE,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var o,a;null==(o=e.onCloseAutoFocus)||o.call(e,t),t.defaultPrevented||(n.current||null==(a=r.triggerRef.current)||a.focus(),t.preventDefault()),n.current=!1,i.current=!1},onInteractOutside:t=>{var o,a;null==(o=e.onInteractOutside)||o.call(e,t),t.defaultPrevented||(n.current=!0,"pointerdown"===t.detail.originalEvent.type&&(i.current=!0));let u=t.target;(null==(a=r.triggerRef.current)?void 0:a.contains(u))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&i.current&&t.preventDefault()}})}),eE=o.forwardRef((e,t)=>{let{__scopePopover:r,trapFocus:n,onOpenAutoFocus:o,onCloseAutoFocus:i,disableOutsidePointerEvents:a,onEscapeKeyDown:u,onPointerDownOutside:c,onFocusOutside:s,onInteractOutside:f,...p}=e,d=ef(ex,r),h=el(r);return(0,w.Oh)(),(0,l.jsx)(E,{asChild:!0,loop:!0,trapped:n,onMountAutoFocus:o,onUnmountAutoFocus:i,children:(0,l.jsx)(b,{asChild:!0,disableOutsidePointerEvents:a,onInteractOutside:f,onEscapeKeyDown:u,onPointerDownOutside:c,onFocusOutside:s,onDismiss:()=>d.onOpenChange(!1),children:(0,l.jsx)(Z,{"data-state":eP(d.open),role:"dialog",id:d.contentId,...h,...p,ref:t,style:{...p.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),eA="PopoverClose";function eP(e){return e?"open":"closed"}o.forwardRef((e,t)=>{let{__scopePopover:r,...n}=e,o=ef(eA,r);return(0,l.jsx)(d.button,{type:"button",...n,ref:t,onClick:(0,i.m)(e.onClick,()=>o.onOpenChange(!1))})}).displayName=eA,o.forwardRef((e,t)=>{let{__scopePopover:r,...n}=e,o=el(r);return(0,l.jsx)(K,{...o,...n,ref:t})}).displayName="PopoverArrow";var ek=ep,eM=ey,eT=eg,e_=ew},50091:(e,t,r)=>{"use strict";r.d(t,{DR:()=>g,pj:()=>O,rY:()=>k,yi:()=>P,Yp:()=>x,hj:()=>A,sk:()=>E,AF:()=>w,Nk:()=>S,$G:()=>j});var n=r(12115),o=r(93179),i=r(97124),a=r.n(i),u=r(29794),c=r.n(u),l=r(91113),s=r.n(l)()(function(e){return{x:e.left,y:e.top,width:e.width,height:e.height}},function(e){return["l",e.left,"t",e.top,"w",e.width,"h",e.height].join("")}),f=r(16377),p=(0,n.createContext)(void 0),d=(0,n.createContext)(void 0),h=(0,n.createContext)(void 0),y=(0,n.createContext)({}),v=(0,n.createContext)(void 0),m=(0,n.createContext)(0),b=(0,n.createContext)(0),g=function(e){var t=e.state,r=t.xAxisMap,o=t.yAxisMap,i=t.offset,a=e.clipPathId,u=e.children,c=e.width,l=e.height,f=s(i);return n.createElement(p.Provider,{value:r},n.createElement(d.Provider,{value:o},n.createElement(y.Provider,{value:i},n.createElement(h.Provider,{value:f},n.createElement(v.Provider,{value:a},n.createElement(m.Provider,{value:l},n.createElement(b.Provider,{value:c},u)))))))},x=function(){return(0,n.useContext)(v)},w=function(e){var t=(0,n.useContext)(p);null==t&&(0,o.A)(!1);var r=t[e];return null==r&&(0,o.A)(!1),r},O=function(){var e=(0,n.useContext)(p);return(0,f.lX)(e)},j=function(){var e=(0,n.useContext)(d);return a()(e,function(e){return c()(e.domain,Number.isFinite)})||(0,f.lX)(e)},S=function(e){var t=(0,n.useContext)(d);null==t&&(0,o.A)(!1);var r=t[e];return null==r&&(0,o.A)(!1),r},E=function(){return(0,n.useContext)(h)},A=function(){return(0,n.useContext)(y)},P=function(){return(0,n.useContext)(b)},k=function(){return(0,n.useContext)(m)}},50111:(e,t,r)=>{var n=r(73800),o=r(9813),i=r(39608),a=r(99544),u=r(15631),c=r(94356);e.exports=function(e,t,r){t=n(t,e);for(var l=-1,s=t.length,f=!1;++l<s;){var p=c(t[l]);if(!(f=null!=e&&r(e,p)))break;e=e[p]}return f||++l!=s?f:!!(s=null==e?0:e.length)&&u(s)&&a(p,s)&&(i(e)||o(e))}},50330:(e,t,r)=>{"use strict";e.exports=r(30294)},50523:(e,t,r)=>{var n=r(24376),o=r(76957),i=r(58817),a=r(38406),u=r(90724),c=r(74166),l=n?n.prototype:void 0,s=l?l.valueOf:void 0;e.exports=function(e,t,r,n,l,f,p){switch(r){case"[object DataView]":if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)break;e=e.buffer,t=t.buffer;case"[object ArrayBuffer]":if(e.byteLength!=t.byteLength||!f(new o(e),new o(t)))break;return!0;case"[object Boolean]":case"[object Date]":case"[object Number]":return i(+e,+t);case"[object Error]":return e.name==t.name&&e.message==t.message;case"[object RegExp]":case"[object String]":return e==t+"";case"[object Map]":var d=u;case"[object Set]":var h=1&n;if(d||(d=c),e.size!=t.size&&!h)break;var y=p.get(e);if(y)return y==t;n|=2,p.set(e,t);var v=a(d(e),d(t),n,l,f,p);return p.delete(e),v;case"[object Symbol]":if(s)return s.call(e)==s.call(t)}return!1}},50664:e=>{e.exports=function(e,t){var r=e.length;for(e.sort(t);r--;)e[r]=e[r].value;return e}},50687:e=>{e.exports=function(e){return function(){return e}}},50741:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("chart-no-axes-column-increasing",[["line",{x1:"12",x2:"12",y1:"20",y2:"10",key:"1vz5eb"}],["line",{x1:"18",x2:"18",y1:"20",y2:"4",key:"cun8e5"}],["line",{x1:"6",x2:"6",y1:"20",y2:"16",key:"hq0ia6"}]])},50851:e=>{e.exports=function(e){return e.split("")}},51154:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},51172:(e,t,r)=>{"use strict";r.d(t,{c:()=>c});var n=r(12115),o=r(52596),i=r(43597),a=r(70788);function u(){return(u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var c=function(e){var t=e.cx,r=e.cy,c=e.r,l=e.className,s=(0,o.A)("recharts-dot",l);return t===+t&&r===+r&&c===+c?n.createElement("circle",u({},(0,a.J9)(e,!1),(0,i._U)(e),{className:s,cx:t,cy:r,r:c})):null}},51445:(e,t,r)=>{var n=r(53516);e.exports=function(e,t){var r;return n(e,function(e,n,o){return!(r=t(e,n,o))}),!!r}},51911:(e,t,r)=>{var n=r(69229),o=r(48611);e.exports=function e(t,r,i,a,u){return t===r||(null!=t&&null!=r&&(o(t)||o(r))?n(t,r,i,a,e,u):t!=t&&r!=r)}},52521:e=>{e.exports=function(e,t){return e<t}},53516:(e,t,r)=>{var n=r(20480);e.exports=r(49872)(n)},53696:(e,t,r)=>{var n=r(75899),o=Object.prototype.hasOwnProperty;e.exports=function(e){var t=this.__data__;if(n){var r=t[e];return"__lodash_hash_undefined__"===r?void 0:r}return o.call(t,e)?t[e]:void 0}},54360:(e,t,r)=>{var n=r(58817);e.exports=function(e,t){for(var r=e.length;r--;)if(n(e[r][0],t))return r;return -1}},54811:(e,t,r)=>{"use strict";r.d(t,{f:()=>n});var n=function(e){return null};n.displayName="Cell"},54906:(e,t,r)=>{var n=r(83711);e.exports=function(){try{var e=n(Object,"defineProperty");return e({},"",{}),e}catch(e){}}()},55794:(e,t,r)=>{var n=r(57213),o=r(34711),i=r(18028),a=r(6305),u=r(50664),c=r(33332),l=r(9699),s=r(13465),f=r(39608);e.exports=function(e,t,r){t=t.length?n(t,function(e){return f(e)?function(t){return o(t,1===e.length?e[0]:e)}:e}):[s];var p=-1;return t=n(t,c(i)),u(a(e,function(e,r,o){return{criteria:n(t,function(t){return t(e)}),index:++p,value:e}}),function(e,t){return l(e,t,r)})}},55910:(e,t,r)=>{var n=r(4854),o=r(27569),i=r(92972);e.exports=function(e){var t=o(e);return 1==t.length&&t[0][2]?i(t[0][0],t[0][1]):function(r){return r===e||n(r,e,t)}}},56811:(e,t,r)=>{"use strict";r.d(t,{i3:()=>eP,UC:()=>eA,ZL:()=>eE,Kq:()=>eO,bL:()=>ej,l9:()=>eS});var n,o=r(12115),i=r(85185),a=r(6101),u=r(46081),c=r(47650),l=r(95155),s=Symbol("radix.slottable");function f(e){return o.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===s}var p=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=function(e){let t=function(e){let t=o.forwardRef((e,t)=>{var r,n,i;let u,c,{children:l,...s}=e,f=o.isValidElement(l)?(c=(u=null==(n=Object.getOwnPropertyDescriptor((r=l).props,"ref"))?void 0:n.get)&&"isReactWarning"in u&&u.isReactWarning)?r.ref:(c=(u=null==(i=Object.getOwnPropertyDescriptor(r,"ref"))?void 0:i.get)&&"isReactWarning"in u&&u.isReactWarning)?r.props.ref:r.props.ref||r.ref:void 0,p=(0,a.s)(f,t);if(o.isValidElement(l)){let e=function(e,t){let r={...t};for(let n in t){let o=e[n],i=t[n];/^on[A-Z]/.test(n)?o&&i?r[n]=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let n=i(...t);return o(...t),n}:o&&(r[n]=o):"style"===n?r[n]={...o,...i}:"className"===n&&(r[n]=[o,i].filter(Boolean).join(" "))}return{...e,...r}}(s,l.props);return l.type!==o.Fragment&&(e.ref=p),o.cloneElement(l,e)}return o.Children.count(l)>1?o.Children.only(null):null});return t.displayName="".concat(e,".SlotClone"),t}(e),r=o.forwardRef((e,r)=>{let{children:n,...i}=e,a=o.Children.toArray(n),u=a.find(f);if(u){let e=u.props.children,n=a.map(t=>t!==u?t:o.Children.count(e)>1?o.Children.only(null):o.isValidElement(e)?e.props.children:null);return(0,l.jsx)(t,{...i,ref:r,children:o.isValidElement(e)?o.cloneElement(e,void 0,n):null})}return(0,l.jsx)(t,{...i,ref:r,children:n})});return r.displayName="".concat(e,".Slot"),r}(`Primitive.${t}`),n=o.forwardRef((e,n)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(o?r:t,{...i,ref:n})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{}),d=r(39033),h=r(51595),y="dismissableLayer.update",v=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),m=o.forwardRef((e,t)=>{var r,u;let{disableOutsidePointerEvents:c=!1,onEscapeKeyDown:s,onPointerDownOutside:f,onFocusOutside:m,onInteractOutside:x,onDismiss:w,...O}=e,j=o.useContext(v),[S,E]=o.useState(null),A=null!=(u=null==S?void 0:S.ownerDocument)?u:null==(r=globalThis)?void 0:r.document,[,P]=o.useState({}),k=(0,a.s)(t,e=>E(e)),M=Array.from(j.layers),[T]=[...j.layersWithOutsidePointerEventsDisabled].slice(-1),_=M.indexOf(T),C=S?M.indexOf(S):-1,D=j.layersWithOutsidePointerEventsDisabled.size>0,N=C>=_,I=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,n=(0,d.c)(e),i=o.useRef(!1),a=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!i.current){let t=function(){g("dismissableLayer.pointerDownOutside",n,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(r.removeEventListener("click",a.current),a.current=t,r.addEventListener("click",a.current,{once:!0})):t()}else r.removeEventListener("click",a.current);i.current=!1},t=window.setTimeout(()=>{r.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),r.removeEventListener("pointerdown",e),r.removeEventListener("click",a.current)}},[r,n]),{onPointerDownCapture:()=>i.current=!0}}(e=>{let t=e.target,r=[...j.branches].some(e=>e.contains(t));N&&!r&&(null==f||f(e),null==x||x(e),e.defaultPrevented||null==w||w())},A),R=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,n=(0,d.c)(e),i=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!i.current&&g("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return r.addEventListener("focusin",e),()=>r.removeEventListener("focusin",e)},[r,n]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}(e=>{let t=e.target;![...j.branches].some(e=>e.contains(t))&&(null==m||m(e),null==x||x(e),e.defaultPrevented||null==w||w())},A);return(0,h.U)(e=>{C===j.layers.size-1&&(null==s||s(e),!e.defaultPrevented&&w&&(e.preventDefault(),w()))},A),o.useEffect(()=>{if(S)return c&&(0===j.layersWithOutsidePointerEventsDisabled.size&&(n=A.body.style.pointerEvents,A.body.style.pointerEvents="none"),j.layersWithOutsidePointerEventsDisabled.add(S)),j.layers.add(S),b(),()=>{c&&1===j.layersWithOutsidePointerEventsDisabled.size&&(A.body.style.pointerEvents=n)}},[S,A,c,j]),o.useEffect(()=>()=>{S&&(j.layers.delete(S),j.layersWithOutsidePointerEventsDisabled.delete(S),b())},[S,j]),o.useEffect(()=>{let e=()=>P({});return document.addEventListener(y,e),()=>document.removeEventListener(y,e)},[]),(0,l.jsx)(p.div,{...O,ref:k,style:{pointerEvents:D?N?"auto":"none":void 0,...e.style},onFocusCapture:(0,i.m)(e.onFocusCapture,R.onFocusCapture),onBlurCapture:(0,i.m)(e.onBlurCapture,R.onBlurCapture),onPointerDownCapture:(0,i.m)(e.onPointerDownCapture,I.onPointerDownCapture)})});function b(){let e=new CustomEvent(y);document.dispatchEvent(e)}function g(e,t,r,n){let{discrete:o}=n,i=r.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});if(t&&i.addEventListener(e,t,{once:!0}),o)i&&c.flushSync(()=>i.dispatchEvent(a));else i.dispatchEvent(a)}m.displayName="DismissableLayer",o.forwardRef((e,t)=>{let r=o.useContext(v),n=o.useRef(null),i=(0,a.s)(t,n);return o.useEffect(()=>{let e=n.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,l.jsx)(p.div,{...e,ref:i})}).displayName="DismissableLayerBranch";var x=r(61285),w=r(84945),O=r(22475),j=o.forwardRef((e,t)=>{let{children:r,width:n=10,height:o=5,...i}=e;return(0,l.jsx)(p.svg,{...i,ref:t,width:n,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?r:(0,l.jsx)("polygon",{points:"0,0 30,0 15,10"})})});j.displayName="Arrow";var S=r(52712),E=r(11275),A="Popper",[P,k]=(0,u.A)(A),[M,T]=P(A),_=e=>{let{__scopePopper:t,children:r}=e,[n,i]=o.useState(null);return(0,l.jsx)(M,{scope:t,anchor:n,onAnchorChange:i,children:r})};_.displayName=A;var C="PopperAnchor",D=o.forwardRef((e,t)=>{let{__scopePopper:r,virtualRef:n,...i}=e,u=T(C,r),c=o.useRef(null),s=(0,a.s)(t,c);return o.useEffect(()=>{u.onAnchorChange((null==n?void 0:n.current)||c.current)}),n?null:(0,l.jsx)(p.div,{...i,ref:s})});D.displayName=C;var N="PopperContent",[I,R]=P(N),B=o.forwardRef((e,t)=>{var r,n,i,u,c,s,f,h;let{__scopePopper:y,side:v="bottom",sideOffset:m=0,align:b="center",alignOffset:g=0,arrowPadding:x=0,avoidCollisions:j=!0,collisionBoundary:A=[],collisionPadding:P=0,sticky:k="partial",hideWhenDetached:M=!1,updatePositionStrategy:_="optimized",onPlaced:C,...D}=e,R=T(N,y),[B,L]=o.useState(null),W=(0,a.s)(t,e=>L(e)),[F,Y]=o.useState(null),H=(0,E.X)(F),q=null!=(f=null==H?void 0:H.width)?f:0,X=null!=(h=null==H?void 0:H.height)?h:0,Z="number"==typeof P?P:{top:0,right:0,bottom:0,left:0,...P},V=Array.isArray(A)?A:[A],G=V.length>0,K={padding:Z,boundary:V.filter(z),altBoundary:G},{refs:J,floatingStyles:Q,placement:ee,isPositioned:et,middlewareData:er}=(0,w.we)({strategy:"fixed",placement:v+("center"!==b?"-"+b:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,O.ll)(...t,{animationFrame:"always"===_})},elements:{reference:R.anchor},middleware:[(0,w.cY)({mainAxis:m+X,alignmentAxis:g}),j&&(0,w.BN)({mainAxis:!0,crossAxis:!1,limiter:"partial"===k?(0,w.ER)():void 0,...K}),j&&(0,w.UU)({...K}),(0,w.Ej)({...K,apply:e=>{let{elements:t,rects:r,availableWidth:n,availableHeight:o}=e,{width:i,height:a}=r.reference,u=t.floating.style;u.setProperty("--radix-popper-available-width","".concat(n,"px")),u.setProperty("--radix-popper-available-height","".concat(o,"px")),u.setProperty("--radix-popper-anchor-width","".concat(i,"px")),u.setProperty("--radix-popper-anchor-height","".concat(a,"px"))}}),F&&(0,w.UE)({element:F,padding:x}),U({arrowWidth:q,arrowHeight:X}),M&&(0,w.jD)({strategy:"referenceHidden",...K})]}),[en,eo]=$(ee),ei=(0,d.c)(C);(0,S.N)(()=>{et&&(null==ei||ei())},[et,ei]);let ea=null==(r=er.arrow)?void 0:r.x,eu=null==(n=er.arrow)?void 0:n.y,ec=(null==(i=er.arrow)?void 0:i.centerOffset)!==0,[el,es]=o.useState();return(0,S.N)(()=>{B&&es(window.getComputedStyle(B).zIndex)},[B]),(0,l.jsx)("div",{ref:J.setFloating,"data-radix-popper-content-wrapper":"",style:{...Q,transform:et?Q.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:el,"--radix-popper-transform-origin":[null==(u=er.transformOrigin)?void 0:u.x,null==(c=er.transformOrigin)?void 0:c.y].join(" "),...(null==(s=er.hide)?void 0:s.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,l.jsx)(I,{scope:y,placedSide:en,onArrowChange:Y,arrowX:ea,arrowY:eu,shouldHideArrow:ec,children:(0,l.jsx)(p.div,{"data-side":en,"data-align":eo,...D,ref:W,style:{...D.style,animation:et?void 0:"none"}})})})});B.displayName=N;var L="PopperArrow",W={top:"bottom",right:"left",bottom:"top",left:"right"},F=o.forwardRef(function(e,t){let{__scopePopper:r,...n}=e,o=R(L,r),i=W[o.placedSide];return(0,l.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,l.jsx)(j,{...n,ref:t,style:{...n.style,display:"block"}})})});function z(e){return null!==e}F.displayName=L;var U=e=>({name:"transformOrigin",options:e,fn(t){var r,n,o,i,a;let{placement:u,rects:c,middlewareData:l}=t,s=(null==(r=l.arrow)?void 0:r.centerOffset)!==0,f=s?0:e.arrowWidth,p=s?0:e.arrowHeight,[d,h]=$(u),y={start:"0%",center:"50%",end:"100%"}[h],v=(null!=(i=null==(n=l.arrow)?void 0:n.x)?i:0)+f/2,m=(null!=(a=null==(o=l.arrow)?void 0:o.y)?a:0)+p/2,b="",g="";return"bottom"===d?(b=s?y:"".concat(v,"px"),g="".concat(-p,"px")):"top"===d?(b=s?y:"".concat(v,"px"),g="".concat(c.floating.height+p,"px")):"right"===d?(b="".concat(-p,"px"),g=s?y:"".concat(m,"px")):"left"===d&&(b="".concat(c.floating.width+p,"px"),g=s?y:"".concat(m,"px")),{data:{x:b,y:g}}}});function $(e){let[t,r="center"]=e.split("-");return[t,r]}var Y=o.forwardRef((e,t)=>{var r,n;let{container:i,...a}=e,[u,s]=o.useState(!1);(0,S.N)(()=>s(!0),[]);let f=i||u&&(null==(n=globalThis)||null==(r=n.document)?void 0:r.body);return f?c.createPortal((0,l.jsx)(p.div,{...a,ref:t}),f):null});Y.displayName="Portal";var H=r(28905),q=r(5845),X=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),Z=o.forwardRef((e,t)=>(0,l.jsx)(p.span,{...e,ref:t,style:{...X,...e.style}}));Z.displayName="VisuallyHidden";var[V,G]=(0,u.A)("Tooltip",[k]),K=k(),J="TooltipProvider",Q="tooltip.open",[ee,et]=V(J),er=e=>{let{__scopeTooltip:t,delayDuration:r=700,skipDelayDuration:n=300,disableHoverableContent:i=!1,children:a}=e,u=o.useRef(!0),c=o.useRef(!1),s=o.useRef(0);return o.useEffect(()=>{let e=s.current;return()=>window.clearTimeout(e)},[]),(0,l.jsx)(ee,{scope:t,isOpenDelayedRef:u,delayDuration:r,onOpen:o.useCallback(()=>{window.clearTimeout(s.current),u.current=!1},[]),onClose:o.useCallback(()=>{window.clearTimeout(s.current),s.current=window.setTimeout(()=>u.current=!0,n)},[n]),isPointerInTransitRef:c,onPointerInTransitChange:o.useCallback(e=>{c.current=e},[]),disableHoverableContent:i,children:a})};er.displayName=J;var en="Tooltip",[eo,ei]=V(en),ea=e=>{let{__scopeTooltip:t,children:r,open:n,defaultOpen:i,onOpenChange:a,disableHoverableContent:u,delayDuration:c}=e,s=et(en,e.__scopeTooltip),f=K(t),[p,d]=o.useState(null),h=(0,x.B)(),y=o.useRef(0),v=null!=u?u:s.disableHoverableContent,m=null!=c?c:s.delayDuration,b=o.useRef(!1),[g,w]=(0,q.i)({prop:n,defaultProp:null!=i&&i,onChange:e=>{e?(s.onOpen(),document.dispatchEvent(new CustomEvent(Q))):s.onClose(),null==a||a(e)},caller:en}),O=o.useMemo(()=>g?b.current?"delayed-open":"instant-open":"closed",[g]),j=o.useCallback(()=>{window.clearTimeout(y.current),y.current=0,b.current=!1,w(!0)},[w]),S=o.useCallback(()=>{window.clearTimeout(y.current),y.current=0,w(!1)},[w]),E=o.useCallback(()=>{window.clearTimeout(y.current),y.current=window.setTimeout(()=>{b.current=!0,w(!0),y.current=0},m)},[m,w]);return o.useEffect(()=>()=>{y.current&&(window.clearTimeout(y.current),y.current=0)},[]),(0,l.jsx)(_,{...f,children:(0,l.jsx)(eo,{scope:t,contentId:h,open:g,stateAttribute:O,trigger:p,onTriggerChange:d,onTriggerEnter:o.useCallback(()=>{s.isOpenDelayedRef.current?E():j()},[s.isOpenDelayedRef,E,j]),onTriggerLeave:o.useCallback(()=>{v?S():(window.clearTimeout(y.current),y.current=0)},[S,v]),onOpen:j,onClose:S,disableHoverableContent:v,children:r})})};ea.displayName=en;var eu="TooltipTrigger",ec=o.forwardRef((e,t)=>{let{__scopeTooltip:r,...n}=e,u=ei(eu,r),c=et(eu,r),s=K(r),f=o.useRef(null),d=(0,a.s)(t,f,u.onTriggerChange),h=o.useRef(!1),y=o.useRef(!1),v=o.useCallback(()=>h.current=!1,[]);return o.useEffect(()=>()=>document.removeEventListener("pointerup",v),[v]),(0,l.jsx)(D,{asChild:!0,...s,children:(0,l.jsx)(p.button,{"aria-describedby":u.open?u.contentId:void 0,"data-state":u.stateAttribute,...n,ref:d,onPointerMove:(0,i.m)(e.onPointerMove,e=>{"touch"!==e.pointerType&&(y.current||c.isPointerInTransitRef.current||(u.onTriggerEnter(),y.current=!0))}),onPointerLeave:(0,i.m)(e.onPointerLeave,()=>{u.onTriggerLeave(),y.current=!1}),onPointerDown:(0,i.m)(e.onPointerDown,()=>{u.open&&u.onClose(),h.current=!0,document.addEventListener("pointerup",v,{once:!0})}),onFocus:(0,i.m)(e.onFocus,()=>{h.current||u.onOpen()}),onBlur:(0,i.m)(e.onBlur,u.onClose),onClick:(0,i.m)(e.onClick,u.onClose)})})});ec.displayName=eu;var el="TooltipPortal",[es,ef]=V(el,{forceMount:void 0}),ep=e=>{let{__scopeTooltip:t,forceMount:r,children:n,container:o}=e,i=ei(el,t);return(0,l.jsx)(es,{scope:t,forceMount:r,children:(0,l.jsx)(H.C,{present:r||i.open,children:(0,l.jsx)(Y,{asChild:!0,container:o,children:n})})})};ep.displayName=el;var ed="TooltipContent",eh=o.forwardRef((e,t)=>{let r=ef(ed,e.__scopeTooltip),{forceMount:n=r.forceMount,side:o="top",...i}=e,a=ei(ed,e.__scopeTooltip);return(0,l.jsx)(H.C,{present:n||a.open,children:a.disableHoverableContent?(0,l.jsx)(eg,{side:o,...i,ref:t}):(0,l.jsx)(ey,{side:o,...i,ref:t})})}),ey=o.forwardRef((e,t)=>{let r=ei(ed,e.__scopeTooltip),n=et(ed,e.__scopeTooltip),i=o.useRef(null),u=(0,a.s)(t,i),[c,s]=o.useState(null),{trigger:f,onClose:p}=r,d=i.current,{onPointerInTransitChange:h}=n,y=o.useCallback(()=>{s(null),h(!1)},[h]),v=o.useCallback((e,t)=>{let r=e.currentTarget,n={x:e.clientX,y:e.clientY},o=function(e,t){let r=Math.abs(t.top-e.y),n=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),i=Math.abs(t.left-e.x);switch(Math.min(r,n,o,i)){case i:return"left";case o:return"right";case r:return"top";case n:return"bottom";default:throw Error("unreachable")}}(n,r.getBoundingClientRect());s(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:1*!!(e.y>t.y)),function(e){if(e.length<=1)return e.slice();let t=[];for(let r=0;r<e.length;r++){let n=e[r];for(;t.length>=2;){let e=t[t.length-1],r=t[t.length-2];if((e.x-r.x)*(n.y-r.y)>=(e.y-r.y)*(n.x-r.x))t.pop();else break}t.push(n)}t.pop();let r=[];for(let t=e.length-1;t>=0;t--){let n=e[t];for(;r.length>=2;){let e=r[r.length-1],t=r[r.length-2];if((e.x-t.x)*(n.y-t.y)>=(e.y-t.y)*(n.x-t.x))r.pop();else break}r.push(n)}return(r.pop(),1===t.length&&1===r.length&&t[0].x===r[0].x&&t[0].y===r[0].y)?t:t.concat(r)}(t)}([...function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,n=[];switch(t){case"top":n.push({x:e.x-r,y:e.y+r},{x:e.x+r,y:e.y+r});break;case"bottom":n.push({x:e.x-r,y:e.y-r},{x:e.x+r,y:e.y-r});break;case"left":n.push({x:e.x+r,y:e.y-r},{x:e.x+r,y:e.y+r});break;case"right":n.push({x:e.x-r,y:e.y-r},{x:e.x-r,y:e.y+r})}return n}(n,o),...function(e){let{top:t,right:r,bottom:n,left:o}=e;return[{x:o,y:t},{x:r,y:t},{x:r,y:n},{x:o,y:n}]}(t.getBoundingClientRect())])),h(!0)},[h]);return o.useEffect(()=>()=>y(),[y]),o.useEffect(()=>{if(f&&d){let e=e=>v(e,d),t=e=>v(e,f);return f.addEventListener("pointerleave",e),d.addEventListener("pointerleave",t),()=>{f.removeEventListener("pointerleave",e),d.removeEventListener("pointerleave",t)}}},[f,d,v,y]),o.useEffect(()=>{if(c){let e=e=>{let t=e.target,r={x:e.clientX,y:e.clientY},n=(null==f?void 0:f.contains(t))||(null==d?void 0:d.contains(t)),o=!function(e,t){let{x:r,y:n}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let a=t[e],u=t[i],c=a.x,l=a.y,s=u.x,f=u.y;l>n!=f>n&&r<(s-c)*(n-l)/(f-l)+c&&(o=!o)}return o}(r,c);n?y():o&&(y(),p())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[f,d,c,p,y]),(0,l.jsx)(eg,{...e,ref:u})}),[ev,em]=V(en,{isInside:!1}),eb=function(e){let t=e=>{let{children:t}=e;return(0,l.jsx)(l.Fragment,{children:t})};return t.displayName="".concat(e,".Slottable"),t.__radixId=s,t}("TooltipContent"),eg=o.forwardRef((e,t)=>{let{__scopeTooltip:r,children:n,"aria-label":i,onEscapeKeyDown:a,onPointerDownOutside:u,...c}=e,s=ei(ed,r),f=K(r),{onClose:p}=s;return o.useEffect(()=>(document.addEventListener(Q,p),()=>document.removeEventListener(Q,p)),[p]),o.useEffect(()=>{if(s.trigger){let e=e=>{let t=e.target;(null==t?void 0:t.contains(s.trigger))&&p()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[s.trigger,p]),(0,l.jsx)(m,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:a,onPointerDownOutside:u,onFocusOutside:e=>e.preventDefault(),onDismiss:p,children:(0,l.jsxs)(B,{"data-state":s.stateAttribute,...f,...c,ref:t,style:{...c.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,l.jsx)(eb,{children:n}),(0,l.jsx)(ev,{scope:r,isInside:!0,children:(0,l.jsx)(Z,{id:s.contentId,role:"tooltip",children:i||n})})]})})});eh.displayName=ed;var ex="TooltipArrow",ew=o.forwardRef((e,t)=>{let{__scopeTooltip:r,...n}=e,o=K(r);return em(ex,r).isInside?null:(0,l.jsx)(F,{...o,...n,ref:t})});ew.displayName=ex;var eO=er,ej=ea,eS=ec,eE=ep,eA=eh,eP=ew},56917:(e,t,r)=>{var n=r(98233),o=r(48611);e.exports=function(e){return!0===e||!1===e||o(e)&&"[object Boolean]"==n(e)}},57213:e=>{e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length,o=Array(n);++r<n;)o[r]=t(e[r],r,e);return o}},58096:e=>{e.exports=function(e){return this.__data__.has(e)}},58817:e=>{e.exports=function(e,t){return e===t||e!=e&&t!=t}},58918:(e,t,r)=>{var n=r(70771);e.exports=function(e,t,r){for(var o=-1,i=e.length;++o<i;){var a=e[o],u=t(a);if(null!=u&&(void 0===c?u==u&&!n(u):r(u,c)))var c=u,l=a}return l}},59355:(e,t,r)=>{"use strict";r.d(t,{S:()=>o,w:()=>i});var n=r(12115);let o=(0,n.createContext)(void 0);function i(){let e=(0,n.useContext)(o);if(void 0===e)throw Error("useDayPicker() must be used within a custom component.");return e}},59882:e=>{e.exports=function(e){return null==e}},60245:(e,t,r)=>{var n=r(51911);e.exports=function(e,t){return n(e,t)}},60379:(e,t,r)=>{"use strict";r.d(t,{J:()=>E});var n=r(12115),o=r(59882),i=r.n(o),a=r(40139),u=r.n(a),c=r(67460),l=r.n(c),s=r(52596),f=r(79095),p=r(70788),d=r(16377),h=r(25641);function y(e){return(y="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var v=["offset"];function m(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function b(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function g(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?b(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=y(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=y(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==y(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):b(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function x(){return(x=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var w=function(e){var t=e.value,r=e.formatter,n=i()(e.children)?t:e.children;return u()(r)?r(n):n},O=function(e,t,r){var o,a,u=e.position,c=e.viewBox,l=e.offset,f=e.className,p=c.cx,y=c.cy,v=c.innerRadius,m=c.outerRadius,b=c.startAngle,g=c.endAngle,w=c.clockWise,O=(v+m)/2,j=(0,d.sA)(g-b)*Math.min(Math.abs(g-b),360),S=j>=0?1:-1;"insideStart"===u?(o=b+S*l,a=w):"insideEnd"===u?(o=g-S*l,a=!w):"end"===u&&(o=g+S*l,a=w),a=j<=0?a:!a;var E=(0,h.IZ)(p,y,O,o),A=(0,h.IZ)(p,y,O,o+(a?1:-1)*359),P="M".concat(E.x,",").concat(E.y,"\n    A").concat(O,",").concat(O,",0,1,").concat(+!a,",\n    ").concat(A.x,",").concat(A.y),k=i()(e.id)?(0,d.NF)("recharts-radial-line-"):e.id;return n.createElement("text",x({},r,{dominantBaseline:"central",className:(0,s.A)("recharts-radial-bar-label",f)}),n.createElement("defs",null,n.createElement("path",{id:k,d:P})),n.createElement("textPath",{xlinkHref:"#".concat(k)},t))},j=function(e){var t=e.viewBox,r=e.offset,n=e.position,o=t.cx,i=t.cy,a=t.innerRadius,u=t.outerRadius,c=(t.startAngle+t.endAngle)/2;if("outside"===n){var l=(0,h.IZ)(o,i,u+r,c),s=l.x;return{x:s,y:l.y,textAnchor:s>=o?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"end"};var f=(0,h.IZ)(o,i,(a+u)/2,c);return{x:f.x,y:f.y,textAnchor:"middle",verticalAnchor:"middle"}},S=function(e){var t=e.viewBox,r=e.parentViewBox,n=e.offset,o=e.position,i=t.x,a=t.y,u=t.width,c=t.height,s=c>=0?1:-1,f=s*n,p=s>0?"end":"start",h=s>0?"start":"end",y=u>=0?1:-1,v=y*n,m=y>0?"end":"start",b=y>0?"start":"end";if("top"===o)return g(g({},{x:i+u/2,y:a-s*n,textAnchor:"middle",verticalAnchor:p}),r?{height:Math.max(a-r.y,0),width:u}:{});if("bottom"===o)return g(g({},{x:i+u/2,y:a+c+f,textAnchor:"middle",verticalAnchor:h}),r?{height:Math.max(r.y+r.height-(a+c),0),width:u}:{});if("left"===o){var x={x:i-v,y:a+c/2,textAnchor:m,verticalAnchor:"middle"};return g(g({},x),r?{width:Math.max(x.x-r.x,0),height:c}:{})}if("right"===o){var w={x:i+u+v,y:a+c/2,textAnchor:b,verticalAnchor:"middle"};return g(g({},w),r?{width:Math.max(r.x+r.width-w.x,0),height:c}:{})}var O=r?{width:u,height:c}:{};return"insideLeft"===o?g({x:i+v,y:a+c/2,textAnchor:b,verticalAnchor:"middle"},O):"insideRight"===o?g({x:i+u-v,y:a+c/2,textAnchor:m,verticalAnchor:"middle"},O):"insideTop"===o?g({x:i+u/2,y:a+f,textAnchor:"middle",verticalAnchor:h},O):"insideBottom"===o?g({x:i+u/2,y:a+c-f,textAnchor:"middle",verticalAnchor:p},O):"insideTopLeft"===o?g({x:i+v,y:a+f,textAnchor:b,verticalAnchor:h},O):"insideTopRight"===o?g({x:i+u-v,y:a+f,textAnchor:m,verticalAnchor:h},O):"insideBottomLeft"===o?g({x:i+v,y:a+c-f,textAnchor:b,verticalAnchor:p},O):"insideBottomRight"===o?g({x:i+u-v,y:a+c-f,textAnchor:m,verticalAnchor:p},O):l()(o)&&((0,d.Et)(o.x)||(0,d._3)(o.x))&&((0,d.Et)(o.y)||(0,d._3)(o.y))?g({x:i+(0,d.F4)(o.x,u),y:a+(0,d.F4)(o.y,c),textAnchor:"end",verticalAnchor:"end"},O):g({x:i+u/2,y:a+c/2,textAnchor:"middle",verticalAnchor:"middle"},O)};function E(e){var t,r=e.offset,o=g({offset:void 0===r?5:r},function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,v)),a=o.viewBox,c=o.position,l=o.value,h=o.children,y=o.content,m=o.className,b=o.textBreakAll;if(!a||i()(l)&&i()(h)&&!(0,n.isValidElement)(y)&&!u()(y))return null;if((0,n.isValidElement)(y))return(0,n.cloneElement)(y,o);if(u()(y)){if(t=(0,n.createElement)(y,o),(0,n.isValidElement)(t))return t}else t=w(o);var E="cx"in a&&(0,d.Et)(a.cx),A=(0,p.J9)(o,!0);if(E&&("insideStart"===c||"insideEnd"===c||"end"===c))return O(o,t,A);var P=E?j(o):S(o);return n.createElement(f.E,x({className:(0,s.A)("recharts-label",void 0===m?"":m)},A,P,{breakAll:b}),t)}E.displayName="Label";var A=function(e){var t=e.cx,r=e.cy,n=e.angle,o=e.startAngle,i=e.endAngle,a=e.r,u=e.radius,c=e.innerRadius,l=e.outerRadius,s=e.x,f=e.y,p=e.top,h=e.left,y=e.width,v=e.height,m=e.clockWise,b=e.labelViewBox;if(b)return b;if((0,d.Et)(y)&&(0,d.Et)(v)){if((0,d.Et)(s)&&(0,d.Et)(f))return{x:s,y:f,width:y,height:v};if((0,d.Et)(p)&&(0,d.Et)(h))return{x:p,y:h,width:y,height:v}}return(0,d.Et)(s)&&(0,d.Et)(f)?{x:s,y:f,width:0,height:0}:(0,d.Et)(t)&&(0,d.Et)(r)?{cx:t,cy:r,startAngle:o||n||0,endAngle:i||n||0,innerRadius:c||0,outerRadius:l||u||a||0,clockWise:m}:e.viewBox?e.viewBox:{}};E.parseViewBox=A,E.renderCallByParent=function(e,t){var r,o,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!e||!e.children&&i&&!e.label)return null;var a=e.children,c=A(e),s=(0,p.aS)(a,E).map(function(e,r){return(0,n.cloneElement)(e,{viewBox:t||c,key:"label-".concat(r)})});if(!i)return s;return[(r=e.label,o=t||c,!r?null:!0===r?n.createElement(E,{key:"label-implicit",viewBox:o}):(0,d.vh)(r)?n.createElement(E,{key:"label-implicit",viewBox:o,value:r}):(0,n.isValidElement)(r)?r.type===E?(0,n.cloneElement)(r,{key:"label-implicit",viewBox:o}):n.createElement(E,{key:"label-implicit",content:r,viewBox:o}):u()(r)?n.createElement(E,{key:"label-implicit",content:r,viewBox:o}):l()(r)?n.createElement(E,x({viewBox:o},r,{key:"label-implicit"})):null)].concat(function(e){if(Array.isArray(e))return m(e)}(s)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(s)||function(e,t){if(e){if("string"==typeof e)return m(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return m(e,t)}}(s)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())}},61632:(e,t,r)=>{var n=r(16571);e.exports=r(36730)(n)},61830:(e,t,r)=>{var n=r(54360);e.exports=function(e){return n(this.__data__,e)>-1}},62464:(e,t,r)=>{var n=r(3562),o=r(18028),i=r(23360),a=Math.max;e.exports=function(e,t,r){var u=null==e?0:e.length;if(!u)return -1;var c=null==r?0:i(r);return c<0&&(c=a(u+c,0)),n(e,o(t,3),c)}},62962:(e,t,r)=>{var n=r(48659),o=r(65531),i=r(75145),a=r(85855);e.exports=function(e){return function(t){var r=o(t=a(t))?i(t):void 0,u=r?r[0]:t.charAt(0),c=r?n(r,1).join(""):t.slice(1);return u[e]()+c}}},64189:(e,t,r)=>{var n=r(74366),o=r(39641),i=r(42233);e.exports=function(e){return function(t,r,a){return a&&"number"!=typeof a&&o(t,r,a)&&(r=a=void 0),t=i(t),void 0===r?(r=t,t=0):r=i(r),a=void 0===a?t<r?1:-1:i(a),n(t,r,a,e)}}},64439:e=>{var t=Object.prototype.toString;e.exports=function(e){return t.call(e)}},64588:(e,t,r)=>{var n=r(84760),o=Math.max;e.exports=function(e,t,r){return t=o(void 0===t?e.length-1:t,0),function(){for(var i=arguments,a=-1,u=o(i.length-t,0),c=Array(u);++a<u;)c[a]=i[t+a];a=-1;for(var l=Array(t+1);++a<t;)l[a]=i[a];return l[t]=r(c),n(e,this,l)}}},65531:e=>{var t=RegExp("[\\u200d\ud800-\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");e.exports=function(e){return t.test(e)}},65646:(e,t,r)=>{var n=r(91569),o=r(39608);e.exports=function(e,t,r){var i=t(e);return o(e)?i:n(i,r(e))}},65796:e=>{e.exports=function(e){return this.__data__.get(e)}},65836:(e,t,r)=>{var n=r(85090),o=r(7548),i=r(39984),a=r(82954),u=r(82596),c=r(74166);e.exports=function(e,t,r){var l=-1,s=o,f=e.length,p=!0,d=[],h=d;if(r)p=!1,s=i;else if(f>=200){var y=t?null:u(e);if(y)return c(y);p=!1,s=a,h=new n}else h=t?[]:d;t:for(;++l<f;){var v=e[l],m=t?t(v):v;if(v=r||0!==v?v:0,p&&m==m){for(var b=h.length;b--;)if(h[b]===m)continue t;t&&h.push(m),d.push(v)}else s(h,m,r)||(h!==d&&h.push(m),d.push(v))}return d}},66373:(e,t,r)=>{e.exports=r(83711)(r(82500),"Promise")},67206:(e,t,r)=>{var n=r(77969),o=r(55794),i=r(21087),a=r(39641);e.exports=i(function(e,t){if(null==e)return[];var r=t.length;return r>1&&a(e,t[0],t[1])?t=[]:r>2&&a(t[0],t[1],t[2])&&(t=[t[0]]),o(e,n(t,1),[])})},67460:e=>{e.exports=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}},67472:(e,t,r)=>{var n=r(31598),o=r(90453),i=r(47995),a=r(65796),u=r(58096),c=r(7771);function l(e){var t=this.__data__=new n(e);this.size=t.size}l.prototype.clear=o,l.prototype.delete=i,l.prototype.get=a,l.prototype.has=u,l.prototype.set=c,e.exports=l},67790:(e,t,r)=>{"use strict";r.d(t,{yp:()=>C,GG:()=>W,NE:()=>D,nZ:()=>N,xQ:()=>I});var n=r(12115),o=r(40139),i=r.n(o),a=r(44482),u=r.n(a),c=r(56917),l=r.n(c),s=r(60245),f=r.n(s),p=r(44538),d=r(52596),h=r(9557),y=r(70788);function v(e){return(v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function m(){return(m=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function b(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function g(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function x(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?g(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=v(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=v(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==v(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):g(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var w=function(e,t,r,n,o){var i,a=r-n;return"M ".concat(e,",").concat(t)+"L ".concat(e+r,",").concat(t)+"L ".concat(e+r-a/2,",").concat(t+o)+"L ".concat(e+r-a/2-n,",").concat(t+o)+"L ".concat(e,",").concat(t," Z")},O={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},j=function(e){var t,r=x(x({},O),e),o=(0,n.useRef)(),i=function(e){if(Array.isArray(e))return e}(t=(0,n.useState)(-1))||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{i=(r=r.call(e)).next,!1;for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(e){l=!0,o=e}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(t,2)||function(e,t){if(e){if("string"==typeof e)return b(e,2);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return b(e,t)}}(t,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),a=i[0],u=i[1];(0,n.useEffect)(function(){if(o.current&&o.current.getTotalLength)try{var e=o.current.getTotalLength();e&&u(e)}catch(e){}},[]);var c=r.x,l=r.y,s=r.upperWidth,f=r.lowerWidth,p=r.height,v=r.className,g=r.animationEasing,j=r.animationDuration,S=r.animationBegin,E=r.isUpdateAnimationActive;if(c!==+c||l!==+l||s!==+s||f!==+f||p!==+p||0===s&&0===f||0===p)return null;var A=(0,d.A)("recharts-trapezoid",v);return E?n.createElement(h.Ay,{canBegin:a>0,from:{upperWidth:0,lowerWidth:0,height:p,x:c,y:l},to:{upperWidth:s,lowerWidth:f,height:p,x:c,y:l},duration:j,animationEasing:g,isActive:E},function(e){var t=e.upperWidth,i=e.lowerWidth,u=e.height,c=e.x,l=e.y;return n.createElement(h.Ay,{canBegin:a>0,from:"0px ".concat(-1===a?1:a,"px"),to:"".concat(a,"px 0px"),attributeName:"strokeDasharray",begin:S,duration:j,easing:g},n.createElement("path",m({},(0,y.J9)(r,!0),{className:A,d:w(c,l,t,i,u),ref:o})))}):n.createElement("g",null,n.createElement("path",m({},(0,y.J9)(r,!0),{className:A,d:w(c,l,s,f,p)})))},S=r(77283),E=r(2348),A=r(9795),P=["option","shapeType","propTransformer","activeClassName","isActive"];function k(e){return(k="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function M(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function T(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?M(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=k(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=k(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==k(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):M(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function _(e){var t=e.shapeType,r=e.elementProps;switch(t){case"rectangle":return n.createElement(p.M,r);case"trapezoid":return n.createElement(j,r);case"sector":return n.createElement(S.h,r);case"symbols":if("symbols"===t)return n.createElement(A.i,r);break;default:return null}}function C(e){var t,r=e.option,o=e.shapeType,a=e.propTransformer,c=e.activeClassName,s=e.isActive,f=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,P);if((0,n.isValidElement)(r))t=(0,n.cloneElement)(r,T(T({},f),(0,n.isValidElement)(r)?r.props:r));else if(i()(r))t=r(f);else if(u()(r)&&!l()(r)){var p=(void 0===a?function(e,t){return T(T({},t),e)}:a)(r,f);t=n.createElement(_,{shapeType:o,elementProps:p})}else t=n.createElement(_,{shapeType:o,elementProps:f});return s?n.createElement(E.W,{className:void 0===c?"recharts-active-shape":c},t):t}function D(e,t){return null!=t&&"trapezoids"in e.props}function N(e,t){return null!=t&&"sectors"in e.props}function I(e,t){return null!=t&&"points"in e.props}function R(e,t){var r,n,o=e.x===(null==t||null==(r=t.labelViewBox)?void 0:r.x)||e.x===t.x,i=e.y===(null==t||null==(n=t.labelViewBox)?void 0:n.y)||e.y===t.y;return o&&i}function B(e,t){var r=e.endAngle===t.endAngle,n=e.startAngle===t.startAngle;return r&&n}function L(e,t){var r=e.x===t.x,n=e.y===t.y,o=e.z===t.z;return r&&n&&o}function W(e){var t,r,n,o=e.activeTooltipItem,i=e.graphicalItem,a=e.itemData,u=(D(i,o)?t="trapezoids":N(i,o)?t="sectors":I(i,o)&&(t="points"),t),c=D(i,o)?null==(r=o.tooltipPayload)||null==(r=r[0])||null==(r=r.payload)?void 0:r.payload:N(i,o)?null==(n=o.tooltipPayload)||null==(n=n[0])||null==(n=n.payload)?void 0:n.payload:I(i,o)?o.payload:{},l=a.filter(function(e,t){var r=f()(c,e),n=i.props[u].filter(function(e){var t;return(D(i,o)?t=R:N(i,o)?t=B:I(i,o)&&(t=L),t)(e,o)}),a=i.props[u].indexOf(n[n.length-1]);return r&&t===a});return a.indexOf(l[l.length-1])}},69229:(e,t,r)=>{var n=r(67472),o=r(38406),i=r(50523),a=r(84464),u=r(94380),c=r(39608),l=r(33497),s=r(35190),f="[object Arguments]",p="[object Array]",d="[object Object]",h=Object.prototype.hasOwnProperty;e.exports=function(e,t,r,y,v,m){var b=c(e),g=c(t),x=b?p:u(e),w=g?p:u(t);x=x==f?d:x,w=w==f?d:w;var O=x==d,j=w==d,S=x==w;if(S&&l(e)){if(!l(t))return!1;b=!0,O=!1}if(S&&!O)return m||(m=new n),b||s(e)?o(e,t,r,y,v,m):i(e,t,x,r,y,v,m);if(!(1&r)){var E=O&&h.call(e,"__wrapped__"),A=j&&h.call(t,"__wrapped__");if(E||A){var P=E?e.value():e,k=A?t.value():t;return m||(m=new n),v(P,k,r,y,m)}}return!!S&&(m||(m=new n),a(e,t,r,y,v,m))}},69363:(e,t,r)=>{var n=r(57213),o=r(18028),i=r(6305),a=r(39608);e.exports=function(e,t){return(a(e)?n:i)(e,o(t,3))}},69806:e=>{e.exports=function(e,t,r){for(var n=r-1,o=e.length;++n<o;)if(e[n]===t)return n;return -1}},70688:(e,t,r)=>{"use strict";r.d(t,{I:()=>X});var n=r(12115);function o(){}function i(e,t,r){e._context.bezierCurveTo((2*e._x0+e._x1)/3,(2*e._y0+e._y1)/3,(e._x0+2*e._x1)/3,(e._y0+2*e._y1)/3,(e._x0+4*e._x1+t)/6,(e._y0+4*e._y1+r)/6)}function a(e){this._context=e}function u(e){this._context=e}function c(e){this._context=e}a.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:i(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:i(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},u.prototype={areaStart:o,areaEnd:o,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._x2=e,this._y2=t;break;case 1:this._point=2,this._x3=e,this._y3=t;break;case 2:this._point=3,this._x4=e,this._y4=t,this._context.moveTo((this._x0+4*this._x1+e)/6,(this._y0+4*this._y1+t)/6);break;default:i(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},c.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+e)/6,n=(this._y0+4*this._y1+t)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:i(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};class l{constructor(e,t){this._context=e,this._x=t}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+e)/2,this._y0,this._x0,t,e,t):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+t)/2,e,this._y0,e,t)}this._x0=e,this._y0=t}}function s(e){this._context=e}function f(e){this._context=e}function p(e){return new f(e)}s.prototype={areaStart:o,areaEnd:o,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(e,t){e*=1,t*=1,this._point?this._context.lineTo(e,t):(this._point=1,this._context.moveTo(e,t))}};function d(e,t,r){var n=e._x1-e._x0,o=t-e._x1,i=(e._y1-e._y0)/(n||o<0&&-0),a=(r-e._y1)/(o||n<0&&-0);return((i<0?-1:1)+(a<0?-1:1))*Math.min(Math.abs(i),Math.abs(a),.5*Math.abs((i*o+a*n)/(n+o)))||0}function h(e,t){var r=e._x1-e._x0;return r?(3*(e._y1-e._y0)/r-t)/2:t}function y(e,t,r){var n=e._x0,o=e._y0,i=e._x1,a=e._y1,u=(i-n)/3;e._context.bezierCurveTo(n+u,o+u*t,i-u,a-u*r,i,a)}function v(e){this._context=e}function m(e){this._context=new b(e)}function b(e){this._context=e}function g(e){this._context=e}function x(e){var t,r,n=e.length-1,o=Array(n),i=Array(n),a=Array(n);for(o[0]=0,i[0]=2,a[0]=e[0]+2*e[1],t=1;t<n-1;++t)o[t]=1,i[t]=4,a[t]=4*e[t]+2*e[t+1];for(o[n-1]=2,i[n-1]=7,a[n-1]=8*e[n-1]+e[n],t=1;t<n;++t)r=o[t]/i[t-1],i[t]-=r,a[t]-=r*a[t-1];for(o[n-1]=a[n-1]/i[n-1],t=n-2;t>=0;--t)o[t]=(a[t]-o[t+1])/i[t];for(t=0,i[n-1]=(e[n]+o[n-1])/2;t<n-1;++t)i[t]=2*e[t+1]-o[t+1];return[o,i]}function w(e,t){this._context=e,this._t=t}f.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._context.lineTo(e,t)}}},v.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:y(this,this._t0,h(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){var r=NaN;if(t*=1,(e*=1)!==this._x1||t!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,y(this,h(this,r=d(this,e,t)),r);break;default:y(this,this._t0,r=d(this,e,t))}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t,this._t0=r}}},(m.prototype=Object.create(v.prototype)).point=function(e,t){v.prototype.point.call(this,t,e)},b.prototype={moveTo:function(e,t){this._context.moveTo(t,e)},closePath:function(){this._context.closePath()},lineTo:function(e,t){this._context.lineTo(t,e)},bezierCurveTo:function(e,t,r,n,o,i){this._context.bezierCurveTo(t,e,n,r,i,o)}},g.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var e=this._x,t=this._y,r=e.length;if(r)if(this._line?this._context.lineTo(e[0],t[0]):this._context.moveTo(e[0],t[0]),2===r)this._context.lineTo(e[1],t[1]);else for(var n=x(e),o=x(t),i=0,a=1;a<r;++i,++a)this._context.bezierCurveTo(n[0][i],o[0][i],n[1][i],o[1][i],e[a],t[a]);(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(e,t){this._x.push(+e),this._y.push(+t)}},w.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,t),this._context.lineTo(e,t);else{var r=this._x*(1-this._t)+e*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,t)}}this._x=e,this._y=t}};var O=r(9819),j=r(85654),S=r(31847);function E(e){return e[0]}function A(e){return e[1]}function P(e,t){var r=(0,j.A)(!0),n=null,o=p,i=null,a=(0,S.i)(u);function u(u){var c,l,s,f=(u=(0,O.A)(u)).length,p=!1;for(null==n&&(i=o(s=a())),c=0;c<=f;++c)!(c<f&&r(l=u[c],c,u))===p&&((p=!p)?i.lineStart():i.lineEnd()),p&&i.point(+e(l,c,u),+t(l,c,u));if(s)return i=null,s+""||null}return e="function"==typeof e?e:void 0===e?E:(0,j.A)(e),t="function"==typeof t?t:void 0===t?A:(0,j.A)(t),u.x=function(t){return arguments.length?(e="function"==typeof t?t:(0,j.A)(+t),u):e},u.y=function(e){return arguments.length?(t="function"==typeof e?e:(0,j.A)(+e),u):t},u.defined=function(e){return arguments.length?(r="function"==typeof e?e:(0,j.A)(!!e),u):r},u.curve=function(e){return arguments.length?(o=e,null!=n&&(i=o(n)),u):o},u.context=function(e){return arguments.length?(null==e?n=i=null:i=o(n=e),u):n},u}function k(e,t,r){var n=null,o=(0,j.A)(!0),i=null,a=p,u=null,c=(0,S.i)(l);function l(l){var s,f,p,d,h,y=(l=(0,O.A)(l)).length,v=!1,m=Array(y),b=Array(y);for(null==i&&(u=a(h=c())),s=0;s<=y;++s){if(!(s<y&&o(d=l[s],s,l))===v)if(v=!v)f=s,u.areaStart(),u.lineStart();else{for(u.lineEnd(),u.lineStart(),p=s-1;p>=f;--p)u.point(m[p],b[p]);u.lineEnd(),u.areaEnd()}v&&(m[s]=+e(d,s,l),b[s]=+t(d,s,l),u.point(n?+n(d,s,l):m[s],r?+r(d,s,l):b[s]))}if(h)return u=null,h+""||null}function s(){return P().defined(o).curve(a).context(i)}return e="function"==typeof e?e:void 0===e?E:(0,j.A)(+e),t="function"==typeof t?t:void 0===t?(0,j.A)(0):(0,j.A)(+t),r="function"==typeof r?r:void 0===r?A:(0,j.A)(+r),l.x=function(t){return arguments.length?(e="function"==typeof t?t:(0,j.A)(+t),n=null,l):e},l.x0=function(t){return arguments.length?(e="function"==typeof t?t:(0,j.A)(+t),l):e},l.x1=function(e){return arguments.length?(n=null==e?null:"function"==typeof e?e:(0,j.A)(+e),l):n},l.y=function(e){return arguments.length?(t="function"==typeof e?e:(0,j.A)(+e),r=null,l):t},l.y0=function(e){return arguments.length?(t="function"==typeof e?e:(0,j.A)(+e),l):t},l.y1=function(e){return arguments.length?(r=null==e?null:"function"==typeof e?e:(0,j.A)(+e),l):r},l.lineX0=l.lineY0=function(){return s().x(e).y(t)},l.lineY1=function(){return s().x(e).y(r)},l.lineX1=function(){return s().x(n).y(t)},l.defined=function(e){return arguments.length?(o="function"==typeof e?e:(0,j.A)(!!e),l):o},l.curve=function(e){return arguments.length?(a=e,null!=i&&(u=a(i)),l):a},l.context=function(e){return arguments.length?(null==e?i=u=null:u=a(i=e),l):i},l}var M=r(23633),T=r.n(M),_=r(40139),C=r.n(_),D=r(52596),N=r(43597),I=r(70788),R=r(16377);function B(e){return(B="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function L(){return(L=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function W(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function F(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?W(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=B(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=B(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==B(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):W(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var z={curveBasisClosed:function(e){return new u(e)},curveBasisOpen:function(e){return new c(e)},curveBasis:function(e){return new a(e)},curveBumpX:function(e){return new l(e,!0)},curveBumpY:function(e){return new l(e,!1)},curveLinearClosed:function(e){return new s(e)},curveLinear:p,curveMonotoneX:function(e){return new v(e)},curveMonotoneY:function(e){return new m(e)},curveNatural:function(e){return new g(e)},curveStep:function(e){return new w(e,.5)},curveStepAfter:function(e){return new w(e,1)},curveStepBefore:function(e){return new w(e,0)}},U=function(e){return e.x===+e.x&&e.y===+e.y},$=function(e){return e.x},Y=function(e){return e.y},H=function(e,t){if(C()(e))return e;var r="curve".concat(T()(e));return("curveMonotone"===r||"curveBump"===r)&&t?z["".concat(r).concat("vertical"===t?"Y":"X")]:z[r]||p},q=function(e){var t,r=e.type,n=e.points,o=void 0===n?[]:n,i=e.baseLine,a=e.layout,u=e.connectNulls,c=void 0!==u&&u,l=H(void 0===r?"linear":r,a),s=c?o.filter(function(e){return U(e)}):o;if(Array.isArray(i)){var f=c?i.filter(function(e){return U(e)}):i,p=s.map(function(e,t){return F(F({},e),{},{base:f[t]})});return(t="vertical"===a?k().y(Y).x1($).x0(function(e){return e.base.x}):k().x($).y1(Y).y0(function(e){return e.base.y})).defined(U).curve(l),t(p)}return(t="vertical"===a&&(0,R.Et)(i)?k().y(Y).x1($).x0(i):(0,R.Et)(i)?k().x($).y1(Y).y0(i):P().x($).y(Y)).defined(U).curve(l),t(s)},X=function(e){var t=e.className,r=e.points,o=e.path,i=e.pathRef;if((!r||!r.length)&&!o)return null;var a=r&&r.length?q(e):o;return n.createElement("path",L({},(0,I.J9)(e,!1),(0,N._U)(e),{className:(0,D.A)("recharts-curve",t),d:a,ref:i}))}},70771:(e,t,r)=>{var n=r(98233),o=r(48611);e.exports=function(e){return"symbol"==typeof e||o(e)&&"[object Symbol]"==n(e)}},70788:(e,t,r)=>{"use strict";r.d(t,{AW:()=>R,BU:()=>P,J9:()=>_,Me:()=>k,Mn:()=>O,OV:()=>C,X_:()=>I,aS:()=>A,ee:()=>N});var n=r(48973),o=r.n(n),i=r(59882),a=r.n(i),u=r(15438),c=r.n(u),l=r(40139),s=r.n(l),f=r(67460),p=r.n(f),d=r(12115),h=r(50330),y=r(16377),v=r(15232),m=r(43597),b=["children"],g=["children"];function x(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}var w={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart",contextmenu:"onContextMenu",dblclick:"onDoubleClick"},O=function(e){return"string"==typeof e?e:e?e.displayName||e.name||"Component":""},j=null,S=null,E=function e(t){if(t===j&&Array.isArray(S))return S;var r=[];return d.Children.forEach(t,function(t){a()(t)||((0,h.isFragment)(t)?r=r.concat(e(t.props.children)):r.push(t))}),S=r,j=t,r};function A(e,t){var r=[],n=[];return n=Array.isArray(t)?t.map(function(e){return O(e)}):[O(t)],E(e).forEach(function(e){var t=o()(e,"type.displayName")||o()(e,"type.name");-1!==n.indexOf(t)&&r.push(e)}),r}function P(e,t){var r=A(e,t);return r&&r[0]}var k=function(e){if(!e||!e.props)return!1;var t=e.props,r=t.width,n=t.height;return!!(0,y.Et)(r)&&!(r<=0)&&!!(0,y.Et)(n)&&!(n<=0)},M=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],T=function(e,t,r,n){var o,i=null!=(o=null===m.VU||void 0===m.VU?void 0:m.VU[n])?o:[];return t.startsWith("data-")||!s()(e)&&(n&&i.includes(t)||m.QQ.includes(t))||r&&m.j2.includes(t)},_=function(e,t,r){if(!e||"function"==typeof e||"boolean"==typeof e)return null;var n=e;if((0,d.isValidElement)(e)&&(n=e.props),!p()(n))return null;var o={};return Object.keys(n).forEach(function(e){var i;T(null==(i=n)?void 0:i[e],e,t,r)&&(o[e]=n[e])}),o},C=function e(t,r){if(t===r)return!0;var n=d.Children.count(t);if(n!==d.Children.count(r))return!1;if(0===n)return!0;if(1===n)return D(Array.isArray(t)?t[0]:t,Array.isArray(r)?r[0]:r);for(var o=0;o<n;o++){var i=t[o],a=r[o];if(Array.isArray(i)||Array.isArray(a)){if(!e(i,a))return!1}else if(!D(i,a))return!1}return!0},D=function(e,t){if(a()(e)&&a()(t))return!0;if(!a()(e)&&!a()(t)){var r=e.props||{},n=r.children,o=x(r,b),i=t.props||{},u=i.children,c=x(i,g);if(n&&u)return(0,v.b)(o,c)&&C(n,u);if(!n&&!u)return(0,v.b)(o,c)}return!1},N=function(e,t){var r=[],n={};return E(e).forEach(function(e,o){var i;if((i=e)&&i.type&&c()(i.type)&&M.indexOf(i.type)>=0)r.push(e);else if(e){var a=O(e.type),u=t[a]||{},l=u.handler,s=u.once;if(l&&(!s||!n[a])){var f=l(e,a,o);r.push(f),n[a]=!0}}}),r},I=function(e){var t=e&&e.type;return t&&w[t]?w[t]:null},R=function(e,t){return E(t).indexOf(e)}},70966:e=>{e.exports=function(e,t){return function(r){return e(t(r))}}},71571:(e,t,r)=>{var n=r(92313),o=r(18028),i=r(51445),a=r(39608),u=r(39641);e.exports=function(e,t,r){var c=a(e)?n:i;return r&&u(e,t,r)&&(t=void 0),c(e,o(t,3))}},72043:(e,t,r)=>{e.exports=r(70966)(Object.keys,Object)},72790:(e,t,r)=>{"use strict";r.d(t,{u:()=>c});var n=r(12115),o=r(52596),i=r(70788),a=["children","width","height","viewBox","className","style","title","desc"];function u(){return(u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function c(e){var t=e.children,r=e.width,c=e.height,l=e.viewBox,s=e.className,f=e.style,p=e.title,d=e.desc,h=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,a),y=l||{width:r,height:c,x:0,y:0},v=(0,o.A)("recharts-surface",s);return n.createElement("svg",u({},(0,i.J9)(h,!0,"svg"),{className:v,width:r,height:c,style:f,viewBox:"".concat(y.x," ").concat(y.y," ").concat(y.width," ").concat(y.height)}),n.createElement("title",null,p),n.createElement("desc",null,d),t)}},72948:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},73726:(e,t,r)=>{e.exports=r(70966)(Object.getPrototypeOf,Object)},73800:(e,t,r)=>{var n=r(39608),o=r(79595),i=r(37835),a=r(85855);e.exports=function(e,t){return n(e)?e:o(e,t)?[e]:i(a(e))}},73956:(e,t,r)=>{var n=r(5658);e.exports=function(e){return n(this,e).has(e)}},74166:e=>{e.exports=function(e){var t=-1,r=Array(e.size);return e.forEach(function(e){r[++t]=e}),r}},74366:e=>{var t=Math.ceil,r=Math.max;e.exports=function(e,n,o,i){for(var a=-1,u=r(t((n-e)/(o||1)),0),c=Array(u);u--;)c[i?u:++a]=e,e+=o;return c}},74544:(e,t,r)=>{var n=r(5658);e.exports=function(e){return n(this,e).get(e)}},74888:(e,t,r)=>{var n=r(53516);e.exports=function(e,t){var r=!0;return n(e,function(e,n,o){return r=!!t(e,n,o)}),r}},74925:(e,t,r)=>{var n=r(28897),o=r(20480),i=r(18028);e.exports=function(e,t){var r={};return t=i(t,3),o(e,function(e,o,i){n(r,o,t(e,o,i))}),r}},75031:(e,t,r)=>{var n=r(18028),o=r(22471),i=r(35095);e.exports=function(e){return function(t,r,a){var u=Object(t);if(!o(t)){var c=n(r,3);t=i(t),r=function(e){return c(u[e],e,u)}}var l=e(t,r,a);return l>-1?u[c?t[l]:l]:void 0}}},75145:(e,t,r)=>{var n=r(50851),o=r(65531),i=r(17855);e.exports=function(e){return o(e)?i(e):n(e)}},75899:(e,t,r)=>{e.exports=r(83711)(Object,"create")},76685:(e,t,r)=>{var n=r(82500);e.exports=function(){return n.Date.now()}},76957:(e,t,r)=>{e.exports=r(82500).Uint8Array},77283:(e,t,r)=>{"use strict";r.d(t,{h:()=>v});var n=r(12115),o=r(52596),i=r(70788),a=r(25641),u=r(16377);function c(e){return(c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function s(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function f(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?s(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=c(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=c(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==c(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var p=function(e){var t=e.cx,r=e.cy,n=e.radius,o=e.angle,i=e.sign,u=e.isExternal,c=e.cornerRadius,l=e.cornerIsExternal,s=c*(u?1:-1)+n,f=Math.asin(c/s)/a.Kg,p=l?o:o+i*f;return{center:(0,a.IZ)(t,r,s,p),circleTangency:(0,a.IZ)(t,r,n,p),lineTangency:(0,a.IZ)(t,r,s*Math.cos(f*a.Kg),l?o-i*f:o),theta:f}},d=function(e){var t=e.cx,r=e.cy,n=e.innerRadius,o=e.outerRadius,i=e.startAngle,c=e.endAngle,l=(0,u.sA)(c-i)*Math.min(Math.abs(c-i),359.999),s=i+l,f=(0,a.IZ)(t,r,o,i),p=(0,a.IZ)(t,r,o,s),d="M ".concat(f.x,",").concat(f.y,"\n    A ").concat(o,",").concat(o,",0,\n    ").concat(+(Math.abs(l)>180),",").concat(+(i>s),",\n    ").concat(p.x,",").concat(p.y,"\n  ");if(n>0){var h=(0,a.IZ)(t,r,n,i),y=(0,a.IZ)(t,r,n,s);d+="L ".concat(y.x,",").concat(y.y,"\n            A ").concat(n,",").concat(n,",0,\n            ").concat(+(Math.abs(l)>180),",").concat(+(i<=s),",\n            ").concat(h.x,",").concat(h.y," Z")}else d+="L ".concat(t,",").concat(r," Z");return d},h=function(e){var t=e.cx,r=e.cy,n=e.innerRadius,o=e.outerRadius,i=e.cornerRadius,a=e.forceCornerRadius,c=e.cornerIsExternal,l=e.startAngle,s=e.endAngle,f=(0,u.sA)(s-l),h=p({cx:t,cy:r,radius:o,angle:l,sign:f,cornerRadius:i,cornerIsExternal:c}),y=h.circleTangency,v=h.lineTangency,m=h.theta,b=p({cx:t,cy:r,radius:o,angle:s,sign:-f,cornerRadius:i,cornerIsExternal:c}),g=b.circleTangency,x=b.lineTangency,w=b.theta,O=c?Math.abs(l-s):Math.abs(l-s)-m-w;if(O<0)return a?"M ".concat(v.x,",").concat(v.y,"\n        a").concat(i,",").concat(i,",0,0,1,").concat(2*i,",0\n        a").concat(i,",").concat(i,",0,0,1,").concat(-(2*i),",0\n      "):d({cx:t,cy:r,innerRadius:n,outerRadius:o,startAngle:l,endAngle:s});var j="M ".concat(v.x,",").concat(v.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(f<0),",").concat(y.x,",").concat(y.y,"\n    A").concat(o,",").concat(o,",0,").concat(+(O>180),",").concat(+(f<0),",").concat(g.x,",").concat(g.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(f<0),",").concat(x.x,",").concat(x.y,"\n  ");if(n>0){var S=p({cx:t,cy:r,radius:n,angle:l,sign:f,isExternal:!0,cornerRadius:i,cornerIsExternal:c}),E=S.circleTangency,A=S.lineTangency,P=S.theta,k=p({cx:t,cy:r,radius:n,angle:s,sign:-f,isExternal:!0,cornerRadius:i,cornerIsExternal:c}),M=k.circleTangency,T=k.lineTangency,_=k.theta,C=c?Math.abs(l-s):Math.abs(l-s)-P-_;if(C<0&&0===i)return"".concat(j,"L").concat(t,",").concat(r,"Z");j+="L".concat(T.x,",").concat(T.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(f<0),",").concat(M.x,",").concat(M.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(C>180),",").concat(+(f>0),",").concat(E.x,",").concat(E.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(f<0),",").concat(A.x,",").concat(A.y,"Z")}else j+="L".concat(t,",").concat(r,"Z");return j},y={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},v=function(e){var t,r=f(f({},y),e),a=r.cx,c=r.cy,s=r.innerRadius,p=r.outerRadius,v=r.cornerRadius,m=r.forceCornerRadius,b=r.cornerIsExternal,g=r.startAngle,x=r.endAngle,w=r.className;if(p<s||g===x)return null;var O=(0,o.A)("recharts-sector",w),j=p-s,S=(0,u.F4)(v,j,0,!0);return t=S>0&&360>Math.abs(g-x)?h({cx:a,cy:c,innerRadius:s,outerRadius:p,cornerRadius:Math.min(S,j/2),forceCornerRadius:m,cornerIsExternal:b,startAngle:g,endAngle:x}):d({cx:a,cy:c,innerRadius:s,outerRadius:p,startAngle:g,endAngle:x}),n.createElement("path",l({},(0,i.J9)(r,!0),{className:O,d:t,role:"img"}))}},77969:(e,t,r)=>{var n=r(91569),o=r(36314);e.exports=function e(t,r,i,a,u){var c=-1,l=t.length;for(i||(i=o),u||(u=[]);++c<l;){var s=t[c];r>0&&i(s)?r>1?e(s,r-1,i,a,u):n(u,s):a||(u[u.length]=s)}return u}},79095:(e,t,r)=>{"use strict";r.d(t,{E:()=>B});var n=r(12115),o=r(59882),i=r.n(o),a=r(52596),u=r(16377),c=r(41643),l=r(70788),s=r(46605);function f(e){return(f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function p(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(e){l=!0,o=e}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(e,t)||function(e,t){if(e){if("string"==typeof e)return d(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return d(e,t)}}(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function d(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function h(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,function(e){var t=function(e,t){if("object"!=f(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=f(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==f(t)?t:t+""}(n.key),n)}}var y=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,v=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,m=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,b=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,g={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},x=Object.keys(g),w=function(){var e,t;function r(e,t){if(!(this instanceof r))throw TypeError("Cannot call a class as a function");this.num=e,this.unit=t,this.num=e,this.unit=t,Number.isNaN(e)&&(this.unit=""),""===t||m.test(t)||(this.num=NaN,this.unit=""),x.includes(t)&&(this.num=e*g[t],this.unit="px")}return e=[{key:"add",value:function(e){return this.unit!==e.unit?new r(NaN,""):new r(this.num+e.num,this.unit)}},{key:"subtract",value:function(e){return this.unit!==e.unit?new r(NaN,""):new r(this.num-e.num,this.unit)}},{key:"multiply",value:function(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new r(NaN,""):new r(this.num*e.num,this.unit||e.unit)}},{key:"divide",value:function(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new r(NaN,""):new r(this.num/e.num,this.unit||e.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}],t=[{key:"parse",value:function(e){var t,n=p(null!=(t=b.exec(e))?t:[],3),o=n[1],i=n[2];return new r(parseFloat(o),null!=i?i:"")}}],e&&h(r.prototype,e),t&&h(r,t),Object.defineProperty(r,"prototype",{writable:!1}),r}();function O(e){if(e.includes("NaN"))return"NaN";for(var t=e;t.includes("*")||t.includes("/");){var r,n=p(null!=(r=y.exec(t))?r:[],4),o=n[1],i=n[2],a=n[3],u=w.parse(null!=o?o:""),c=w.parse(null!=a?a:""),l="*"===i?u.multiply(c):u.divide(c);if(l.isNaN())return"NaN";t=t.replace(y,l.toString())}for(;t.includes("+")||/.-\d+(?:\.\d+)?/.test(t);){var s,f=p(null!=(s=v.exec(t))?s:[],4),d=f[1],h=f[2],m=f[3],b=w.parse(null!=d?d:""),g=w.parse(null!=m?m:""),x="+"===h?b.add(g):b.subtract(g);if(x.isNaN())return"NaN";t=t.replace(v,x.toString())}return t}var j=/\(([^()]*)\)/;function S(e){var t=function(e){try{var t;return t=e.replace(/\s+/g,""),t=function(e){for(var t=e;t.includes("(");){var r=p(j.exec(t),2)[1];t=t.replace(j,O(r))}return t}(t),t=O(t)}catch(e){return"NaN"}}(e.slice(5,-1));return"NaN"===t?"":t}var E=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],A=["dx","dy","angle","className","breakAll"];function P(){return(P=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function k(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}function M(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(e){l=!0,o=e}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(e,t)||function(e,t){if(e){if("string"==typeof e)return T(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return T(e,t)}}(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function T(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var _=/[ \f\n\r\t\v\u2028\u2029]+/,C=function(e){var t=e.children,r=e.breakAll,n=e.style;try{var o=[];i()(t)||(o=r?t.toString().split(""):t.toString().split(_));var a=o.map(function(e){return{word:e,width:(0,s.Pu)(e,n).width}}),u=r?0:(0,s.Pu)("\xa0",n).width;return{wordsWithComputedWidth:a,spaceWidth:u}}catch(e){return null}},D=function(e,t,r,n,o){var i,a=e.maxLines,c=e.children,l=e.style,s=e.breakAll,f=(0,u.Et)(a),p=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return e.reduce(function(e,t){var i=t.word,a=t.width,u=e[e.length-1];return u&&(null==n||o||u.width+a+r<Number(n))?(u.words.push(i),u.width+=a+r):e.push({words:[i],width:a}),e},[])},d=p(t);if(!f)return d;for(var h=function(e){var t=p(C({breakAll:s,style:l,children:c.slice(0,e)+"…"}).wordsWithComputedWidth);return[t.length>a||t.reduce(function(e,t){return e.width>t.width?e:t}).width>Number(n),t]},y=0,v=c.length-1,m=0;y<=v&&m<=c.length-1;){var b=Math.floor((y+v)/2),g=M(h(b-1),2),x=g[0],w=g[1],O=M(h(b),1)[0];if(x||O||(y=b+1),x&&O&&(v=b-1),!x&&O){i=w;break}m++}return i||d},N=function(e){return[{words:i()(e)?[]:e.toString().split(_)}]},I=function(e){var t=e.width,r=e.scaleToFit,n=e.children,o=e.style,i=e.breakAll,a=e.maxLines;if((t||r)&&!c.m.isSsr){var u=C({breakAll:i,children:n,style:o});if(!u)return N(n);var l=u.wordsWithComputedWidth,s=u.spaceWidth;return D({breakAll:i,children:n,maxLines:a,style:o},l,s,t,r)}return N(n)},R="#808080",B=function(e){var t,r=e.x,o=void 0===r?0:r,i=e.y,c=void 0===i?0:i,s=e.lineHeight,f=void 0===s?"1em":s,p=e.capHeight,d=void 0===p?"0.71em":p,h=e.scaleToFit,y=void 0!==h&&h,v=e.textAnchor,m=e.verticalAnchor,b=e.fill,g=void 0===b?R:b,x=k(e,E),w=(0,n.useMemo)(function(){return I({breakAll:x.breakAll,children:x.children,maxLines:x.maxLines,scaleToFit:y,style:x.style,width:x.width})},[x.breakAll,x.children,x.maxLines,y,x.style,x.width]),O=x.dx,j=x.dy,M=x.angle,T=x.className,_=x.breakAll,C=k(x,A);if(!(0,u.vh)(o)||!(0,u.vh)(c))return null;var D=o+((0,u.Et)(O)?O:0),N=c+((0,u.Et)(j)?j:0);switch(void 0===m?"end":m){case"start":t=S("calc(".concat(d,")"));break;case"middle":t=S("calc(".concat((w.length-1)/2," * -").concat(f," + (").concat(d," / 2))"));break;default:t=S("calc(".concat(w.length-1," * -").concat(f,")"))}var B=[];if(y){var L=w[0].width,W=x.width;B.push("scale(".concat(((0,u.Et)(W)?W/L:1)/L,")"))}return M&&B.push("rotate(".concat(M,", ").concat(D,", ").concat(N,")")),B.length&&(C.transform=B.join(" ")),n.createElement("text",P({},(0,l.J9)(C,!0),{x:D,y:N,className:(0,a.A)("recharts-text",T),textAnchor:void 0===v?"start":v,fill:g.includes("url")?R:g}),w.map(function(e,r){var o=e.words.join(_?"":" ");return n.createElement("tspan",{x:D,dy:0===r?t:f,key:"".concat(o,"-").concat(r)},o)}))}},79399:(e,t,r)=>{"use strict";var n=r(72948);function o(){}function i(){}i.resetWarningCache=o,e.exports=function(){function e(e,t,r,o,i,a){if(a!==n){var u=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw u.name="Invariant Violation",u}}function t(){return e}e.isRequired=e;var r={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:i,resetWarningCache:o};return r.PropTypes=r,r}},79401:e=>{e.exports=function(e,t){for(var r=-1,n=Array(e);++r<e;)n[r]=t(r);return n}},79595:(e,t,r)=>{var n=r(39608),o=r(70771),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;e.exports=function(e,t){if(n(e))return!1;var r=typeof e;return!!("number"==r||"symbol"==r||"boolean"==r||null==e||o(e))||a.test(e)||!i.test(e)||null!=t&&e in Object(t)}},81519:(e,t,r)=>{"use strict";r.d(t,{A:()=>i,z:()=>a});var n=r(28749),o=r(95442);function i(){var e,t,r=(0,o.A)().unknown(void 0),a=r.domain,u=r.range,c=0,l=1,s=!1,f=0,p=0,d=.5;function h(){var r=a().length,n=l<c,o=n?l:c,i=n?c:l;e=(i-o)/Math.max(1,r-f+2*p),s&&(e=Math.floor(e)),o+=(i-o-e*(r-f))*d,t=e*(1-f),s&&(o=Math.round(o),t=Math.round(t));var h=(function(e,t,r){e*=1,t*=1,r=(o=arguments.length)<2?(t=e,e=0,1):o<3?1:+r;for(var n=-1,o=0|Math.max(0,Math.ceil((t-e)/r)),i=Array(o);++n<o;)i[n]=e+n*r;return i})(r).map(function(t){return o+e*t});return u(n?h.reverse():h)}return delete r.unknown,r.domain=function(e){return arguments.length?(a(e),h()):a()},r.range=function(e){return arguments.length?([c,l]=e,c*=1,l*=1,h()):[c,l]},r.rangeRound=function(e){return[c,l]=e,c*=1,l*=1,s=!0,h()},r.bandwidth=function(){return t},r.step=function(){return e},r.round=function(e){return arguments.length?(s=!!e,h()):s},r.padding=function(e){return arguments.length?(f=Math.min(1,p=+e),h()):f},r.paddingInner=function(e){return arguments.length?(f=Math.min(1,e),h()):f},r.paddingOuter=function(e){return arguments.length?(p=+e,h()):p},r.align=function(e){return arguments.length?(d=Math.max(0,Math.min(1,e)),h()):d},r.copy=function(){return i(a(),[c,l]).round(s).paddingInner(f).paddingOuter(p).align(d)},n.C.apply(h(),arguments)}function a(){return function e(t){var r=t.copy;return t.padding=t.paddingOuter,delete t.paddingInner,delete t.paddingOuter,t.copy=function(){return e(r())},t}(i.apply(null,arguments).paddingInner(1))}},82500:(e,t,r)=>{var n=r(7985),o="object"==typeof self&&self&&self.Object===Object&&self;e.exports=n||o||Function("return this")()},82596:(e,t,r)=>{var n=r(38008),o=r(31431),i=r(74166);e.exports=n&&1/i(new n([,-0]))[1]==1/0?function(e){return new n(e)}:o},82661:e=>{"use strict";var t=Object.prototype.hasOwnProperty,r="~";function n(){}function o(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function i(e,t,n,i,a){if("function"!=typeof n)throw TypeError("The listener must be a function");var u=new o(n,i||e,a),c=r?r+t:t;return e._events[c]?e._events[c].fn?e._events[c]=[e._events[c],u]:e._events[c].push(u):(e._events[c]=u,e._eventsCount++),e}function a(e,t){0==--e._eventsCount?e._events=new n:delete e._events[t]}function u(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1)),u.prototype.eventNames=function(){var e,n,o=[];if(0===this._eventsCount)return o;for(n in e=this._events)t.call(e,n)&&o.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?o.concat(Object.getOwnPropertySymbols(e)):o},u.prototype.listeners=function(e){var t=r?r+e:e,n=this._events[t];if(!n)return[];if(n.fn)return[n.fn];for(var o=0,i=n.length,a=Array(i);o<i;o++)a[o]=n[o].fn;return a},u.prototype.listenerCount=function(e){var t=r?r+e:e,n=this._events[t];return n?n.fn?1:n.length:0},u.prototype.emit=function(e,t,n,o,i,a){var u=r?r+e:e;if(!this._events[u])return!1;var c,l,s=this._events[u],f=arguments.length;if(s.fn){switch(s.once&&this.removeListener(e,s.fn,void 0,!0),f){case 1:return s.fn.call(s.context),!0;case 2:return s.fn.call(s.context,t),!0;case 3:return s.fn.call(s.context,t,n),!0;case 4:return s.fn.call(s.context,t,n,o),!0;case 5:return s.fn.call(s.context,t,n,o,i),!0;case 6:return s.fn.call(s.context,t,n,o,i,a),!0}for(l=1,c=Array(f-1);l<f;l++)c[l-1]=arguments[l];s.fn.apply(s.context,c)}else{var p,d=s.length;for(l=0;l<d;l++)switch(s[l].once&&this.removeListener(e,s[l].fn,void 0,!0),f){case 1:s[l].fn.call(s[l].context);break;case 2:s[l].fn.call(s[l].context,t);break;case 3:s[l].fn.call(s[l].context,t,n);break;case 4:s[l].fn.call(s[l].context,t,n,o);break;default:if(!c)for(p=1,c=Array(f-1);p<f;p++)c[p-1]=arguments[p];s[l].fn.apply(s[l].context,c)}}return!0},u.prototype.on=function(e,t,r){return i(this,e,t,r,!1)},u.prototype.once=function(e,t,r){return i(this,e,t,r,!0)},u.prototype.removeListener=function(e,t,n,o){var i=r?r+e:e;if(!this._events[i])return this;if(!t)return a(this,i),this;var u=this._events[i];if(u.fn)u.fn!==t||o&&!u.once||n&&u.context!==n||a(this,i);else{for(var c=0,l=[],s=u.length;c<s;c++)(u[c].fn!==t||o&&!u[c].once||n&&u[c].context!==n)&&l.push(u[c]);l.length?this._events[i]=1===l.length?l[0]:l:a(this,i)}return this},u.prototype.removeAllListeners=function(e){var t;return e?(t=r?r+e:e,this._events[t]&&a(this,t)):(this._events=new n,this._eventsCount=0),this},u.prototype.off=u.prototype.removeListener,u.prototype.addListener=u.prototype.on,u.prefixed=r,u.EventEmitter=u,e.exports=u},82954:e=>{e.exports=function(e,t){return e.has(t)}},83134:(e,t,r)=>{var n=r(58918),o=r(21582),i=r(18028);e.exports=function(e,t){return e&&e.length?n(e,i(t,2),o):void 0}},83197:(e,t,r)=>{"use strict";r.d(t,{g:()=>l});var n=r(24026),o=r(12814),i=r(70788);function a(e){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function u(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function c(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?u(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=a(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=a(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==a(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):u(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var l=function(e){var t,r=e.children,a=e.formattedGraphicalItems,u=e.legendWidth,l=e.legendContent,s=(0,i.BU)(r,n.s);if(!s)return null;var f=n.s.defaultProps,p=void 0!==f?c(c({},f),s.props):{};return t=s.props&&s.props.payload?s.props&&s.props.payload:"children"===l?(a||[]).reduce(function(e,t){var r=t.item,n=t.props,o=n.sectors||n.data||[];return e.concat(o.map(function(e){return{type:s.props.iconType||r.props.legendType,value:e.name,color:e.fill,payload:e}}))},[]):(a||[]).map(function(e){var t=e.item,r=t.type.defaultProps,n=void 0!==r?c(c({},r),t.props):{},i=n.dataKey,a=n.name,u=n.legendType;return{inactive:n.hide,dataKey:i,type:p.iconType||u||"square",color:(0,o.Ps)(t),value:a||i,payload:n}}),c(c(c({},p),n.s.getWithHeight(s,u)),{},{payload:t,item:s})}},83394:(e,t,r)=>{"use strict";r.d(t,{y:()=>F});var n=r(12115),o=r(52596),i=r(9557),a=r(60245),u=r.n(a),c=r(59882),l=r.n(c),s=r(2348),f=r(94011),p=r(54811),d=r(36079),h=r(16377),y=r(70788),v=r(41643),m=r(12814),b=r(43597),g=r(93179),x=r(67790),w=["x","y"];function O(e){return(O="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function j(){return(j=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function S(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function E(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?S(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=O(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=O(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==O(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):S(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function A(e,t){var r=e.x,n=e.y,o=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,w),i=parseInt("".concat(r),10),a=parseInt("".concat(n),10),u=parseInt("".concat(t.height||o.height),10),c=parseInt("".concat(t.width||o.width),10);return E(E(E(E(E({},t),o),i?{x:i}:{}),a?{y:a}:{}),{},{height:u,width:c,name:t.name,radius:t.radius})}function P(e){return n.createElement(x.yp,j({shapeType:"rectangle",propTransformer:A,activeClassName:"recharts-active-bar"},e))}var k=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return function(r,n){if("number"==typeof e)return e;var o="number"==typeof r;return o?e(r,n):(o||(0,g.A)(!1),t)}},M=["value","background"];function T(e){return(T="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _(){return(_=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function C(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function D(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?C(Object(r),!0).forEach(function(t){L(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):C(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function N(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,W(n.key),n)}}function I(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(I=function(){return!!e})()}function R(e){return(R=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function B(e,t){return(B=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function L(e,t,r){return(t=W(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function W(e){var t=function(e,t){if("object"!=T(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=T(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==T(t)?t:t+""}var F=function(e){var t,r;function a(){var e,t,r;if(!(this instanceof a))throw TypeError("Cannot call a class as a function");for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];return t=a,r=[].concat(o),t=R(t),L(e=function(e,t){if(t&&("object"===T(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");var r=e;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,I()?Reflect.construct(t,r||[],R(this).constructor):t.apply(this,r)),"state",{isAnimationFinished:!1}),L(e,"id",(0,h.NF)("recharts-bar-")),L(e,"handleAnimationEnd",function(){var t=e.props.onAnimationEnd;e.setState({isAnimationFinished:!0}),t&&t()}),L(e,"handleAnimationStart",function(){var t=e.props.onAnimationStart;e.setState({isAnimationFinished:!1}),t&&t()}),e}if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");return a.prototype=Object.create(e&&e.prototype,{constructor:{value:a,writable:!0,configurable:!0}}),Object.defineProperty(a,"prototype",{writable:!1}),e&&B(a,e),t=[{key:"renderRectanglesStatically",value:function(e){var t=this,r=this.props,o=r.shape,i=r.dataKey,a=r.activeIndex,u=r.activeBar,c=(0,y.J9)(this.props,!1);return e&&e.map(function(e,r){var l=r===a,f=D(D(D({},c),e),{},{isActive:l,option:l?u:o,index:r,dataKey:i,onAnimationStart:t.handleAnimationStart,onAnimationEnd:t.handleAnimationEnd});return n.createElement(s.W,_({className:"recharts-bar-rectangle"},(0,b.XC)(t.props,e,r),{key:"rectangle-".concat(null==e?void 0:e.x,"-").concat(null==e?void 0:e.y,"-").concat(null==e?void 0:e.value,"-").concat(r)}),n.createElement(P,f))})}},{key:"renderRectanglesWithAnimation",value:function(){var e=this,t=this.props,r=t.data,o=t.layout,a=t.isAnimationActive,u=t.animationBegin,c=t.animationDuration,l=t.animationEasing,f=t.animationId,p=this.state.prevData;return n.createElement(i.Ay,{begin:u,duration:c,isActive:a,easing:l,from:{t:0},to:{t:1},key:"bar-".concat(f),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(t){var i=t.t,a=r.map(function(e,t){var r=p&&p[t];if(r){var n=(0,h.Dj)(r.x,e.x),a=(0,h.Dj)(r.y,e.y),u=(0,h.Dj)(r.width,e.width),c=(0,h.Dj)(r.height,e.height);return D(D({},e),{},{x:n(i),y:a(i),width:u(i),height:c(i)})}if("horizontal"===o){var l=(0,h.Dj)(0,e.height)(i);return D(D({},e),{},{y:e.y+e.height-l,height:l})}var s=(0,h.Dj)(0,e.width)(i);return D(D({},e),{},{width:s})});return n.createElement(s.W,null,e.renderRectanglesStatically(a))})}},{key:"renderRectangles",value:function(){var e=this.props,t=e.data,r=e.isAnimationActive,n=this.state.prevData;return r&&t&&t.length&&(!n||!u()(n,t))?this.renderRectanglesWithAnimation():this.renderRectanglesStatically(t)}},{key:"renderBackground",value:function(){var e=this,t=this.props,r=t.data,o=t.dataKey,i=t.activeIndex,a=(0,y.J9)(this.props.background,!1);return r.map(function(t,r){t.value;var u=t.background,c=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(t,M);if(!u)return null;var l=D(D(D(D(D({},c),{},{fill:"#eee"},u),a),(0,b.XC)(e.props,t,r)),{},{onAnimationStart:e.handleAnimationStart,onAnimationEnd:e.handleAnimationEnd,dataKey:o,index:r,className:"recharts-bar-background-rectangle"});return n.createElement(P,_({key:"background-bar-".concat(r),option:e.props.background,isActive:r===i},l))})}},{key:"renderErrorBar",value:function(e,t){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var r=this.props,o=r.data,i=r.xAxis,a=r.yAxis,u=r.layout,c=r.children,l=(0,y.aS)(c,f.u);if(!l)return null;var p="vertical"===u?o[0].height/2:o[0].width/2,d=function(e,t){var r=Array.isArray(e.value)?e.value[1]:e.value;return{x:e.x,y:e.y,value:r,errorVal:(0,m.kr)(e,t)}};return n.createElement(s.W,{clipPath:e?"url(#clipPath-".concat(t,")"):null},l.map(function(e){return n.cloneElement(e,{key:"error-bar-".concat(t,"-").concat(e.props.dataKey),data:o,xAxis:i,yAxis:a,layout:u,offset:p,dataPointFormatter:d})}))}},{key:"render",value:function(){var e=this.props,t=e.hide,r=e.data,i=e.className,a=e.xAxis,u=e.yAxis,c=e.left,f=e.top,p=e.width,h=e.height,y=e.isAnimationActive,v=e.background,m=e.id;if(t||!r||!r.length)return null;var b=this.state.isAnimationFinished,g=(0,o.A)("recharts-bar",i),x=a&&a.allowDataOverflow,w=u&&u.allowDataOverflow,O=x||w,j=l()(m)?this.id:m;return n.createElement(s.W,{className:g},x||w?n.createElement("defs",null,n.createElement("clipPath",{id:"clipPath-".concat(j)},n.createElement("rect",{x:x?c:c-p/2,y:w?f:f-h/2,width:x?p:2*p,height:w?h:2*h}))):null,n.createElement(s.W,{className:"recharts-bar-rectangles",clipPath:O?"url(#clipPath-".concat(j,")"):null},v?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(O,j),(!y||b)&&d.Z.renderCallByParent(this.props,r))}}],r=[{key:"getDerivedStateFromProps",value:function(e,t){return e.animationId!==t.prevAnimationId?{prevAnimationId:e.animationId,curData:e.data,prevData:t.curData}:e.data!==t.curData?{curData:e.data}:null}}],t&&N(a.prototype,t),r&&N(a,r),Object.defineProperty(a,"prototype",{writable:!1}),a}(n.PureComponent);L(F,"displayName","Bar"),L(F,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",activeBar:!1,isAnimationActive:!v.m.isSsr,animationBegin:0,animationDuration:400,animationEasing:"ease"}),L(F,"getComposedData",function(e){var t=e.props,r=e.item,n=e.barPosition,o=e.bandSize,i=e.xAxis,a=e.yAxis,u=e.xAxisTicks,c=e.yAxisTicks,l=e.stackedData,s=e.dataStartIndex,f=e.displayedData,d=e.offset,v=(0,m.xi)(n,r);if(!v)return null;var b=t.layout,g=r.type.defaultProps,x=void 0!==g?D(D({},g),r.props):r.props,w=x.dataKey,O=x.children,j=x.minPointSize,S="horizontal"===b?a:i,E=l?S.scale.domain():null,A=(0,m.DW)({numericAxis:S}),P=(0,y.aS)(O,p.f),M=f.map(function(e,t){l?f=(0,m._f)(l[s+t],E):Array.isArray(f=(0,m.kr)(e,w))||(f=[A,f]);var n=k(j,F.defaultProps.minPointSize)(f[1],t);if("horizontal"===b){var f,p,d,y,g,x,O,S=[a.scale(f[0]),a.scale(f[1])],M=S[0],T=S[1];p=(0,m.y2)({axis:i,ticks:u,bandSize:o,offset:v.offset,entry:e,index:t}),d=null!=(O=null!=T?T:M)?O:void 0,y=v.size;var _=M-T;if(g=Number.isNaN(_)?0:_,x={x:p,y:a.y,width:y,height:a.height},Math.abs(n)>0&&Math.abs(g)<Math.abs(n)){var C=(0,h.sA)(g||n)*(Math.abs(n)-Math.abs(g));d-=C,g+=C}}else{var N=[i.scale(f[0]),i.scale(f[1])],I=N[0],R=N[1];if(p=I,d=(0,m.y2)({axis:a,ticks:c,bandSize:o,offset:v.offset,entry:e,index:t}),y=R-I,g=v.size,x={x:i.x,y:d,width:i.width,height:g},Math.abs(n)>0&&Math.abs(y)<Math.abs(n)){var B=(0,h.sA)(y||n)*(Math.abs(n)-Math.abs(y));y+=B}}return D(D(D({},e),{},{x:p,y:d,width:y,height:g,value:l?f:f[1],payload:e,background:x},P&&P[t]&&P[t].props),{},{tooltipPayload:[(0,m.zb)(r,e)],tooltipPosition:{x:p+y/2,y:d+g/2}})});return D({data:M,layout:b},d)})},83455:(e,t,r)=>{"use strict";r.d(t,{P2:()=>w,bx:()=>O,pr:()=>m,sl:()=>b,vh:()=>g});var n=r(74925),o=r.n(n),i=r(29794),a=r.n(i),u=r(12814),c=r(70788),l=r(16377),s=r(83394);function f(e){return(f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function p(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,v(n.key),n)}}function d(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function h(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?d(Object(r),!0).forEach(function(t){y(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):d(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function y(e,t,r){return(t=v(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function v(e){var t=function(e,t){if("object"!=f(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=f(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==f(t)?t:t+""}var m=function(e,t,r,n,o){var i=e.width,a=e.height,f=e.layout,p=e.children,d=Object.keys(t),v={left:r.left,leftMirror:r.left,right:i-r.right,rightMirror:i-r.right,top:r.top,topMirror:r.top,bottom:a-r.bottom,bottomMirror:a-r.bottom},m=!!(0,c.BU)(p,s.y);return d.reduce(function(i,a){var c,s,p,d,b,g=t[a],x=g.orientation,w=g.domain,O=g.padding,j=void 0===O?{}:O,S=g.mirror,E=g.reversed,A="".concat(x).concat(S?"Mirror":"");if("number"===g.type&&("gap"===g.padding||"no-gap"===g.padding)){var P=w[1]-w[0],k=1/0,M=g.categoricalDomain.sort(l.ck);if(M.forEach(function(e,t){t>0&&(k=Math.min((e||0)-(M[t-1]||0),k))}),Number.isFinite(k)){var T=k/P,_="vertical"===g.layout?r.height:r.width;if("gap"===g.padding&&(c=T*_/2),"no-gap"===g.padding){var C=(0,l.F4)(e.barCategoryGap,T*_),D=T*_/2;c=D-C-(D-C)/_*C}}}s="xAxis"===n?[r.left+(j.left||0)+(c||0),r.left+r.width-(j.right||0)-(c||0)]:"yAxis"===n?"horizontal"===f?[r.top+r.height-(j.bottom||0),r.top+(j.top||0)]:[r.top+(j.top||0)+(c||0),r.top+r.height-(j.bottom||0)-(c||0)]:g.range,E&&(s=[s[1],s[0]]);var N=(0,u.W7)(g,o,m),I=N.scale,R=N.realScaleType;I.domain(w).range(s),(0,u.YB)(I);var B=(0,u.w7)(I,h(h({},g),{},{realScaleType:R}));"xAxis"===n?(b="top"===x&&!S||"bottom"===x&&S,p=r.left,d=v[A]-b*g.height):"yAxis"===n&&(b="left"===x&&!S||"right"===x&&S,p=v[A]-b*g.width,d=r.top);var L=h(h(h({},g),B),{},{realScaleType:R,x:p,y:d,scale:I,width:"xAxis"===n?r.width:g.width,height:"yAxis"===n?r.height:g.height});return L.bandSize=(0,u.Hj)(L,B),g.hide||"xAxis"!==n?g.hide||(v[A]+=(b?-1:1)*L.width):v[A]+=(b?-1:1)*L.height,h(h({},i),{},y({},a,L))},{})},b=function(e,t){var r=e.x,n=e.y,o=t.x,i=t.y;return{x:Math.min(r,o),y:Math.min(n,i),width:Math.abs(o-r),height:Math.abs(i-n)}},g=function(e){return b({x:e.x1,y:e.y1},{x:e.x2,y:e.y2})},x=function(){var e,t;function r(e){if(!(this instanceof r))throw TypeError("Cannot call a class as a function");this.scale=e}return e=[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.bandAware,n=t.position;if(void 0!==e){if(n)switch(n){case"start":default:return this.scale(e);case"middle":var o=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+o;case"end":var i=this.bandwidth?this.bandwidth():0;return this.scale(e)+i}if(r){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+a}return this.scale(e)}}},{key:"isInRange",value:function(e){var t=this.range(),r=t[0],n=t[t.length-1];return r<=n?e>=r&&e<=n:e>=n&&e<=r}}],t=[{key:"create",value:function(e){return new r(e)}}],e&&p(r.prototype,e),t&&p(r,t),Object.defineProperty(r,"prototype",{writable:!1}),r}();y(x,"EPS",1e-4);var w=function(e){var t=Object.keys(e).reduce(function(t,r){return h(h({},t),{},y({},r,x.create(e[r])))},{});return h(h({},t),{},{apply:function(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.bandAware,i=r.position;return o()(e,function(e,r){return t[r].apply(e,{bandAware:n,position:i})})},isInRange:function(e){return a()(e,function(e,r){return t[r].isInRange(e)})}})},O=function(e){var t=e.width,r=e.height,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,o=(n%180+180)%180*Math.PI/180,i=Math.atan(r/t);return Math.abs(o>i&&o<Math.PI-i?r/Math.sin(o):t/Math.cos(o))}},83540:(e,t,r)=>{"use strict";r.d(t,{u:()=>h});var n=r(52596),o=r(12115),i=r(91959),a=r.n(i),u=r(16377),c=r(675),l=r(70788);function s(e){return(s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function f(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function p(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?f(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=s(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=s(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==s(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function d(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var h=(0,o.forwardRef)(function(e,t){var r,i=e.aspect,s=e.initialDimension,f=void 0===s?{width:-1,height:-1}:s,h=e.width,y=void 0===h?"100%":h,v=e.height,m=void 0===v?"100%":v,b=e.minWidth,g=void 0===b?0:b,x=e.minHeight,w=e.maxHeight,O=e.children,j=e.debounce,S=void 0===j?0:j,E=e.id,A=e.className,P=e.onResize,k=e.style,M=(0,o.useRef)(null),T=(0,o.useRef)();T.current=P,(0,o.useImperativeHandle)(t,function(){return Object.defineProperty(M.current,"current",{get:function(){return console.warn("The usage of ref.current.current is deprecated and will no longer be supported."),M.current},configurable:!0})});var _=function(e){if(Array.isArray(e))return e}(r=(0,o.useState)({containerWidth:f.width,containerHeight:f.height}))||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{i=(r=r.call(e)).next,!1;for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(e){l=!0,o=e}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(r,2)||function(e,t){if(e){if("string"==typeof e)return d(e,2);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return d(e,t)}}(r,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),C=_[0],D=_[1],N=(0,o.useCallback)(function(e,t){D(function(r){var n=Math.round(e),o=Math.round(t);return r.containerWidth===n&&r.containerHeight===o?r:{containerWidth:n,containerHeight:o}})},[]);(0,o.useEffect)(function(){var e=function(e){var t,r=e[0].contentRect,n=r.width,o=r.height;N(n,o),null==(t=T.current)||t.call(T,n,o)};S>0&&(e=a()(e,S,{trailing:!0,leading:!1}));var t=new ResizeObserver(e),r=M.current.getBoundingClientRect();return N(r.width,r.height),t.observe(M.current),function(){t.disconnect()}},[N,S]);var I=(0,o.useMemo)(function(){var e=C.containerWidth,t=C.containerHeight;if(e<0||t<0)return null;(0,c.R)((0,u._3)(y)||(0,u._3)(m),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",y,m),(0,c.R)(!i||i>0,"The aspect(%s) must be greater than zero.",i);var r=(0,u._3)(y)?e:y,n=(0,u._3)(m)?t:m;i&&i>0&&(r?n=r/i:n&&(r=n*i),w&&n>w&&(n=w)),(0,c.R)(r>0||n>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",r,n,y,m,g,x,i);var a=!Array.isArray(O)&&(0,l.Mn)(O.type).endsWith("Chart");return o.Children.map(O,function(e){return o.isValidElement(e)?(0,o.cloneElement)(e,p({width:r,height:n},a?{style:p({height:"100%",width:"100%",maxHeight:n,maxWidth:r},e.props.style)}:{})):e})},[i,O,m,w,x,g,C,y]);return o.createElement("div",{id:E?"".concat(E):void 0,className:(0,n.A)("recharts-responsive-container",A),style:p(p({},void 0===k?{}:k),{},{width:y,height:m,minWidth:g,minHeight:x,maxHeight:w}),ref:M},I)})},83711:(e,t,r)=>{var n=r(13122),o=r(15473);e.exports=function(e,t){var r=o(e,t);return n(r)?r:void 0}},83979:e=>{e.exports=function(e){var t=null==e?0:e.length;return t?e[t-1]:void 0}},84355:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("chart-pie",[["path",{d:"M21 12c.552 0 1.005-.449.95-.998a10 10 0 0 0-8.953-8.951c-.55-.055-.998.398-.998.95v8a1 1 0 0 0 1 1z",key:"pzmjnu"}],["path",{d:"M21.21 15.89A10 10 0 1 1 8 2.83",key:"k2fpak"}]])},84464:(e,t,r)=>{var n=r(20963),o=Object.prototype.hasOwnProperty;e.exports=function(e,t,r,i,a,u){var c=1&r,l=n(e),s=l.length;if(s!=n(t).length&&!c)return!1;for(var f=s;f--;){var p=l[f];if(!(c?p in t:o.call(t,p)))return!1}var d=u.get(e),h=u.get(t);if(d&&h)return d==t&&h==e;var y=!0;u.set(e,t),u.set(t,e);for(var v=c;++f<s;){var m=e[p=l[f]],b=t[p];if(i)var g=c?i(b,m,p,t,e,u):i(m,b,p,e,t,u);if(!(void 0===g?m===b||a(m,b,r,i,u):g)){y=!1;break}v||(v="constructor"==p)}if(y&&!v){var x=e.constructor,w=t.constructor;x!=w&&"constructor"in e&&"constructor"in t&&!("function"==typeof x&&x instanceof x&&"function"==typeof w&&w instanceof w)&&(y=!1)}return u.delete(e),u.delete(t),y}},84760:e=>{e.exports=function(e,t,r){switch(r.length){case 0:return e.call(t);case 1:return e.call(t,r[0]);case 2:return e.call(t,r[0],r[1]);case 3:return e.call(t,r[0],r[1],r[2])}return e.apply(t,r)}},84949:(e,t,r)=>{"use strict";r.d(t,{cq:()=>n});let n=r(59355).w},85090:(e,t,r)=>{var n=r(88748),o=r(6997),i=r(34210);function a(e){var t=-1,r=null==e?0:e.length;for(this.__data__=new n;++t<r;)this.add(e[t])}a.prototype.add=a.prototype.push=o,a.prototype.has=i,e.exports=a},85654:(e,t,r)=>{"use strict";function n(e){return function(){return e}}r.d(t,{A:()=>n})},85855:(e,t,r)=>{var n=r(16613);e.exports=function(e){return null==e?"":n(e)}},86216:(e,t,r)=>{e.exports=r(30716)()},86452:e=>{e.exports=function(e,t){return null!=e&&t in Object(e)}},88748:(e,t,r)=>{var n=r(10537),o=r(94999),i=r(74544),a=r(73956),u=r(5516);function c(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}c.prototype.clear=n,c.prototype.delete=o,c.prototype.get=i,c.prototype.has=a,c.prototype.set=u,e.exports=c},89053:(e,t,r)=>{var n=r(58918),o=r(52521),i=r(13465);e.exports=function(e){return e&&e.length?n(e,i,o):void 0}},89316:(e,t,r)=>{var n=r(98233),o=r(15631),i=r(48611),a={};a["[object Float32Array]"]=a["[object Float64Array]"]=a["[object Int8Array]"]=a["[object Int16Array]"]=a["[object Int32Array]"]=a["[object Uint8Array]"]=a["[object Uint8ClampedArray]"]=a["[object Uint16Array]"]=a["[object Uint32Array]"]=!0,a["[object Arguments]"]=a["[object Array]"]=a["[object ArrayBuffer]"]=a["[object Boolean]"]=a["[object DataView]"]=a["[object Date]"]=a["[object Error]"]=a["[object Function]"]=a["[object Map]"]=a["[object Number]"]=a["[object Object]"]=a["[object RegExp]"]=a["[object Set]"]=a["[object String]"]=a["[object WeakMap]"]=!1,e.exports=function(e){return i(e)&&o(e.length)&&!!a[n(e)]}},90453:(e,t,r)=>{var n=r(31598);e.exports=function(){this.__data__=new n,this.size=0}},90724:e=>{e.exports=function(e){var t=-1,r=Array(e.size);return e.forEach(function(e,n){r[++t]=[n,e]}),r}},90929:(e,t,r)=>{var n=r(54360),o=Array.prototype.splice;e.exports=function(e){var t=this.__data__,r=n(t,e);return!(r<0)&&(r==t.length-1?t.pop():o.call(t,r,1),--this.size,!0)}},91113:(e,t,r)=>{var n=r(88748);function o(e,t){if("function"!=typeof e||null!=t&&"function"!=typeof t)throw TypeError("Expected a function");var r=function(){var n=arguments,o=t?t.apply(this,n):n[0],i=r.cache;if(i.has(o))return i.get(o);var a=e.apply(this,n);return r.cache=i.set(o,a)||i,a};return r.cache=new(o.Cache||n),r}o.Cache=n,e.exports=o},91569:e=>{e.exports=function(e,t){for(var r=-1,n=t.length,o=e.length;++r<n;)e[o+r]=t[r];return e}},91959:(e,t,r)=>{var n=r(45964),o=r(67460);e.exports=function(e,t,r){var i=!0,a=!0;if("function"!=typeof e)throw TypeError("Expected a function");return o(r)&&(i="leading"in r?!!r.leading:i,a="trailing"in r?!!r.trailing:a),n(e,t,{leading:i,maxWait:t,trailing:a})}},92313:e=>{e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length;++r<n;)if(t(e[r],r,e))return!0;return!1}},92418:(e,t,r)=>{"use strict";r.d(t,{gu:()=>tD});var n=r(12115),o=r(59882),i=r.n(o),a=r(40139),u=r.n(a),c=r(18940),l=r.n(c),s=r(48973),f=r.n(s),p=r(67206),d=r.n(p),h=r(91959),y=r.n(h),v=r(52596),m=r(93179),b=r(72790),g=r(2348),x=r(94517),w=r(24026),O=r(51172),j=r(44538),S=r(70788),E=r(81519),A=r(79095),P=r(12814),k=r(16377);function M(e){return(M="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function T(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function _(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?T(Object(r),!0).forEach(function(t){C(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):T(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function C(e,t,r){var n;return(n=function(e,t){if("object"!=M(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=M(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"==M(n)?n:n+"")in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var D=["Webkit","Moz","O","ms"],N=function(e,t){if(!e)return null;var r=e.replace(/(\w)/,function(e){return e.toUpperCase()}),n=D.reduce(function(e,n){return _(_({},e),{},C({},n+r,t))},{});return n[e]=t,n};function I(e){return(I="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function R(){return(R=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function B(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function L(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?B(Object(r),!0).forEach(function(t){$(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):B(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function W(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Y(n.key),n)}}function F(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(F=function(){return!!e})()}function z(e){return(z=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function U(e,t){return(U=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function $(e,t,r){return(t=Y(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Y(e){var t=function(e,t){if("object"!=I(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=I(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==I(t)?t:t+""}var H=function(e){var t=e.data,r=e.startIndex,n=e.endIndex,o=e.x,i=e.width,a=e.travellerWidth;if(!t||!t.length)return{};var u=t.length,c=(0,E.z)().domain(l()(0,u)).range([o,o+i-a]),s=c.domain().map(function(e){return c(e)});return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:c(r),endX:c(n),scale:c,scaleValues:s}},q=function(e){return e.changedTouches&&!!e.changedTouches.length},X=function(e){var t,r;function o(e){var t,r,n;if(!(this instanceof o))throw TypeError("Cannot call a class as a function");return r=o,n=[e],r=z(r),$(t=function(e,t){if(t&&("object"===I(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");var r=e;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,F()?Reflect.construct(r,n||[],z(this).constructor):r.apply(this,n)),"handleDrag",function(e){t.leaveTimer&&(clearTimeout(t.leaveTimer),t.leaveTimer=null),t.state.isTravellerMoving?t.handleTravellerMove(e):t.state.isSlideMoving&&t.handleSlideDrag(e)}),$(t,"handleTouchMove",function(e){null!=e.changedTouches&&e.changedTouches.length>0&&t.handleDrag(e.changedTouches[0])}),$(t,"handleDragEnd",function(){t.setState({isTravellerMoving:!1,isSlideMoving:!1},function(){var e=t.props,r=e.endIndex,n=e.onDragEnd,o=e.startIndex;null==n||n({endIndex:r,startIndex:o})}),t.detachDragEndListener()}),$(t,"handleLeaveWrapper",function(){(t.state.isTravellerMoving||t.state.isSlideMoving)&&(t.leaveTimer=window.setTimeout(t.handleDragEnd,t.props.leaveTimeOut))}),$(t,"handleEnterSlideOrTraveller",function(){t.setState({isTextActive:!0})}),$(t,"handleLeaveSlideOrTraveller",function(){t.setState({isTextActive:!1})}),$(t,"handleSlideDragStart",function(e){var r=q(e)?e.changedTouches[0]:e;t.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:r.pageX}),t.attachDragEndListener()}),t.travellerDragStartHandlers={startX:t.handleTravellerDragStart.bind(t,"startX"),endX:t.handleTravellerDragStart.bind(t,"endX")},t.state={},t}if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");return o.prototype=Object.create(e&&e.prototype,{constructor:{value:o,writable:!0,configurable:!0}}),Object.defineProperty(o,"prototype",{writable:!1}),e&&U(o,e),t=[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(e){var t=e.startX,r=e.endX,n=this.state.scaleValues,i=this.props,a=i.gap,u=i.data.length-1,c=Math.min(t,r),l=Math.max(t,r),s=o.getIndexInRange(n,c),f=o.getIndexInRange(n,l);return{startIndex:s-s%a,endIndex:f===u?u:f-f%a}}},{key:"getTextOfTick",value:function(e){var t=this.props,r=t.data,n=t.tickFormatter,o=t.dataKey,i=(0,P.kr)(r[e],o,e);return u()(n)?n(i,e):i}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(e){var t=this.state,r=t.slideMoveStartX,n=t.startX,o=t.endX,i=this.props,a=i.x,u=i.width,c=i.travellerWidth,l=i.startIndex,s=i.endIndex,f=i.onChange,p=e.pageX-r;p>0?p=Math.min(p,a+u-c-o,a+u-c-n):p<0&&(p=Math.max(p,a-n,a-o));var d=this.getIndex({startX:n+p,endX:o+p});(d.startIndex!==l||d.endIndex!==s)&&f&&f(d),this.setState({startX:n+p,endX:o+p,slideMoveStartX:e.pageX})}},{key:"handleTravellerDragStart",value:function(e,t){var r=q(t)?t.changedTouches[0]:t;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:e,brushMoveStartX:r.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(e){var t=this.state,r=t.brushMoveStartX,n=t.movingTravellerId,o=t.endX,i=t.startX,a=this.state[n],u=this.props,c=u.x,l=u.width,s=u.travellerWidth,f=u.onChange,p=u.gap,d=u.data,h={startX:this.state.startX,endX:this.state.endX},y=e.pageX-r;y>0?y=Math.min(y,c+l-s-a):y<0&&(y=Math.max(y,c-a)),h[n]=a+y;var v=this.getIndex(h),m=v.startIndex,b=v.endIndex,g=function(){var e=d.length-1;return"startX"===n&&(o>i?m%p==0:b%p==0)||!!(o<i)&&b===e||"endX"===n&&(o>i?b%p==0:m%p==0)||!!(o>i)&&b===e};this.setState($($({},n,a+y),"brushMoveStartX",e.pageX),function(){f&&g()&&f(v)})}},{key:"handleTravellerMoveKeyboard",value:function(e,t){var r=this,n=this.state,o=n.scaleValues,i=n.startX,a=n.endX,u=this.state[t],c=o.indexOf(u);if(-1!==c){var l=c+e;if(-1!==l&&!(l>=o.length)){var s=o[l];"startX"===t&&s>=a||"endX"===t&&s<=i||this.setState($({},t,s),function(){r.props.onChange(r.getIndex({startX:r.state.startX,endX:r.state.endX}))})}}}},{key:"renderBackground",value:function(){var e=this.props,t=e.x,r=e.y,o=e.width,i=e.height,a=e.fill,u=e.stroke;return n.createElement("rect",{stroke:u,fill:a,x:t,y:r,width:o,height:i})}},{key:"renderPanorama",value:function(){var e=this.props,t=e.x,r=e.y,o=e.width,i=e.height,a=e.data,u=e.children,c=e.padding,l=n.Children.only(u);return l?n.cloneElement(l,{x:t,y:r,width:o,height:i,margin:c,compact:!0,data:a}):null}},{key:"renderTravellerLayer",value:function(e,t){var r,i,a=this,u=this.props,c=u.y,l=u.travellerWidth,s=u.height,f=u.traveller,p=u.ariaLabel,d=u.data,h=u.startIndex,y=u.endIndex,v=Math.max(e,this.props.x),m=L(L({},(0,S.J9)(this.props,!1)),{},{x:v,y:c,width:l,height:s}),b=p||"Min value: ".concat(null==(r=d[h])?void 0:r.name,", Max value: ").concat(null==(i=d[y])?void 0:i.name);return n.createElement(g.W,{tabIndex:0,role:"slider","aria-label":b,"aria-valuenow":e,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[t],onTouchStart:this.travellerDragStartHandlers[t],onKeyDown:function(e){["ArrowLeft","ArrowRight"].includes(e.key)&&(e.preventDefault(),e.stopPropagation(),a.handleTravellerMoveKeyboard("ArrowRight"===e.key?1:-1,t))},onFocus:function(){a.setState({isTravellerFocused:!0})},onBlur:function(){a.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},o.renderTraveller(f,m))}},{key:"renderSlide",value:function(e,t){var r=this.props,o=r.y,i=r.height,a=r.stroke,u=r.travellerWidth,c=Math.min(e,t)+u,l=Math.max(Math.abs(t-e)-u,0);return n.createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:a,fillOpacity:.2,x:c,y:o,width:l,height:i})}},{key:"renderText",value:function(){var e=this.props,t=e.startIndex,r=e.endIndex,o=e.y,i=e.height,a=e.travellerWidth,u=e.stroke,c=this.state,l=c.startX,s=c.endX,f={pointerEvents:"none",fill:u};return n.createElement(g.W,{className:"recharts-brush-texts"},n.createElement(A.E,R({textAnchor:"end",verticalAnchor:"middle",x:Math.min(l,s)-5,y:o+i/2},f),this.getTextOfTick(t)),n.createElement(A.E,R({textAnchor:"start",verticalAnchor:"middle",x:Math.max(l,s)+a+5,y:o+i/2},f),this.getTextOfTick(r)))}},{key:"render",value:function(){var e=this.props,t=e.data,r=e.className,o=e.children,i=e.x,a=e.y,u=e.width,c=e.height,l=e.alwaysShowText,s=this.state,f=s.startX,p=s.endX,d=s.isTextActive,h=s.isSlideMoving,y=s.isTravellerMoving,m=s.isTravellerFocused;if(!t||!t.length||!(0,k.Et)(i)||!(0,k.Et)(a)||!(0,k.Et)(u)||!(0,k.Et)(c)||u<=0||c<=0)return null;var b=(0,v.A)("recharts-brush",r),x=1===n.Children.count(o),w=N("userSelect","none");return n.createElement(g.W,{className:b,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:w},this.renderBackground(),x&&this.renderPanorama(),this.renderSlide(f,p),this.renderTravellerLayer(f,"startX"),this.renderTravellerLayer(p,"endX"),(d||h||y||m||l)&&this.renderText())}}],r=[{key:"renderDefaultTraveller",value:function(e){var t=e.x,r=e.y,o=e.width,i=e.height,a=e.stroke,u=Math.floor(r+i/2)-1;return n.createElement(n.Fragment,null,n.createElement("rect",{x:t,y:r,width:o,height:i,fill:a,stroke:"none"}),n.createElement("line",{x1:t+1,y1:u,x2:t+o-1,y2:u,fill:"none",stroke:"#fff"}),n.createElement("line",{x1:t+1,y1:u+2,x2:t+o-1,y2:u+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(e,t){var r;return n.isValidElement(e)?n.cloneElement(e,t):u()(e)?e(t):o.renderDefaultTraveller(t)}},{key:"getDerivedStateFromProps",value:function(e,t){var r=e.data,n=e.width,o=e.x,i=e.travellerWidth,a=e.updateId,u=e.startIndex,c=e.endIndex;if(r!==t.prevData||a!==t.prevUpdateId)return L({prevData:r,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:n},r&&r.length?H({data:r,width:n,x:o,travellerWidth:i,startIndex:u,endIndex:c}):{scale:null,scaleValues:null});if(t.scale&&(n!==t.prevWidth||o!==t.prevX||i!==t.prevTravellerWidth)){t.scale.range([o,o+n-i]);var l=t.scale.domain().map(function(e){return t.scale(e)});return{prevData:r,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:n,startX:t.scale(e.startIndex),endX:t.scale(e.endIndex),scaleValues:l}}return null}},{key:"getIndexInRange",value:function(e,t){for(var r=e.length,n=0,o=r-1;o-n>1;){var i=Math.floor((n+o)/2);e[i]>t?o=i:n=i}return t>=e[o]?o:n}}],t&&W(o.prototype,t),r&&W(o,r),Object.defineProperty(o,"prototype",{writable:!1}),o}(n.PureComponent);$(X,"displayName","Brush"),$(X,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var Z=r(46605),V=r(83197),G=r(60379),K=function(e,t){var r=e.alwaysShow,n=e.ifOverflow;return r&&(n="extendDomain"),n===t},J=r(83455),Q=r(675);function ee(){return(ee=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function et(e){return(et="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function er(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function en(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?er(Object(r),!0).forEach(function(t){eu(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):er(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function eo(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(eo=function(){return!!e})()}function ei(e){return(ei=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function ea(e,t){return(ea=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function eu(e,t,r){return(t=ec(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ec(e){var t=function(e,t){if("object"!=et(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=et(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==et(t)?t:t+""}var el=function(e){var t=e.x,r=e.y,n=e.xAxis,o=e.yAxis,i=(0,J.P2)({x:n.scale,y:o.scale}),a=i.apply({x:t,y:r},{bandAware:!0});return K(e,"discard")&&!i.isInRange(a)?null:a},es=function(e){var t;function r(){var e,t;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return e=r,t=arguments,e=ei(e),function(e,t){if(t&&("object"===et(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");var r=e;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,eo()?Reflect.construct(e,t||[],ei(this).constructor):e.apply(this,t))}if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(e&&e.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),e&&ea(r,e),t=[{key:"render",value:function(){var e=this.props,t=e.x,o=e.y,i=e.r,a=e.alwaysShow,u=e.clipPathId,c=(0,k.vh)(t),l=(0,k.vh)(o);if((0,Q.R)(void 0===a,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!c||!l)return null;var s=el(this.props);if(!s)return null;var f=s.x,p=s.y,d=this.props,h=d.shape,y=d.className,m=en(en({clipPath:K(this.props,"hidden")?"url(#".concat(u,")"):void 0},(0,S.J9)(this.props,!0)),{},{cx:f,cy:p});return n.createElement(g.W,{className:(0,v.A)("recharts-reference-dot",y)},r.renderDot(h,m),G.J.renderCallByParent(this.props,{x:f-i,y:p-i,width:2*i,height:2*i}))}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,ec(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(n.Component);eu(es,"displayName","ReferenceDot"),eu(es,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1}),eu(es,"renderDot",function(e,t){var r;return n.isValidElement(e)?n.cloneElement(e,t):u()(e)?e(t):n.createElement(O.c,ee({},t,{cx:t.cx,cy:t.cy,className:"recharts-reference-dot-dot"}))});var ef=r(71571),ep=r.n(ef),ed=r(50091);function eh(e){return(eh="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ey(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(ey=function(){return!!e})()}function ev(e){return(ev=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function em(e,t){return(em=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function eb(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function eg(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?eb(Object(r),!0).forEach(function(t){ex(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):eb(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function ex(e,t,r){return(t=ew(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ew(e){var t=function(e,t){if("object"!=eh(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=eh(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==eh(t)?t:t+""}function eO(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function ej(){return(ej=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var eS=function(e,t){var r;return n.isValidElement(e)?n.cloneElement(e,t):u()(e)?e(t):n.createElement("line",ej({},t,{className:"recharts-reference-line-line"}))},eE=function(e,t,r,n,o,i,a,u,c){var l=o.x,s=o.y,f=o.width,p=o.height;if(r){var d=c.y,h=e.y.apply(d,{position:i});if(K(c,"discard")&&!e.y.isInRange(h))return null;var y=[{x:l+f,y:h},{x:l,y:h}];return"left"===u?y.reverse():y}if(t){var v=c.x,m=e.x.apply(v,{position:i});if(K(c,"discard")&&!e.x.isInRange(m))return null;var b=[{x:m,y:s+p},{x:m,y:s}];return"top"===a?b.reverse():b}if(n){var g=c.segment.map(function(t){return e.apply(t,{position:i})});return K(c,"discard")&&ep()(g,function(t){return!e.isInRange(t)})?null:g}return null};function eA(e){var t=e.x,r=e.y,o=e.segment,i=e.xAxisId,a=e.yAxisId,u=e.shape,c=e.className,l=e.alwaysShow,s=(0,ed.Yp)(),f=(0,ed.AF)(i),p=(0,ed.Nk)(a),d=(0,ed.sk)();if(!s||!d)return null;(0,Q.R)(void 0===l,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var h=eE((0,J.P2)({x:f.scale,y:p.scale}),(0,k.vh)(t),(0,k.vh)(r),o&&2===o.length,d,e.position,f.orientation,p.orientation,e);if(!h)return null;var y=function(e){if(Array.isArray(e))return e}(h)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{i=(r=r.call(e)).next,!1;for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(e){l=!0,o=e}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(h,2)||function(e,t){if(e){if("string"==typeof e)return eO(e,2);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return eO(e,t)}}(h,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),m=y[0],b=m.x,x=m.y,w=y[1],O=w.x,j=w.y,E=eg(eg({clipPath:K(e,"hidden")?"url(#".concat(s,")"):void 0},(0,S.J9)(e,!0)),{},{x1:b,y1:x,x2:O,y2:j});return n.createElement(g.W,{className:(0,v.A)("recharts-reference-line",c)},eS(u,E),G.J.renderCallByParent(e,(0,J.vh)({x1:b,y1:x,x2:O,y2:j})))}var eP=function(e){var t;function r(){var e,t;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return e=r,t=arguments,e=ev(e),function(e,t){if(t&&("object"===eh(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");var r=e;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,ey()?Reflect.construct(e,t||[],ev(this).constructor):e.apply(this,t))}if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(e&&e.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),e&&em(r,e),t=[{key:"render",value:function(){return n.createElement(eA,this.props)}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,ew(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(n.Component);function ek(){return(ek=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function eM(e){return(eM="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function eT(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function e_(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?eT(Object(r),!0).forEach(function(t){eI(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):eT(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}ex(eP,"displayName","ReferenceLine"),ex(eP,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});function eC(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(eC=function(){return!!e})()}function eD(e){return(eD=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function eN(e,t){return(eN=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function eI(e,t,r){return(t=eR(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function eR(e){var t=function(e,t){if("object"!=eM(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=eM(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==eM(t)?t:t+""}var eB=function(e,t,r,n,o){var i=o.x1,a=o.x2,u=o.y1,c=o.y2,l=o.xAxis,s=o.yAxis;if(!l||!s)return null;var f=(0,J.P2)({x:l.scale,y:s.scale}),p={x:e?f.x.apply(i,{position:"start"}):f.x.rangeMin,y:r?f.y.apply(u,{position:"start"}):f.y.rangeMin},d={x:t?f.x.apply(a,{position:"end"}):f.x.rangeMax,y:n?f.y.apply(c,{position:"end"}):f.y.rangeMax};return!K(o,"discard")||f.isInRange(p)&&f.isInRange(d)?(0,J.sl)(p,d):null},eL=function(e){var t;function r(){var e,t;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return e=r,t=arguments,e=eD(e),function(e,t){if(t&&("object"===eM(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");var r=e;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,eC()?Reflect.construct(e,t||[],eD(this).constructor):e.apply(this,t))}if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(e&&e.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),e&&eN(r,e),t=[{key:"render",value:function(){var e=this.props,t=e.x1,o=e.x2,i=e.y1,a=e.y2,u=e.className,c=e.alwaysShow,l=e.clipPathId;(0,Q.R)(void 0===c,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var s=(0,k.vh)(t),f=(0,k.vh)(o),p=(0,k.vh)(i),d=(0,k.vh)(a),h=this.props.shape;if(!s&&!f&&!p&&!d&&!h)return null;var y=eB(s,f,p,d,this.props);if(!y&&!h)return null;var m=K(this.props,"hidden")?"url(#".concat(l,")"):void 0;return n.createElement(g.W,{className:(0,v.A)("recharts-reference-area",u)},r.renderRect(h,e_(e_({clipPath:m},(0,S.J9)(this.props,!0)),y)),G.J.renderCallByParent(this.props,y))}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,eR(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(n.Component);function eW(e){return function(e){if(Array.isArray(e))return eF(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return eF(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return eF(e,t)}}(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function eF(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}eI(eL,"displayName","ReferenceArea"),eI(eL,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1}),eI(eL,"renderRect",function(e,t){var r;return n.isValidElement(e)?n.cloneElement(e,t):u()(e)?e(t):n.createElement(j.M,ek({},t,{className:"recharts-reference-area-rect"}))});var ez=function(e,t,r,n,o){var i=(0,S.aS)(e,eP),a=(0,S.aS)(e,es),u=[].concat(eW(i),eW(a)),c=(0,S.aS)(e,eL),l="".concat(n,"Id"),s=n[0],f=t;if(u.length&&(f=u.reduce(function(e,t){if(t.props[l]===r&&K(t.props,"extendDomain")&&(0,k.Et)(t.props[s])){var n=t.props[s];return[Math.min(e[0],n),Math.max(e[1],n)]}return e},f)),c.length){var p="".concat(s,"1"),d="".concat(s,"2");f=c.reduce(function(e,t){if(t.props[l]===r&&K(t.props,"extendDomain")&&(0,k.Et)(t.props[p])&&(0,k.Et)(t.props[d])){var n=t.props[p],o=t.props[d];return[Math.min(e[0],n,o),Math.max(e[1],n,o)]}return e},f)}return o&&o.length&&(f=o.reduce(function(e,t){return(0,k.Et)(t)?[Math.min(e[0],t),Math.max(e[1],t)]:e},f)),f},eU=r(25641),e$=r(15232),eY=r(82661),eH=new(r.n(eY)()),eq="recharts.syncMouseEvents",eX=r(43597);function eZ(e){return(eZ="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function eV(e,t,r){return(t=eG(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function eG(e){var t=function(e,t){if("object"!=eZ(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=eZ(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==eZ(t)?t:t+""}var eK=function(){var e,t;return e=function e(){if(!(this instanceof e))throw TypeError("Cannot call a class as a function");eV(this,"activeIndex",0),eV(this,"coordinateList",[]),eV(this,"layout","horizontal")},t=[{key:"setDetails",value:function(e){var t,r=e.coordinateList,n=void 0===r?null:r,o=e.container,i=void 0===o?null:o,a=e.layout,u=void 0===a?null:a,c=e.offset,l=void 0===c?null:c,s=e.mouseHandlerCallback,f=void 0===s?null:s;this.coordinateList=null!=(t=null!=n?n:this.coordinateList)?t:[],this.container=null!=i?i:this.container,this.layout=null!=u?u:this.layout,this.offset=null!=l?l:this.offset,this.mouseHandlerCallback=null!=f?f:this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(e){if(0!==this.coordinateList.length)switch(e.key){case"ArrowRight":if("horizontal"!==this.layout)return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break;case"ArrowLeft":if("horizontal"!==this.layout)return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse()}}},{key:"setIndex",value:function(e){this.activeIndex=e}},{key:"spoofMouse",value:function(){if("horizontal"===this.layout&&0!==this.coordinateList.length){var e,t,r=this.container.getBoundingClientRect(),n=r.x,o=r.y,i=r.height,a=this.coordinateList[this.activeIndex].coordinate,u=(null==(e=window)?void 0:e.scrollX)||0,c=(null==(t=window)?void 0:t.scrollY)||0,l=o+this.offset.top+i/2+c;this.mouseHandlerCallback({pageX:n+a+u,pageY:l})}}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,eG(n.key),n)}}(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}(),eJ=r(67790),eQ=r(70688);function e0(e){return(e0="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var e1=["x","y","top","left","width","height","className"];function e2(){return(e2=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function e5(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}var e3=function(e){var t=e.x,r=void 0===t?0:t,o=e.y,i=void 0===o?0:o,a=e.top,u=void 0===a?0:a,c=e.left,l=void 0===c?0:c,s=e.width,f=void 0===s?0:s,p=e.height,d=void 0===p?0:p,h=e.className,y=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?e5(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=e0(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=e0(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==e0(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):e5(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({x:r,y:i,top:u,left:l,width:f,height:d},function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,e1));return(0,k.Et)(r)&&(0,k.Et)(i)&&(0,k.Et)(f)&&(0,k.Et)(d)&&(0,k.Et)(u)&&(0,k.Et)(l)?n.createElement("path",e2({},(0,S.J9)(y,!0),{className:(0,v.A)("recharts-cross",h),d:"M".concat(r,",").concat(u,"v").concat(d,"M").concat(l,",").concat(i,"h").concat(f)})):null};function e8(e){var t=e.cx,r=e.cy,n=e.radius,o=e.startAngle,i=e.endAngle;return{points:[(0,eU.IZ)(t,r,n,o),(0,eU.IZ)(t,r,n,i)],cx:t,cy:r,radius:n,startAngle:o,endAngle:i}}var e9=r(77283);function e6(e){return(e6="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function e4(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function e7(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?e4(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=e6(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=e6(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==e6(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):e4(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function te(e){var t,r,o,i,a=e.element,u=e.tooltipEventType,c=e.isActive,l=e.activeCoordinate,s=e.activePayload,f=e.offset,p=e.activeTooltipIndex,d=e.tooltipAxisBandSize,h=e.layout,y=e.chartName,m=null!=(r=a.props.cursor)?r:null==(o=a.type.defaultProps)?void 0:o.cursor;if(!a||!m||!c||!l||"ScatterChart"!==y&&"axis"!==u)return null;var b=eQ.I;if("ScatterChart"===y)i=l,b=e3;else if("BarChart"===y)t=d/2,i={stroke:"none",fill:"#ccc",x:"horizontal"===h?l.x-t:f.left+.5,y:"horizontal"===h?f.top+.5:l.y-t,width:"horizontal"===h?d:f.width-1,height:"horizontal"===h?f.height-1:d},b=j.M;else if("radial"===h){var g=e8(l),x=g.cx,w=g.cy,O=g.radius;i={cx:x,cy:w,startAngle:g.startAngle,endAngle:g.endAngle,innerRadius:O,outerRadius:O},b=e9.h}else i={points:function(e,t,r){var n,o,i,a;if("horizontal"===e)i=n=t.x,o=r.top,a=r.top+r.height;else if("vertical"===e)a=o=t.y,n=r.left,i=r.left+r.width;else if(null!=t.cx&&null!=t.cy)if("centric"!==e)return e8(t);else{var u=t.cx,c=t.cy,l=t.innerRadius,s=t.outerRadius,f=t.angle,p=(0,eU.IZ)(u,c,l,f),d=(0,eU.IZ)(u,c,s,f);n=p.x,o=p.y,i=d.x,a=d.y}return[{x:n,y:o},{x:i,y:a}]}(h,l,f)},b=eQ.I;var E=e7(e7(e7(e7({stroke:"#ccc",pointerEvents:"none"},f),i),(0,S.J9)(m,!1)),{},{payload:s,payloadIndex:p,className:(0,v.A)("recharts-tooltip-cursor",m.className)});return(0,n.isValidElement)(m)?(0,n.cloneElement)(m,E):(0,n.createElement)(b,E)}var tt=["item"],tr=["children","className","width","height","style","compact","title","desc"];function tn(e){return(tn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function to(){return(to=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function ti(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(e){l=!0,o=e}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(e,t)||tf(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ta(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}function tu(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(tu=function(){return!!e})()}function tc(e){return(tc=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function tl(e,t){return(tl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function ts(e){return function(e){if(Array.isArray(e))return tp(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||tf(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function tf(e,t){if(e){if("string"==typeof e)return tp(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return tp(e,t)}}function tp(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function td(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function th(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?td(Object(r),!0).forEach(function(t){ty(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):td(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function ty(e,t,r){return(t=tv(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function tv(e){var t=function(e,t){if("object"!=tn(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=tn(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==tn(t)?t:t+""}var tm={xAxis:["bottom","top"],yAxis:["left","right"]},tb={width:"100%",height:"100%"},tg={x:0,y:0};function tx(e){return e}var tw=function(e,t,r,n){var o=t.find(function(e){return e&&e.index===r});if(o){if("horizontal"===e)return{x:o.coordinate,y:n.y};if("vertical"===e)return{x:n.x,y:o.coordinate};if("centric"===e){var i=o.coordinate,a=n.radius;return th(th(th({},n),(0,eU.IZ)(n.cx,n.cy,a,i)),{},{angle:i,radius:a})}var u=o.coordinate,c=n.angle;return th(th(th({},n),(0,eU.IZ)(n.cx,n.cy,u,c)),{},{angle:c,radius:u})}return tg},tO=function(e,t){var r=t.graphicalItems,n=t.dataStartIndex,o=t.dataEndIndex,i=(null!=r?r:[]).reduce(function(e,t){var r=t.props.data;return r&&r.length?[].concat(ts(e),ts(r)):e},[]);return i.length>0?i:e&&e.length&&(0,k.Et)(n)&&(0,k.Et)(o)?e.slice(n,o+1):[]};function tj(e){return"number"===e?[0,"auto"]:void 0}var tS=function(e,t,r,n){var o=e.graphicalItems,i=e.tooltipAxis,a=tO(t,e);return r<0||!o||!o.length||r>=a.length?null:o.reduce(function(o,u){var c,l,s=null!=(c=u.props.data)?c:t;if(s&&e.dataStartIndex+e.dataEndIndex!==0&&e.dataEndIndex-e.dataStartIndex>=r&&(s=s.slice(e.dataStartIndex,e.dataEndIndex+1)),i.dataKey&&!i.allowDuplicatedCategory){var f=void 0===s?a:s;l=(0,k.eP)(f,i.dataKey,n)}else l=s&&s[r]||a[r];return l?[].concat(ts(o),[(0,P.zb)(u,l)]):o},[])},tE=function(e,t,r,n){var o=n||{x:e.chartX,y:e.chartY},i="horizontal"===r?o.x:"vertical"===r?o.y:"centric"===r?o.angle:o.radius,a=e.orderedTooltipTicks,u=e.tooltipAxis,c=e.tooltipTicks,l=(0,P.gH)(i,a,c,u);if(l>=0&&c){var s=c[l]&&c[l].value,f=tS(e,t,l,s),p=tw(r,a,l,o);return{activeTooltipIndex:l,activeLabel:s,activePayload:f,activeCoordinate:p}}return null},tA=function(e,t){var r=t.axes,n=t.graphicalItems,o=t.axisType,a=t.axisIdKey,u=t.stackGroups,c=t.dataStartIndex,s=t.dataEndIndex,f=e.layout,p=e.children,d=e.stackOffset,h=(0,P._L)(f,o);return r.reduce(function(t,r){var y=void 0!==r.type.defaultProps?th(th({},r.type.defaultProps),r.props):r.props,v=y.type,m=y.dataKey,b=y.allowDataOverflow,g=y.allowDuplicatedCategory,x=y.scale,w=y.ticks,O=y.includeHidden,j=y[a];if(t[j])return t;var S=tO(e.data,{graphicalItems:n.filter(function(e){var t;return(a in e.props?e.props[a]:null==(t=e.type.defaultProps)?void 0:t[a])===j}),dataStartIndex:c,dataEndIndex:s}),E=S.length;(function(e,t,r){if("number"===r&&!0===t&&Array.isArray(e)){var n=null==e?void 0:e[0],o=null==e?void 0:e[1];if(n&&o&&(0,k.Et)(n)&&(0,k.Et)(o))return!0}return!1})(y.domain,b,v)&&(T=(0,P.AQ)(y.domain,null,b),h&&("number"===v||"auto"!==x)&&(C=(0,P.Ay)(S,m,"category")));var A=tj(v);if(!T||0===T.length){var M,T,_,C,D,N=null!=(D=y.domain)?D:A;if(m){if(T=(0,P.Ay)(S,m,v),"category"===v&&h){var I=(0,k.CG)(T);g&&I?(_=T,T=l()(0,E)):g||(T=(0,P.KC)(N,T,r).reduce(function(e,t){return e.indexOf(t)>=0?e:[].concat(ts(e),[t])},[]))}else if("category"===v)T=g?T.filter(function(e){return""!==e&&!i()(e)}):(0,P.KC)(N,T,r).reduce(function(e,t){return e.indexOf(t)>=0||""===t||i()(t)?e:[].concat(ts(e),[t])},[]);else if("number"===v){var R=(0,P.A1)(S,n.filter(function(e){var t,r,n=a in e.props?e.props[a]:null==(t=e.type.defaultProps)?void 0:t[a],o="hide"in e.props?e.props.hide:null==(r=e.type.defaultProps)?void 0:r.hide;return n===j&&(O||!o)}),m,o,f);R&&(T=R)}h&&("number"===v||"auto"!==x)&&(C=(0,P.Ay)(S,m,"category"))}else T=h?l()(0,E):u&&u[j]&&u[j].hasStack&&"number"===v?"expand"===d?[0,1]:(0,P.Mk)(u[j].stackGroups,c,s):(0,P.vf)(S,n.filter(function(e){var t=a in e.props?e.props[a]:e.type.defaultProps[a],r="hide"in e.props?e.props.hide:e.type.defaultProps.hide;return t===j&&(O||!r)}),v,f,!0);"number"===v?(T=ez(p,T,j,o,w),N&&(T=(0,P.AQ)(N,T,b))):"category"===v&&N&&T.every(function(e){return N.indexOf(e)>=0})&&(T=N)}return th(th({},t),{},ty({},j,th(th({},y),{},{axisType:o,domain:T,categoricalDomain:C,duplicateDomain:_,originalDomain:null!=(M=y.domain)?M:A,isCategorical:h,layout:f})))},{})},tP=function(e,t){var r=t.graphicalItems,n=t.Axis,o=t.axisType,i=t.axisIdKey,a=t.stackGroups,u=t.dataStartIndex,c=t.dataEndIndex,s=e.layout,p=e.children,d=tO(e.data,{graphicalItems:r,dataStartIndex:u,dataEndIndex:c}),h=d.length,y=(0,P._L)(s,o),v=-1;return r.reduce(function(e,t){var m,b=(void 0!==t.type.defaultProps?th(th({},t.type.defaultProps),t.props):t.props)[i],g=tj("number");return e[b]?e:(v++,m=y?l()(0,h):a&&a[b]&&a[b].hasStack?ez(p,m=(0,P.Mk)(a[b].stackGroups,u,c),b,o):ez(p,m=(0,P.AQ)(g,(0,P.vf)(d,r.filter(function(e){var t,r,n=i in e.props?e.props[i]:null==(t=e.type.defaultProps)?void 0:t[i],o="hide"in e.props?e.props.hide:null==(r=e.type.defaultProps)?void 0:r.hide;return n===b&&!o}),"number",s),n.defaultProps.allowDataOverflow),b,o),th(th({},e),{},ty({},b,th(th({axisType:o},n.defaultProps),{},{hide:!0,orientation:f()(tm,"".concat(o,".").concat(v%2),null),domain:m,originalDomain:g,isCategorical:y,layout:s}))))},{})},tk=function(e,t){var r=t.axisType,n=void 0===r?"xAxis":r,o=t.AxisComp,i=t.graphicalItems,a=t.stackGroups,u=t.dataStartIndex,c=t.dataEndIndex,l=e.children,s="".concat(n,"Id"),f=(0,S.aS)(l,o),p={};return f&&f.length?p=tA(e,{axes:f,graphicalItems:i,axisType:n,axisIdKey:s,stackGroups:a,dataStartIndex:u,dataEndIndex:c}):i&&i.length&&(p=tP(e,{Axis:o,graphicalItems:i,axisType:n,axisIdKey:s,stackGroups:a,dataStartIndex:u,dataEndIndex:c})),p},tM=function(e){var t=(0,k.lX)(e),r=(0,P.Rh)(t,!1,!0);return{tooltipTicks:r,orderedTooltipTicks:d()(r,function(e){return e.coordinate}),tooltipAxis:t,tooltipAxisBandSize:(0,P.Hj)(t,r)}},tT=function(e){var t=e.children,r=e.defaultShowTooltip,n=(0,S.BU)(t,X),o=0,i=0;return e.data&&0!==e.data.length&&(i=e.data.length-1),n&&n.props&&(n.props.startIndex>=0&&(o=n.props.startIndex),n.props.endIndex>=0&&(i=n.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:o,dataEndIndex:i,activeTooltipIndex:-1,isTooltipActive:!!r}},t_=function(e){return"horizontal"===e?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:"vertical"===e?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:"centric"===e?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},tC=function(e,t){var r=e.props,n=e.graphicalItems,o=e.xAxisMap,i=void 0===o?{}:o,a=e.yAxisMap,u=void 0===a?{}:a,c=r.width,l=r.height,s=r.children,p=r.margin||{},d=(0,S.BU)(s,X),h=(0,S.BU)(s,w.s),y=Object.keys(u).reduce(function(e,t){var r=u[t],n=r.orientation;return r.mirror||r.hide?e:th(th({},e),{},ty({},n,e[n]+r.width))},{left:p.left||0,right:p.right||0}),v=Object.keys(i).reduce(function(e,t){var r=i[t],n=r.orientation;return r.mirror||r.hide?e:th(th({},e),{},ty({},n,f()(e,"".concat(n))+r.height))},{top:p.top||0,bottom:p.bottom||0}),m=th(th({},v),y),b=m.bottom;d&&(m.bottom+=d.props.height||X.defaultProps.height),h&&t&&(m=(0,P.s0)(m,n,r,t));var g=c-m.left-m.right,x=l-m.top-m.bottom;return th(th({brushBottom:b},m),{},{width:Math.max(g,0),height:Math.max(x,0)})},tD=function(e){var t=e.chartName,r=e.GraphicalChild,o=e.defaultTooltipEventType,a=void 0===o?"axis":o,c=e.validateTooltipEventTypes,l=void 0===c?["axis"]:c,s=e.axisComponents,p=e.legendContent,d=e.formatAxisMap,h=e.defaultProps,w=function(e,t){var r=t.graphicalItems,n=t.stackGroups,o=t.offset,a=t.updateId,u=t.dataStartIndex,c=t.dataEndIndex,l=e.barSize,f=e.layout,p=e.barGap,d=e.barCategoryGap,h=e.maxBarSize,y=t_(f),v=y.numericAxisName,b=y.cateAxisName,g=!!r&&!!r.length&&r.some(function(e){var t=(0,S.Mn)(e&&e.type);return t&&t.indexOf("Bar")>=0}),x=[];return r.forEach(function(r,y){var w=tO(e.data,{graphicalItems:[r],dataStartIndex:u,dataEndIndex:c}),O=void 0!==r.type.defaultProps?th(th({},r.type.defaultProps),r.props):r.props,j=O.dataKey,E=O.maxBarSize,A=O["".concat(v,"Id")],k=O["".concat(b,"Id")],M=s.reduce(function(e,r){var n=t["".concat(r.axisType,"Map")],o=O["".concat(r.axisType,"Id")];n&&n[o]||"zAxis"===r.axisType||(0,m.A)(!1);var i=n[o];return th(th({},e),{},ty(ty({},r.axisType,i),"".concat(r.axisType,"Ticks"),(0,P.Rh)(i)))},{}),T=M[b],_=M["".concat(b,"Ticks")],C=n&&n[A]&&n[A].hasStack&&(0,P.kA)(r,n[A].stackGroups),D=(0,S.Mn)(r.type).indexOf("Bar")>=0,N=(0,P.Hj)(T,_),I=[],R=g&&(0,P.tA)({barSize:l,stackGroups:n,totalSize:"xAxis"===b?M[b].width:"yAxis"===b?M[b].height:void 0});if(D){var B,L,W=i()(E)?h:E,F=null!=(B=null!=(L=(0,P.Hj)(T,_,!0))?L:W)?B:0;I=(0,P.BX)({barGap:p,barCategoryGap:d,bandSize:F!==N?F:N,sizeList:R[k],maxBarSize:W}),F!==N&&(I=I.map(function(e){return th(th({},e),{},{position:th(th({},e.position),{},{offset:e.position.offset-F/2})})}))}var z=r&&r.type&&r.type.getComposedData;z&&x.push({props:th(th({},z(th(th({},M),{},{displayedData:w,props:e,dataKey:j,item:r,bandSize:N,barPosition:I,offset:o,stackedData:C,layout:f,dataStartIndex:u,dataEndIndex:c}))),{},ty(ty(ty({key:r.key||"item-".concat(y)},v,M[v]),b,M[b]),"animationId",a)),childIndex:(0,S.AW)(r,e.children),item:r})}),x},E=function(e,n){var o=e.props,i=e.dataStartIndex,a=e.dataEndIndex,u=e.updateId;if(!(0,S.Me)({props:o}))return null;var c=o.children,l=o.layout,f=o.stackOffset,p=o.data,h=o.reverseStackOrder,y=t_(l),v=y.numericAxisName,m=y.cateAxisName,b=(0,S.aS)(c,r),g=(0,P.Mn)(p,b,"".concat(v,"Id"),"".concat(m,"Id"),f,h),x=s.reduce(function(e,t){var r="".concat(t.axisType,"Map");return th(th({},e),{},ty({},r,tk(o,th(th({},t),{},{graphicalItems:b,stackGroups:t.axisType===v&&g,dataStartIndex:i,dataEndIndex:a}))))},{}),O=tC(th(th({},x),{},{props:o,graphicalItems:b}),null==n?void 0:n.legendBBox);Object.keys(x).forEach(function(e){x[e]=d(o,x[e],O,e.replace("Map",""),t)});var j=tM(x["".concat(m,"Map")]),E=w(o,th(th({},x),{},{dataStartIndex:i,dataEndIndex:a,updateId:u,graphicalItems:b,stackGroups:g,offset:O}));return th(th({formattedGraphicalItems:E,graphicalItems:b,offset:O,stackGroups:g},j),x)},A=function(e){var r;function o(e){var r,a,c,l,s;if(!(this instanceof o))throw TypeError("Cannot call a class as a function");return l=o,s=[e],l=tc(l),ty(c=function(e,t){if(t&&("object"===tn(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");var r=e;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,tu()?Reflect.construct(l,s||[],tc(this).constructor):l.apply(this,s)),"eventEmitterSymbol",Symbol("rechartsEventEmitter")),ty(c,"accessibilityManager",new eK),ty(c,"handleLegendBBoxUpdate",function(e){if(e){var t=c.state,r=t.dataStartIndex,n=t.dataEndIndex,o=t.updateId;c.setState(th({legendBBox:e},E({props:c.props,dataStartIndex:r,dataEndIndex:n,updateId:o},th(th({},c.state),{},{legendBBox:e}))))}}),ty(c,"handleReceiveSyncEvent",function(e,t,r){c.props.syncId===e&&(r!==c.eventEmitterSymbol||"function"==typeof c.props.syncMethod)&&c.applySyncEvent(t)}),ty(c,"handleBrushChange",function(e){var t=e.startIndex,r=e.endIndex;if(t!==c.state.dataStartIndex||r!==c.state.dataEndIndex){var n=c.state.updateId;c.setState(function(){return th({dataStartIndex:t,dataEndIndex:r},E({props:c.props,dataStartIndex:t,dataEndIndex:r,updateId:n},c.state))}),c.triggerSyncEvent({dataStartIndex:t,dataEndIndex:r})}}),ty(c,"handleMouseEnter",function(e){var t=c.getMouseInfo(e);if(t){var r=th(th({},t),{},{isTooltipActive:!0});c.setState(r),c.triggerSyncEvent(r);var n=c.props.onMouseEnter;u()(n)&&n(r,e)}}),ty(c,"triggeredAfterMouseMove",function(e){var t=c.getMouseInfo(e),r=t?th(th({},t),{},{isTooltipActive:!0}):{isTooltipActive:!1};c.setState(r),c.triggerSyncEvent(r);var n=c.props.onMouseMove;u()(n)&&n(r,e)}),ty(c,"handleItemMouseEnter",function(e){c.setState(function(){return{isTooltipActive:!0,activeItem:e,activePayload:e.tooltipPayload,activeCoordinate:e.tooltipPosition||{x:e.cx,y:e.cy}}})}),ty(c,"handleItemMouseLeave",function(){c.setState(function(){return{isTooltipActive:!1}})}),ty(c,"handleMouseMove",function(e){e.persist(),c.throttleTriggeredAfterMouseMove(e)}),ty(c,"handleMouseLeave",function(e){c.throttleTriggeredAfterMouseMove.cancel();var t={isTooltipActive:!1};c.setState(t),c.triggerSyncEvent(t);var r=c.props.onMouseLeave;u()(r)&&r(t,e)}),ty(c,"handleOuterEvent",function(e){var t,r,n=(0,S.X_)(e),o=f()(c.props,"".concat(n));n&&u()(o)&&o(null!=(t=/.*touch.*/i.test(n)?c.getMouseInfo(e.changedTouches[0]):c.getMouseInfo(e))?t:{},e)}),ty(c,"handleClick",function(e){var t=c.getMouseInfo(e);if(t){var r=th(th({},t),{},{isTooltipActive:!0});c.setState(r),c.triggerSyncEvent(r);var n=c.props.onClick;u()(n)&&n(r,e)}}),ty(c,"handleMouseDown",function(e){var t=c.props.onMouseDown;u()(t)&&t(c.getMouseInfo(e),e)}),ty(c,"handleMouseUp",function(e){var t=c.props.onMouseUp;u()(t)&&t(c.getMouseInfo(e),e)}),ty(c,"handleTouchMove",function(e){null!=e.changedTouches&&e.changedTouches.length>0&&c.throttleTriggeredAfterMouseMove(e.changedTouches[0])}),ty(c,"handleTouchStart",function(e){null!=e.changedTouches&&e.changedTouches.length>0&&c.handleMouseDown(e.changedTouches[0])}),ty(c,"handleTouchEnd",function(e){null!=e.changedTouches&&e.changedTouches.length>0&&c.handleMouseUp(e.changedTouches[0])}),ty(c,"handleDoubleClick",function(e){var t=c.props.onDoubleClick;u()(t)&&t(c.getMouseInfo(e),e)}),ty(c,"handleContextMenu",function(e){var t=c.props.onContextMenu;u()(t)&&t(c.getMouseInfo(e),e)}),ty(c,"triggerSyncEvent",function(e){void 0!==c.props.syncId&&eH.emit(eq,c.props.syncId,e,c.eventEmitterSymbol)}),ty(c,"applySyncEvent",function(e){var t=c.props,r=t.layout,n=t.syncMethod,o=c.state.updateId,i=e.dataStartIndex,a=e.dataEndIndex;if(void 0!==e.dataStartIndex||void 0!==e.dataEndIndex)c.setState(th({dataStartIndex:i,dataEndIndex:a},E({props:c.props,dataStartIndex:i,dataEndIndex:a,updateId:o},c.state)));else if(void 0!==e.activeTooltipIndex){var u=e.chartX,l=e.chartY,s=e.activeTooltipIndex,f=c.state,p=f.offset,d=f.tooltipTicks;if(!p)return;if("function"==typeof n)s=n(d,e);else if("value"===n){s=-1;for(var h=0;h<d.length;h++)if(d[h].value===e.activeLabel){s=h;break}}var y=th(th({},p),{},{x:p.left,y:p.top}),v=Math.min(u,y.x+y.width),m=Math.min(l,y.y+y.height),b=d[s]&&d[s].value,g=tS(c.state,c.props.data,s),x=d[s]?{x:"horizontal"===r?d[s].coordinate:v,y:"horizontal"===r?m:d[s].coordinate}:tg;c.setState(th(th({},e),{},{activeLabel:b,activeCoordinate:x,activePayload:g,activeTooltipIndex:s}))}else c.setState(e)}),ty(c,"renderCursor",function(e){var r,o=c.state,i=o.isTooltipActive,a=o.activeCoordinate,u=o.activePayload,l=o.offset,s=o.activeTooltipIndex,f=o.tooltipAxisBandSize,p=c.getTooltipEventType(),d=null!=(r=e.props.active)?r:i,h=c.props.layout,y=e.key||"_recharts-cursor";return n.createElement(te,{key:y,activeCoordinate:a,activePayload:u,activeTooltipIndex:s,chartName:t,element:e,isActive:d,layout:h,offset:l,tooltipAxisBandSize:f,tooltipEventType:p})}),ty(c,"renderPolarAxis",function(e,t,r){var o=f()(e,"type.axisType"),i=f()(c.state,"".concat(o,"Map")),a=e.type.defaultProps,u=void 0!==a?th(th({},a),e.props):e.props,l=i&&i[u["".concat(o,"Id")]];return(0,n.cloneElement)(e,th(th({},l),{},{className:(0,v.A)(o,l.className),key:e.key||"".concat(t,"-").concat(r),ticks:(0,P.Rh)(l,!0)}))}),ty(c,"renderPolarGrid",function(e){var t=e.props,r=t.radialLines,o=t.polarAngles,i=t.polarRadius,a=c.state,u=a.radiusAxisMap,l=a.angleAxisMap,s=(0,k.lX)(u),f=(0,k.lX)(l),p=f.cx,d=f.cy,h=f.innerRadius,y=f.outerRadius;return(0,n.cloneElement)(e,{polarAngles:Array.isArray(o)?o:(0,P.Rh)(f,!0).map(function(e){return e.coordinate}),polarRadius:Array.isArray(i)?i:(0,P.Rh)(s,!0).map(function(e){return e.coordinate}),cx:p,cy:d,innerRadius:h,outerRadius:y,key:e.key||"polar-grid",radialLines:r})}),ty(c,"renderLegend",function(){var e=c.state.formattedGraphicalItems,t=c.props,r=t.children,o=t.width,i=t.height,a=c.props.margin||{},u=o-(a.left||0)-(a.right||0),l=(0,V.g)({children:r,formattedGraphicalItems:e,legendWidth:u,legendContent:p});if(!l)return null;var s=l.item,f=ta(l,tt);return(0,n.cloneElement)(s,th(th({},f),{},{chartWidth:o,chartHeight:i,margin:a,onBBoxUpdate:c.handleLegendBBoxUpdate}))}),ty(c,"renderTooltip",function(){var e,t=c.props,r=t.children,o=t.accessibilityLayer,i=(0,S.BU)(r,x.m);if(!i)return null;var a=c.state,u=a.isTooltipActive,l=a.activeCoordinate,s=a.activePayload,f=a.activeLabel,p=a.offset,d=null!=(e=i.props.active)?e:u;return(0,n.cloneElement)(i,{viewBox:th(th({},p),{},{x:p.left,y:p.top}),active:d,label:f,payload:d?s:[],coordinate:l,accessibilityLayer:o})}),ty(c,"renderBrush",function(e){var t=c.props,r=t.margin,o=t.data,i=c.state,a=i.offset,u=i.dataStartIndex,l=i.dataEndIndex,s=i.updateId;return(0,n.cloneElement)(e,{key:e.key||"_recharts-brush",onChange:(0,P.HQ)(c.handleBrushChange,e.props.onChange),data:o,x:(0,k.Et)(e.props.x)?e.props.x:a.left,y:(0,k.Et)(e.props.y)?e.props.y:a.top+a.height+a.brushBottom-(r.bottom||0),width:(0,k.Et)(e.props.width)?e.props.width:a.width,startIndex:u,endIndex:l,updateId:"brush-".concat(s)})}),ty(c,"renderReferenceElement",function(e,t,r){if(!e)return null;var o=c.clipPathId,i=c.state,a=i.xAxisMap,u=i.yAxisMap,l=i.offset,s=e.type.defaultProps||{},f=e.props,p=f.xAxisId,d=void 0===p?s.xAxisId:p,h=f.yAxisId,y=void 0===h?s.yAxisId:h;return(0,n.cloneElement)(e,{key:e.key||"".concat(t,"-").concat(r),xAxis:a[d],yAxis:u[y],viewBox:{x:l.left,y:l.top,width:l.width,height:l.height},clipPathId:o})}),ty(c,"renderActivePoints",function(e){var t=e.item,r=e.activePoint,n=e.basePoint,i=e.childIndex,a=e.isRange,u=[],c=t.props.key,l=void 0!==t.item.type.defaultProps?th(th({},t.item.type.defaultProps),t.item.props):t.item.props,s=l.activeDot,f=th(th({index:i,dataKey:l.dataKey,cx:r.x,cy:r.y,r:4,fill:(0,P.Ps)(t.item),strokeWidth:2,stroke:"#fff",payload:r.payload,value:r.value},(0,S.J9)(s,!1)),(0,eX._U)(s));return u.push(o.renderActiveDot(s,f,"".concat(c,"-activePoint-").concat(i))),n?u.push(o.renderActiveDot(s,th(th({},f),{},{cx:n.x,cy:n.y}),"".concat(c,"-basePoint-").concat(i))):a&&u.push(null),u}),ty(c,"renderGraphicChild",function(e,t,r){var o=c.filterFormatItem(e,t,r);if(!o)return null;var a=c.getTooltipEventType(),u=c.state,l=u.isTooltipActive,s=u.tooltipAxis,f=u.activeTooltipIndex,p=u.activeLabel,d=c.props.children,h=(0,S.BU)(d,x.m),y=o.props,v=y.points,m=y.isRange,b=y.baseLine,g=void 0!==o.item.type.defaultProps?th(th({},o.item.type.defaultProps),o.item.props):o.item.props,w=g.activeDot,O=g.hide,j=g.activeBar,E=g.activeShape,A=!!(!O&&l&&h&&(w||j||E)),M={};"axis"!==a&&h&&"click"===h.props.trigger?M={onClick:(0,P.HQ)(c.handleItemMouseEnter,e.props.onClick)}:"axis"!==a&&(M={onMouseLeave:(0,P.HQ)(c.handleItemMouseLeave,e.props.onMouseLeave),onMouseEnter:(0,P.HQ)(c.handleItemMouseEnter,e.props.onMouseEnter)});var T=(0,n.cloneElement)(e,th(th({},o.props),M));if(A)if(f>=0){if(s.dataKey&&!s.allowDuplicatedCategory){var _="function"==typeof s.dataKey?function(e){return"function"==typeof s.dataKey?s.dataKey(e.payload):null}:"payload.".concat(s.dataKey.toString());D=(0,k.eP)(v,_,p),N=m&&b&&(0,k.eP)(b,_,p)}else D=null==v?void 0:v[f],N=m&&b&&b[f];if(E||j){var C=void 0!==e.props.activeIndex?e.props.activeIndex:f;return[(0,n.cloneElement)(e,th(th(th({},o.props),M),{},{activeIndex:C})),null,null]}if(!i()(D))return[T].concat(ts(c.renderActivePoints({item:o,activePoint:D,basePoint:N,childIndex:f,isRange:m})))}else{var D,N,I,R=(null!=(I=c.getItemByXY(c.state.activeCoordinate))?I:{graphicalItem:T}).graphicalItem,B=R.item,L=void 0===B?e:B,W=R.childIndex,F=th(th(th({},o.props),M),{},{activeIndex:W});return[(0,n.cloneElement)(L,F),null,null]}return m?[T,null,null]:[T,null]}),ty(c,"renderCustomized",function(e,t,r){return(0,n.cloneElement)(e,th(th({key:"recharts-customized-".concat(r)},c.props),c.state))}),ty(c,"renderMap",{CartesianGrid:{handler:tx,once:!0},ReferenceArea:{handler:c.renderReferenceElement},ReferenceLine:{handler:tx},ReferenceDot:{handler:c.renderReferenceElement},XAxis:{handler:tx},YAxis:{handler:tx},Brush:{handler:c.renderBrush,once:!0},Bar:{handler:c.renderGraphicChild},Line:{handler:c.renderGraphicChild},Area:{handler:c.renderGraphicChild},Radar:{handler:c.renderGraphicChild},RadialBar:{handler:c.renderGraphicChild},Scatter:{handler:c.renderGraphicChild},Pie:{handler:c.renderGraphicChild},Funnel:{handler:c.renderGraphicChild},Tooltip:{handler:c.renderCursor,once:!0},PolarGrid:{handler:c.renderPolarGrid,once:!0},PolarAngleAxis:{handler:c.renderPolarAxis},PolarRadiusAxis:{handler:c.renderPolarAxis},Customized:{handler:c.renderCustomized}}),c.clipPathId="".concat(null!=(r=e.id)?r:(0,k.NF)("recharts"),"-clip"),c.throttleTriggeredAfterMouseMove=y()(c.triggeredAfterMouseMove,null!=(a=e.throttleDelay)?a:1e3/60),c.state={},c}if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");return o.prototype=Object.create(e&&e.prototype,{constructor:{value:o,writable:!0,configurable:!0}}),Object.defineProperty(o,"prototype",{writable:!1}),e&&tl(o,e),r=[{key:"componentDidMount",value:function(){var e,t;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:null!=(e=this.props.margin.left)?e:0,top:null!=(t=this.props.margin.top)?t:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var e=this.props,t=e.children,r=e.data,n=e.height,o=e.layout,i=(0,S.BU)(t,x.m);if(i){var a=i.props.defaultIndex;if("number"==typeof a&&!(a<0)&&!(a>this.state.tooltipTicks.length-1)){var u=this.state.tooltipTicks[a]&&this.state.tooltipTicks[a].value,c=tS(this.state,r,a,u),l=this.state.tooltipTicks[a].coordinate,s=(this.state.offset.top+n)/2,f="horizontal"===o?{x:l,y:s}:{y:l,x:s},p=this.state.formattedGraphicalItems.find(function(e){return"Scatter"===e.item.type.name});p&&(f=th(th({},f),p.props.points[a].tooltipPosition),c=p.props.points[a].tooltipPayload);var d={activeTooltipIndex:a,isTooltipActive:!0,activeLabel:u,activePayload:c,activeCoordinate:f};this.setState(d),this.renderCursor(i),this.accessibilityManager.setIndex(a)}}}},{key:"getSnapshotBeforeUpdate",value:function(e,t){if(!this.props.accessibilityLayer)return null;if(this.state.tooltipTicks!==t.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==e.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==e.margin){var r,n;this.accessibilityManager.setDetails({offset:{left:null!=(r=this.props.margin.left)?r:0,top:null!=(n=this.props.margin.top)?n:0}})}return null}},{key:"componentDidUpdate",value:function(e){(0,S.OV)([(0,S.BU)(e.children,x.m)],[(0,S.BU)(this.props.children,x.m)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var e=(0,S.BU)(this.props.children,x.m);if(e&&"boolean"==typeof e.props.shared){var t=e.props.shared?"axis":"item";return l.indexOf(t)>=0?t:a}return a}},{key:"getMouseInfo",value:function(e){if(!this.container)return null;var t=this.container,r=t.getBoundingClientRect(),n=(0,Z.A3)(r),o={chartX:Math.round(e.pageX-n.left),chartY:Math.round(e.pageY-n.top)},i=r.width/t.offsetWidth||1,a=this.inRange(o.chartX,o.chartY,i);if(!a)return null;var u=this.state,c=u.xAxisMap,l=u.yAxisMap,s=this.getTooltipEventType(),f=tE(this.state,this.props.data,this.props.layout,a);if("axis"!==s&&c&&l){var p=(0,k.lX)(c).scale,d=(0,k.lX)(l).scale,h=p&&p.invert?p.invert(o.chartX):null,y=d&&d.invert?d.invert(o.chartY):null;return th(th({},o),{},{xValue:h,yValue:y},f)}return f?th(th({},o),f):null}},{key:"inRange",value:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=this.props.layout,o=e/r,i=t/r;if("horizontal"===n||"vertical"===n){var a=this.state.offset;return o>=a.left&&o<=a.left+a.width&&i>=a.top&&i<=a.top+a.height?{x:o,y:i}:null}var u=this.state,c=u.angleAxisMap,l=u.radiusAxisMap;if(c&&l){var s=(0,k.lX)(c);return(0,eU.yy)({x:o,y:i},s)}return null}},{key:"parseEventsOfWrapper",value:function(){var e=this.props.children,t=this.getTooltipEventType(),r=(0,S.BU)(e,x.m),n={};return r&&"axis"===t&&(n="click"===r.props.trigger?{onClick:this.handleClick}:{onMouseEnter:this.handleMouseEnter,onDoubleClick:this.handleDoubleClick,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onContextMenu:this.handleContextMenu}),th(th({},(0,eX._U)(this.props,this.handleOuterEvent)),n)}},{key:"addListener",value:function(){eH.on(eq,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){eH.removeListener(eq,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(e,t,r){for(var n=this.state.formattedGraphicalItems,o=0,i=n.length;o<i;o++){var a=n[o];if(a.item===e||a.props.key===e.key||t===(0,S.Mn)(a.item.type)&&r===a.childIndex)return a}return null}},{key:"renderClipPath",value:function(){var e=this.clipPathId,t=this.state.offset,r=t.left,o=t.top,i=t.height,a=t.width;return n.createElement("defs",null,n.createElement("clipPath",{id:e},n.createElement("rect",{x:r,y:o,height:i,width:a})))}},{key:"getXScales",value:function(){var e=this.state.xAxisMap;return e?Object.entries(e).reduce(function(e,t){var r=ti(t,2),n=r[0],o=r[1];return th(th({},e),{},ty({},n,o.scale))},{}):null}},{key:"getYScales",value:function(){var e=this.state.yAxisMap;return e?Object.entries(e).reduce(function(e,t){var r=ti(t,2),n=r[0],o=r[1];return th(th({},e),{},ty({},n,o.scale))},{}):null}},{key:"getXScaleByAxisId",value:function(e){var t;return null==(t=this.state.xAxisMap)||null==(t=t[e])?void 0:t.scale}},{key:"getYScaleByAxisId",value:function(e){var t;return null==(t=this.state.yAxisMap)||null==(t=t[e])?void 0:t.scale}},{key:"getItemByXY",value:function(e){var t=this.state,r=t.formattedGraphicalItems,n=t.activeItem;if(r&&r.length)for(var o=0,i=r.length;o<i;o++){var a=r[o],u=a.props,c=a.item,l=void 0!==c.type.defaultProps?th(th({},c.type.defaultProps),c.props):c.props,s=(0,S.Mn)(c.type);if("Bar"===s){var f=(u.data||[]).find(function(t){return(0,j.J)(e,t)});if(f)return{graphicalItem:a,payload:f}}else if("RadialBar"===s){var p=(u.data||[]).find(function(t){return(0,eU.yy)(e,t)});if(p)return{graphicalItem:a,payload:p}}else if((0,eJ.NE)(a,n)||(0,eJ.nZ)(a,n)||(0,eJ.xQ)(a,n)){var d=(0,eJ.GG)({graphicalItem:a,activeTooltipItem:n,itemData:l.data}),h=void 0===l.activeIndex?d:l.activeIndex;return{graphicalItem:th(th({},a),{},{childIndex:h}),payload:(0,eJ.xQ)(a,n)?l.data[d]:a.props.data[d]}}}return null}},{key:"render",value:function(){var e,t,r=this;if(!(0,S.Me)(this))return null;var o=this.props,i=o.children,a=o.className,u=o.width,c=o.height,l=o.style,s=o.compact,f=o.title,p=o.desc,d=ta(o,tr),h=(0,S.J9)(d,!1);if(s)return n.createElement(ed.DR,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},n.createElement(b.u,to({},h,{width:u,height:c,title:f,desc:p}),this.renderClipPath(),(0,S.ee)(i,this.renderMap)));this.props.accessibilityLayer&&(h.tabIndex=null!=(e=this.props.tabIndex)?e:0,h.role=null!=(t=this.props.role)?t:"application",h.onKeyDown=function(e){r.accessibilityManager.keyboardEvent(e)},h.onFocus=function(){r.accessibilityManager.focus()});var y=this.parseEventsOfWrapper();return n.createElement(ed.DR,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},n.createElement("div",to({className:(0,v.A)("recharts-wrapper",a),style:th({position:"relative",cursor:"default",width:u,height:c},l)},y,{ref:function(e){r.container=e}}),n.createElement(b.u,to({},h,{width:u,height:c,title:f,desc:p,style:tb}),this.renderClipPath(),(0,S.ee)(i,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,tv(n.key),n)}}(o.prototype,r),Object.defineProperty(o,"prototype",{writable:!1}),o}(n.Component);ty(A,"displayName",t),ty(A,"defaultProps",th({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},h)),ty(A,"getDerivedStateFromProps",function(e,t){var r=e.dataKey,n=e.data,o=e.children,a=e.width,u=e.height,c=e.layout,l=e.stackOffset,s=e.margin,f=t.dataStartIndex,p=t.dataEndIndex;if(void 0===t.updateId){var d=tT(e);return th(th(th({},d),{},{updateId:0},E(th(th({props:e},d),{},{updateId:0}),t)),{},{prevDataKey:r,prevData:n,prevWidth:a,prevHeight:u,prevLayout:c,prevStackOffset:l,prevMargin:s,prevChildren:o})}if(r!==t.prevDataKey||n!==t.prevData||a!==t.prevWidth||u!==t.prevHeight||c!==t.prevLayout||l!==t.prevStackOffset||!(0,e$.b)(s,t.prevMargin)){var h=tT(e),y={chartX:t.chartX,chartY:t.chartY,isTooltipActive:t.isTooltipActive},v=th(th({},tE(t,n,c)),{},{updateId:t.updateId+1}),m=th(th(th({},h),y),v);return th(th(th({},m),E(th({props:e},m),t)),{},{prevDataKey:r,prevData:n,prevWidth:a,prevHeight:u,prevLayout:c,prevStackOffset:l,prevMargin:s,prevChildren:o})}if(!(0,S.OV)(o,t.prevChildren)){var b,g,x,w,O=(0,S.BU)(o,X),j=O&&null!=(b=null==(g=O.props)?void 0:g.startIndex)?b:f,A=O&&null!=(x=null==(w=O.props)?void 0:w.endIndex)?x:p,P=i()(n)||j!==f||A!==p?t.updateId+1:t.updateId;return th(th({updateId:P},E(th(th({props:e},t),{},{updateId:P,dataStartIndex:j,dataEndIndex:A}),t)),{},{prevChildren:o,dataStartIndex:j,dataEndIndex:A})}return null}),ty(A,"renderActiveDot",function(e,t,r){var o;return o=(0,n.isValidElement)(e)?(0,n.cloneElement)(e,t):u()(e)?e(t):n.createElement(O.c,t),n.createElement(g.W,{className:"recharts-active-dot",key:r},o)});var M=(0,n.forwardRef)(function(e,t){return n.createElement(A,to({},e,{ref:t}))});return M.displayName=A.displayName,M}},92972:e=>{e.exports=function(e,t){return function(r){return null!=r&&r[e]===t&&(void 0!==t||e in Object(r))}}},93179:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});function n(e,t){if(!e)throw Error("Invariant failed")}},93294:(e,t,r)=>{var n=r(34711);e.exports=function(e){return function(t){return n(t,e)}}},94011:(e,t,r)=>{"use strict";r.d(t,{u:()=>v});var n=r(12115),o=r(93179),i=r(2348),a=r(70788),u=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function c(e){return(c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function s(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function f(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(f=function(){return!!e})()}function p(e){return(p=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function d(e,t){return(d=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function h(e,t,r){return(t=y(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function y(e){var t=function(e,t){if("object"!=c(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=c(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==c(t)?t:t+""}var v=function(e){var t;function r(){var e,t;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return e=r,t=arguments,e=p(e),function(e,t){if(t&&("object"===c(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");var r=e;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,f()?Reflect.construct(e,t||[],p(this).constructor):e.apply(this,t))}if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(e&&e.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),e&&d(r,e),t=[{key:"render",value:function(){var e=this.props,t=e.offset,r=e.layout,c=e.width,f=e.dataKey,p=e.data,d=e.dataPointFormatter,h=e.xAxis,y=e.yAxis,v=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,u),m=(0,a.J9)(v,!1);"x"===this.props.direction&&"number"!==h.type&&(0,o.A)(!1);var b=p.map(function(e){var o,a,u=d(e,f),p=u.x,v=u.y,b=u.value,g=u.errorVal;if(!g)return null;var x=[];if(Array.isArray(g)){var w=function(e){if(Array.isArray(e))return e}(g)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{i=(r=r.call(e)).next,!1;for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(e){l=!0,o=e}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(g,2)||function(e,t){if(e){if("string"==typeof e)return s(e,2);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return s(e,t)}}(g,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();o=w[0],a=w[1]}else o=a=g;if("vertical"===r){var O=h.scale,j=v+t,S=j+c,E=j-c,A=O(b-o),P=O(b+a);x.push({x1:P,y1:S,x2:P,y2:E}),x.push({x1:A,y1:j,x2:P,y2:j}),x.push({x1:A,y1:S,x2:A,y2:E})}else if("horizontal"===r){var k=y.scale,M=p+t,T=M-c,_=M+c,C=k(b-o),D=k(b+a);x.push({x1:T,y1:D,x2:_,y2:D}),x.push({x1:M,y1:C,x2:M,y2:D}),x.push({x1:T,y1:C,x2:_,y2:C})}return n.createElement(i.W,l({className:"recharts-errorBar",key:"bar-".concat(x.map(function(e){return"".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2)}))},m),x.map(function(e){return n.createElement("line",l({},e,{key:"line-".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2)}))}))});return n.createElement(i.W,{className:"recharts-errorBars"},b)}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,y(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(n.Component);h(v,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"}),h(v,"displayName","ErrorBar")},94356:(e,t,r)=>{var n=r(70771),o=1/0;e.exports=function(e){if("string"==typeof e||n(e))return e;var t=e+"";return"0"==t&&1/e==-o?"-0":t}},94380:(e,t,r)=>{var n=r(44101),o=r(18686),i=r(66373),a=r(38008),u=r(40382),c=r(98233),l=r(7512),s="[object Map]",f="[object Promise]",p="[object Set]",d="[object WeakMap]",h="[object DataView]",y=l(n),v=l(o),m=l(i),b=l(a),g=l(u),x=c;(n&&x(new n(new ArrayBuffer(1)))!=h||o&&x(new o)!=s||i&&x(i.resolve())!=f||a&&x(new a)!=p||u&&x(new u)!=d)&&(x=function(e){var t=c(e),r="[object Object]"==t?e.constructor:void 0,n=r?l(r):"";if(n)switch(n){case y:return h;case v:return s;case m:return f;case b:return p;case g:return d}return t}),e.exports=x},94517:(e,t,r)=>{"use strict";r.d(t,{m:()=>U});var n=r(12115),o=r(67206),i=r.n(o),a=r(59882),u=r.n(a),c=r(52596),l=r(16377);function s(e){return(s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function f(){return(f=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function p(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function d(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function h(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?d(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=s(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=s(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==s(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):d(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function y(e){return Array.isArray(e)&&(0,l.vh)(e[0])&&(0,l.vh)(e[1])?e.join(" ~ "):e}var v=function(e){var t=e.separator,r=void 0===t?" : ":t,o=e.contentStyle,a=e.itemStyle,s=void 0===a?{}:a,d=e.labelStyle,v=e.payload,m=e.formatter,b=e.itemSorter,g=e.wrapperClassName,x=e.labelClassName,w=e.label,O=e.labelFormatter,j=e.accessibilityLayer,S=h({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},void 0===o?{}:o),E=h({margin:0},void 0===d?{}:d),A=!u()(w),P=A?w:"",k=(0,c.A)("recharts-default-tooltip",g),M=(0,c.A)("recharts-tooltip-label",x);return A&&O&&null!=v&&(P=O(w,v)),n.createElement("div",f({className:k,style:S},void 0!==j&&j?{role:"status","aria-live":"assertive"}:{}),n.createElement("p",{className:M,style:E},n.isValidElement(P)?P:"".concat(P)),function(){if(v&&v.length){var e=(b?i()(v,b):v).map(function(e,t){if("none"===e.type)return null;var o=h({display:"block",paddingTop:4,paddingBottom:4,color:e.color||"#000"},s),i=e.formatter||m||y,a=e.value,u=e.name,c=a,f=u;if(i&&null!=c&&null!=f){var d=i(a,u,e,t,v);if(Array.isArray(d)){var b=function(e){if(Array.isArray(e))return e}(d)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{i=(r=r.call(e)).next,!1;for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(e){l=!0,o=e}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(d,2)||function(e,t){if(e){if("string"==typeof e)return p(e,2);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return p(e,t)}}(d,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();c=b[0],f=b[1]}else c=d}return n.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(t),style:o},(0,l.vh)(f)?n.createElement("span",{className:"recharts-tooltip-item-name"},f):null,(0,l.vh)(f)?n.createElement("span",{className:"recharts-tooltip-item-separator"},r):null,n.createElement("span",{className:"recharts-tooltip-item-value"},c),n.createElement("span",{className:"recharts-tooltip-item-unit"},e.unit||""))});return n.createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},e)}return null}())};function m(e){return(m="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function b(e,t,r){var n;return(n=function(e,t){if("object"!=m(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=m(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"==m(n)?n:n+"")in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var g="recharts-tooltip-wrapper",x={visibility:"hidden"};function w(e){var t=e.allowEscapeViewBox,r=e.coordinate,n=e.key,o=e.offsetTopLeft,i=e.position,a=e.reverseDirection,u=e.tooltipDimension,c=e.viewBox,s=e.viewBoxDimension;if(i&&(0,l.Et)(i[n]))return i[n];var f=r[n]-u-o,p=r[n]+o;return t[n]?a[n]?f:p:a[n]?f<c[n]?Math.max(p,c[n]):Math.max(f,c[n]):p+u>c[n]+s?Math.max(f,c[n]):Math.max(p,c[n])}function O(e){return(O="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function j(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function S(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?j(Object(r),!0).forEach(function(t){k(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):j(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function E(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(E=function(){return!!e})()}function A(e){return(A=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function P(e,t){return(P=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function k(e,t,r){return(t=M(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function M(e){var t=function(e,t){if("object"!=O(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=O(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==O(t)?t:t+""}var T=function(e){var t;function r(){var e,t,n;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");for(var o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return t=r,n=[].concat(i),t=A(t),k(e=function(e,t){if(t&&("object"===O(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");var r=e;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,E()?Reflect.construct(t,n||[],A(this).constructor):t.apply(this,n)),"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),k(e,"handleKeyDown",function(t){if("Escape"===t.key){var r,n,o,i;e.setState({dismissed:!0,dismissedAtCoordinate:{x:null!=(r=null==(n=e.props.coordinate)?void 0:n.x)?r:0,y:null!=(o=null==(i=e.props.coordinate)?void 0:i.y)?o:0}})}}),e}if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(e&&e.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),e&&P(r,e),t=[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var e=this.wrapperNode.getBoundingClientRect();(Math.abs(e.width-this.state.lastBoundingBox.width)>1||Math.abs(e.height-this.state.lastBoundingBox.height)>1)&&this.setState({lastBoundingBox:{width:e.width,height:e.height}})}else(-1!==this.state.lastBoundingBox.width||-1!==this.state.lastBoundingBox.height)&&this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var e,t;this.props.active&&this.updateBBox(),this.state.dismissed&&((null==(e=this.props.coordinate)?void 0:e.x)!==this.state.dismissedAtCoordinate.x||(null==(t=this.props.coordinate)?void 0:t.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}},{key:"render",value:function(){var e,t,r,o,i,a,u,s,f,p,d,h,y,v,m,O,j,E,A,P=this,k=this.props,M=k.active,T=k.allowEscapeViewBox,_=k.animationDuration,C=k.animationEasing,D=k.children,N=k.coordinate,I=k.hasPayload,R=k.isAnimationActive,B=k.offset,L=k.position,W=k.reverseDirection,F=k.useTranslate3d,z=k.viewBox,U=k.wrapperStyle,$=(h=(e={allowEscapeViewBox:T,coordinate:N,offsetTopLeft:B,position:L,reverseDirection:W,tooltipBox:this.state.lastBoundingBox,useTranslate3d:F,viewBox:z}).allowEscapeViewBox,y=e.coordinate,v=e.offsetTopLeft,m=e.position,O=e.reverseDirection,j=e.tooltipBox,E=e.useTranslate3d,A=e.viewBox,j.height>0&&j.width>0&&y?(r=(t={translateX:p=w({allowEscapeViewBox:h,coordinate:y,key:"x",offsetTopLeft:v,position:m,reverseDirection:O,tooltipDimension:j.width,viewBox:A,viewBoxDimension:A.width}),translateY:d=w({allowEscapeViewBox:h,coordinate:y,key:"y",offsetTopLeft:v,position:m,reverseDirection:O,tooltipDimension:j.height,viewBox:A,viewBoxDimension:A.height}),useTranslate3d:E}).translateX,o=t.translateY,f={transform:t.useTranslate3d?"translate3d(".concat(r,"px, ").concat(o,"px, 0)"):"translate(".concat(r,"px, ").concat(o,"px)")}):f=x,{cssProperties:f,cssClasses:(a=(i={translateX:p,translateY:d,coordinate:y}).coordinate,u=i.translateX,s=i.translateY,(0,c.A)(g,b(b(b(b({},"".concat(g,"-right"),(0,l.Et)(u)&&a&&(0,l.Et)(a.x)&&u>=a.x),"".concat(g,"-left"),(0,l.Et)(u)&&a&&(0,l.Et)(a.x)&&u<a.x),"".concat(g,"-bottom"),(0,l.Et)(s)&&a&&(0,l.Et)(a.y)&&s>=a.y),"".concat(g,"-top"),(0,l.Et)(s)&&a&&(0,l.Et)(a.y)&&s<a.y)))}),Y=$.cssClasses,H=$.cssProperties,q=S(S({transition:R&&M?"transform ".concat(_,"ms ").concat(C):void 0},H),{},{pointerEvents:"none",visibility:!this.state.dismissed&&M&&I?"visible":"hidden",position:"absolute",top:0,left:0},U);return n.createElement("div",{tabIndex:-1,className:Y,style:q,ref:function(e){P.wrapperNode=e}},D)}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,M(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(n.PureComponent),_=r(41643),C=r(2494);function D(e){return(D="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function N(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function I(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?N(Object(r),!0).forEach(function(t){W(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):N(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function R(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(R=function(){return!!e})()}function B(e){return(B=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function L(e,t){return(L=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function W(e,t,r){return(t=F(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function F(e){var t=function(e,t){if("object"!=D(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=D(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==D(t)?t:t+""}function z(e){return e.dataKey}var U=function(e){var t;function r(){var e,t;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return e=r,t=arguments,e=B(e),function(e,t){if(t&&("object"===D(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");var r=e;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,R()?Reflect.construct(e,t||[],B(this).constructor):e.apply(this,t))}if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(e&&e.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),e&&L(r,e),t=[{key:"render",value:function(){var e,t=this,r=this.props,o=r.active,i=r.allowEscapeViewBox,a=r.animationDuration,u=r.animationEasing,c=r.content,l=r.coordinate,s=r.filterNull,f=r.isAnimationActive,p=r.offset,d=r.payload,h=r.payloadUniqBy,y=r.position,m=r.reverseDirection,b=r.useTranslate3d,g=r.viewBox,x=r.wrapperStyle,w=null!=d?d:[];s&&w.length&&(w=(0,C.s)(d.filter(function(e){return null!=e.value&&(!0!==e.hide||t.props.includeHidden)}),h,z));var O=w.length>0;return n.createElement(T,{allowEscapeViewBox:i,animationDuration:a,animationEasing:u,isAnimationActive:f,active:o,coordinate:l,hasPayload:O,offset:p,position:y,reverseDirection:m,useTranslate3d:b,viewBox:g,wrapperStyle:x},(e=I(I({},this.props),{},{payload:w}),n.isValidElement(c)?n.cloneElement(c,e):"function"==typeof c?n.createElement(c,e):n.createElement(v,e)))}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,F(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(n.PureComponent);W(U,"displayName","Tooltip"),W(U,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!_.m.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}})},94754:(e,t,r)=>{"use strict";r.d(t,{d:()=>M});var n=r(12115),o=r(40139),i=r.n(o),a=r(675),u=r(16377),c=r(70788),l=r(12814),s=r(36447),f=r(45167),p=r(50091),d=["x1","y1","x2","y2","key"],h=["offset"];function y(e){return(y="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function v(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function m(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?v(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=y(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=y(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==y(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):v(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function b(){return(b=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function g(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}var x=function(e){var t=e.fill;if(!t||"none"===t)return null;var r=e.fillOpacity,o=e.x,i=e.y,a=e.width,u=e.height,c=e.ry;return n.createElement("rect",{x:o,y:i,ry:c,width:a,height:u,stroke:"none",fill:t,fillOpacity:r,className:"recharts-cartesian-grid-bg"})};function w(e,t){var r;if(n.isValidElement(e))r=n.cloneElement(e,t);else if(i()(e))r=e(t);else{var o=t.x1,a=t.y1,u=t.x2,l=t.y2,s=t.key,f=g(t,d),p=(0,c.J9)(f,!1),y=(p.offset,g(p,h));r=n.createElement("line",b({},y,{x1:o,y1:a,x2:u,y2:l,fill:"none",key:s}))}return r}function O(e){var t=e.x,r=e.width,o=e.horizontal,i=void 0===o||o,a=e.horizontalPoints;if(!i||!a||!a.length)return null;var u=a.map(function(n,o){return w(i,m(m({},e),{},{x1:t,y1:n,x2:t+r,y2:n,key:"line-".concat(o),index:o}))});return n.createElement("g",{className:"recharts-cartesian-grid-horizontal"},u)}function j(e){var t=e.y,r=e.height,o=e.vertical,i=void 0===o||o,a=e.verticalPoints;if(!i||!a||!a.length)return null;var u=a.map(function(n,o){return w(i,m(m({},e),{},{x1:n,y1:t,x2:n,y2:t+r,key:"line-".concat(o),index:o}))});return n.createElement("g",{className:"recharts-cartesian-grid-vertical"},u)}function S(e){var t=e.horizontalFill,r=e.fillOpacity,o=e.x,i=e.y,a=e.width,u=e.height,c=e.horizontalPoints,l=e.horizontal;if(!(void 0===l||l)||!t||!t.length)return null;var s=c.map(function(e){return Math.round(e+i-i)}).sort(function(e,t){return e-t});i!==s[0]&&s.unshift(0);var f=s.map(function(e,c){var l=s[c+1]?s[c+1]-e:i+u-e;if(l<=0)return null;var f=c%t.length;return n.createElement("rect",{key:"react-".concat(c),y:e,x:o,height:l,width:a,stroke:"none",fill:t[f],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return n.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},f)}function E(e){var t=e.vertical,r=e.verticalFill,o=e.fillOpacity,i=e.x,a=e.y,u=e.width,c=e.height,l=e.verticalPoints;if(!(void 0===t||t)||!r||!r.length)return null;var s=l.map(function(e){return Math.round(e+i-i)}).sort(function(e,t){return e-t});i!==s[0]&&s.unshift(0);var f=s.map(function(e,t){var l=s[t+1]?s[t+1]-e:i+u-e;if(l<=0)return null;var f=t%r.length;return n.createElement("rect",{key:"react-".concat(t),x:e,y:a,width:l,height:c,stroke:"none",fill:r[f],fillOpacity:o,className:"recharts-cartesian-grid-bg"})});return n.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},f)}var A=function(e,t){var r=e.xAxis,n=e.width,o=e.height,i=e.offset;return(0,l.PW)((0,s.f)(m(m(m({},f.u.defaultProps),r),{},{ticks:(0,l.Rh)(r,!0),viewBox:{x:0,y:0,width:n,height:o}})),i.left,i.left+i.width,t)},P=function(e,t){var r=e.yAxis,n=e.width,o=e.height,i=e.offset;return(0,l.PW)((0,s.f)(m(m(m({},f.u.defaultProps),r),{},{ticks:(0,l.Rh)(r,!0),viewBox:{x:0,y:0,width:n,height:o}})),i.top,i.top+i.height,t)},k={horizontal:!0,vertical:!0,stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[]};function M(e){var t,r,o,c,l,s,f=(0,p.yi)(),d=(0,p.rY)(),h=(0,p.hj)(),v=m(m({},e),{},{stroke:null!=(t=e.stroke)?t:k.stroke,fill:null!=(r=e.fill)?r:k.fill,horizontal:null!=(o=e.horizontal)?o:k.horizontal,horizontalFill:null!=(c=e.horizontalFill)?c:k.horizontalFill,vertical:null!=(l=e.vertical)?l:k.vertical,verticalFill:null!=(s=e.verticalFill)?s:k.verticalFill,x:(0,u.Et)(e.x)?e.x:h.left,y:(0,u.Et)(e.y)?e.y:h.top,width:(0,u.Et)(e.width)?e.width:h.width,height:(0,u.Et)(e.height)?e.height:h.height}),g=v.x,w=v.y,M=v.width,T=v.height,_=v.syncWithTicks,C=v.horizontalValues,D=v.verticalValues,N=(0,p.pj)(),I=(0,p.$G)();if(!(0,u.Et)(M)||M<=0||!(0,u.Et)(T)||T<=0||!(0,u.Et)(g)||g!==+g||!(0,u.Et)(w)||w!==+w)return null;var R=v.verticalCoordinatesGenerator||A,B=v.horizontalCoordinatesGenerator||P,L=v.horizontalPoints,W=v.verticalPoints;if((!L||!L.length)&&i()(B)){var F=C&&C.length,z=B({yAxis:I?m(m({},I),{},{ticks:F?C:I.ticks}):void 0,width:f,height:d,offset:h},!!F||_);(0,a.R)(Array.isArray(z),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(y(z),"]")),Array.isArray(z)&&(L=z)}if((!W||!W.length)&&i()(R)){var U=D&&D.length,$=R({xAxis:N?m(m({},N),{},{ticks:U?D:N.ticks}):void 0,width:f,height:d,offset:h},!!U||_);(0,a.R)(Array.isArray($),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(y($),"]")),Array.isArray($)&&(W=$)}return n.createElement("g",{className:"recharts-cartesian-grid"},n.createElement(x,{fill:v.fill,fillOpacity:v.fillOpacity,x:v.x,y:v.y,width:v.width,height:v.height,ry:v.ry}),n.createElement(O,b({},v,{offset:h,horizontalPoints:L,xAxis:N,yAxis:I})),n.createElement(j,b({},v,{offset:h,verticalPoints:W,xAxis:N,yAxis:I})),n.createElement(S,b({},v,{horizontalPoints:L})),n.createElement(E,b({},v,{verticalPoints:W})))}M.displayName="CartesianGrid"},94999:(e,t,r)=>{var n=r(5658);e.exports=function(e){var t=n(this,e).delete(e);return this.size-=!!t,t}},95442:(e,t,r)=>{"use strict";r.d(t,{A:()=>function e(){var t=new n,r=[],o=[],i=u;function c(e){let n=t.get(e);if(void 0===n){if(i!==u)return i;t.set(e,n=r.push(e)-1)}return o[n%o.length]}return c.domain=function(e){if(!arguments.length)return r.slice();for(let o of(r=[],t=new n,e))t.has(o)||t.set(o,r.push(o)-1);return c},c.range=function(e){return arguments.length?(o=Array.from(e),c):o.slice()},c.unknown=function(e){return arguments.length?(i=e,c):i},c.copy=function(){return e(r,o).unknown(i)},a.C.apply(c,arguments),c},h:()=>u});class n extends Map{constructor(e,t=i){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:t}}),null!=e)for(let[t,r]of e)this.set(t,r)}get(e){return super.get(o(this,e))}has(e){return super.has(o(this,e))}set(e,t){return super.set(function({_intern:e,_key:t},r){let n=t(r);return e.has(n)?e.get(n):(e.set(n,r),r)}(this,e),t)}delete(e){return super.delete(function({_intern:e,_key:t},r){let n=t(r);return e.has(n)&&(r=e.get(n),e.delete(n)),r}(this,e))}}function o({_intern:e,_key:t},r){let n=t(r);return e.has(n)?e.get(n):r}function i(e){return null!==e&&"object"==typeof e?e.valueOf():e}var a=r(28749);let u=Symbol("implicit")},96025:(e,t,r)=>{"use strict";r.d(t,{W:()=>v});var n=r(12115),o=r(52596),i=r(50091),a=r(45167),u=r(12814);function c(e){return(c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function l(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(l=function(){return!!e})()}function s(e){return(s=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function f(e,t){return(f=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function p(e,t,r){return(t=d(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function d(e){var t=function(e,t){if("object"!=c(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=c(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==c(t)?t:t+""}function h(){return(h=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function y(e){var t=e.xAxisId,r=(0,i.yi)(),c=(0,i.rY)(),l=(0,i.AF)(t);return null==l?null:n.createElement(a.u,h({},l,{className:(0,o.A)("recharts-".concat(l.axisType," ").concat(l.axisType),l.className),viewBox:{x:0,y:0,width:r,height:c},ticksGenerator:function(e){return(0,u.Rh)(e,!0)}}))}var v=function(e){var t;function r(){var e,t;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return e=r,t=arguments,e=s(e),function(e,t){if(t&&("object"===c(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");var r=e;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,l()?Reflect.construct(e,t||[],s(this).constructor):e.apply(this,t))}if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(e&&e.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),e&&f(r,e),t=[{key:"render",value:function(){return n.createElement(y,this.props)}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,d(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(n.Component);p(v,"displayName","XAxis"),p(v,"defaultProps",{allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0})},96294:e=>{var t=Object.prototype;e.exports=function(e){var r=e&&e.constructor;return e===("function"==typeof r&&r.prototype||t)}},96540:(e,t,r)=>{var n=r(31545),o=r(26151),i=r(53696),a=r(13364),u=r(20988);function c(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}c.prototype.clear=n,c.prototype.delete=o,c.prototype.get=i,c.prototype.has=a,c.prototype.set=u,e.exports=c},96548:e=>{e.exports=function(e){return function(t){return null==t?void 0:t[e]}}},96699:(e,t,r)=>{var n=r(51911),o=r(48973),i=r(20134),a=r(79595),u=r(32197),c=r(92972),l=r(94356);e.exports=function(e,t){return a(e)&&u(t)?c(l(e),t):function(r){var a=o(r,e);return void 0===a&&a===t?i(r,e):n(t,a,3)}}},97124:(e,t,r)=>{e.exports=r(75031)(r(62464))},97972:(e,t,r)=>{"use strict";r.d(t,{h:()=>e8});var n,o,i,a,u,c={};r.r(c),r.d(c,{Button:()=>U,CaptionLabel:()=>$,Chevron:()=>Y,Day:()=>H,DayButton:()=>q,Dropdown:()=>X,DropdownNav:()=>Z,Footer:()=>V,Month:()=>G,MonthCaption:()=>K,MonthGrid:()=>J,Months:()=>Q,MonthsDropdown:()=>et,Nav:()=>er,NextMonthButton:()=>en,Option:()=>eo,PreviousMonthButton:()=>ei,Root:()=>ea,Select:()=>eu,Week:()=>ec,WeekNumber:()=>ef,WeekNumberHeader:()=>ep,Weekday:()=>el,Weekdays:()=>es,Weeks:()=>ed,YearsDropdown:()=>eh});var l={};r.r(l),r.d(l,{formatCaption:()=>ey,formatDay:()=>em,formatMonthCaption:()=>ev,formatMonthDropdown:()=>eb,formatWeekNumber:()=>eg,formatWeekNumberHeader:()=>ex,formatWeekdayName:()=>ew,formatYearCaption:()=>ej,formatYearDropdown:()=>eO});var s={};r.r(s),r.d(s,{labelCaption:()=>eE,labelDay:()=>ek,labelDayButton:()=>eP,labelGrid:()=>eS,labelGridcell:()=>eA,labelMonthDropdown:()=>eT,labelNav:()=>eM,labelNext:()=>e_,labelPrevious:()=>eC,labelWeekNumber:()=>eN,labelWeekNumberHeader:()=>eI,labelWeekday:()=>eD,labelYearDropdown:()=>eR});var f=r(12115);!function(e){e.Root="root",e.Chevron="chevron",e.Day="day",e.DayButton="day_button",e.CaptionLabel="caption_label",e.Dropdowns="dropdowns",e.Dropdown="dropdown",e.DropdownRoot="dropdown_root",e.Footer="footer",e.MonthGrid="month_grid",e.MonthCaption="month_caption",e.MonthsDropdown="months_dropdown",e.Month="month",e.Months="months",e.Nav="nav",e.NextMonthButton="button_next",e.PreviousMonthButton="button_previous",e.Week="week",e.Weeks="weeks",e.Weekday="weekday",e.Weekdays="weekdays",e.WeekNumber="week_number",e.WeekNumberHeader="week_number_header",e.YearsDropdown="years_dropdown"}(n||(n={})),function(e){e.disabled="disabled",e.hidden="hidden",e.outside="outside",e.focused="focused",e.today="today"}(o||(o={})),function(e){e.range_end="range_end",e.range_middle="range_middle",e.range_start="range_start",e.selected="selected"}(i||(i={})),function(e){e.weeks_before_enter="weeks_before_enter",e.weeks_before_exit="weeks_before_exit",e.weeks_after_enter="weeks_after_enter",e.weeks_after_exit="weeks_after_exit",e.caption_after_enter="caption_after_enter",e.caption_after_exit="caption_after_exit",e.caption_before_enter="caption_before_enter",e.caption_before_exit="caption_before_exit"}(a||(a={}));var p=r(8093);Symbol.for("constructDateFrom");let d={},h={};function y(e,t){try{let r=(d[e]||=new Intl.DateTimeFormat("en-GB",{timeZone:e,hour:"numeric",timeZoneName:"longOffset"}).format)(t).split("GMT")[1]||"";if(r in h)return h[r];return m(r,r.split(":"))}catch{if(e in h)return h[e];let t=e?.match(v);if(t)return m(e,t.slice(1));return NaN}}let v=/([+-]\d\d):?(\d\d)?/;function m(e,t){let r=+t[0],n=+(t[1]||0);return h[e]=r>0?60*r+n:60*r-n}class b extends Date{constructor(...e){super(),e.length>1&&"string"==typeof e[e.length-1]&&(this.timeZone=e.pop()),this.internal=new Date,isNaN(y(this.timeZone,this))?this.setTime(NaN):e.length?"number"==typeof e[0]&&(1===e.length||2===e.length&&"number"!=typeof e[1])?this.setTime(e[0]):"string"==typeof e[0]?this.setTime(+new Date(e[0])):e[0]instanceof Date?this.setTime(+e[0]):(this.setTime(+new Date(...e)),w(this,NaN),x(this)):this.setTime(Date.now())}static tz(e,...t){return t.length?new b(...t,e):new b(Date.now(),e)}withTimeZone(e){return new b(+this,e)}getTimezoneOffset(){return-y(this.timeZone,this)}setTime(e){return Date.prototype.setTime.apply(this,arguments),x(this),+this}[Symbol.for("constructDateFrom")](e){return new b(+new Date(e),this.timeZone)}}let g=/^(get|set)(?!UTC)/;function x(e){e.internal.setTime(+e),e.internal.setUTCMinutes(e.internal.getUTCMinutes()-e.getTimezoneOffset())}function w(e){let t=y(e.timeZone,e),r=new Date(+e);r.setUTCHours(r.getUTCHours()-1);let n=-new Date(+e).getTimezoneOffset(),o=n- -new Date(+r).getTimezoneOffset(),i=Date.prototype.getHours.apply(e)!==e.internal.getUTCHours();o&&i&&e.internal.setUTCMinutes(e.internal.getUTCMinutes()+o);let a=n-t;a&&Date.prototype.setUTCMinutes.call(e,Date.prototype.getUTCMinutes.call(e)+a);let u=y(e.timeZone,e),c=-new Date(+e).getTimezoneOffset()-u-a;if(u!==t&&c){Date.prototype.setUTCMinutes.call(e,Date.prototype.getUTCMinutes.call(e)+c);let t=u-y(e.timeZone,e);t&&(e.internal.setUTCMinutes(e.internal.getUTCMinutes()+t),Date.prototype.setUTCMinutes.call(e,Date.prototype.getUTCMinutes.call(e)+t))}}Object.getOwnPropertyNames(Date.prototype).forEach(e=>{if(!g.test(e))return;let t=e.replace(g,"$1UTC");b.prototype[t]&&(e.startsWith("get")?b.prototype[e]=function(){return this.internal[t]()}:(b.prototype[e]=function(){var e;return Date.prototype[t].apply(this.internal,arguments),e=this,Date.prototype.setFullYear.call(e,e.internal.getUTCFullYear(),e.internal.getUTCMonth(),e.internal.getUTCDate()),Date.prototype.setHours.call(e,e.internal.getUTCHours(),e.internal.getUTCMinutes(),e.internal.getUTCSeconds(),e.internal.getUTCMilliseconds()),w(e),+this},b.prototype[t]=function(){return Date.prototype[t].apply(this,arguments),x(this),+this}))});class O extends b{static tz(e,...t){return t.length?new O(...t,e):new O(Date.now(),e)}toISOString(){let[e,t,r]=this.tzComponents(),n=`${e}${t}:${r}`;return this.internal.toISOString().slice(0,-1)+n}toString(){return`${this.toDateString()} ${this.toTimeString()}`}toDateString(){let[e,t,r,n]=this.internal.toUTCString().split(" ");return`${e?.slice(0,-1)} ${r} ${t} ${n}`}toTimeString(){var e,t;let r=this.internal.toUTCString().split(" ")[4],[n,o,i]=this.tzComponents();return`${r} GMT${n}${o}${i} (${e=this.timeZone,t=this,new Intl.DateTimeFormat("en-GB",{timeZone:e,timeZoneName:"long"}).format(t).slice(12)})`}toLocaleString(e,t){return Date.prototype.toLocaleString.call(this,e,{...t,timeZone:t?.timeZone||this.timeZone})}toLocaleDateString(e,t){return Date.prototype.toLocaleDateString.call(this,e,{...t,timeZone:t?.timeZone||this.timeZone})}toLocaleTimeString(e,t){return Date.prototype.toLocaleTimeString.call(this,e,{...t,timeZone:t?.timeZone||this.timeZone})}tzComponents(){let e=this.getTimezoneOffset(),t=String(Math.floor(Math.abs(e)/60)).padStart(2,"0"),r=String(Math.abs(e)%60).padStart(2,"0");return[e>0?"-":"+",t,r]}withTimeZone(e){return new O(+this,e)}[Symbol.for("constructDateFrom")](e){return new O(+new Date(e),this.timeZone)}}var j=r(7239),S=r(89447);function E(e,t,r){let n=(0,S.a)(e,null==r?void 0:r.in);return isNaN(t)?(0,j.w)((null==r?void 0:r.in)||e,NaN):(t&&n.setDate(n.getDate()+t),n)}function A(e,t,r){let n=(0,S.a)(e,null==r?void 0:r.in);if(isNaN(t))return(0,j.w)((null==r?void 0:r.in)||e,NaN);if(!t)return n;let o=n.getDate(),i=(0,j.w)((null==r?void 0:r.in)||e,n.getTime());return(i.setMonth(n.getMonth()+t+1,0),o>=i.getDate())?i:(n.setFullYear(i.getFullYear(),i.getMonth(),o),n)}var P=r(48637),k=r(61183),M=r(95490);function T(e,t){var r,n,o,i,a,u,c,l;let s=(0,M.q)(),f=null!=(l=null!=(c=null!=(u=null!=(a=null==t?void 0:t.weekStartsOn)?a:null==t||null==(n=t.locale)||null==(r=n.options)?void 0:r.weekStartsOn)?u:s.weekStartsOn)?c:null==(i=s.locale)||null==(o=i.options)?void 0:o.weekStartsOn)?l:0,p=(0,S.a)(e,null==t?void 0:t.in),d=p.getDay();return p.setDate(p.getDate()+((d<f?-7:0)+6-(d-f))),p.setHours(23,59,59,999),p}var _=r(63008),C=r(17519),D=r(21391),N=r(99026),I=r(6711),R=r(70540),B=r(84423),L=r(67386);function W(e,t){let r=t.startOfMonth(e),n=r.getDay();return 1===n?r:0===n?t.addDays(r,-6):t.addDays(r,-1*(n-1))}class F{constructor(e,t){this.Date=Date,this.today=()=>this.overrides?.today?this.overrides.today():this.options.timeZone?O.tz(this.options.timeZone):new this.Date,this.newDate=(e,t,r)=>this.overrides?.newDate?this.overrides.newDate(e,t,r):this.options.timeZone?new O(e,t,r,this.options.timeZone):new Date(e,t,r),this.addDays=(e,t)=>this.overrides?.addDays?this.overrides.addDays(e,t):E(e,t),this.addMonths=(e,t)=>this.overrides?.addMonths?this.overrides.addMonths(e,t):A(e,t),this.addWeeks=(e,t)=>this.overrides?.addWeeks?this.overrides.addWeeks(e,t):E(e,7*t,void 0),this.addYears=(e,t)=>this.overrides?.addYears?this.overrides.addYears(e,t):A(e,12*t,void 0),this.differenceInCalendarDays=(e,t)=>this.overrides?.differenceInCalendarDays?this.overrides.differenceInCalendarDays(e,t):(0,P.m)(e,t),this.differenceInCalendarMonths=(e,t)=>this.overrides?.differenceInCalendarMonths?this.overrides.differenceInCalendarMonths(e,t):function(e,t,r){let[n,o]=(0,k.x)(void 0,e,t);return 12*(n.getFullYear()-o.getFullYear())+(n.getMonth()-o.getMonth())}(e,t),this.eachMonthOfInterval=e=>this.overrides?.eachMonthOfInterval?this.overrides.eachMonthOfInterval(e):function(e,t){var r;let{start:n,end:o}=function(e,t){let[r,n]=(0,k.x)(e,t.start,t.end);return{start:r,end:n}}(void 0,e),i=+n>+o,a=i?+n:+o,u=i?o:n;u.setHours(0,0,0,0),u.setDate(1);let c=(r=void 0,1);if(!c)return[];c<0&&(c=-c,i=!i);let l=[];for(;+u<=a;)l.push((0,j.w)(n,u)),u.setMonth(u.getMonth()+c);return i?l.reverse():l}(e),this.endOfBroadcastWeek=e=>this.overrides?.endOfBroadcastWeek?this.overrides.endOfBroadcastWeek(e,this):function(e,t){let r=W(e,t),n=function(e,t){let r=t.startOfMonth(e),n=r.getDay()>0?r.getDay():7,o=t.addDays(e,-n+1),i=t.addDays(o,34);return t.getMonth(e)===t.getMonth(i)?5:4}(e,t);return t.addDays(r,7*n-1)}(e,this),this.endOfISOWeek=e=>this.overrides?.endOfISOWeek?this.overrides.endOfISOWeek(e):T(e,{...void 0,weekStartsOn:1}),this.endOfMonth=e=>this.overrides?.endOfMonth?this.overrides.endOfMonth(e):function(e,t){let r=(0,S.a)(e,void 0),n=r.getMonth();return r.setFullYear(r.getFullYear(),n+1,0),r.setHours(23,59,59,999),r}(e),this.endOfWeek=e=>this.overrides?.endOfWeek?this.overrides.endOfWeek(e,this.options):T(e,this.options),this.endOfYear=e=>this.overrides?.endOfYear?this.overrides.endOfYear(e):function(e,t){let r=(0,S.a)(e,void 0),n=r.getFullYear();return r.setFullYear(n+1,0,0),r.setHours(23,59,59,999),r}(e),this.format=(e,t)=>{let r=this.overrides?.format?this.overrides.format(e,t,this.options):(0,_.GP)(e,t,this.options);return this.options.numerals&&"latn"!==this.options.numerals?this.replaceDigits(r):r},this.getISOWeek=e=>this.overrides?.getISOWeek?this.overrides.getISOWeek(e):(0,C.s)(e),this.getMonth=e=>this.overrides?.getMonth?this.overrides.getMonth(e,this.options):function(e,t){return(0,S.a)(e,null==t?void 0:t.in).getMonth()}(e,this.options),this.getYear=e=>this.overrides?.getYear?this.overrides.getYear(e,this.options):function(e,t){return(0,S.a)(e,null==t?void 0:t.in).getFullYear()}(e,this.options),this.getWeek=e=>this.overrides?.getWeek?this.overrides.getWeek(e,this.options):(0,D.N)(e,this.options),this.isAfter=(e,t)=>this.overrides?.isAfter?this.overrides.isAfter(e,t):+(0,S.a)(e)>+(0,S.a)(t),this.isBefore=(e,t)=>this.overrides?.isBefore?this.overrides.isBefore(e,t):+(0,S.a)(e)<+(0,S.a)(t),this.isDate=e=>this.overrides?.isDate?this.overrides.isDate(e):(0,N.$)(e),this.isSameDay=(e,t)=>this.overrides?.isSameDay?this.overrides.isSameDay(e,t):function(e,t,r){let[n,o]=(0,k.x)(void 0,e,t);return+(0,I.o)(n)==+(0,I.o)(o)}(e,t),this.isSameMonth=(e,t)=>this.overrides?.isSameMonth?this.overrides.isSameMonth(e,t):function(e,t,r){let[n,o]=(0,k.x)(void 0,e,t);return n.getFullYear()===o.getFullYear()&&n.getMonth()===o.getMonth()}(e,t),this.isSameYear=(e,t)=>this.overrides?.isSameYear?this.overrides.isSameYear(e,t):function(e,t,r){let[n,o]=(0,k.x)(void 0,e,t);return n.getFullYear()===o.getFullYear()}(e,t),this.max=e=>this.overrides?.max?this.overrides.max(e):function(e,t){let r,n;return e.forEach(e=>{n||"object"!=typeof e||(n=j.w.bind(null,e));let t=(0,S.a)(e,n);(!r||r<t||isNaN(+t))&&(r=t)}),(0,j.w)(n,r||NaN)}(e),this.min=e=>this.overrides?.min?this.overrides.min(e):function(e,t){let r,n;return e.forEach(e=>{n||"object"!=typeof e||(n=j.w.bind(null,e));let t=(0,S.a)(e,n);(!r||r>t||isNaN(+t))&&(r=t)}),(0,j.w)(n,r||NaN)}(e),this.setMonth=(e,t)=>this.overrides?.setMonth?this.overrides.setMonth(e,t):function(e,t,r){let n=(0,S.a)(e,void 0),o=n.getFullYear(),i=n.getDate(),a=(0,j.w)(e,0);a.setFullYear(o,t,15),a.setHours(0,0,0,0);let u=function(e,t){let r=(0,S.a)(e,void 0),n=r.getFullYear(),o=r.getMonth(),i=(0,j.w)(r,0);return i.setFullYear(n,o+1,0),i.setHours(0,0,0,0),i.getDate()}(a);return n.setMonth(t,Math.min(i,u)),n}(e,t),this.setYear=(e,t)=>this.overrides?.setYear?this.overrides.setYear(e,t):function(e,t,r){let n=(0,S.a)(e,void 0);return isNaN(+n)?(0,j.w)(e,NaN):(n.setFullYear(t),n)}(e,t),this.startOfBroadcastWeek=e=>this.overrides?.startOfBroadcastWeek?this.overrides.startOfBroadcastWeek(e,this):W(e,this),this.startOfDay=e=>this.overrides?.startOfDay?this.overrides.startOfDay(e):(0,I.o)(e),this.startOfISOWeek=e=>this.overrides?.startOfISOWeek?this.overrides.startOfISOWeek(e):(0,R.b)(e),this.startOfMonth=e=>this.overrides?.startOfMonth?this.overrides.startOfMonth(e):function(e,t){let r=(0,S.a)(e,void 0);return r.setDate(1),r.setHours(0,0,0,0),r}(e),this.startOfWeek=e=>this.overrides?.startOfWeek?this.overrides.startOfWeek(e,this.options):(0,B.k)(e,this.options),this.startOfYear=e=>this.overrides?.startOfYear?this.overrides.startOfYear(e):(0,L.D)(e),this.options={locale:p.c,...e},this.overrides=t}getDigitMap(){let{numerals:e="latn"}=this.options,t=new Intl.NumberFormat("en-US",{numberingSystem:e}),r={};for(let e=0;e<10;e++)r[e.toString()]=t.format(e);return r}replaceDigits(e){let t=this.getDigitMap();return e.replace(/\d/g,e=>t[e]||e)}formatNumber(e){return this.replaceDigits(e.toString())}}let z=new F;function U(e){return f.createElement("button",{...e})}function $(e){return f.createElement("span",{...e})}function Y(e){let{size:t=24,orientation:r="left",className:n}=e;return f.createElement("svg",{className:n,width:t,height:t,viewBox:"0 0 24 24"},"up"===r&&f.createElement("polygon",{points:"6.77 17 12.5 11.43 18.24 17 20 15.28 12.5 8 5 15.28"}),"down"===r&&f.createElement("polygon",{points:"6.77 8 12.5 13.57 18.24 8 20 9.72 12.5 17 5 9.72"}),"left"===r&&f.createElement("polygon",{points:"16 18.112 9.81111111 12 16 5.87733333 14.0888889 4 6 12 14.0888889 20"}),"right"===r&&f.createElement("polygon",{points:"8 18.112 14.18888889 12 8 5.87733333 9.91111111 4 18 12 9.91111111 20"}))}function H(e){let{day:t,modifiers:r,...n}=e;return f.createElement("td",{...n})}function q(e){let{day:t,modifiers:r,...n}=e,o=f.useRef(null);return f.useEffect(()=>{r.focused&&o.current?.focus()},[r.focused]),f.createElement("button",{ref:o,...n})}function X(e){let{options:t,className:r,components:o,classNames:i,...a}=e,u=[i[n.Dropdown],r].join(" "),c=t?.find(({value:e})=>e===a.value);return f.createElement("span",{"data-disabled":a.disabled,className:i[n.DropdownRoot]},f.createElement(o.Select,{className:u,...a},t?.map(({value:e,label:t,disabled:r})=>f.createElement(o.Option,{key:e,value:e,disabled:r},t))),f.createElement("span",{className:i[n.CaptionLabel],"aria-hidden":!0},c?.label,f.createElement(o.Chevron,{orientation:"down",size:18,className:i[n.Chevron]})))}function Z(e){return f.createElement("div",{...e})}function V(e){return f.createElement("div",{...e})}function G(e){let{calendarMonth:t,displayIndex:r,...n}=e;return f.createElement("div",{...n},e.children)}function K(e){let{calendarMonth:t,displayIndex:r,...n}=e;return f.createElement("div",{...n})}function J(e){return f.createElement("table",{...e})}function Q(e){return f.createElement("div",{...e})}var ee=r(59355);function et(e){let{components:t}=(0,ee.w)();return f.createElement(t.Dropdown,{...e})}function er(e){let{onPreviousClick:t,onNextClick:r,previousMonth:o,nextMonth:i,...a}=e,{components:u,classNames:c,labels:{labelPrevious:l,labelNext:s}}=(0,ee.w)(),p=(0,f.useCallback)(e=>{i&&r?.(e)},[i,r]),d=(0,f.useCallback)(e=>{o&&t?.(e)},[o,t]);return f.createElement("nav",{...a},f.createElement(u.PreviousMonthButton,{type:"button",className:c[n.PreviousMonthButton],tabIndex:o?void 0:-1,"aria-disabled":!o||void 0,"aria-label":l(o),onClick:d},f.createElement(u.Chevron,{disabled:!o||void 0,className:c[n.Chevron],orientation:"left"})),f.createElement(u.NextMonthButton,{type:"button",className:c[n.NextMonthButton],tabIndex:i?void 0:-1,"aria-disabled":!i||void 0,"aria-label":s(i),onClick:p},f.createElement(u.Chevron,{disabled:!i||void 0,orientation:"right",className:c[n.Chevron]})))}function en(e){let{components:t}=(0,ee.w)();return f.createElement(t.Button,{...e})}function eo(e){return f.createElement("option",{...e})}function ei(e){let{components:t}=(0,ee.w)();return f.createElement(t.Button,{...e})}function ea(e){let{rootRef:t,...r}=e;return f.createElement("div",{...r,ref:t})}function eu(e){return f.createElement("select",{...e})}function ec(e){let{week:t,...r}=e;return f.createElement("tr",{...r})}function el(e){return f.createElement("th",{...e})}function es(e){return f.createElement("thead",{"aria-hidden":!0},f.createElement("tr",{...e}))}function ef(e){let{week:t,...r}=e;return f.createElement("th",{...r})}function ep(e){return f.createElement("th",{...e})}function ed(e){return f.createElement("tbody",{...e})}function eh(e){let{components:t}=(0,ee.w)();return f.createElement(t.Dropdown,{...e})}function ey(e,t,r){return(r??new F(t)).format(e,"LLLL y")}let ev=ey;function em(e,t,r){return(r??new F(t)).format(e,"d")}function eb(e,t=z){return t.format(e,"LLLL")}function eg(e){return e<10?`0${e.toLocaleString()}`:`${e.toLocaleString()}`}function ex(){return""}function ew(e,t,r){return(r??new F(t)).format(e,"cccccc")}function eO(e,t=z){return t.format(e,"yyyy")}let ej=eO;function eS(e,t,r){return(r??new F(t)).format(e,"LLLL y")}let eE=eS;function eA(e,t,r,n){let o=(n??new F(r)).format(e,"PPPP");return t?.today&&(o=`Today, ${o}`),o}function eP(e,t,r,n){let o=(n??new F(r)).format(e,"PPPP");return t.today&&(o=`Today, ${o}`),t.selected&&(o=`${o}, selected`),o}let ek=eP;function eM(){return""}function eT(e){return"Choose the Month"}function e_(e){return"Go to the Next Month"}function eC(e){return"Go to the Previous Month"}function eD(e,t,r){return(r??new F(t)).format(e,"cccc")}function eN(e,t){return`Week ${e}`}function eI(e){return"Week Number"}function eR(e){return"Choose the Year"}let eB=e=>e instanceof HTMLElement?e:null,eL=e=>[...e.querySelectorAll("[data-animated-month]")??[]],eW=e=>eB(e.querySelector("[data-animated-month]")),eF=e=>eB(e.querySelector("[data-animated-caption]")),ez=e=>eB(e.querySelector("[data-animated-weeks]")),eU=e=>eB(e.querySelector("[data-animated-nav]")),e$=e=>eB(e.querySelector("[data-animated-weekdays]"));function eY(e,t){let{month:r,defaultMonth:n,today:o=t.today(),numberOfMonths:i=1,endMonth:a,startMonth:u,timeZone:c}=e,l=r||n||o,{differenceInCalendarMonths:s,addMonths:f,startOfMonth:p}=t;return a&&0>s(a,l)&&(l=f(a,-1*(i-1))),u&&0>s(l,u)&&(l=u),p(l=c?new O(l,c):l)}class eH{constructor(e,t,r=z){this.date=e,this.displayMonth=t,this.outside=!!(t&&!r.isSameMonth(e,t)),this.dateLib=r}isEqualTo(e){return this.dateLib.isSameDay(e.date,this.date)&&this.dateLib.isSameMonth(e.displayMonth,this.displayMonth)}}class eq{constructor(e,t){this.days=t,this.weekNumber=e}}class eX{constructor(e,t){this.date=e,this.weeks=t}}function eZ(e,t){let[r,n]=(0,f.useState)(e);return[void 0===t?r:t,n]}function eV(e){return!e[o.disabled]&&!e[o.hidden]&&!e[o.outside]}function eG(e,t,r=!1,n=z){let{from:o,to:i}=e,{differenceInCalendarDays:a,isSameDay:u}=n;return o&&i?(0>a(i,o)&&([o,i]=[i,o]),a(t,o)>=+!!r&&a(i,t)>=+!!r):!r&&i?u(i,t):!r&&!!o&&u(o,t)}function eK(e){return!!(e&&"object"==typeof e&&"before"in e&&"after"in e)}function eJ(e){return!!(e&&"object"==typeof e&&"from"in e)}function eQ(e){return!!(e&&"object"==typeof e&&"after"in e)}function e0(e){return!!(e&&"object"==typeof e&&"before"in e)}function e1(e){return!!(e&&"object"==typeof e&&"dayOfWeek"in e)}function e2(e,t){return Array.isArray(e)&&e.every(t.isDate)}function e5(e,t,r=z){let n=Array.isArray(t)?t:[t],{isSameDay:o,differenceInCalendarDays:i,isAfter:a}=r;return n.some(t=>{if("boolean"==typeof t)return t;if(r.isDate(t))return o(e,t);if(e2(t,r))return t.includes(e);if(eJ(t))return eG(t,e,!1,r);if(e1(t))return Array.isArray(t.dayOfWeek)?t.dayOfWeek.includes(e.getDay()):t.dayOfWeek===e.getDay();if(eK(t)){let r=i(t.before,e),n=i(t.after,e),o=r>0,u=n<0;return a(t.before,t.after)?u&&o:o||u}return eQ(t)?i(e,t.after)>0:e0(t)?i(t.before,e)>0:"function"==typeof t&&t(e)})}function e3(e,t,r=z){return eG(e,t.from,!1,r)||eG(e,t.to,!1,r)||eG(t,e.from,!1,r)||eG(t,e.to,!1,r)}function e8(e){let{components:t,formatters:r,labels:d,dateLib:h,locale:y,classNames:v}=(0,f.useMemo)(()=>{var t,r;let u={...p.c,...e.locale};return{dateLib:new F({locale:u,weekStartsOn:e.broadcastCalendar?1:e.weekStartsOn,firstWeekContainsDate:e.firstWeekContainsDate,useAdditionalWeekYearTokens:e.useAdditionalWeekYearTokens,useAdditionalDayOfYearTokens:e.useAdditionalDayOfYearTokens,timeZone:e.timeZone,numerals:e.numerals},e.dateLib),components:(t=e.components,{...c,...t}),formatters:(r=e.formatters,r?.formatMonthCaption&&!r.formatCaption&&(r.formatCaption=r.formatMonthCaption),r?.formatYearCaption&&!r.formatYearDropdown&&(r.formatYearDropdown=r.formatYearCaption),{...l,...r}),labels:{...s,...e.labels},locale:u,classNames:{...function(){let e={};for(let t in n)e[n[t]]=`rdp-${n[t]}`;for(let t in o)e[o[t]]=`rdp-${o[t]}`;for(let t in i)e[i[t]]=`rdp-${i[t]}`;for(let t in a)e[a[t]]=`rdp-${a[t]}`;return e}(),...e.classNames}}},[e.locale,e.broadcastCalendar,e.weekStartsOn,e.firstWeekContainsDate,e.useAdditionalWeekYearTokens,e.useAdditionalDayOfYearTokens,e.timeZone,e.numerals,e.dateLib,e.components,e.formatters,e.labels,e.classNames]),{captionLayout:m,mode:b,onDayBlur:g,onDayClick:x,onDayFocus:w,onDayKeyDown:O,onDayMouseEnter:j,onDayMouseLeave:S,onNextClick:E,onPrevClick:A,showWeekNumber:P,styles:k}=e,{formatCaption:M,formatDay:T,formatMonthDropdown:_,formatWeekNumber:C,formatWeekNumberHeader:D,formatWeekdayName:N,formatYearDropdown:I}=r,R=function(e,t){let[r,n]=function(e,t){let{startMonth:r,endMonth:n}=e,{startOfYear:o,startOfDay:i,startOfMonth:a,endOfMonth:u,addYears:c,endOfYear:l,newDate:s,today:f}=t,{fromYear:p,toYear:d,fromMonth:h,toMonth:y}=e;!r&&h&&(r=h),!r&&p&&(r=t.newDate(p,0,1)),!n&&y&&(n=y),!n&&d&&(n=s(d,11,31));let v="dropdown"===e.captionLayout||"dropdown-years"===e.captionLayout;return r?r=a(r):p?r=s(p,0,1):!r&&v&&(r=o(c(e.today??f(),-100))),n?n=u(n):d?n=s(d,11,31):!n&&v&&(n=l(e.today??f())),[r?i(r):r,n?i(n):n]}(e,t),{startOfMonth:o,endOfMonth:i}=t,a=eY(e,t),[u,c]=eZ(a,e.month?a:void 0);(0,f.useEffect)(()=>{c(eY(e,t))},[e.timeZone]);let l=function(e,t,r,n){let{numberOfMonths:o=1}=r,i=[];for(let r=0;r<o;r++){let o=n.addMonths(e,r);if(t&&o>t)break;i.push(o)}return i}(u,n,e,t),s=function(e,t,r,n){let o=e[0],i=e[e.length-1],{ISOWeek:a,fixedWeeks:u,broadcastCalendar:c}=r??{},{addDays:l,differenceInCalendarDays:s,differenceInCalendarMonths:f,endOfBroadcastWeek:p,endOfISOWeek:d,endOfMonth:h,endOfWeek:y,isAfter:v,startOfBroadcastWeek:m,startOfISOWeek:b,startOfWeek:g}=n,x=c?m(o,n):a?b(o):g(o),w=s(c?p(i,n):a?d(h(i)):y(h(i)),x),O=f(i,o)+1,j=[];for(let e=0;e<=w;e++){let r=l(x,e);if(t&&v(r,t))break;j.push(r)}let S=(c?35:42)*O;if(u&&j.length<S){let e=S-j.length;for(let t=0;t<e;t++){let e=l(j[j.length-1],1);j.push(e)}}return j}(l,e.endMonth?i(e.endMonth):void 0,e,t),p=function(e,t,r,n){let{addDays:o,endOfBroadcastWeek:i,endOfISOWeek:a,endOfMonth:u,endOfWeek:c,getISOWeek:l,getWeek:s,startOfBroadcastWeek:f,startOfISOWeek:p,startOfWeek:d}=n,h=e.reduce((e,h)=>{let y=r.broadcastCalendar?f(h,n):r.ISOWeek?p(h):d(h),v=r.broadcastCalendar?i(h,n):r.ISOWeek?a(u(h)):c(u(h)),m=t.filter(e=>e>=y&&e<=v),b=r.broadcastCalendar?35:42;if(r.fixedWeeks&&m.length<b){let e=t.filter(e=>{let t=b-m.length;return e>v&&e<=o(v,t)});m.push(...e)}let g=m.reduce((e,t)=>{let o=r.ISOWeek?l(t):s(t),i=e.find(e=>e.weekNumber===o),a=new eH(t,h,n);return i?i.days.push(a):e.push(new eq(o,[a])),e},[]),x=new eX(h,g);return e.push(x),e},[]);return r.reverseMonths?h.reverse():h}(l,s,e,t),d=p.reduce((e,t)=>[...e,...t.weeks],[]),h=p.reduce((e,t)=>[...e,...t.weeks.reduce((e,t)=>[...e,...t.days],[])],[]),y=function(e,t,r,n){if(r.disableNavigation)return;let{pagedNavigation:o,numberOfMonths:i}=r,{startOfMonth:a,addMonths:u,differenceInCalendarMonths:c}=n,l=a(e);if(!t||!(0>=c(l,t)))return u(l,-(o?i??1:1))}(u,r,e,t),v=function(e,t,r,n){if(r.disableNavigation)return;let{pagedNavigation:o,numberOfMonths:i=1}=r,{startOfMonth:a,addMonths:u,differenceInCalendarMonths:c}=n,l=a(e);if(!t||!(c(t,e)<i))return u(l,o?i:1)}(u,n,e,t),{disableNavigation:m,onMonthChange:b}=e,g=e=>d.some(t=>t.days.some(t=>t.isEqualTo(e))),x=e=>{if(m)return;let t=o(e);r&&t<o(r)&&(t=o(r)),n&&t>o(n)&&(t=o(n)),c(t),b?.(t)};return{months:p,weeks:d,days:h,navStart:r,navEnd:n,previousMonth:y,nextMonth:v,goToMonth:x,goToDay:e=>{g(e)||x(e.date)}}}(e,h),{days:B,months:L,navStart:W,navEnd:U,previousMonth:$,nextMonth:Y,goToMonth:H}=R,q=function(e,t,r){let{disabled:n,hidden:i,modifiers:a,showOutsideDays:u,broadcastCalendar:c,today:l}=t,{isSameDay:s,isSameMonth:f,startOfMonth:p,isBefore:d,endOfMonth:h,isAfter:y}=r,v=t.startMonth&&p(t.startMonth),m=t.endMonth&&h(t.endMonth),b={[o.focused]:[],[o.outside]:[],[o.disabled]:[],[o.hidden]:[],[o.today]:[]},g={};for(let t of e){let{date:e,displayMonth:o}=t,p=!!(o&&!f(e,o)),h=!!(v&&d(e,v)),x=!!(m&&y(e,m)),w=!!(n&&e5(e,n,r)),O=!!(i&&e5(e,i,r))||h||x||!c&&!u&&p||c&&!1===u&&p,j=s(e,l??r.today());p&&b.outside.push(t),w&&b.disabled.push(t),O&&b.hidden.push(t),j&&b.today.push(t),a&&Object.keys(a).forEach(n=>{let o=a?.[n];o&&e5(e,o,r)&&(g[n]?g[n].push(t):g[n]=[t])})}return e=>{let t={[o.focused]:!1,[o.disabled]:!1,[o.hidden]:!1,[o.outside]:!1,[o.today]:!1},r={};for(let r in b){let n=b[r];t[r]=n.some(t=>t===e)}for(let t in g)r[t]=g[t].some(t=>t===e);return{...t,...r}}}(B,e,h),{isSelected:X,select:Z,selected:V}=function(e,t){let r=function(e,t){let{selected:r,required:n,onSelect:o}=e,[i,a]=eZ(r,o?r:void 0),u=o?r:i,{isSameDay:c}=t;return{selected:u,select:(e,t,r)=>{let i=e;return!n&&u&&u&&c(e,u)&&(i=void 0),o||a(i),o?.(i,e,t,r),i},isSelected:e=>!!u&&c(u,e)}}(e,t),n=function(e,t){let{selected:r,required:n,onSelect:o}=e,[i,a]=eZ(r,o?r:void 0),u=o?r:i,{isSameDay:c}=t,l=e=>u?.some(t=>c(t,e))??!1,{min:s,max:f}=e;return{selected:u,select:(e,t,r)=>{let i=[...u??[]];if(l(e)){if(u?.length===s||n&&u?.length===1)return;i=u?.filter(t=>!c(t,e))}else i=u?.length===f?[e]:[...i,e];return o||a(i),o?.(i,e,t,r),i},isSelected:l}}(e,t),o=function(e,t){let{disabled:r,excludeDisabled:n,selected:o,required:i,onSelect:a}=e,[u,c]=eZ(o,a?o:void 0),l=a?o:u;return{selected:l,select:(o,u,s)=>{let{min:f,max:p}=e,d=o?function(e,t,r=0,n=0,o=!1,i=z){let a,{from:u,to:c}=t||{},{isSameDay:l,isAfter:s,isBefore:f}=i;if(u||c){if(u&&!c)a=l(u,e)?o?{from:u,to:void 0}:void 0:f(e,u)?{from:e,to:u}:{from:u,to:e};else if(u&&c)if(l(u,e)&&l(c,e))a=o?{from:u,to:c}:void 0;else if(l(u,e))a={from:u,to:r>0?void 0:e};else if(l(c,e))a={from:e,to:r>0?void 0:e};else if(f(e,u))a={from:e,to:c};else if(s(e,u))a={from:u,to:e};else if(s(e,c))a={from:u,to:e};else throw Error("Invalid range")}else a={from:e,to:r>0?void 0:e};if(a?.from&&a?.to){let t=i.differenceInCalendarDays(a.to,a.from);n>0&&t>n?a={from:e,to:void 0}:r>1&&t<r&&(a={from:e,to:void 0})}return a}(o,l,f,p,i,t):void 0;return n&&r&&d?.from&&d.to&&function(e,t,r=z){let n=Array.isArray(t)?t:[t];if(n.filter(e=>"function"!=typeof e).some(t=>"boolean"==typeof t?t:r.isDate(t)?eG(e,t,!1,r):e2(t,r)?t.some(t=>eG(e,t,!1,r)):eJ(t)?!!t.from&&!!t.to&&e3(e,{from:t.from,to:t.to},r):e1(t)?function(e,t,r=z){let n=Array.isArray(t)?t:[t],o=e.from,i=Math.min(r.differenceInCalendarDays(e.to,e.from),6);for(let e=0;e<=i;e++){if(n.includes(o.getDay()))return!0;o=r.addDays(o,1)}return!1}(e,t.dayOfWeek,r):eK(t)?r.isAfter(t.before,t.after)?e3(e,{from:r.addDays(t.after,1),to:r.addDays(t.before,-1)},r):e5(e.from,t,r)||e5(e.to,t,r):!!(eQ(t)||e0(t))&&(e5(e.from,t,r)||e5(e.to,t,r))))return!0;let o=n.filter(e=>"function"==typeof e);if(o.length){let t=e.from,n=r.differenceInCalendarDays(e.to,e.from);for(let e=0;e<=n;e++){if(o.some(e=>e(t)))return!0;t=r.addDays(t,1)}}return!1}({from:d.from,to:d.to},r,t)&&(d.from=o,d.to=void 0),a||c(d),a?.(d,o,u,s),d},isSelected:e=>l&&eG(l,e,!1,t)}}(e,t);switch(e.mode){case"single":return r;case"multiple":return n;case"range":return o;default:return}}(e,h)??{},{blur:G,focused:K,isFocusTarget:J,moveFocus:Q,setFocused:et}=function(e,t,r,n,i){let{autoFocus:a}=e,[c,l]=(0,f.useState)(),s=function(e,t,r,n){let i,a=-1;for(let c of e){let e=t(c);eV(e)&&(e[o.focused]&&a<u.FocusedModifier?(i=c,a=u.FocusedModifier):n?.isEqualTo(c)&&a<u.LastFocused?(i=c,a=u.LastFocused):r(c.date)&&a<u.Selected?(i=c,a=u.Selected):e[o.today]&&a<u.Today&&(i=c,a=u.Today))}return i||(i=e.find(e=>eV(t(e)))),i}(t.days,r,n||(()=>!1),c),[p,d]=(0,f.useState)(a?s:void 0);return{isFocusTarget:e=>!!s?.isEqualTo(e),setFocused:d,focused:p,blur:()=>{l(p),d(void 0)},moveFocus:(r,n)=>{if(!p)return;let o=function e(t,r,n,o,i,a,u,c=0){if(c>365)return;let l=function(e,t,r,n,o,i,a){let{ISOWeek:u,broadcastCalendar:c}=i,{addDays:l,addMonths:s,addWeeks:f,addYears:p,endOfBroadcastWeek:d,endOfISOWeek:h,endOfWeek:y,max:v,min:m,startOfBroadcastWeek:b,startOfISOWeek:g,startOfWeek:x}=a,w=({day:l,week:f,month:s,year:p,startOfWeek:e=>c?b(e,a):u?g(e):x(e),endOfWeek:e=>c?d(e,a):u?h(e):y(e)})[e](r,"after"===t?1:-1);return"before"===t&&n?w=v([n,w]):"after"===t&&o&&(w=m([o,w])),w}(t,r,n.date,o,i,a,u),s=!!(a.disabled&&e5(l,a.disabled,u)),f=!!(a.hidden&&e5(l,a.hidden,u)),p=new eH(l,l,u);return s||f?e(t,r,p,o,i,a,u,c+1):p}(r,n,p,t.navStart,t.navEnd,e,i);o&&(t.goToDay(o),d(o))}}}(e,R,q,X??(()=>!1),h),{labelDayButton:er,labelGridcell:en,labelGrid:eo,labelMonthDropdown:ei,labelNav:ea,labelWeekday:eu,labelWeekNumber:ec,labelWeekNumberHeader:el,labelYearDropdown:es}=d,ef=(0,f.useMemo)(()=>(function(e,t,r){let n=e.today(),o=t?e.startOfISOWeek(n):e.startOfWeek(n),i=[];for(let t=0;t<7;t++){let r=e.addDays(o,t);i.push(r)}return i})(h,e.ISOWeek),[h,e.ISOWeek]),ep=void 0!==b||void 0!==x,ed=(0,f.useCallback)(()=>{$&&(H($),A?.($))},[$,H,A]),eh=(0,f.useCallback)(()=>{Y&&(H(Y),E?.(Y))},[H,Y,E]),ey=(0,f.useCallback)((e,t)=>r=>{r.preventDefault(),r.stopPropagation(),et(e),Z?.(e.date,t,r),x?.(e.date,t,r)},[Z,x,et]),ev=(0,f.useCallback)((e,t)=>r=>{et(e),w?.(e.date,t,r)},[w,et]),em=(0,f.useCallback)((e,t)=>r=>{G(),g?.(e.date,t,r)},[G,g]),eb=(0,f.useCallback)((t,r)=>n=>{let o={ArrowLeft:["day","rtl"===e.dir?"after":"before"],ArrowRight:["day","rtl"===e.dir?"before":"after"],ArrowDown:["week","after"],ArrowUp:["week","before"],PageUp:[n.shiftKey?"year":"month","before"],PageDown:[n.shiftKey?"year":"month","after"],Home:["startOfWeek","before"],End:["endOfWeek","after"]};if(o[n.key]){n.preventDefault(),n.stopPropagation();let[e,t]=o[n.key];Q(e,t)}O?.(t.date,r,n)},[Q,O,e.dir]),eg=(0,f.useCallback)((e,t)=>r=>{j?.(e.date,t,r)},[j]),ex=(0,f.useCallback)((e,t)=>r=>{S?.(e.date,t,r)},[S]),ew=(0,f.useCallback)(e=>t=>{let r=Number(t.target.value);H(h.setMonth(h.startOfMonth(e),r))},[h,H]),eO=(0,f.useCallback)(e=>t=>{let r=Number(t.target.value);H(h.setYear(h.startOfMonth(e),r))},[h,H]),{className:ej,style:eS}=(0,f.useMemo)(()=>({className:[v[n.Root],e.className].filter(Boolean).join(" "),style:{...k?.[n.Root],...e.style}}),[v,e.className,e.style,k]),eE=function(e){let t={"data-mode":e.mode??void 0,"data-required":"required"in e?e.required:void 0,"data-multiple-months":e.numberOfMonths&&e.numberOfMonths>1||void 0,"data-week-numbers":e.showWeekNumber||void 0,"data-broadcast-calendar":e.broadcastCalendar||void 0};return Object.entries(e).forEach(([e,r])=>{e.startsWith("data-")&&(t[e]=r)}),t}(e),eA=(0,f.useRef)(null);return!function(e,t,{classNames:r,months:n,focused:o,dateLib:i}){let u=(0,f.useRef)(null),c=(0,f.useRef)(n),l=(0,f.useRef)(!1);(0,f.useLayoutEffect)(()=>{let s=c.current;if(c.current=n,!t||!e.current||!(e.current instanceof HTMLElement)||0===n.length||0===s.length||n.length!==s.length)return;let f=i.isSameMonth(n[0].date,s[0].date),p=i.isAfter(n[0].date,s[0].date),d=p?r[a.caption_after_enter]:r[a.caption_before_enter],h=p?r[a.weeks_after_enter]:r[a.weeks_before_enter],y=u.current,v=e.current.cloneNode(!0);if(v instanceof HTMLElement?(eL(v).forEach(e=>{if(!(e instanceof HTMLElement))return;let t=eW(e);t&&e.contains(t)&&e.removeChild(t);let r=eF(e);r&&r.classList.remove(d);let n=ez(e);n&&n.classList.remove(h)}),u.current=v):u.current=null,l.current||f||o)return;let m=y instanceof HTMLElement?eL(y):[],b=eL(e.current);if(b&&b.every(e=>e instanceof HTMLElement)&&m&&m.every(e=>e instanceof HTMLElement)){l.current=!0;let t=[];e.current.style.isolation="isolate";let n=eU(e.current);n&&(n.style.zIndex="1"),b.forEach((o,i)=>{let u=m[i];if(!u)return;o.style.position="relative",o.style.overflow="hidden";let c=eF(o);c&&c.classList.add(d);let s=ez(o);s&&s.classList.add(h);let f=()=>{l.current=!1,e.current&&(e.current.style.isolation=""),n&&(n.style.zIndex=""),c&&c.classList.remove(d),s&&s.classList.remove(h),o.style.position="",o.style.overflow="",o.contains(u)&&o.removeChild(u)};t.push(f),u.style.pointerEvents="none",u.style.position="absolute",u.style.overflow="hidden",u.setAttribute("aria-hidden","true");let y=e$(u);y&&(y.style.opacity="0");let v=eF(u);v&&(v.classList.add(p?r[a.caption_before_exit]:r[a.caption_after_exit]),v.addEventListener("animationend",f));let b=ez(u);b&&b.classList.add(p?r[a.weeks_before_exit]:r[a.weeks_after_exit]),o.insertBefore(u,o.firstChild)})}})}(eA,!!e.animate,{classNames:v,months:L,focused:K,dateLib:h}),f.createElement(ee.S.Provider,{value:{dayPickerProps:e,selected:V,select:Z,isSelected:X,months:L,nextMonth:Y,previousMonth:$,goToMonth:H,getModifiers:q,components:t,classNames:v,styles:k,labels:d,formatters:r}},f.createElement(t.Root,{rootRef:e.animate?eA:void 0,className:ej,style:eS,dir:e.dir,id:e.id,lang:e.lang,nonce:e.nonce,title:e.title,role:e.role,"aria-label":e["aria-label"],...eE},f.createElement(t.Months,{className:v[n.Months],style:k?.[n.Months]},!e.hideNavigation&&f.createElement(t.Nav,{"data-animated-nav":e.animate?"true":void 0,className:v[n.Nav],style:k?.[n.Nav],"aria-label":ea(),onPreviousClick:ed,onNextClick:eh,previousMonth:$,nextMonth:Y}),L.map((a,u)=>{let c=function(e,t,r,n,o){let{startOfMonth:i,startOfYear:a,endOfYear:u,eachMonthOfInterval:c,getMonth:l}=o;return c({start:a(e),end:u(e)}).map(e=>{let a=n.formatMonthDropdown(e,o);return{value:l(e),label:a,disabled:t&&e<i(t)||r&&e>i(r)||!1}})}(a.date,W,U,r,h),l=function(e,t,r,n){if(!e||!t)return;let{startOfYear:o,endOfYear:i,addYears:a,getYear:u,isBefore:c,isSameYear:l}=n,s=o(e),f=i(t),p=[],d=s;for(;c(d,f)||l(d,f);)p.push(d),d=a(d,1);return p.map(e=>{let t=r.formatYearDropdown(e,n);return{value:u(e),label:t,disabled:!1}})}(W,U,r,h);return f.createElement(t.Month,{"data-animated-month":e.animate?"true":void 0,className:v[n.Month],style:k?.[n.Month],key:u,displayIndex:u,calendarMonth:a},f.createElement(t.MonthCaption,{"data-animated-caption":e.animate?"true":void 0,className:v[n.MonthCaption],style:k?.[n.MonthCaption],calendarMonth:a,displayIndex:u},m?.startsWith("dropdown")?f.createElement(t.DropdownNav,{className:v[n.Dropdowns],style:k?.[n.Dropdowns]},"dropdown"===m||"dropdown-months"===m?f.createElement(t.MonthsDropdown,{className:v[n.MonthsDropdown],"aria-label":ei(),classNames:v,components:t,disabled:!!e.disableNavigation,onChange:ew(a.date),options:c,style:k?.[n.Dropdown],value:h.getMonth(a.date)}):f.createElement("span",null,_(a.date,h)),"dropdown"===m||"dropdown-years"===m?f.createElement(t.YearsDropdown,{className:v[n.YearsDropdown],"aria-label":es(h.options),classNames:v,components:t,disabled:!!e.disableNavigation,onChange:eO(a.date),options:l,style:k?.[n.Dropdown],value:h.getYear(a.date)}):f.createElement("span",null,I(a.date,h)),f.createElement("span",{role:"status","aria-live":"polite",style:{border:0,clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"absolute",width:"1px",whiteSpace:"nowrap",wordWrap:"normal"}},M(a.date,h.options,h))):f.createElement(t.CaptionLabel,{className:v[n.CaptionLabel],role:"status","aria-live":"polite"},M(a.date,h.options,h))),f.createElement(t.MonthGrid,{role:"grid","aria-multiselectable":"multiple"===b||"range"===b,"aria-label":eo(a.date,h.options,h)||void 0,className:v[n.MonthGrid],style:k?.[n.MonthGrid]},!e.hideWeekdays&&f.createElement(t.Weekdays,{"data-animated-weekdays":e.animate?"true":void 0,className:v[n.Weekdays],style:k?.[n.Weekdays]},P&&f.createElement(t.WeekNumberHeader,{"aria-label":el(h.options),className:v[n.WeekNumberHeader],style:k?.[n.WeekNumberHeader],scope:"col"},D()),ef.map((e,r)=>f.createElement(t.Weekday,{"aria-label":eu(e,h.options,h),className:v[n.Weekday],key:r,style:k?.[n.Weekday],scope:"col"},N(e,h.options,h)))),f.createElement(t.Weeks,{"data-animated-weeks":e.animate?"true":void 0,className:v[n.Weeks],style:k?.[n.Weeks]},a.weeks.map((r,a)=>f.createElement(t.Week,{className:v[n.Week],key:r.weekNumber,style:k?.[n.Week],week:r},P&&f.createElement(t.WeekNumber,{week:r,style:k?.[n.WeekNumber],"aria-label":ec(r.weekNumber,{locale:y}),className:v[n.WeekNumber],scope:"row",role:"rowheader"},C(r.weekNumber)),r.days.map(r=>{let{date:a}=r,u=q(r);if(u[o.focused]=!u.hidden&&!!K?.isEqualTo(r),u[i.selected]=X?.(a)||u.selected,eJ(V)){let{from:e,to:t}=V;u[i.range_start]=!!(e&&t&&h.isSameDay(a,e)),u[i.range_end]=!!(e&&t&&h.isSameDay(a,t)),u[i.range_middle]=eG(V,a,!0,h)}let c=function(e,t={},r={}){let o={...t?.[n.Day]};return Object.entries(e).filter(([,e])=>!0===e).forEach(([e])=>{o={...o,...r?.[e]}}),o}(u,k,e.modifiersStyles),l=function(e,t,r={}){return Object.entries(e).filter(([,e])=>!0===e).reduce((e,[n])=>(r[n]?e.push(r[n]):t[o[n]]?e.push(t[o[n]]):t[i[n]]&&e.push(t[i[n]]),e),[t[n.Day]])}(u,v,e.modifiersClassNames),s=ep||u.hidden?void 0:en(a,u,h.options,h);return f.createElement(t.Day,{key:`${h.format(a,"yyyy-MM-dd")}_${h.format(r.displayMonth,"yyyy-MM")}`,day:r,modifiers:u,className:l.join(" "),style:c,role:"gridcell","aria-selected":u.selected||void 0,"aria-label":s,"data-day":h.format(a,"yyyy-MM-dd"),"data-month":r.outside?h.format(a,"yyyy-MM"):void 0,"data-selected":u.selected||void 0,"data-disabled":u.disabled||void 0,"data-hidden":u.hidden||void 0,"data-outside":r.outside||void 0,"data-focused":u.focused||void 0,"data-today":u.today||void 0},!u.hidden&&ep?f.createElement(t.DayButton,{className:v[n.DayButton],style:k?.[n.DayButton],type:"button",day:r,modifiers:u,disabled:u.disabled||void 0,tabIndex:J(r)?0:-1,"aria-label":er(a,u,h.options,h),onClick:ey(r,u),onBlur:em(r,u),onFocus:ev(r,u),onKeyDown:eb(r,u),onMouseEnter:eg(r,u),onMouseLeave:ex(r,u)},T(a,h.options,h)):!u.hidden&&T(r.date,h.options,h))}))))))})),e.footer&&f.createElement(t.Footer,{className:v[n.Footer],style:k?.[n.Footer],role:"status","aria-live":"polite"},e.footer)))}!function(e){e[e.Today=0]="Today",e[e.Selected=1]="Selected",e[e.LastFocused=2]="LastFocused",e[e.FocusedModifier=3]="FocusedModifier"}(u||(u={}))},98233:(e,t,r)=>{var n=r(24376),o=r(20570),i=r(64439),a=n?n.toStringTag:void 0;e.exports=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":a&&a in Object(e)?o(e):i(e)}},99544:e=>{var t=/^(?:0|[1-9]\d*)$/;e.exports=function(e,r){var n=typeof e;return!!(r=null==r?0x1fffffffffffff:r)&&("number"==n||"symbol"!=n&&t.test(e))&&e>-1&&e%1==0&&e<r}}}]);