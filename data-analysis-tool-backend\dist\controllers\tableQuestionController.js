"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getTablesByProject = exports.deleteTable = exports.createTable = exports.getTable = exports.getTableQuestionsByProject = exports.deleteTableQuestion = exports.updateTableQuestion = exports.saveCellValues = exports.getTableQuestion = exports.createTableQuestion = void 0;
const tableQuestionRepository_1 = __importDefault(require("../repositories/tableQuestionRepository"));
const tableQuestionValidator_1 = require("../validators/tableQuestionValidator");
/**
 * TableQuestionController
 *
 * Handles all HTTP requests related to table questions.
 * Responsible for:
 * - Validating input data
 * - Calling appropriate repository methods
 * - Formatting and returning responses
 * - Error handling
 */
/**
 * Create a new table question
 *
 * @param req - Request object containing table question data
 * @param res - Response object
 * @returns JSON response with created table question or error
 */
const createTableQuestion = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // User authentication is handled by the middleware
        const result = tableQuestionValidator_1.createTableQuestionSchema.safeParse(req.body);
        if (!result.success) {
            console.error("Validation error:", result.error.flatten().fieldErrors);
            return res.status(400).json({
                success: false,
                message: "Invalid input data",
                errors: result.error.flatten().fieldErrors,
            });
        }
        // Check if user has access to the project
        // This would typically use your existing project access check logic
        // Note: Most validation is now handled by the Zod schema in createTableQuestionSchema
        // The schema validates:
        // 1. Required fields (label, projectId)
        // 2. Column and row existence
        // 3. Parent-child relationships
        // 4. Maximum of 2 children per parent
        // 5. Maximum of 2 levels of nesting
        // 6. No circular references
        const { columns, rows } = result.data;
        const tableQuestion = yield tableQuestionRepository_1.default.createTableQuestion(result.data);
        return res.status(201).json({
            success: true,
            message: "Table question created successfully",
            data: tableQuestion,
        });
    }
    catch (error) {
        console.error("Error creating table question:", error);
        // Determine appropriate status code based on error
        let statusCode = 500;
        if (error.message.includes("required") ||
            error.message.includes("valid") ||
            error.message.includes("must have")) {
            statusCode = 400;
        }
        return res.status(statusCode).json({
            success: false,
            message: error.message || "Error creating table question",
            error: error.stack,
        });
    }
});
exports.createTableQuestion = createTableQuestion;
/**
 * Get a table question by ID
 *
 * @param req - Request object containing question ID
 * @param res - Response object
 * @returns JSON response with table question and cell values or error
 */
const getTableQuestion = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // User authentication is handled by the middleware
        const questionId = parseInt(req.params.id);
        if (isNaN(questionId)) {
            return res.status(400).json({
                success: false,
                message: "Invalid question ID",
            });
        }
        const tableQuestion = yield tableQuestionRepository_1.default.getTableQuestionById(questionId);
        if (!tableQuestion) {
            return res.status(404).json({
                success: false,
                message: "Table question not found",
            });
        }
        // Check if user has access to the project
        // This would typically use your existing project access check logic
        // Get cell values - now returns a formatted map directly
        const cellValuesMap = yield tableQuestionRepository_1.default.getCellValues(questionId);
        return res.status(200).json({
            success: true,
            data: {
                question: tableQuestion,
                cellValues: cellValuesMap,
            },
        });
    }
    catch (error) {
        console.error("Error retrieving table question:", error);
        return res.status(500).json({
            success: false,
            message: "Error retrieving table question",
            error: error.message,
        });
    }
});
exports.getTableQuestion = getTableQuestion;
/**
 * Save cell values for a table question
 *
 * @param req - Request object containing cell values data
 * @param res - Response object
 * @returns JSON response with success message or error
 */
const saveCellValues = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // User authentication is handled by the middleware
        const result = tableQuestionValidator_1.saveCellValuesSchema.safeParse(req.body);
        if (!result.success) {
            return res.status(400).json({
                success: false,
                message: "Invalid input data",
                errors: result.error.flatten().fieldErrors,
            });
        }
        const { questionId, cellValues } = result.data;
        // Verify the question exists
        const question = yield tableQuestionRepository_1.default.getTableQuestionById(questionId);
        if (!question) {
            return res.status(404).json({
                success: false,
                message: "Table question not found",
            });
        }
        // Check if user has access to the project
        // This would typically use your existing project access check logic
        // Save cell values with the updated repository method signature
        yield tableQuestionRepository_1.default.saveCellValues({
            questionId,
            cellValues,
        });
        return res.status(200).json({
            success: true,
            message: "Cell values saved successfully",
        });
    }
    catch (error) {
        console.error("Error saving cell values:", error);
        return res.status(500).json({
            success: false,
            message: "Error saving cell values",
            error: error.message,
        });
    }
});
exports.saveCellValues = saveCellValues;
/**
 * Update an existing table question
 *
 * @param req - Request object containing updated table question data
 * @param res - Response object
 * @returns JSON response with updated table question or error
 */
const updateTableQuestion = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // User authentication is handled by the middleware
        const questionId = parseInt(req.params.id);
        if (isNaN(questionId)) {
            return res.status(400).json({
                success: false,
                message: "Invalid question ID",
            });
        }
        // Validate the request body
        const result = tableQuestionValidator_1.updateTableQuestionSchema.safeParse(req.body);
        if (!result.success) {
            console.error("Validation error:", result.error.flatten().fieldErrors);
            return res.status(400).json({
                success: false,
                message: "Invalid input data",
                errors: result.error.flatten().fieldErrors,
            });
        }
        // Get the existing table question
        const existingQuestion = yield tableQuestionRepository_1.default.getTableQuestionById(questionId);
        if (!existingQuestion) {
            return res.status(404).json({
                success: false,
                message: "Table question not found",
            });
        }
        // Check if user has access to the project
        // This would typically use your existing project access check logic
        // Extract validated data
        const { label, columns, rows } = result.data;
        // Check for empty column names
        const emptyColumns = columns.filter((col) => !col.columnName.trim());
        if (emptyColumns.length > 0) {
            return res.status(400).json({
                success: false,
                message: "All columns must have valid names",
            });
        }
        // Check for empty row names
        const emptyRows = rows.filter((row) => !row.rowsName.trim());
        if (emptyRows.length > 0) {
            return res.status(400).json({
                success: false,
                message: "All rows must have valid names",
            });
        }
        // Note: We don't need to manually validate parent-child relationships here
        // because the Zod schema in updateTableQuestionSchema already does this validation
        // The schema checks for:
        // 1. Parent column existence
        // 2. Maximum of 2 children per parent
        // 3. Maximum of 2 levels of nesting
        // 4. No circular references
        // Update the table question
        try {
            const updatedTableQuestion = yield tableQuestionRepository_1.default.updateTableQuestion(questionId, {
                label,
                columns,
                rows,
                projectId: existingQuestion.projectId, // Include the project ID from the existing question
            });
            return res.status(200).json({
                success: true,
                message: "Table question updated successfully",
                data: updatedTableQuestion,
            });
        }
        catch (updateError) {
            console.error("Error in repository while updating table question:", updateError);
            // Determine appropriate status code based on error
            let statusCode = 500;
            if (updateError.message.includes("required") ||
                updateError.message.includes("valid") ||
                updateError.message.includes("must have") ||
                updateError.message.includes("not found") ||
                updateError.message.includes("Invalid")) {
                statusCode = 400;
            }
            return res.status(statusCode).json({
                success: false,
                message: updateError.message || "Error updating table question",
                error: updateError.stack,
            });
        }
    }
    catch (error) {
        console.error("Unexpected error updating table question:", error);
        return res.status(500).json({
            success: false,
            message: "Unexpected error updating table question",
            error: error.message,
        });
    }
});
exports.updateTableQuestion = updateTableQuestion;
/**
 * Delete a table question
 *
 * @param req - Request object containing question ID
 * @param res - Response object
 * @returns JSON response with success message or error
 */
const deleteTableQuestion = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // User authentication is handled by the middleware
        const questionId = parseInt(req.params.id);
        if (isNaN(questionId)) {
            return res.status(400).json({
                success: false,
                message: "Invalid question ID",
            });
        }
        const question = yield tableQuestionRepository_1.default.getTableQuestionById(questionId);
        if (!question) {
            return res.status(404).json({
                success: false,
                message: "Table question not found",
            });
        }
        // Check if user has access to the project
        // This would typically use your existing project access check logic
        yield tableQuestionRepository_1.default.deleteTableQuestion(questionId);
        return res.status(200).json({
            success: true,
            message: "Table question deleted successfully",
        });
    }
    catch (error) {
        console.error("Error deleting table question:", error);
        return res.status(500).json({
            success: false,
            message: "Error deleting table question",
            error: error.message,
        });
    }
});
exports.deleteTableQuestion = deleteTableQuestion;
/**
 * Get all table questions for a project
 *
 * @param req - Request object containing project ID
 * @param res - Response object
 * @returns JSON response with table questions or error
 */
const getTableQuestionsByProject = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { projectId } = req.params;
        if (!projectId || isNaN(Number(projectId))) {
            return res.status(400).json({
                success: false,
                message: "Invalid project ID",
            });
        }
        const tables = yield tableQuestionRepository_1.default.getTableQuestionsByProjectId(Number(projectId));
        return res.status(200).json({
            success: true,
            data: tables,
        });
    }
    catch (error) {
        console.error("Error getting table questions:", error);
        return res.status(500).json({
            success: false,
            message: "Failed to get table questions",
            error: error.message,
        });
    }
});
exports.getTableQuestionsByProject = getTableQuestionsByProject;
/**
 * Aliases for backward compatibility with the old /tables endpoints
 *
 * These aliases allow existing frontend code to continue working without changes.
 * They simply reference the corresponding tableQuestion methods.
 */
exports.getTable = exports.getTableQuestion;
exports.createTable = exports.createTableQuestion;
exports.deleteTable = exports.deleteTableQuestion;
exports.getTablesByProject = exports.getTableQuestionsByProject;
