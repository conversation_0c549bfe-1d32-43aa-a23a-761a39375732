"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __asyncValues = (this && this.__asyncValues) || function (o) {
    if (!Symbol.asyncIterator) throw new TypeError("Symbol.asyncIterator is not defined.");
    var m = o[Symbol.asyncIterator], i;
    return m ? m.call(o) : (o = typeof __values === "function" ? __values(o) : o[Symbol.iterator](), i = {}, verb("next"), verb("throw"), verb("return"), i[Symbol.asyncIterator] = function () { return this; }, i);
    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }
    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeleteExportFile = exports.findAllExportFile = exports.downloadExportedFile = exports.generateAndSaveExport = void 0;
const exceljs_1 = __importDefault(require("exceljs"));
const format_1 = require("@fast-csv/format");
const stream_1 = __importDefault(require("stream"));
const formSubmissionRepository_1 = __importDefault(require("../repositories/formSubmissionRepository"));
const exportFileRepository_1 = __importDefault(require("../repositories/exportFileRepository"));
// 📦 Generate and store Excel or CSV
const generateAndSaveExport = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a, e_1, _b, _c;
    var _d, _e;
    try {
        const { type } = req.query; // "excel" or "csv"
        const projectId = Number(req.params.id);
        const userId = Number(req.user.id);
        const formSubmissions = yield formSubmissionRepository_1.default.findByProjectId(projectId);
        if (!["excel", "csv"].includes(String(type))) {
            return res.status(400).json({ message: "Invalid file type" });
        }
        // Step 1: Get all unique question labels
        const questionSet = new Set();
        formSubmissions.forEach((form) => {
            form.answers.forEach((answer) => {
                var _a;
                if ((_a = answer.question) === null || _a === void 0 ? void 0 : _a.label) {
                    questionSet.add(answer.question.label);
                }
            });
        });
        const dynamicQuestions = Array.from(questionSet);
        // Step 2: Define fixed columns
        const fixedHeaders = [
            "ID",
            "Submission Time",
            "Submitted By",
            "Device Info",
            "Location",
            "Is Other Option",
        ];
        // Combine fixed + dynamic headers
        const allHeaders = [...fixedHeaders, ...dynamicQuestions];
        let buffer;
        let contentType;
        let fileName;
        if (type === "excel") {
            const workbook = new exceljs_1.default.Workbook();
            const worksheet = workbook.addWorksheet("Answers");
            // Define columns in Excel
            worksheet.columns = allHeaders.map((header) => ({
                header,
                key: header,
                width: 30,
            }));
            // Add each form submission row
            formSubmissions.forEach((form) => {
                var _a, _b;
                const row = {
                    ID: form.id,
                    "Submission Time": ((_a = form.submittedAt) === null || _a === void 0 ? void 0 : _a.toISOString()) || "N/A",
                    "Submitted By": ((_b = form.user) === null || _b === void 0 ? void 0 : _b.name) || "N/A",
                    "Device Info": form.deviceInfo || "N/A",
                    Location: form.location || "N/A",
                    "Is Other Option": form.answers.some((a) => a.isOtherOption) || false,
                };
                // Map answers by question label
                form.answers.forEach((answer) => {
                    var _a;
                    const label = (_a = answer.question) === null || _a === void 0 ? void 0 : _a.label;
                    if (label) {
                        row[label] = answer.value;
                    }
                });
                worksheet.addRow(row);
            });
            const buf = yield workbook.xlsx.writeBuffer();
            buffer = Buffer.from(buf);
            contentType =
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            fileName = `answers-${projectId}.xlsx`;
        }
        else {
            // CSV generation
            const csvBufferStream = new stream_1.default.PassThrough();
            const chunks = [];
            const csvStream = (0, format_1.format)({ headers: allHeaders });
            csvStream.pipe(csvBufferStream);
            for (const form of formSubmissions) {
                const row = {
                    ID: form.id,
                    "Submission Time": ((_d = form.submittedAt) === null || _d === void 0 ? void 0 : _d.toISOString()) || "N/A",
                    "Submitted By": ((_e = form.user) === null || _e === void 0 ? void 0 : _e.name) || "N/A",
                    "Device Info": form.deviceInfo || "N/A",
                    Location: form.location || "N/A",
                    "Is Other Option": form.answers.some((a) => a.isOtherOption) || false,
                };
                form.answers.forEach((answer) => {
                    var _a;
                    const label = (_a = answer.question) === null || _a === void 0 ? void 0 : _a.label;
                    if (label) {
                        row[label] = answer.value;
                    }
                });
                csvStream.write(row);
            }
            csvStream.end();
            try {
                for (var _f = true, csvBufferStream_1 = __asyncValues(csvBufferStream), csvBufferStream_1_1; csvBufferStream_1_1 = yield csvBufferStream_1.next(), _a = csvBufferStream_1_1.done, !_a; _f = true) {
                    _c = csvBufferStream_1_1.value;
                    _f = false;
                    const chunk = _c;
                    chunks.push(chunk);
                }
            }
            catch (e_1_1) { e_1 = { error: e_1_1 }; }
            finally {
                try {
                    if (!_f && !_a && (_b = csvBufferStream_1.return)) yield _b.call(csvBufferStream_1);
                }
                finally { if (e_1) throw e_1.error; }
            }
            buffer = Buffer.concat(chunks);
            contentType = "text/csv";
            fileName = `answers-${projectId}.csv`;
        }
        // Save exported file in DB
        const file = yield exportFileRepository_1.default.create({
            projectId,
            userId,
            fileName,
            fileType: String(type),
            contentType,
            fileBuffer: buffer,
        });
        return res.status(201).json({
            message: "File exported and saved",
            fileId: file.id,
        });
    }
    catch (err) {
        console.error(err);
        res.status(500).json({ message: "Export failed", error: err.message });
    }
});
exports.generateAndSaveExport = generateAndSaveExport;
// 📥 Download file by ID
const downloadExportedFile = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const fileId = Number(req.params.fileId);
        const file = yield exportFileRepository_1.default.findById(fileId);
        if (!file) {
            return res.status(404).json({ message: "File not found" });
        }
        res.setHeader("Content-Type", file.contentType);
        res.setHeader("Content-Disposition", `attachment; filename=${file.fileName}`);
        res.send(file.fileBuffer);
    }
    catch (err) {
        console.error(err);
        res.status(500).json({ message: "Download failed", error: err.message });
    }
});
exports.downloadExportedFile = downloadExportedFile;
const findAllExportFile = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const projectId = Number(req.query.projectId);
        const userId = Number(req.user.id);
        if (!projectId) {
            return res.status(400).json({
                message: "project id not passed in query",
            });
        }
        const files = yield exportFileRepository_1.default.findAll(userId, projectId);
        return res.status(200).json({
            success: true,
            message: "success",
            data: { files },
        });
    }
    catch (error) {
        return res.status(500).json({
            success: false,
            message: "Error getting export files",
            error: error instanceof Error ? error.message : "Unexpected error",
        });
    }
});
exports.findAllExportFile = findAllExportFile;
const DeleteExportFile = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = Number(req.user.id);
        const fileId = Number(req.params.id);
        if (!fileId) {
            return res.status(400).json({
                message: "file id required",
            });
        }
        yield exportFileRepository_1.default.deleteFile(fileId);
        return res.status(200).json({
            success: true,
            message: "success",
        });
    }
    catch (error) {
        return res.status(500).json({
            success: false,
            message: "Error deleting export files",
            error: error instanceof Error ? error.message : "Unexpected error",
        });
    }
});
exports.DeleteExportFile = DeleteExportFile;
