(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8295],{6217:(e,s,a)=>{Promise.resolve().then(a.bind(a,62840))},62840:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>m});var t=a(95155),l=a(53904),r=a(54653),i=a(50505),d=a(66932),n=a(47924),o=a(12115),c=a(66766);let h=[{id:1,title:"Mountain Landscape",url:"https://images.unsplash.com/photo-1506905925346-21bda4d32df4",date:"2023-05-15",author:"Unsplash"},{id:2,title:"Ocean Sunset",url:"https://images.unsplash.com/photo-1503803548695-c2a7b4a5b875",date:"2023-05-16",author:"Unsplash"},{id:3,title:"Forest Path",url:"https://images.unsplash.com/photo-1448375240586-882707db888b",date:"2023-05-17",author:"Unsplash"},{id:4,title:"City Skyline",url:"https://images.unsplash.com/photo-1514565131-fce0801e5785",date:"2023-05-18",author:"Unsplash"},{id:5,title:"Desert Dunes",url:"https://images.unsplash.com/photo-1509316785289-025f5b846b35",date:"2023-05-19",author:"Unsplash"},{id:6,title:"Snowy Mountains",url:"https://images.unsplash.com/photo-1483728642387-6c3bdd6c93e5",date:"2023-05-20",author:"Unsplash"}];function m(){let[e,s]=(0,o.useState)(""),[a,m]=(0,o.useState)("grid"),[u,p]=(0,o.useState)(h),[x,b]=(0,o.useState)(!0);return(0,o.useEffect)(()=>{let e=setTimeout(()=>{b(!1)},1e3);return()=>clearTimeout(e)},[]),(0,o.useEffect)(()=>{e?p(h.filter(s=>s.title.toLowerCase().includes(e.toLowerCase()))):p(h)},[e]),(0,t.jsxs)("div",{className:"flex flex-col space-y-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("h1",{className:"text-2xl font-semibold text-neutral-800",children:"Gallery"}),(0,t.jsx)("div",{className:"flex gap-2",children:(0,t.jsxs)("button",{className:"btn-primary",title:"Refresh gallery",onClick:()=>b(!0),children:[(0,t.jsx)(l.A,{className:"w-4 h-4 ".concat(x?"animate-spin":"")}),(0,t.jsx)("span",{children:"Refresh"})]})})]}),(0,t.jsxs)("div",{className:"flex justify-between gap-4 flex-wrap",children:[(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)("button",{className:"btn-primary ".concat("grid"===a?"bg-primary-600 text-neutral-100":"bg-primary-500 hover:bg-primary-600 text-neutral-100"),onClick:()=>m("grid"),children:[(0,t.jsx)(r.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:"Grid"})]}),(0,t.jsxs)("button",{className:"btn-primary ".concat("list"===a?"bg-primary-600 text-neutral-100":"bg-primary-500 hover:bg-primary-600 text-neutral-100"),onClick:()=>m("list"),children:[(0,t.jsx)(i.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:"List"})]}),(0,t.jsxs)("button",{className:"btn-primary",children:[(0,t.jsx)(d.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:"Filter"})]})]}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{type:"text",placeholder:"Search images...",className:"pl-9 pr-4 py-2 border border-neutral-300 rounded w-64 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 focus:outline-none transition-colors",value:e,onChange:e=>s(e.target.value)}),(0,t.jsx)(n.A,{className:"absolute left-3 top-2.5 w-4 h-4 text-neutral-400"})]})]}),x?(0,t.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:[1,2,3,4,5,6].map(e=>(0,t.jsxs)("div",{className:"bg-neutral-100 border border-neutral-200 rounded-md overflow-hidden shadow-sm",children:[(0,t.jsx)("div",{className:"aspect-video bg-neutral-200 animate-pulse"}),(0,t.jsxs)("div",{className:"p-3",children:[(0,t.jsx)("div",{className:"h-4 bg-neutral-200 rounded animate-pulse w-3/4 mb-2"}),(0,t.jsx)("div",{className:"h-3 bg-neutral-200 rounded animate-pulse w-1/2"})]})]},e))}):(0,t.jsx)(t.Fragment,{children:"grid"===a?(0,t.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:u.map(e=>(0,t.jsxs)("div",{className:"bg-neutral-100 border border-neutral-200 rounded-md overflow-hidden shadow-sm hover:shadow-md transition-shadow",children:[(0,t.jsx)("div",{className:"aspect-video overflow-hidden bg-neutral-100 relative",children:(0,t.jsx)(c.default,{src:"".concat(e.url,"?w=600&h=400&auto=format&fit=crop"),alt:e.title,fill:!0,sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",className:"object-cover hover:scale-105 transition-transform duration-300"})}),(0,t.jsxs)("div",{className:"p-3",children:[(0,t.jsx)("h3",{className:"font-medium text-neutral-800",children:e.title}),(0,t.jsxs)("p",{className:"text-xs text-neutral-500 mt-1",children:[e.date," • Photo by ",e.author]})]})]},e.id))}):(0,t.jsx)("div",{className:"bg-neutral-100 border border-neutral-200 rounded-md overflow-hidden shadow-sm",children:(0,t.jsx)("ul",{className:"divide-y divide-neutral-200",children:u.map(e=>(0,t.jsxs)("li",{className:"flex items-center gap-4 p-3 hover:bg-neutral-50",children:[(0,t.jsx)("div",{className:"w-20 h-16 bg-neutral-100 overflow-hidden rounded relative",children:(0,t.jsx)(c.default,{src:"".concat(e.url,"?w=160&h=120&auto=format&fit=crop"),alt:e.title,fill:!0,sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",className:"object-cover"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium text-neutral-800",children:e.title}),(0,t.jsxs)("p",{className:"text-xs text-neutral-500 mt-1",children:[e.date," • Photo by ",e.author]})]})]},e.id))})})})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[7315,8441,1684,7358],()=>s(6217)),_N_E=e.O()}]);