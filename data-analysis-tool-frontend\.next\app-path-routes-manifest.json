{"/_not-found/page": "/_not-found", "/favicon.ico/route": "/favicon.ico", "/form-submission/[hashedId]/sign-in/page": "/form-submission/[hashedId]/sign-in", "/form-submission/[hashedId]/page": "/form-submission/[hashedId]", "/edit-submission/[hashedId]/[submissionId]/page": "/edit-submission/[hashedId]/[submissionId]", "/[locale]/(auth)/reset-password/change-password/page": "/[locale]/reset-password/change-password", "/[locale]/(auth)/reset-password/page": "/[locale]/reset-password", "/[locale]/(auth)/page": "/[locale]", "/[locale]/(auth)/signup/page": "/[locale]/signup", "/[locale]/[hashedId]/page": "/[locale]/[hashedId]", "/[locale]/[hashedId]/sign-in/page": "/[locale]/[hashedId]/sign-in", "/[locale]/test-page/page": "/[locale]/test-page", "/[locale]/(main)/dashboard/page": "/[locale]/dashboard", "/[locale]/(main)/dashboard/[status]/not-available/page": "/[locale]/dashboard/[status]/not-available", "/[locale]/(main)/policy/page": "/[locale]/policy", "/[locale]/(main)/library/not-available/page": "/[locale]/library/not-available", "/[locale]/(main)/library/page": "/[locale]/library", "/[locale]/(main)/library/asset/page": "/[locale]/library/asset", "/[locale]/(main)/terms/page": "/[locale]/terms", "/[locale]/(main)/account/security/page": "/[locale]/account/security", "/[locale]/(main)/account/profile/page": "/[locale]/account/profile", "/[locale]/(main)/library/question-block/form-builder/page": "/[locale]/library/question-block/form-builder", "/[locale]/(main)/library/template/[hashedId]/form-builder/page": "/[locale]/library/template/[hashedId]/form-builder", "/[locale]/(main)/library/template/[hashedId]/settings/page": "/[locale]/library/template/[hashedId]/settings", "/[locale]/(main)/project/[hashedId]/settings/page": "/[locale]/project/[hashedId]/settings", "/[locale]/(main)/project/[hashedId]/overview/page": "/[locale]/project/[hashedId]/overview", "/[locale]/(main)/project/[hashedId]/form-builder/page": "/[locale]/project/[hashedId]/form-builder", "/[locale]/(main)/project/[hashedId]/data/page": "/[locale]/project/[hashedId]/data", "/[locale]/(main)/project/[hashedId]/data/gallery/page": "/[locale]/project/[hashedId]/data/gallery", "/[locale]/(main)/project/[hashedId]/data/map/page": "/[locale]/project/[hashedId]/data/map", "/[locale]/(main)/project/[hashedId]/data/downloads/page": "/[locale]/project/[hashedId]/data/downloads", "/[locale]/(main)/project/[hashedId]/data/table/page": "/[locale]/project/[hashedId]/data/table", "/[locale]/(main)/project/[hashedId]/data/reports/page": "/[locale]/project/[hashedId]/data/reports"}