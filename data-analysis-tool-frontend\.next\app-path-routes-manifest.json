{"/_not-found/page": "/_not-found", "/favicon.ico/route": "/favicon.ico", "/edit-submission/[hashedId]/[submissionId]/page": "/edit-submission/[hashedId]/[submissionId]", "/form-submission/[hashedId]/page": "/form-submission/[hashedId]", "/form-submission/[hashedId]/sign-in/page": "/form-submission/[hashedId]/sign-in", "/[locale]/(auth)/page": "/[locale]", "/[locale]/(auth)/signup/page": "/[locale]/signup", "/[locale]/[hashedId]/page": "/[locale]/[hashedId]", "/[locale]/(auth)/reset-password/page": "/[locale]/reset-password", "/[locale]/[hashedId]/sign-in/page": "/[locale]/[hashedId]/sign-in", "/[locale]/(auth)/reset-password/change-password/page": "/[locale]/reset-password/change-password", "/[locale]/test-page/page": "/[locale]/test-page", "/[locale]/(main)/dashboard/[status]/not-available/page": "/[locale]/dashboard/[status]/not-available", "/[locale]/(main)/dashboard/page": "/[locale]/dashboard", "/[locale]/(main)/library/not-available/page": "/[locale]/library/not-available", "/[locale]/(main)/library/asset/page": "/[locale]/library/asset", "/[locale]/(main)/library/page": "/[locale]/library", "/[locale]/(main)/policy/page": "/[locale]/policy", "/[locale]/(main)/terms/page": "/[locale]/terms", "/[locale]/(main)/account/profile/page": "/[locale]/account/profile", "/[locale]/(main)/account/security/page": "/[locale]/account/security", "/[locale]/(main)/library/template/[hashedId]/form-builder/page": "/[locale]/library/template/[hashedId]/form-builder", "/[locale]/(main)/library/template/[hashedId]/settings/page": "/[locale]/library/template/[hashedId]/settings", "/[locale]/(main)/project/[hashedId]/overview/page": "/[locale]/project/[hashedId]/overview", "/[locale]/(main)/library/question-block/form-builder/page": "/[locale]/library/question-block/form-builder", "/[locale]/(main)/project/[hashedId]/form-builder/page": "/[locale]/project/[hashedId]/form-builder", "/[locale]/(main)/project/[hashedId]/settings/page": "/[locale]/project/[hashedId]/settings", "/[locale]/(main)/project/[hashedId]/data/downloads/page": "/[locale]/project/[hashedId]/data/downloads", "/[locale]/(main)/project/[hashedId]/data/page": "/[locale]/project/[hashedId]/data", "/[locale]/(main)/project/[hashedId]/data/map/page": "/[locale]/project/[hashedId]/data/map", "/[locale]/(main)/project/[hashedId]/data/table/page": "/[locale]/project/[hashedId]/data/table", "/[locale]/(main)/project/[hashedId]/data/reports/page": "/[locale]/project/[hashedId]/data/reports", "/[locale]/(main)/project/[hashedId]/data/gallery/page": "/[locale]/project/[hashedId]/data/gallery"}