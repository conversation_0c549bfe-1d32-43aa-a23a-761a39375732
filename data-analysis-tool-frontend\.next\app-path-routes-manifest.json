{"/_not-found/page": "/_not-found", "/favicon.ico/route": "/favicon.ico", "/edit-submission/[hashedId]/[submissionId]/page": "/edit-submission/[hashedId]/[submissionId]", "/form-submission/[hashedId]/sign-in/page": "/form-submission/[hashedId]/sign-in", "/form-submission/[hashedId]/page": "/form-submission/[hashedId]", "/[locale]/(auth)/reset-password/page": "/[locale]/reset-password", "/[locale]/(auth)/reset-password/change-password/page": "/[locale]/reset-password/change-password", "/[locale]/(auth)/page": "/[locale]", "/[locale]/[hashedId]/page": "/[locale]/[hashedId]", "/[locale]/[hashedId]/sign-in/page": "/[locale]/[hashedId]/sign-in", "/[locale]/test-page/page": "/[locale]/test-page", "/[locale]/(auth)/signup/page": "/[locale]/signup", "/[locale]/(main)/library/asset/page": "/[locale]/library/asset", "/[locale]/(main)/dashboard/[status]/not-available/page": "/[locale]/dashboard/[status]/not-available", "/[locale]/(main)/dashboard/page": "/[locale]/dashboard", "/[locale]/(main)/library/page": "/[locale]/library", "/[locale]/(main)/library/not-available/page": "/[locale]/library/not-available", "/[locale]/(main)/policy/page": "/[locale]/policy", "/[locale]/(main)/terms/page": "/[locale]/terms", "/[locale]/(main)/account/security/page": "/[locale]/account/security", "/[locale]/(main)/library/template/[hashedId]/settings/page": "/[locale]/library/template/[hashedId]/settings", "/[locale]/(main)/account/profile/page": "/[locale]/account/profile", "/[locale]/(main)/library/template/[hashedId]/form-builder/page": "/[locale]/library/template/[hashedId]/form-builder", "/[locale]/(main)/library/question-block/form-builder/page": "/[locale]/library/question-block/form-builder", "/[locale]/(main)/project/[hashedId]/form-builder/page": "/[locale]/project/[hashedId]/form-builder", "/[locale]/(main)/project/[hashedId]/overview/page": "/[locale]/project/[hashedId]/overview", "/[locale]/(main)/project/[hashedId]/settings/page": "/[locale]/project/[hashedId]/settings", "/[locale]/(main)/project/[hashedId]/data/page": "/[locale]/project/[hashedId]/data", "/[locale]/(main)/project/[hashedId]/data/gallery/page": "/[locale]/project/[hashedId]/data/gallery", "/[locale]/(main)/project/[hashedId]/data/table/page": "/[locale]/project/[hashedId]/data/table", "/[locale]/(main)/project/[hashedId]/data/reports/page": "/[locale]/project/[hashedId]/data/reports", "/[locale]/(main)/project/[hashedId]/data/downloads/page": "/[locale]/project/[hashedId]/data/downloads", "/[locale]/(main)/project/[hashedId]/data/map/page": "/[locale]/project/[hashedId]/data/map"}