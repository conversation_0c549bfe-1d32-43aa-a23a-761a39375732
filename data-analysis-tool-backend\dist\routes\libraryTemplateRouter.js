"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const libraryTemplateController_1 = require("../controllers/libraryTemplateController");
const auth_1 = require("../middleware/auth");
const router = express_1.default.Router();
router.post("/", auth_1.authenticate, libraryTemplateController_1.createLibraryTemplate);
router.get("/", auth_1.authenticate, libraryTemplateController_1.getAllLibraryTemplates);
router.delete("/delete-multiple", auth_1.authenticate, libraryTemplateController_1.DeleteMultipleLibraryTemplate);
router.patch("/:id", auth_1.authenticate, libraryTemplateController_1.updateLibraryTemplate);
router.get("/:id", auth_1.authenticate, libraryTemplateController_1.getLibraryTemplateById);
router.delete("/:id", auth_1.authenticate, libraryTemplateController_1.deleteLibraryTemplate);
router.get("/form/:id", auth_1.authenticate, libraryTemplateController_1.fetchQuestionForLibraryTemplate);
exports.default = router;
