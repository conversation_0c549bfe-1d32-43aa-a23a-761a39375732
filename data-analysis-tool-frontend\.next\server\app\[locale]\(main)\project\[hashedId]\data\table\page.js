(()=>{var e={};e.id=1361,e.ids=[1361],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7182:(e,t,s)=>{Promise.resolve().then(s.bind(s,90541))},9197:(e,t,s)=>{Promise.resolve().then(s.bind(s,99870))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},13861:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20174:(e,t,s)=>{"use strict";s.d(t,{F:()=>o});var r=s(60687),a=s(16189),l=s(85814),n=s.n(l);s(43210);let o=({items:e})=>{let t=(0,a.usePathname)(),s=e=>t.startsWith(e);return(0,r.jsx)("div",{className:"border-y border-neutral-400 rounded-md bg-primary-500 my-4 shadow-md",children:(0,r.jsx)("div",{className:"flex items-center",children:e.map(e=>e.disabled?(0,r.jsxs)("div",{className:"flex items-center gap-2 font-medium transition-all duration-300 p-4 border-b-2 text-gray-400 cursor-not-allowed",children:[e.icon,e.label]},e.route):(0,r.jsxs)(n(),{href:e.route,className:`flex items-center gap-2 font-medium transition-all duration-300 p-4 border-b-2 text-neutral-100 ${s(e.route)?"border-neutral-100":"border-transparent hover:border-neutral-400"}`,children:[e.icon,e.label]},e.route))})})}},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31158:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},33873:e=>{"use strict";e.exports=require("path")},37446:(e,t,s)=>{Promise.resolve().then(s.bind(s,51129))},51129:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Kobo-project\\\\data-analysis-tool-frontend\\\\app\\\\[locale]\\\\(main)\\\\project\\\\[hashedId]\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\project\\[hashedId]\\layout.tsx","default")},51200:(e,t,s)=>{Promise.resolve().then(s.bind(s,84638))},51219:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>_});var r=s(60687),a=s(93437);let l=(0,s(62688).A)("arrow-up-down",[["path",{d:"m21 16-4 4-4-4",key:"f6ql7i"}],["path",{d:"M17 20V4",key:"1ejh1v"}],["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}]]);var n=s(13861),o=s(96241),i=s(43210),d=s.n(i),c=s(38587),u=s(96752);let m=({isOpen:e,onClose:t,title:s,tableData:a,uniqueColumns:l,uniqueRows:n,rowsMap:o,useParentChildColumns:i=!1,loading:m=!1,tableStructure:p})=>{let h=d().useMemo(()=>{if(!a||0===a.length)return[];let e=[];return a.forEach(t=>{let s=parseInt(t.column),r=parseInt(t.row);isNaN(s)||isNaN(r)||e.push({columnId:s,rowsId:r,value:t.value})}),e},[a]),x=d().useMemo(()=>{if(!p?.tableColumns||0===p.tableColumns.length)return{parentColumns:[],columnMap:new Map,hasChildColumns:!1};let e=p.tableColumns.filter(e=>void 0===e.parentColumnId||null===e.parentColumnId),t=new Map;e.forEach(e=>{let s=p.tableColumns.filter(t=>t.parentColumnId===e.id);t.set(e.id,s)});let s=e.some(e=>(t.get(e.id)||[]).length>0);return{parentColumns:e,columnMap:t,hasChildColumns:s}},[p]),b=d().useMemo(()=>{let e=new Map;return h.forEach(t=>{e.set(`${t.columnId}_${t.rowsId}`,t.value)}),e},[h]);return(0,r.jsx)(c.A,{isOpen:e,onClose:t,className:"p-6 rounded-md max-w-4xl w-[95%] mobile:w-[85%] tablet:w-[75%] laptop:w-[60%]",children:(0,r.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-neutral-700",children:s}),m?(0,r.jsxs)("div",{className:"flex items-center justify-center p-8",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"}),(0,r.jsx)("span",{className:"ml-2 text-neutral-600",children:"Loading table data..."})]}):p?.tableColumns&&0!==p.tableColumns.length?(0,r.jsx)("div",{className:"overflow-auto max-h-[70vh]",children:(0,r.jsxs)(u.XI,{className:"border-collapse border border-amber-700",children:[(0,r.jsxs)(u.A0,{className:"bg-amber-100",children:[(0,r.jsxs)(u.Hj,{children:[(0,r.jsx)(u.nd,{className:"px-3 py-2 text-xs font-medium text-gray-700 uppercase tracking-wider border border-amber-700 bg-amber-100",rowSpan:x.hasChildColumns?2:1,children:s}),x.parentColumns.map(e=>{let t=(x.columnMap.get(e.id)||[]).length||1;return(0,r.jsx)(u.nd,{colSpan:t,className:"px-3 py-2 text-xs font-medium text-gray-700 uppercase tracking-wider text-center border border-amber-700 bg-amber-100",children:e.columnName},e.id)})]}),x.hasChildColumns&&(0,r.jsx)(u.Hj,{children:x.parentColumns.map(e=>{let t=x.columnMap.get(e.id)||[];return 0===t.length?null:t.map(e=>(0,r.jsx)(u.nd,{className:"px-3 py-2 text-xs font-medium text-gray-700 uppercase tracking-wider border border-amber-700 bg-amber-50",children:e.columnName},e.id))})})]}),(0,r.jsx)(u.BF,{children:p.tableRows?.map((e,t)=>(0,r.jsxs)(u.Hj,{className:"bg-white",children:[(0,r.jsx)(u.nA,{className:"px-3 py-2 text-xs font-medium border border-amber-700 bg-amber-50",children:e.rowsName}),x.parentColumns.map(t=>{let s=x.columnMap.get(t.id)||[];return 0===s.length?(0,r.jsx)(u.nA,{className:"px-3 py-2 text-xs border border-amber-700",children:b.get(`${t.id}_${e.id}`)||""},`cell-${t.id}-${e.id}`):s.map(t=>(0,r.jsx)(u.nA,{className:"px-3 py-2 text-xs border border-amber-700",children:b.get(`${t.id}_${e.id}`)||""},`cell-${t.id}-${e.id}`))})]},e.id))})]})}):(0,r.jsxs)("div",{className:"py-4 text-center text-amber-600",children:[(0,r.jsx)("p",{children:"No table structure available."}),(0,r.jsx)("p",{className:"text-sm mt-2",children:"Debug info:"}),(0,r.jsx)("pre",{className:"text-xs mt-2 bg-gray-100 p-2 rounded overflow-auto max-h-40",children:JSON.stringify({hasTableStructure:!!p,tableColumnsLength:p?.tableColumns?.length||0,tableRowsLength:p?.tableRows?.length||0,tableDataLength:a?.length||0,useParentChildColumns:i},null,2)})]}),(0,r.jsx)("div",{className:"flex justify-end mt-4",children:(0,r.jsx)("button",{onClick:t,className:"px-4 py-2 bg-primary-500 text-white rounded-md hover:bg-primary-600 transition-colors",children:"Close"})})]})})};var p=s(12810),h=s(76847);let x=(e,t)=>{if(null==e)return"-";if("boolean"==typeof e)return e?"Yes":"No";if(e instanceof Date)return(0,o.Y)(e);if("date"===t&&"string"==typeof e)try{return(0,o.Y)(new Date(e))}catch{return e}return String(e)},b=async e=>{try{let t=null;try{let{data:s}=await p.A.get(`/table-questions/${e}`);s&&s.success&&s.data&&(t=s.data.question)}catch(e){console.error("Error fetching from /table-questions/ endpoint:",e)}if(t&&t.tableColumns&&!t.tableRows)try{let{data:s}=await p.A.get(`/table-rows/${e}`);s&&s.data&&s.data.tableRows&&(t.tableRows=s.data.tableRows)}catch(e){console.error("Error fetching table rows separately:",e)}if(!t)return{id:e,label:"Table Data",tableColumns:[],tableRows:[{id:1,rowsName:"Row 1"},{id:2,rowsName:"Row 2"},{id:3,rowsName:"Row 3"}]};if(t.tableColumns&&Array.isArray(t.tableColumns)){let e=[];t.tableColumns.forEach(t=>{e.push({id:t.id,columnName:t.columnName,parentColumnId:t.parentColumnId||null}),t.childColumns&&Array.isArray(t.childColumns)&&t.childColumns.forEach(s=>{e.push({id:s.id,columnName:s.columnName,parentColumnId:s.parentColumnId||t.id})})}),t.tableColumns=e}else console.error("tableColumns is missing or not an array, creating default tableColumns"),t.tableColumns=[];return t.tableRows&&Array.isArray(t.tableRows)||(console.error("tableRows is missing or not an array, creating default tableRows"),t.tableColumns&&t.tableColumns.length>0?t.tableRows=t.tableColumns.map(e=>({id:e.id,rowsName:`Row ${e.id}`})):t.tableRows=[{id:1,rowsName:"Row 1"},{id:2,rowsName:"Row 2"},{id:3,rowsName:"Row 3"}]),t}catch(t){return console.error("Error fetching table structure:",t),{id:e,label:"Table Data",tableColumns:[],tableRows:[{id:1,rowsName:"Row 1"},{id:2,rowsName:"Row 2"},{id:3,rowsName:"Row 3"}]}}},f=(e,t,s,o)=>{let d=[{id:"select",header:({table:e})=>(0,r.jsx)(a.S,{className:"w-5 h-5 data-[state=checked]:bg-white data-[state=checked]:text-primary-500 border-neutral-100 cursor-pointer",checked:e.getIsAllPageRowsSelected()||e.getIsSomePageRowsSelected()&&"indeterminate",onCheckedChange:t=>e.toggleAllPageRowsSelected(!!t),"aria-label":"Select all"}),cell:({row:e})=>(0,r.jsx)(a.S,{className:"w-5 h-5 bg-neutral-100 border-neutral-400 data-[state=checked]:bg-white data-[state=checked]:text-primary-500 cursor-pointer",checked:e.getIsSelected(),onCheckedChange:t=>e.toggleSelected(!!t),"aria-label":"Select row"}),enableHiding:!1},{id:"id",header:({column:e})=>(0,r.jsxs)("div",{className:"flex items-center hover:text-neutral-300",children:[(0,r.jsx)("span",{children:"ID"}),(0,r.jsx)("span",{className:"ml-2 flex-shrink-0 w-4 h-4",children:(0,r.jsx)(l,{className:"w-full h-full",onClick:()=>e.toggleSorting("asc"===e.getIsSorted())})})]}),accessorFn:(e,t)=>t+1,enableSorting:!0,cell:({row:t})=>(0,r.jsxs)("div",{className:"flex items-center gap-8 font-medium text-neutral-700",children:[t.index+1,(0,r.jsxs)("span",{className:"flex items-center gap-2",children:[(0,r.jsx)(n.A,{onClick:()=>e(t.original),className:"w-4 h-4 cursor-pointer hover:text-primary-500"}),(0,r.jsx)(h.JBV,{className:"w-4 h-4 cursor-pointer hover:text-primary-500",title:"Edit",onClick:()=>{let e=t.original.id;e&&s&&window.open(`/edit-submission/${s}/${e}`,"_blank")}})]})]})},{id:"validation",header:"Validation",accessorKey:"validation"}],c=[];if(o&&o.length>0)c=o;else{if(!(t&&t.answers?.length))return d;c=Array.from(new Map(t.answers.map(e=>[e.question.id,e.question])).values())}return[...d,...c.map(e=>({id:`${e.label}`,header:({column:t})=>(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{children:e.label}),(0,r.jsx)(l,{className:"ml-1 h-4 w-4 cursor-pointer opacity-60",onClick:()=>t.toggleSorting("asc"===t.getIsSorted())})]}),accessorFn:t=>{let s=t.answers.filter(t=>t.question?.id===e.id);if(0===s.length)return null;if("selectmany"===e.inputType){let e=s.map(e=>e.value).filter(e=>e&&""!==String(e).trim()).sort();return e.length>0?e.join(", "):null}return s[0]?.value??null},cell:({getValue:t})=>{let s=t();if("table"===e.inputType)try{let t,a="string"==typeof s?s:String(s);if(a.startsWith("[")&&a.includes("{"))try{t=JSON.parse(a)}catch(e){console.error("Failed to parse JSON string:",e),a=a.replace(/\\"/g,'"').replace(/^"/,"").replace(/"$/,"");try{t=JSON.parse(a)}catch(s){console.error("Failed second parse attempt:",s);let e=a.match(/\[([\s\S]*)\]/);if(e&&e[0])try{t=JSON.parse(e[0])}catch(s){console.error("Failed third parse attempt:",s);try{let s=e[0].replace(/'/g,'"');t=JSON.parse(s)}catch(e){console.error("Failed fourth parse attempt:",e)}}}}if(!t&&a.includes("columnId")&&a.includes("rowsId"))try{let e=a.replace(/'/g,'"');t=JSON.parse(e)}catch(e){console.error("Failed custom format parsing:",e)}if(Array.isArray(t)){let s=t.map(e=>{let t="";e.columnName?t=e.columnName:e.column&&e.column.columnName?t=e.column.columnName:e.columnId&&(t=e.name?e.name:e.label?e.label:String(e.columnId));let s="";e.rowsName?s=e.rowsName:e.row&&e.row.rowsName?s=e.row.rowsName:e.rowsId&&(s=e.name?e.name:e.label?e.label:String(e.rowsId));let r=void 0!==e.value?e.value:"";return{column:t,row:s,value:r}}),a=new Map,l=new Set;s.forEach(e=>{l.add(String(e.column)),a.has(String(e.row))||a.set(String(e.row),new Map),a.get(String(e.row))?.set(String(e.column),String(e.value))});let o=Array.from(l),d=Array.from(a.keys());return(0,r.jsx)(()=>{let[t,l]=(0,i.useState)(!1),[c,u]=(0,i.useState)(null),[p,h]=(0,i.useState)(!1);(0,i.useEffect)(()=>{},[c]);let x=async()=>{if(!e.id)return void console.error("No question ID available");h(!0);try{let t=await b(e.id);if(t&&(t.tableRows||console.error("tableRows is missing from the structure!"),u(t),t.tableColumns&&t.tableRows)){let e=new Map,r=new Map;t.tableColumns.forEach(t=>{e.set(t.id,t.columnName),e.set(String(t.id),t.columnName)}),t.tableRows.forEach(e=>{r.set(e.id,e.rowsName),r.set(String(e.id),e.rowsName)}),s.forEach(s=>{if(s.column&&!isNaN(Number(s.column))){let r=s.column;if(e.has(r))s.column,s.column=e.get(r);else{let e=t.tableColumns.find(e=>String(e.id)===String(r));e&&(s.column,s.column=e.columnName)}}if(s.row&&!isNaN(Number(s.row))){let e=s.row;if(r.has(e))s.row,s.row=r.get(e);else{let r=t.tableRows.find(t=>String(t.id)===String(e));r&&(s.row,s.row=r.rowsName)}}});let l=new Map,n=new Set;s.forEach(e=>{n.add(String(e.column)),l.has(String(e.row))||l.set(String(e.row),new Map),l.get(String(e.row))?.set(String(e.column),String(e.value))}),o.length=0,d.length=0,n.forEach(e=>o.push(e)),l.forEach((e,t)=>d.push(t)),a.clear(),l.forEach((e,t)=>{a.set(t,e)})}}catch(e){console.error("Error fetching table structure:",e)}finally{h(!1)}};return(0,r.jsxs)("div",{className:"font-medium text-neutral-700",children:[(0,r.jsxs)("a",{href:"#",onClick:async e=>{e.preventDefault(),h(!0),l(!0),await x()},className:"inline-flex items-center gap-1 text-primary-500 hover:text-primary-700 hover:underline whitespace-nowrap",children:[(0,r.jsx)(n.A,{size:12,className:"inline"})," Click to view table"]}),null,(0,r.jsx)(m,{isOpen:t,onClose:()=>l(!1),title:e.label||"Table Data",tableData:s,uniqueColumns:o,uniqueRows:d,rowsMap:a,useParentChildColumns:!0,loading:p,tableStructure:c})]})},{})}}catch(e){console.error("Error parsing table data:",e,"Value:",s)}return null==s||""===s?(0,r.jsx)("div",{className:"font-medium text-neutral-400 italic",children:"-"}):(0,r.jsx)("div",{className:"font-medium text-neutral-700",children:String(s)})},enableSorting:!0})),{id:"submissionTime",header:({column:e})=>(0,r.jsxs)("div",{className:"flex items-center gap-4 hover:text-neutral-300",children:[(0,r.jsx)("span",{children:"Submission Time"}),(0,r.jsx)("span",{className:"ml-2 flex-shrink-0 w-4 h-4",children:(0,r.jsx)(l,{className:"w-full h-full",onClick:()=>e.toggleSorting("asc"===e.getIsSorted())})})]}),accessorKey:"submissionTime",cell:({getValue:e})=>{let t=e();return(0,r.jsx)("div",{className:"font-medium text-neutral-700",children:x(t,"date")||"Not recorded"})},enableSorting:!0},{id:"submittedBy",header:({column:e})=>(0,r.jsxs)("div",{className:"flex items-center gap-4 hover:text-neutral-300",children:[(0,r.jsx)("span",{children:"Submitted By"}),(0,r.jsx)("span",{className:"ml-2 flex-shrink-0 w-4 h-4",children:(0,r.jsx)(l,{className:"w-full h-full",onClick:()=>e.toggleSorting("asc"===e.getIsSorted())})})]}),accessorKey:"submittedBy",accessorFn:e=>e.user?.name||"Anonymous",cell:({getValue:e})=>{let t=e();return(0,r.jsx)("div",{className:"font-medium text-neutral-700",children:t})},enableSorting:!0}]};var g=s(68988),y=s(44255),j=s(69587),w=s(26273),v=s(56090),N=s(93772),C=s(54864),S=s(19150),q=s(80967),I=s(8693),k=s(54050),A=s(77618);let E=e=>{if(!e||!e.answers)return[];let t=new Map;return e.answers.forEach(e=>{let s=e.question?.id,r=e.question?.label||"unknown";if(s)if(t.has(s)){let r=t.get(s);r.answers.push(e.value),r.originalData.push(e)}else t.set(s,{type:e.question?.inputType||"text",question:r,questionObject:e.question,answers:[e.value],originalData:[e]})}),Array.from(t.values())},O=({showModal:e,onClose:t,onConfirm:s,submission:a,isMultipleSelection:l=!1,selectedSubmissions:n=[],projectId:o})=>{let[m,p]=(0,i.useState)(!1),[h,x]=(0,i.useState)(null),[b,f]=(0,i.useState)(""),[y,j]=(0,i.useState)(null),w=(0,C.wA)(),O=(0,I.jE)(),$=(0,A.c3)(),R=(0,i.useRef)(null),M=(0,i.useRef)(null),[D,P]=(0,i.useState)(""),[F,T]=(0,i.useState)(""),K=d().useMemo(()=>{if(l&&y){let e=n.find(e=>e.id===y);return e?E(e):[]}return E(a)},[a,l,y,n]);(0,i.useEffect)(()=>{if(l&&n.length>0&&!y){let e=n[0]?.id;void 0!==e&&j(e)}},[l,n,y]);let U=d().useMemo(()=>K.filter(e=>{let t=e.question.toLowerCase().includes(D.toLowerCase())||!D,s=String(e.answers).toLowerCase().includes(F.toLowerCase())||!F;return t&&s}),[K,D,F]),B=(0,v.N4)({data:U,columns:[{accessorKey:"type",header:$("type")},{accessorKey:"question",header:()=>(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{children:"Question"}),(0,r.jsx)(g.p,{ref:R,placeholder:$("searchQuestions"),value:D,onChange:e=>P(e.target.value),className:"bg-neutral-100 text-neutral-700 mt-2 h-8"})]})},{accessorKey:"answers",header:()=>(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{children:"Answer"}),(0,r.jsx)(g.p,{ref:M,placeholder:$("searchAnswers"),value:F,onChange:e=>T(e.target.value),className:"bg-neutral-100 text-neutral-700 mt-2 h-8"})]}),cell:({row:e})=>{let t=e.original.answers;return l?(0,r.jsx)("div",{className:"flex flex-col",children:(0,r.jsx)("div",{className:"text-neutral-800 italic",children:$("multipleResponses")})}):(0,r.jsx)("div",{className:"flex flex-col",children:t.map((e,t)=>(0,r.jsx)("div",{className:"",children:String(e)},t))})}},{accessorKey:"action",header:$("action"),cell:({row:e})=>(0,r.jsx)("button",{className:"btn-primary",onClick:()=>V(e.original),children:$("edit")})}],getCoreRowModel:(0,N.HT)()}),V=e=>{p(!0),x(e),f(e.answers.join("\n"))},_=(0,k.n)({mutationFn:e=>{if(!e.questionId)throw Error($("questionIdRequired"));if(!e.submissionId)throw Error($("submissionIdRequired"));let t={submissionId:e.submissionId,questionId:e.questionId,answerType:e.answerType,value:e.value};return"selectmany"===e.answerType?t.questionOptionId=Array.isArray(e.questionOptionId)?e.questionOptionId:e.questionOptionId?[e.questionOptionId]:[]:void 0!==e.questionOptionId&&(t.questionOptionId=Array.isArray(e.questionOptionId)?e.questionOptionId[0]:e.questionOptionId),"number"===e.answerType||"decimal"===e.answerType?t.value="string"==typeof e.value?parseFloat(e.value):"number"==typeof e.value?e.value:0:"selectmany"===e.answerType?t.value=Array.isArray(e.value)?e.value.map(e=>String(e)):[String(e.value)]:t.value=String(e.value),(0,q.s4)(t,o)},onSuccess:e=>{w((0,S.Ds)({message:$("answerUpdated"),type:"success"})),O.invalidateQueries({queryKey:["formSubmissions"]}),p(!1),x(null),f(""),s()},onError:e=>{console.error("Error updating answer:",e),console.error("Error details:",{response:e?.response?.data,status:e?.response?.status,headers:e?.response?.headers});let t=e?.response?.data?.message||e?.response?.data?.errors||"Failed to update answer";w((0,S.Ds)({message:"string"==typeof t?t:$("validationError"),type:"error"}))}}),z=async()=>{if(!h)return;let e=l&&y&&n.find(e=>e.id===y)||a;if(!e?.id)return void w((0,S.Ds)({message:$("submissionIdMissing"),type:"error"}));let t=b.split("\n").map(e=>e.trim()).filter(e=>e);if(0===t.length)return void w((0,S.Ds)({message:$("enterValidResponse"),type:"error"}));let s=h.questionObject?.id;if(!s)return void w((0,S.Ds)({message:$("questionIdMissing"),type:"error"}));let r=h.type||"text";try{let a;if("selectmany"===r){let e=h.originalData?.map(e=>e.questionOptionId).filter(Boolean);if(e&&0!==e.length)if(e.length!==t.length)for(a=[...e];a.length<t.length;)a.push(a[0]||null);else a=e;else a=t.map(()=>null)}else a=h.originalData?.[0]?.questionOptionId||null;let l={submissionId:e.id,questionId:s,answerType:r,value:"selectmany"===r?t:t[0],questionOptionId:a};_.mutate(l)}catch(e){console.error("Form validation error:",e),w((0,S.Ds)({message:$("checkInputTryAgain"),type:"error"}))}};return(0,r.jsx)(r.Fragment,{children:(0,r.jsx)(c.A,{isOpen:e,onClose:t,className:"flex flex-col gap-5 p-6 rounded-md",children:m?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("h2",{className:"text-lg font-semibold text-neutral-700",children:[$("editingQuestion"),": ",h?.question]}),(0,r.jsx)("p",{className:"text-sm text-neutral-700",children:$("editingMultipleInfo")}),(0,r.jsx)("textarea",{value:b,onChange:e=>f(e.target.value),className:"mt-4 border border-neutral-400 rounded-md p-2 w-full h-24 focus:outline-none focus:ring-2 focus:ring-primary-500",placeholder:$("enterNewResponse")}),(0,r.jsxs)("div",{className:"flex justify-end gap-4 mt-6",children:[(0,r.jsx)("button",{className:"btn-outline",onClick:()=>{p(!1),x(null),f("")},disabled:_.isPending,children:$("cancel")}),(0,r.jsx)("button",{className:"btn-primary",onClick:z,disabled:_.isPending,children:_.isPending?$("saving"):$("confirmAndSave")})]})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"flex flex-col gap-4 max-h-[500px] overflow-y-auto",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-neutral-700",children:l?$("editSelectedSubmission"):$("editSubmission")}),(0,r.jsx)("div",{children:l&&n.length>0&&(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("label",{htmlFor:"submission-selector",className:"text-sm font-medium text-neutral-700",children:$("selectSubmissionToEdit")}),(0,r.jsx)("select",{id:"submission-selector",className:"border border-neutral-300 rounded-md p-1 text-sm bg-white",value:y||"",onChange:e=>{j(Number(e.target.value))},children:n.map(e=>(0,r.jsxs)("option",{value:e.id,children:[$("id"),": ",e.id]},e.id))})]})})]}),l&&y&&(0,r.jsxs)("div",{className:"bg-primary-500 border text-neutral-100 rounded-md p-3 text-sm",children:[(0,r.jsxs)("p",{className:"font-medium",children:[$("editingSubmissionId"),": ",y]}),(0,r.jsx)("p",{className:"text-xs mt-1",children:$("editingOneFromMultiple")})]}),(0,r.jsx)("p",{className:"text-sm text-neutral-700",children:l?$("multipleSelectedChoose"):$("editingSingle")}),(0,r.jsx)("div",{className:"rounded-md border border-neutral-400 max-h-[450px] overflow-auto",children:(0,r.jsxs)(u.XI,{children:[(0,r.jsx)(u.A0,{className:"h-20",children:B.getHeaderGroups().map(e=>(0,r.jsx)(u.Hj,{className:"text-sm border-neutral-400",children:e.headers.map(e=>(0,r.jsx)(u.nd,{className:"py-1 px-6 text-left bg-primary-500 text-neutral-100 font-semibold",children:e.isPlaceholder?null:(0,v.Kv)(e.column.columnDef.header,e.getContext())},e.id))},e.id))}),(0,r.jsx)(u.BF,{children:B.getRowModel().rows?.length?B.getRowModel().rows.map(e=>(0,r.jsx)(u.Hj,{className:" text-sm border-neutral-400","data-state":e.getIsSelected()&&"selected",children:e.getVisibleCells().map(e=>(0,r.jsx)(u.nA,{className:"py-4 px-6",children:(0,v.Kv)(e.column.columnDef.cell,e.getContext())},e.id))},e.id)):(0,r.jsx)(u.Hj,{children:(0,r.jsx)(u.nA,{colSpan:B.getAllColumns().length,className:"h-24 text-center",children:$("noResults")})})})]})}),(0,r.jsxs)("div",{className:"flex justify-end gap-4 mt-6",children:[(0,r.jsx)("button",{className:"btn-outline",onClick:t,children:$("cancel")}),(0,r.jsx)("button",{className:"btn-primary",onClick:z,disabled:_.isPending,children:_.isPending?$("saving"):$("confirmAndSave")})]})]})})})};var $=s(93617);let R=({isOpen:e,onClose:t,submission:s})=>{let[a,l]=(0,i.useState)(!1),n=(0,A.c3)(),o=(0,i.useRef)(null),d=new Map;return s.answers.forEach(e=>{let t=e.question.label;d.has(t)||d.set(t,[]),d.get(t).push(e.value)}),(0,r.jsx)(c.A,{isOpen:e,onClose:t,className:"p-6 rounded-md max-w-4xl w-full ",children:(0,r.jsxs)("div",{ref:o,className:`flex flex-col gap-4 transition-all duration-300 ${a?"fixed inset-0 bg-neutral-100 w-screen h-screen z-50 p-6 overflow-auto":""}`,children:[(0,r.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-neutral-700",children:n("submissionDetails")}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{className:"text-sm text-neutral-600",children:"Validation status:"}),(0,r.jsxs)("select",{className:"px-3 py-1 border border-neutral-500 rounded-md text-sm",children:[(0,r.jsx)("option",{value:"",children:n("select")}),(0,r.jsx)("option",{value:"valid",children:n("valid")}),(0,r.jsx)("option",{value:"invalid",children:n("notValid")}),(0,r.jsx)("option",{value:"pending",children:n("pending")})]}),(0,r.jsxs)("button",{onClick:()=>{l(e=>!e)},className:"btn-primary",children:[(0,r.jsx)(w.D4o,{className:"w-5 h-5"}),a?n("exitFullscreen"):n("fullscreen")]})]})]}),(0,r.jsx)("div",{className:"overflow-x-auto rounded-md border border-neutral-200 bg-neutral-100",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-neutral-200",children:[(0,r.jsx)("thead",{className:"bg-primary-500 text-neutral-100",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium  uppercase tracking-wider",children:n("question")}),(0,r.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium uppercase tracking-wider",children:n("response")})]})}),(0,r.jsx)("tbody",{className:"bg-neutral-100 divide-y divide-neutral-200",children:[...d.entries()].map(([e,t])=>{let a=s.answers.find(t=>t.question.label===e),l=a?.question.inputType==="table";return(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{className:"px-4 py-2 align-top",children:e}),(0,r.jsx)("td",{className:"px-4 py-2",children:l?(0,r.jsx)("div",{className:"text-neutral-600 italic",children:n("tableDataNote")}):t.join(", ")})]},e)})})]})}),(0,r.jsxs)("div",{className:"flex justify-between items-center mt-4",children:[(0,r.jsxs)("div",{className:"text-sm text-neutral-600 font-semibold",children:[(0,r.jsxs)("p",{children:[n("submittedBy"),": ",s.user?.name]}),(0,r.jsxs)("p",{children:[n("submissionTime"),": ",s.submissionTime]})]}),(0,r.jsx)("div",{children:(0,r.jsx)("button",{className:"btn-primary",onClick:t,children:n("close")})})]})]})})};var M=s(75531),D=s(55629),P=s(24934),F=s(73678),T=s(16189),K=s(6986),U=s(29494);let B=async e=>{let{data:t}=await p.A.get(`/form-submissions/${e}`);return t.data.formSubmissions},V="data-table-column-visibility",_=()=>{let{hashedId:e}=(0,T.useParams)(),t=(0,A.c3)(),s=Number((0,K.D)(e)),a=(0,C.wA)(),l=(0,I.jE)(),{data:n=[],isLoading:o,refetch:c}=(0,U.I)({queryKey:["formSubmissions",s],queryFn:()=>B(s),enabled:null!==s,refetchInterval:1e3,staleTime:0,gcTime:0}),{data:u=[],isLoading:m}=(0,U.I)({queryKey:["allQuestions",s],queryFn:()=>(0,M.K4)({projectId:s}),enabled:null!==s,staleTime:3e5}),[p,x]=(0,i.useState)(!1),[b,v]=(0,i.useState)(!1),[N,E]=(0,i.useState)(""),[_,z]=(0,i.useState)(!1),[H,J]=d().useState(null),[L,Q]=(0,i.useState)(!1),[G,Y]=d().useState({}),[X,W]=d().useState(null),[Z,ee]=d().useState({}),[et,es]=(0,i.useState)(!1),[er,ea]=(0,i.useState)(null),el=n.length>0&&u.length>0?f(e=>{ea(e),es(!0)},n[0],e,u):[],[en,eo]=d().useState(!1),[ei,ed]=(0,i.useState)(!1),ec=(0,i.useRef)(null),eu=(0,i.useRef)(null),em=(0,k.n)({mutationFn:e=>(0,q.O8)(e,s),onSuccess:()=>{a((0,S.Ds)({message:t("submissionDeleted"),type:"success"})),l.invalidateQueries({queryKey:["formSubmissions",s]}),ee({}),z(!1),ea(null),v(!1)},onError:e=>{console.error("Error deleting submission:",e),a((0,S.Ds)({message:t("submissionDeleteFailed"),type:"error"})),v(!1)}}),ep=(0,k.n)({mutationFn:e=>(0,q.J6)(e,s),onSuccess:(e,r)=>{a((0,S.Ds)({message:`${r.length} ${t("submissionsDeleted")}`,type:"success"})),l.invalidateQueries({queryKey:["formSubmissions",s]}),ee({}),z(!1),ea(null),v(!1)},onError:e=>{console.error("Error deleting multiple submissions:",e),a((0,S.Ds)({message:t("submissionsDeleteFailed"),type:"error"})),v(!1)}});return(0,i.useEffect)(()=>{let e=e=>{ec.current&&!ec.current.contains(e.target)&&ed(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]),(0,i.useEffect)(()=>{try{let e=localStorage.getItem(V);if(e){let t=JSON.parse(e);t&&"object"==typeof t&&!Array.isArray(t)?Y(t):console.warn("Invalid format in localstorage for column visibility")}}catch(e){console.error("Error loading column visibility:",e)}},[]),(0,i.useEffect)(()=>{if(Object.keys(G).length>0)try{localStorage.setItem(V,JSON.stringify(G))}catch(e){console.error("Error saving column visibility:",e)}},[G]),(0,i.useEffect)(()=>{X&&0===Object.keys(Z).length&&X.resetRowSelection()},[Z,X]),(0,r.jsxs)("div",{ref:eu,className:`flex flex-col gap-4 transition-all duration-300 ${p?"fixed inset-0 bg-neutral-100 w-screen h-screen z-50 p-6 overflow-auto":""}`,children:[(0,r.jsxs)("div",{className:"flex flex-col desktop:flex-row justify-between gap-8 items-center py-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)(g.p,{placeholder:t("searchAllColumns"),value:N,onChange:e=>E(e.target.value)}),X&&(0,r.jsxs)(D.rI,{open:en,onOpenChange:e=>eo(e),children:[(0,r.jsx)(D.ty,{asChild:!0,children:(0,r.jsxs)(P.$,{variant:"outline",className:"flex items-center gap-2 cursor-pointer",children:[t("showHideColumns"),en?(0,r.jsx)(j.Ucs,{className:"w-3 h-3"}):(0,r.jsx)(j.Vr3,{className:"w-3 h-3"})]})}),(0,r.jsx)(D.SQ,{align:"start",className:"bg-neutral-100 border border-neutral-200 shadow-md",children:X.getAllColumns().filter(e=>e.getCanHide()).map(e=>(0,r.jsx)(D.hO,{className:"capitalize cursor-pointer hover:bg-neutral-200",checked:G[e.id]??!0,onCheckedChange:t=>Y(s=>({...s,[e.id]:t})),children:e.id},e.id))})]})]}),(0,r.jsxs)("div",{ref:ec,className:"flex relative items-center gap-4 text-neutral-800",children:[(0,r.jsxs)("button",{onClick:()=>{x(e=>!e)},className:"btn-primary",children:[(0,r.jsx)(w.D4o,{className:"w-5 h-5"}),p?t("exitFullscreen"):t("fullscreen")]}),(0,r.jsxs)("button",{className:` bg-primary-500 font-medium rounded-lg shadow-md text-neutral-100 flex items-center justify-center gap-2 px-4 py-2  active:scale-95 transition-all duration-300 ${_?"hover:bg-primary-600  cursor-pointer":"opacity-50"}`,onClick:_?()=>{ed(e=>!e)}:void 0,children:[t("status"),ei?(0,r.jsx)(j.Ucs,{className:"w-3 h-3"}):(0,r.jsx)(j.Vr3,{className:"w-3 h-3"})]}),ei&&(0,r.jsx)("div",{className:"absolute left-30 top-10 mt-2 w-64 bg-neutral-100 border border-gray-200 shadow-md rounded-md p-2 z-40",children:(0,r.jsxs)("div",{className:"flex flex-col  gap-2",children:[(0,r.jsx)("div",{className:"hover:bg-neutral-200 cursor-pointer p-2 text-neutral-800 rounded-sm",children:t("setOnApproved")}),(0,r.jsx)("div",{className:"hover:bg-neutral-200 cursor-pointer p-2 text-neutral-800 rounded-sm",children:t("setOnNotApproved")}),(0,r.jsx)("div",{className:"hover:bg-neutral-200 cursor-pointer p-2 text-neutral-800 rounded-sm",children:t("setOnHold")})]})}),(0,r.jsxs)("button",{className:` bg-primary-500 font-medium rounded-lg shadow-md text-neutral-100 flex items-center justify-center gap-2 px-4 py-2  active:scale-95 transition-all duration-300 ${_?"hover:bg-primary-600  cursor-pointer":"opacity-50"}`,onClick:_?()=>{let e=Object.keys(Z);if(0===e.length)return void a((0,S.Ds)({message:t("noSubmissionSelected"),type:"error"}));let s=e.map(e=>n[Number(e)]).filter(Boolean);if(1===e.length){ea(s[0]),Q(!0);return}e.length>1&&(ea(s[0]),Q(!0))}:void 0,children:[(0,r.jsx)(h.JBV,{className:"h-4 w-4"}),t("edit")]}),(0,r.jsxs)("button",{className:` bg-primary-500 font-medium rounded-lg shadow-md text-neutral-100 flex items-center justify-center gap-2 px-4 py-2  active:scale-95 transition-all duration-300 ${_?"hover:bg-primary-600  cursor-pointer":"opacity-50"}`,onClick:_?()=>{let e=Object.keys(Z).map(e=>{let t=parseInt(e);return n[t]?.id||0}).filter(e=>e>0);J({title:t("confirmDeletion"),description:(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)("p",{children:[t("areYouSureToDelete"),e.length>1?`${t("these")} ${e.length} ${t("submissions")}`:t("thisSubmission"),"? ",t("cannotRecoverSubmissions")]})}),confirmButtonText:t("delete"),confirmButtonClass:"bg-red-500 hover:bg-red-600 cursor-pointer",onConfirm:()=>{1===e.length?em.mutate(e[0]):e.length>1&&ep.mutate(e)}}),v(!0)}:void 0,children:[(0,r.jsx)(y.hJ0,{className:"h-4 w-4"}),t("delete")]})]})]}),o||m?(0,r.jsx)("div",{className:"flex justify-center items-center py-12",children:(0,r.jsx)("div",{className:"text-muted-foreground",children:t("loadingData")})}):(0,r.jsx)($.x,{columns:el,data:n,globalFilter:N,setGlobalFilter:E,onTableInit:e=>{W(e),Object.keys(G).length>0&&e.setColumnVisibility(G)},columnVisibility:G,setColumnVisibility:e=>{Y(e)},onRowSelectionChange:e=>{z(Object.keys(e).length>0),ee(e),1===Object.keys(e).length?ea(n[Number(Object.keys(e)[0])]):0===Object.keys(e).length&&ea(null)},rowSelection:Z}),L&&er&&(0,r.jsx)(O,{showModal:L,projectId:s,onClose:()=>{Q(!1),ea(null),ee({}),z(!1)},onConfirm:()=>{l.invalidateQueries({queryKey:["formSubmissions",s]}),ee({}),z(!1),Q(!1),ea(null)},submission:er,isMultipleSelection:Object.keys(Z).length>1,selectedSubmissions:Object.keys(Z).length>1?Object.keys(Z).map(e=>n[Number(e)]).filter(Boolean):[]}),H&&(0,r.jsx)(F.R,{showModal:b,onClose:()=>v(!1),onConfirm:H.onConfirm,title:H.title,description:H.description,confirmButtonText:H.confirmButtonText,confirmButtonClass:H.confirmButtonClass}),er&&(0,r.jsx)(R,{isOpen:et,onClose:()=>es(!1),submission:er})]})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56461:(e,t,s)=>{Promise.resolve().then(s.bind(s,51219))},61611:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64352:(e,t,s)=>{Promise.resolve().then(s.bind(s,87282))},73678:(e,t,s)=>{"use strict";s.d(t,{R:()=>l});var r=s(60687);s(43210);var a=s(38587);let l=({showModal:e,onClose:t,onConfirm:s,title:l,description:n,confirmButtonText:o,cancelButtonText:i,confirmButtonClass:d,children:c})=>(0,r.jsxs)(a.A,{isOpen:e,onClose:t,className:"p-6 rounded-md max-w-xl",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-neutral-700",children:l}),(0,r.jsx)("div",{className:"text-neutral-700 mt-2",children:n}),c&&(0,r.jsx)("div",{className:"mt-6 space-y-4",children:c}),(0,r.jsxs)("div",{className:"flex justify-end gap-4 mt-6",children:[(0,r.jsx)("button",{className:"btn-outline",onClick:t,type:"button",children:i||"Cancel"}),(0,r.jsx)("button",{className:`font-medium rounded-md shadow-md text-neutral-100 flex items-center justify-center gap-2 px-4 py-2 active:scale-95 transition-all duration-300 ${d}`,onClick:s,type:"button",children:o})]})]})},74075:e=>{"use strict";e.exports=require("zlib")},75531:(e,t,s)=>{"use strict";s.d(t,{Af:()=>o,K4:()=>l,ae:()=>m,dI:()=>u,eL:()=>p,ej:()=>n,gf:()=>h,ku:()=>d,sr:()=>c,ul:()=>i});var r=s(12810);let a=e=>{if("project"===e)return"/questions";if("template"===e)return"/template-questions";if("questionBlock"===e)return"/question-blocks";throw Error("Unsupported context type")},l=async({projectId:e})=>{let{data:t}=await r.A.get(`/questions/${e}`);return t.questions},n=async({templateId:e})=>{let{data:t}=await r.A.get(`/template-questions/${e}`);return t.questions},o=async({contextType:e,contextId:t,dataToSend:s,position:l})=>{let n="questionBlock"===e?`${a(e)}`:`${a(e)}/${t}`;if(!s.label||!s.inputType)throw Error("Label and inputType are required");let o=["selectone","selectmany"].includes(s.inputType),i=s.file instanceof File,d=Array.isArray(s.questionOptions)&&s.questionOptions.length>0;if(o&&!i&&!d)throw Error("Options are required for select input types");if(i){let e=new FormData;e.append("label",s.label),e.append("isRequired",s.isRequired?"true":"false"),e.append("inputType",s.inputType),s.hint&&e.append("hint",s.hint),s.placeholder&&e.append("placeholder",s.placeholder),e.append("position",String(l||1)),e.append("file",s.file);try{let{data:t}=await r.A.post(n,e,{headers:{"Content-Type":"multipart/form-data"}});return t}catch(e){throw console.error("Upload error details:",e.response?.data||e.message),Error(`Failed to upload question with file: ${e.response?.data?.message||e.message}`)}}try{let{data:e}=await r.A.post(n,{label:s.label,isRequired:s.isRequired,hint:s.hint,placeholder:s.placeholder,inputType:s.inputType,questionOptions:s.questionOptions,position:l||1});return e}catch(e){throw console.error("API error details:",e.response?.data||e.message),Error(`Failed to add question: ${e.response?.data?.message||e.message}`)}},i=async({contextType:e,id:t,projectId:s})=>{let{data:l}=await r.A.delete(`${a(e)}/${t}?projectId=${s}`);return l},d=async({id:e,contextType:t,contextId:s})=>{let{data:l}=await r.A.post(`${a(t)}/duplicate/${e}?projectId=${s}`,"questionBlock"===t?{}:"project"===t?{projectId:s}:{templateId:s});return l},c=async({id:e,contextType:t,dataToSend:s,contextId:l})=>{let{data:n}=await r.A.patch(`${a(t)}/${e}?projectId=${l}`,s);return n},u=async()=>{try{return(await r.A.get("/question-blocks")).data.questions||[]}catch(e){throw console.error("Error fetching question block questions:",e),e}},m=async({contextType:e,contextId:t,questionPositions:s})=>{if("project"!==e)throw Error("Question position updates are only supported for projects");let l=`${a(e)}/positions?projectId=${t}`;try{let{data:e}=await r.A.patch(l,{questionPositions:s});return e}catch(e){throw console.error("Update failed - Full error:",e),console.error("Update failed - Error details:",{status:e.response?.status,statusText:e.response?.statusText,data:e.response?.data,message:e.message,config:{url:e.config?.url,method:e.config?.method,data:e.config?.data}}),e}},p=async({contextType:e,contextId:t,positionUpdates:s})=>{if("project"!==e)throw Error("Unified position updates are only supported for projects");let l=`${a(e)}/unified-positions?projectId=${t}`;try{let{data:e}=await r.A.patch(l,{positionUpdates:s});return e}catch(e){throw console.error("Unified position update failed - Full error:",e),console.error("Unified position update failed - Error details:",{status:e.response?.status,statusText:e.response?.statusText,data:e.response?.data,message:e.message,config:{url:e.config?.url,method:e.config?.method,data:e.config?.data}}),e}},h=async({projectId:e})=>{let{data:t}=await r.A.get(`/projects/getalldata/${e}`);return t.data}},78407:(e,t,s)=>{"use strict";s.d(t,{F:()=>a});var r=s(43210);let a=({projectData:e,user:t})=>(0,r.useMemo)(()=>{let s=t?.id===e?.user?.id,r=e?.projectUser?.[0],a=r?.permission||{};return{viewForm:s||a.viewForm||!1,editForm:s||a.editForm||!1,viewSubmissions:s||a.viewSubmissions||!1,addSubmissions:s||a.addSubmissions||!1,deleteSubmissions:s||a.deleteSubmissions||!1,validateSubmissions:s||a.validateSubmissions||!1,editSubmissions:s||a.editSubmissions||!1,manageProject:s||a.manageProject||!1}},[t?.id,e])},79551:e=>{"use strict";e.exports=require("url")},80967:(e,t,s)=>{"use strict";s.d(t,{GN:()=>o,J6:()=>l,O8:()=>a,s4:()=>n});var r=s(12810);let a=async(e,t)=>{try{let{data:s}=await r.A.delete(`/form-submissions/${e}?projectId=${t}`);return s}catch(e){throw console.error("Error deleting form submission:",e),e}},l=async(e,t)=>{try{let s=e.map(e=>r.A.delete(`/form-submissions/${e}?projectId=${t}`));return(await Promise.all(s)).map(e=>e.data)}catch(e){throw console.error("Error deleting multiple form submissions:",e),e}},n=async(e,t)=>{try{if(!e.submissionId||!e.questionId)throw Error("submissionId and questionId are required");let s={...e};null===s.questionOptionId?delete s.questionOptionId:Array.isArray(s.questionOptionId)&&(s.questionOptionId=s.questionOptionId.filter(e=>null!=e),0===s.questionOptionId.length&&delete s.questionOptionId);let{data:a}=await r.A.patch(`/answers/${e.questionId}?projectId=${t}`,s);return a}catch(e){throw console.error("Error updating answer:",e),e}},o=async(e,t)=>{try{let{data:s}=await r.A.patch(`/answers/multiple?projectId=${t}`,e);return s}catch(e){throw console.error("Error updating multiple answers with endpoint:",e),e}}},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84027:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},84638:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c});var r=s(60687);s(43210);var a=s(16189),l=s(62688);let n=(0,l.A)("table-2",[["path",{d:"M9 3H5a2 2 0 0 0-2 2v4m6-6h10a2 2 0 0 1 2 2v4M9 3v18m0 0h10a2 2 0 0 0 2-2V9M9 21H5a2 2 0 0 1-2-2V9m0 0h18",key:"gugj83"}]]),o=(0,l.A)("chart-no-axes-column",[["line",{x1:"18",x2:"18",y1:"20",y2:"10",key:"1xfpm4"}],["line",{x1:"12",x2:"12",y1:"20",y2:"4",key:"be30l9"}],["line",{x1:"6",x2:"6",y1:"20",y2:"14",key:"1r4le6"}]]);var i=s(31158),d=s(77618);function c({children:e}){let{hashedId:t}=(0,a.useParams)(),s=(0,d.c3)(),l=[{label:s("data"),href:`/project/${t}/data`,icon:n},{label:s("reports"),href:`/project/${t}/data/reports`,icon:o},{label:s("downloads"),href:`/project/${t}/data/downloads`,icon:i.A}],c=(0,a.usePathname)(),u=(0,a.useRouter)();return(0,r.jsxs)("div",{className:"flex flex-col min-h-screen bg-neutral-100",children:[(0,r.jsxs)("div",{className:"flex justify-between mb-4",children:[(0,r.jsx)("h2",{className:"heading-text",children:s("surveyResults")}),(0,r.jsxs)("div",{className:"",children:[(0,r.jsx)("h2",{className:"flex flex-col text-sm font-medium text-neutral-700 mb-1",children:s("navigate")}),(0,r.jsxs)("select",{value:l.some(e=>e.href===c)?c:"",onChange:e=>{let t=e.target.value;t!==c&&u.push(t)},className:" p-2 border border-neutral-300 rounded-md shadow-sm cursor-pointer",children:[(0,r.jsx)("option",{value:"",children:s(c===`/project/${t}/data`?"datatableOverview":"select")}),l.map(({label:e,href:t})=>(0,r.jsx)("option",{value:t,children:e},e))]})]})]}),(0,r.jsx)("main",{className:"p-4 bg-neutral-100 rounded-md border border-neutral-300 shadow-sm",children:(0,r.jsx)("div",{children:e})})]})}},86757:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("panels-top-left",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M9 21V9",key:"1oto5p"}]])},87282:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Kobo-project\\\\data-analysis-tool-frontend\\\\app\\\\[locale]\\\\(main)\\\\project\\\\[hashedId]\\\\data\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\project\\[hashedId]\\data\\layout.tsx","default")},90541:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>v});var r=s(60687),a=s(86429),l=s(86757),n=s(17090),o=s(61611),i=s(84027),d=s(16189),c=s(20174),u=s(77618);let m=({permissions:e})=>{let{hashedId:t}=(0,d.useParams)(),s=(0,u.c3)(),a=e.manageProject,m=a||e.viewForm||e.editForm,p=a||e.viewSubmissions||e.editSubmissions||e.addSubmissions||e.deleteSubmissions,h=[{label:s("overview"),icon:(0,r.jsx)(l.A,{size:16}),route:`/project/${t}/overview`,disabled:!1},{label:s("formBuilder"),icon:(0,r.jsx)(n.A,{size:16}),route:`/project/${t}/form-builder`,disabled:!m},{label:s("data"),icon:(0,r.jsx)(o.A,{size:16}),route:`/project/${t}/data`,disabled:!p},{label:s("settings"),icon:(0,r.jsx)(i.A,{size:16}),route:`/project/${t}/settings`,disabled:!a}];return(0,r.jsx)(c.F,{items:h})};var p=s(21650),h=s(78407),x=s(71845),b=s(6986),f=s(29494),g=s(28559),y=s(85814),j=s.n(y),w=s(43210);let v=({children:e})=>{let{hashedId:t}=(0,d.useParams)(),{user:s}=(0,p.A)(),l=(0,u.c3)(),n=(0,w.useMemo)(()=>(0,b.D)(t),[t]),{data:o,isLoading:i,isError:c}=(0,f.I)({queryKey:["projects",s?.id,n],queryFn:()=>(0,x.kf)({projectId:n}),enabled:!!n&&!!s?.id}),y=(0,h.F)({projectData:o,user:s});return t&&null!==n?i?(0,r.jsx)(a.A,{}):c?(0,r.jsx)("p",{className:"text-red-500",children:l("fetchProjectFailed")}):(0,r.jsxs)("div",{className:"section flex flex-col gap-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h1",{className:"heading-text capitalize",children:o?.name}),(0,r.jsxs)(j(),{href:"/dashboard",className:"flex items-center gap-2",children:[(0,r.jsx)(g.A,{size:16}),l("backToDashboard")]})]}),(0,r.jsx)(m,{permissions:y}),(0,r.jsx)("div",{className:"px-8",children:e})]}):(0,r.jsxs)("div",{className:"error-message",children:[(0,r.jsx)("h1",{className:"text-red-500",children:l("invalidProjectIdError")}),(0,r.jsx)("p",{className:"text-neutral-700",children:l("invalidProjectIdMessage")})]})}},94735:e=>{"use strict";e.exports=require("events")},95402:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var r=s(65239),a=s(48088),l=s(88170),n=s.n(l),o=s(30893),i={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>o[e]);s.d(t,i);let d={children:["",{children:["[locale]",{children:["(main)",{children:["project",{children:["[hashedId]",{children:["data",{children:["table",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,99870)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\project\\[hashedId]\\data\\table\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,87282)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\project\\[hashedId]\\data\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,51129)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\project\\[hashedId]\\layout.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,84606)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,72121)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,58014)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\project\\[hashedId]\\data\\table\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/(main)/project/[hashedId]/data/table/page",pathname:"/[locale]/project/[hashedId]/data/table",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},99870:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Kobo-project\\\\data-analysis-tool-frontend\\\\app\\\\[locale]\\\\(main)\\\\project\\\\[hashedId]\\\\data\\\\table\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\project\\[hashedId]\\data\\table\\page.tsx","default")}};var t=require("../../../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,7404,1658,6560,7618,63,7605,3851,8581,6886,6226,5233],()=>s(95402));module.exports=r})();