"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.removeLibraryQuestionBlockGroupFromParentGroup = exports.updateLibraryQuestionBlockOneGroupInsideAnotherGroup = exports.updateLibraryQuestionBlockQuestionFromOneGroupToAnother = exports.removeLibraryQuestionBlockQuestionIdFromGroup = exports.deleteLibraryQuestionBlockQuestionAndGroup = exports.deleteLibraryQuestionBlockQuestionGroup = exports.updateLibraryQuestionBlockQuestionGroup = exports.createLibraryQuestionBlockQuestionGroup = void 0;
const prisma_1 = require("../utils/prisma");
const libraryQuestionBlockQuestionGroupValidator_1 = require("../validators/libraryQuestionBlockQuestionGroupValidator");
const libraryQuestionBlockQuestionGroupRepository_1 = __importDefault(require("../repositories/libraryQuestionBlockQuestionGroupRepository"));
const createLibraryQuestionBlockQuestionGroup = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const result = libraryQuestionBlockQuestionGroupValidator_1.libraryQuestionBlockQuestionGroupSchema.safeParse(req.body);
        if (!result.success) {
            return res.status(400).json({
                success: false,
                errors: result.error.flatten().fieldErrors,
            });
        }
        const questionData = result.data;
        const questionGroup = yield libraryQuestionBlockQuestionGroupRepository_1.default.create(questionData);
        return res.status(200).json({
            success: true,
            message: "library question block question group created",
            data: { questionGroup },
        });
    }
    catch (error) {
        return res.status(500).json({
            success: false,
            message: "error creating question group",
            error: error instanceof Error ? error.message : "unexpected error",
        });
    }
});
exports.createLibraryQuestionBlockQuestionGroup = createLibraryQuestionBlockQuestionGroup;
const updateLibraryQuestionBlockQuestionGroup = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const result = libraryQuestionBlockQuestionGroupValidator_1.updateLibraryQuestionBlockQuestionGroupSchema.safeParse(req.body);
        if (!result.success) {
            return res.status(400).json({
                success: false,
                errors: result.error.flatten().fieldErrors,
            });
        }
        const { id, title, order, parentGroupId, selectedQuestionIds } = result.data;
        const updates = {
            title,
            order,
            parentGroupId,
        };
        if (selectedQuestionIds && selectedQuestionIds.length > 0) {
            updates.questionBlockQuestion = {
                set: selectedQuestionIds.map((questionId) => ({
                    id: questionId,
                })),
            };
        }
        const updateQuestionGroup = yield libraryQuestionBlockQuestionGroupRepository_1.default.update(id, updates);
        return res.status(200).json({
            success: true,
            message: "Group question updated successfully",
            data: { updateQuestionGroup },
        });
    }
    catch (error) {
        return res.status(500).json({
            success: false,
            message: "Error updating question group",
            error: error instanceof Error ? error.message : "Unexpected error",
        });
    }
});
exports.updateLibraryQuestionBlockQuestionGroup = updateLibraryQuestionBlockQuestionGroup;
const deleteLibraryQuestionBlockQuestionGroup = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const id = Number(req.params.id);
        if (!id) {
            return res.status(404).json({
                success: false,
                message: "invalid id",
            });
        }
        yield libraryQuestionBlockQuestionGroupRepository_1.default.delete(id);
        return res.status(200).json({
            success: true,
            message: "group deleted sucess",
        });
    }
    catch (error) {
        return res.status(500).json({
            success: false,
            message: "error delete question group",
            error: error instanceof Error ? error.message : "unexpected error",
        });
    }
});
exports.deleteLibraryQuestionBlockQuestionGroup = deleteLibraryQuestionBlockQuestionGroup;
const deleteLibraryQuestionBlockQuestionAndGroup = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const id = Number(req.params.id);
        if (!id) {
            return res.status(404).json({
                success: false,
                message: "invalid id",
            });
        }
        yield libraryQuestionBlockQuestionGroupRepository_1.default.deleteManyQuestionByGroup(id);
        yield libraryQuestionBlockQuestionGroupRepository_1.default.delete(id);
        return res.status(200).json({
            success: true,
            message: "group and question related to that group are delete succesfuly",
        });
    }
    catch (error) {
        return res.status(500).json({
            success: false,
            message: "error delete question group",
            error: error instanceof Error ? error.message : "unexpected error",
        });
    }
});
exports.deleteLibraryQuestionBlockQuestionAndGroup = deleteLibraryQuestionBlockQuestionAndGroup;
const removeLibraryQuestionBlockQuestionIdFromGroup = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { groupId, questionId } = req.body;
        const group = yield prisma_1.prisma.libraryQuestionBlockQuestionGroup.findUnique({
            where: { id: Number(groupId) },
            include: { questionBlockQuestion: true },
        });
        if (!group) {
            return res.status(404).json({
                success: false,
                message: "Question group not found",
            });
        }
        const questionExists = group.questionBlockQuestion.some((q) => q.id === Number(questionId));
        if (!questionExists) {
            return res.status(404).json({
                success: false,
                message: "Question not found in this group",
            });
        }
        yield prisma_1.prisma.libraryQuestionBlockQuestion.update({
            where: { id: Number(questionId) },
            data: { libraryQuestionBlockQuestionGroupId: null }, // 👈 remove its link to the group
        });
        res.status(200).json({
            success: true,
            message: "Question removed from group successfully",
        });
    }
    catch (error) {
        return res.status(500).json({
            success: false,
            message: "error removing question from group",
            error: error instanceof Error ? error.message : "unexpected error",
        });
    }
});
exports.removeLibraryQuestionBlockQuestionIdFromGroup = removeLibraryQuestionBlockQuestionIdFromGroup;
const updateLibraryQuestionBlockQuestionFromOneGroupToAnother = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { groupId, newGroupId, questionId } = req.body;
        if (!groupId || !newGroupId || !questionId) {
            return res.status(404).json({
                success: false,
                message: "id not found",
            });
        }
        const groupid = libraryQuestionBlockQuestionGroupRepository_1.default.findById(groupId);
        if (!groupid) {
            return res.status(404).json({
                success: false,
                message: "group id not found",
            });
        }
        const newGroupid = libraryQuestionBlockQuestionGroupRepository_1.default.findById(newGroupId);
        if (!newGroupid) {
            return res.status(404).json({
                success: false,
                message: "new group id not found",
            });
        }
        const question = yield prisma_1.prisma.libraryQuestionBlockQuestion.findUnique({
            where: { id: Number(questionId) },
        });
        if (!question) {
            return res.status(404).json({
                success: false,
                message: "question id not found",
            });
        }
        if (question.libraryQuestionBlockQuestionGroupId !== Number(groupId)) {
            return res.status(400).json({
                success: false,
                message: "Question does not belong to the old group",
            });
        }
        yield prisma_1.prisma.libraryQuestionBlockQuestion.update({
            where: { id: Number(questionId) },
            data: {
                libraryQuestionBlockQuestionGroupId: Number(newGroupId),
            },
        });
        return res.status(200).json({
            success: true,
            message: "update success",
        });
    }
    catch (error) {
        return res.status(500).json({
            success: false,
            message: "error adding question from one group to another",
            error: error instanceof Error ? error.message : "unexpected error",
        });
    }
});
exports.updateLibraryQuestionBlockQuestionFromOneGroupToAnother = updateLibraryQuestionBlockQuestionFromOneGroupToAnother;
const updateLibraryQuestionBlockOneGroupInsideAnotherGroup = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { childGroupId, ParentGroupId } = req.body;
        const childGroupid = libraryQuestionBlockQuestionGroupRepository_1.default.findById(childGroupId);
        if (!childGroupid) {
            return res.status(404).json({
                success: false,
                message: "group id not found",
            });
        }
        const ParentGroupid = libraryQuestionBlockQuestionGroupRepository_1.default.findById(ParentGroupId);
        if (!ParentGroupid) {
            return res.status(404).json({
                success: false,
                message: "new group id not found",
            });
        }
        const update = yield libraryQuestionBlockQuestionGroupRepository_1.default.updateGroupInsideParentGroup(childGroupId, ParentGroupId);
        return res.status(200).json({
            success: true,
            message: "question Group updated success",
            data: { update },
        });
    }
    catch (error) {
        return res.status(500).json({
            success: false,
            message: "error moving group inside the parentGroup",
            error: error instanceof Error ? error.message : "unexpected error",
        });
    }
});
exports.updateLibraryQuestionBlockOneGroupInsideAnotherGroup = updateLibraryQuestionBlockOneGroupInsideAnotherGroup;
const removeLibraryQuestionBlockGroupFromParentGroup = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { groupId } = req.body;
        const groupid = libraryQuestionBlockQuestionGroupRepository_1.default.findById(groupId);
        if (!groupid) {
            return res.status(400).json({
                success: false,
                message: "Group id is required",
            });
        }
        const group = yield libraryQuestionBlockQuestionGroupRepository_1.default.findById(groupId);
        if (!group) {
            return res.status(404).json({
                success: false,
                message: "Group id not found",
            });
        }
        // Optional: Check if group has a parentGroupId
        if (!group.parentGroupId) {
            return res.status(400).json({
                success: false,
                message: "Group has no parent group to remove",
            });
        }
        yield libraryQuestionBlockQuestionGroupRepository_1.default.RemoveGroupFromParentGroup(groupId);
        return res.status(200).json({
            success: false,
            message: "question remove success",
        });
    }
    catch (error) {
        return res.status(500).json({
            success: false,
            message: "error adding question from one group to another",
            error: error instanceof Error ? error.message : "unexpected error",
        });
    }
});
exports.removeLibraryQuestionBlockGroupFromParentGroup = removeLibraryQuestionBlockGroupFromParentGroup;
