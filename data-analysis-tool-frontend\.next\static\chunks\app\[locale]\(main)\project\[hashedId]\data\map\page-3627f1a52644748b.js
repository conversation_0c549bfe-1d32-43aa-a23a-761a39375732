(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4835],{4516:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(19946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},19946:(e,r,s)=>{"use strict";s.d(r,{A:()=>d});var t=s(12115);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,s)=>s?s.toUpperCase():r.toLowerCase()),n=e=>{let r=l(e);return r.charAt(0).toUpperCase()+r.slice(1)},i=function(){for(var e=arguments.length,r=Array(e),s=0;s<e;s++)r[s]=arguments[s];return r.filter((e,r,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===r).join(" ").trim()};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,t.forwardRef)((e,r)=>{let{color:s="currentColor",size:a=24,strokeWidth:l=2,absoluteStrokeWidth:n,className:o="",children:d,iconNode:x,...u}=e;return(0,t.createElement)("svg",{ref:r,...c,width:a,height:a,stroke:s,strokeWidth:n?24*Number(l)/Number(a):l,className:i("lucide",o),...u},[...x.map(e=>{let[r,s]=e;return(0,t.createElement)(r,s)}),...Array.isArray(d)?d:[d]])}),d=(e,r)=>{let s=(0,t.forwardRef)((s,l)=>{let{className:c,...d}=s;return(0,t.createElement)(o,{ref:l,iconNode:r,className:i("lucide-".concat(a(n(e))),"lucide-".concat(e),c),...d})});return s.displayName=n(e),s}},47924:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(19946).A)("search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},53904:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(19946).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},66932:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(19946).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},78862:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>h});var t=s(95155),a=s(53904),l=s(19946);let n=(0,l.A)("layers",[["path",{d:"M12.83 2.18a2 2 0 0 0-1.66 0L2.6 6.08a1 1 0 0 0 0 1.83l8.58 3.91a2 2 0 0 0 1.66 0l8.58-3.9a1 1 0 0 0 0-1.83z",key:"zw3jo"}],["path",{d:"M2 12a1 1 0 0 0 .58.91l8.6 3.91a2 2 0 0 0 1.65 0l8.58-3.9A1 1 0 0 0 22 12",key:"1wduqc"}],["path",{d:"M2 17a1 1 0 0 0 .58.91l8.6 3.91a2 2 0 0 0 1.65 0l8.58-3.9A1 1 0 0 0 22 17",key:"kqbvx6"}]]);var i=s(66932),c=s(47924),o=s(4516);let d=(0,l.A)("zoom-in",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"11",x2:"11",y1:"8",y2:"14",key:"1vmskp"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]]),x=(0,l.A)("zoom-out",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]]);var u=s(12115);function h(){let[e,r]=(0,u.useState)("");return(0,t.jsxs)("div",{className:"flex flex-col space-y-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("h1",{className:"text-2xl font-semibold text-neutral-800",children:"Map"}),(0,t.jsx)("div",{className:"flex gap-2",children:(0,t.jsxs)("button",{className:"btn-primary",title:"Refresh map",children:[(0,t.jsx)(a.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:"Refresh"})]})})]}),(0,t.jsxs)("div",{className:"flex justify-between gap-4 flex-wrap",children:[(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)("select",{className:"border border-neutral-300 rounded px-3 py-2 bg-neutral-100 text-neutral-800 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 focus:outline-none transition-colors cursor-pointer",children:[(0,t.jsx)("option",{children:"Base Map"}),(0,t.jsx)("option",{children:"Satellite"}),(0,t.jsx)("option",{children:"Street Map"}),(0,t.jsx)("option",{children:"Terrain"})]}),(0,t.jsxs)("button",{className:"btn-primary",children:[(0,t.jsx)(n,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:"Layers"})]}),(0,t.jsxs)("button",{className:"btn-primary",children:[(0,t.jsx)(i.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:"Filter"})]})]}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{type:"text",placeholder:"Search location...",className:"pl-9 pr-4 py-2 border border-neutral-300 rounded w-64 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 focus:outline-none transition-colors",value:e,onChange:e=>r(e.target.value)}),(0,t.jsx)(c.A,{className:"absolute left-3 top-2.5 w-4 h-4 text-neutral-400"})]})]}),(0,t.jsxs)("div",{className:"relative bg-neutral-100 border border-neutral-200 rounded-md h-[500px] shadow-sm overflow-hidden flex items-center justify-center",children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-[url('https://via.placeholder.com/1200x800/f3f4f6/d1d5db?text=Map+Placeholder')] bg-cover bg-center opacity-40"}),(0,t.jsx)("div",{className:"absolute left-1/4 top-1/2 transform -translate-y-1/2",children:(0,t.jsx)(o.A,{className:"w-8 h-8 text-red-500"})}),(0,t.jsx)("div",{className:"absolute left-1/2 top-1/3 transform -translate-x-1/2",children:(0,t.jsx)(o.A,{className:"w-8 h-8 text-red-500"})}),(0,t.jsx)("div",{className:"absolute right-1/4 top-2/3 transform -translate-y-1/2",children:(0,t.jsx)(o.A,{className:"w-8 h-8 text-red-500"})}),(0,t.jsxs)("div",{className:"absolute right-4 top-4 flex flex-col gap-2",children:[(0,t.jsx)("button",{className:"p-2 bg-primary-500 rounded-full shadow hover:bg-primary-600 text-neutral-100 transition-colors cursor-pointer",children:(0,t.jsx)(d,{className:"w-4 h-4"})}),(0,t.jsx)("button",{className:"p-2 bg-primary-500 rounded-full shadow hover:bg-primary-600 text-neutral-100 transition-colors cursor-pointer",children:(0,t.jsx)(x,{className:"w-4 h-4"})})]}),(0,t.jsxs)("div",{className:"relative z-10 bg-neutral-100 px-6 py-4 rounded-lg shadow-lg",children:[(0,t.jsx)("p",{className:"font-medium text-neutral-800",children:"Map will be displayed here"}),(0,t.jsx)("p",{className:"text-sm text-neutral-500",children:"We need to integrate mapping library here."})]})]}),(0,t.jsxs)("div",{className:"bg-neutral-100 rounded-md border border-neutral-200 p-4 shadow-sm",children:[(0,t.jsx)("h3",{className:"font-medium text-neutral-800 mb-2",children:"Legend"}),(0,t.jsxs)("div",{className:"flex items-center gap-2 text-sm text-neutral-600 mb-1",children:[(0,t.jsx)(o.A,{className:"w-4 h-4 text-red-500"}),(0,t.jsx)("span",{children:"Survey Location"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2 text-sm text-neutral-600",children:[(0,t.jsx)("div",{className:"w-4 h-4 bg-primary-300 rounded"}),(0,t.jsx)("span",{children:"High Density Area"})]})]})]})}},88009:(e,r,s)=>{Promise.resolve().then(s.bind(s,78862))}},e=>{var r=r=>e(e.s=r);e.O(0,[8441,1684,7358],()=>r(88009)),_N_E=e.O()}]);