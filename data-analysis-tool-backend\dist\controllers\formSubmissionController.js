"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeleteManyFormSubmission = exports.ToggleFormSubmissionLoginRequired = exports.deleteFormSubmission = exports.updateFormSubmission = exports.getFormSubmission = exports.getProjectFormSubmissions = exports.createFormSubmission = void 0;
const formSubmissionRepository_1 = __importDefault(require("../repositories/formSubmissionRepository"));
const formSubmissionValidator_1 = require("../validators/formSubmissionValidator");
const useragent_1 = __importDefault(require("useragent"));
const axios_1 = __importDefault(require("axios"));
const zod_1 = require("zod");
// Helper function to get location from IP
const getLocationByIP = (ip) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const response = yield axios_1.default.get(`https://ipapi.co/${ip}/json/`);
        if (response.data && !response.data.error) {
            const { city, region, country_name } = response.data;
            return `${city}, ${region}, ${country_name}`;
        }
        return null;
    }
    catch (error) {
        console.error("Error getting location:", error);
        return null;
    }
});
// Create schema for form submission with answers
const createFormSubmissionSchema = formSubmissionValidator_1.formSubmissionSchema.extend({
    libraryTemplateId: zod_1.z.number().optional(),
    answers: zod_1.z
        .array(zod_1.z.object({
        questionId: zod_1.z.number(),
        value: zod_1.z.any(),
        questionOptionId: zod_1.z.number().optional(),
        isOtherOption: zod_1.z.boolean().optional(),
        answerType: zod_1.z.string().optional(),
        imageUrl: zod_1.z.string().optional(),
    }))
        .optional(),
});
// Create a new form submission
const createFormSubmission = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
        if (!userId) {
            return res.status(401).json({
                success: false,
                message: "Unauthorized",
            });
        }
        // Validate request body
        const validationResult = formSubmissionValidator_1.formSubmissionSchema.safeParse(req.body);
        if (!validationResult.success) {
            return res.status(400).json({
                success: false,
                message: "Validation error",
                errors: validationResult.error.errors,
            });
        }
        const data = validationResult.data;
        // Check if user has access to project or library template
        if (data.projectId) {
            const hasAccess = yield formSubmissionRepository_1.default.isProjectAccessible(userId, data.projectId);
            if (!hasAccess) {
                return res.status(403).json({
                    success: false,
                    message: "You don't have access to this project",
                });
            }
        }
        // In test environment, user-agent might be undefined
        let deviceInfo = "Test Device";
        try {
            if (req.headers["user-agent"]) {
                const agent = useragent_1.default.parse(req.headers["user-agent"]);
                deviceInfo = `${agent.family} on ${agent.os}`;
            }
        }
        catch (error) {
            console.error("Error parsing user agent:", error);
        }
        let location = undefined;
        try {
            const ip = req.ip || "127.0.0.1";
            // Skip IP location lookup in test environment
            if (process.env.NODE_ENV !== "test") {
                const rawLocation = yield getLocationByIP(ip);
                location = rawLocation !== null && rawLocation !== void 0 ? rawLocation : undefined;
            }
        }
        catch (error) {
            console.error("Error getting location:", error);
        }
        // Create form submission
        try {
            const formSubmission = yield formSubmissionRepository_1.default.create({
                projectId: data.projectId,
                status: data.status,
                metadata: data.metadata,
            }, userId, deviceInfo, location);
            return res.status(200).json({
                success: true,
                message: "Form submission created successfully",
                data: { formSubmission },
            });
        }
        catch (createError) {
            console.error("Error in repository.create:", createError);
            throw createError; // Re-throw to be caught by the outer try-catch
        }
    }
    catch (error) {
        return res.status(500).json({
            success: false,
            message: "Error creating form submission",
            error: error instanceof Error ? error.message : "Unknown error",
        });
    }
});
exports.createFormSubmission = createFormSubmission;
// Get all form submissions for a project
const getProjectFormSubmissions = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
        const projectId = Number(req.params.projectId);
        if (!userId) {
            return res.status(401).json({
                success: false,
                message: "Unauthorized",
            });
        }
        if (isNaN(projectId)) {
            return res.status(400).json({
                success: false,
                message: "Invalid project ID",
            });
        }
        // Check if user has access to project
        const hasAccess = yield formSubmissionRepository_1.default.isProjectAccessible(userId, projectId);
        if (!hasAccess) {
            return res.status(403).json({
                success: false,
                message: "You don't have access to this project",
            });
        }
        // Get form submissions
        const formSubmissions = yield formSubmissionRepository_1.default.findByProjectId(projectId);
        return res.status(200).json({
            success: true,
            message: "get project form success",
            data: { formSubmissions },
        });
    }
    catch (error) {
        return res.status(500).json({
            success: false,
            message: "Error retrieving form submissions",
            error: error instanceof Error ? error.message : "Unknown error",
        });
    }
});
exports.getProjectFormSubmissions = getProjectFormSubmissions;
// Get a form submission by ID
const getFormSubmission = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
        const submissionId = Number(req.params.id);
        if (!userId) {
            return res.status(401).json({
                success: false,
                message: "Unauthorized",
            });
        }
        if (isNaN(submissionId)) {
            return res.status(400).json({
                success: false,
                message: "Invalid submission ID",
            });
        }
        // Get form submission
        const formSubmission = yield formSubmissionRepository_1.default.findById(submissionId);
        if (!formSubmission) {
            return res.status(404).json({
                success: false,
                message: "Form submission not found",
            });
        }
        // Check if user has access to project
        if (formSubmission.projectId) {
            const hasAccess = yield formSubmissionRepository_1.default.isProjectAccessible(userId, formSubmission.projectId);
            if (!hasAccess) {
                return res.status(403).json({
                    success: false,
                    message: "You don't have access to this form submission",
                });
            }
        }
        return res.status(200).json({
            success: true,
            message: "get form submission success",
            data: { formSubmission },
        });
    }
    catch (error) {
        return res.status(500).json({
            success: false,
            message: "Error retrieving form submission",
            error: error instanceof Error ? error.message : "Unknown error",
        });
    }
});
exports.getFormSubmission = getFormSubmission;
// Update a form submission
const updateFormSubmission = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
        const submissionId = Number(req.params.id);
        if (!userId) {
            return res.status(401).json({
                success: false,
                message: "Unauthorized",
            });
        }
        if (isNaN(submissionId)) {
            return res.status(400).json({
                success: false,
                message: "Invalid submission ID",
            });
        }
        // Get form submission
        const formSubmission = yield formSubmissionRepository_1.default.findById(submissionId);
        if (!formSubmission) {
            return res.status(404).json({
                success: false,
                message: "Form submission not found",
            });
        }
        // Check if user has access to project
        if (formSubmission.projectId) {
            const hasAccess = yield formSubmissionRepository_1.default.isProjectAccessible(userId, formSubmission.projectId);
            if (!hasAccess) {
                return res.status(403).json({
                    success: false,
                    message: "You don't have access to this form submission",
                });
            }
        }
        // Validate request body
        const updateSchema = formSubmissionValidator_1.updateFormSubmissionSchema.extend({
            answers: zod_1.z
                .array(zod_1.z.object({
                id: zod_1.z.number().optional(),
                questionId: zod_1.z.number(),
                value: zod_1.z.any(),
                questionOptionId: zod_1.z.number().optional(),
                isOtherOption: zod_1.z.boolean().optional(),
                answerType: zod_1.z.string().optional(),
                imageUrl: zod_1.z.string().optional(),
            }))
                .optional(),
        });
        const validationResult = updateSchema.safeParse(req.body);
        if (!validationResult.success) {
            return res.status(400).json({
                success: false,
                message: "Validation error",
                errors: validationResult.error.errors,
            });
        }
        const data = validationResult.data;
        // Update form submission
        const updatedFormSubmission = yield formSubmissionRepository_1.default.update(submissionId, {
            status: data.status ? String(data.status) : undefined,
            completedAt: data.completedAt
                ? typeof data.completedAt === "string"
                    ? new Date(data.completedAt)
                    : data.completedAt
                : undefined,
            durationSeconds: data.durationSeconds
                ? Number(data.durationSeconds)
                : undefined,
            metadata: data.metadata
                ? data.metadata
                : undefined,
            submittedAt: data.submittedAt
                ? typeof data.submittedAt === "string"
                    ? new Date(data.submittedAt)
                    : data.submittedAt
                : undefined,
        });
        return res.status(200).json({
            success: true,
            message: "Form submission updated successfully",
            data: { formSubmission: updatedFormSubmission },
        });
    }
    catch (error) {
        return res.status(500).json({
            success: false,
            message: "Error updating form submission",
            error: error instanceof Error ? error.message : "Unknown error",
        });
    }
});
exports.updateFormSubmission = updateFormSubmission;
// Delete a form submission
const deleteFormSubmission = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
        const submissionId = Number(req.params.id);
        if (!userId) {
            return res.status(401).json({
                success: false,
                message: "Unauthorized",
            });
        }
        if (isNaN(submissionId)) {
            return res.status(400).json({
                success: false,
                message: "Invalid submission ID",
            });
        }
        // Get form submission
        const formSubmission = yield formSubmissionRepository_1.default.findById(submissionId);
        if (!formSubmission) {
            return res.status(404).json({
                success: false,
                message: "Form submission not found",
            });
        }
        // Check if user has access to project
        if (formSubmission.projectId) {
            const hasAccess = yield formSubmissionRepository_1.default.isProjectAccessible(userId, formSubmission.projectId);
            if (!hasAccess) {
                return res.status(403).json({
                    success: false,
                    message: "You don't have access to this form submission",
                });
            }
        }
        // Delete form submission
        yield formSubmissionRepository_1.default.delete(submissionId);
        return res.status(200).json({
            success: true,
            message: "Form submission deleted successfully",
        });
    }
    catch (error) {
        return res.status(500).json({
            success: false,
            message: "Error deleting form submission",
            error: error instanceof Error ? error.message : "Unknown error",
        });
    }
});
exports.deleteFormSubmission = deleteFormSubmission;
const ToggleFormSubmissionLoginRequired = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
        const submissionId = parseInt(req.params.id);
        if (!userId) {
            return res.status(401).json({
                success: false,
                message: "Unauthorized",
            });
        }
        if (isNaN(submissionId)) {
            return res.status(400).json({
                success: false,
                message: "Invalid submission ID",
            });
        }
        // Get the existing submission
        const existingSubmission = yield formSubmissionRepository_1.default.findById(submissionId);
        if (!existingSubmission) {
            return res.status(404).json({
                success: false,
                message: "Form submission not found",
            });
        }
        // Check if user has access to the project
        if (!existingSubmission.projectId) {
            return res.status(403).json({
                success: false,
                message: "Submission has no associated project",
            });
        }
        const hasAccess = yield formSubmissionRepository_1.default.isProjectAccessible(userId, existingSubmission.projectId);
        if (!hasAccess) {
            return res.status(403).json({
                success: false,
                message: "You don't have access to this submission",
            });
        }
        // Toggle login required
        const formSubmission = yield formSubmissionRepository_1.default.changeLoginRequired(submissionId);
        return res.status(200).json({
            success: true,
            message: "Form submission login required toggled successfully",
            data: { formSubmission },
        });
    }
    catch (error) {
        return res.status(500).json({
            success: false,
            message: "Error toggling form submission login required",
            error: error instanceof Error ? error.message : "Unknown error",
        });
    }
});
exports.ToggleFormSubmissionLoginRequired = ToggleFormSubmissionLoginRequired;
const DeleteManyFormSubmission = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { ids } = req.body;
        if (!Array.isArray(ids) || ids.length === 0) {
            return res.status(400).json({ message: "ids must be non empty array" });
        }
        const deleted = yield formSubmissionRepository_1.default.deleteMultipleSubmission(ids);
        return res.status(200).json({
            message: `${deleted.count} submission deleted`,
            deletedCount: deleted.count,
        });
    }
    catch (error) {
        return res.status(500).json({
            success: false,
            message: "Error deleting multiple forms",
            error: error instanceof Error ? error.message : "Unknown error",
        });
    }
});
exports.DeleteManyFormSubmission = DeleteManyFormSubmission;
