(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8552],{17652:(e,t,r)=>{"use strict";r.d(t,{c3:()=>s});var n=r(46453);function a(e,t){return(...e)=>{try{return t(...e)}catch{throw Error(void 0)}}}let s=a(0,n.c3);a(0,n.kc)},35695:(e,t,r)=>{"use strict";var n=r(18999);r.o(n,"useParams")&&r.d(t,{useParams:function(){return n.useParams}}),r.o(n,"usePathname")&&r.d(t,{usePathname:function(){return n.usePathname}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}}),r.o(n,"useSearchParams")&&r.d(t,{useSearchParams:function(){return n.useSearchParams}})},39286:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>o,Gl:()=>a,th:()=>s});let n=(0,r(51990).Z0)({name:"createProject",initialState:{visible:!1},reducers:{showCreateProjectModal:e=>{e.visible=!0},hideCreateProjectModal:e=>{e.visible=!1}}}),{showCreateProjectModal:a,hideCreateProjectModal:s}=n.actions,o=n.reducer},56303:(e,t,r)=>{Promise.resolve().then(r.bind(r,61113))},61113:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var n=r(95155);r(12115);var a=r(35695),s=r(15305),o=r(34540),c=r(39286),i=r(17652);let l=()=>{let{status:e}=(0,a.useParams)(),t=(0,o.wA)(),r=(0,i.c3)();return(0,n.jsx)("div",{className:"flex flex-col items-center justify-center py-16 px-4 min-h-[70vh]",children:(0,n.jsxs)("div",{className:"bg-neutral-100 rounded-lg shadow-sm p-8 max-w-md w-full text-center",children:[(0,n.jsx)("div",{className:"flex justify-center mb-6",children:(0,n.jsx)("div",{className:"bg-neutral-200 p-5 rounded-full",children:(0,n.jsx)(s._PQ,{size:50,className:"text-primary-500"})})}),(0,n.jsx)("h2",{className:"text-2xl font-semibold text-neutral-800 mb-2",children:r("noProjects",{status:r(e&&"string"==typeof e?"status2.".concat(e):"projects")})}),(0,n.jsxs)("p",{className:"text-neutral-600 mb-8",children:["draft"===e&&r("noDraftProjects"),"deployed"===e&&r("noDeployedProjects"),"archived"===e&&r("noArchivedProjects"),!e&&r("noProjectsInCategory")]}),"draft"===e&&(0,n.jsx)("button",{onClick:()=>{t((0,c.Gl)())},className:"btn-primary w-full",children:r("createNewProject")}),"deployed"===e&&(0,n.jsx)("div",{className:"text-sm text-neutral-500",children:r("createAndDeployProject")}),"archived"===e&&(0,n.jsx)("div",{className:"text-sm text-neutral-500",children:r("archiveProjectsInfo")})]})})}},74436:(e,t,r)=>{"use strict";r.d(t,{k5:()=>u});var n=r(12115),a={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},s=n.createContext&&n.createContext(a),o=["attr","size","title"];function c(){return(c=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function i(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function l(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?i(Object(r),!0).forEach(function(t){var n,a,s;n=e,a=t,s=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in n?Object.defineProperty(n,a,{value:s,enumerable:!0,configurable:!0,writable:!0}):n[a]=s}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function u(e){return t=>n.createElement(d,c({attr:l({},e.attr)},t),function e(t){return t&&t.map((t,r)=>n.createElement(t.tag,l({key:r},t.attr),e(t.child)))}(e.child))}function d(e){var t=t=>{var r,{attr:a,size:s,title:i}=e,u=function(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(n=0;n<s.length;n++)r=s[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}(e,o),d=s||t.size||"1em";return t.className&&(r=t.className),e.className&&(r=(r?r+" ":"")+e.className),n.createElement("svg",c({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,a,u,{className:r,style:l(l({color:e.color||t.color},t.style),e.style),height:d,width:d,xmlns:"http://www.w3.org/2000/svg"}),i&&n.createElement("title",null,i),e.children)};return void 0!==s?n.createElement(s.Consumer,null,e=>t(e)):t(a)}}},e=>{var t=t=>e(e.s=t);e.O(0,[8087,6453,635,8441,1684,7358],()=>t(56303)),_N_E=e.O()}]);