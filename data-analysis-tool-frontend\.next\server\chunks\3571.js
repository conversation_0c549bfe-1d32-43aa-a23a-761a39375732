exports.id=3571,exports.ids=[3571],exports.modules={6986:(e,t,r)=>{"use strict";r.d(t,{D:()=>n,l:()=>o});var s=r(53907);let a=process.env.SALT||"rushan-salt",i=new s.A(a,12),o=e=>i.encode(e),n=e=>{let t=i.decode(e)[0];return"bigint"==typeof t?t<Number.MAX_SAFE_INTEGER?Number(t):null:"number"==typeof t?t:null}},10125:(e,t,r)=>{"use strict";r.d(t,{Notification:()=>m});var s=r(60687),a=r(43210),i=r(54864),o=r(88920),n=r(57101),l=r(19150),c=r(14719),d=r(43649),u=r(93613);let m=()=>{let e=(0,i.wA)(),{message:t,type:r,visible:m}=(0,i.d4)(e=>e.notification);(0,a.useEffect)(()=>{if(m){let t=setTimeout(()=>{e((0,l._b)())},5e3);return()=>clearTimeout(t)}},[m,e]);let p="success"===r?(0,s.jsx)(c.A,{}):"warning"===r?(0,s.jsx)(d.A,{}):(0,s.jsx)(u.A,{});return(0,s.jsx)(o.N,{children:m&&(0,s.jsxs)(n.P.div,{className:`z-50 fixed top-0 right-0 m-4 px-4 py-2 rounded font-semibold w-auto max-w-xs flex items-center gap-2 cursor-pointer ${"success"===r?"bg-green-500 hover:bg-green-600":"warning"===r?"bg-yellow-500 hover:bg-yellow-600":"bg-red-500 hover:bg-red-600"} transition-colors duration-300`,onClick:()=>e((0,l._b)()),initial:{opacity:0,y:-20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.3,ease:"easeIn"},children:[(0,s.jsx)("span",{className:"text-2xl",children:p}),(0,s.jsx)("span",{className:"break-words neutral-100space-normal",children:t})]})})}},10271:(e,t,r)=>{"use strict";r.d(t,{ReactQueryProvider:()=>l});var s=r(60687),a=r(43210),i=r(39091),o=r(8693),n=r(9124);let l=({children:e})=>{let[t]=(0,a.useState)(()=>new i.E({defaultOptions:{queries:{staleTime:3e5,refetchOnWindowFocus:!1}}}));return(0,s.jsxs)(o.Ht,{client:t,children:[e,(0,s.jsx)(n.E,{initialIsOpen:!1})]})}},12810:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let s=r(51060).A.create({baseURL:"http://localhost:4000/api",headers:{"Content-Type":"application/json"},withCredentials:!0});s.interceptors.request.use(e=>e,e=>Promise.reject(e)),s.interceptors.response.use(e=>e,e=>("ERR_NETWORK"===e.code&&console.error("Network error - Please check if the backend server is running"),Promise.reject(e)));let a=s},14952:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},15616:(e,t,r)=>{"use strict";r.d(t,{T:()=>o});var s=r(60687),a=r(43210),i=r(96241);let o=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("textarea",{className:(0,i.cn)("flex min-h-[80px] w-full rounded-md border border-gray-200 bg-neutral-100 px-3 py-2 text-sm shadow-sm placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-700 dark:bg-gray-900 dark:placeholder:text-gray-500",e),ref:r,...t}));o.displayName="Textarea"},16319:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},19150:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>o,Ds:()=>a,_b:()=>i});let s=(0,r(9317).Z0)({name:"notification",initialState:{message:"",type:"",visible:!1},reducers:{showNotification:(e,t)=>{e.message=t.payload.message,e.type=t.payload.type,e.visible=!0},hideNotification:e=>{e.message="",e.type="",e.visible=!1}}}),{showNotification:a,hideNotification:i}=s.actions,o=s.reducer},26946:(e,t,r)=>{Promise.resolve().then(r.bind(r,10125)),Promise.resolve().then(r.bind(r,10271)),Promise.resolve().then(r.bind(r,49271))},35790:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>o,l:()=>i,yg:()=>a});let s=(0,r(9317).Z0)({name:"createLibraryItem",initialState:{visible:!1},reducers:{showCreateLibraryModal:e=>{e.visible=!0},hideCreateLibraryModal:e=>{e.visible=!1}}}),{showCreateLibraryModal:a,hideCreateLibraryModal:i}=s.actions,o=s.reducer},39390:(e,t,r)=>{"use strict";r.d(t,{J:()=>n});var s=r(60687),a=r(43210),i=r(78148),o=r(96241);let n=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(i.b,{ref:r,className:(0,o.cn)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",e),...t}));n.displayName=i.b.displayName},40347:(e,t,r)=>{"use strict";r.d(t,{C:()=>c,z:()=>l});var s=r(60687),a=r(43210),i=r(14555),o=r(65822),n=r(96241);let l=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(i.bL,{className:(0,n.cn)("grid gap-2",e),...t,ref:r}));l.displayName=i.bL.displayName;let c=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(i.q7,{ref:r,className:(0,n.cn)("aspect-square h-4 w-4 rounded-full border text-gray-900 shadow focus:outline-none focus-visible:ring-1 focus-visible:ring-gray-950 disabled:cursor-not-allowed disabled:opacity-50 dark:text-gray-50 dark:focus-visible:ring-gray-300",e),...t,children:(0,s.jsx)(i.C1,{className:"flex items-center justify-center",children:(0,s.jsx)(o.A,{className:"h-2.5 w-2.5 fill-current text-current"})})}));c.displayName=i.q7.displayName},42895:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>l,Le:()=>o,jB:()=>n,tQ:()=>a,x9:()=>i});let s=(0,r(9317).Z0)({name:"auth",initialState:{status:"loading",user:null,error:null},reducers:{setAuthenticatedUser:(e,t)=>{e.status="authenticated",e.user=t.payload,e.error=null},setUnauthenticated:e=>{e.status="unauthenticated",e.user=null,e.error=null},setAuthLoading:e=>{e.status="loading"},setAuthError:(e,t)=>{e.status="unauthenticated",e.error=t.payload,e.user=null}}}),{setAuthenticatedUser:a,setUnauthenticated:i,setAuthLoading:o,setAuthError:n}=s.actions,l=s.reducer},44395:(e,t,r)=>{"use strict";r.d(t,{Notification:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call Notification() from the server but Notification is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\components\\general\\Notification.tsx","Notification")},46055:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},49271:(e,t,r)=>{"use strict";r.d(t,{ReduxProvider:()=>m});var s=r(60687),a=r(9317),i=r(19150),o=r(58432),n=r(42895),l=r(35790),c=r(89011);let d=(0,a.U1)({reducer:{notification:i.Ay,createProject:o.Ay,auth:n.Ay,createLibrary:l.Ay,createLibraryItem:c.Ay}});r(43210);var u=r(54864);let m=({children:e})=>(0,s.jsx)(u.Kq,{store:d,children:e})},50823:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},58014:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>m,metadata:()=>u});var s=r(37413);r(82704);var a=r(7990),i=r.n(a),o=r(60866),n=r.n(o),l=r(77832),c=r(44395),d=r(60265);let u={title:"Data analysis tool",description:"A tool for data collection and analysis."};function m({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:`${i().className} ${n().className} antialiased`,children:(0,s.jsx)(l.ReduxProvider,{children:(0,s.jsxs)(d.ReactQueryProvider,{children:[(0,s.jsx)(c.Notification,{}),(0,s.jsx)("main",{className:"bg-neutral-200",children:e})]})})})})}},58432:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>o,Gl:()=>a,th:()=>i});let s=(0,r(9317).Z0)({name:"createProject",initialState:{visible:!1},reducers:{showCreateProjectModal:e=>{e.visible=!0},hideCreateProjectModal:e=>{e.visible=!1}}}),{showCreateProjectModal:a,hideCreateProjectModal:i}=s.actions,o=s.reducer},60265:(e,t,r)=>{"use strict";r.d(t,{ReactQueryProvider:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call ReactQueryProvider() from the server but ReactQueryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\providers\\ReactQueryProvider.tsx","ReactQueryProvider")},68988:(e,t,r)=>{"use strict";r.d(t,{p:()=>i});var s=r(60687);r(43210);var a=r(96241);function i({className:e,type:t,...r}){return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-primary-500 focus-visible:ring-[1px]","focus-visible:outline-none","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}},70334:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},71845:(e,t,r)=>{"use strict";r.d(t,{D_:()=>u,Im:()=>c,Oo:()=>m,c3:()=>i,kf:()=>a,lj:()=>h,or:()=>l,pf:()=>d,vj:()=>o,wI:()=>p,xx:()=>n});var s=r(12810);let a=async({projectId:e})=>{let{data:t}=await s.A.get(`/projects/${e}`);return t.project},i=async e=>{let{data:t}=await s.A.post("/projects/from-template",e);return t},o=async()=>{try{let{data:e}=await s.A.get("/projects");return e.projects}catch(e){throw console.error("Error fetching projects:",e),e}},n=async e=>{let{data:t}=await s.A.delete(`/projects/delete/${e}`);return t},l=async e=>{try{let{data:t}=await s.A.delete("/projects/delete-multiple",{data:{projectIds:e}});return t}catch(e){throw console.error("Error deleting multiple projects:",e),e}},c=async e=>{try{let{data:t}=await s.A.patch(`/projects/change-status/${e}`,{status:"archived"});return t}catch(e){throw console.error("Error archiving project:",e),e}},d=async(e,t=!1)=>{try{let{data:t}=await s.A.patch(`/projects/change-status/${e}`,{status:"deployed"});return t}catch(e){throw console.error("Error deploying project:",e),e}},u=async e=>{try{let{data:t}=await s.A.patch("/projects/update-many-status",{projectIds:e,status:"archived"});return t}catch(e){throw console.error("Error archiving multiple projects:",e),e}},m=async e=>{try{let{data:t}=await s.A.post("/users/check-email",{email:e});return t}catch(e){throw Error("object"==typeof e.response?.data?.message?JSON.stringify(e.response?.data?.message):e.response?.data?.message||e.message||"Failed to check user")}},p=async({projectId:e,email:t,permissions:r})=>{try{let a=await m(t);if(!a||!a.success)throw Error(a?.message||"User not found");let{data:i}=await s.A.post("/project-users",{userId:a.user.id,projectId:e,permission:r});return i}catch(e){throw console.error("Error adding user to project:",e),Error("object"==typeof e.response?.data?.message?JSON.stringify(e.response?.data?.message):e.response?.data?.message||e.message||"Failed to add user")}},h=async e=>{try{let{data:t}=await s.A.post("/answers/multiple",e);return t}catch(e){throw console.error("Error creating answer submission:",e),e}}},77832:(e,t,r)=>{"use strict";r.d(t,{ReduxProvider:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call ReduxProvider() from the server but ReduxProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\providers\\ReduxProvider.tsx","ReduxProvider")},82704:()=>{},86429:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(60687);r(43210);let a=()=>(0,s.jsx)("div",{className:"w-full flex items-center justify-center",children:(0,s.jsx)("div",{className:"size-8 rounded-full border-x-2 border-primary-500 animate-spin my-16"})})},86778:(e,t,r)=>{Promise.resolve().then(r.bind(r,44395)),Promise.resolve().then(r.bind(r,60265)),Promise.resolve().then(r.bind(r,77832))},89011:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>o,dQ:()=>a,g7:()=>i});let s=(0,r(9317).Z0)({initialState:{visible:!1,option:""},name:"createLibraryItem",reducers:{showCreateLibraryItemModal:(e,t)=>{e.visible=!0,e.option=t.payload},hideCreateLibraryItemModal:e=>{e.visible=!1,e.option=""}}}),{showCreateLibraryItemModal:a,hideCreateLibraryItemModal:i}=s.actions,o=s.reducer},93437:(e,t,r)=>{"use strict";r.d(t,{S:()=>n});var s=r(60687);r(43210);var a=r(40211),i=r(13964),o=r(96241);function n({className:e,...t}){return(0,s.jsx)(a.bL,{"data-slot":"checkbox",className:(0,o.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:(0,s.jsx)(a.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,s.jsx)(i.A,{className:"size-3.5"})})})}},96241:(e,t,r)=>{"use strict";r.d(t,{Y:()=>o,cn:()=>i});var s=r(49384),a=r(82348);function i(...e){return(0,a.QP)((0,s.$)(e))}function o(e,t="short"){if(!e)return"";try{let r="string"==typeof e?new Date(e):e;if(isNaN(r.getTime()))return"";switch(t){case"short":return r.toLocaleDateString(void 0,{year:"numeric",month:"short",day:"numeric"});case"long":return r.toLocaleDateString(void 0,{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"});case"full":return r.toLocaleDateString(void 0,{year:"numeric",month:"long",day:"numeric",weekday:"long",hour:"2-digit",minute:"2-digit",second:"2-digit"});default:return r.toLocaleDateString()}}catch(t){return console.error("Error formatting date:",t),String(e)}}},96752:(e,t,r)=>{"use strict";r.d(t,{A0:()=>o,BF:()=>n,Hj:()=>l,XI:()=>i,nA:()=>d,nd:()=>c});var s=r(60687);r(43210);var a=r(96241);function i({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,s.jsx)("table",{"data-slot":"table",className:(0,a.cn)("w-full caption-bottom text-sm",e),...t})})}function o({className:e,...t}){return(0,s.jsx)("thead",{"data-slot":"table-header",className:(0,a.cn)("[&_tr]:border-b",e),...t})}function n({className:e,...t}){return(0,s.jsx)("tbody",{"data-slot":"table-body",className:(0,a.cn)("[&_tr:last-child]:border-0",e),...t})}function l({className:e,...t}){return(0,s.jsx)("tr",{"data-slot":"table-row",className:(0,a.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...t})}function c({className:e,...t}){return(0,s.jsx)("th",{"data-slot":"table-head",className:(0,a.cn)("text-foreground h-10 px-2 text-left align-middle font-medium neutral-100space-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}function d({className:e,...t}){return(0,s.jsx)("td",{"data-slot":"table-cell",className:(0,a.cn)("p-2 align-middle neutral-100space-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}}};