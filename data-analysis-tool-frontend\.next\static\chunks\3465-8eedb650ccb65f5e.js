"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3465],{10786:(e,s,t)=>{t.d(s,{m:()=>w});var r=t(95155),a=t(12115),l=t(13163),n=t(14549),i=t(19373),o=t(77361),c=t(35695),d=t(88570),u=t(29350),m=t(57799),p=t(54416),h=t(26715),x=t(5041),g=t(34540),v=t(71402),j=t(17652);let f=e=>{let{onClose:s,projectId:t,onUserAdded:l}=e,n=(0,j.c3)(),i=[{label:n("viewForm"),value:"viewForm"},{label:n("editForm"),value:"editForm"},{label:n("viewSubmissions"),value:"viewSubmissions"},{label:n("editSubmissions"),value:"editSubmissions"},{label:n("addSubmissions"),value:"addSubmissions"},{label:n("deleteSubmissions"),value:"deleteSubmissions"},{label:n("validateSubmissions"),value:"validateSubmissions"},{label:n("manageProject"),value:"manageProject"}],[c,d]=(0,a.useState)(""),[u,m]=(0,a.useState)([]),[f,b]=(0,a.useState)(""),[y,w]=(0,a.useState)(!1),[N,A]=(0,a.useState)(null),S=(0,h.jE)(),E=(0,g.wA)(),k=(0,a.useRef)(null),F=e=>/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(e),C=e=>{m(s=>s.includes(e)?s.filter(s=>s!==e):[...s,e])},U=(0,x.n)({mutationFn:o.Oo,onSuccess:()=>{A(!0),b("")},onError:e=>{var s,t;A(!1),b((null==(t=e.response)||null==(s=t.data)?void 0:s.message)||n("userNotFound"))},onSettled:()=>{w(!1)}}),P=()=>c?F(c)?N?0===u.length?(b(n("selectPermission")),!1):(b(""),!0):(b(n("userNotFound")),!1):(b(n("invalidEmail")),!1):(b(n("emailRequired")),!1),I=(0,x.n)({mutationFn:()=>{let e=u.reduce((e,s)=>(e[s]=!0,e),{});return(0,o.wI)({projectId:t,email:c,permissions:e})},onSuccess:()=>{S.invalidateQueries({queryKey:["projectUsers",t]}),E((0,v.Ds)({message:n("userAdded"),type:"success"})),l&&l(),s()},onError:e=>{var s,t;let r;r="string"==typeof e?e:e instanceof Error?e.message:(null==(t=e.response)||null==(s=t.data)?void 0:s.message)?"object"==typeof e.response.data.message?JSON.stringify(e.response.data.message):e.response.data.message:n("failedToAddUser"),E((0,v.Ds)({message:r,type:"error"})),b(r)}});return(0,r.jsxs)("div",{className:"bg-neutral-100 p-6 rounded-md",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{className:"w-full border ".concat(f?"border-red-500":"border-neutral-300"," rounded px-3 py-2 mb-4 focus:outline-none focus:ring-2 focus:ring-primary-200 placeholder:text-neutral-500"),placeholder:n("email"),value:c,onChange:e=>{let s=e.target.value;if(d(s),A(null),b(""),s){if(!F(s))return void b(n("invalidEmail"));k.current&&clearTimeout(k.current),k.current=setTimeout(()=>{w(!0),U.mutate(s)},800)}}}),(0,r.jsx)("button",{className:"absolute right-2 top-2 text-neutral-700 hover:text-neutral-900",onClick:s,type:"button",children:(0,r.jsx)(p.A,{size:22})}),y&&(0,r.jsx)("p",{className:"text-neutral-500 text-sm mb-2",children:n("verifyingEmail")}),!0===N&&(0,r.jsx)("p",{className:"text-green-500 text-sm mb-2",children:n("userFound")}),f&&(0,r.jsx)("p",{className:"text-red-500 text-sm mb-2",children:f})]}),(0,r.jsx)("div",{className:"flex flex-col gap-2",children:i.map(e=>(0,r.jsx)("div",{className:"flex flex-col",children:(0,r.jsxs)("label",{className:"flex items-center gap-2",children:[(0,r.jsx)("input",{type:"checkbox",checked:u.includes(e.value),onChange:()=>C(e.value)}),e.label]})},e.value))}),(0,r.jsx)("button",{className:"mt-6 ".concat(I.isPending||y?"bg-neutral-400":"bg-blue-400 hover:bg-blue-500"," text-white px-6 py-2 rounded disabled:opacity-50"),disabled:I.isPending||y||!c||0===u.length||!N,onClick:()=>{if(!t)return void b(n("projectIdRequired"));P()&&I.mutate()},children:I.isPending?n("adding"):n("grantPermissions")})]})};var b=t(25784),y=t(97168);let w=e=>{let{showModal:s,onClose:t,onShare:p,selectedProject:h}=e,{hashedId:x}=(0,c.useParams)(),{user:g}=(0,u.A)(),[v,w]=(0,a.useState)(!1),N=x?(0,d.D)(x):null,A=(null==h?void 0:h.id)||N,S=(0,j.c3)(),{data:E,isLoading:k}=(0,i.I)({queryKey:["project",A],queryFn:async()=>await (0,o.kf)({projectId:A}),enabled:!!A&&!!(null==g?void 0:g.id)}),[F,C]=(0,a.useState)([]),[U,P]=(0,a.useState)(!1);if((0,a.useEffect)(()=>{let e=async()=>{if(A){P(!0);try{let e=await b.A.get("/project-users/".concat(A));if(e.data&&e.data.data&&e.data.data.AllUser){let s=e.data.data.AllUser||[];C(s)}else console.warn("No users data in response:",e.data),C([])}catch(e){console.error("Error fetching project users:",e),C([])}finally{P(!1)}}};s&&A&&e()},[A,s]),k)return(0,r.jsx)(m.A,{});let I=E||h;if(!I)return(0,r.jsx)(l.A,{isOpen:s,onClose:t,className:"p-6 rounded-md",children:(0,r.jsxs)("div",{className:"text-center py-4",children:[(0,r.jsx)("p",{className:"text-red-500",children:S("projectNotFound")}),(0,r.jsx)(y.$,{onClick:t,className:"mt-4",children:S("close")})]})});let O=e=>{if(!e)return"bg-gray-500";let s=["bg-green-500","bg-blue-500","bg-red-500","bg-purple-500","bg-yellow-500","bg-pink-500","bg-indigo-500","bg-orange-500"];return s[e.charCodeAt(0)%s.length]},_=e=>e?e.charAt(0).toUpperCase():"?";return(0,r.jsxs)(l.A,{isOpen:s,onClose:t,className:"p-6 rounded-md",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-neutral-700",children:"".concat(S("sharingProject"),": ").concat(I.name||"")}),(0,r.jsxs)("div",{className:"w-2xl mt-4 p-4 max-h-[500px] overflow-y-auto",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsx)("div",{className:"text-xl font-semibold",children:S("whoHasAccess")}),(0,r.jsxs)("div",{className:"flex items-center border rounded-md px-3 py-1.5 cursor-pointer hover:bg-gray-50",onClick:()=>w(!0),children:[(0,r.jsx)(n._rf,{size:18,className:"mr-2"}),(0,r.jsx)("div",{className:"text-sm",children:S("addUser")})]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[I.user&&(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-10 h-10 rounded-full ".concat(O(I.user.name)," flex items-center justify-center text-neutral-100 font-medium mr-3"),children:_(I.user.name)}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:"font-medium",children:I.user.name||I.user.email||S("unknownUser")}),(0,r.jsx)("div",{className:"inline-block bg-gray-100 text-xs px-2 py-0.5 rounded",children:S("owner")})]})]}),U?(0,r.jsx)("div",{className:"py-2 text-center",children:(0,r.jsx)("div",{className:"inline-block w-6 h-6 rounded-full border-2 border-t-transparent border-primary-500 animate-spin"})}):F&&F.length>0?F.map((e,s)=>{let t=e.user&&e.user.name||e.user&&e.user.email||"User ".concat(e.userId);return(0,r.jsxs)("div",{className:"flex items-center mt-4",children:[(0,r.jsx)("div",{className:"w-10 h-10 rounded-full ".concat(O(t)," flex items-center justify-center text-neutral-100 font-medium mr-3"),children:_(t)}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:"font-medium",children:t}),(0,r.jsx)("div",{className:"flex flex-wrap gap-1 mt-1",children:e.permission&&Object.entries(e.permission).filter(e=>{let[s,t]=e;return!0===t}).map(e=>{let[s]=e;return(0,r.jsx)("div",{className:"inline-block bg-gray-100 text-xs px-2 py-0.5 rounded",children:"viewForm"===s?S("viewForm"):"editForm"===s?S("editForm"):"viewSubmissions"===s?S("viewSubmissions"):"editSubmissions"===s?S("editSubmissions"):"addSubmissions"===s?S("addSubmissions"):"deleteSubmissions"===s?S("deleteSubmissions"):"validateSubmissions"===s?S("validateSubmissions"):"manageProject"===s?S("manageProject"):s},s)})})]})]},s)}):null]}),v&&A&&(0,r.jsx)("div",{className:"mt-6",children:(0,r.jsx)(f,{onClose:()=>w(!1),projectId:A,onUserAdded:()=>{(async()=>{P(!0);try{let e=await b.A.get("/project-users/".concat(A));if(e.data&&e.data.data&&e.data.data.AllUser){let s=e.data.data.AllUser||[];C(s)}else C([])}catch(e){console.error("Error fetching project users:",e),C([])}finally{P(!1)}})()}})}),(0,r.jsx)("div",{className:"mt-8 border-t pt-6",children:(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium",children:S("anonymousSubmissions")}),(0,r.jsx)("div",{className:"text-sm text-gray-500 mt-1",children:S("allowAnonymousSubmissions")})]}),(0,r.jsx)("div",{className:"w-12 h-6 bg-gray-200 rounded-full relative cursor-pointer",children:(0,r.jsx)("div",{className:"w-5 h-5 bg-neutral-100 rounded-full absolute top-0.5 left-0.5 shadow"})})]})}),(0,r.jsx)("div",{className:"mt-8",children:(0,r.jsx)("div",{className:"inline-block border rounded-md px-4 py-2 text-sm cursor-pointer hover:bg-gray-50",children:S("copyTeamFromProject")})})]})]})}},29350:(e,s,t)=>{t.d(s,{A:()=>c});var r=t(97381),a=t(59362),l=t(25784),n=t(35695),i=t(12115),o=t(34540);let c=e=>{let s=(0,o.wA)(),t=(0,n.useRouter)(),c=(0,n.usePathname)(),{status:d,user:u,error:m}=(0,o.d4)(e=>e.auth),p=async()=>{try{s((0,r.Le)());let e=(await l.A.get("/users/me")).data;s((0,r.tQ)(e))}catch(l){if(s((0,r.x9)()),(0,a.F0)(l)){var e,n,i,o,d;if(console.error("Auth error:",null==(e=l.response)?void 0:e.status,null==(n=l.response)?void 0:n.data),(null==(i=l.response)?void 0:i.status)===401){if(c.startsWith("/form-submission"))return;t.push("/")}else s((0,r.jB)((null==(d=l.response)||null==(o=d.data)?void 0:o.message)||l.message))}else s((0,r.jB)(l instanceof Error?l.message:"An unknown error occurred."))}};return(0,i.useEffect)(()=>{(null==e?void 0:e.skipFetchUser)||p()},[null==e?void 0:e.skipFetchUser]),(0,i.useEffect)(()=>{let e=e=>{if("logout"===e.key&&"true"===e.newValue)if(s((0,r.x9)()),c.startsWith("/form-submission")){let e=c.split("/")[2];e?t.push("/form-submission/".concat(e,"/sign-in")):t.push("/")}else t.push("/")};return window.addEventListener("storage",e),()=>{window.removeEventListener("storage",e)}},[s,t,c]),{status:d,user:u,error:m,isAuthenticated:"authenticated"===d,isLoading:"loading"===d,refreshAuthState:()=>{p()},signin:async(e,s,t)=>{try{await l.A.post("/users/login",e),await p(),null==s||s()}catch(e){if(e instanceof a.pe){var r,n;let s=null==(n=e.response)||null==(r=n.data)?void 0:r.errorType;null==t||t(s)}else null==t||t()}},logout:async()=>{try{await l.A.post("/users/logout"),localStorage.setItem("logout","true"),setTimeout(()=>localStorage.removeItem("logout"),100)}finally{if(s((0,r.x9)()),c.startsWith("/form-submission")){let e=c.split("/")[2];e?t.push("/form-submission/".concat(e,"/sign-in")):t.push("/")}else t.push("/")}}}}},57799:(e,s,t)=>{t.d(s,{A:()=>a});var r=t(95155);t(12115);let a=()=>(0,r.jsx)("div",{className:"w-full flex items-center justify-center",children:(0,r.jsx)("div",{className:"size-8 rounded-full border-x-2 border-primary-500 animate-spin my-16"})})},63642:(e,s,t)=>{t.d(s,{R:()=>l});var r=t(95155);t(12115);var a=t(13163);let l=e=>{let{showModal:s,onClose:t,onConfirm:l,title:n,description:i,confirmButtonText:o,cancelButtonText:c,confirmButtonClass:d,children:u}=e;return(0,r.jsxs)(a.A,{isOpen:s,onClose:t,className:"p-6 rounded-md max-w-xl",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-neutral-700",children:n}),(0,r.jsx)("div",{className:"text-neutral-700 mt-2",children:i}),u&&(0,r.jsx)("div",{className:"mt-6 space-y-4",children:u}),(0,r.jsxs)("div",{className:"flex justify-end gap-4 mt-6",children:[(0,r.jsx)("button",{className:"btn-outline",onClick:t,type:"button",children:c||"Cancel"}),(0,r.jsx)("button",{className:"font-medium rounded-md shadow-md text-neutral-100 flex items-center justify-center gap-2 px-4 py-2 active:scale-95 transition-all duration-300 ".concat(d),onClick:l,type:"button",children:o})]})]})}},71402:(e,s,t)=>{t.d(s,{Ay:()=>n,Ds:()=>a,_b:()=>l});let r=(0,t(51990).Z0)({name:"notification",initialState:{message:"",type:"",visible:!1},reducers:{showNotification:(e,s)=>{e.message=s.payload.message,e.type=s.payload.type,e.visible=!0},hideNotification:e=>{e.message="",e.type="",e.visible=!1}}}),{showNotification:a,hideNotification:l}=r.actions,n=r.reducer},77361:(e,s,t)=>{t.d(s,{D_:()=>u,Im:()=>c,Oo:()=>m,c3:()=>l,kf:()=>a,lj:()=>h,or:()=>o,pf:()=>d,vj:()=>n,wI:()=>p,xx:()=>i});var r=t(25784);let a=async e=>{let{projectId:s}=e,{data:t}=await r.A.get("/projects/".concat(s));return t.project},l=async e=>{let{data:s}=await r.A.post("/projects/from-template",e);return s},n=async()=>{try{let{data:e}=await r.A.get("/projects");return e.projects}catch(e){throw console.error("Error fetching projects:",e),e}},i=async e=>{let{data:s}=await r.A.delete("/projects/delete/".concat(e));return s},o=async e=>{try{let{data:s}=await r.A.delete("/projects/delete-multiple",{data:{projectIds:e}});return s}catch(e){throw console.error("Error deleting multiple projects:",e),e}},c=async e=>{try{let{data:s}=await r.A.patch("/projects/change-status/".concat(e),{status:"archived"});return s}catch(e){throw console.error("Error archiving project:",e),e}},d=async function(e){arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{let{data:s}=await r.A.patch("/projects/change-status/".concat(e),{status:"deployed"});return s}catch(e){throw console.error("Error deploying project:",e),e}},u=async e=>{try{let{data:s}=await r.A.patch("/projects/update-many-status",{projectIds:e,status:"archived"});return s}catch(e){throw console.error("Error archiving multiple projects:",e),e}},m=async e=>{try{let{data:s}=await r.A.post("/users/check-email",{email:e});return s}catch(e){var s,t,a,l,n,i;throw Error("object"==typeof(null==(t=e.response)||null==(s=t.data)?void 0:s.message)?JSON.stringify(null==(l=e.response)||null==(a=l.data)?void 0:a.message):(null==(i=e.response)||null==(n=i.data)?void 0:n.message)||e.message||"Failed to check user")}},p=async e=>{let{projectId:s,email:t,permissions:a}=e;try{let e=await m(t);if(!e||!e.success)throw Error((null==e?void 0:e.message)||"User not found");let{data:l}=await r.A.post("/project-users",{userId:e.user.id,projectId:s,permission:a});return l}catch(e){var l,n,i,o,c,d;throw console.error("Error adding user to project:",e),Error("object"==typeof(null==(n=e.response)||null==(l=n.data)?void 0:l.message)?JSON.stringify(null==(o=e.response)||null==(i=o.data)?void 0:i.message):(null==(d=e.response)||null==(c=d.data)?void 0:c.message)||e.message||"Failed to add user")}},h=async e=>{try{let{data:s}=await r.A.post("/answers/multiple",e);return s}catch(e){throw console.error("Error creating answer submission:",e),e}}},97381:(e,s,t)=>{t.d(s,{Ay:()=>o,Le:()=>n,jB:()=>i,tQ:()=>a,x9:()=>l});let r=(0,t(51990).Z0)({name:"auth",initialState:{status:"loading",user:null,error:null},reducers:{setAuthenticatedUser:(e,s)=>{e.status="authenticated",e.user=s.payload,e.error=null},setUnauthenticated:e=>{e.status="unauthenticated",e.user=null,e.error=null},setAuthLoading:e=>{e.status="loading"},setAuthError:(e,s)=>{e.status="unauthenticated",e.error=s.payload,e.user=null}}}),{setAuthenticatedUser:a,setUnauthenticated:l,setAuthLoading:n,setAuthError:i}=r.actions,o=r.reducer}}]);