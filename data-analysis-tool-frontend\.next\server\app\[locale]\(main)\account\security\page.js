(()=>{var e={};e.id=2525,e.ids=[2525],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},12597:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},13170:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Kobo-project\\\\data-analysis-tool-frontend\\\\app\\\\[locale]\\\\(main)\\\\account\\\\security\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\account\\security\\page.tsx","default")},13861:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},14083:(e,s,t)=>{Promise.resolve().then(t.bind(t,13170))},14368:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>u,tree:()=>c});var a=t(65239),r=t(48088),l=t(88170),n=t.n(l),i=t(30893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);t.d(s,o);let c={children:["",{children:["[locale]",{children:["(main)",{children:["account",{children:["security",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,13170)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\account\\security\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,87186)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\account\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,84606)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,72121)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,58014)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\account\\security\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/[locale]/(main)/account/security/page",pathname:"/[locale]/account/security",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19169:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},20174:(e,s,t)=>{"use strict";t.d(s,{F:()=>i});var a=t(60687),r=t(16189),l=t(85814),n=t.n(l);t(43210);let i=({items:e})=>{let s=(0,r.usePathname)(),t=e=>s.startsWith(e);return(0,a.jsx)("div",{className:"border-y border-neutral-400 rounded-md bg-primary-500 my-4 shadow-md",children:(0,a.jsx)("div",{className:"flex items-center",children:e.map(e=>e.disabled?(0,a.jsxs)("div",{className:"flex items-center gap-2 font-medium transition-all duration-300 p-4 border-b-2 text-gray-400 cursor-not-allowed",children:[e.icon,e.label]},e.route):(0,a.jsxs)(n(),{href:e.route,className:`flex items-center gap-2 font-medium transition-all duration-300 p-4 border-b-2 text-neutral-100 ${t(e.route)?"border-neutral-100":"border-transparent hover:border-neutral-400"}`,children:[e.icon,e.label]},e.route))})})}},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28155:(e,s,t)=>{Promise.resolve().then(t.bind(t,81593))},28354:e=>{"use strict";e.exports=require("util")},28555:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>f});var a=t(60687),r=t(86429),l=t(58869);let n=(0,t(62688).A)("shield-alert",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"M12 8v4",key:"1got3b"}],["path",{d:"M12 16h.01",key:"1drbdi"}]]);t(43210);var i=t(20174),o=t(77618);let c=()=>{let e=(0,o.c3)(),s=[{label:e("profile"),icon:(0,a.jsx)(l.A,{size:16}),route:"/account/profile"},{label:e("security"),icon:(0,a.jsx)(n,{size:16}),route:"/account/security"}];return(0,a.jsx)(i.F,{items:s})};var d=t(21650),p=t(34091),u=t(29494),m=t(28559),x=t(85814),h=t.n(x);let f=({children:e})=>{let{user:s}=(0,d.A)(),t=(0,o.c3)(),{data:l,isLoading:n,isError:i}=(0,u.I)({queryKey:["profile",s?.id],queryFn:p.l2,enabled:!!s?.id});return n?(0,a.jsx)(r.A,{}):i?(0,a.jsx)("p",{className:"text-red-500",children:t("profileError")}):(0,a.jsxs)("div",{className:"section flex flex-col gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h1",{className:"heading-text capitalize",children:l?.name}),(0,a.jsxs)(h(),{href:"/dashboard",className:"flex items-center gap-2",children:[(0,a.jsx)(m.A,{size:16}),t("backToDashboard")]})]}),(0,a.jsx)(c,{}),(0,a.jsx)("div",{className:"px-8",children:e})]})}},28559:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34091:(e,s,t)=>{"use strict";t.d(s,{BZ:()=>l,eg:()=>n,ep:()=>i,kH:()=>o,l2:()=>r});var a=t(12810);let r=async()=>{let{data:e}=await a.A.get("/users/profile");return e.profile},l=async({email:e})=>{let{data:s}=await a.A.patch("/users/change-email",{email:e});return s},n=async({dataToSend:e})=>{let{data:s}=await a.A.patch("/users/update",e);return s},i=async()=>{let{data:e}=await a.A.get("/users/sessions");return e.sessions},o=async e=>{let{data:s}=await a.A.post("/users/sendverificationemail",{email:e});return s}},50964:(e,s,t)=>{Promise.resolve().then(t.bind(t,87186))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58869:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73678:(e,s,t)=>{"use strict";t.d(s,{R:()=>l});var a=t(60687);t(43210);var r=t(38587);let l=({showModal:e,onClose:s,onConfirm:t,title:l,description:n,confirmButtonText:i,cancelButtonText:o,confirmButtonClass:c,children:d})=>(0,a.jsxs)(r.A,{isOpen:e,onClose:s,className:"p-6 rounded-md max-w-xl",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-neutral-700",children:l}),(0,a.jsx)("div",{className:"text-neutral-700 mt-2",children:n}),d&&(0,a.jsx)("div",{className:"mt-6 space-y-4",children:d}),(0,a.jsxs)("div",{className:"flex justify-end gap-4 mt-6",children:[(0,a.jsx)("button",{className:"btn-outline",onClick:s,type:"button",children:o||"Cancel"}),(0,a.jsx)("button",{className:`font-medium rounded-md shadow-md text-neutral-100 flex items-center justify-center gap-2 px-4 py-2 active:scale-95 transition-all duration-300 ${c}`,onClick:t,type:"button",children:i})]})]})},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81593:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>S});var a=t(60687),r=t(43210),l=t.n(r),n=t(62688);let i=(0,n.A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]),o=(0,n.A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]);var c=t(19169),d=t(19150),p=t(12810),u=t(12597),m=t(13861),x=t(27605),h=t(54864),f=t(77618);let y=()=>{let e=(0,f.c3)(),[s,t]=(0,r.useState)(!1),[l,n]=(0,r.useState)(!1),[i,o]=(0,r.useState)(!1),[c,y]=(0,r.useState)(!1),j=(0,h.wA)(),{register:b,handleSubmit:g,formState:{errors:w},setValue:v,watch:N}=(0,x.mN)({defaultValues:{currentPassword:"",newPassword:"",confirmPassword:"",email:""}}),_=N("newPassword");N("confirmPassword");let k=async s=>{let{currentPassword:a,newPassword:r,confirmPassword:l}=s;if(r!==l)return void j((0,d.Ds)({message:e("password_mismatch"),type:"error"}));try{let s=await p.A.post("/users/changepassword",{currentPassword:a,newPassword:r,confirmPassword:l});200===s.status&&(j((0,d.Ds)({message:e("password_changed_successfully"),type:"success"})),t(!1),v("currentPassword",""),v("newPassword",""),v("confirmPassword",""))}catch(e){j((0,d.Ds)({message:e.response?.data?.message||"Server error",type:"error"}))}};return(0,a.jsx)("div",{children:s?(0,a.jsxs)("form",{onSubmit:g(k),className:"flex-col flex gap-4",children:[(0,a.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,a.jsxs)("div",{className:"label-input-group group",children:[(0,a.jsx)("label",{htmlFor:"current-password",className:"label-text",children:e("current_password")}),(0,a.jsxs)("div",{className:"relative laptop:w-1/3",children:[(0,a.jsx)("input",{id:"current-password",type:l?"text":"password",placeholder:e("enter_current_password"),className:"input-field w-full pr-10",...b("currentPassword",{required:e("current_password_required")})}),(0,a.jsxs)("button",{type:"button",tabIndex:-1,className:"absolute right-2 top-1/2 -translate-y-1/2 text-gray-500",onClick:()=>n(!l),children:[l?(0,a.jsx)(u.A,{className:"h-4 w-4"}):(0,a.jsx)(m.A,{className:"h-4 w-4"}),(0,a.jsxs)("span",{className:"sr-only",children:[l?"Hide":"Show"," password"]})]})]}),w.currentPassword&&(0,a.jsx)("p",{className:"text-red-500 text-sm",children:w.currentPassword.message})]}),(0,a.jsxs)("div",{className:"label-input-group group",children:[(0,a.jsx)("label",{htmlFor:"new-password",className:"label-text",children:e("new_password")}),(0,a.jsxs)("div",{className:"relative laptop:w-1/3",children:[(0,a.jsx)("input",{id:"new-password",type:i?"text":"password",placeholder:e("enter_new_password"),className:"input-field w-full pr-10",...b("newPassword",{required:e("new_password_required"),minLength:{value:6,message:e("password_min_length")},validate:s=>s!==N("currentPassword")||e("new_password_must_be_different")})}),(0,a.jsxs)("button",{type:"button",tabIndex:-1,className:"absolute right-2 top-1/2 -translate-y-1/2 text-gray-500",onClick:()=>o(!i),children:[i?(0,a.jsx)(u.A,{className:"h-4 w-4"}):(0,a.jsx)(m.A,{className:"h-4 w-4"}),(0,a.jsxs)("span",{className:"sr-only",children:[i?"Hide":"Show"," password"]})]})]}),w.newPassword&&(0,a.jsx)("p",{className:"text-red-500 text-sm",children:w.newPassword.message})]}),(0,a.jsxs)("div",{className:"label-input-group group",children:[(0,a.jsx)("label",{htmlFor:"confirm-password",className:"label-text",children:e("confirm_password")}),(0,a.jsxs)("div",{className:"relative laptop:w-1/3",children:[(0,a.jsx)("input",{id:"confirm-password",type:c?"text":"password",placeholder:e("enter_confirm_password"),className:"input-field w-full pr-10",...b("confirmPassword",{required:e("confirm_password_required"),validate:s=>s===_||e("passwords_do_not_match")})}),(0,a.jsxs)("button",{type:"button",tabIndex:-1,className:"absolute right-2 top-1/2 -translate-y-1/2 text-gray-500",onClick:()=>y(!c),children:[c?(0,a.jsx)(u.A,{className:"h-4 w-4"}):(0,a.jsx)(m.A,{className:"h-4 w-4"}),(0,a.jsxs)("span",{className:"sr-only",children:[c?"Hide":"Show"," password"]})]})]}),w.confirmPassword&&(0,a.jsx)("p",{className:"text-red-500 text-sm",children:w.confirmPassword.message})]})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)("button",{type:"submit",className:"btn-primary",children:e("update_password")}),(0,a.jsx)("button",{className:"btn-outline",onClick:()=>t(!1),children:e("cancel")})]})]}):(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsx)("button",{className:"btn-primary",onClick:()=>t(!0),children:e("change_password")})})})};var j=t(56090),b=t(93772),g=t(96752);function w({columns:e,data:s}){let[t,r]=l().useState({pageIndex:0,pageSize:8}),n=(0,j.N4)({data:s,columns:e,onPaginationChange:r,getPaginationRowModel:(0,b.kW)(),getCoreRowModel:(0,b.HT)(),state:{pagination:t}}),i=(0,f.c3)();return(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"rounded-md border border-neutral-400 overflow-hidden",children:(0,a.jsxs)(g.XI,{children:[(0,a.jsx)(g.A0,{children:n.getHeaderGroups().map(e=>(0,a.jsx)(g.Hj,{className:"text-sm border-neutral-400",children:e.headers.map(e=>(0,a.jsx)(g.nd,{className:"py-1 px-6 text-left bg-primary-500 text-neutral-100 font-semibold",children:e.isPlaceholder?null:(0,j.Kv)(e.column.columnDef.header,e.getContext())},e.id))},e.id))}),(0,a.jsx)(g.BF,{children:n.getRowModel().rows?.length?n.getRowModel().rows.map(e=>(0,a.jsx)(g.Hj,{className:" text-sm border-neutral-400","data-state":e.getIsSelected()&&"selected",children:e.getVisibleCells().map(e=>(0,a.jsx)(g.nA,{className:"py-4 px-6",children:(0,j.Kv)(e.column.columnDef.cell,e.getContext())},e.id))},e.id)):(0,a.jsx)(g.Hj,{children:(0,a.jsx)(g.nA,{colSpan:e.length,className:"h-24 text-center",children:i("no_results")})})})]})}),s.length>t.pageSize&&(0,a.jsxs)("div",{className:"flex items-center justify-end space-x-2 py-4",children:[(0,a.jsx)("button",{className:"btn-primary",onClick:()=>n.previousPage(),disabled:!n.getCanPreviousPage(),children:i("previous")}),(0,a.jsx)("button",{className:"btn-primary",onClick:()=>n.nextPage(),disabled:!n.getCanNextPage(),children:i("next")})]})]})}var v=t(96241);let N=(e,s,t)=>{if(null==e)return"-";if("boolean"==typeof e)return e?t?t("yes"):"Yes":t?t("no"):"No";if(e instanceof Date)return(0,v.Y)(e);if("date"===s&&"string"==typeof e)try{return(0,v.Y)(new Date(e))}catch{return e}return String(e)},_=()=>{let e=(0,f.c3)();return[{accessorKey:"deviceInfo",header:e("device")},{accessorKey:"browserInfo",header:e("browser")},{accessorKey:"updatedAt",header:e("last_activity"),cell:({getValue:s})=>{let t=s();return(0,a.jsx)("div",{className:"font-medium text-neutral-700",children:N(t,"date",e)||e("notRecorded")})}},{accessorKey:"ipAddress",header:e("ip_address")},{accessorKey:"isActive",header:e("status"),cell:({getValue:s})=>s()?e("active"):e("inactive")}]};var k=t(21650),P=t(29494),C=t(8693),A=t(54050),q=t(34091),M=t(86429),E=t(73678),D=t(1510);let S=()=>{let[e,s]=(0,r.useState)(!1),{user:t,logout:l}=(0,k.A)(),n=(0,f.c3)(),p=_(),{register:u,formState:{errors:m},handleSubmit:j,getValues:b,reset:g,setError:v}=(0,x.mN)(),{data:N,isLoading:S,isError:K}=(0,P.I)({queryKey:["sessions",t?.id],queryFn:q.ep,enabled:!!t?.id}),z=(0,h.wA)(),F=(0,C.jE)(),[H,I]=(0,r.useState)(!1),O=(0,A.n)({mutationFn:q.BZ,onSuccess:async()=>{try{await F.invalidateQueries({queryKey:["profile",t?.id]}),await (0,q.kH)(b("email")),s(!1),z((0,d.Ds)({message:n("email_change_success"),type:"success"})),l()}catch(e){z((0,d.Ds)({message:n("email_change_verificationFailed"),type:"warning"}))}},onError:e=>{e instanceof D.pe?v(e.response?.data.errorField,{message:e.response?.data.message}):z((0,d.Ds)({message:n("email_change_failed"),type:"error"}))}});(0,r.useEffect)(()=>{e||g()},[e,g]);let[R,U]=(0,r.useState)(!1);(0,r.useEffect)(()=>{U(!0)},[]);let G=async e=>{O.mutate({email:e.email})};return R?S?(0,a.jsx)(M.A,{}):K||!N?(0,a.jsxs)("p",{className:"text-sm text-red-500",children:[" ",n("error_loading_data")]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(E.R,{showModal:H,onClose:()=>I(!1),onConfirm:()=>{j(G)(),I(!1)},title:n("email_change_confirm"),description:n("email_change_warning"),confirmButtonText:n("change"),confirmButtonClass:"btn-primary"}),(0,a.jsxs)("div",{className:"flex flex-col gap-10",children:[(0,a.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(i,{className:"h-8 w-8"}),(0,a.jsx)("h2",{className:"heading-text",children:n("security_settings")})]}),(0,a.jsx)("p",{className:"sub-text",children:n("account_security_settings")})]}),(0,a.jsxs)("div",{className:"flex-col gap-10 flex",children:[(0,a.jsxs)("div",{className:"flex flex-col gap-5 shadow-sm border-muted p-4",children:[(0,a.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(o,{className:"h-5 w-5"}),(0,a.jsx)("h2",{className:"sub-heading-text",children:n("password")})]}),(0,a.jsx)("p",{className:"sub-text",children:n("account_update_password")})]}),(0,a.jsx)("div",{children:(0,a.jsx)(y,{})})]}),(0,a.jsxs)("div",{className:"flex flex-col gap-5 shadow-sm border-muted p-4",children:[(0,a.jsxs)("div",{className:"flex flex-col gap-2 ",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(c.A,{className:"h-5 w-5"}),(0,a.jsx)("h2",{className:"sub-heading-text",children:n("email_address")})]}),(0,a.jsx)("p",{className:"sub-text",children:n("account_email_usage")})]}),(0,a.jsx)("div",{children:e?(0,a.jsxs)("form",{className:"space-y-4",noValidate:!0,onSubmit:e=>e.preventDefault(),children:[(0,a.jsx)("input",{...u("email",{required:n("enter_new_email")}),type:"email",placeholder:"eg: <EMAIL>",className:"input-field"}),m.email&&(0,a.jsx)("p",{className:"text-sm text-red-500",children:`${m.email?.message}`}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)("button",{type:"button",className:"btn-primary",onClick:()=>I(!0),children:n("save")}),(0,a.jsx)("button",{type:"button",className:"btn-outline",onClick:()=>s(!1),children:n("cancel")})]})]}):(0,a.jsxs)("div",{className:"flex justify-between items-center ",children:[(0,a.jsx)("span",{children:t?.email}),(0,a.jsx)("button",{type:"button",className:"btn-primary",onClick:()=>s(!0),children:n("change")})]})})]})]}),(0,a.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,a.jsxs)("h2",{className:"heading-text",children:[n("recent_account_activity")," "]}),(0,a.jsx)(w,{columns:p,data:N})]})]})]}):null}},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},87186:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Kobo-project\\\\data-analysis-tool-frontend\\\\app\\\\[locale]\\\\(main)\\\\account\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\account\\layout.tsx","default")},87348:(e,s,t)=>{Promise.resolve().then(t.bind(t,28555))},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[4447,7404,1658,6560,8610,5374,2198,5814,3851,8581,5841,5041],()=>t(14368));module.exports=a})();