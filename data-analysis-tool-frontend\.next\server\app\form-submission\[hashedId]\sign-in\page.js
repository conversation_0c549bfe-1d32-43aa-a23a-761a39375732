(()=>{var e={};e.id=7969,e.ids=[7969],e.modules={1510:(e,t,s)=>{"use strict";s.d(t,{F0:()=>u,pe:()=>i});let{Axios:r,AxiosError:i,CanceledError:a,isCancel:o,CancelToken:n,VERSION:l,all:d,Cancel:c,isAxiosError:u,spread:m,toFormData:p,AxiosHeaders:h,HttpStatusCode:x,formToJSON:f,getAdapter:b,mergeConfig:v}=s(51060).A},2828:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>v});var r=s(60687),i=s(3204),a=s(19150),o=s(63442),n=s(38038),l=s(12597),d=s(13861),c=s(16189),u=s(43210),m=s(27605),p=s(54864),h=s(45880),x=s(21650),f=s(86429),b=s(77618);let v=()=>{let e=(0,b.c3)(),t=h.z.object({email:h.z.string().min(1,e("emailRequired")).email(e("invalidEmail")),password:h.z.string().min(1,e("passwordRequired"))}),{register:s,formState:{errors:v,isSubmitting:y},handleSubmit:g,getValues:j,watch:w}=(0,m.mN)({resolver:(0,o.u)(t)}),N=w("password"),P=(0,c.useRouter)(),A=(0,p.wA)(),{hashedId:C}=(0,c.useParams)(),k=`/form-submission/${C}`,[E,R]=(0,u.useState)(!1),[I,q]=(0,u.useState)(!1),{signin:M,isAuthenticated:S,isLoading:_}=(0,x.A)();(0,u.useEffect)(()=>{!_&&S&&P.push(k)},[_,S,P,k]);let L=async t=>{M({email:t.email,password:t.password},()=>{A((0,a.Ds)({message:e("signInSuccessful"),type:"success"})),P.push(k)},t=>{"unverified"===t?R(!0):A((0,a.Ds)({message:e("invalidEmailOrPassword"),type:"error"}))})};return _?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsx)(f.A,{})}):S?null:(0,r.jsxs)("div",{className:"min-h-screen flex items-center justify-center",children:[(0,r.jsx)(i.x,{email:j("email"),showModal:E,setShowModal:R}),(0,r.jsxs)("div",{className:"flex flex-col section w-11/12 mobile:w-4/5 tablet:w-lg",children:[(0,r.jsxs)("div",{className:"flex flex-col items-center gap-2 mb-8",children:[(0,r.jsx)(n.A,{size:36}),(0,r.jsx)("h1",{className:"text-2xl tablet:text-3xl font-semibold text-center",children:e("signInToAccessForm")}),(0,r.jsx)("p",{className:"text-neutral-700 text-center",children:e("loginToCompleteForm")})]}),(0,r.jsxs)("form",{className:"flex flex-col gap-4 mb-4",onSubmit:g(L),children:[(0,r.jsxs)("div",{className:"group label-input-group",children:[(0,r.jsx)("label",{htmlFor:"email",className:"label-text",children:e("email")}),(0,r.jsx)("input",{...s("email"),id:"email",type:"email",placeholder:e("enterEmail"),className:"input-field"}),v.email&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:`${v.email.message}`})]}),(0,r.jsxs)("div",{className:"group label-input-group",children:[(0,r.jsx)("label",{htmlFor:"password",className:"label-text",children:e("password")}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{...s("password"),id:"password",type:I?"text":"password",placeholder:e("enterPassword"),className:"input-field w-full pr-10"}),N&&N.length>0&&(0,r.jsxs)("button",{type:"button",tabIndex:-1,className:"absolute right-2 top-1/2 -translate-y-1/2 text-gray-500",onClick:()=>q(!I),children:[I?(0,r.jsx)(l.A,{className:"h-4 w-4"}):(0,r.jsx)(d.A,{className:"h-4 w-4"}),(0,r.jsxs)("span",{className:"sr-only",children:[I?"Hide":"Show"," password"]})]})]}),v.password&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:`${v.password.message}`})]}),(0,r.jsx)("button",{type:"submit",className:"btn-primary",disabled:y,children:y?(0,r.jsxs)("span",{className:"flex items-center gap-2",children:[e("signingIn"),(0,r.jsx)("div",{className:"size-4 rounded-full border-x-2 animate-spin"})]}):e("submit")})]})]})]})}},3204:(e,t,s)=>{"use strict";s.d(t,{x:()=>m});var r=s(60687),i=s(43210),a=s(38587),o=s(19169),n=s(43649),l=s(12810),d=s(54864),c=s(19150),u=s(77618);let m=({email:e,showModal:t,setShowModal:s})=>{let m=(0,d.wA)(),p=(0,u.c3)(),h=async()=>{try{await l.A.post("/users/sendverificationemail",{email:e})}catch(e){m((0,c.Ds)({message:p("failedToSendVerification"),type:"error"}))}},[x,f]=(0,i.useState)(!0),[b,v]=(0,i.useState)(60);(0,i.useEffect)(()=>{let e;return x&&b>0?e=window.setInterval(()=>{v(e=>e-1)},1e3):0===b&&(f(!1),v(60)),()=>clearInterval(e)},[x,b]),(0,i.useEffect)(()=>(t&&e&&h(),()=>{v(60),f(!0)}),[t]);let y=async()=>{f(!0);try{await l.A.post("/users/sendverificationemail",{email:e})}catch(e){console.error("error sending email",e)}};return(0,r.jsxs)(a.A,{isOpen:t,onClose:()=>s(!1),className:"flex flex-col items-center gap-4",children:[(0,r.jsx)("div",{className:"rounded-full p-2 bg-primary-300",children:(0,r.jsx)(o.A,{className:"text-primary-500"})}),(0,r.jsx)("h1",{className:"heading-text",children:p("checkYourEmail")}),(0,r.jsxs)("p",{className:"flex flex-col items-center",children:[p("verificationSentTo"),(0,r.jsx)("span",{className:"font-medium",children:e})]}),(0,r.jsxs)("div",{className:"flex gap-2 items-center bg-yellow-100 text-yellow-900 px-4 py-2 rounded-md",children:[(0,r.jsx)(n.A,{size:16})," ",p("didNotReceiveEmail")]}),(0,r.jsx)("button",{className:"btn-primary",onClick:y,disabled:x,children:x?(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsxs)("span",{children:[p("resendIn")," ",b,"s"]}),(0,r.jsx)("div",{className:"size-4 animate-spin border-x-2 rounded-full"})]}):(0,r.jsx)("span",{children:p("resend")})})]})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10125:(e,t,s)=>{"use strict";s.d(t,{Notification:()=>m});var r=s(60687),i=s(43210),a=s(54864),o=s(88920),n=s(57101),l=s(19150),d=s(14719),c=s(43649),u=s(93613);let m=()=>{let e=(0,a.wA)(),{message:t,type:s,visible:m}=(0,a.d4)(e=>e.notification);(0,i.useEffect)(()=>{if(m){let t=setTimeout(()=>{e((0,l._b)())},5e3);return()=>clearTimeout(t)}},[m,e]);let p="success"===s?(0,r.jsx)(d.A,{}):"warning"===s?(0,r.jsx)(c.A,{}):(0,r.jsx)(u.A,{});return(0,r.jsx)(o.N,{children:m&&(0,r.jsxs)(n.P.div,{className:`z-50 fixed top-0 right-0 m-4 px-4 py-2 rounded font-semibold w-auto max-w-xs flex items-center gap-2 cursor-pointer ${"success"===s?"bg-green-500 hover:bg-green-600":"warning"===s?"bg-yellow-500 hover:bg-yellow-600":"bg-red-500 hover:bg-red-600"} transition-colors duration-300`,onClick:()=>e((0,l._b)()),initial:{opacity:0,y:-20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.3,ease:"easeIn"},children:[(0,r.jsx)("span",{className:"text-2xl",children:p}),(0,r.jsx)("span",{className:"break-words neutral-100space-normal",children:t})]})})}},10271:(e,t,s)=>{"use strict";s.d(t,{ReactQueryProvider:()=>l});var r=s(60687),i=s(43210),a=s(39091),o=s(8693),n=s(9124);let l=({children:e})=>{let[t]=(0,i.useState)(()=>new a.E({defaultOptions:{queries:{staleTime:3e5,refetchOnWindowFocus:!1}}}));return(0,r.jsxs)(o.Ht,{client:t,children:[e,(0,r.jsx)(n.E,{initialIsOpen:!1})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11860:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},12412:e=>{"use strict";e.exports=require("assert")},12810:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});let r=s(51060).A.create({baseURL:"http://localhost:4000/api",headers:{"Content-Type":"application/json"},withCredentials:!0});r.interceptors.request.use(e=>e,e=>Promise.reject(e)),r.interceptors.response.use(e=>e,e=>("ERR_NETWORK"===e.code&&console.error("Network error - Please check if the backend server is running"),Promise.reject(e)));let i=r},16319:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,86346,23)),Promise.resolve().then(s.t.bind(s,27924,23)),Promise.resolve().then(s.t.bind(s,35656,23)),Promise.resolve().then(s.t.bind(s,40099,23)),Promise.resolve().then(s.t.bind(s,38243,23)),Promise.resolve().then(s.t.bind(s,28827,23)),Promise.resolve().then(s.t.bind(s,62763,23)),Promise.resolve().then(s.t.bind(s,97173,23))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19150:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>o,Ds:()=>i,_b:()=>a});let r=(0,s(9317).Z0)({name:"notification",initialState:{message:"",type:"",visible:!1},reducers:{showNotification:(e,t)=>{e.message=t.payload.message,e.type=t.payload.type,e.visible=!0},hideNotification:e=>{e.message="",e.type="",e.visible=!1}}}),{showNotification:i,hideNotification:a}=r.actions,o=r.reducer},19169:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},21650:(e,t,s)=>{"use strict";s.d(t,{A:()=>d});var r=s(42895),i=s(1510),a=s(12810),o=s(16189),n=s(43210),l=s(54864);let d=e=>{let t=(0,l.wA)(),s=(0,o.useRouter)(),d=(0,o.usePathname)(),{status:c,user:u,error:m}=(0,l.d4)(e=>e.auth),p=async()=>{try{t((0,r.Le)());let e=(await a.A.get("/users/me")).data;t((0,r.tQ)(e))}catch(e){if(t((0,r.x9)()),(0,i.F0)(e))if(console.error("Auth error:",e.response?.status,e.response?.data),e.response?.status===401){if(d.startsWith("/form-submission"))return;s.push("/")}else t((0,r.jB)(e.response?.data?.message||e.message));else t((0,r.jB)(e instanceof Error?e.message:"An unknown error occurred."))}};return(0,n.useEffect)(()=>{e?.skipFetchUser||p()},[e?.skipFetchUser]),(0,n.useEffect)(()=>{let e=e=>{if("logout"===e.key&&"true"===e.newValue)if(t((0,r.x9)()),d.startsWith("/form-submission")){let e=d.split("/")[2];e?s.push(`/form-submission/${e}/sign-in`):s.push("/")}else s.push("/")};return window.addEventListener("storage",e),()=>{window.removeEventListener("storage",e)}},[t,s,d]),{status:c,user:u,error:m,isAuthenticated:"authenticated"===c,isLoading:"loading"===c,refreshAuthState:()=>{p()},signin:async(e,t,s)=>{try{await a.A.post("/users/login",e),await p(),t?.()}catch(e){if(e instanceof i.pe){let t=e.response?.data?.errorType;s?.(t)}else s?.()}},logout:async()=>{try{await a.A.post("/users/logout"),localStorage.setItem("logout","true"),setTimeout(()=>localStorage.removeItem("logout"),100)}finally{if(t((0,r.x9)()),d.startsWith("/form-submission")){let e=d.split("/")[2];e?s.push(`/form-submission/${e}/sign-in`):s.push("/")}else s.push("/")}}}}},21820:e=>{"use strict";e.exports=require("os")},25390:(e,t,s)=>{Promise.resolve().then(s.bind(s,2828))},26946:(e,t,s)=>{Promise.resolve().then(s.bind(s,10125)),Promise.resolve().then(s.bind(s,10271)),Promise.resolve().then(s.bind(s,49271))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30844:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var r=s(65239),i=s(48088),a=s(88170),o=s.n(a),n=s(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);s.d(t,l);let d={children:["",{children:["form-submission",{children:["[hashedId]",{children:["sign-in",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,58918)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\form-submission\\[hashedId]\\sign-in\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,58014)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\form-submission\\[hashedId]\\sign-in\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/form-submission/[hashedId]/sign-in/page",pathname:"/form-submission/[hashedId]/sign-in",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},33873:e=>{"use strict";e.exports=require("path")},35790:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>o,l:()=>a,yg:()=>i});let r=(0,s(9317).Z0)({name:"createLibraryItem",initialState:{visible:!1},reducers:{showCreateLibraryModal:e=>{e.visible=!0},hideCreateLibraryModal:e=>{e.visible=!1}}}),{showCreateLibraryModal:i,hideCreateLibraryModal:a}=r.actions,o=r.reducer},38038:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("shield-check",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},38587:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var r=s(60687),i=s(88920),a=s(57101),o=s(74699),n=s(11860);s(43210);let l=({children:e,className:t,isOpen:s,onClose:l,preventOutsideClick:d=!1})=>(0,r.jsx)(i.N,{children:s&&(0,r.jsx)(a.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-neutral-900/50 flex items-center justify-center z-40 p-4 overflow-y-auto",onClick:e=>{d||l()},children:(0,r.jsxs)(a.P.div,{initial:{scale:.6,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.6,opacity:0},transition:{duration:.3,ease:o.am},className:`relative section flex flex-col max-w-4xl max-h-[90vh] overflow-y-auto ${t}`,onClick:e=>e.stopPropagation(),children:[(0,r.jsx)(n.A,{onClick:l,className:"absolute self-end cursor-pointer text-neutral-700 hover:text-neutral-900 active:scale-90 transition-all duration-300"}),e]})})})},42895:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>l,Le:()=>o,jB:()=>n,tQ:()=>i,x9:()=>a});let r=(0,s(9317).Z0)({name:"auth",initialState:{status:"loading",user:null,error:null},reducers:{setAuthenticatedUser:(e,t)=>{e.status="authenticated",e.user=t.payload,e.error=null},setUnauthenticated:e=>{e.status="unauthenticated",e.user=null,e.error=null},setAuthLoading:e=>{e.status="loading"},setAuthError:(e,t)=>{e.status="unauthenticated",e.error=t.payload,e.user=null}}}),{setAuthenticatedUser:i,setUnauthenticated:a,setAuthLoading:o,setAuthError:n}=r.actions,l=r.reducer},44395:(e,t,s)=>{"use strict";s.d(t,{Notification:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call Notification() from the server but Notification is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\components\\general\\Notification.tsx","Notification")},46055:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var r=s(31658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},48358:(e,t,s)=>{Promise.resolve().then(s.bind(s,58918))},49271:(e,t,s)=>{"use strict";s.d(t,{ReduxProvider:()=>m});var r=s(60687),i=s(9317),a=s(19150),o=s(58432),n=s(42895),l=s(35790),d=s(89011);let c=(0,i.U1)({reducer:{notification:a.Ay,createProject:o.Ay,auth:n.Ay,createLibrary:l.Ay,createLibraryItem:d.Ay}});s(43210);var u=s(54864);let m=({children:e})=>(0,r.jsx)(u.Kq,{store:c,children:e})},50823:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,16444,23)),Promise.resolve().then(s.t.bind(s,16042,23)),Promise.resolve().then(s.t.bind(s,88170,23)),Promise.resolve().then(s.t.bind(s,49477,23)),Promise.resolve().then(s.t.bind(s,29345,23)),Promise.resolve().then(s.t.bind(s,12089,23)),Promise.resolve().then(s.t.bind(s,46577,23)),Promise.resolve().then(s.t.bind(s,31307,23))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58014:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m,metadata:()=>u});var r=s(37413);s(82704);var i=s(7990),a=s.n(i),o=s(60866),n=s.n(o),l=s(77832),d=s(44395),c=s(60265);let u={title:"Data analysis tool",description:"A tool for data collection and analysis."};function m({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:`${a().className} ${n().className} antialiased`,children:(0,r.jsx)(l.ReduxProvider,{children:(0,r.jsxs)(c.ReactQueryProvider,{children:[(0,r.jsx)(d.Notification,{}),(0,r.jsx)("main",{className:"bg-neutral-200",children:e})]})})})})}},58432:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>o,Gl:()=>i,th:()=>a});let r=(0,s(9317).Z0)({name:"createProject",initialState:{visible:!1},reducers:{showCreateProjectModal:e=>{e.visible=!0},hideCreateProjectModal:e=>{e.visible=!1}}}),{showCreateProjectModal:i,hideCreateProjectModal:a}=r.actions,o=r.reducer},58918:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Kobo-project\\\\data-analysis-tool-frontend\\\\app\\\\form-submission\\\\[hashedId]\\\\sign-in\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\form-submission\\[hashedId]\\sign-in\\page.tsx","default")},60265:(e,t,s)=>{"use strict";s.d(t,{ReactQueryProvider:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call ReactQueryProvider() from the server but ReactQueryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\providers\\ReactQueryProvider.tsx","ReactQueryProvider")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},77832:(e,t,s)=>{"use strict";s.d(t,{ReduxProvider:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call ReduxProvider() from the server but ReduxProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\providers\\ReduxProvider.tsx","ReduxProvider")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},82704:()=>{},83997:e=>{"use strict";e.exports=require("tty")},86429:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(60687);s(43210);let i=()=>(0,r.jsx)("div",{className:"w-full flex items-center justify-center",children:(0,r.jsx)("div",{className:"size-8 rounded-full border-x-2 border-primary-500 animate-spin my-16"})})},86778:(e,t,s)=>{Promise.resolve().then(s.bind(s,44395)),Promise.resolve().then(s.bind(s,60265)),Promise.resolve().then(s.bind(s,77832))},89011:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>o,dQ:()=>i,g7:()=>a});let r=(0,s(9317).Z0)({initialState:{visible:!1,option:""},name:"createLibraryItem",reducers:{showCreateLibraryItemModal:(e,t)=>{e.visible=!0,e.option=t.payload},hideCreateLibraryItemModal:e=>{e.visible=!1,e.option=""}}}),{showCreateLibraryItemModal:i,hideCreateLibraryItemModal:a}=r.actions,o=r.reducer},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,7404,1658,6560,7618,7605,4921],()=>s(30844));module.exports=r})();