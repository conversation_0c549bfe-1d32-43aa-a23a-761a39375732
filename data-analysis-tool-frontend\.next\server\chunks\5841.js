exports.id=5841,exports.ids=[5841],exports.modules={10125:(e,t,r)=>{"use strict";r.d(t,{Notification:()=>m});var s=r(60687),i=r(43210),o=r(54864),n=r(88920),a=r(57101),l=r(19150),d=r(14719),c=r(43649),u=r(93613);let m=()=>{let e=(0,o.wA)(),{message:t,type:r,visible:m}=(0,o.d4)(e=>e.notification);(0,i.useEffect)(()=>{if(m){let t=setTimeout(()=>{e((0,l._b)())},5e3);return()=>clearTimeout(t)}},[m,e]);let h="success"===r?(0,s.jsx)(d.A,{}):"warning"===r?(0,s.jsx)(c.A,{}):(0,s.jsx)(u.A,{});return(0,s.jsx)(n.N,{children:m&&(0,s.jsxs)(a.P.div,{className:`z-50 fixed top-0 right-0 m-4 px-4 py-2 rounded font-semibold w-auto max-w-xs flex items-center gap-2 cursor-pointer ${"success"===r?"bg-green-500 hover:bg-green-600":"warning"===r?"bg-yellow-500 hover:bg-yellow-600":"bg-red-500 hover:bg-red-600"} transition-colors duration-300`,onClick:()=>e((0,l._b)()),initial:{opacity:0,y:-20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.3,ease:"easeIn"},children:[(0,s.jsx)("span",{className:"text-2xl",children:h}),(0,s.jsx)("span",{className:"break-words neutral-100space-normal",children:t})]})})}},10271:(e,t,r)=>{"use strict";r.d(t,{ReactQueryProvider:()=>l});var s=r(60687),i=r(43210),o=r(39091),n=r(8693),a=r(9124);let l=({children:e})=>{let[t]=(0,i.useState)(()=>new o.E({defaultOptions:{queries:{staleTime:3e5,refetchOnWindowFocus:!1}}}));return(0,s.jsxs)(n.Ht,{client:t,children:[e,(0,s.jsx)(a.E,{initialIsOpen:!1})]})}},12810:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let s=r(51060).A.create({baseURL:"http://localhost:4000/api",headers:{"Content-Type":"application/json"},withCredentials:!0});s.interceptors.request.use(e=>e,e=>Promise.reject(e)),s.interceptors.response.use(e=>e,e=>("ERR_NETWORK"===e.code&&console.error("Network error - Please check if the backend server is running"),Promise.reject(e)));let i=s},16319:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},19150:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>n,Ds:()=>i,_b:()=>o});let s=(0,r(9317).Z0)({name:"notification",initialState:{message:"",type:"",visible:!1},reducers:{showNotification:(e,t)=>{e.message=t.payload.message,e.type=t.payload.type,e.visible=!0},hideNotification:e=>{e.message="",e.type="",e.visible=!1}}}),{showNotification:i,hideNotification:o}=s.actions,n=s.reducer},21650:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var s=r(42895),i=r(1510),o=r(12810),n=r(16189),a=r(43210),l=r(54864);let d=e=>{let t=(0,l.wA)(),r=(0,n.useRouter)(),d=(0,n.usePathname)(),{status:c,user:u,error:m}=(0,l.d4)(e=>e.auth),h=async()=>{try{t((0,s.Le)());let e=(await o.A.get("/users/me")).data;t((0,s.tQ)(e))}catch(e){if(t((0,s.x9)()),(0,i.F0)(e))if(console.error("Auth error:",e.response?.status,e.response?.data),e.response?.status===401){if(d.startsWith("/form-submission"))return;r.push("/")}else t((0,s.jB)(e.response?.data?.message||e.message));else t((0,s.jB)(e instanceof Error?e.message:"An unknown error occurred."))}};return(0,a.useEffect)(()=>{e?.skipFetchUser||h()},[e?.skipFetchUser]),(0,a.useEffect)(()=>{let e=e=>{if("logout"===e.key&&"true"===e.newValue)if(t((0,s.x9)()),d.startsWith("/form-submission")){let e=d.split("/")[2];e?r.push(`/form-submission/${e}/sign-in`):r.push("/")}else r.push("/")};return window.addEventListener("storage",e),()=>{window.removeEventListener("storage",e)}},[t,r,d]),{status:c,user:u,error:m,isAuthenticated:"authenticated"===c,isLoading:"loading"===c,refreshAuthState:()=>{h()},signin:async(e,t,r)=>{try{await o.A.post("/users/login",e),await h(),t?.()}catch(e){if(e instanceof i.pe){let t=e.response?.data?.errorType;r?.(t)}else r?.()}},logout:async()=>{try{await o.A.post("/users/logout"),localStorage.setItem("logout","true"),setTimeout(()=>localStorage.removeItem("logout"),100)}finally{if(t((0,s.x9)()),d.startsWith("/form-submission")){let e=d.split("/")[2];e?r.push(`/form-submission/${e}/sign-in`):r.push("/")}else r.push("/")}}}}},26946:(e,t,r)=>{Promise.resolve().then(r.bind(r,10125)),Promise.resolve().then(r.bind(r,10271)),Promise.resolve().then(r.bind(r,49271))},35790:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>n,l:()=>o,yg:()=>i});let s=(0,r(9317).Z0)({name:"createLibraryItem",initialState:{visible:!1},reducers:{showCreateLibraryModal:e=>{e.visible=!0},hideCreateLibraryModal:e=>{e.visible=!1}}}),{showCreateLibraryModal:i,hideCreateLibraryModal:o}=s.actions,n=s.reducer},42895:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>l,Le:()=>n,jB:()=>a,tQ:()=>i,x9:()=>o});let s=(0,r(9317).Z0)({name:"auth",initialState:{status:"loading",user:null,error:null},reducers:{setAuthenticatedUser:(e,t)=>{e.status="authenticated",e.user=t.payload,e.error=null},setUnauthenticated:e=>{e.status="unauthenticated",e.user=null,e.error=null},setAuthLoading:e=>{e.status="loading"},setAuthError:(e,t)=>{e.status="unauthenticated",e.error=t.payload,e.user=null}}}),{setAuthenticatedUser:i,setUnauthenticated:o,setAuthLoading:n,setAuthError:a}=s.actions,l=s.reducer},44395:(e,t,r)=>{"use strict";r.d(t,{Notification:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call Notification() from the server but Notification is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\components\\general\\Notification.tsx","Notification")},46055:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(31658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},49271:(e,t,r)=>{"use strict";r.d(t,{ReduxProvider:()=>m});var s=r(60687),i=r(9317),o=r(19150),n=r(58432),a=r(42895),l=r(35790),d=r(89011);let c=(0,i.U1)({reducer:{notification:o.Ay,createProject:n.Ay,auth:a.Ay,createLibrary:l.Ay,createLibraryItem:d.Ay}});r(43210);var u=r(54864);let m=({children:e})=>(0,s.jsx)(u.Kq,{store:c,children:e})},50823:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},58014:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>m,metadata:()=>u});var s=r(37413);r(82704);var i=r(7990),o=r.n(i),n=r(60866),a=r.n(n),l=r(77832),d=r(44395),c=r(60265);let u={title:"Data analysis tool",description:"A tool for data collection and analysis."};function m({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:`${o().className} ${a().className} antialiased`,children:(0,s.jsx)(l.ReduxProvider,{children:(0,s.jsxs)(c.ReactQueryProvider,{children:[(0,s.jsx)(d.Notification,{}),(0,s.jsx)("main",{className:"bg-neutral-200",children:e})]})})})})}},58432:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>n,Gl:()=>i,th:()=>o});let s=(0,r(9317).Z0)({name:"createProject",initialState:{visible:!1},reducers:{showCreateProjectModal:e=>{e.visible=!0},hideCreateProjectModal:e=>{e.visible=!1}}}),{showCreateProjectModal:i,hideCreateProjectModal:o}=s.actions,n=s.reducer},60265:(e,t,r)=>{"use strict";r.d(t,{ReactQueryProvider:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call ReactQueryProvider() from the server but ReactQueryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\providers\\ReactQueryProvider.tsx","ReactQueryProvider")},77832:(e,t,r)=>{"use strict";r.d(t,{ReduxProvider:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call ReduxProvider() from the server but ReduxProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\providers\\ReduxProvider.tsx","ReduxProvider")},82704:()=>{},86778:(e,t,r)=>{Promise.resolve().then(r.bind(r,44395)),Promise.resolve().then(r.bind(r,60265)),Promise.resolve().then(r.bind(r,77832))},89011:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>n,dQ:()=>i,g7:()=>o});let s=(0,r(9317).Z0)({initialState:{visible:!1,option:""},name:"createLibraryItem",reducers:{showCreateLibraryItemModal:(e,t)=>{e.visible=!0,e.option=t.payload},hideCreateLibraryItemModal:e=>{e.visible=!1,e.option=""}}}),{showCreateLibraryItemModal:i,hideCreateLibraryItemModal:o}=s.actions,n=s.reducer}};