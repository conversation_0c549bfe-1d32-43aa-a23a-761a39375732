(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7767],{5287:(e,r,t)=>{"use strict";t.d(r,{GN:()=>o,J6:()=>i,O8:()=>n,s4:()=>a});var s=t(25784);let n=async(e,r)=>{try{let{data:t}=await s.A.delete("/form-submissions/".concat(e,"?projectId=").concat(r));return t}catch(e){throw console.error("Error deleting form submission:",e),e}},i=async(e,r)=>{try{let t=e.map(e=>s.A.delete("/form-submissions/".concat(e,"?projectId=").concat(r)));return(await Promise.all(t)).map(e=>e.data)}catch(e){throw console.error("Error deleting multiple form submissions:",e),e}},a=async(e,r)=>{try{if(!e.submissionId||!e.questionId)throw Error("submissionId and questionId are required");let t={...e};null===t.questionOptionId?delete t.questionOptionId:Array.isArray(t.questionOptionId)&&(t.questionOptionId=t.questionOptionId.filter(e=>null!=e),0===t.questionOptionId.length&&delete t.questionOptionId);let{data:n}=await s.A.patch("/answers/".concat(e.questionId,"?projectId=").concat(r),t);return n}catch(e){throw console.error("Error updating answer:",e),e}},o=async(e,r)=>{try{let{data:t}=await s.A.patch("/answers/multiple?projectId=".concat(r),e);return t}catch(e){throw console.error("Error updating multiple answers with endpoint:",e),e}}},37818:(e,r,t)=>{Promise.resolve().then(t.bind(t,60289))},52707:(e,r,t)=>{"use strict";t.d(r,{XV:()=>n,cZ:()=>o,ru:()=>i,yi:()=>s});let s=(e,r)=>{let t=new Map;e.forEach(e=>{let s=r.filter(r=>r.questionGroupId===e.id).sort((e,r)=>e.position-r.position);t.set(e.id,{...e,subGroups:[],question:s})});let s=[];return e.forEach(e=>{let r=t.get(e.id);if(e.parentGroupId){let s=t.get(e.parentGroupId);s&&(s.subGroups=s.subGroups||[],s.subGroups.push(r))}else s.push(r)}),s},n=(e,r)=>{let t=[];return e.forEach(e=>{let r=e=>[...e.question||[],...(e.subGroups||[]).flatMap(r)],s=r(e),n=s.length>0?Math.min(...s.map(e=>e.position)):e.order;t.push({type:"group",data:e,order:n,originalPosition:n})}),r.forEach(e=>{t.push({type:"question",data:e,order:e.position,originalPosition:e.position})}),t.sort((e,r)=>e.order===r.order?(e.originalPosition||e.order)-(r.originalPosition||r.order):e.order-r.order)},i=e=>e.filter(e=>null===e.questionGroupId||void 0===e.questionGroupId),a=e=>{let r=[];return e.forEach(e=>{r.push(e.id),e.subGroups&&e.subGroups.length>0&&r.push(...a(e.subGroups))}),r},o=function(e){let r=!(arguments.length>1)||void 0===arguments[1]||arguments[1],t={};return a(e).forEach(e=>{t[e]=r}),t}},60289:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>k});var s=t(95155),n=t(35695),i=t(19373),a=t(25784),o=t(88570),l=t(34947),u=t(57799),d=t(12115),c=t(82714),p=t(99474),m=t(95139),h=t(55747),y=t(92138),g=t(5041),f=t(34540),b=t(71402),v=t(16112),x=t(5287),w=t(10150),j=t(77361),q=t(3587),I=t(13388),N=t(80423),O=t(52707);function S(e){let{questions:r,submission:t,projectId:n,submissionId:o,onClose:l,onSave:u}=e,S=(0,f.wA)(),[E,k]=(0,d.useState)({}),[A,T]=(0,d.useState)({}),[G,C]=(0,d.useState)({}),[F,J]=(0,d.useState)(!1),[R,D]=(0,d.useState)([]),[Q,_]=(0,d.useState)([]),[M,P]=(0,d.useState)({}),[L,K]=(0,d.useState)(new Set),Y=(0,d.useRef)(new Set),z=(0,d.useRef)(!1),U=(0,d.useRef)(""),{data:V=[]}=(0,i.I)({queryKey:["questionGroups",n],queryFn:()=>(0,w.pr)({projectId:n}),enabled:!!n}),{data:X}=(0,i.I)({queryKey:["project",n],queryFn:()=>(0,j.kf)({projectId:n}),enabled:!!n});(0,d.useEffect)(()=>{let e={};r.forEach(r=>{if("selectmany"===r.inputType){let s=t.answers.filter(e=>e.question.id===r.id);e[r.id]=s.map(e=>e.value).filter(e=>null!=e&&""!==String(e).trim())}else{var s;let n=t.answers.find(e=>e.question.id===r.id);e[r.id]=null!=(s=null==n?void 0:n.value)?s:""}}),k(e),T(JSON.parse(JSON.stringify(e)))},[r,t]),(0,d.useEffect)(()=>{if(!r||0===Object.keys(A).length)return;let e=JSON.stringify(E);if(e===U.current)return;let t=(0,q.UL)(r,E),s=new Set(t.map(e=>e.id));if(D(t),_((0,q.Tr)(r,E)),!z.current){Y.current=s,z.current=!0,U.current=e;return}if(!(s.size!==Y.current.size||[...s].some(e=>!Y.current.has(e)))){U.current=e;return}let n=new Set([...s].filter(e=>!Y.current.has(e))),i=!1,a={...E};n.size>0&&n.forEach(e=>{let r=e.toString();!L.has(e)&&(void 0===E[r]||""===E[r]||Array.isArray(E[r])&&0===E[r].length)&&void 0!==A[r]&&""!==A[r]&&!(Array.isArray(A[r])&&0===A[r].length)&&(a[r]=A[r],i=!0)});let o=(0,q.OD)(a,t),l=Object.keys(o).length!==Object.keys(a).length;Y.current=s,i||l?(U.current=JSON.stringify(o),k(o)):U.current=e},[r,E,A]);let H=(0,d.useMemo)(()=>(0,O.yi)(V,r),[V,r]);(0,d.useEffect)(()=>{H.length>0&&P((0,O.cZ)(H,!0))},[H.length]);let Z=(0,d.useMemo)(()=>(0,O.ru)(r),[r]),B=(0,d.useMemo)(()=>(0,O.XV)(H,Z),[H,Z]),W=(0,d.useCallback)(e=>{P(r=>({...r,[e]:!r[e]}))},[]),$=(0,d.useCallback)((e,r)=>{K(r=>new Set(r).add(e)),k(t=>({...t,[e]:r})),C(r=>({...r,[e]:""}))},[]),ee=()=>{let e=(0,q.WK)(R,E);return C(e),0===Object.keys(e).length},er=(0,g.n)({mutationFn:async e=>{let s=r.map(r=>{let s=e[r.id],i="selectmany"===r.inputType,a=t.answers.find(e=>e.question.id===r.id),l=!(null==a?void 0:a.id);if(i&&Array.isArray(s)){if(s.length>0){let e=[];r.questionOptions&&(e=s.map(e=>{let t=r.questionOptions.find(r=>r.label===e);return null==t?void 0:t.id}).filter(e=>void 0!==e));let t={projectId:n,questionId:r.id,answerType:r.inputType,value:s.join(", "),questionOptionId:e,isOtherOption:!1,formSubmissionId:o};return l?t:{...t,id:a.id}}return null}{let e,t;if(void 0===(e="number"===r.inputType||"decimal"===r.inputType?s?Number(s):void 0:"date"===r.inputType||"dateandtime"===r.inputType?s||void 0:"table"===r.inputType?Array.isArray(s)&&s.length>0?JSON.stringify(s):void 0:s?String(s):void 0))return null;if("selectone"===r.inputType&&s&&r.questionOptions){let e=r.questionOptions.find(e=>e.label===s);t=null==e?void 0:e.id}let i={projectId:n,questionId:r.id,answerType:r.inputType,value:e,questionOptionId:t,isOtherOption:!1,formSubmissionId:o};return l?i:{...i,id:a.id}}}).filter(e=>null!==e);if(0===s.length)throw Error("No valid answers with IDs to submit");let i=s.map(e=>e.id?{id:e.id,questionId:e.questionId,projectId:n,value:e.value,answerType:e.answerType,questionOptionId:e.questionOptionId,isOtherOption:e.isOtherOption||!1,formSubmissionId:e.formSubmissionId}:e.questionId?{questionId:e.questionId,projectId:n,value:e.value,answerType:e.answerType,questionOptionId:e.questionOptionId,isOtherOption:e.isOtherOption||!1,formSubmissionId:e.formSubmissionId}:null).filter(e=>null!==e);try{return await (0,x.GN)(i,n)}catch(t){console.error("Error with /answers/multiple endpoint:",t),t.response&&(console.error("Error response data:",JSON.stringify(t.response.data,null,2)),console.error("Error response status:",t.response.status),console.error("Error response headers:",t.response.headers));let e=[],r=[];for(let t of s)try{if(t.id){let{data:r}=await a.A.patch("/answers/".concat(t.id,"?projectId=").concat(n),{id:t.id,questionId:t.questionId,projectId:n,value:t.value,answerType:t.answerType,questionOptionId:t.questionOptionId,isOtherOption:t.isOtherOption||!1,formSubmissionId:t.formSubmissionId});e.push(r)}else if(t.questionId){let{data:r}=await a.A.post("/answers?projectId=".concat(n),{submissionId:t.formSubmissionId,questionId:t.questionId,value:t.value,answerType:t.answerType,questionOptionId:t.questionOptionId,isOtherOption:t.isOtherOption||!1});e.push(r)}}catch(s){let e=t.id||t.questionId;console.error("Error handling answer ".concat(e,":"),s),s.response&&console.error("Individual error response data:",JSON.stringify(s.response.data,null,2)),r.push(e)}if(r.length>0)throw Error("Failed to update answers with IDs: ".concat(r.join(", ")));if(e.length>0)return S((0,b.Ds)({message:"Submission updated successfully using individual updates. Consider checking the bulk update endpoint.",type:"warning"})),e;throw t}},onSuccess:()=>{S((0,b.Ds)({message:"Submission updated successfully. You can continue editing if needed.",type:"success"})),K(new Set),u()},onError:e=>{var r,t,s,n,i,a;let o=(null==(t=e.response)||null==(r=t.data)?void 0:r.message)||(null==(n=e.response)||null==(s=n.data)?void 0:s.error)||e.message||"Failed to update submission. Please check your input and try again.";S((0,b.Ds)({message:o,type:"error"})),console.error("Update Error:",{message:o,status:null==(i=e.response)?void 0:i.status,data:JSON.stringify(null==(a=e.response)?void 0:a.data,null,2)})},onSettled:()=>{J(!1)}}),et=async e=>{e.preventDefault(),ee()&&(J(!0),er.mutate(E))},es=e=>r.some(r=>{var t;return null==(t=r.questionOptions)?void 0:t.some(r=>r.nextQuestionId===e)}),en=e=>{var r;return(null==(r=e.questionOptions)?void 0:r.some(e=>e.nextQuestionId))||!1},ei=e=>{let r=es(e.id),t=en(e);return(0,s.jsxs)("div",{className:"border rounded-md p-4 ".concat(r?"border-primary-200 dark:border-primary-700 bg-primary-100 dark:bg-primary-900/20":"border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800"),children:[(0,s.jsxs)("div",{className:"mb-2",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsxs)(c.J,{className:"text-base font-medium",children:[e.label,e.isRequired&&(0,s.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),r&&(0,s.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200",children:[(0,s.jsx)(y.A,{className:"w-3 h-3 mr-1"}),"Follow-up"]}),t&&(0,s.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-accent-200 text-accent-700 dark:bg-accent-700/20 dark:text-accent-200",children:"Has conditions"})]}),e.hint&&(0,s.jsx)("p",{className:"text-sm mt-1 ".concat(r?"text-primary-700 dark:text-primary-300":"text-muted-foreground"),children:e.hint}),G[e.id]&&(0,s.jsx)("p",{className:"text-sm text-red-500 mt-1",children:G[e.id]})]}),(0,s.jsx)("div",{className:"mt-2",children:ea(e)})]},e.id)},ea=e=>{var r,t,n,i;let a=null!=(r=E[e.id])?r:"selectmany"===e.inputType?[]:"";switch(e.inputType){case"text":if(null==(t=e.hint)?void 0:t.includes("multiline"))return(0,s.jsx)(p.T,{value:a,onChange:r=>$(e.id,r.target.value),placeholder:e.hint||"Your answer",required:e.isRequired});return(0,s.jsx)("input",{className:"input-field w-full",value:a,onChange:r=>$(e.id,r.target.value),placeholder:e.hint||"Your answer",required:e.isRequired});case"number":return(0,s.jsx)("input",{className:"input-field w-full",type:"number",value:a,onChange:r=>$(e.id,r.target.value),placeholder:e.hint||"Your answer",required:e.isRequired});case"decimal":return(0,s.jsx)("input",{className:"input-field w-full",type:"number",step:"any",value:a,onChange:r=>$(e.id,r.target.value),placeholder:e.hint||"Your answer",required:e.isRequired});case"selectone":return(0,s.jsx)(h.z,{value:a,onValueChange:r=>$(e.id,r),required:e.isRequired,children:(0,s.jsx)("div",{className:"space-y-2",children:null==(n=e.questionOptions)?void 0:n.map((e,r)=>(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(h.C,{value:e.label,id:"option-".concat(e.id)}),(0,s.jsx)(c.J,{htmlFor:"option-".concat(e.id),className:"cursor-pointer",children:e.label})]},r))})});case"selectmany":return(0,s.jsx)("div",{className:"space-y-2",children:null==(i=e.questionOptions)?void 0:i.map(r=>(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(m.S,{id:"option-".concat(r.id),checked:(a||[]).includes(r.label),onCheckedChange:t=>{let s=a||[],n=t?[...s,r.label]:s.filter(e=>e!==r.label);$(e.id,n)}}),(0,s.jsx)(c.J,{htmlFor:"option-".concat(r.id),className:"cursor-pointer",children:r.label})]},r.id))});case"date":return(0,s.jsx)("div",{className:"relative",children:(0,s.jsx)("input",{className:"input-field w-full",type:"date",value:a,onChange:r=>$(e.id,r.target.value),placeholder:e.hint||"Select date",required:e.isRequired})});case"dateandtime":return(0,s.jsx)("div",{className:"relative",children:(0,s.jsx)("input",{className:"input-field w-full",type:"time",value:a,onChange:r=>$(e.id,r.target.value),placeholder:e.hint||"Select time",required:e.isRequired})});case"table":return(0,s.jsx)(v.N,{questionId:e.id,value:a,onChange:r=>$(e.id,r),required:e.isRequired,tableLabel:e.label});default:return null}};return(0,s.jsxs)("div",{className:"w-full max-w-screen-lg bg-neutral-100 dark:bg-gray-800 rounded-md shadow-sm border border-gray-200 dark:border-gray-700",children:[(0,s.jsxs)("h2",{className:"text-2xl font-semibold p-6 border-b border-gray-200 dark:border-gray-700",children:["Edit Submission",(null==X?void 0:X.name)?" for ".concat(X.name):""]}),(0,s.jsx)("form",{onSubmit:et,className:"p-6",children:(0,s.jsxs)("div",{className:"space-y-6",children:[0===r.length?(0,s.jsx)("div",{className:"text-center py-12",children:(0,s.jsx)("p",{className:"text-muted-foreground",children:"This form has no questions yet."})}):B.map(e=>{if("group"===e.type){let r=e.data,t=M[r.id];return(0,s.jsx)(N.A,{group:r,nestingLevel:0,visibleQuestions:R,nestedQuestions:Q,renderQuestionInput:ea,errors:G,onToggleExpansion:W,isExpanded:t,expandedGroups:M,className:""},"group-".concat(r.id))}{let r=e.data;if(!R.some(e=>e.id===r.id))return null;let t=Q.find(e=>e.question.id===r.id);return t?(0,s.jsx)(I.A,{questionGroup:t,renderQuestionInput:ea,errors:G,className:""},r.id):ei(r)}}),r.length>0&&(0,s.jsxs)("div",{className:"mt-6 flex justify-end gap-4",children:[(0,s.jsx)("button",{className:"btn-primary bg-neutral-500 hover:bg-neutral-600",type:"button",onClick:l,disabled:F,children:"Cancel"}),(0,s.jsx)("button",{className:"btn-primary",type:"submit",disabled:F,children:F?"Saving...":"Save Changes"})]})]})})]})}let E=async(e,r)=>{let{data:t}=await a.A.get("/form-submissions/".concat(e)),s=t.data.formSubmissions.find(e=>e.id===r);if(!s)throw Error("Submission not found");return s};function k(){let{hashedId:e,submissionId:r}=(0,n.useParams)(),t=(0,o.D)(e),a=Number(r);if(null===t||isNaN(a))return(0,s.jsx)("div",{children:"Error: Invalid project or submission ID."});let{data:d=[],isLoading:c,isError:p}=(0,i.I)({queryKey:["questions",t],queryFn:()=>(0,l.K4)({projectId:t}),enabled:!!t}),{data:m,isLoading:h,isError:y,refetch:g}=(0,i.I)({queryKey:["submission",t,a],queryFn:()=>E(t,a),enabled:!!t&&!!a});return c||h?(0,s.jsx)(u.A,{}):p||y||!d||!m?(0,s.jsx)("p",{className:"text-sm text-red-500",children:"Error loading submission or form. Please try again."}):(0,s.jsx)("div",{className:"min-h-screen w-full bg-neutral-100 dark:bg-gray-900 flex flex-col items-center p-4 md:p-6",children:(0,s.jsx)(S,{questions:d,submission:m,projectId:t,submissionId:a,onSave:()=>{window.opener&&window.opener.postMessage({type:"REFETCH_SUBMISSIONS"},"*"),g()}})})}},77361:(e,r,t)=>{"use strict";t.d(r,{D_:()=>c,Im:()=>u,Oo:()=>p,c3:()=>i,kf:()=>n,lj:()=>h,or:()=>l,pf:()=>d,vj:()=>a,wI:()=>m,xx:()=>o});var s=t(25784);let n=async e=>{let{projectId:r}=e,{data:t}=await s.A.get("/projects/".concat(r));return t.project},i=async e=>{let{data:r}=await s.A.post("/projects/from-template",e);return r},a=async()=>{try{let{data:e}=await s.A.get("/projects");return e.projects}catch(e){throw console.error("Error fetching projects:",e),e}},o=async e=>{let{data:r}=await s.A.delete("/projects/delete/".concat(e));return r},l=async e=>{try{let{data:r}=await s.A.delete("/projects/delete-multiple",{data:{projectIds:e}});return r}catch(e){throw console.error("Error deleting multiple projects:",e),e}},u=async e=>{try{let{data:r}=await s.A.patch("/projects/change-status/".concat(e),{status:"archived"});return r}catch(e){throw console.error("Error archiving project:",e),e}},d=async function(e){arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{let{data:r}=await s.A.patch("/projects/change-status/".concat(e),{status:"deployed"});return r}catch(e){throw console.error("Error deploying project:",e),e}},c=async e=>{try{let{data:r}=await s.A.patch("/projects/update-many-status",{projectIds:e,status:"archived"});return r}catch(e){throw console.error("Error archiving multiple projects:",e),e}},p=async e=>{try{let{data:r}=await s.A.post("/users/check-email",{email:e});return r}catch(e){var r,t,n,i,a,o;throw Error("object"==typeof(null==(t=e.response)||null==(r=t.data)?void 0:r.message)?JSON.stringify(null==(i=e.response)||null==(n=i.data)?void 0:n.message):(null==(o=e.response)||null==(a=o.data)?void 0:a.message)||e.message||"Failed to check user")}},m=async e=>{let{projectId:r,email:t,permissions:n}=e;try{let e=await p(t);if(!e||!e.success)throw Error((null==e?void 0:e.message)||"User not found");let{data:i}=await s.A.post("/project-users",{userId:e.user.id,projectId:r,permission:n});return i}catch(e){var i,a,o,l,u,d;throw console.error("Error adding user to project:",e),Error("object"==typeof(null==(a=e.response)||null==(i=a.data)?void 0:i.message)?JSON.stringify(null==(l=e.response)||null==(o=l.data)?void 0:o.message):(null==(d=e.response)||null==(u=d.data)?void 0:u.message)||e.message||"Failed to add user")}},h=async e=>{try{let{data:r}=await s.A.post("/answers/multiple",e);return r}catch(e){throw console.error("Error creating answer submission:",e),e}}},80423:(e,r,t)=>{"use strict";t.d(r,{A:()=>u});var s=t(95155),n=t(12115),i=t(66474),a=t(13052),o=t(13388);let l=e=>{let{group:r,nestingLevel:t=0,visibleQuestions:u,nestedQuestions:d,renderQuestionInput:c,errors:p,onToggleExpansion:m,isExpanded:h,expandedGroups:y,className:g=""}=e,[f,b]=(0,n.useState)(!0),v=void 0!==h?h:f,x=r.question||[],w=x.filter(e=>u.some(r=>r.id===e.id)),j=(r.subGroups||[]).filter(e=>(e.question||[]).some(e=>u.some(r=>r.id===e.id)));return 0===w.length&&0===j.length?null:(0,s.jsxs)("div",{className:"border border-neutral-400 rounded-md bg-card shadow-sm mb-4 ".concat(t>0?"ml-8 border-l-4 border-l-primary-300":""," ").concat(g),children:[(0,s.jsx)("div",{className:"flex items-center justify-between p-4 bg-neutral-100 border-b border-neutral-300 rounded-t-md cursor-pointer hover:bg-neutral-200 dark:bg-gray-700 dark:border-gray-600 dark:hover:bg-gray-600",onClick:()=>{m?m(r.id):b(!f)},children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[v?(0,s.jsx)(i.A,{className:"h-5 w-5 text-neutral-700 dark:text-neutral-300"}):(0,s.jsx)(a.A,{className:"h-5 w-5 text-neutral-700 dark:text-neutral-300"}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-neutral-900 dark:text-neutral-100",children:r.title}),(0,s.jsxs)("span",{className:"text-sm text-neutral-700 dark:text-neutral-400",children:["(",w.length+j.reduce((e,r)=>{var t;return e+((null==(t=r.question)?void 0:t.length)||0)},0)," visible question",w.length+j.reduce((e,r)=>{var t;return e+((null==(t=r.question)?void 0:t.length)||0)},0)!==1?"s":"",")"]})]})}),v&&(0,s.jsxs)("div",{className:"p-4 space-y-4",children:[j.sort((e,r)=>e.order-r.order).map(e=>{let r=y?y[e.id]:void 0;return(0,s.jsx)(l,{group:e,nestingLevel:t+1,visibleQuestions:u,nestedQuestions:d,renderQuestionInput:c,errors:p,onToggleExpansion:m,isExpanded:r,expandedGroups:y,className:g},e.id)}),d.filter(e=>x.some(r=>r.id===e.question.id)).map(e=>(0,s.jsx)(o.A,{questionGroup:e,renderQuestionInput:c,errors:p,className:""},e.question.id))]})]})},u=l},88570:(e,r,t)=>{"use strict";t.d(r,{D:()=>o,l:()=>a});var s=t(41050);let n=t(49509).env.SALT||"rushan-salt",i=new s.A(n,12),a=e=>i.encode(e),o=e=>{let r=i.decode(e)[0];return"bigint"==typeof r?r<Number.MAX_SAFE_INTEGER?Number(r):null:"number"==typeof r?r:null}}},e=>{var r=r=>e(e.s=r);e.O(0,[635,1111,6967,9373,4277,556,3481,7823,7248,8441,1684,7358],()=>r(37818)),_N_E=e.O()}]);