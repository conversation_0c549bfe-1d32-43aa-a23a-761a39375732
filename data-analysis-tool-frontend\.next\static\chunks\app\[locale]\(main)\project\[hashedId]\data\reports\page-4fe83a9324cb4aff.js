(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7042],{25784:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let s=a(23464).A.create({baseURL:"http://localhost:4000/api",headers:{"Content-Type":"application/json"},withCredentials:!0});s.interceptors.request.use(e=>e,e=>Promise.reject(e)),s.interceptors.response.use(e=>e,e=>("ERR_NETWORK"===e.code&&console.error("Network error - Please check if the backend server is running"),Promise.reject(e)));let r=s},53999:(e,t,a)=>{"use strict";a.d(t,{Y:()=>l,cn:()=>n});var s=a(52596),r=a(39688);function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,r.QP)((0,s.$)(t))}function l(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"short";if(!e)return"";try{let a="string"==typeof e?new Date(e):e;if(isNaN(a.getTime()))return"";switch(t){case"short":return a.toLocaleDateString(void 0,{year:"numeric",month:"short",day:"numeric"});case"long":return a.toLocaleDateString(void 0,{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"});case"full":return a.toLocaleDateString(void 0,{year:"numeric",month:"long",day:"numeric",weekday:"long",hour:"2-digit",minute:"2-digit",second:"2-digit"});default:return a.toLocaleDateString()}}catch(t){return console.error("Error formatting date:",t),String(e)}}},65280:(e,t,a)=>{Promise.resolve().then(a.bind(a,95779))},95779:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>et});var s=a(95155),r=a(12115),n=a(50741),l=a(84355),i=a(51154);function o(e){let{className:t="",variant:a="default",...r}=e;return(0,s.jsx)("div",{className:"inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-semibold ".concat({default:"bg-blue-500 text-neutral-100",secondary:"bg-gray-500 text-neutral-100",destructive:"bg-red-500 text-neutral-100",outline:"bg-transparent text-gray-700 border border-gray-300"}[a]," ").concat(t),...r})}function c(e){let{className:t="",...a}=e;return(0,s.jsx)("div",{className:"rounded-lg border border-gray-200 bg-neutral-100 shadow-sm ".concat(t),...a})}function d(e){let{className:t="",...a}=e;return(0,s.jsx)("div",{className:"flex flex-col space-y-1.5 p-6 ".concat(t),...a})}function u(e){let{className:t="",...a}=e;return(0,s.jsx)("h3",{className:"text-2xl font-semibold leading-none tracking-tight ".concat(t),...a})}function m(e){let{className:t="",...a}=e;return(0,s.jsx)("div",{className:"p-6 pt-0 ".concat(t),...a})}let x=(0,r.createContext)(void 0);function h(){let e=(0,r.useContext)(x);if(!e)throw Error("Tabs components must be used within a Tabs component");return e}function p(e){let{defaultValue:t,children:a,className:n=""}=e,[l,i]=(0,r.useState)(t);return(0,s.jsx)(x.Provider,{value:{activeTab:l,setActiveTab:i},children:(0,s.jsx)("div",{className:n,children:a})})}function b(e){let{children:t,className:a=""}=e;return(0,s.jsx)("div",{className:"inline-flex h-10 items-center justify-center rounded-md bg-gray-100 p-1 ".concat(a),children:t})}function f(e){let{value:t,children:a,className:r=""}=e,{activeTab:n,setActiveTab:l}=h(),i=n===t;return(0,s.jsx)("button",{className:"inline-flex items-center justify-center px-3 py-1.5 text-sm font-medium\n        ".concat(i?"bg-neutral-100 text-black shadow-sm":"text-gray-600","\n        ").concat(r),onClick:()=>l(t),children:a})}function g(e){let{value:t,children:a,className:r=""}=e,{activeTab:n}=h();return n!==t?null:(0,s.jsx)("div",{className:"mt-2 ".concat(r),children:a})}var j=a(69074),v=a(63008),y=a(53999),N=a(97168),w=a(42355),k=a(13052),S=a(84949),A=a(97972);function D(){let{previousMonth:e,nextMonth:t,goToMonth:a}=(0,S.cq)();return(0,s.jsxs)("div",{className:"space-x-1 flex items-center",children:[(0,s.jsx)("button",{type:"button",onClick:()=>e&&a(e),className:"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100",children:(0,s.jsx)(w.A,{className:"h-4 w-4"})}),(0,s.jsx)("button",{type:"button",onClick:()=>t&&a(t),className:"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100",children:(0,s.jsx)(k.A,{className:"h-4 w-4"})})]})}function R(e){let{className:t,classNames:a,showOutsideDays:r=!0,...n}=e;return(0,s.jsx)(A.h,{showOutsideDays:r,className:(0,y.cn)("p-4 bg-neutral-100 rounded-2xl shadow-xl max-w-md mx-auto border border-gray-100",t),components:{Nav:D},...n})}a(39303),R.displayName="Calendar";var C=a(49956);let L=C.bL,z=C.l9,E=r.forwardRef((e,t)=>{let{className:a,align:r="center",sideOffset:n=4,...l}=e;return(0,s.jsx)(C.ZL,{children:(0,s.jsx)(C.UC,{ref:t,align:r,sideOffset:n,className:(0,y.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...l})})});E.displayName=C.UC.displayName;var F=a(17652);function P(e){let{value:t,onChange:a,className:r}=e,n=(0,F.c3)();return(0,s.jsx)("div",{className:(0,y.cn)("grid gap-2",r),children:(0,s.jsxs)(L,{children:[(0,s.jsx)(z,{asChild:!0,children:(0,s.jsxs)(N.$,{id:"date",variant:"outline",className:(0,y.cn)("w-[300px] justify-start text-left font-normal",!t&&"text-muted-foreground"),children:[(0,s.jsx)(j.A,{className:"mr-2 h-4 w-4"}),(null==t?void 0:t.from)?t.to?(0,s.jsxs)(s.Fragment,{children:[(0,v.GP)(t.from,"LLL dd, y")," -"," ",(0,v.GP)(t.to,"LLL dd, y")]}):(0,v.GP)(t.from,"LLL dd, y"):(0,s.jsx)("span",{children:n("pickDateRange")})]})}),(0,s.jsx)(E,{className:"w-auto p-0",align:"start",children:(0,s.jsx)(R,{initialFocus:!0,mode:"range",defaultMonth:null==t?void 0:t.from,selected:t,onSelect:a,numberOfMonths:2})})]})})}var B=a(35695),_=a(25784),I=a(83540),O=a(8782),T=a(34e3),U=a(54811),K=a(94517),M=a(24026),q=a(3401),V=a(94754),G=a(96025),Q=a(16238),W=a(83394),Y=a(56811);let $=e=>{let{children:t,content:a,side:r="top",align:n="center",...l}=e;return(0,s.jsx)(Y.Kq,{children:(0,s.jsxs)(Y.bL,{delayDuration:0,children:[(0,s.jsx)(Y.l9,{asChild:!0,children:t}),(0,s.jsx)(Y.ZL,{children:(0,s.jsxs)(Y.UC,{side:r,align:n,className:(0,y.cn)("z-50 rounded bg-black px-2 py-1 text-xs text-neutral-100 shadow-md animate-fade-in-up","data-[state=delayed-open]:data-[side=top]:animate-slide-in-from-bottom-2"),children:[a,(0,s.jsx)(Y.i3,{className:"fill-black"})]})})]})})},Z=["#0088FE","#00C49F","#FFBB28","#FF8042","#8884D8","#82CA9D","#FFC658","#FF6B6B"],X=[{value:"verticalBar",label:"Vertical Bar",icon:n.A},{value:"pie",label:"Pie",icon:l.A},{value:"donut",label:"Donut",icon:l.A}],H=e=>{let{data:t,donut:a=!1}=e,r=(0,F.c3)(),n=t.labels.map((e,a)=>({name:e,value:t.values[a]}));return(0,s.jsx)("div",{className:"h-[400px] w-full",children:(0,s.jsx)(I.u,{children:(0,s.jsxs)(O.r,{children:[(0,s.jsx)(T.F,{data:n,dataKey:"value",nameKey:"name",cx:"50%",cy:"50%",outerRadius:150,innerRadius:80*!!a,fill:"#8884d8",label:e=>{let{name:t,percent:a}=e;return"".concat(t," (").concat((100*a).toFixed(0),"%)")},children:n.map((e,t)=>(0,s.jsx)(U.f,{fill:Z[t%Z.length]},"cell-".concat(t)))}),(0,s.jsx)(K.m,{formatter:e=>["".concat(e," responses"),"".concat(r("count"))]}),(0,s.jsx)(M.s,{})]})})})},J=e=>{let{data:t,layout:a="verticalBar",questionType:r}=e,n=t.labels.map((e,a)=>({name:e,value:t.values[a]})),l="table"===r?"horizontalBar":a,i="table"===r&&n.length>10?n.slice(0,10):n;return(0,s.jsx)("div",{className:"h-[400px] w-full",children:(0,s.jsx)(I.u,{children:(0,s.jsxs)(q.E,{data:i,layout:"horizontalBar"===l?"vertical":"horizontal",margin:{top:20,right:30,left:20,bottom:5},children:[(0,s.jsx)(V.d,{strokeDasharray:"3 3"}),(0,s.jsx)(G.W,{dataKey:"name",type:"horizontalBar"===l?"number":"category",tick:{fontSize:12}}),(0,s.jsx)(Q.h,{type:"horizontalBar"===l?"category":"number",tick:{fontSize:12},width:"table"===r?150:60}),(0,s.jsx)(K.m,{formatter:e=>["".concat(e," responses"),"Count"]}),(0,s.jsx)(M.s,{}),(0,s.jsx)(W.y,{dataKey:"value",fill:"#8884d8",children:i.map((e,t)=>(0,s.jsx)(U.f,{fill:Z[t%Z.length]},"cell-".concat(t)))})]})})})},ee=e=>{let{report:t}=e,{question:a,type:n,answered:l,total:i,table:x,chartData:h,stats:j}=t,v=i>0?Math.round(l/i*100):0,[y,N]=(0,r.useState)("table"===n?"verticalBar":"pie"),w=(0,F.c3)(),k="selectone"===n||"selectmany"===n,S=()=>{if("table"===n&&!Array.isArray(x)&&x.structure){let{structure:e,data:t,cellValues:r,metadata:n}=x,l=e.columns.filter(e=>null===e.parentId),i=new Map;return r&&("object"!=typeof r||Array.isArray(r)?r instanceof Map&&(i=r):Object.entries(r).forEach(e=>{let[t,a]=e;i.set(t,a)})),t&&t.length>0&&t.forEach(e=>{if(e.rowId&&e.columnId){let t="".concat(e.rowId,"_").concat(e.columnId);e.cellValue&&i.set(t,e.cellValue)}}),0===i.size&&e.rows.forEach(t=>{e.columns.forEach(e=>{if(null===e.parentId){let a="".concat(t.id,"_").concat(e.id);i.set(a,"Test value for ".concat(t.name," - ").concat(e.name))}})}),(0,s.jsxs)("div",{className:"overflow-auto",children:[(0,s.jsxs)("div",{className:"mb-2 p-2 bg-blue-50 rounded-md",children:[(0,s.jsxs)("p",{className:"text-sm text-blue-700",children:[(0,s.jsxs)("span",{className:"font-medium",children:[w("matrixQuestion"),":"]})," ",w("matrixInfo")]}),n&&(0,s.jsxs)("div",{className:"mt-1 text-xs text-blue-600 flex items-center justify-between",children:[(0,s.jsx)("span",{children:n.hasRealData?"Showing data from ".concat(n.submissionsWithData," of ").concat(n.totalSubmissions," submissions"):w("noSubmissionsForTable")}),(0,s.jsxs)("span",{children:[w("lastUpdated"),":",new Date(n.lastUpdated).toLocaleString()]})]})]}),(0,s.jsxs)("table",{className:"w-full my-4 text-sm border-collapse shadow-sm",children:[(0,s.jsx)("thead",{children:(0,s.jsxs)("tr",{className:"border-b bg-gray-100",children:[(0,s.jsx)("th",{className:"pb-3 pt-3 text-left border p-2 bg-gray-50 font-semibold text-gray-700",children:a}),l.map(e=>(0,s.jsx)("th",{className:"pb-3 pt-3 text-center border p-2 bg-gray-50 font-semibold text-gray-700",children:e.name},e.id))]})}),(0,s.jsx)("tbody",{children:e.rows.map((e,t)=>(0,s.jsxs)("tr",{className:"border-b last:border-0 ".concat(t%2==0?"bg-white":"bg-gray-50"," hover:bg-blue-50 transition-colors duration-150"),children:[(0,s.jsx)("td",{className:"py-3 border p-3 font-medium text-gray-700",children:e.name}),l.map(t=>{if(t.children&&t.children.length>0)return(0,s.jsx)("td",{className:"py-3 border p-3 text-center",children:t.children.map(t=>{let a="".concat(e.id,"_").concat(t.id),n=r instanceof Map?r.get(a):"object"!=typeof r||Array.isArray(r)?i.get(a):r[a];return(0,s.jsxs)("div",{className:"mb-2 p-1 rounded hover:bg-blue-100",children:[(0,s.jsx)("span",{className:"font-medium text-xs text-gray-600 block mb-1",children:t.name}),(0,s.jsx)("span",{className:"text-gray-800",children:void 0!==n&&""!==n?n.includes(",")||n.includes("(")?(0,s.jsx)("span",{className:"text-xs",children:n.split(", ").map((e,t)=>(0,s.jsx)("span",{className:"inline-block mr-1 mb-1 px-1 py-0.5 rounded ".concat(e.includes("(")?"bg-blue-100 text-blue-800":"bg-gray-100 text-gray-700"),children:e},t))}):n:"-"})]},t.id)})},t.id);{let a="".concat(e.id,"_").concat(t.id),n=r instanceof Map?r.get(a):"object"!=typeof r||Array.isArray(r)?i.get(a):r[a];return(0,s.jsx)("td",{className:"py-3 border p-3 text-center ".concat(n&&"-"!==n?"text-gray-800 font-medium":"text-gray-400"),children:void 0!==n&&""!==n?n.includes(",")||n.includes("(")?(0,s.jsx)("span",{className:"text-xs",children:n.split(", ").map((e,t)=>(0,s.jsx)("span",{className:"inline-block mr-1 mb-1 px-1 py-0.5 rounded ".concat(e.includes("(")?"bg-blue-100 text-blue-800":"bg-gray-100 text-gray-700"),children:e},t))}):n:"-"},t.id)}})]},e.id))})]})]})}return(0,s.jsx)("div",{className:"overflow-auto",children:(0,s.jsxs)("table",{className:"w-full my-4 text-sm",children:[(0,s.jsx)("thead",{children:(0,s.jsxs)("tr",{className:"border-b",children:[(0,s.jsx)("th",{className:"pb-2 text-left",children:w("value")}),(0,s.jsx)("th",{className:"pb-2 text-right",children:w("count")}),(0,s.jsx)("th",{className:"pb-2 text-right",children:w("percentage")})]})}),(0,s.jsx)("tbody",{children:Array.isArray(x)&&x.map((e,t)=>(0,s.jsxs)("tr",{className:"border-b last:border-0",children:[(0,s.jsx)("td",{className:"py-2",children:e.value||w("noAnswer")}),(0,s.jsx)("td",{className:"py-2 text-right",children:e.frequency}),(0,s.jsxs)("td",{className:"py-2 text-right",children:[e.percentage,"%"]})]},t))})]})})};return(0,s.jsxs)(c,{className:"mb-6",children:[(0,s.jsx)(d,{children:(0,s.jsxs)("div",{className:"flex flex-col space-y-2 md:space-y-0 md:flex-row md:justify-between md:items-center",children:[(0,s.jsx)(u,{className:"text-base",children:a}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsxs)(o,{variant:v>75?"default":v>50?"secondary":"outline",children:[l," of ",i," responses (",v,"%)"]}),(0,s.jsx)(o,{variant:"outline",children:n})]})]})}),(0,s.jsx)(m,{children:k?(0,s.jsxs)(p,{defaultValue:"table",children:[(0,s.jsxs)(b,{className:"grid w-full grid-cols-2",children:[(0,s.jsx)(f,{value:"table",children:w("table")}),(0,s.jsx)(f,{value:"chart",children:w("chart")})]}),(0,s.jsx)(g,{value:"table",children:S()}),(0,s.jsxs)(g,{value:"chart",children:[(0,s.jsx)("div",{className:"flex items-center space-x-2 mb-4",children:X.map(e=>{let t=e.icon,a="verticalBar"===e.value?{transform:"rotate(90deg)"}:{};return(0,s.jsx)($,{content:e.label,side:"top",children:(0,s.jsx)("button",{onClick:()=>N(e.value),className:"p-2 rounded-full border transition-colors duration-150 ".concat(y===e.value?"bg-primary text-neutral-100 border-primary":"bg-muted text-muted-foreground border-transparent"),style:a,children:(0,s.jsx)(t,{size:20})})},e.value)})}),(()=>{switch(y){case"verticalBar":return(0,s.jsx)(J,{data:h,layout:"verticalBar",questionType:n});case"pie":default:return(0,s.jsx)(H,{data:h});case"donut":return(0,s.jsx)(H,{data:h,donut:!0})}})()]})]}):(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-sm font-medium mb-4",children:w("responseData")}),S()]})})]})};function et(){let e=(0,F.c3)(),[t,a]=(0,r.useState)(!0),[n,l]=(0,r.useState)(null),[o,x]=(0,r.useState)(null),[h,p]=(0,r.useState)(),b=(0,B.usePathname)(),f=async(e,t)=>{var s,r,n,i,o,c;try{a(!0),l(null);let r=b.split("/"),n=r[r.indexOf("project")+1];if(!n)throw Error("Project ID not found in URL");let i=new URLSearchParams;e&&i.append("startDate",e.toISOString()),t&&i.append("endDate",t.toISOString());let o=await _.A.get("/projects/".concat(n,"/report?").concat(i.toString()));if(null==(s=o.data)?void 0:s.data)x(o.data.data);else throw Error("Invalid response format from server")}catch(e){console.error("Error fetching report data:",e),"ERR_NETWORK"===e.code?l("Unable to connect to the server. Please make sure the backend server is running."):(null==(r=e.response)?void 0:r.status)===401?l("You are not authorized to view this report. Please log in again."):(null==(n=e.response)?void 0:n.status)===403?l("You don't have permission to view this report."):(null==(i=e.response)?void 0:i.status)===404?l("Report not found. The project may have been deleted or you don't have access."):l((null==(c=e.response)||null==(o=c.data)?void 0:o.message)||e.message||"Failed to load report data. Please try again later.")}finally{a(!1)}};return((0,r.useEffect)(()=>{f()},[b]),t)?(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center h-[calc(100vh-200px)]",children:[(0,s.jsx)(i.A,{className:"w-10 h-10 animate-spin text-primary"}),(0,s.jsx)("p",{className:"mt-4 text-lg",children:e("loadingReportData")})]}):n||!o?(0,s.jsx)("div",{className:"p-4 md:p-8",children:(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold",children:e("dataReport")}),(0,s.jsxs)("div",{className:"p-8 mt-4 text-center border rounded-md border-gray-200 bg-gray-50",children:[(0,s.jsx)("p",{className:"text-lg text-red-500",children:n||e("noDataAvailable")}),(0,s.jsx)("p",{className:"mt-2 text-gray-600",children:!n&&e("submitToGenerateReport")})]})]})}):(0,s.jsxs)("div",{className:"p-4 md:p-8",children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsxs)("div",{className:"flex flex-col space-y-4 md:space-y-0 md:flex-row md:justify-between md:items-center",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold",children:e("dataReport")}),(0,s.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:o.metadata.projectName})]}),(0,s.jsx)(P,{value:h,onChange:e=>{p(e),(null==e?void 0:e.from)&&(null==e?void 0:e.to)&&f(e.from,e.to)}})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 gap-4 mt-6 md:grid-cols-3",children:[(0,s.jsxs)(c,{children:[(0,s.jsx)(d,{children:(0,s.jsx)(u,{className:"text-sm font-medium",children:e("totalSubmissions")})}),(0,s.jsx)(m,{children:(0,s.jsx)("p",{className:"text-2xl font-bold",children:o.summary.totalSubmissions})})]}),(0,s.jsxs)(c,{children:[(0,s.jsx)(d,{children:(0,s.jsx)(u,{className:"text-sm font-medium",children:e("totalQuestions")})}),(0,s.jsx)(m,{children:(0,s.jsx)("p",{className:"text-2xl font-bold",children:o.summary.totalQuestions})})]}),(0,s.jsxs)(c,{children:[(0,s.jsx)(d,{children:(0,s.jsx)(u,{className:"text-sm font-medium",children:e("responseRate")})}),(0,s.jsx)(m,{children:(0,s.jsxs)("p",{className:"text-2xl font-bold",children:[o.summary.averageResponseRate,"%"]})})]})]})]}),(0,s.jsx)("div",{children:o.data.map((e,t)=>(0,s.jsx)(ee,{report:e},t))})]})}},97168:(e,t,a)=>{"use strict";a.d(t,{$:()=>o});var s=a(95155);a(12115);var r=a(99708),n=a(74466),l=a(53999);let i=(0,n.F)("inline-flex items-center justify-center gap-2 neutral-100space-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-neutral-100 shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:a,size:n,asChild:o=!1,...c}=e,d=o?r.DX:"button";return(0,s.jsx)(d,{"data-slot":"button",className:(0,l.cn)(i({variant:a,size:n,className:t})),...c})}}},e=>{var t=t=>e(e.s=t);e.O(0,[8638,6453,1111,4277,556,2854,7836,4916,8441,1684,7358],()=>t(65280)),_N_E=e.O()}]);