"use strict";exports.id=4072,exports.ids=[4072],exports.modules={96:(e,r,t)=>{t.d(r,{BU:()=>i,IF:()=>o,Vq:()=>c,_U:()=>d,kO:()=>u,lr:()=>n,pr:()=>s,yb:()=>l});var a=t(12810);let s=async({projectId:e})=>{try{let{data:r}=await a.A.get(`/projects/form/${e}`);return r.data?.project?.questionGroup||[]}catch(r){console.error("Error fetching question groups from project endpoint:",r);try{let{data:r}=await a.A.post("/question-groups",{projectId:e});return r.data?.projectGroup||[]}catch(e){return console.error("Error in fallback fetch:",e),[]}}},o=async({title:e,order:r,projectId:t,selectedQuestionIds:s,parentGroupId:o})=>{try{let{data:n}=await a.A.post("/question-groups",{title:e,order:r,projectId:t,selectedQuestionIds:s||[],parentGroupId:o});return n}catch(e){throw console.error("Error creating question group:",e),e}},n=async({id:e,title:r,order:t,selectedQuestionIds:s})=>{try{let{data:o}=await a.A.patch("/question-groups",{id:e,title:r,order:t,selectedQuestionIds:s});return o}catch(e){throw console.error("Error updating question group:",e),e}},i=async({id:e})=>{try{let{data:r}=await a.A.delete(`/question-groups/${e}`);return r}catch(e){throw console.error("Error deleting question group:",e),e}},l=async({id:e})=>{try{let{data:r}=await a.A.delete(`/question-groups/group/question/${e}`);return r}catch(e){throw console.error("Error deleting question group and questions:",e),e}},d=async({groupId:e,newGroupId:r,questionId:t})=>{let{data:s}=await a.A.patch("/question-groups/question/move",{groupId:e,newGroupId:r,questionId:t});return s},u=async({childGroupId:e,parentGroupId:r})=>{let{data:t}=await a.A.patch("/question-groups/group/add",{childGroupId:e,ParentGroupId:r});return t},c=async({projectId:e,groupPositions:r})=>{let{data:t}=await a.A.patch("/question-groups/positions",{projectId:e,groupPositions:r});return t}},13784:(e,r,t)=>{t.d(r,{N:()=>l});var a=t(60687),s=t(43210),o=t.n(s),n=t(96752),i=t(68988);function l({questionId:e,value:r,onChange:t,required:l=!1,tableLabel:d}){let[u,c]=(0,s.useState)([]),[p,m]=(0,s.useState)([]),[h,f]=(0,s.useState)({}),[g,w]=(0,s.useState)(!0),[b,x]=(0,s.useState)(null),[y,q]=(0,s.useState)({}),j=o().useMemo(()=>{if(0===u.length)return{parentColumns:[],columnMap:new Map,hasChildColumns:!1};let e=u.filter(e=>void 0===e.parentColumnId||null===e.parentColumnId),r=new Map;e.forEach(e=>{let t=u.filter(r=>r.parentColumnId===e.id);r.set(e.id,t)});let t=e.some(e=>(r.get(e.id)||[]).length>0);return{parentColumns:e,columnMap:r,hasChildColumns:t}},[u]),N=(e,r,a)=>{let s=`${e}_${r}`;f(e=>({...e,[s]:a})),setTimeout(()=>{let e={...h,[s]:a},r=[];Object.entries(e).forEach(([e,t])=>{if(""!==t.trim()){let[a,s]=e.split("_").map(Number);r.push({columnId:a,rowsId:s,value:t})}}),t(r)},0)},v=0===u.length;return(0,a.jsx)("div",{className:"overflow-x-auto",children:g?(0,a.jsx)("div",{className:"py-4 text-center",children:"Loading table..."}):b?(0,a.jsx)("div",{className:"py-4 text-center text-red-500",children:b}):v?(0,a.jsx)("div",{className:"py-4 text-center text-amber-600",children:"This table has no columns defined. Please configure the table question first."}):(0,a.jsxs)(n.XI,{className:"border-collapse",children:[(0,a.jsxs)(n.A0,{children:[(0,a.jsx)(n.Hj,{children:j.parentColumns.map(e=>{let r=(j.columnMap.get(e.id)||[]).length||1;return(0,a.jsx)(n.nd,{colSpan:r,className:"text-center border bg-blue-50 font-medium",children:e.columnName},e.id)})}),j.hasChildColumns&&(0,a.jsx)(n.Hj,{children:j.parentColumns.map(e=>{let r=j.columnMap.get(e.id)||[];return 0===r.length?(0,a.jsx)(n.nd,{className:"border bg-blue-50/50 text-sm"},`empty-${e.id}`):r.map(e=>(0,a.jsx)(n.nd,{className:"border bg-blue-50/50 text-sm",children:e.columnName},e.id))})})]}),(0,a.jsx)(n.BF,{children:p.length>0?p.map((e,r)=>(0,a.jsx)(n.Hj,{className:r%2==0?"bg-white":"bg-gray-50",children:j.parentColumns.map(r=>{let t=j.columnMap.get(r.id)||[];return 0===t.length?(0,a.jsx)(n.nA,{className:"border p-1",children:(0,a.jsx)(i.p,{value:h[`${r.id}_${e.id}`]||"",onChange:t=>N(r.id,e.id,t.target.value),className:"w-full",required:l,placeholder:"Enter value"})},`cell-${r.id}-${e.id}`):t.map(r=>(0,a.jsx)(n.nA,{className:"border p-1",children:(0,a.jsx)(i.p,{value:h[`${r.id}_${e.id}`]||"",onChange:t=>N(r.id,e.id,t.target.value),className:"w-full",required:l,placeholder:"Enter value"})},`cell-${r.id}-${e.id}`))})},e.id)):(0,a.jsx)(n.Hj,{children:j.parentColumns.map(e=>{let r=j.columnMap.get(e.id)||[];return 0===r.length?(0,a.jsx)(n.nA,{className:"border p-1",children:(0,a.jsx)(i.p,{value:h[`${e.id}_no_row`]||"",onChange:r=>N(e.id,"no_row",r.target.value),className:"w-full",required:l,placeholder:"Enter value"})},`cell-${e.id}-no-row`):r.map(e=>(0,a.jsx)(n.nA,{className:"border p-1",children:(0,a.jsx)(i.p,{value:h[`${e.id}_no_row`]||"",onChange:r=>N(e.id,"no_row",r.target.value),className:"w-full",required:l,placeholder:"Enter value"})},`cell-${e.id}-no-row`))})})})]})})}t(15695)},15695:(e,r,t)=>{t.d(r,{ZR:()=>o,am:()=>n,q7:()=>s});var a=t(12810);let s=async e=>{try{if(!e||isNaN(e))throw console.error("Invalid questionId:",e),Error("Invalid question ID provided");try{let r=await a.A.get(`/table-questions/${e}`);if(r.data&&r.data.data&&r.data.data.question)return r.data.data.question;if(r.data&&r.data.data)return r.data.data;if(r.data&&r.data.success)return r.data}catch(e){console.error("Error from /table-questions/ endpoint:",e)}try{let r=await a.A.get(`/questions/${e}`);if(r.data&&r.data.data)return r.data.data}catch(e){console.error("Error from /questions/ endpoint:",e)}try{let r=await a.A.get(`/tables/${e}`);if(r.data&&r.data.data&&r.data.data.question)return r.data.data.question}catch(e){console.error("Error from /tables/ endpoint:",e)}throw console.error("All endpoints failed to return valid data"),Error("Failed to fetch table structure from any endpoint")}catch(e){throw console.error("Error fetching table structure:",e),e}},o=async(e,r,t,s)=>{try{if(!e||!e.trim())throw Error("Table label is required");if(!r||isNaN(r))throw Error("Valid project ID is required");if(!t||!Array.isArray(t)||0===t.length)throw Error("At least one column is required");if(s&&!Array.isArray(s))throw Error("Rows must be an array if provided");if(t.filter(e=>!e.columnName||!e.columnName.trim()).length>0)throw Error("All columns must have valid names");if(s&&s.filter(e=>!e.rowsName||!e.rowsName.trim()).length>0)throw Error("All rows must have valid names");let o=t.map(e=>({columnName:e.columnName,parentColumnId:e.parentColumnId})),{data:n}=await a.A.post("/table-questions",{label:e,projectId:r,columns:o,rows:s||[]});if(!n||!n.success)throw Error(n?.message||"Failed to create table");return n.data}catch(e){throw console.error("Error creating table:",e),e.response&&(console.error("Response status:",e.response.status),console.error("Response data:",e.response.data),e.response.data&&e.response.data.message&&(e.message=e.response.data.message)),e}},n=async(e,r,t,s)=>{try{if(!r||!r.trim())throw Error("Table label is required");if(!e||isNaN(e))throw Error("Valid table ID is required");if(!t||!Array.isArray(t)||0===t.length)throw Error("At least one column is required");if(s&&!Array.isArray(s))throw Error("Rows must be an array if provided");if(t.filter(e=>!e.columnName||!e.columnName.trim()).length>0)throw Error("All columns must have valid names");if(s&&s.filter(e=>!e.rowsName||!e.rowsName.trim()).length>0)throw Error("All rows must have valid names");let o=new Map,n=new Map;for(let e of(t.forEach((e,r)=>{e.id&&o.set(e.id,e),n.set(r+1,e)}),t))if(e.parentColumnId){if(e.parentColumnId<=0)throw Error(`Invalid parent column ID: ${e.parentColumnId}. Must be a positive number.`);let r=t.find(r=>r.id===e.parentColumnId);if(!r&&e.parentColumnId<=t.length&&(r=n.get(e.parentColumnId)),!r)throw Error(`Parent column with ID/position ${e.parentColumnId} not found in the columns array.`);if(r.parentColumnId)throw Error("Cannot create more than 2 levels of nested columns (parent → child → grandchild)")}let i=t.map(e=>{let r={columnName:e.columnName.trim()};return e.id&&(r.id=e.id),void 0!==e.parentColumnId&&(r.parentColumnId=e.parentColumnId),r});try{let{data:t}=await a.A.patch(`/table-questions/${e}`,{label:r.trim(),columns:i,rows:s?s.map(e=>({...e,rowsName:e.rowsName.trim()})):[]});if(!t||!t.success)throw Error(t?.message||"Failed to update table");return t.data}catch(e){if(console.error("API error updating table:",e),e.response&&(console.error("Response status:",e.response.status),console.error("Response data:",e.response.data),e.response.data&&e.response.data.message))throw Error(e.response.data.message);throw e}}catch(e){if(console.error("Error updating table:",e),e.message)throw Error(`Failed to update table: ${e.message}`);throw Error("Failed to update table due to an unknown error")}}},24527:(e,r,t)=>{t.d(r,{OD:()=>d,Tr:()=>l,UL:()=>o,WK:()=>u});let a=(e,r)=>{if(!e.questionOptions||0===e.questionOptions.length)return null;if("selectone"===e.inputType&&"string"==typeof r){let t=e.questionOptions.find(e=>e.label===r);return t?.nextQuestionId||null}if("selectmany"===e.inputType&&Array.isArray(r))for(let t of r){let r=e.questionOptions.find(e=>e.label===t);if(r?.nextQuestionId)return r.nextQuestionId}return null},s=e=>e.questionOptions&&0!==e.questionOptions.length?e.questionOptions.map(e=>e.nextQuestionId).filter(e=>null!=e):[],o=(e,r)=>{let t=new Set,o=new Set;return e.forEach(e=>{s(e).forEach(e=>o.add(e))}),e.forEach(e=>{o.has(e.id)||t.add(e.id)}),Object.entries(r).forEach(([r,s])=>{let o=parseInt(r),n=e.find(e=>e.id===o);if(n&&s){let e=a(n,s);e&&t.add(e)}}),e.filter(e=>t.has(e.id))},n=(e,r)=>{let t=r.find(r=>r.id===e);if(!t)return[];let a=s(t);return r.filter(e=>a.includes(e.id))},i=(e,r)=>r.some(r=>s(r).includes(e)),l=(e,r)=>{let t=new Set(o(e,r).map(e=>e.id));return e.filter(r=>!i(r.id,e)).sort((e,r)=>e.position-r.position).map(r=>{let a=n(r.id,e).sort((e,r)=>e.position-r.position);return{question:r,isVisible:t.has(r.id),isFollowUp:!1,followUps:a.map(e=>({question:e,isVisible:t.has(e.id)}))}}).filter(e=>e.isVisible||e.followUps.some(e=>e.isVisible))},d=(e,r)=>{let t=new Set(r.map(e=>e.id)),a={};return Object.entries(e).forEach(([e,r])=>{t.has(parseInt(e))&&(a[e]=r)}),a},u=(e,r)=>{let t={};return e.forEach(e=>{if(e.isRequired){let a=r[e.id];("string"==typeof a&&!a.trim()||Array.isArray(a)&&0===a.length||null==a)&&(t[e.id]=`${e.label} is required`)}}),t}},69396:(e,r,t)=>{t.d(r,{A:()=>o});var a=t(60687);t(43210);var s=t(39390);let o=({questionGroup:e,renderQuestionInput:r,errors:t,className:o=""})=>{let{question:n,isVisible:i,followUps:l}=e;return i||l.some(e=>e.isVisible)?(0,a.jsxs)("div",{className:`${o}`,children:[i&&(0,a.jsxs)("div",{className:"border border-neutral-500 dark:border-neutral-700 rounded-md p-4 bg-neutral-100 dark:bg-neutral-800",children:[(0,a.jsxs)("div",{className:"mb-2",children:[(0,a.jsxs)(s.J,{className:"text-base font-medium",children:[n.label,n.isRequired&&(0,a.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),n.hint&&(0,a.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:n.hint}),t[n.id]&&(0,a.jsx)("p",{className:"text-sm text-red-500 mt-1",children:t[n.id]})]}),(0,a.jsx)("div",{className:"mt-2",children:r(n)}),l.some(e=>e.isVisible)&&(0,a.jsx)("div",{className:"mt-4 ml-4 space-y-3 border-l-2 border-primary-200 dark:border-primary-700 pl-4",children:l.map(({question:e,isVisible:o})=>o&&(0,a.jsxs)("div",{className:"border border-neutral-100 dark:border-neutral-600 rounded-md p-3 bg-primary-50 dark:bg-primary-900/20",children:[(0,a.jsxs)("div",{className:"mb-2",children:[(0,a.jsxs)(s.J,{className:"text-sm font-medium text-primary-900 dark:text-primary-100",children:[e.label,e.isRequired&&(0,a.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),e.hint&&(0,a.jsx)("p",{className:"text-xs text-primary-700 dark:text-primary-300 mt-1",children:e.hint}),t[e.id]&&(0,a.jsx)("p",{className:"text-xs text-red-500 mt-1",children:t[e.id]})]}),(0,a.jsx)("div",{className:"mt-2",children:r(e)})]},e.id))})]}),!i&&l.some(e=>e.isVisible)&&(0,a.jsx)("div",{className:"space-y-3",children:l.map(({question:e,isVisible:o})=>o&&(0,a.jsxs)("div",{className:"border border-neutral-200 dark:border-neutral-700 rounded-md p-4 bg-white dark:bg-neutral-800",children:[(0,a.jsxs)("div",{className:"mb-2",children:[(0,a.jsxs)(s.J,{className:"text-base font-medium",children:[e.label,e.isRequired&&(0,a.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),e.hint&&(0,a.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:e.hint}),t[e.id]&&(0,a.jsx)("p",{className:"text-sm text-red-500 mt-1",children:t[e.id]})]}),(0,a.jsx)("div",{className:"mt-2",children:r(e)})]},e.id))})]}):null}},75531:(e,r,t)=>{t.d(r,{Af:()=>i,K4:()=>o,ae:()=>p,dI:()=>c,ej:()=>n,gf:()=>m,ku:()=>d,sr:()=>u,ul:()=>l});var a=t(12810);let s=e=>{if("project"===e)return"/questions";if("template"===e)return"/template-questions";if("questionBlock"===e)return"/question-blocks";throw Error("Unsupported context type")},o=async({projectId:e})=>{let{data:r}=await a.A.get(`/questions/${e}`);return r.questions},n=async({templateId:e})=>{let{data:r}=await a.A.get(`/template-questions/${e}`);return r.questions},i=async({contextType:e,contextId:r,dataToSend:t,position:o})=>{let n="questionBlock"===e?`${s(e)}`:`${s(e)}/${r}`;if(!t.label||!t.inputType)throw Error("Label and inputType are required");let i=["selectone","selectmany"].includes(t.inputType),l=t.file instanceof File,d=Array.isArray(t.questionOptions)&&t.questionOptions.length>0;if(i&&!l&&!d)throw Error("Options are required for select input types");if(l){let e=new FormData;e.append("label",t.label),e.append("isRequired",t.isRequired?"true":"false"),e.append("inputType",t.inputType),t.hint&&e.append("hint",t.hint),t.placeholder&&e.append("placeholder",t.placeholder),e.append("position",String(o||1)),e.append("file",t.file);try{let{data:r}=await a.A.post(n,e,{headers:{"Content-Type":"multipart/form-data"}});return r}catch(e){throw console.error("Upload error details:",e.response?.data||e.message),Error(`Failed to upload question with file: ${e.response?.data?.message||e.message}`)}}try{let{data:e}=await a.A.post(n,{label:t.label,isRequired:t.isRequired,hint:t.hint,placeholder:t.placeholder,inputType:t.inputType,questionOptions:t.questionOptions,position:o||1});return e}catch(e){throw console.error("API error details:",e.response?.data||e.message),Error(`Failed to add question: ${e.response?.data?.message||e.message}`)}},l=async({contextType:e,id:r,projectId:t})=>{let{data:o}=await a.A.delete(`${s(e)}/${r}?projectId=${t}`);return o},d=async({id:e,contextType:r,contextId:t})=>{let{data:o}=await a.A.post(`${s(r)}/duplicate/${e}?projectId=${t}`,"questionBlock"===r?{}:"project"===r?{projectId:t}:{templateId:t});return o},u=async({id:e,contextType:r,dataToSend:t,contextId:o})=>{let{data:n}=await a.A.patch(`${s(r)}/${e}?projectId=${o}`,t);return n},c=async()=>{try{return(await a.A.get("/question-blocks")).data.questions||[]}catch(e){throw console.error("Error fetching question block questions:",e),e}},p=async({contextType:e,contextId:r,questionPositions:t})=>{if("project"!==e)throw Error("Question position updates are only supported for projects");let o=`${s(e)}/positions?projectId=${r}`;try{let{data:e}=await a.A.patch(o,{questionPositions:t});return e}catch(e){throw console.error("Update failed - Full error:",e),console.error("Update failed - Error details:",{status:e.response?.status,statusText:e.response?.statusText,data:e.response?.data,message:e.message,config:{url:e.config?.url,method:e.config?.method,data:e.config?.data}}),e}},m=async({projectId:e})=>{let{data:r}=await a.A.get(`/projects/getalldata/${e}`);return r.data}}};