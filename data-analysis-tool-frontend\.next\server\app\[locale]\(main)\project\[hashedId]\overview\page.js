(()=>{var e={};e.id=4653,e.ids=[4653],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7182:(e,s,t)=>{Promise.resolve().then(t.bind(t,90541))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},15762:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Kobo-project\\\\data-analysis-tool-frontend\\\\app\\\\[locale]\\\\(main)\\\\project\\\\[hashedId]\\\\overview\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\project\\[hashedId]\\overview\\page.tsx","default")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20174:(e,s,t)=>{"use strict";t.d(s,{F:()=>o});var r=t(60687),a=t(16189),i=t(85814),l=t.n(i);t(43210);let o=({items:e})=>{let s=(0,a.usePathname)(),t=e=>s.startsWith(e);return(0,r.jsx)("div",{className:"border-y border-neutral-400 rounded-md bg-primary-500 my-4 shadow-md",children:(0,r.jsx)("div",{className:"flex items-center",children:e.map(e=>e.disabled?(0,r.jsxs)("div",{className:"flex items-center gap-2 font-medium transition-all duration-300 p-4 border-b-2 text-gray-400 cursor-not-allowed",children:[e.icon,e.label]},e.route):(0,r.jsxs)(l(),{href:e.route,className:`flex items-center gap-2 font-medium transition-all duration-300 p-4 border-b-2 text-neutral-100 ${t(e.route)?"border-neutral-100":"border-transparent hover:border-neutral-400"}`,children:[e.icon,e.label]},e.route))})})}},21820:e=>{"use strict";e.exports=require("os")},24466:(e,s,t)=>{Promise.resolve().then(t.bind(t,64222))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29950:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>m,tree:()=>d});var r=t(65239),a=t(48088),i=t(88170),l=t.n(i),o=t(30893),n={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>o[e]);t.d(s,n);let d={children:["",{children:["[locale]",{children:["(main)",{children:["project",{children:["[hashedId]",{children:["overview",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,15762)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\project\\[hashedId]\\overview\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,51129)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\project\\[hashedId]\\layout.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,84606)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,72121)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,58014)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\project\\[hashedId]\\overview\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/(main)/project/[hashedId]/overview/page",pathname:"/[locale]/project/[hashedId]/overview",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},33873:e=>{"use strict";e.exports=require("path")},37446:(e,s,t)=>{Promise.resolve().then(t.bind(t,51129))},48730:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},51129:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Kobo-project\\\\data-analysis-tool-frontend\\\\app\\\\[locale]\\\\(main)\\\\project\\\\[hashedId]\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\project\\[hashedId]\\layout.tsx","default")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58869:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64222:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>M});var r=t(60687),a=t(93613),i=t(62688);let l=(0,i.A)("circle-help",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);var o=t(58869),n=t(57800);let d=(0,i.A)("chart-gantt",[["path",{d:"M10 6h8",key:"zvc2xc"}],["path",{d:"M12 16h6",key:"yi5mkt"}],["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M8 11h7",key:"wz2hg0"}]]);var c=t(48730),p=t(40228);let m=(0,i.A)("rocket",[["path",{d:"M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z",key:"m3kijz"}],["path",{d:"m12 15-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z",key:"1fmvmk"}],["path",{d:"M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0",key:"1f8sc4"}],["path",{d:"M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5",key:"qeys4"}]]),u=(0,i.A)("upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]]);var x=t(11437),h=t(43210),j=t(85650),b=t(86429),f=t(29494),v=t(71845),y=t(16189),g=t(6986),N=t(21650),k=t(54864),w=t(19150),A=t(77618);let M=()=>{let[e,s]=(0,h.useState)(!1);(0,h.useEffect)(()=>{s(!0)},[]);let{hashedId:t}=(0,y.useParams)(),i=(0,g.D)(t),M=(0,A.c3)(),{user:P}=(0,N.A)(),z=(0,k.wA)(),{data:q,isLoading:D,isError:C}=(0,f.I)({queryKey:["projects",P?.id,i],queryFn:()=>(0,v.kf)({projectId:i}),enabled:!!i&&!!P?.id});return e?D?(0,r.jsx)(b.A,{}):t&&null!==i?C?(0,r.jsx)("p",{className:"text-red-500"}):(0,r.jsxs)("div",{className:"flex flex-col gap-8",children:[(0,r.jsxs)("section",{className:"flex flex-col gap-2",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("span",{className:"text-lg flex items-center gap-2 font-medium capitalize",children:[(0,r.jsx)(a.A,{size:18})," ",M("description")]}),(0,r.jsxs)("span",{className:"rounded-full px-2 py-1 bg-accent-200 text-accent-700 font-medium text-sm flex items-center gap-2",children:[(0,r.jsx)("span",{className:"size-2 rounded-full bg-accent-700"}),q?.status?M(q.status):M("unknownStatus")]})]}),(0,r.jsx)("p",{children:q?.description})]}),(0,r.jsxs)("section",{className:"grid grid-cols-3 gap-8",children:[(0,r.jsxs)("div",{className:"rounded-md border-2 border-neutral-400 p-8 flex items-center justify-center flex-col gap-2",children:[(0,r.jsxs)("span",{className:"label-text capitalize",children:[(0,r.jsx)(l,{size:16}),M("questions")]}),(0,r.jsx)("span",{className:"text-lg font-medium",children:q?.questions?.length})]}),(0,r.jsxs)("div",{className:"rounded-md border-2 border-neutral-400 p-8 flex items-center justify-center flex-col gap-2",children:[(0,r.jsxs)("span",{className:"label-text capitalize",children:[(0,r.jsx)(o.A,{size:16}),M("owner")]}),(0,r.jsx)("span",{className:"text-lg font-medium",children:q?.user.name})]}),(0,r.jsxs)("div",{className:"rounded-md border-2 border-neutral-400 p-8 flex items-center justify-center text-center flex-col gap-2",children:[(0,r.jsxs)("span",{className:"label-text capitalize",children:[(0,r.jsx)(n.A,{size:16}),M("sector")]}),(0,r.jsx)("span",{className:"text-lg font-medium w-full truncate",children:q?.sector})]}),(0,r.jsxs)("div",{className:"flex flex-col gap-4",children:[q?.status==="deployed"&&(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-lg flex items-center gap-2 font-medium capitalize pb-3",children:M("collectData")}),(0,r.jsxs)("div",{className:"flex gap-3",children:[(0,r.jsx)("button",{className:"btn-primary",onClick:()=>{if(t){let e=`${window.location.origin}/form-submission/${t}`;navigator.clipboard.writeText(e).then(()=>{z((0,w.Ds)({message:M("linkCopied"),type:"success"}))}).catch(e=>{console.error("Failed to copy: ",e),z((0,w.Ds)({message:M("copyFailed"),type:"error"}))})}else z((0,w.Ds)({message:M("invalidLink"),type:"warning"}))},children:M("copy")}),(0,r.jsx)("button",{className:"btn-primary",onClick:()=>{t?window.open(`/form-submission/${t}`,"_blank"):console.error("hashedId is missing")},children:M("open")})]})]}),(0,r.jsxs)("span",{className:"text-lg flex items-center gap-2 font-medium capitalize",children:[(0,r.jsx)(d,{size:18}),M("timeline")]}),(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)("div",{className:"rounded-full p-2 bg-primary-200",children:(0,r.jsx)(c.A,{size:16,className:"text-primary-500"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"capitalize",children:M("lastModified")}),(0,r.jsxs)("span",{className:"label-text",children:[(0,r.jsx)(p.A,{size:16}),q?.updatedAt?(0,j.GP)(new Date(q.updatedAt),"MMMM d, yyyy h:mm a"):"N/A"]})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)("div",{className:"rounded-full p-2 bg-primary-200",children:(0,r.jsx)(m,{size:16,className:"text-primary-500"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"capitalize",children:M("lastDeployed")}),(0,r.jsxs)("span",{className:"label-text",children:[(0,r.jsx)(p.A,{size:16}),q?.lastDeployedAt?(0,j.GP)(new Date(q.lastDeployedAt),"MMMM d, yyyy h:mm a"):M("notDeployedYet")]})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)("div",{className:"rounded-full p-2 bg-primary-200",children:(0,r.jsx)(u,{size:16,className:"text-primary-500"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"capitalize",children:M("latestSubmission")}),(0,r.jsxs)("span",{className:"label-text",children:[(0,r.jsx)(p.A,{size:16}),q?.lastSubmissionAt?(0,j.GP)(new Date(q.lastSubmissionAt),"MMMM d, yyyy h:mm a"):M("noSubmissionsYet")]})]})]})]})]}),(0,r.jsxs)("section",{className:"label-text cursor-default",children:[(0,r.jsx)(x.A,{size:16})," ",q?.country]})]}):(0,r.jsxs)("div",{className:"error-message",children:[(0,r.jsxs)("h1",{className:"text-red-500",children:[" ",M("invalidProjectId")," (hashedId)."]}),(0,r.jsx)("p",{className:"text-neutral-700",children:M("invalidProjectUrl")})]}):null}},74075:e=>{"use strict";e.exports=require("zlib")},78407:(e,s,t)=>{"use strict";t.d(s,{F:()=>a});var r=t(43210);let a=({projectData:e,user:s})=>(0,r.useMemo)(()=>{let t=s?.id===e?.user?.id,r=e?.projectUser?.[0],a=r?.permission||{};return{viewForm:t||a.viewForm||!1,editForm:t||a.editForm||!1,viewSubmissions:t||a.viewSubmissions||!1,addSubmissions:t||a.addSubmissions||!1,deleteSubmissions:t||a.deleteSubmissions||!1,validateSubmissions:t||a.validateSubmissions||!1,editSubmissions:t||a.editSubmissions||!1,manageProject:t||a.manageProject||!1}},[s?.id,e])},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84714:(e,s,t)=>{Promise.resolve().then(t.bind(t,15762))},90541:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>N});var r=t(60687),a=t(86429),i=t(86757),l=t(17090),o=t(61611),n=t(84027),d=t(16189),c=t(20174),p=t(77618);let m=({permissions:e})=>{let{hashedId:s}=(0,d.useParams)(),t=(0,p.c3)(),a=e.manageProject,m=a||e.viewForm||e.editForm,u=a||e.viewSubmissions||e.editSubmissions||e.addSubmissions||e.deleteSubmissions,x=[{label:t("overview"),icon:(0,r.jsx)(i.A,{size:16}),route:`/project/${s}/overview`,disabled:!1},{label:t("formBuilder"),icon:(0,r.jsx)(l.A,{size:16}),route:`/project/${s}/form-builder`,disabled:!m},{label:t("data"),icon:(0,r.jsx)(o.A,{size:16}),route:`/project/${s}/data`,disabled:!u},{label:t("settings"),icon:(0,r.jsx)(n.A,{size:16}),route:`/project/${s}/settings`,disabled:!a}];return(0,r.jsx)(c.F,{items:x})};var u=t(21650),x=t(78407),h=t(71845),j=t(6986),b=t(29494),f=t(28559),v=t(85814),y=t.n(v),g=t(43210);let N=({children:e})=>{let{hashedId:s}=(0,d.useParams)(),{user:t}=(0,u.A)(),i=(0,p.c3)(),l=(0,g.useMemo)(()=>(0,j.D)(s),[s]),{data:o,isLoading:n,isError:c}=(0,b.I)({queryKey:["projects",t?.id,l],queryFn:()=>(0,h.kf)({projectId:l}),enabled:!!l&&!!t?.id}),v=(0,x.F)({projectData:o,user:t});return s&&null!==l?n?(0,r.jsx)(a.A,{}):c?(0,r.jsx)("p",{className:"text-red-500",children:i("fetchProjectFailed")}):(0,r.jsxs)("div",{className:"section flex flex-col gap-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h1",{className:"heading-text capitalize",children:o?.name}),(0,r.jsxs)(y(),{href:"/dashboard",className:"flex items-center gap-2",children:[(0,r.jsx)(f.A,{size:16}),i("backToDashboard")]})]}),(0,r.jsx)(m,{permissions:v}),(0,r.jsx)("div",{className:"px-8",children:e})]}):(0,r.jsxs)("div",{className:"error-message",children:[(0,r.jsx)("h1",{className:"text-red-500",children:i("invalidProjectIdError")}),(0,r.jsx)("p",{className:"text-neutral-700",children:i("invalidProjectIdMessage")})]})}},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4447,7404,1658,6560,7618,63,7605,3851,8581,4898,6226,5233],()=>t(29950));module.exports=r})();