import axios from "@/lib/axios";
import { ContextType } from "@/types";

const getQuestionsEndPoint = (contextType: ContextType) => {
  if (contextType === "project") return "/questions";
  else if (contextType === "template") return "/template-questions";
  else if (contextType === "questionBlock") return "/question-blocks";
  throw new Error("Unsupported context type");
};

const fetchQuestions = async ({ projectId }: { projectId: number }) => {
  const { data } = await axios.get(`/questions/${projectId}`);
  return data.questions;
};

const fetchTemplateQuestions = async ({
  templateId,
}: {
  templateId: number;
}) => {
  const { data } = await axios.get(`/template-questions/${templateId}`);
  return data.questions;
};

const addQuestion = async ({
  contextType,
  contextId,
  dataToSend,
  position,
}: {
  contextType: ContextType;
  contextId: number;
  dataToSend: {
    label: string;
    isRequired: boolean;
    hint?: string;
    placeholder?: string;
    inputType: string;
    questionOptions?: {
      label: string;
      sublabel?: string;
      code: string;
      nextQuestionId?: number | null;
    }[];
    file?: File;
  };
  position?: number;
}) => {
  const url =
    contextType === "questionBlock"
      ? `${getQuestionsEndPoint(contextType)}`
      : `${getQuestionsEndPoint(contextType)}/${contextId}`;

  // Validate required fields
  if (!dataToSend.label || !dataToSend.inputType) {
    throw new Error("Label and inputType are required");
  }

  // Check if this input type requires options
  const needsOptions = ["selectone", "selectmany"].includes(
    dataToSend.inputType
  );
  const hasFile = dataToSend.file instanceof File;
  const hasOptions =
    Array.isArray(dataToSend.questionOptions) &&
    dataToSend.questionOptions.length > 0;

  // Validate options based on input type and upload method
  if (needsOptions && !hasFile && !hasOptions) {
    throw new Error("Options are required for select input types");
  }

  if (hasFile) {
    const formData = new FormData();

    // Add basic question data
    formData.append("label", dataToSend.label);
    // Convert boolean to string in a way backend can parse
    formData.append("isRequired", dataToSend.isRequired ? "true" : "false");
    formData.append("inputType", dataToSend.inputType);
    if (dataToSend.hint) formData.append("hint", dataToSend.hint);
    if (dataToSend.placeholder)
      formData.append("placeholder", dataToSend.placeholder);
    // Convert number to string
    formData.append("position", String(position || 1));

    // Add file with the correct field name
    formData.append("file", dataToSend.file as File);

    // Important: Do NOT include questionOptions when uploading a file
    // They will be parsed from the file on the server

    try {
      const { data } = await axios.post(url, formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });
      return data;
    } catch (error: any) {
      console.error(
        "Upload error details:",
        error.response?.data || error.message
      );
      throw new Error(
        `Failed to upload question with file: ${
          error.response?.data?.message || error.message
        }`
      );
    }
  } else {
    // Regular JSON request (no file)
    try {
      const { data } = await axios.post(url, {
        label: dataToSend.label,
        isRequired: dataToSend.isRequired,
        hint: dataToSend.hint,
        placeholder: dataToSend.placeholder,
        inputType: dataToSend.inputType,
        questionOptions: dataToSend.questionOptions,
        position: position || 1,
      });
      return data;
    } catch (error: any) {
      console.error(
        "API error details:",
        error.response?.data || error.message
      );
      throw new Error(
        `Failed to add question: ${
          error.response?.data?.message || error.message
        }`
      );
    }
  }
};
const deleteQuestion = async ({
  contextType,
  id,
  projectId,
}: {
  contextType: ContextType;
  id: number;
  projectId: number;
}) => {
  const { data } = await axios.delete(
    `${getQuestionsEndPoint(contextType)}/${id}?projectId=${projectId}`
  );
  return data;
};

const duplicateQuestion = async ({
  id,
  contextType,
  contextId,
}: {
  id: number;
  contextType: ContextType;
  contextId: number;
}) => {
  // For question blocks, we don't need to send the contextId in the body
  // The userId is taken from the authenticated user in the backend
  const requestBody =
    contextType === "questionBlock"
      ? {}
      : contextType === "project"
        ? { projectId: contextId }
        : { templateId: contextId };

  const { data } = await axios.post(
    `${getQuestionsEndPoint(
      contextType
    )}/duplicate/${id}?projectId=${contextId}`,
    requestBody
  );

  return data;
};

const updateQuestion = async ({
  id,
  contextType,
  dataToSend,
  contextId,
}: {
  id: number;
  contextType: ContextType;
  dataToSend: {
    label: string;
    isRequired: boolean;
    hint: string;
    placeholder: string;
    position?: number; // Optional position field to preserve question order
    questionOptions?: {
      label: string;
      sublabel?: string;
      code: string;
      nextQuestionId?: number | null;
    }[];
  };
  contextId: number;
}) => {
  const { data } = await axios.patch(
    `${getQuestionsEndPoint(contextType)}/${id}?projectId=${contextId}`,
    dataToSend
  );
  return data;
};

const fetchQuestionBlockQuestions = async () => {
  try {
    const response = await axios.get(`/question-blocks`);
    return response.data.questions || [];
  } catch (error) {
    console.error("Error fetching question block questions:", error);
    throw error;
  }
};

const updateQuestionPositions = async ({
  contextType,
  contextId,
  questionPositions,
}: {
  contextType: ContextType;
  contextId: number;
  questionPositions: { id: number; position: number }[];
}) => {
  // Only support position updates for projects currently
  if (contextType !== "project") {
    throw new Error(
      "Question position updates are only supported for projects"
    );
  }

  const url = `${getQuestionsEndPoint(
    contextType
  )}/positions?projectId=${contextId}`;
  const payload = { questionPositions };

  try {
    const { data } = await axios.patch(url, payload);
    return data;
  } catch (error: any) {
    console.error("Update failed - Full error:", error);
    console.error("Update failed - Error details:", {
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      message: error.message,
      config: {
        url: error.config?.url,
        method: error.config?.method,
        data: error.config?.data,
      },
    });
    throw error;
  }
};

// NEW UNIFIED POSITION SYSTEM: Update positions using ProjectQuestionOrder schema
const updateUnifiedPositions = async ({
  contextType,
  contextId,
  positionUpdates,
}: {
  contextType: ContextType;
  contextId: number;
  positionUpdates: {
    id: number;
    position: number;
    type: 'question' | 'group';
    parentGroupId?: number | null;
    groupId?: number | null;
  }[];
}) => {
  // Only support position updates for projects currently
  if (contextType !== "project") {
    throw new Error(
      "Unified position updates are only supported for projects"
    );
  }

  const url = `${getQuestionsEndPoint(
    contextType
  )}/unified-positions?projectId=${contextId}`;
  const payload = { positionUpdates };

  try {
    const { data } = await axios.patch(url, payload);
    return data;
  } catch (error: any) {
    console.error("Unified position update failed - Full error:", error);
    console.error("Unified position update failed - Error details:", {
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      message: error.message,
      config: {
        url: error.config?.url,
        method: error.config?.method,
        data: error.config?.data,
      },
    });
    throw error;
  }
};

// Fetch form builder data with ordered structure (groups and questions)
const fetchFormBuilderData = async ({ projectId }: { projectId: number }) => {
  const { data } = await axios.get(`/projects/getalldata/${projectId}`);
  return data.data;
};

export {
  fetchQuestions,
  fetchTemplateQuestions,
  addQuestion,
  deleteQuestion,
  duplicateQuestion,
  updateQuestion,
  fetchQuestionBlockQuestions,
  updateQuestionPositions,
  updateUnifiedPositions,
  fetchFormBuilderData,
};
