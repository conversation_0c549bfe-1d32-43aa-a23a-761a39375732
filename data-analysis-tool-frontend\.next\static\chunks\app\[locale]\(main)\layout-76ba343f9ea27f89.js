(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1969,9660],{2511:(e,t,a)=>{"use strict";a.d(t,{b:()=>r});let r={information_media:"Information / Media",econommic_social_development:"Economic & Social Development",security_police_peacekeeping:"Security / Police / Peacekeeping",disarmament_and_demobilization:"Disarmament & Demobilization",environment:"Environment",private_sector:"Private Sector",humanitarian_coordination_information_management:"Humanitarian - Coordination & Info Management",humanitarian_multiple_clusters:"Humanitarian - Multiple Clusters",humanitarian_camp_management_and_coordination:"Humanitarian - Camp Management & Coordination",humanitarian_early_recovery:"Humanitarian - Early Recovery",humanitarian_education:"Humanitarian - Education",humanitarian_emergency_shelter:"Humanitarian - Emergency Shelter",humanitarian_emergency_telecoms:"Humanitarian - Emergency Telecoms",humanitarian_food_security:"Humanitarian - Food Security",humanitarian_health:"Humanitarian - Health",humanitarian_logistics:"Humanitarian - Logistics",humanitarian_nutrition:"Humanitarian - Nutrition",humanitarian_protection:"Humanitarian - Protection",humanitarian_sanitation_water_and_hygiene:"Humanitarian - Sanitation / Water / Hygiene",other:"Other"}},13163:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var r=a(95155),s=a(60760),n=a(44518),i=a(95233),l=a(54416);a(12115);let o=e=>{let{children:t,className:a,isOpen:o,onClose:c,preventOutsideClick:d=!1}=e;return(0,r.jsx)(s.N,{children:o&&(0,r.jsx)(n.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-neutral-900/50 flex items-center justify-center z-40 p-4 overflow-y-auto",onClick:e=>{d||c()},children:(0,r.jsxs)(n.P.div,{initial:{scale:.6,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.6,opacity:0},transition:{duration:.3,ease:i.am},className:"relative section flex flex-col max-w-4xl max-h-[90vh] overflow-y-auto ".concat(a),onClick:e=>e.stopPropagation(),children:[(0,r.jsx)(l.A,{onClick:c,className:"absolute self-end cursor-pointer text-neutral-700 hover:text-neutral-900 active:scale-90 transition-all duration-300"}),t]})})})}},15616:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var r=a(95155),s=a(46453),n=a(35695),i=a(6874),l=a.n(i);function o(){let e=(0,s.Ym)(),t=(0,n.usePathname)(),a=e=>{let a=t.replace(/^\/(en|ne)/,"");return"/".concat(e).concat(a)};return(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(l(),{href:a("en"),className:"px-3 py-1 rounded ".concat("en"===e?"bg-primary-600 text-white":"bg-gray-200"),children:"English"}),(0,r.jsx)(l(),{href:a("ne"),className:"px-3 py-1 rounded ".concat("ne"===e?"bg-primary-600 text-white":"bg-gray-200"),children:"नेपाली"})]})}},17576:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},25784:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let r=a(23464).A.create({baseURL:"http://localhost:4000/api",headers:{"Content-Type":"application/json"},withCredentials:!0});r.interceptors.request.use(e=>e,e=>Promise.reject(e)),r.interceptors.response.use(e=>e,e=>("ERR_NETWORK"===e.code&&console.error("Network error - Please check if the backend server is running"),Promise.reject(e)));let s=r},26862:(e,t,a)=>{"use strict";a.d(t,{Sc:()=>h.S,dO:()=>p}),a(97168),a(89852),a(82714),a(99474);var r=a(95155),s=a(12115),n=a(38715),i=a(66474),l=a(47863),o=a(5196),c=a(53999);n.bL,n.YJ,n.WT,s.forwardRef((e,t)=>{let{className:a,children:s,...l}=e;return(0,r.jsxs)(n.l9,{ref:t,className:(0,c.cn)("flex h-10 w-full items-center justify-between rounded-md border border-gray-200 bg-neutral-100 px-3 py-2 text-sm shadow-sm ring-offset-neutral-100 placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1 dark:border-gray-700 dark:bg-gray-900 dark:ring-offset-gray-900 dark:placeholder:text-gray-500",a),...l,children:[s,(0,r.jsx)(n.In,{asChild:!0,children:(0,r.jsx)(i.A,{className:"h-4 w-4 opacity-50"})})]})}).displayName=n.l9.displayName;let d=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(n.PP,{ref:t,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",a),...s,children:(0,r.jsx)(l.A,{className:"h-4 w-4"})})});d.displayName=n.PP.displayName;let u=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(n.wn,{ref:t,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",a),...s,children:(0,r.jsx)(i.A,{className:"h-4 w-4"})})});u.displayName=n.wn.displayName,s.forwardRef((e,t)=>{let{className:a,children:s,position:i="popper",...l}=e;return(0,r.jsx)(n.ZL,{children:(0,r.jsxs)(n.UC,{ref:t,className:(0,c.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border border-gray-200 bg-neutral-100 text-slate-700 shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 dark:border-gray-700 dark:bg-gray-900 dark:text-slate-200","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:i,...l,children:[(0,r.jsx)(d,{}),(0,r.jsx)(n.LM,{className:(0,c.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),(0,r.jsx)(u,{})]})})}).displayName=n.UC.displayName,s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(n.JU,{ref:t,className:(0,c.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",a),...s})}).displayName=n.JU.displayName,s.forwardRef((e,t)=>{let{className:a,children:s,...i}=e;return(0,r.jsxs)(n.q7,{ref:t,className:(0,c.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-gray-100 focus:text-gray-900 data-[disabled]:pointer-events-none data-[disabled]:opacity-50 dark:focus:bg-gray-800 dark:focus:text-gray-50",a),...i,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(n.VF,{children:(0,r.jsx)(o.A,{className:"h-4 w-4"})})}),(0,r.jsx)(n.p4,{children:s})]})}).displayName=n.q7.displayName,s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(n.wv,{ref:t,className:(0,c.cn)("-mx-1 my-1 h-px bg-gray-200 dark:bg-gray-700",a),...s})}).displayName=n.wv.displayName;var m=a(4884);let p=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(m.bL,{className:(0,c.cn)("peer inline-flex h-[24px] w-[44px] shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 focus-visible:ring-offset-neutral-100 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary-500 data-[state=unchecked]:bg-neutral-100 dark:focus-visible:ring-offset-gray-900",a),...s,ref:t,children:(0,r.jsx)(m.zi,{className:(0,c.cn)("pointer-events-none block h-5 w-5 rounded-full shadow-lg ring-0 transition-transform data-[state=checked]:bg-neutral-100 data-[state=unchecked]:bg-primary-500 data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})});p.displayName=m.bL.displayName;var h=a(95139);a(55747)},29350:(e,t,a)=>{"use strict";a.d(t,{A:()=>c});var r=a(97381),s=a(59362),n=a(25784),i=a(35695),l=a(12115),o=a(34540);let c=e=>{let t=(0,o.wA)(),a=(0,i.useRouter)(),c=(0,i.usePathname)(),{status:d,user:u,error:m}=(0,o.d4)(e=>e.auth),p=async()=>{try{t((0,r.Le)());let e=(await n.A.get("/users/me")).data;t((0,r.tQ)(e))}catch(n){if(t((0,r.x9)()),(0,s.F0)(n)){var e,i,l,o,d;if(console.error("Auth error:",null==(e=n.response)?void 0:e.status,null==(i=n.response)?void 0:i.data),(null==(l=n.response)?void 0:l.status)===401){if(c.startsWith("/form-submission"))return;a.push("/")}else t((0,r.jB)((null==(d=n.response)||null==(o=d.data)?void 0:o.message)||n.message))}else t((0,r.jB)(n instanceof Error?n.message:"An unknown error occurred."))}};return(0,l.useEffect)(()=>{(null==e?void 0:e.skipFetchUser)||p()},[null==e?void 0:e.skipFetchUser]),(0,l.useEffect)(()=>{let e=e=>{if("logout"===e.key&&"true"===e.newValue)if(t((0,r.x9)()),c.startsWith("/form-submission")){let e=c.split("/")[2];e?a.push("/form-submission/".concat(e,"/sign-in")):a.push("/")}else a.push("/")};return window.addEventListener("storage",e),()=>{window.removeEventListener("storage",e)}},[t,a,c]),{status:d,user:u,error:m,isAuthenticated:"authenticated"===d,isLoading:"loading"===d,refreshAuthState:()=>{p()},signin:async(e,t,a)=>{try{await n.A.post("/users/login",e),await p(),null==t||t()}catch(e){if(e instanceof s.pe){var r,i;let t=null==(i=e.response)||null==(r=i.data)?void 0:r.errorType;null==a||a(t)}else null==a||a()}},logout:async()=>{try{await n.A.post("/users/logout"),localStorage.setItem("logout","true"),setTimeout(()=>localStorage.removeItem("logout"),100)}finally{if(t((0,r.x9)()),c.startsWith("/form-submission")){let e=c.split("/")[2];e?a.push("/form-submission/".concat(e,"/sign-in")):a.push("/")}else a.push("/")}}}}},31917:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>es});var r=a(95155),s=a(12115),n=a(29350);let i=(0,a(19946).A)("log-out",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]);var l=a(6874),o=a.n(l),c=a(35695),d=a(11906),u=a(15616),m=a(17652);let p=e=>{let{toggleSidebar:t,navbarRef:a}=e,[l,p]=(0,s.useState)(!1),h=(0,s.useRef)(null);(0,c.useRouter)();let x=(0,m.c3)();(0,s.useEffect)(()=>{let e=e=>{h.current&&!h.current.contains(e.target)&&p(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]);let{user:g,logout:b}=(0,n.A)();return(0,r.jsx)("header",{className:"bg-primary-800 p-4 sticky top-0 z-40",ref:a,children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)("button",{onClick:t,className:"laptop:hidden text-neutral-100 p-2 rounded hover:bg-primary-700",children:(0,r.jsx)(d.$4x,{size:24})}),(0,r.jsx)(o(),{href:"/dashboard",className:"text-neutral-100 text-2xl font-bold cursor-pointer hover:text-neutral-300 transition-all ease-in-out",children:x("dataAnalysis")})]}),(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)(u.A,{}),(0,r.jsxs)("div",{className:"relative",ref:h,children:[(0,r.jsx)("button",{onClick:()=>p(!l),className:"w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center text-neutral-100 font-semibold cursor-pointer",children:null==g?void 0:g.name[0].toUpperCase()}),l&&(0,r.jsxs)("div",{className:"absolute right-0 mt-2 w-64 bg-neutral-100 rounded-md shadow-lg p-4 flex flex-col gap-2",children:[(0,r.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"size-10 bg-orange-500 rounded-full flex items-center justify-center text-neutral-100 text-xl font-semibold shrink-0",children:null==g?void 0:g.name[0].toUpperCase()}),(0,r.jsxs)("div",{className:"flex flex-col min-w-0",children:[(0,r.jsx)("div",{className:"font-medium capitalize",children:null==g?void 0:g.name}),(0,r.jsx)("div",{className:"text-sm text-neutral-700 truncate",children:null==g?void 0:g.email})]})]}),(0,r.jsx)(o(),{href:"/account/profile",className:"btn-primary",onClick:()=>{p(!1)},children:x("profile")})]}),(0,r.jsx)("hr",{className:"border-neutral-400"}),(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsx)(o(),{onClick:()=>p(!1),href:"/terms",className:"text-sm text-neutral-700 hover:bg-neutral-700/10 px-4 py-1 rounded-md",children:x("termsOfService")}),(0,r.jsx)(o(),{onClick:()=>p(!1),href:"/policy",className:"block px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-200",children:x("privacyPolicy")})]}),(0,r.jsx)("hr",{className:"border-neutral-400"}),(0,r.jsxs)("button",{onClick:b,className:"flex items-center text-neutral-700 gap-2 px-4 py-2 hover:bg-neutral-700/10 rounded-md active:scale-95 transition-all duration-300 w-full cursor-pointer",children:[(0,r.jsx)(i,{size:16}),x("logout")]})]})]})]})]})})};var h=a(93347),x=a(14549),g=a(34540),b=a(39286),f=a(62672),y=a(15305),v=a(12515),j=a(29911),N=a(19373),w=a(77361),k=a(46453);let S=()=>{let{user:e}=(0,n.A)(),t=(0,k.Ym)(),a=(0,m.c3)(),{data:r,isLoading:s,isError:i}=(0,N.I)({queryKey:["projects",null==e?void 0:e.id],queryFn:w.vj,enabled:!!(null==e?void 0:e.id)}),l=[],o=[],c=[];return!s&&r&&(l=r.filter(e=>"deployed"===e.status),o=r.filter(e=>"draft"===e.status),c=r.filter(e=>"archived"===e.status)),{navItems:[{id:1,icon:y.nko,label:a("deployed"),count:l.length||0,href:"/".concat(t,"/dashboard/deployed"),category:"project",status:"deployed"},{id:2,icon:v.fzI,label:a("draft"),count:o.length||0,href:"/".concat(t,"/dashboard/draft"),category:"project",status:"draft"},{id:3,icon:j.Wlj,label:a("archived"),count:c.length||0,href:"/".concat(t,"/dashboard/archived"),category:"project",status:"archived"},{id:4,icon:x.rjU,label:a("myLibrary"),count:0,href:"/".concat(t,"/library"),category:"library"},{id:5,icon:v.Blu,label:a("collections"),count:0,href:"/".concat(t,"/library/#"),category:"library"}],deployedProjects:l,draftStatusProjects:o,archivedProjects:c}};var C=a(88570);let A=e=>{let{href:t,label:a,icon:n,count:i,status:l}=e,d=(0,c.usePathname)(),u=(0,c.useRouter)(),{deployedProjects:m,draftStatusProjects:p,archivedProjects:h}=S(),[x,g]=(0,s.useState)(!1),b=(0,k.Ym)(),f=e=>{let t=(0,C.l)(e);u.push("/project/".concat(t,"/overview"))},y=e=>{let t=(0,C.l)(e);return"/project/".concat(t,"/overview")},v=(()=>{switch(l){case"draft":return p;case"deployed":return m;case"archived":return h;default:return[]}})();return(0,r.jsxs)("li",{children:[(0,r.jsxs)(o(),{href:t,onClick:e=>{l&&(i>0?(e.preventDefault(),g(!x)):(e.preventDefault(),u.push("/".concat(b,"/dashboard/").concat(l,"/not-available"))))},className:"flex items-center px-4 py-3 hover:bg-primary-500 hover:text-neutral-100 transition-all duration-300 ease-in-out rounded-md text-neutral-800",children:[(0,r.jsx)(n,{className:"mr-2",size:20}),(0,r.jsx)("span",{className:"text-sm font-bold",children:a}),(0,r.jsx)("span",{className:"ml-auto bg-neutral-200 text-neutral-700 rounded-full px-2 py-0.5 text-xs",children:i})]}),x&&v.length>0&&(0,r.jsx)("div",{className:"ml-6 mt-1 space-y-1",children:v.map(e=>(0,r.jsx)("div",{onClick:()=>f(e.id),className:"flex items-center px-4 py-2 text-sm hover:bg-primary-500 hover:text-neutral-100 transition-all duration-300 ease-in-out rounded-md cursor-pointer ".concat(d===y(e.id)?"bg-primary-500 text-neutral-100":""),children:e.name},e.id))})]})},z=e=>{let{isOpen:t,topOffset:a,onNewProject:s}=e,n=(0,c.usePathname)(),i=(0,g.wA)(),{navItems:l,deployedProjects:d,draftStatusProjects:u,archivedProjects:p}=S(),y=(0,k.Ym)(),v=(0,m.c3)(),j=n.includes("/library")?"library":"project",N=l.filter(e=>e.category===j);return(0,r.jsxs)("aside",{style:{top:"".concat(a,"px"),height:"calc(100vh - ".concat(a,"px)")},className:"\n        ".concat(t?"translate-x-0":"-translate-x-full","\n        fixed left-0 h-full w-64 shadow-xl bg-neutral-100 z-30\n        transform transition-all duration-300 ease-in-out\n        laptop:translate-x-0 laptop:static laptop:flex flex\n      "),children:[(0,r.jsx)("div",{className:"w-1/5 bg-neutral-200 p-4",children:(0,r.jsxs)("div",{className:"flex flex-col gap-5 items-center",children:[(0,r.jsx)(o(),{href:"/".concat(y,"/dashboard"),"aria-label":"Projects",className:"p-2  hover:bg-primary-500 hover:rounded-full transition-all duration-300 ease-in-out cursor-pointer\n              ".concat("project"===j?"bg-primary-500 text-neutral-100 rounded-full":"text-neutral-700 hover:bg-primary-500 hover:text-neutral-100","\n            "),children:(0,r.jsx)(h._zY,{size:20,title:v("project")})}),(0,r.jsx)(o(),{href:"/".concat(y,"/library"),"aria-label":"Library",className:"p-2 hover:bg-primary-500 hover:rounded-full transition-all duration-300 ease-in-out cursor-pointer\n              ".concat("library"===j?"bg-primary-500 text-neutral-100 rounded-full":"text-neutral-700 hover:bg-primary-500 hover:text-neutral-100","\n            "),children:(0,r.jsx)(x.rjU,{size:20,title:v("library")})})]})}),(0,r.jsxs)("div",{className:"w-4/5 bg-neutral-100 flex-1",children:[(0,r.jsx)("div",{className:"p-4",children:(0,r.jsx)("button",{onClick:()=>{"project"===j?s?s():i((0,b.Gl)()):i((0,f.yg)())},className:"btn-primary w-full",children:v("project"===j?"newProject":"newItem")})}),(0,r.jsx)("nav",{className:"mt-2",children:(0,r.jsx)("ul",{className:"space-y-1",children:N.map(e=>(0,r.jsx)(A,{href:e.href,label:e.label,icon:e.icon,count:e.count,status:e.status},e.id))})})]})]})};var E=a(13163),_=a(62177),P=a(50408),L=a(74567),F=a(2511),M=a(57434),R=a(34869),q=a(17576),I=a(25784),H=a(26715),T=a(5041),O=a(71402),V=a(64368);let D=async e=>{let{name:t,description:a,sector:r,country:s}=e,{data:n}=await I.A.post("/projects",{name:t,description:a,sector:r,country:s});return n},B=function(){let{isOpen:e,onClose:t,onBack:a}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=(0,g.d4)(e=>e.createProject.visible),i=(0,g.wA)(),l=(0,m.c3)(),{register:o,formState:{isSubmitting:d,errors:u,isSubmitted:p},handleSubmit:h,setValue:x}=(0,_.mN)(),[f,y]=(0,s.useState)(null),[v,j]=(0,s.useState)(null);(0,s.useEffect)(()=>{o("country",{required:l("pleaseSelectCountry")}),o("sector",{required:l("pleaseSelectSector")})},[o]),(0,s.useEffect)(()=>{x("country",f,{shouldValidate:p}),x("sector",v,{shouldValidate:p})},[x,f,v]);let[N,w]=(0,s.useState)(!1),k=()=>{w(!0),setTimeout(()=>{i((0,b.th)())},300)},S=(0,c.useRouter)(),C=(0,H.jE)(),A=(0,T.n)({mutationFn:D,onSuccess:()=>{C.invalidateQueries({queryKey:["projects"],exact:!1}),k(),S.push("/dashboard"),i((0,O.Ds)({message:l("projectCreatedSuccess"),type:"success"}))},onError:()=>{i((0,O.Ds)({message:l("projectCreateFailed"),type:"error"}))}}),z=async e=>{A.mutate({name:e.projectName,description:e.description,sector:e.sector,country:e.country})};return(0,r.jsxs)(E.A,{isOpen:n&&!N,onClose:k,className:"w-4/5 laptop:w-3/5 ",children:[(0,r.jsx)("h1",{className:"heading-text",children:l("createNewProject")}),(0,r.jsx)("form",{className:"flex flex-col gap-8 max-h-[600px] overflow-y-auto p-4",onSubmit:h(z),children:(0,r.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsxs)("label",{htmlFor:"project-name",className:"label-text",children:[(0,r.jsx)(M.A,{size:16})," ",l("projectName")]}),(0,r.jsx)("input",{...o("projectName",{required:l("projectNameRequired")}),id:"project-name",type:"text",className:"input-field",placeholder:l("enterProjectName")}),u.projectName&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:"".concat(u.projectName.message)})]}),(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsx)("label",{htmlFor:"description",className:"label-text",children:l("description")}),(0,r.jsx)("textarea",{id:"description",...o("description",{required:l("enterProjectDescription")}),className:"input-field resize-none",cols:4,placeholder:l("enterProjectDescription")}),u.description&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:"".concat(u.description.message)})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsxs)("label",{htmlFor:"country",className:"label-text",children:[(0,r.jsx)(R.A,{size:16}),l("country")]}),(0,r.jsx)(P.l,{id:"country",options:L,value:f||l("selectOption"),onChange:y}),u.country&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:"".concat(u.country.message)})]}),(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsxs)("label",{htmlFor:"sector",className:"label-text",children:[(0,r.jsx)(q.A,{size:16})," ",l("sector")]}),(0,r.jsx)(P.l,{id:"sector",options:Object.values(F.b),value:v&&F.b[v]?F.b[v]:l("selectOption"),onChange:e=>{j((0,V.H)(e,F.b))}}),u.sector&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:"".concat(u.sector.message)})]})]}),(0,r.jsxs)("div",{className:"flex justify-end gap-3 mt-4",children:[a&&(0,r.jsx)("button",{type:"button",onClick:()=>{a&&a()},className:"btn-outline",children:l("back")}),(0,r.jsx)("button",{type:"submit",className:"btn-primary",children:d?(0,r.jsxs)("span",{className:"flex items-center gap-2",children:[l("creating"),(0,r.jsx)("div",{className:"size-4 animate-spin border-x border-neutral-100 rounded-full"})]}):l("createProject")})]})]})})]})};var K=a(75173),U=a(94974);let G=e=>{let{handleClose:t}=e,a=(0,g.wA)(),{register:i,formState:{isSubmitting:l,errors:o,isSubmitted:c},handleSubmit:d,setValue:u}=(0,_.mN)(),[p,h]=(0,s.useState)(null),[x,b]=(0,s.useState)(null);(0,s.useEffect)(()=>{i("country",{required:v("pleaseSelectCountry")}),i("sector",{required:v("pleaseSelectSector")})},[i]),(0,s.useEffect)(()=>{u("country",p,{shouldValidate:c}),u("sector",x,{shouldValidate:c})},[u,p,x]);let f=(0,H.jE)(),{user:y}=(0,n.A)(),v=(0,m.c3)(),j=(0,T.n)({mutationFn:U.Xu,onSuccess:()=>{f.invalidateQueries({queryKey:["templates",null==y?void 0:y.id]}),t(),a((0,O.Ds)({type:"success",message:v("templateCreated")}))},onError:()=>{a((0,O.Ds)({type:"error",message:v("templateCreationFailed")}))}}),N=async e=>{let t={name:e.name,description:e.description,sector:e.sector,country:e.country};j.mutate(t)};return(0,r.jsxs)("form",{className:"flex flex-col gap-8",onSubmit:d(N),children:[(0,r.jsx)("h1",{className:"heading-text capitalize",children:v("createNewProjectTemplate")}),(0,r.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsxs)("label",{htmlFor:"project-name",className:"label-text",children:[(0,r.jsx)(M.A,{size:16})," ",v("templateName")]}),(0,r.jsx)("input",{...i("name",{required:v("templateNameRequired")}),id:"project-name",type:"text",className:"input-field",placeholder:v("enterProjectName")}),o.name&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:"".concat(o.name.message)})]}),(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsx)("label",{htmlFor:"description",className:"label-text",children:v("description")}),(0,r.jsx)("textarea",{id:"description",...i("description",{required:v("enterTemplateDescription")}),className:"input-field resize-none",cols:4,placeholder:v("enterProjectDescription")}),o.description&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:"".concat(o.description.message)})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsxs)("label",{htmlFor:"country",className:"label-text",children:[(0,r.jsx)(R.A,{size:16}),v("country")]}),(0,r.jsx)(P.l,{id:"country",options:L,value:p||v("selectOption"),onChange:h}),o.country&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:"".concat(o.country.message)})]}),(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsxs)("label",{htmlFor:"sector",className:"label-text",children:[(0,r.jsx)(q.A,{size:16})," ",v("sector")]}),(0,r.jsx)(P.l,{id:"sector",options:Object.values(F.b),value:x&&F.b[x]?F.b[x]:v("selectOption"),onChange:e=>{b((0,V.H)(e,F.b))}}),o.sector&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:"".concat(o.sector.message)})]})]}),(0,r.jsx)("button",{type:"submit",className:"btn-primary self-end",children:l?(0,r.jsxs)("span",{className:"flex items-center gap-2",children:[v("creating"),(0,r.jsx)("div",{className:"size-4 animate-spin border-x border-neutral-100 rounded-full"})]}):v("createProject")})]})]})},Q=()=>{let{visible:e,option:t}=(0,g.d4)(e=>e.createLibraryItem),a=(0,g.wA)(),[n,i]=(0,s.useState)(!1),l=()=>{i(!0),setTimeout(()=>{a((0,K.g7)())},300)};return(0,r.jsx)(E.A,{isOpen:e&&!n,onClose:l,className:"w-3/5",children:(()=>{switch(t){case"question-block":return(0,r.jsx)("div",{children:"Question Block"});case"template":return(0,r.jsx)(G,{handleClose:l});case"upload":return(0,r.jsx)("div",{children:"Upload"});case"collection":return(0,r.jsx)("div",{children:"Collection"});default:return null}})()})};var J=a(51013),Z=a(57799),W=a(66163),Y=a(26862);let X=e=>{let{selectedRowId:t,setSelectedRowId:a}=e,s=(0,m.c3)();return[{id:"select",header:"",cell:e=>{let{row:s}=e,n=s.original.id;return(0,r.jsx)(Y.Sc,{className:"w-6 h-6 bg-neutral-100 rounded border border-neutral-400 data-[state=checked]:bg-neutral-100 data-[state=checked]:text-primary-500 data-[state=checked]:border-primary-500 cursor-pointer",checked:n===t,onCheckedChange:e=>a(e?n:null),"aria-label":"Select row"})}},{accessorKey:"name",header:s("name")},{id:"owner",accessorFn:e=>{var t,a;return null!=(a=null==(t=e.user)?void 0:t.name)?a:"unknown"},header:s("owner"),cell:e=>{let{getValue:t}=e;return t()}},{id:"questions",accessorFn:e=>{var t,a;return null!=(a=null==(t=e.libraryQuestions)?void 0:t.length.toString())?a:"0"},header:s("questions"),cell:e=>{let{getValue:t}=e;return t()}},{accessorKey:"updatedAt",header:s("lastModified")}]},$=e=>{let{showModal:t,closeModal:a,back:i,templateId:l}=e,{register:o,formState:{isSubmitting:d,errors:u,isSubmitted:m},handleSubmit:p,setValue:h,reset:x}=(0,_.mN)(),b=(0,g.wA)(),f=(0,c.useRouter)(),[y,v]=(0,s.useState)(null),[j,k]=(0,s.useState)(null);(0,s.useEffect)(()=>{o("country",{required:"Please select a country"}),o("sector",{required:"Please select a sector"})},[o]),(0,s.useEffect)(()=>{h("country",y,{shouldValidate:m}),h("sector",j,{shouldValidate:m})},[h,y,j]);let{user:S,isLoading:C}=(0,n.A)(),{data:A,isLoading:z,isError:I}=(0,N.I)({queryKey:["templates",null==S?void 0:S.id,l],queryFn:()=>(0,U.J2)({templateId:l}),enabled:!!(null==S?void 0:S.id)});(0,s.useEffect)(()=>{A&&(x({projectName:A.name,description:A.description,sector:A.sector,country:A.country}),v(A.country),k(A.sector))},[A,x]);let D=(0,H.jE)(),B=(0,T.n)({mutationFn:w.c3,onSuccess:()=>{D.invalidateQueries({queryKey:["projects"],exact:!1}),a(),f.push("/dashboard"),b((0,O.Ds)({message:"Project has been created successfully.",type:"success"}))},onError:e=>{b((0,O.Ds)({message:"Failed to create project"+e.message,type:"error"}))}}),K=async e=>{let t={templateId:l,name:e.projectName,description:e.description,sector:e.sector,country:e.country};B.mutate(t)};return C||z||I?null:(0,r.jsx)(E.A,{isOpen:t,onClose:a,className:"w-3/6",children:(0,r.jsxs)("form",{className:"flex flex-col gap-8",onSubmit:p(K),children:[(0,r.jsx)("h1",{className:"heading-text",children:"Create a new project"}),(0,r.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsxs)("label",{htmlFor:"project-name",className:"label-text",children:[(0,r.jsx)(M.A,{size:16})," Project Name"]}),(0,r.jsx)("input",{...o("projectName",{required:"Project name is required."}),id:"project-name",type:"text",className:"input-field",placeholder:"Enter a project name"}),u.projectName&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:"".concat(u.projectName.message)})]}),(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsx)("label",{htmlFor:"description",className:"label-text",children:"Description"}),(0,r.jsx)("textarea",{id:"description",...o("description",{required:"Please enter the project description"}),className:"input-field resize-none",cols:4,placeholder:"Enter the project description"}),u.description&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:"".concat(u.description.message)})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsxs)("label",{htmlFor:"country",className:"label-text",children:[(0,r.jsx)(R.A,{size:16}),"Country"]}),(0,r.jsx)(P.l,{id:"country",options:L,value:y,onChange:v}),u.country&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:"".concat(u.country.message)})]}),(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsxs)("label",{htmlFor:"sector",className:"label-text",children:[(0,r.jsx)(q.A,{size:16})," Sector"]}),(0,r.jsx)(P.l,{id:"sector",options:Object.values(F.b),value:j&&F.b[j]?F.b[j]:"Select an option",onChange:e=>{k((0,V.H)(e,F.b))}}),u.sector&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:"".concat(u.sector.message)})]})]}),(0,r.jsxs)("div",{className:"flex justify-end gap-3 mt-4",children:[(0,r.jsx)("button",{type:"button",onClick:i,className:"btn-outline",children:"Back"}),(0,r.jsx)("button",{type:"submit",className:"btn-primary",children:d?(0,r.jsxs)("span",{className:"flex items-center gap-2",children:["Creating"," ",(0,r.jsx)("div",{className:"size-4 animate-spin border-x border-neutral-100 rounded-full"})]}):"Create Project"})]})]})]})})},ee=e=>{let{isOpen:t,back:a,onClose:i}=e,{user:l}=(0,n.A)(),{data:o,isLoading:c,isError:d}=(0,N.I)({queryFn:U.QK,queryKey:["templates",null==l?void 0:l.id],enabled:!!(null==l?void 0:l.id)}),[u,p]=(0,s.useState)(null),[h,x]=(0,s.useState)(!1),[g,b]=(0,s.useState)(!1),f=(0,m.c3)(),y=X({selectedRowId:u,setSelectedRowId:p}),v=e=>{b(!0),setTimeout(()=>{x(!1),p(null),"close"===e&&i(),b(!1)},300)};return o?c?(0,r.jsx)(Z.A,{}):d?(0,r.jsx)("div",{children:f("error_loading_data")}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(E.A,{isOpen:t&&!h,onClose:()=>{p(null),i()},className:"bg-neutral-100 p-4 mobile:p-6 rounded-lg w-[95%] mobile:w-[85%] tablet:w-[75%] laptop:w-[60%] desktop:w-[50%] max-w-4xl mx-auto",children:(0,r.jsxs)("div",{className:"flex flex-col gap-6",children:[(0,r.jsx)("div",{className:"flex justify-between items-center",children:(0,r.jsx)("h2",{className:"text-xl font-semibold text-neutral-700",children:f("selectTemplate")})}),(0,r.jsx)(W.x,{data:o,columns:y}),(0,r.jsxs)("div",{className:"flex justify-end gap-3 mt-4",children:[(0,r.jsx)("button",{onClick:()=>{p(null),a()},className:"btn-outline",children:f("back")}),(0,r.jsxs)("button",{type:"button",disabled:!u,className:"btn-primary",onClick:()=>{u&&x(!0)},children:[f("next"),(0,r.jsx)(J.ZK4,{})]})]})]})}),(h||g)&&null!==u&&(0,r.jsx)($,{showModal:h&&!g,closeModal:()=>v("close"),back:()=>v("back"),templateId:u})]}):null},et=e=>{let{isOpen:t,onClose:a}=e,n=(0,g.wA)(),i=(0,m.c3)(),[l,o]=(0,s.useState)(!1),c=e=>{o(!1),"close"===e&&a()};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(E.A,{isOpen:t&&!l,onClose:a,className:"bg-neutral-100 p-4 mobile:p-6 rounded-lg w-[95%] mobile:w-[85%] tablet:w-[75%] laptop:w-[60%] desktop:w-[50%] max-w-3xl mx-auto",children:(0,r.jsxs)("div",{className:"flex flex-col gap-4 mobile:gap-6",children:[(0,r.jsx)("div",{className:"flex justify-between items-center",children:(0,r.jsx)("h2",{className:"text-lg mobile:text-xl font-semibold text-neutral-700",children:i("createProjectChooseSource")})}),(0,r.jsx)("p",{className:"text-sm mobile:text-base text-neutral-600",children:i("chooseOptionToContinue")}),(0,r.jsxs)("div",{className:"grid grid-cols-1 mobile:grid-cols-2 gap-3 mobile:gap-4",children:[(0,r.jsxs)("div",{onClick:()=>{a(),n((0,b.Gl)())},className:"bg-neutral-200 rounded-lg p-4 mobile:p-6 tablet:p-8 flex flex-col items-center justify-center gap-2 mobile:gap-3 cursor-pointer hover:bg-primary-500 hover:text-neutral-100 transition-all duration-300",children:[(0,r.jsx)(v.Yvo,{className:"w-5 h-5 mobile:w-6 mobile:h-6"}),(0,r.jsx)("span",{className:"text-sm mobile:text-base text-center",children:i("buildFromScratch")})]}),(0,r.jsxs)("div",{onClick:()=>{o(!0)},className:"bg-neutral-200 rounded-lg p-4 mobile:p-6 tablet:p-8 flex flex-col items-center justify-center gap-2 mobile:gap-3 cursor-pointer hover:bg-primary-500 hover:text-neutral-100 transition-all duration-300",children:[(0,r.jsx)(x.S1H,{className:"w-5 h-5 mobile:w-6 mobile:h-6"}),(0,r.jsx)("span",{className:"text-sm mobile:text-base text-center",children:i("useTemplate")})]})]})]})}),(0,r.jsx)(ee,{isOpen:l,onClose:()=>c("close"),back:()=>c("back")})]})};var ea=a(10351);let er=()=>{let e=(0,g.wA)(),t=(0,g.d4)(e=>e.createLibrary.visible),[a,n]=(0,s.useState)(!1),i=()=>{n(!0),setTimeout(()=>{e((0,f.l)())},300)},l=(0,c.useRouter)(),o=(0,m.c3)(),u=[{id:"question-block",title:o("questionBlock"),icon:h.Kt4,onClick:()=>{i(),l.push("/library/question-block/form-builder")}},{id:"template",title:o("templates"),icon:d.zFA,onClick:()=>{i(),e((0,K.dQ)("template"))}},{id:"upload",title:o("upload"),icon:ea.B88,onClick:()=>{i()}},{id:"collection",title:o("collections"),icon:j.M1W,onClick:()=>{i()}}];return(0,r.jsxs)(E.A,{isOpen:t&&!a,onClose:i,className:"p-6 rounded-md w-3/5",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-neutral-700 mb-4",children:o("createLibraryItem")}),(0,r.jsx)("div",{className:"grid grid-cols-2 gap-4",children:u.map(e=>(0,r.jsxs)("button",{onClick:e.onClick,className:"flex flex-col gap-2 items-center justify-center p-6 bg-neutral-200 rounded-md hover:bg-primary-500 hover:text-neutral-100 cursor-pointer transition-all duration-300",children:[(0,r.jsx)(e.icon,{size:24,className:""}),(0,r.jsx)("span",{className:"",children:e.title})]},e.id))})]})};function es(e){let{children:t}=e,[a,n]=(0,s.useState)(!1),[i,l]=(0,s.useState)(0),[o,c]=(0,s.useState)(!1),d=(0,s.useRef)(null),u=()=>n(!a);(0,s.useLayoutEffect)(()=>{let e=new ResizeObserver(()=>{d.current&&l(d.current.offsetHeight)});return d.current&&e.observe(d.current),()=>e.disconnect()},[]);let m=(0,g.d4)(e=>e.createProject.visible),h=(0,g.d4)(e=>e.createLibrary.visible),x=(0,g.d4)(e=>e.createLibraryItem.visible);return(0,r.jsxs)("div",{className:"min-h-screen flex flex-col",children:[m&&(0,r.jsx)(B,{}),h&&(0,r.jsx)(er,{}),x&&(0,r.jsx)(Q,{}),(0,r.jsx)(et,{isOpen:o,onClose:()=>c(!1)}),(0,r.jsx)(p,{toggleSidebar:u,isSidebarOpen:a,navbarRef:d}),(0,r.jsxs)("div",{className:"flex flex-1 overflow-hidden",children:[(0,r.jsx)(z,{isOpen:a,toggleSidebar:u,topOffset:i,onNewProject:()=>{c(!0)}}),(0,r.jsx)("main",{className:"flex-1 p-6 overflow-y-auto",style:{height:"calc(100vh - ".concat(i,"px)")},children:t})]})]})}},34869:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},39286:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>i,Gl:()=>s,th:()=>n});let r=(0,a(51990).Z0)({name:"createProject",initialState:{visible:!1},reducers:{showCreateProjectModal:e=>{e.visible=!0},hideCreateProjectModal:e=>{e.visible=!1}}}),{showCreateProjectModal:s,hideCreateProjectModal:n}=r.actions,i=r.reducer},50408:(e,t,a)=>{"use strict";a.d(t,{l:()=>i});var r=a(95155),s=a(66474),n=a(12115);let i=e=>{let{id:t,options:a,value:i,onChange:l}=e,[o,c]=(0,n.useState)(!1),d=(0,n.useRef)(null),u=(0,n.useRef)([]),m=(0,n.useRef)(null);(0,n.useEffect)(()=>{let e=e=>{m.current&&!m.current.contains(e.target)&&c(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]);let p=e=>{if(!o)return;let t=e.key.toLowerCase();if(t.match(/[a-z]/)){let e=a.findIndex(e=>e.toLowerCase().startsWith(t));if(-1!==e&&u.current[e]){var r;null==(r=u.current[e])||r.scrollIntoView({behavior:"auto",block:"nearest"})}}};return(0,n.useEffect)(()=>(document.addEventListener("keydown",p),()=>{document.removeEventListener("keydown",p)}),[o,a]),(0,r.jsxs)("div",{className:"relative",ref:m,children:[(0,r.jsxs)("button",{id:t,type:"button",className:"px-4 py-2 flex items-center justify-between rounded-md border border-neutral-400 focus:border-primary-500 duration-300 w-full text-left cursor-pointer",onClick:()=>{c(!o)},children:[(0,r.jsx)("span",{children:i||"Select an option"}),(0,r.jsx)(s.A,{})]}),o&&(0,r.jsx)("ul",{className:"absolute z-10 max-h-[180px] overflow-auto border border-neutral-400 rounded-md bg-neutral-100 w-full mt-1 flex flex-col",ref:d,children:a.map((e,t)=>(0,r.jsx)("li",{ref:e=>{u.current[t]=e},className:"cursor-pointer bg-neutral-100 hover:bg-neutral-200 px-4 py-2",onClick:()=>{l(e),c(!1)},children:e},t))})]})}},53999:(e,t,a)=>{"use strict";a.d(t,{Y:()=>i,cn:()=>n});var r=a(52596),s=a(39688);function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,s.QP)((0,r.$)(t))}function i(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"short";if(!e)return"";try{let a="string"==typeof e?new Date(e):e;if(isNaN(a.getTime()))return"";switch(t){case"short":return a.toLocaleDateString(void 0,{year:"numeric",month:"short",day:"numeric"});case"long":return a.toLocaleDateString(void 0,{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"});case"full":return a.toLocaleDateString(void 0,{year:"numeric",month:"long",day:"numeric",weekday:"long",hour:"2-digit",minute:"2-digit",second:"2-digit"});default:return a.toLocaleDateString()}}catch(t){return console.error("Error formatting date:",t),String(e)}}},55747:(e,t,a)=>{"use strict";a.d(t,{C:()=>c,z:()=>o});var r=a(95155),s=a(12115),n=a(54059),i=a(9428),l=a(53999);let o=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(n.bL,{className:(0,l.cn)("grid gap-2",a),...s,ref:t})});o.displayName=n.bL.displayName;let c=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(n.q7,{ref:t,className:(0,l.cn)("aspect-square h-4 w-4 rounded-full border text-gray-900 shadow focus:outline-none focus-visible:ring-1 focus-visible:ring-gray-950 disabled:cursor-not-allowed disabled:opacity-50 dark:text-gray-50 dark:focus-visible:ring-gray-300",a),...s,children:(0,r.jsx)(n.C1,{className:"flex items-center justify-center",children:(0,r.jsx)(i.A,{className:"h-2.5 w-2.5 fill-current text-current"})})})});c.displayName=n.q7.displayName},57434:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},57799:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var r=a(95155);a(12115);let s=()=>(0,r.jsx)("div",{className:"w-full flex items-center justify-center",children:(0,r.jsx)("div",{className:"size-8 rounded-full border-x-2 border-primary-500 animate-spin my-16"})})},59362:(e,t,a)=>{"use strict";a.d(t,{F0:()=>u,pe:()=>s});let{Axios:r,AxiosError:s,CanceledError:n,isCancel:i,CancelToken:l,VERSION:o,all:c,Cancel:d,isAxiosError:u,spread:m,toFormData:p,AxiosHeaders:h,HttpStatusCode:x,formToJSON:g,getAdapter:b,mergeConfig:f}=a(23464).A},62672:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>i,l:()=>n,yg:()=>s});let r=(0,a(51990).Z0)({name:"createLibraryItem",initialState:{visible:!1},reducers:{showCreateLibraryModal:e=>{e.visible=!0},hideCreateLibraryModal:e=>{e.visible=!1}}}),{showCreateLibraryModal:s,hideCreateLibraryModal:n}=r.actions,i=r.reducer},64368:(e,t,a)=>{"use strict";a.d(t,{H:()=>r});let r=(e,t)=>{let a=Object.entries(t).find(t=>{let[a,r]=t;return r===e});return a?a[0]:null}},66163:(e,t,a)=>{"use strict";a.d(t,{x:()=>m});var r=a(95155),s=a(12115),n=a(36268),i=a(11032),l=a(88524),o=a(67133),c=a(97168),d=a(29911),u=a(17652);let m=e=>{let{columns:t,data:a,globalFilter:m,setGlobalFilter:p,onTableInit:h,columnVisibility:x,setColumnVisibility:g,onRowSelectionChange:b,rowSelection:f,onRowClick:y}=e,[v,j]=s.useState({pageIndex:0,pageSize:8}),[N,w]=s.useState([]),[k,S]=s.useState([]),C=(0,u.c3)(),[A,z]=s.useState({}),[E,_]=s.useState({}),P=void 0!==f?f:E,L=(0,n.N4)({data:a,columns:t,onPaginationChange:j,onColumnFiltersChange:w,onGlobalFilterChange:p,onColumnVisibilityChange:null!=g?g:z,onRowSelectionChange:e=>{let t="function"==typeof e?e(P):e;void 0===f&&_(t),b&&b(t)},onSortingChange:S,getCoreRowModel:(0,i.HT)(),getFilteredRowModel:(0,i.hM)(),getPaginationRowModel:(0,i.kW)(),getSortedRowModel:(0,i.h5)(),enableRowSelection:!0,enableSorting:!0,enableSortingRemoval:!0,state:{pagination:v,columnFilters:N,globalFilter:m,columnVisibility:null!=x?x:A,rowSelection:P,sorting:k}});return s.useEffect(()=>{h&&h(L)},[h,L]),s.useEffect(()=>{void 0!==f&&L.setRowSelection(f)},[f,L]),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"rounded-md border border-neutral-400 overflow-hidden",children:(0,r.jsxs)(l.XI,{className:"min-w-full",children:[(0,r.jsx)(l.A0,{className:"h-20",children:L.getHeaderGroups().map(e=>(0,r.jsx)(l.Hj,{className:"text-sm border-neutral-400",children:e.headers.map(t=>(0,r.jsxs)(l.nd,{className:"py-1 px-6 text-left bg-primary-500 text-neutral-100 font-semibold ".concat(0===t.index?"w-12 py-3 px-6":""),style:{cursor:t.column.getCanSort()?"pointer":"default"},children:[(0,r.jsx)("div",{onClick:t.column.getToggleSortingHandler(),children:(0,r.jsx)("div",{children:t.isPlaceholder?null:(0,n.Kv)(t.column.columnDef.header,t.getContext())})}),"validation"===t.column.id?(0,r.jsxs)(o.rI,{children:[(0,r.jsx)(o.ty,{asChild:!0,children:(0,r.jsxs)(c.$,{variant:"outline",className:"h-8 my-1 text-neutral-700 cursor-pointer",children:[C("filter"),(0,r.jsx)(d.Vr3,{})]})}),(0,r.jsxs)(o.SQ,{className:"bg-neutral-100 border border-neutral-200 shadow-md cursor-pointer",children:[(0,r.jsx)(o._2,{className:"cursor-pointer hover:bg-neutral-300",onClick:()=>t.column.setFilterValue("Valid"),children:C("valid")}),(0,r.jsx)(o._2,{className:"cursor-pointer hover:bg-neutral-300",onClick:()=>t.column.setFilterValue("Not Valid"),children:C("notValid")}),(0,r.jsx)(o._2,{className:"cursor-pointer hover:bg-neutral-300",onClick:()=>t.column.setFilterValue(""),children:C("clearFilter")})]})]}):t.column.getCanFilter()&&(0,r.jsx)("input",{placeholder:C("search"),value:t.column.getFilterValue()||"",onChange:e=>t.column.setFilterValue(e.target.value),className:"input-field placeholder:font-semibold max-w-48 text-sm my-1 px-2 py-1 bg-neutral-100 text-neutral-700  border-none rounded-md"})]},"".concat(e.id,"_").concat(t.id,"_").concat(t.index)))},e.id))}),(0,r.jsx)(l.BF,{children:L.getPaginationRowModel().rows.length?L.getPaginationRowModel().rows.map(e=>(0,r.jsx)(l.Hj,{"data-state":e.getIsSelected()&&"selected",className:"hover:bg-neutral-50 text-sm border-neutral-400",onClick:()=>null==y?void 0:y(e.original),children:e.getVisibleCells().map((t,a)=>(0,r.jsx)(l.nA,{className:"py-4 px-6 max-w-48  ".concat(0===a?"py-3 px-6":""," text-neutral-700 "),children:(0,n.Kv)(t.column.columnDef.cell,t.getContext())},"".concat(e.id,"_").concat(t.id,"_").concat(a)))},e.id)):(0,r.jsx)(l.Hj,{children:(0,r.jsx)(l.nA,{colSpan:t.length,className:"h-24 text-center",children:C("noResults")})})})]})}),(0,r.jsxs)("div",{className:"flex items-center justify-end space-x-2 py-4",children:[(0,r.jsxs)("div",{className:"flex-1 text-sm text-muted-foreground",children:[L.getFilteredSelectedRowModel().rows.length," of"," ",L.getFilteredRowModel().rows.length," ",C("rowSelected")]}),a.length>v.pageSize&&(0,r.jsxs)("div",{className:"flex items-center justify-end space-x-2 py-4",children:[(0,r.jsx)("button",{className:"btn-primary",onClick:()=>L.previousPage(),disabled:!L.getCanPreviousPage(),children:C("previous")}),(0,r.jsx)("button",{className:"btn-primary",onClick:()=>L.nextPage(),disabled:!L.getCanNextPage(),children:C("next")})]})]})]})}},67133:(e,t,a)=>{"use strict";a.d(t,{SQ:()=>c,_2:()=>d,hO:()=>u,rI:()=>l,ty:()=>o});var r=a(95155);a(12115);var s=a(48698),n=a(5196),i=a(53999);function l(e){let{...t}=e;return(0,r.jsx)(s.bL,{"data-slot":"dropdown-menu",...t})}function o(e){let{...t}=e;return(0,r.jsx)(s.l9,{"data-slot":"dropdown-menu-trigger",...t})}function c(e){let{className:t,sideOffset:a=4,...n}=e;return(0,r.jsx)(s.ZL,{children:(0,r.jsx)(s.UC,{"data-slot":"dropdown-menu-content",sideOffset:a,className:(0,i.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",t),...n})})}function d(e){let{className:t,inset:a,variant:n="default",...l}=e;return(0,r.jsx)(s.q7,{"data-slot":"dropdown-menu-item","data-inset":a,"data-variant":n,className:(0,i.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...l})}function u(e){let{className:t,children:a,checked:l,...o}=e;return(0,r.jsxs)(s.H_,{"data-slot":"dropdown-menu-checkbox-item",className:(0,i.cn)("focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),checked:l,...o,children:[(0,r.jsx)("span",{className:"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center",children:(0,r.jsx)(s.VF,{children:(0,r.jsx)(n.A,{className:"size-4"})})}),a]})}},71402:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>i,Ds:()=>s,_b:()=>n});let r=(0,a(51990).Z0)({name:"notification",initialState:{message:"",type:"",visible:!1},reducers:{showNotification:(e,t)=>{e.message=t.payload.message,e.type=t.payload.type,e.visible=!0},hideNotification:e=>{e.message="",e.type="",e.visible=!1}}}),{showNotification:s,hideNotification:n}=r.actions,i=r.reducer},74567:e=>{"use strict";e.exports=JSON.parse('["Afghanistan","Albania","Algeria","Andorra","Angola","Argentina","Armenia","Australia","Austria","Azerbaijan","Bahamas","Bahrain","Bangladesh","Barbados","Belarus","Belgium","Belize","Benin","Bhutan","Bolivia","Bosnia and Herzegovina","Botswana","Brazil","Brunei","Bulgaria","Burkina Faso","Burundi","Cabo Verde","Cambodia","Cameroon","Canada","Central African Republic","Chad","Chile","China","Colombia","Comoros","Congo (Congo-Brazzaville)","Costa Rica","Croatia","Cuba","Cyprus","Czech Republic","Denmark","Djibouti","Dominica","Dominican Republic","Ecuador","Egypt","El Salvador","Equatorial Guinea","Eritrea","Estonia","Eswatini","Ethiopia","Fiji","Finland","France","Gabon","Gambia","Georgia","Germany","Ghana","Greece","Grenada","Guatemala","Guinea","Guinea-Bissau","Guyana","Haiti","Honduras","Hungary","Iceland","India","Indonesia","Iran","Iraq","Ireland","Israel","Italy","Jamaica","Japan","Jordan","Kazakhstan","Kenya","Kiribati","Kuwait","Kyrgyzstan","Laos","Latvia","Lebanon","Lesotho","Liberia","Libya","Liechtenstein","Lithuania","Luxembourg","Madagascar","Malawi","Malaysia","Maldives","Mali","Malta","Marshall Islands","Mauritania","Mauritius","Mexico","Micronesia","Moldova","Monaco","Mongolia","Montenegro","Morocco","Mozambique","Myanmar","Namibia","Nauru","Nepal","Netherlands","New Zealand","Nicaragua","Niger","Nigeria","North Korea","North Macedonia","Norway","Oman","Pakistan","Palau","Palestine","Panama","Papua New Guinea","Paraguay","Peru","Philippines","Poland","Portugal","Qatar","Romania","Russia","Rwanda","Saint Kitts and Nevis","Saint Lucia","Saint Vincent and the Grenadines","Samoa","San Marino","Sao Tome and Principe","Saudi Arabia","Senegal","Serbia","Seychelles","Sierra Leone","Singapore","Slovakia","Slovenia","Solomon Islands","Somalia","South Africa","South Korea","South Sudan","Spain","Sri Lanka","Sudan","Suriname","Sweden","Switzerland","Syria","Taiwan","Tajikistan","Tanzania","Thailand","Timor-Leste","Togo","Tonga","Trinidad and Tobago","Tunisia","Turkey","Turkmenistan","Tuvalu","Uganda","Ukraine","United Arab Emirates","United Kingdom","United States","Uruguay","Uzbekistan","Vanuatu","Vatican City","Venezuela","Vietnam","Yemen","Zambia","Zimbabwe"]')},75173:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>i,dQ:()=>s,g7:()=>n});let r=(0,a(51990).Z0)({initialState:{visible:!1,option:""},name:"createLibraryItem",reducers:{showCreateLibraryItemModal:(e,t)=>{e.visible=!0,e.option=t.payload},hideCreateLibraryItemModal:e=>{e.visible=!1,e.option=""}}}),{showCreateLibraryItemModal:s,hideCreateLibraryItemModal:n}=r.actions,i=r.reducer},77361:(e,t,a)=>{"use strict";a.d(t,{D_:()=>u,Im:()=>c,Oo:()=>m,c3:()=>n,kf:()=>s,lj:()=>h,or:()=>o,pf:()=>d,vj:()=>i,wI:()=>p,xx:()=>l});var r=a(25784);let s=async e=>{let{projectId:t}=e,{data:a}=await r.A.get("/projects/".concat(t));return a.project},n=async e=>{let{data:t}=await r.A.post("/projects/from-template",e);return t},i=async()=>{try{let{data:e}=await r.A.get("/projects");return e.projects}catch(e){throw console.error("Error fetching projects:",e),e}},l=async e=>{let{data:t}=await r.A.delete("/projects/delete/".concat(e));return t},o=async e=>{try{let{data:t}=await r.A.delete("/projects/delete-multiple",{data:{projectIds:e}});return t}catch(e){throw console.error("Error deleting multiple projects:",e),e}},c=async e=>{try{let{data:t}=await r.A.patch("/projects/change-status/".concat(e),{status:"archived"});return t}catch(e){throw console.error("Error archiving project:",e),e}},d=async function(e){arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{let{data:t}=await r.A.patch("/projects/change-status/".concat(e),{status:"deployed"});return t}catch(e){throw console.error("Error deploying project:",e),e}},u=async e=>{try{let{data:t}=await r.A.patch("/projects/update-many-status",{projectIds:e,status:"archived"});return t}catch(e){throw console.error("Error archiving multiple projects:",e),e}},m=async e=>{try{let{data:t}=await r.A.post("/users/check-email",{email:e});return t}catch(e){var t,a,s,n,i,l;throw Error("object"==typeof(null==(a=e.response)||null==(t=a.data)?void 0:t.message)?JSON.stringify(null==(n=e.response)||null==(s=n.data)?void 0:s.message):(null==(l=e.response)||null==(i=l.data)?void 0:i.message)||e.message||"Failed to check user")}},p=async e=>{let{projectId:t,email:a,permissions:s}=e;try{let e=await m(a);if(!e||!e.success)throw Error((null==e?void 0:e.message)||"User not found");let{data:n}=await r.A.post("/project-users",{userId:e.user.id,projectId:t,permission:s});return n}catch(e){var n,i,l,o,c,d;throw console.error("Error adding user to project:",e),Error("object"==typeof(null==(i=e.response)||null==(n=i.data)?void 0:n.message)?JSON.stringify(null==(o=e.response)||null==(l=o.data)?void 0:l.message):(null==(d=e.response)||null==(c=d.data)?void 0:c.message)||e.message||"Failed to add user")}},h=async e=>{try{let{data:t}=await r.A.post("/answers/multiple",e);return t}catch(e){throw console.error("Error creating answer submission:",e),e}}},82714:(e,t,a)=>{"use strict";a.d(t,{J:()=>l});var r=a(95155),s=a(12115),n=a(40968),i=a(53999);let l=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(n.b,{ref:t,className:(0,i.cn)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",a),...s})});l.displayName=n.b.displayName},88524:(e,t,a)=>{"use strict";a.d(t,{A0:()=>i,BF:()=>l,Hj:()=>o,XI:()=>n,nA:()=>d,nd:()=>c});var r=a(95155);a(12115);var s=a(53999);function n(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,r.jsx)("table",{"data-slot":"table",className:(0,s.cn)("w-full caption-bottom text-sm",t),...a})})}function i(e){let{className:t,...a}=e;return(0,r.jsx)("thead",{"data-slot":"table-header",className:(0,s.cn)("[&_tr]:border-b",t),...a})}function l(e){let{className:t,...a}=e;return(0,r.jsx)("tbody",{"data-slot":"table-body",className:(0,s.cn)("[&_tr:last-child]:border-0",t),...a})}function o(e){let{className:t,...a}=e;return(0,r.jsx)("tr",{"data-slot":"table-row",className:(0,s.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",t),...a})}function c(e){let{className:t,...a}=e;return(0,r.jsx)("th",{"data-slot":"table-head",className:(0,s.cn)("text-foreground h-10 px-2 text-left align-middle font-medium neutral-100space-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...a})}function d(e){let{className:t,...a}=e;return(0,r.jsx)("td",{"data-slot":"table-cell",className:(0,s.cn)("p-2 align-middle neutral-100space-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...a})}},88570:(e,t,a)=>{"use strict";a.d(t,{D:()=>l,l:()=>i});var r=a(41050);let s=a(49509).env.SALT||"rushan-salt",n=new r.A(s,12),i=e=>n.encode(e),l=e=>{let t=n.decode(e)[0];return"bigint"==typeof t?t<Number.MAX_SAFE_INTEGER?Number(t):null:"number"==typeof t?t:null}},89852:(e,t,a)=>{"use strict";a.d(t,{p:()=>n});var r=a(95155);a(12115);var s=a(53999);function n(e){let{className:t,type:a,...n}=e;return(0,r.jsx)("input",{type:a,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-primary-500 focus-visible:ring-[1px]","focus-visible:outline-none","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n})}},93807:(e,t,a)=>{Promise.resolve().then(a.bind(a,31917))},94974:(e,t,a)=>{"use strict";a.d(t,{I7:()=>l,J2:()=>s,QK:()=>i,Xu:()=>n,nh:()=>o});var r=a(25784);let s=async e=>{let{templateId:t}=e,{data:a}=await r.A.get("/libraries/".concat(t));return a.template},n=async e=>{let{data:t}=await r.A.post("/libraries",e);return t},i=async()=>{let{data:e}=await r.A.get("/libraries");return e.templates},l=async e=>{let{data:t}=await r.A.delete("/libraries/".concat(e));return t},o=async e=>{let{templateIds:t}=e;return null}},95139:(e,t,a)=>{"use strict";a.d(t,{S:()=>l});var r=a(95155);a(12115);var s=a(76981),n=a(5196),i=a(53999);function l(e){let{className:t,...a}=e;return(0,r.jsx)(s.bL,{"data-slot":"checkbox",className:(0,i.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",t),...a,children:(0,r.jsx)(s.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,r.jsx)(n.A,{className:"size-3.5"})})})}},97168:(e,t,a)=>{"use strict";a.d(t,{$:()=>o});var r=a(95155);a(12115);var s=a(99708),n=a(74466),i=a(53999);let l=(0,n.F)("inline-flex items-center justify-center gap-2 neutral-100space-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-neutral-100 shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:a,size:n,asChild:o=!1,...c}=e,d=o?s.DX:"button";return(0,r.jsx)(d,{"data-slot":"button",className:(0,i.cn)(l({variant:a,size:n,className:t})),...c})}},97381:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>o,Le:()=>i,jB:()=>l,tQ:()=>s,x9:()=>n});let r=(0,a(51990).Z0)({name:"auth",initialState:{status:"loading",user:null,error:null},reducers:{setAuthenticatedUser:(e,t)=>{e.status="authenticated",e.user=t.payload,e.error=null},setUnauthenticated:e=>{e.status="unauthenticated",e.user=null,e.error=null},setAuthLoading:e=>{e.status="loading"},setAuthError:(e,t)=>{e.status="unauthenticated",e.error=t.payload,e.user=null}}}),{setAuthenticatedUser:s,setUnauthenticated:n,setAuthLoading:i,setAuthError:l}=r.actions,o=r.reducer},99474:(e,t,a)=>{"use strict";a.d(t,{T:()=>i});var r=a(95155),s=a(12115),n=a(53999);let i=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("textarea",{className:(0,n.cn)("flex min-h-[80px] w-full rounded-md border border-gray-200 bg-neutral-100 px-3 py-2 text-sm shadow-sm placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-700 dark:bg-gray-900 dark:placeholder:text-gray-500",a),ref:t,...s})});i.displayName="Textarea"}},e=>{var t=t=>e(e.s=t);e.O(0,[2150,6711,9204,8087,5897,5479,512,844,6453,635,1111,6967,9373,4601,1380,4277,6874,556,3481,2854,6539,6268,919,4695,8441,1684,7358],()=>t(93807)),_N_E=e.O()}]);