"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getProjectReport = exports.hashids = exports.salt = void 0;
const ApiResponse_1 = require("../utils/ApiResponse");
const reportRepository_1 = __importDefault(require("../repositories/reportRepository"));
const formSubmissionRepository_1 = __importDefault(require("../repositories/formSubmissionRepository"));
const hashids_1 = __importDefault(require("hashids"));
// For easier testing, export these
exports.salt = process.env.SALT || "rushan-salt";
exports.hashids = new hashids_1.default(exports.salt, 12);
// Generate report from form submissions for a project
const getProjectReport = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
        const hashedProjectId = req.params.projectId;
        const reportType = req.query.type || "default";
        const startDate = req.query.startDate
            ? new Date(req.query.startDate)
            : undefined;
        const endDate = req.query.endDate
            ? new Date(req.query.endDate)
            : undefined;
        if (!userId) {
            res.status(401).json(new ApiResponse_1.ApiResponse(401, null, "Unauthorized"));
            return;
        }
        // Decode the hashed project ID
        const decoded = exports.hashids.decode(hashedProjectId);
        const decodedProjectId = decoded && decoded.length > 0 ? decoded[0] : undefined;
        if (!decodedProjectId || typeof decodedProjectId !== "number") {
            res.status(400).json(new ApiResponse_1.ApiResponse(400, null, "Invalid project ID"));
            return;
        }
        try {
            // Check if user has access to project
            const hasAccess = yield formSubmissionRepository_1.default.isProjectAccessible(userId, decodedProjectId);
            if (!hasAccess) {
                res
                    .status(403)
                    .json(new ApiResponse_1.ApiResponse(403, null, "You don't have access to this project"));
                return;
            }
            // Generate report using repository
            const report = yield reportRepository_1.default.generateReport(decodedProjectId, {
                startDate,
                endDate,
                type: reportType,
            });
            res
                .status(200)
                .json(new ApiResponse_1.ApiResponse(200, report, "Report generated successfully"));
        }
        catch (innerError) {
            console.error("Error in report generation:", innerError);
            res
                .status(500)
                .json(new ApiResponse_1.ApiResponse(500, null, `Error generating report: ${innerError instanceof Error ? innerError.message : "Unknown error"}`));
        }
    }
    catch (error) {
        console.error("Error generating report:", error);
        res
            .status(500)
            .json(new ApiResponse_1.ApiResponse(500, null, `Error generating report: ${error instanceof Error ? error.message : "Unknown error"}`));
    }
});
exports.getProjectReport = getProjectReport;
