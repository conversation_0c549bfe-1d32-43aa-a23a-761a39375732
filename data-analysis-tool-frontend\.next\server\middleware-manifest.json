{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_2c2f7f9b._.js", "server/edge/chunks/[root-of-the-server]__de110ef0._.js", "server/edge/chunks/edge-wrapper_9e9e701a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|images|fonts|api).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|images|fonts|api).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "vxTVhA8XalJO92PbSKJ7lk2zRCVwDY/NQi5gp+1+9f4=", "__NEXT_PREVIEW_MODE_ID": "f51aa4c06bb8dfcc4342fa58a5b5bb6d", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "255d1856ee4bf4a74116e659a2c7dbb1990be8ac3fc7d72785d4024806e892bb", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "99dfba36e59145f205b293d4c32f44d0f8ad8106ba7467cf1d31005467327417"}}}, "sortedMiddleware": ["/"], "functions": {}}