{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|images|fonts|api).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|images|fonts|api).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "iuZ2ZfXkRDVTMNx6_YPFd", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "GuF2MlmWRbtYDW5Qxl3TCpuZqYQD4Z2+pMCTduJlTxc=", "__NEXT_PREVIEW_MODE_ID": "fc791f22e1158d02099cfc67e6d4d6db", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "2df351f79bcab2c2a883bdb25443689800e90702dd3bb8d60769788fcc4964bb", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "949d5220096d09641598698e3841c5c2cfbd31c2c9e2739fb00c8f27d2176728"}}}, "functions": {}, "sortedMiddleware": ["/"]}