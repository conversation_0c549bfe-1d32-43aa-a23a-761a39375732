{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|images|fonts|api).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|images|fonts|api).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "N6vgz8VplbKpoqPfQxfd5", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "GuF2MlmWRbtYDW5Qxl3TCpuZqYQD4Z2+pMCTduJlTxc=", "__NEXT_PREVIEW_MODE_ID": "de3d3411709b36445366ce07ff37b32c", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "0808b0477f63210d39c689d1ab404e9de2f76107a7449ac6ca7c29e1f0852bf0", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "58f53a51827aff1a24a47a1a15517a38a1e574c3de6835477151033d55f016da"}}}, "functions": {}, "sortedMiddleware": ["/"]}