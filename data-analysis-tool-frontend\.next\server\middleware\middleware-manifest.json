{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_2c2f7f9b._.js", "server/edge/chunks/[root-of-the-server]__de110ef0._.js", "server/edge/chunks/edge-wrapper_9e9e701a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|images|fonts|api).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|images|fonts|api).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "GuF2MlmWRbtYDW5Qxl3TCpuZqYQD4Z2+pMCTduJlTxc=", "__NEXT_PREVIEW_MODE_ID": "34eaea27b3973ded81af96c5828947d0", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "fff22a1d6b1be6da4e68392a0938cbaeda87c7df43365a0f321ee663c248f5f8", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "faad8167ec84f8045d621a7d91b9096695566ad3a50d74b0412bac62db9e89da"}}}, "instrumentation": null, "functions": {}}