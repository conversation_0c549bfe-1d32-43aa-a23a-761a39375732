{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_2c2f7f9b._.js", "server/edge/chunks/[root-of-the-server]__de110ef0._.js", "server/edge/chunks/edge-wrapper_9e9e701a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|images|fonts|api).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|images|fonts|api).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "GuF2MlmWRbtYDW5Qxl3TCpuZqYQD4Z2+pMCTduJlTxc=", "__NEXT_PREVIEW_MODE_ID": "376660189bfb2b857d6a65ae20a08eeb", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "b7317bb055fdd8c0ccf5f80e98610c6c1b59db9e4ee30c80d3c47a8ec18157a2", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "78a76d79f5a45b914c220762f0876638ee0f997fe0ff052921022d94f5c7e047"}}}, "instrumentation": null, "functions": {}}