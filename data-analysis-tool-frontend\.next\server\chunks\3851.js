"use strict";exports.id=3851,exports.ids=[3851],exports.modules={43:(e,t,r)=>{r.d(t,{jH:()=>s});var n=r(43210);r(60687);var o=n.createContext(void 0);function s(e){let t=n.useContext(o);return e||t||"ltr"}},8730:(e,t,r)=>{r.d(t,{DX:()=>a,TL:()=>i});var n=r(43210),o=r(98599),s=r(60687);function i(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...s}=e;if(n.isValidElement(r)){var i;let e,a,l=(i=r,(a=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(a=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),c=function(e,t){let r={...t};for(let n in t){let o=e[n],s=t[n];/^on[A-Z]/.test(n)?o&&s?r[n]=(...e)=>{s(...e),o(...e)}:o&&(r[n]=o):"style"===n?r[n]={...o,...s}:"className"===n&&(r[n]=[o,s].filter(Boolean).join(" "))}return{...e,...r}}(s,r.props);return r.type!==n.Fragment&&(c.ref=t?(0,o.t)(t,l):l),n.cloneElement(r,c)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:o,...i}=e,a=n.Children.toArray(o),l=a.find(c);if(l){let e=l.props.children,o=a.map(t=>t!==l?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,s.jsx)(t,{...i,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,o):null})}return(0,s.jsx)(t,{...i,ref:r,children:o})});return r.displayName=`${e}.Slot`,r}var a=i("Slot"),l=Symbol("radix.slottable");function c(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===l}},9510:(e,t,r)=>{r.d(t,{N:()=>l});var n=r(43210),o=r(11273),s=r(98599),i=r(8730),a=r(60687);function l(e){let t=e+"CollectionProvider",[r,l]=(0,o.A)(t),[c,u]=r(t,{collectionRef:{current:null},itemMap:new Map}),d=e=>{let{scope:t,children:r}=e,o=n.useRef(null),s=n.useRef(new Map).current;return(0,a.jsx)(c,{scope:t,itemMap:s,collectionRef:o,children:r})};d.displayName=t;let h=e+"CollectionSlot",p=(0,i.TL)(h),f=n.forwardRef((e,t)=>{let{scope:r,children:n}=e,o=u(h,r),i=(0,s.s)(t,o.collectionRef);return(0,a.jsx)(p,{ref:i,children:n})});f.displayName=h;let m=e+"CollectionItemSlot",b="data-radix-collection-item",g=(0,i.TL)(m),y=n.forwardRef((e,t)=>{let{scope:r,children:o,...i}=e,l=n.useRef(null),c=(0,s.s)(t,l),d=u(m,r);return n.useEffect(()=>(d.itemMap.set(l,{ref:l,...i}),()=>void d.itemMap.delete(l))),(0,a.jsx)(g,{...{[b]:""},ref:c,children:o})});return y.displayName=m,[{Provider:d,Slot:f,ItemSlot:y},function(t){let r=u(e+"CollectionConsumer",t);return n.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${b}]`));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},l]}var c=new WeakMap;function u(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let r=function(e,t){let r=e.length,n=d(t),o=n>=0?n:r+n;return o<0||o>=r?-1:o}(e,t);return -1===r?void 0:e[r]}function d(e){return e!=e||0===e?0:Math.trunc(e)}},11273:(e,t,r)=>{r.d(t,{A:()=>s});var n=r(43210),o=r(60687);function s(e,t=[]){let r=[],i=()=>{let t=r.map(e=>n.createContext(e));return function(r){let o=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:o}}),[r,o])}};return i.scopeName=e,[function(t,s){let i=n.createContext(s),a=r.length;r=[...r,s];let l=t=>{let{scope:r,children:s,...l}=t,c=r?.[e]?.[a]||i,u=n.useMemo(()=>l,Object.values(l));return(0,o.jsx)(c.Provider,{value:u,children:s})};return l.displayName=t+"Provider",[l,function(r,o){let l=o?.[e]?.[a]||i,c=n.useContext(l);if(c)return c;if(void 0!==s)return s;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=r.reduce((t,{useScope:r,scopeName:n})=>{let o=r(e)[`__scope${n}`];return{...t,...o}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return r.scopeName=t.scopeName,r}(i,...t)]}},13495:(e,t,r)=>{r.d(t,{c:()=>o});var n=r(43210);function o(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}},13964:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},14163:(e,t,r)=>{r.d(t,{hO:()=>l,sG:()=>a});var n=r(43210),o=r(51215),s=r(8730),i=r(60687),a=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,s.TL)(`Primitive.${t}`),o=n.forwardRef((e,n)=>{let{asChild:o,...s}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(o?r:t,{...s,ref:n})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function l(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},14555:(e,t,r)=>{r.d(t,{C1:()=>$,bL:()=>D,q7:()=>L});var n=r(43210),o=r(70569),s=r(98599),i=r(11273),a=r(14163),l=r(72942),c=r(65551),u=r(43),d=r(18853),h=r(83721),p=r(46059),f=r(60687),m="Radio",[b,g]=(0,i.A)(m),[y,v]=b(m),w=n.forwardRef((e,t)=>{let{__scopeRadio:r,name:i,checked:l=!1,required:c,disabled:u,value:d="on",onCheck:h,form:p,...m}=e,[b,g]=n.useState(null),v=(0,s.s)(t,e=>g(e)),w=n.useRef(!1),x=!b||p||!!b.closest("form");return(0,f.jsxs)(y,{scope:r,checked:l,disabled:u,children:[(0,f.jsx)(a.sG.button,{type:"button",role:"radio","aria-checked":l,"data-state":E(l),"data-disabled":u?"":void 0,disabled:u,value:d,...m,ref:v,onClick:(0,o.m)(e.onClick,e=>{l||h?.(),x&&(w.current=e.isPropagationStopped(),w.current||e.stopPropagation())})}),x&&(0,f.jsx)(R,{control:b,bubbles:!w.current,name:i,value:d,checked:l,required:c,disabled:u,form:p,style:{transform:"translateX(-100%)"}})]})});w.displayName=m;var x="RadioIndicator",k=n.forwardRef((e,t)=>{let{__scopeRadio:r,forceMount:n,...o}=e,s=v(x,r);return(0,f.jsx)(p.C,{present:n||s.checked,children:(0,f.jsx)(a.sG.span,{"data-state":E(s.checked),"data-disabled":s.disabled?"":void 0,...o,ref:t})})});k.displayName=x;var R=n.forwardRef(({__scopeRadio:e,control:t,checked:r,bubbles:o=!0,...i},l)=>{let c=n.useRef(null),u=(0,s.s)(c,l),p=(0,h.Z)(r),m=(0,d.X)(t);return n.useEffect(()=>{let e=c.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(p!==r&&t){let n=new Event("click",{bubbles:o});t.call(e,r),e.dispatchEvent(n)}},[p,r,o]),(0,f.jsx)(a.sG.input,{type:"radio","aria-hidden":!0,defaultChecked:r,...i,tabIndex:-1,ref:u,style:{...i.style,...m,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function E(e){return e?"checked":"unchecked"}R.displayName="RadioBubbleInput";var I=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],C="RadioGroup",[S,O]=(0,i.A)(C,[l.RG,g]),N=(0,l.RG)(),j=g(),[M,T]=S(C),A=n.forwardRef((e,t)=>{let{__scopeRadioGroup:r,name:n,defaultValue:o,value:s,required:i=!1,disabled:d=!1,orientation:h,dir:p,loop:m=!0,onValueChange:b,...g}=e,y=N(r),v=(0,u.jH)(p),[w,x]=(0,c.i)({prop:s,defaultProp:o??"",onChange:b,caller:C});return(0,f.jsx)(M,{scope:r,name:n,required:i,disabled:d,value:w,onValueChange:x,children:(0,f.jsx)(l.bL,{asChild:!0,...y,orientation:h,dir:v,loop:m,children:(0,f.jsx)(a.sG.div,{role:"radiogroup","aria-required":i,"aria-orientation":h,"data-disabled":d?"":void 0,dir:v,...g,ref:t})})})});A.displayName=C;var z="RadioGroupItem",Q=n.forwardRef((e,t)=>{let{__scopeRadioGroup:r,disabled:i,...a}=e,c=T(z,r),u=c.disabled||i,d=N(r),h=j(r),p=n.useRef(null),m=(0,s.s)(t,p),b=c.value===a.value,g=n.useRef(!1);return n.useEffect(()=>{let e=e=>{I.includes(e.key)&&(g.current=!0)},t=()=>g.current=!1;return document.addEventListener("keydown",e),document.addEventListener("keyup",t),()=>{document.removeEventListener("keydown",e),document.removeEventListener("keyup",t)}},[]),(0,f.jsx)(l.q7,{asChild:!0,...d,focusable:!u,active:b,children:(0,f.jsx)(w,{disabled:u,required:c.required,checked:b,...h,...a,name:c.name,ref:m,onCheck:()=>c.onValueChange(a.value),onKeyDown:(0,o.m)(e=>{"Enter"===e.key&&e.preventDefault()}),onFocus:(0,o.m)(a.onFocus,()=>{g.current&&p.current?.click()})})})});Q.displayName=z;var P=n.forwardRef((e,t)=>{let{__scopeRadioGroup:r,...n}=e,o=j(r);return(0,f.jsx)(k,{...o,...n,ref:t})});P.displayName="RadioGroupIndicator";var D=A,L=Q,$=P},18853:(e,t,r)=>{r.d(t,{X:()=>s});var n=r(43210),o=r(66156);function s(e){let[t,r]=n.useState(void 0);return(0,o.N)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,o;if(!Array.isArray(t)||!t.length)return;let s=t[0];if("borderBoxSize"in s){let e=s.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,o=t.blockSize}else n=e.offsetWidth,o=e.offsetHeight;r({width:n,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}},29494:(e,t,r)=>{r.d(t,{I:()=>O});var n=r(39850),o=r(33465),s=r(61489),i=r(35536),a=r(73458),l=r(31212),c=class extends i.Q{constructor(e,t){super(),this.options=t,this.#e=e,this.#t=null,this.#r=(0,a.T)(),this.options.experimental_prefetchInRender||this.#r.reject(Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(t)}#e;#n=void 0;#o=void 0;#s=void 0;#i;#a;#r;#t;#l;#c;#u;#d;#h;#p;#f=new Set;bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&(this.#n.addObserver(this),u(this.#n,this.options)?this.#m():this.updateResult(),this.#b())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return d(this.#n,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return d(this.#n,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.#g(),this.#y(),this.#n.removeObserver(this)}setOptions(e){let t=this.options,r=this.#n;if(this.options=this.#e.defaultQueryOptions(e),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled&&"function"!=typeof this.options.enabled&&"boolean"!=typeof(0,l.Eh)(this.options.enabled,this.#n))throw Error("Expected enabled to be a boolean or a callback that returns a boolean");this.#v(),this.#n.setOptions(this.options),t._defaulted&&!(0,l.f8)(this.options,t)&&this.#e.getQueryCache().notify({type:"observerOptionsUpdated",query:this.#n,observer:this});let n=this.hasListeners();n&&h(this.#n,r,this.options,t)&&this.#m(),this.updateResult(),n&&(this.#n!==r||(0,l.Eh)(this.options.enabled,this.#n)!==(0,l.Eh)(t.enabled,this.#n)||(0,l.d2)(this.options.staleTime,this.#n)!==(0,l.d2)(t.staleTime,this.#n))&&this.#w();let o=this.#x();n&&(this.#n!==r||(0,l.Eh)(this.options.enabled,this.#n)!==(0,l.Eh)(t.enabled,this.#n)||o!==this.#p)&&this.#k(o)}getOptimisticResult(e){var t,r;let n=this.#e.getQueryCache().build(this.#e,e),o=this.createResult(n,e);return t=this,r=o,(0,l.f8)(t.getCurrentResult(),r)||(this.#s=o,this.#a=this.options,this.#i=this.#n.state),o}getCurrentResult(){return this.#s}trackResult(e,t){let r={};return Object.keys(e).forEach(n=>{Object.defineProperty(r,n,{configurable:!1,enumerable:!0,get:()=>(this.trackProp(n),t?.(n),e[n])})}),r}trackProp(e){this.#f.add(e)}getCurrentQuery(){return this.#n}refetch({...e}={}){return this.fetch({...e})}fetchOptimistic(e){let t=this.#e.defaultQueryOptions(e),r=this.#e.getQueryCache().build(this.#e,t);return r.fetch().then(()=>this.createResult(r,t))}fetch(e){return this.#m({...e,cancelRefetch:e.cancelRefetch??!0}).then(()=>(this.updateResult(),this.#s))}#m(e){this.#v();let t=this.#n.fetch(this.options,e);return e?.throwOnError||(t=t.catch(l.lQ)),t}#w(){this.#g();let e=(0,l.d2)(this.options.staleTime,this.#n);if(l.S$||this.#s.isStale||!(0,l.gn)(e))return;let t=(0,l.j3)(this.#s.dataUpdatedAt,e);this.#d=setTimeout(()=>{this.#s.isStale||this.updateResult()},t+1)}#x(){return("function"==typeof this.options.refetchInterval?this.options.refetchInterval(this.#n):this.options.refetchInterval)??!1}#k(e){this.#y(),this.#p=e,!l.S$&&!1!==(0,l.Eh)(this.options.enabled,this.#n)&&(0,l.gn)(this.#p)&&0!==this.#p&&(this.#h=setInterval(()=>{(this.options.refetchIntervalInBackground||n.m.isFocused())&&this.#m()},this.#p))}#b(){this.#w(),this.#k(this.#x())}#g(){this.#d&&(clearTimeout(this.#d),this.#d=void 0)}#y(){this.#h&&(clearInterval(this.#h),this.#h=void 0)}createResult(e,t){let r,n=this.#n,o=this.options,i=this.#s,c=this.#i,d=this.#a,f=e!==n?e.state:this.#o,{state:m}=e,b={...m},g=!1;if(t._optimisticResults){let r=this.hasListeners(),i=!r&&u(e,t),a=r&&h(e,n,t,o);(i||a)&&(b={...b,...(0,s.k)(m.data,e.options)}),"isRestoring"===t._optimisticResults&&(b.fetchStatus="idle")}let{error:y,errorUpdatedAt:v,status:w}=b;r=b.data;let x=!1;if(void 0!==t.placeholderData&&void 0===r&&"pending"===w){let e;i?.isPlaceholderData&&t.placeholderData===d?.placeholderData?(e=i.data,x=!0):e="function"==typeof t.placeholderData?t.placeholderData(this.#u?.state.data,this.#u):t.placeholderData,void 0!==e&&(w="success",r=(0,l.pl)(i?.data,e,t),g=!0)}if(t.select&&void 0!==r&&!x)if(i&&r===c?.data&&t.select===this.#l)r=this.#c;else try{this.#l=t.select,r=t.select(r),r=(0,l.pl)(i?.data,r,t),this.#c=r,this.#t=null}catch(e){this.#t=e}this.#t&&(y=this.#t,r=this.#c,v=Date.now(),w="error");let k="fetching"===b.fetchStatus,R="pending"===w,E="error"===w,I=R&&k,C=void 0!==r,S={status:w,fetchStatus:b.fetchStatus,isPending:R,isSuccess:"success"===w,isError:E,isInitialLoading:I,isLoading:I,data:r,dataUpdatedAt:b.dataUpdatedAt,error:y,errorUpdatedAt:v,failureCount:b.fetchFailureCount,failureReason:b.fetchFailureReason,errorUpdateCount:b.errorUpdateCount,isFetched:b.dataUpdateCount>0||b.errorUpdateCount>0,isFetchedAfterMount:b.dataUpdateCount>f.dataUpdateCount||b.errorUpdateCount>f.errorUpdateCount,isFetching:k,isRefetching:k&&!R,isLoadingError:E&&!C,isPaused:"paused"===b.fetchStatus,isPlaceholderData:g,isRefetchError:E&&C,isStale:p(e,t),refetch:this.refetch,promise:this.#r};if(this.options.experimental_prefetchInRender){let t=e=>{"error"===S.status?e.reject(S.error):void 0!==S.data&&e.resolve(S.data)},r=()=>{t(this.#r=S.promise=(0,a.T)())},o=this.#r;switch(o.status){case"pending":e.queryHash===n.queryHash&&t(o);break;case"fulfilled":("error"===S.status||S.data!==o.value)&&r();break;case"rejected":("error"!==S.status||S.error!==o.reason)&&r()}}return S}updateResult(){let e=this.#s,t=this.createResult(this.#n,this.options);this.#i=this.#n.state,this.#a=this.options,void 0!==this.#i.data&&(this.#u=this.#n),(0,l.f8)(t,e)||(this.#s=t,this.#R({listeners:(()=>{if(!e)return!0;let{notifyOnChangeProps:t}=this.options,r="function"==typeof t?t():t;if("all"===r||!r&&!this.#f.size)return!0;let n=new Set(r??this.#f);return this.options.throwOnError&&n.add("error"),Object.keys(this.#s).some(t=>this.#s[t]!==e[t]&&n.has(t))})()}))}#v(){let e=this.#e.getQueryCache().build(this.#e,this.options);if(e===this.#n)return;let t=this.#n;this.#n=e,this.#o=e.state,this.hasListeners()&&(t?.removeObserver(this),e.addObserver(this))}onQueryUpdate(){this.updateResult(),this.hasListeners()&&this.#b()}#R(e){o.jG.batch(()=>{e.listeners&&this.listeners.forEach(e=>{e(this.#s)}),this.#e.getQueryCache().notify({query:this.#n,type:"observerResultsUpdated"})})}};function u(e,t){return!1!==(0,l.Eh)(t.enabled,e)&&void 0===e.state.data&&("error"!==e.state.status||!1!==t.retryOnMount)||void 0!==e.state.data&&d(e,t,t.refetchOnMount)}function d(e,t,r){if(!1!==(0,l.Eh)(t.enabled,e)){let n="function"==typeof r?r(e):r;return"always"===n||!1!==n&&p(e,t)}return!1}function h(e,t,r,n){return(e!==t||!1===(0,l.Eh)(n.enabled,e))&&(!r.suspense||"error"!==e.state.status)&&p(e,r)}function p(e,t){return!1!==(0,l.Eh)(t.enabled,e)&&e.isStaleByTime((0,l.d2)(t.staleTime,e))}var f=r(43210),m=r(8693);r(60687);var b=f.createContext(function(){let e=!1;return{clearReset:()=>{e=!1},reset:()=>{e=!0},isReset:()=>e}}()),g=()=>f.useContext(b),y=r(35706),v=(e,t)=>{(e.suspense||e.throwOnError||e.experimental_prefetchInRender)&&!t.isReset()&&(e.retryOnMount=!1)},w=e=>{f.useEffect(()=>{e.clearReset()},[e])},x=({result:e,errorResetBoundary:t,throwOnError:r,query:n,suspense:o})=>e.isError&&!t.isReset()&&!e.isFetching&&n&&(o&&void 0===e.data||(0,y.G)(r,[e.error,n])),k=f.createContext(!1),R=()=>f.useContext(k);k.Provider;var E=e=>{let t=e.staleTime;e.suspense&&(e.staleTime="function"==typeof t?(...e)=>Math.max(t(...e),1e3):Math.max(t??1e3,1e3),"number"==typeof e.gcTime&&(e.gcTime=Math.max(e.gcTime,1e3)))},I=(e,t)=>e.isLoading&&e.isFetching&&!t,C=(e,t)=>e?.suspense&&t.isPending,S=(e,t,r)=>t.fetchOptimistic(e).catch(()=>{r.clearReset()});function O(e,t){return function(e,t,r){let n=(0,m.jE)(r),s=R(),i=g(),a=n.defaultQueryOptions(e);n.getDefaultOptions().queries?._experimental_beforeQuery?.(a),a._optimisticResults=s?"isRestoring":"optimistic",E(a),v(a,i),w(i);let c=!n.getQueryCache().get(a.queryHash),[u]=f.useState(()=>new t(n,a)),d=u.getOptimisticResult(a),h=!s&&!1!==e.subscribed;if(f.useSyncExternalStore(f.useCallback(e=>{let t=h?u.subscribe(o.jG.batchCalls(e)):y.l;return u.updateResult(),t},[u,h]),()=>u.getCurrentResult(),()=>u.getCurrentResult()),f.useEffect(()=>{u.setOptions(a)},[a,u]),C(a,d))throw S(a,u,i);if(x({result:d,errorResetBoundary:i,throwOnError:a.throwOnError,query:n.getQueryCache().get(a.queryHash),suspense:a.suspense}))throw d.error;if(n.getDefaultOptions().queries?._experimental_afterQuery?.(a,d),a.experimental_prefetchInRender&&!l.S$&&I(d,s)){let e=c?S(a,u,i):n.getQueryCache().get(a.queryHash)?.promise;e?.catch(y.l).finally(()=>{u.updateResult()})}return a.notifyOnChangeProps?d:u.trackResult(d)}(e,c,t)}},35706:(e,t,r)=>{function n(e,t){return"function"==typeof e?e(...t):!!e}function o(){}r.d(t,{G:()=>n,l:()=>o})},40211:(e,t,r)=>{r.d(t,{C1:()=>I,bL:()=>E});var n=r(43210),o=r(98599),s=r(11273),i=r(70569),a=r(65551),l=r(83721),c=r(18853),u=r(46059),d=r(14163),h=r(60687),p="Checkbox",[f,m]=(0,s.A)(p),[b,g]=f(p),y=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,name:s,checked:l,defaultChecked:c,required:u,disabled:f,value:m="on",onCheckedChange:g,form:y,...v}=e,[w,E]=n.useState(null),I=(0,o.s)(t,e=>E(e)),C=n.useRef(!1),S=!w||y||!!w.closest("form"),[O,N]=(0,a.i)({prop:l,defaultProp:c??!1,onChange:g,caller:p}),j=n.useRef(O);return n.useEffect(()=>{let e=w?.form;if(e){let t=()=>N(j.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[w,N]),(0,h.jsxs)(b,{scope:r,state:O,disabled:f,children:[(0,h.jsx)(d.sG.button,{type:"button",role:"checkbox","aria-checked":k(O)?"mixed":O,"aria-required":u,"data-state":R(O),"data-disabled":f?"":void 0,disabled:f,value:m,...v,ref:I,onKeyDown:(0,i.m)(e.onKeyDown,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,i.m)(e.onClick,e=>{N(e=>!!k(e)||!e),S&&(C.current=e.isPropagationStopped(),C.current||e.stopPropagation())})}),S&&(0,h.jsx)(x,{control:w,bubbles:!C.current,name:s,value:m,checked:O,required:u,disabled:f,form:y,style:{transform:"translateX(-100%)"},defaultChecked:!k(c)&&c})]})});y.displayName=p;var v="CheckboxIndicator",w=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,forceMount:n,...o}=e,s=g(v,r);return(0,h.jsx)(u.C,{present:n||k(s.state)||!0===s.state,children:(0,h.jsx)(d.sG.span,{"data-state":R(s.state),"data-disabled":s.disabled?"":void 0,...o,ref:t,style:{pointerEvents:"none",...e.style}})})});w.displayName=v;var x=n.forwardRef(({__scopeCheckbox:e,control:t,checked:r,bubbles:s=!0,defaultChecked:i,...a},u)=>{let p=n.useRef(null),f=(0,o.s)(p,u),m=(0,l.Z)(r),b=(0,c.X)(t);n.useEffect(()=>{let e=p.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(m!==r&&t){let n=new Event("click",{bubbles:s});e.indeterminate=k(r),t.call(e,!k(r)&&r),e.dispatchEvent(n)}},[m,r,s]);let g=n.useRef(!k(r)&&r);return(0,h.jsx)(d.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:i??g.current,...a,tabIndex:-1,ref:f,style:{...a.style,...b,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function k(e){return"indeterminate"===e}function R(e){return k(e)?"indeterminate":e?"checked":"unchecked"}x.displayName="CheckboxBubbleInput";var E=y,I=w},46059:(e,t,r)=>{r.d(t,{C:()=>i});var n=r(43210),o=r(98599),s=r(66156),i=e=>{let{present:t,children:r}=e,i=function(e){var t,r;let[o,i]=n.useState(),l=n.useRef(null),c=n.useRef(e),u=n.useRef("none"),[d,h]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>r[e][t]??e,t));return n.useEffect(()=>{let e=a(l.current);u.current="mounted"===d?e:"none"},[d]),(0,s.N)(()=>{let t=l.current,r=c.current;if(r!==e){let n=u.current,o=a(t);e?h("MOUNT"):"none"===o||t?.display==="none"?h("UNMOUNT"):r&&n!==o?h("ANIMATION_OUT"):h("UNMOUNT"),c.current=e}},[e,h]),(0,s.N)(()=>{if(o){let e,t=o.ownerDocument.defaultView??window,r=r=>{let n=a(l.current).includes(r.animationName);if(r.target===o&&n&&(h("ANIMATION_END"),!c.current)){let r=o.style.animationFillMode;o.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=r)})}},n=e=>{e.target===o&&(u.current=a(l.current))};return o.addEventListener("animationstart",n),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{t.clearTimeout(e),o.removeEventListener("animationstart",n),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}h("ANIMATION_END")},[o,h]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:n.useCallback(e=>{l.current=e?getComputedStyle(e):null,i(e)},[])}}(t),l="function"==typeof r?r({present:i.isPresent}):n.Children.only(r),c=(0,o.s)(i.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(l));return"function"==typeof r||i.isPresent?n.cloneElement(l,{ref:c}):null};function a(e){return e?.animationName||"none"}i.displayName="Presence"},49384:(e,t,r)=>{function n(){for(var e,t,r=0,n="",o=arguments.length;r<o;r++)(e=arguments[r])&&(t=function e(t){var r,n,o="";if("string"==typeof t||"number"==typeof t)o+=t;else if("object"==typeof t)if(Array.isArray(t)){var s=t.length;for(r=0;r<s;r++)t[r]&&(n=e(t[r]))&&(o&&(o+=" "),o+=n)}else for(n in t)t[n]&&(o&&(o+=" "),o+=n);return o}(e))&&(n&&(n+=" "),n+=t);return n}r.d(t,{$:()=>n,A:()=>o});let o=n},53907:(e,t,r)=>{r.d(t,{A:()=>y});let n=e=>[...new Set(e)],o=(e,t)=>e.filter(e=>!t.includes(e)),s=(e,t)=>e.filter(e=>t.includes(e)),i=e=>"bigint"==typeof e||!Number.isNaN(Number(e))&&Math.floor(Number(e))===e,a=e=>"bigint"==typeof e||e>=0&&Number.isSafeInteger(e);function l(e,t){let r;if(0===t.length)return e;let n=[...e];for(let e=n.length-1,o=0,s=0;e>0;e--,o++){o%=t.length,s+=r=t[o].codePointAt(0);let i=(r+o+s)%e,a=n[e],l=n[i];n[i]=a,n[e]=l}return n}let c=(e,t)=>{let r=[],n=e;if("bigint"==typeof n){let e=BigInt(t.length);do r.unshift(t[Number(n%e)]),n/=e;while(n>BigInt(0))}else do r.unshift(t[n%t.length]),n=Math.floor(n/t.length);while(n>0);return r},u=(e,t)=>e.reduce((r,n)=>{let o=t.indexOf(n);if(-1===o)throw Error(`The provided ID (${e.join("")}) is invalid, as it contains characters that do not exist in the alphabet (${t.join("")})`);if("bigint"==typeof r)return r*BigInt(t.length)+BigInt(o);let s=r*t.length+o;return Number.isSafeInteger(s)?s:(g("Unable to decode the provided string, due to lack of support for BigInt numbers in the current environment"),BigInt(r)*BigInt(t.length)+BigInt(o))},0),d=/^\+?\d+$/,h=e=>{if(!d.test(e))return Number.NaN;let t=Number.parseInt(e,10);return Number.isSafeInteger(t)?t:(g("Unable to encode the provided BigInt string without loss of information due to lack of support for BigInt type in the current environment"),BigInt(e))},p=(e,t,r)=>Array.from({length:Math.ceil(e.length/t)},(n,o)=>r(e.slice(o*t,(o+1)*t))),f=e=>new RegExp(e.map(e=>b(e)).sort((e,t)=>t.length-e.length).join("|")),m=e=>RegExp(`^[${e.map(e=>b(e)).sort((e,t)=>t.length-e.length).join("")}]+$`),b=e=>e.replace(/[\s#$()*+,.?[\\\]^{|}-]/g,"\\$&"),g=(e="BigInt is not available in this environment")=>{if("function"!=typeof BigInt)throw TypeError(e)};class y{constructor(e="",t=0,r="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890",i="cfhistuCFHISTU"){let a,c;if(this.minLength=t,"number"!=typeof t)throw TypeError(`Hashids: Provided 'minLength' has to be a number (is ${typeof t})`);if("string"!=typeof e)throw TypeError(`Hashids: Provided 'salt' has to be a string (is ${typeof e})`);if("string"!=typeof r)throw TypeError(`Hashids: Provided alphabet has to be a string (is ${typeof r})`);let u=Array.from(e),d=Array.from(r),h=Array.from(i);this.salt=u;let p=n(d);if(p.length<16)throw Error(`Hashids: alphabet must contain at least 16 unique characters, provided: ${p.join("")}`);this.alphabet=o(p,h);let b=s(h,p);this.seps=l(b,u),(0===this.seps.length||this.alphabet.length/this.seps.length>3.5)&&(a=Math.ceil(this.alphabet.length/3.5))>this.seps.length&&(c=a-this.seps.length,this.seps.push(...this.alphabet.slice(0,c)),this.alphabet=this.alphabet.slice(c)),this.alphabet=l(this.alphabet,u);let g=Math.ceil(this.alphabet.length/12);this.alphabet.length<3?(this.guards=this.seps.slice(0,g),this.seps=this.seps.slice(g)):(this.guards=this.alphabet.slice(0,g),this.alphabet=this.alphabet.slice(g)),this.guardsRegExp=f(this.guards),this.sepsRegExp=f(this.seps),this.allowedCharsRegExp=m([...this.alphabet,...this.guards,...this.seps])}encode(e,...t){let r=Array.isArray(e)?e:[...null!=e?[e]:[],...t];return 0===r.length?"":(r.every(i)||(r=r.map(e=>"bigint"==typeof e||"number"==typeof e?e:h(String(e)))),r.every(a))?this._encode(r).join(""):""}decode(e){return e&&"string"==typeof e&&0!==e.length?this._decode(e):[]}encodeHex(e){let t=e;switch(typeof t){case"bigint":t=t.toString(16);break;case"string":if(!/^[\dA-Fa-f]+$/.test(t))return"";break;default:throw Error(`Hashids: The provided value is neither a string, nor a BigInt (got: ${typeof t})`)}let r=p(t,12,e=>Number.parseInt(`1${e}`,16));return this.encode(r)}decodeHex(e){return this.decode(e).map(e=>e.toString(16).slice(1)).join("")}isValidId(e){return this.allowedCharsRegExp.test(e)}_encode(e){let{alphabet:t}=this,r=e.reduce((e,t,r)=>e+("bigint"==typeof t?Number(t%BigInt(r+100)):t%(r+100)),0),n=[t[r%t.length]],o=[...n],{seps:s}=this,{guards:i}=this;if(e.forEach((r,i)=>{let a=o.concat(this.salt,t),u=c(r,t=l(t,a));if(n.push(...u),i+1<e.length){let e=u[0].codePointAt(0)+i,t="bigint"==typeof r?Number(r%BigInt(e)):r%e;n.push(s[t%s.length])}}),n.length<this.minLength){let e=(r+n[0].codePointAt(0))%i.length;if(n.unshift(i[e]),n.length<this.minLength){let e=(r+n[2].codePointAt(0))%i.length;n.push(i[e])}}let a=Math.floor(t.length/2);for(;n.length<this.minLength;){t=l(t,t),n.unshift(...t.slice(a)),n.push(...t.slice(0,a));let e=n.length-this.minLength;if(e>0){let t=e/2;n=n.slice(t,t+this.minLength)}}return n}_decode(e){if(!this.isValidId(e))throw Error(`The provided ID (${e}) is invalid, as it contains characters that do not exist in the alphabet (${this.guards.join("")}${this.seps.join("")}${this.alphabet.join("")})`);let t=e.split(this.guardsRegExp),r=+(3===t.length||2===t.length),n=t[r];if(0===n.length)return[];let o=n[Symbol.iterator]().next().value,s=n.slice(o.length).split(this.sepsRegExp),i=this.alphabet,a=[];for(let e of s){let t=[o,...this.salt,...i],r=l(i,t.slice(0,i.length));a.push(u(Array.from(e),r)),i=r}return this._encode(a).join("")!==e?[]:a}}},54050:(e,t,r)=>{r.d(t,{n:()=>d});var n=r(43210),o=r(65406),s=r(33465),i=r(35536),a=r(31212),l=class extends i.Q{#e;#s=void 0;#E;#I;constructor(e,t){super(),this.#e=e,this.setOptions(t),this.bindMethods(),this.#C()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){let t=this.options;this.options=this.#e.defaultMutationOptions(e),(0,a.f8)(this.options,t)||this.#e.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#E,observer:this}),t?.mutationKey&&this.options.mutationKey&&(0,a.EN)(t.mutationKey)!==(0,a.EN)(this.options.mutationKey)?this.reset():this.#E?.state.status==="pending"&&this.#E.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#E?.removeObserver(this)}onMutationUpdate(e){this.#C(),this.#R(e)}getCurrentResult(){return this.#s}reset(){this.#E?.removeObserver(this),this.#E=void 0,this.#C(),this.#R()}mutate(e,t){return this.#I=t,this.#E?.removeObserver(this),this.#E=this.#e.getMutationCache().build(this.#e,this.options),this.#E.addObserver(this),this.#E.execute(e)}#C(){let e=this.#E?.state??(0,o.$)();this.#s={...e,isPending:"pending"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset}}#R(e){s.jG.batch(()=>{if(this.#I&&this.hasListeners()){let t=this.#s.variables,r=this.#s.context;e?.type==="success"?(this.#I.onSuccess?.(e.data,t,r),this.#I.onSettled?.(e.data,null,t,r)):e?.type==="error"&&(this.#I.onError?.(e.error,t,r),this.#I.onSettled?.(void 0,e.error,t,r))}this.listeners.forEach(e=>{e(this.#s)})})}},c=r(8693),u=r(35706);function d(e,t){let r=(0,c.jE)(t),[o]=n.useState(()=>new l(r,e));n.useEffect(()=>{o.setOptions(e)},[o,e]);let i=n.useSyncExternalStore(n.useCallback(e=>o.subscribe(s.jG.batchCalls(e)),[o]),()=>o.getCurrentResult(),()=>o.getCurrentResult()),a=n.useCallback((e,t)=>{o.mutate(e,t).catch(u.l)},[o]);if(i.error&&(0,u.G)(o.options.throwOnError,[i.error]))throw i.error;return{...i,mutate:a,mutateAsync:i.mutate}}},65551:(e,t,r)=>{r.d(t,{i:()=>a});var n,o=r(43210),s=r(66156),i=(n||(n=r.t(o,2)))[" useInsertionEffect ".trim().toString()]||s.N;function a({prop:e,defaultProp:t,onChange:r=()=>{},caller:n}){let[s,a,l]=function({defaultProp:e,onChange:t}){let[r,n]=o.useState(e),s=o.useRef(r),a=o.useRef(t);return i(()=>{a.current=t},[t]),o.useEffect(()=>{s.current!==r&&(a.current?.(r),s.current=r)},[r,s]),[r,n,a]}({defaultProp:t,onChange:r}),c=void 0!==e,u=c?e:s;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==c){let t=c?"controlled":"uncontrolled";console.warn(`${n} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=c},[c,n])}return[u,o.useCallback(t=>{if(c){let r="function"==typeof t?t(e):t;r!==e&&l.current?.(r)}else a(t)},[c,e,a,l])]}Symbol("RADIX:SYNC_STATE")},65822:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},66156:(e,t,r)=>{r.d(t,{N:()=>o});var n=r(43210),o=globalThis?.document?n.useLayoutEffect:()=>{}},70569:(e,t,r)=>{r.d(t,{m:()=>n});function n(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}},72942:(e,t,r)=>{r.d(t,{RG:()=>x,bL:()=>j,q7:()=>M});var n=r(43210),o=r(70569),s=r(9510),i=r(98599),a=r(11273),l=r(96963),c=r(14163),u=r(13495),d=r(65551),h=r(43),p=r(60687),f="rovingFocusGroup.onEntryFocus",m={bubbles:!1,cancelable:!0},b="RovingFocusGroup",[g,y,v]=(0,s.N)(b),[w,x]=(0,a.A)(b,[v]),[k,R]=w(b),E=n.forwardRef((e,t)=>(0,p.jsx)(g.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(g.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(I,{...e,ref:t})})}));E.displayName=b;var I=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:s,loop:a=!1,dir:l,currentTabStopId:g,defaultCurrentTabStopId:v,onCurrentTabStopIdChange:w,onEntryFocus:x,preventScrollOnEntryFocus:R=!1,...E}=e,I=n.useRef(null),C=(0,i.s)(t,I),S=(0,h.jH)(l),[O,j]=(0,d.i)({prop:g,defaultProp:v??null,onChange:w,caller:b}),[M,T]=n.useState(!1),A=(0,u.c)(x),z=y(r),Q=n.useRef(!1),[P,D]=n.useState(0);return n.useEffect(()=>{let e=I.current;if(e)return e.addEventListener(f,A),()=>e.removeEventListener(f,A)},[A]),(0,p.jsx)(k,{scope:r,orientation:s,dir:S,loop:a,currentTabStopId:O,onItemFocus:n.useCallback(e=>j(e),[j]),onItemShiftTab:n.useCallback(()=>T(!0),[]),onFocusableItemAdd:n.useCallback(()=>D(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>D(e=>e-1),[]),children:(0,p.jsx)(c.sG.div,{tabIndex:M||0===P?-1:0,"data-orientation":s,...E,ref:C,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{Q.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!Q.current;if(e.target===e.currentTarget&&t&&!M){let t=new CustomEvent(f,m);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=z().filter(e=>e.focusable);N([e.find(e=>e.active),e.find(e=>e.id===O),...e].filter(Boolean).map(e=>e.ref.current),R)}}Q.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>T(!1))})})}),C="RovingFocusGroupItem",S=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:s=!0,active:i=!1,tabStopId:a,children:u,...d}=e,h=(0,l.B)(),f=a||h,m=R(C,r),b=m.currentTabStopId===f,v=y(r),{onFocusableItemAdd:w,onFocusableItemRemove:x,currentTabStopId:k}=m;return n.useEffect(()=>{if(s)return w(),()=>x()},[s,w,x]),(0,p.jsx)(g.ItemSlot,{scope:r,id:f,focusable:s,active:i,children:(0,p.jsx)(c.sG.span,{tabIndex:b?0:-1,"data-orientation":m.orientation,...d,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{s?m.onItemFocus(f):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>m.onItemFocus(f)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void m.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let o=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return O[o]}(e,m.orientation,m.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=v().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=m.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>N(r))}}),children:"function"==typeof u?u({isCurrentTabStop:b,hasTabStop:null!=k}):u})})});S.displayName=C;var O={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function N(e,t=!1){let r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var j=E,M=S},78148:(e,t,r)=>{r.d(t,{b:()=>a});var n=r(43210),o=r(14163),s=r(60687),i=n.forwardRef((e,t)=>(0,s.jsx)(o.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var a=i},78272:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},82348:(e,t,r)=>{r.d(t,{QP:()=>ec});let n=e=>{let t=a(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:n}=e;return{getClassGroupId:e=>{let r=e.split("-");return""===r[0]&&1!==r.length&&r.shift(),o(r,t)||i(e)},getConflictingClassGroupIds:(e,t)=>{let o=r[e]||[];return t&&n[e]?[...o,...n[e]]:o}}},o=(e,t)=>{if(0===e.length)return t.classGroupId;let r=e[0],n=t.nextPart.get(r),s=n?o(e.slice(1),n):void 0;if(s)return s;if(0===t.validators.length)return;let i=e.join("-");return t.validators.find(({validator:e})=>e(i))?.classGroupId},s=/^\[(.+)\]$/,i=e=>{if(s.test(e)){let t=s.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},a=e=>{let{theme:t,classGroups:r}=e,n={nextPart:new Map,validators:[]};for(let e in r)l(r[e],n,e,t);return n},l=(e,t,r,n)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:c(t,e)).classGroupId=r;return}if("function"==typeof e)return u(e)?void l(e(n),t,r,n):void t.validators.push({validator:e,classGroupId:r});Object.entries(e).forEach(([e,o])=>{l(o,c(t,e),r,n)})})},c=(e,t)=>{let r=e;return t.split("-").forEach(e=>{r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)}),r},u=e=>e.isThemeGetter,d=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,r=new Map,n=new Map,o=(o,s)=>{r.set(o,s),++t>e&&(t=0,n=r,r=new Map)};return{get(e){let t=r.get(e);return void 0!==t?t:void 0!==(t=n.get(e))?(o(e,t),t):void 0},set(e,t){r.has(e)?r.set(e,t):o(e,t)}}},h=e=>{let{prefix:t,experimentalParseClassName:r}=e,n=e=>{let t,r=[],n=0,o=0,s=0;for(let i=0;i<e.length;i++){let a=e[i];if(0===n&&0===o){if(":"===a){r.push(e.slice(s,i)),s=i+1;continue}if("/"===a){t=i;continue}}"["===a?n++:"]"===a?n--:"("===a?o++:")"===a&&o--}let i=0===r.length?e:e.substring(s),a=p(i);return{modifiers:r,hasImportantModifier:a!==i,baseClassName:a,maybePostfixModifierPosition:t&&t>s?t-s:void 0}};if(t){let e=t+":",r=n;n=t=>t.startsWith(e)?r(t.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:t,maybePostfixModifierPosition:void 0}}if(r){let e=n;n=t=>r({className:t,parseClassName:e})}return n},p=e=>e.endsWith("!")?e.substring(0,e.length-1):e.startsWith("!")?e.substring(1):e,f=e=>{let t=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let r=[],n=[];return e.forEach(e=>{"["===e[0]||t[e]?(r.push(...n.sort(),e),n=[]):n.push(e)}),r.push(...n.sort()),r}},m=e=>({cache:d(e.cacheSize),parseClassName:h(e),sortModifiers:f(e),...n(e)}),b=/\s+/,g=(e,t)=>{let{parseClassName:r,getClassGroupId:n,getConflictingClassGroupIds:o,sortModifiers:s}=t,i=[],a=e.trim().split(b),l="";for(let e=a.length-1;e>=0;e-=1){let t=a[e],{isExternal:c,modifiers:u,hasImportantModifier:d,baseClassName:h,maybePostfixModifierPosition:p}=r(t);if(c){l=t+(l.length>0?" "+l:l);continue}let f=!!p,m=n(f?h.substring(0,p):h);if(!m){if(!f||!(m=n(h))){l=t+(l.length>0?" "+l:l);continue}f=!1}let b=s(u).join(":"),g=d?b+"!":b,y=g+m;if(i.includes(y))continue;i.push(y);let v=o(m,f);for(let e=0;e<v.length;++e){let t=v[e];i.push(g+t)}l=t+(l.length>0?" "+l:l)}return l};function y(){let e,t,r=0,n="";for(;r<arguments.length;)(e=arguments[r++])&&(t=v(e))&&(n&&(n+=" "),n+=t);return n}let v=e=>{let t;if("string"==typeof e)return e;let r="";for(let n=0;n<e.length;n++)e[n]&&(t=v(e[n]))&&(r&&(r+=" "),r+=t);return r},w=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},x=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,k=/^\((?:(\w[\w-]*):)?(.+)\)$/i,R=/^\d+\/\d+$/,E=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,I=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,C=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,S=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,O=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,N=e=>R.test(e),j=e=>!!e&&!Number.isNaN(Number(e)),M=e=>!!e&&Number.isInteger(Number(e)),T=e=>e.endsWith("%")&&j(e.slice(0,-1)),A=e=>E.test(e),z=()=>!0,Q=e=>I.test(e)&&!C.test(e),P=()=>!1,D=e=>S.test(e),L=e=>O.test(e),$=e=>!G(e)&&!W(e),F=e=>ee(e,eo,P),G=e=>x.test(e),_=e=>ee(e,es,Q),U=e=>ee(e,ei,j),B=e=>ee(e,er,P),q=e=>ee(e,en,L),H=e=>ee(e,el,D),W=e=>k.test(e),K=e=>et(e,es),V=e=>et(e,ea),X=e=>et(e,er),Z=e=>et(e,eo),Y=e=>et(e,en),J=e=>et(e,el,!0),ee=(e,t,r)=>{let n=x.exec(e);return!!n&&(n[1]?t(n[1]):r(n[2]))},et=(e,t,r=!1)=>{let n=k.exec(e);return!!n&&(n[1]?t(n[1]):r)},er=e=>"position"===e||"percentage"===e,en=e=>"image"===e||"url"===e,eo=e=>"length"===e||"size"===e||"bg-size"===e,es=e=>"length"===e,ei=e=>"number"===e,ea=e=>"family-name"===e,el=e=>"shadow"===e;Symbol.toStringTag;let ec=function(e,...t){let r,n,o,s=function(a){return n=(r=m(t.reduce((e,t)=>t(e),e()))).cache.get,o=r.cache.set,s=i,i(a)};function i(e){let t=n(e);if(t)return t;let s=g(e,r);return o(e,s),s}return function(){return s(y.apply(null,arguments))}}(()=>{let e=w("color"),t=w("font"),r=w("text"),n=w("font-weight"),o=w("tracking"),s=w("leading"),i=w("breakpoint"),a=w("container"),l=w("spacing"),c=w("radius"),u=w("shadow"),d=w("inset-shadow"),h=w("text-shadow"),p=w("drop-shadow"),f=w("blur"),m=w("perspective"),b=w("aspect"),g=w("ease"),y=w("animate"),v=()=>["auto","avoid","all","avoid-page","page","left","right","column"],x=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],k=()=>[...x(),W,G],R=()=>["auto","hidden","clip","visible","scroll"],E=()=>["auto","contain","none"],I=()=>[W,G,l],C=()=>[N,"full","auto",...I()],S=()=>[M,"none","subgrid",W,G],O=()=>["auto",{span:["full",M,W,G]},M,W,G],Q=()=>[M,"auto",W,G],P=()=>["auto","min","max","fr",W,G],D=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],L=()=>["start","end","center","stretch","center-safe","end-safe"],ee=()=>["auto",...I()],et=()=>[N,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...I()],er=()=>[e,W,G],en=()=>[...x(),X,B,{position:[W,G]}],eo=()=>["no-repeat",{repeat:["","x","y","space","round"]}],es=()=>["auto","cover","contain",Z,F,{size:[W,G]}],ei=()=>[T,K,_],ea=()=>["","none","full",c,W,G],el=()=>["",j,K,_],ec=()=>["solid","dashed","dotted","double"],eu=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],ed=()=>[j,T,X,B],eh=()=>["","none",f,W,G],ep=()=>["none",j,W,G],ef=()=>["none",j,W,G],em=()=>[j,W,G],eb=()=>[N,"full",...I()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[A],breakpoint:[A],color:[z],container:[A],"drop-shadow":[A],ease:["in","out","in-out"],font:[$],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[A],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[A],shadow:[A],spacing:["px",j],text:[A],"text-shadow":[A],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",N,G,W,b]}],container:["container"],columns:[{columns:[j,G,W,a]}],"break-after":[{"break-after":v()}],"break-before":[{"break-before":v()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:k()}],overflow:[{overflow:R()}],"overflow-x":[{"overflow-x":R()}],"overflow-y":[{"overflow-y":R()}],overscroll:[{overscroll:E()}],"overscroll-x":[{"overscroll-x":E()}],"overscroll-y":[{"overscroll-y":E()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:C()}],"inset-x":[{"inset-x":C()}],"inset-y":[{"inset-y":C()}],start:[{start:C()}],end:[{end:C()}],top:[{top:C()}],right:[{right:C()}],bottom:[{bottom:C()}],left:[{left:C()}],visibility:["visible","invisible","collapse"],z:[{z:[M,"auto",W,G]}],basis:[{basis:[N,"full","auto",a,...I()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[j,N,"auto","initial","none",G]}],grow:[{grow:["",j,W,G]}],shrink:[{shrink:["",j,W,G]}],order:[{order:[M,"first","last","none",W,G]}],"grid-cols":[{"grid-cols":S()}],"col-start-end":[{col:O()}],"col-start":[{"col-start":Q()}],"col-end":[{"col-end":Q()}],"grid-rows":[{"grid-rows":S()}],"row-start-end":[{row:O()}],"row-start":[{"row-start":Q()}],"row-end":[{"row-end":Q()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":P()}],"auto-rows":[{"auto-rows":P()}],gap:[{gap:I()}],"gap-x":[{"gap-x":I()}],"gap-y":[{"gap-y":I()}],"justify-content":[{justify:[...D(),"normal"]}],"justify-items":[{"justify-items":[...L(),"normal"]}],"justify-self":[{"justify-self":["auto",...L()]}],"align-content":[{content:["normal",...D()]}],"align-items":[{items:[...L(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...L(),{baseline:["","last"]}]}],"place-content":[{"place-content":D()}],"place-items":[{"place-items":[...L(),"baseline"]}],"place-self":[{"place-self":["auto",...L()]}],p:[{p:I()}],px:[{px:I()}],py:[{py:I()}],ps:[{ps:I()}],pe:[{pe:I()}],pt:[{pt:I()}],pr:[{pr:I()}],pb:[{pb:I()}],pl:[{pl:I()}],m:[{m:ee()}],mx:[{mx:ee()}],my:[{my:ee()}],ms:[{ms:ee()}],me:[{me:ee()}],mt:[{mt:ee()}],mr:[{mr:ee()}],mb:[{mb:ee()}],ml:[{ml:ee()}],"space-x":[{"space-x":I()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":I()}],"space-y-reverse":["space-y-reverse"],size:[{size:et()}],w:[{w:[a,"screen",...et()]}],"min-w":[{"min-w":[a,"screen","none",...et()]}],"max-w":[{"max-w":[a,"screen","none","prose",{screen:[i]},...et()]}],h:[{h:["screen",...et()]}],"min-h":[{"min-h":["screen","none",...et()]}],"max-h":[{"max-h":["screen",...et()]}],"font-size":[{text:["base",r,K,_]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[n,W,U]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",T,G]}],"font-family":[{font:[V,G,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[o,W,G]}],"line-clamp":[{"line-clamp":[j,"none",W,U]}],leading:[{leading:[s,...I()]}],"list-image":[{"list-image":["none",W,G]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",W,G]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:er()}],"text-color":[{text:er()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ec(),"wavy"]}],"text-decoration-thickness":[{decoration:[j,"from-font","auto",W,_]}],"text-decoration-color":[{decoration:er()}],"underline-offset":[{"underline-offset":[j,"auto",W,G]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:I()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",W,G]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",W,G]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:en()}],"bg-repeat":[{bg:eo()}],"bg-size":[{bg:es()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},M,W,G],radial:["",W,G],conic:[M,W,G]},Y,q]}],"bg-color":[{bg:er()}],"gradient-from-pos":[{from:ei()}],"gradient-via-pos":[{via:ei()}],"gradient-to-pos":[{to:ei()}],"gradient-from":[{from:er()}],"gradient-via":[{via:er()}],"gradient-to":[{to:er()}],rounded:[{rounded:ea()}],"rounded-s":[{"rounded-s":ea()}],"rounded-e":[{"rounded-e":ea()}],"rounded-t":[{"rounded-t":ea()}],"rounded-r":[{"rounded-r":ea()}],"rounded-b":[{"rounded-b":ea()}],"rounded-l":[{"rounded-l":ea()}],"rounded-ss":[{"rounded-ss":ea()}],"rounded-se":[{"rounded-se":ea()}],"rounded-ee":[{"rounded-ee":ea()}],"rounded-es":[{"rounded-es":ea()}],"rounded-tl":[{"rounded-tl":ea()}],"rounded-tr":[{"rounded-tr":ea()}],"rounded-br":[{"rounded-br":ea()}],"rounded-bl":[{"rounded-bl":ea()}],"border-w":[{border:el()}],"border-w-x":[{"border-x":el()}],"border-w-y":[{"border-y":el()}],"border-w-s":[{"border-s":el()}],"border-w-e":[{"border-e":el()}],"border-w-t":[{"border-t":el()}],"border-w-r":[{"border-r":el()}],"border-w-b":[{"border-b":el()}],"border-w-l":[{"border-l":el()}],"divide-x":[{"divide-x":el()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":el()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...ec(),"hidden","none"]}],"divide-style":[{divide:[...ec(),"hidden","none"]}],"border-color":[{border:er()}],"border-color-x":[{"border-x":er()}],"border-color-y":[{"border-y":er()}],"border-color-s":[{"border-s":er()}],"border-color-e":[{"border-e":er()}],"border-color-t":[{"border-t":er()}],"border-color-r":[{"border-r":er()}],"border-color-b":[{"border-b":er()}],"border-color-l":[{"border-l":er()}],"divide-color":[{divide:er()}],"outline-style":[{outline:[...ec(),"none","hidden"]}],"outline-offset":[{"outline-offset":[j,W,G]}],"outline-w":[{outline:["",j,K,_]}],"outline-color":[{outline:er()}],shadow:[{shadow:["","none",u,J,H]}],"shadow-color":[{shadow:er()}],"inset-shadow":[{"inset-shadow":["none",d,J,H]}],"inset-shadow-color":[{"inset-shadow":er()}],"ring-w":[{ring:el()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:er()}],"ring-offset-w":[{"ring-offset":[j,_]}],"ring-offset-color":[{"ring-offset":er()}],"inset-ring-w":[{"inset-ring":el()}],"inset-ring-color":[{"inset-ring":er()}],"text-shadow":[{"text-shadow":["none",h,J,H]}],"text-shadow-color":[{"text-shadow":er()}],opacity:[{opacity:[j,W,G]}],"mix-blend":[{"mix-blend":[...eu(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":eu()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[j]}],"mask-image-linear-from-pos":[{"mask-linear-from":ed()}],"mask-image-linear-to-pos":[{"mask-linear-to":ed()}],"mask-image-linear-from-color":[{"mask-linear-from":er()}],"mask-image-linear-to-color":[{"mask-linear-to":er()}],"mask-image-t-from-pos":[{"mask-t-from":ed()}],"mask-image-t-to-pos":[{"mask-t-to":ed()}],"mask-image-t-from-color":[{"mask-t-from":er()}],"mask-image-t-to-color":[{"mask-t-to":er()}],"mask-image-r-from-pos":[{"mask-r-from":ed()}],"mask-image-r-to-pos":[{"mask-r-to":ed()}],"mask-image-r-from-color":[{"mask-r-from":er()}],"mask-image-r-to-color":[{"mask-r-to":er()}],"mask-image-b-from-pos":[{"mask-b-from":ed()}],"mask-image-b-to-pos":[{"mask-b-to":ed()}],"mask-image-b-from-color":[{"mask-b-from":er()}],"mask-image-b-to-color":[{"mask-b-to":er()}],"mask-image-l-from-pos":[{"mask-l-from":ed()}],"mask-image-l-to-pos":[{"mask-l-to":ed()}],"mask-image-l-from-color":[{"mask-l-from":er()}],"mask-image-l-to-color":[{"mask-l-to":er()}],"mask-image-x-from-pos":[{"mask-x-from":ed()}],"mask-image-x-to-pos":[{"mask-x-to":ed()}],"mask-image-x-from-color":[{"mask-x-from":er()}],"mask-image-x-to-color":[{"mask-x-to":er()}],"mask-image-y-from-pos":[{"mask-y-from":ed()}],"mask-image-y-to-pos":[{"mask-y-to":ed()}],"mask-image-y-from-color":[{"mask-y-from":er()}],"mask-image-y-to-color":[{"mask-y-to":er()}],"mask-image-radial":[{"mask-radial":[W,G]}],"mask-image-radial-from-pos":[{"mask-radial-from":ed()}],"mask-image-radial-to-pos":[{"mask-radial-to":ed()}],"mask-image-radial-from-color":[{"mask-radial-from":er()}],"mask-image-radial-to-color":[{"mask-radial-to":er()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":x()}],"mask-image-conic-pos":[{"mask-conic":[j]}],"mask-image-conic-from-pos":[{"mask-conic-from":ed()}],"mask-image-conic-to-pos":[{"mask-conic-to":ed()}],"mask-image-conic-from-color":[{"mask-conic-from":er()}],"mask-image-conic-to-color":[{"mask-conic-to":er()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:en()}],"mask-repeat":[{mask:eo()}],"mask-size":[{mask:es()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",W,G]}],filter:[{filter:["","none",W,G]}],blur:[{blur:eh()}],brightness:[{brightness:[j,W,G]}],contrast:[{contrast:[j,W,G]}],"drop-shadow":[{"drop-shadow":["","none",p,J,H]}],"drop-shadow-color":[{"drop-shadow":er()}],grayscale:[{grayscale:["",j,W,G]}],"hue-rotate":[{"hue-rotate":[j,W,G]}],invert:[{invert:["",j,W,G]}],saturate:[{saturate:[j,W,G]}],sepia:[{sepia:["",j,W,G]}],"backdrop-filter":[{"backdrop-filter":["","none",W,G]}],"backdrop-blur":[{"backdrop-blur":eh()}],"backdrop-brightness":[{"backdrop-brightness":[j,W,G]}],"backdrop-contrast":[{"backdrop-contrast":[j,W,G]}],"backdrop-grayscale":[{"backdrop-grayscale":["",j,W,G]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[j,W,G]}],"backdrop-invert":[{"backdrop-invert":["",j,W,G]}],"backdrop-opacity":[{"backdrop-opacity":[j,W,G]}],"backdrop-saturate":[{"backdrop-saturate":[j,W,G]}],"backdrop-sepia":[{"backdrop-sepia":["",j,W,G]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":I()}],"border-spacing-x":[{"border-spacing-x":I()}],"border-spacing-y":[{"border-spacing-y":I()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",W,G]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[j,"initial",W,G]}],ease:[{ease:["linear","initial",g,W,G]}],delay:[{delay:[j,W,G]}],animate:[{animate:["none",y,W,G]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[m,W,G]}],"perspective-origin":[{"perspective-origin":k()}],rotate:[{rotate:ep()}],"rotate-x":[{"rotate-x":ep()}],"rotate-y":[{"rotate-y":ep()}],"rotate-z":[{"rotate-z":ep()}],scale:[{scale:ef()}],"scale-x":[{"scale-x":ef()}],"scale-y":[{"scale-y":ef()}],"scale-z":[{"scale-z":ef()}],"scale-3d":["scale-3d"],skew:[{skew:em()}],"skew-x":[{"skew-x":em()}],"skew-y":[{"skew-y":em()}],transform:[{transform:[W,G,"","none","gpu","cpu"]}],"transform-origin":[{origin:k()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:eb()}],"translate-x":[{"translate-x":eb()}],"translate-y":[{"translate-y":eb()}],"translate-z":[{"translate-z":eb()}],"translate-none":["translate-none"],accent:[{accent:er()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:er()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",W,G]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":I()}],"scroll-mx":[{"scroll-mx":I()}],"scroll-my":[{"scroll-my":I()}],"scroll-ms":[{"scroll-ms":I()}],"scroll-me":[{"scroll-me":I()}],"scroll-mt":[{"scroll-mt":I()}],"scroll-mr":[{"scroll-mr":I()}],"scroll-mb":[{"scroll-mb":I()}],"scroll-ml":[{"scroll-ml":I()}],"scroll-p":[{"scroll-p":I()}],"scroll-px":[{"scroll-px":I()}],"scroll-py":[{"scroll-py":I()}],"scroll-ps":[{"scroll-ps":I()}],"scroll-pe":[{"scroll-pe":I()}],"scroll-pt":[{"scroll-pt":I()}],"scroll-pr":[{"scroll-pr":I()}],"scroll-pb":[{"scroll-pb":I()}],"scroll-pl":[{"scroll-pl":I()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",W,G]}],fill:[{fill:["none",...er()]}],"stroke-w":[{stroke:[j,K,_,U]}],stroke:[{stroke:["none",...er()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}})},83721:(e,t,r)=>{r.d(t,{Z:()=>o});var n=r(43210);function o(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},96963:(e,t,r)=>{r.d(t,{B:()=>l});var n,o=r(43210),s=r(66156),i=(n||(n=r.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),a=0;function l(e){let[t,r]=o.useState(i());return(0,s.N)(()=>{e||r(e=>e??String(a++))},[e]),e||(t?`radix-${t}`:"")}},98599:(e,t,r)=>{r.d(t,{s:()=>i,t:()=>s});var n=r(43210);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function s(...e){return t=>{let r=!1,n=e.map(e=>{let n=o(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():o(e[t],null)}}}}function i(...e){return n.useCallback(s(...e),e)}}};