"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const formSubmissionController_1 = require("../../controllers/formSubmissionController");
const formSubmissionRepository_1 = __importDefault(require("../../repositories/formSubmissionRepository"));
// Mock the dependencies
jest.mock("../../repositories/formSubmissionRepository");
describe("Form Submission Controller", () => {
    let mockRequest;
    let mockResponse;
    let responseObject = {};
    beforeEach(() => {
        // Reset mocks before each test
        jest.clearAllMocks();
        jest.resetAllMocks();
        // Setup mock response
        mockResponse = {
            status: jest.fn().mockReturnThis(),
            json: jest.fn().mockImplementation((result) => {
                responseObject = result;
                return mockResponse;
            }),
        };
        // Reset response object
        responseObject = {};
        // Setup default authenticated user
        mockRequest = {
            user: {
                id: 1,
            },
            params: {},
            query: {},
            body: {},
        };
    });
    describe("createFormSubmission", () => {
        beforeEach(() => {
            mockRequest.body = {
                projectId: 1,
                status: "submitted",
                metadata: { key: "value" },
                answers: [
                    {
                        questionId: 1,
                        value: "Answer 1",
                        answerType: "text",
                    },
                ],
            };
        });
        it("should create a form submission successfully", () => __awaiter(void 0, void 0, void 0, function* () {
            const mockSubmission = {
                id: 1,
                projectId: 1,
                status: "submitted",
                metadata: { key: "value" },
                createdAt: new Date(),
            };
            formSubmissionRepository_1.default.isProjectAccessible.mockResolvedValue(true);
            formSubmissionRepository_1.default.create.mockResolvedValue(mockSubmission);
            yield (0, formSubmissionController_1.createFormSubmission)(mockRequest, mockResponse);
            expect(formSubmissionRepository_1.default.isProjectAccessible).toHaveBeenCalledWith(1, 1);
            expect(formSubmissionRepository_1.default.create).toHaveBeenCalledWith(expect.objectContaining({
                projectId: 1,
                status: "submitted",
                metadata: { key: "value" },
            }), 1, "Test Device", undefined);
            expect(mockResponse.status).toHaveBeenCalledWith(200);
            expect(responseObject).toHaveProperty("success", true);
            expect(responseObject).toHaveProperty("message", "Form submission created successfully");
            expect(responseObject.data).toHaveProperty("formSubmission", mockSubmission);
        }));
        it("should return 403 when user cannot access the project", () => __awaiter(void 0, void 0, void 0, function* () {
            formSubmissionRepository_1.default.isProjectAccessible.mockResolvedValue(false);
            yield (0, formSubmissionController_1.createFormSubmission)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(403);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "You don't have access to this project");
            expect(formSubmissionRepository_1.default.create).not.toHaveBeenCalled();
        }));
        it("should return 400 for invalid input", () => __awaiter(void 0, void 0, void 0, function* () {
            // Missing required fields
            mockRequest.body = {};
            yield (0, formSubmissionController_1.createFormSubmission)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(400);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "Validation error");
            expect(formSubmissionRepository_1.default.create).not.toHaveBeenCalled();
        }));
        it("should handle server errors", () => __awaiter(void 0, void 0, void 0, function* () {
            formSubmissionRepository_1.default.isProjectAccessible.mockRejectedValue(new Error("Database error"));
            yield (0, formSubmissionController_1.createFormSubmission)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(500);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "Error creating form submission");
        }));
    });
    describe("getProjectFormSubmissions", () => {
        beforeEach(() => {
            mockRequest.params = { projectId: "1" };
        });
        it("should get all form submissions successfully", () => __awaiter(void 0, void 0, void 0, function* () {
            const mockSubmissions = [
                {
                    id: 1,
                    projectId: 1,
                    status: "submitted",
                    createdAt: new Date(),
                },
                {
                    id: 2,
                    projectId: 1,
                    status: "submitted",
                    createdAt: new Date(),
                },
            ];
            formSubmissionRepository_1.default.isProjectAccessible.mockResolvedValue(true);
            formSubmissionRepository_1.default.findByProjectId.mockResolvedValue(mockSubmissions);
            yield (0, formSubmissionController_1.getProjectFormSubmissions)(mockRequest, mockResponse);
            expect(formSubmissionRepository_1.default.isProjectAccessible).toHaveBeenCalledWith(1, 1);
            expect(formSubmissionRepository_1.default.findByProjectId).toHaveBeenCalledWith(1);
            expect(mockResponse.status).toHaveBeenCalledWith(200);
            expect(responseObject).toHaveProperty("success", true);
            expect(responseObject).toHaveProperty("message", "get project form success");
            expect(responseObject.data).toHaveProperty("formSubmissions", mockSubmissions);
        }));
        it("should return 403 when user cannot access the project", () => __awaiter(void 0, void 0, void 0, function* () {
            formSubmissionRepository_1.default.isProjectAccessible.mockResolvedValue(false);
            yield (0, formSubmissionController_1.getProjectFormSubmissions)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(403);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "You don't have access to this project");
            expect(formSubmissionRepository_1.default.findByProjectId).not.toHaveBeenCalled();
        }));
        it("should handle invalid project ID", () => __awaiter(void 0, void 0, void 0, function* () {
            mockRequest.params = { projectId: "invalid" };
            yield (0, formSubmissionController_1.getProjectFormSubmissions)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(400);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "Invalid project ID");
        }));
        it("should handle server errors", () => __awaiter(void 0, void 0, void 0, function* () {
            formSubmissionRepository_1.default.isProjectAccessible.mockRejectedValue(new Error("Database error"));
            yield (0, formSubmissionController_1.getProjectFormSubmissions)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(500);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "Error retrieving form submissions");
        }));
    });
    describe("getFormSubmission", () => {
        beforeEach(() => {
            mockRequest.params = { id: "1" };
        });
        it("should get a form submission by ID successfully", () => __awaiter(void 0, void 0, void 0, function* () {
            const mockSubmission = {
                id: 1,
                projectId: 1,
                status: "submitted",
                createdAt: new Date(),
            };
            formSubmissionRepository_1.default.findById.mockResolvedValue(mockSubmission);
            formSubmissionRepository_1.default.isProjectAccessible.mockResolvedValue(true);
            yield (0, formSubmissionController_1.getFormSubmission)(mockRequest, mockResponse);
            expect(formSubmissionRepository_1.default.findById).toHaveBeenCalledWith(1);
            expect(formSubmissionRepository_1.default.isProjectAccessible).toHaveBeenCalledWith(1, 1);
            expect(mockResponse.status).toHaveBeenCalledWith(200);
            expect(responseObject).toHaveProperty("success", true);
            expect(responseObject).toHaveProperty("message", "get form submission success");
            expect(responseObject.data).toHaveProperty("formSubmission", mockSubmission);
        }));
        it("should return 404 when form submission not found", () => __awaiter(void 0, void 0, void 0, function* () {
            formSubmissionRepository_1.default.findById.mockResolvedValue(null);
            yield (0, formSubmissionController_1.getFormSubmission)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(404);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "Form submission not found");
        }));
        it("should return 403 when user cannot access the project", () => __awaiter(void 0, void 0, void 0, function* () {
            const mockSubmission = {
                id: 1,
                projectId: 1,
                status: "submitted",
                createdAt: new Date(),
            };
            formSubmissionRepository_1.default.findById.mockResolvedValue(mockSubmission);
            formSubmissionRepository_1.default.isProjectAccessible.mockResolvedValue(false);
            yield (0, formSubmissionController_1.getFormSubmission)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(403);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "You don't have access to this form submission");
        }));
        it("should handle invalid submission ID", () => __awaiter(void 0, void 0, void 0, function* () {
            mockRequest.params = { id: "invalid" };
            yield (0, formSubmissionController_1.getFormSubmission)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(400);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "Invalid submission ID");
        }));
        it("should handle server errors", () => __awaiter(void 0, void 0, void 0, function* () {
            formSubmissionRepository_1.default.findById.mockRejectedValue(new Error("Database error"));
            yield (0, formSubmissionController_1.getFormSubmission)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(500);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "Error retrieving form submission");
        }));
    });
    describe("updateFormSubmission", () => {
        beforeEach(() => {
            mockRequest.params = { id: "1" };
            mockRequest.body = {
                status: "completed",
                metadata: { updatedKey: "updatedValue" },
            };
        });
        it("should update a form submission successfully", () => __awaiter(void 0, void 0, void 0, function* () {
            const existingSubmission = {
                id: 1,
                projectId: 1,
                status: "submitted",
                createdAt: new Date(),
            };
            const updatedSubmission = Object.assign(Object.assign({}, existingSubmission), { status: "completed", metadata: { updatedKey: "updatedValue" } });
            formSubmissionRepository_1.default.findById.mockResolvedValue(existingSubmission);
            formSubmissionRepository_1.default.isProjectAccessible.mockResolvedValue(true);
            formSubmissionRepository_1.default.update.mockResolvedValue(updatedSubmission);
            yield (0, formSubmissionController_1.updateFormSubmission)(mockRequest, mockResponse);
            expect(formSubmissionRepository_1.default.findById).toHaveBeenCalledWith(1);
            expect(formSubmissionRepository_1.default.isProjectAccessible).toHaveBeenCalledWith(1, 1);
            expect(formSubmissionRepository_1.default.update).toHaveBeenCalledWith(1, expect.objectContaining({
                status: "completed",
                metadata: { updatedKey: "updatedValue" },
            }));
            expect(mockResponse.status).toHaveBeenCalledWith(200);
            expect(responseObject).toHaveProperty("success", true);
            expect(responseObject).toHaveProperty("message", "Form submission updated successfully");
            expect(responseObject.data).toHaveProperty("formSubmission", updatedSubmission);
        }));
        it("should return 404 when form submission not found", () => __awaiter(void 0, void 0, void 0, function* () {
            formSubmissionRepository_1.default.findById.mockResolvedValue(null);
            yield (0, formSubmissionController_1.updateFormSubmission)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(404);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "Form submission not found");
            expect(formSubmissionRepository_1.default.update).not.toHaveBeenCalled();
        }));
        it("should return 403 when user cannot access the project", () => __awaiter(void 0, void 0, void 0, function* () {
            const existingSubmission = {
                id: 1,
                projectId: 1,
                status: "submitted",
                createdAt: new Date(),
            };
            formSubmissionRepository_1.default.findById.mockResolvedValue(existingSubmission);
            formSubmissionRepository_1.default.isProjectAccessible.mockResolvedValue(false);
            yield (0, formSubmissionController_1.updateFormSubmission)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(403);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "You don't have access to this form submission");
            expect(formSubmissionRepository_1.default.update).not.toHaveBeenCalled();
        }));
        it("should handle invalid submission ID", () => __awaiter(void 0, void 0, void 0, function* () {
            mockRequest.params = { id: "invalid" };
            yield (0, formSubmissionController_1.updateFormSubmission)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(400);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "Invalid submission ID");
            expect(formSubmissionRepository_1.default.update).not.toHaveBeenCalled();
        }));
        it("should handle server errors", () => __awaiter(void 0, void 0, void 0, function* () {
            formSubmissionRepository_1.default.findById.mockRejectedValue(new Error("Database error"));
            yield (0, formSubmissionController_1.updateFormSubmission)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(500);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "Error updating form submission");
        }));
    });
    describe("deleteFormSubmission", () => {
        beforeEach(() => {
            mockRequest.params = { id: "1" };
        });
        it("should delete a form submission successfully", () => __awaiter(void 0, void 0, void 0, function* () {
            const existingSubmission = {
                id: 1,
                projectId: 1,
                status: "submitted",
                createdAt: new Date(),
            };
            formSubmissionRepository_1.default.findById.mockResolvedValue(existingSubmission);
            formSubmissionRepository_1.default.isProjectAccessible.mockResolvedValue(true);
            formSubmissionRepository_1.default.delete.mockResolvedValue({
                id: 1,
            });
            yield (0, formSubmissionController_1.deleteFormSubmission)(mockRequest, mockResponse);
            expect(formSubmissionRepository_1.default.findById).toHaveBeenCalledWith(1);
            expect(formSubmissionRepository_1.default.isProjectAccessible).toHaveBeenCalledWith(1, 1);
            expect(formSubmissionRepository_1.default.delete).toHaveBeenCalledWith(1);
            expect(mockResponse.status).toHaveBeenCalledWith(200);
            expect(responseObject).toHaveProperty("success", true);
            expect(responseObject).toHaveProperty("message", "Form submission deleted successfully");
        }));
        it("should return 404 when form submission not found", () => __awaiter(void 0, void 0, void 0, function* () {
            formSubmissionRepository_1.default.findById.mockResolvedValue(null);
            yield (0, formSubmissionController_1.deleteFormSubmission)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(404);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "Form submission not found");
            expect(formSubmissionRepository_1.default.delete).not.toHaveBeenCalled();
        }));
        it("should return 403 when user cannot access the project", () => __awaiter(void 0, void 0, void 0, function* () {
            const existingSubmission = {
                id: 1,
                projectId: 1,
                status: "submitted",
                createdAt: new Date(),
            };
            formSubmissionRepository_1.default.findById.mockResolvedValue(existingSubmission);
            formSubmissionRepository_1.default.isProjectAccessible.mockResolvedValue(false);
            yield (0, formSubmissionController_1.deleteFormSubmission)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(403);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "You don't have access to this form submission");
            expect(formSubmissionRepository_1.default.delete).not.toHaveBeenCalled();
        }));
        it("should handle invalid submission ID", () => __awaiter(void 0, void 0, void 0, function* () {
            mockRequest.params = { id: "invalid" };
            yield (0, formSubmissionController_1.deleteFormSubmission)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(400);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "Invalid submission ID");
            expect(formSubmissionRepository_1.default.delete).not.toHaveBeenCalled();
        }));
        it("should handle server errors", () => __awaiter(void 0, void 0, void 0, function* () {
            formSubmissionRepository_1.default.findById.mockRejectedValue(new Error("Database error"));
            yield (0, formSubmissionController_1.deleteFormSubmission)(mockRequest, mockResponse);
            expect(mockResponse.status).toHaveBeenCalledWith(500);
            expect(responseObject).toHaveProperty("success", false);
            expect(responseObject).toHaveProperty("message", "Error deleting form submission");
        }));
    });
});
